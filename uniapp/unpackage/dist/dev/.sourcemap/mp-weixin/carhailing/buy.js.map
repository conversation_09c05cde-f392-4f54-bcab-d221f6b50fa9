{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/buy.vue?58b1", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/buy.vue?10ac", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/buy.vue?ce15", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/buy.vue?50b2", "uni-app:///carhailing/buy.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/buy.vue?0a6b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/buy.vue?1680"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "test", "totalprice", "couponvisible", "linkman", "tel", "userinfo", "editorFormdata", "issubmit", "dateIndex", "proid", "ykset", "couponrid", "couponkey", "coupontype", "coupon_money", "couponList", "couponCount", "product", "workerinfo", "formdata", "gwcnum", "leveldk_money", "formdata_money", "formdata_label", "priceAry", "years", "endyears", "startyears", "hour", "min", "yuyuevisible", "value", "yytime", "open_type", "start_time", "end_time", "difftime", "difftext", "start_value", "end_value", "usescore", "scoredk_money", "buynum", "onLoad", "onPullDownRefresh", "methods", "getTime", "app", "console", "computetime", "bindChange", "dateindex", "toStartYuyue", "toEndYuyue", "getdata", "setTimeout", "that", "chooseCoupon", "showCouponList", "handleClickMask", "calculatePrice", "product_price", "gwcminus", "gwcplus", "gwcinput", "buynumminus", "buynumplus", "buynuminput", "scoredk", "topay", "newformdata", "num", "editorChooseImage", "removeimg", "pics", "editorBind<PERSON>icker<PERSON><PERSON><PERSON>", "selectorPickerChange", "ary", "getSum", "s", "inputLinkman", "inputTel", "getDateDiff", "endTime", "startTime", "diffType", "timeType"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AAC6M;AAC7M,gBAAgB,iNAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClGA;AAAA;AAAA;AAAA;AAAkzB,CAAgB,iyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoNt0B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAEA;MACA;MACA;QACA;QAEA;QACA;UACAC;UACA;QACA;QACA;QACA;QACA;UACA;QACA;UACA;QACA;QAEAC;MACA;MACA;MACA;QACA;QACAA;QACA;QACA;UACAD;UACA;QACA;QACA;QACAC;MACA;MACA;MACAA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;QACAD;QACA;MACA;IACA;IACAE;MACA;MACAF;MACA;MACAG;MAEA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QAAA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACAL;MACA;QACAD;QACA;MACA;MACA;MACA;MACA;QAAA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IACAO;MACA;MACAP;QAAAtC;QAAAD;MAAA;QACA;UACAuC;UACA;YACA;cACAC;cACAO;gBACAR;cACA;cACA;YACA;UACA;UACAQ;YACAR;UACA;UACA;QACA;QACA;UACAS;QACA;QACA;UACAA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAA;UACAA;UACA;UACAA;UACAA;UACAA;UACAA;QACA;QACA;QACA;QACA;QACA;UACAnC;UACAA;QACA;QACAmC;QACAA;QACAA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;UACA3C;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IACA4C;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACAC;MACA;MACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;MACA;MACA;QACA5D;MACA;MACA;MACA;;MAEA;QACAwC;QACAxC;MACA;MACAA;MACAuD;IACA;IACA;IACAM;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACAjB;MACA;MACA;IACA;IACA;IACAkB;MACA;MACA;QACAxB;MACA;MACA;MACA;IACA;IACA;IACAyB;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAtB;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;QACA;QACA;UACAA;UAAA;QACA;QACA;UACAC;UACA7B;QACA;QACAmD;MACA;MACA;MACAvB;MACAA;QAAA5C;QAAAC;QAAAe;QAAAV;QAAAD;QAAAG;QAAA4D;QAAArC;QAAAC;QAAAK;QAAAE;MAAA;QACAK;QACA;UACAS;UACAT;QACA;UACAA;YACAA;UACA;QACA;UACAA;UACAC;UACA;YACAO;cACA;gBACAR;cACA;YACA;UACA;UAEA;QACA;MACA;IACA;IACAyB;MACA;MACA;MACA;MACA;MACAzB;QACAzC;QACAkD;QACAA;MACA;IACA;IACAiB;MACA;MACA;MACA;MACAC;MACAlB;IACA;IACAmB;MACA;MACA;MACA;MACA;MACA;MACArE;MACAkD;MACAA;IACA;IACAoB;MACA;MACA;MACA;MACA;MACA;MACAtE;MACAkD;MACAA;MACAA;MACA;MAEAA;MACA;MACA;QACA;UACAqB;QACA;MACA;MACA;MACArB;MACAA;IACA;IACAsB;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACAC;MACA;MACA;MACAC;MACAD;MACA;MACAE;MACA;MACA;MACArC;MACA;MACA;MACA;QACA;UACAsC;UACA;QACA;UACAA;UACA;QACA;UACAA;UACA;QACA;UACAA;UACA;QACA;UACA;MAAA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACnsBA;AAAA;AAAA;AAAA;AAAypC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACA7qC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "carhailing/buy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './carhailing/buy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./buy.vue?vue&type=template&id=f55d3092&\"\nvar renderjs\nimport script from \"./buy.vue?vue&type=script&lang=js&\"\nexport * from \"./buy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buy.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"carhailing/buy.vue\"\nexport default component.exports", "export * from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=template&id=f55d3092&\"", "var components\ntry {\n  components = {\n    couponlist: function () {\n      return import(\n        /* webpackChunkName: \"components/couponlist/couponlist\" */ \"@/components/couponlist/couponlist.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload && _vm.userinfo.leveldk_money > 0 && _vm.ykset.discount\n      ? _vm.t(\"会员\")\n      : null\n  var m1 = _vm.isload && _vm.product.is_coupon == 1 ? _vm.t(\"优惠券\") : null\n  var m2 =\n    _vm.isload && _vm.product.is_coupon == 1 && _vm.couponCount > 0\n      ? _vm.t(\"color1\")\n      : null\n  var m3 =\n    _vm.isload && _vm.product.is_coupon == 1 && !(_vm.couponCount > 0)\n      ? _vm.t(\"优惠券\")\n      : null\n  var m4 = _vm.isload && _vm.userinfo.score2money > 0 ? _vm.t(\"积分\") : null\n  var m5 = _vm.isload && _vm.userinfo.score2money > 0 ? _vm.t(\"积分\") : null\n  var m6 = _vm.isload && !_vm.issubmit ? _vm.t(\"color1\") : null\n  var m7 = _vm.isload && !_vm.issubmit ? _vm.t(\"color1rgb\") : null\n  var m8 = _vm.isload && _vm.couponvisible ? _vm.t(\"优惠券\") : null\n  var m9 = _vm.isload && _vm.yuyuevisible ? _vm.t(\"color1\") : null\n  var m10 = _vm.isload && _vm.yuyuevisible ? _vm.t(\"color1rgb\") : null\n  var m11 = _vm.isload && _vm.yuyuevisible ? _vm.t(\"color1\") : null\n  var m12 = _vm.isload && _vm.yuyuevisible ? _vm.t(\"color1rgb\") : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.yuyuevisible = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.yuyuevisible = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<form @submit=\"topay\">\r\n\t\t\t\t<view class=\"address-add\">\r\n\t\t\t\t\t<view class=\"linkitem\">\r\n\t\t\t\t\t\t<text class=\"f1\">联 系 人：</text>\r\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" :value=\"linkman\" placeholder=\"请输入您的姓名\" @input=\"inputLinkman\" placeholder-style=\"color:#626262;font-size:28rpx;\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"linkitem\">\r\n\t\t\t\t\t\t<text class=\"f1\">联系电话：</text>\r\n\t\t\t\t\t\t<input type=\"text\" class=\"input\" :value=\"tel\" placeholder=\"请输入您的手机号\" @input=\"inputTel\" placeholder-style=\"color:#626262;font-size:28rpx;\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"buydata\">\t\t\t\r\n\t\t\t\t\t<view class=\"bcontent\">\r\n\t\t\t\t\t\t<view class=\"btitle\">约车信息</view>\r\n\t\t\t\t\t\t<view class=\"product\">\r\n\t\t\t\t\t\t\t<view class=\"item flex\">\r\n\t\t\t\t\t\t\t\t<view class=\"img\" @tap=\"goto\" :data-url=\"'product?id=' + product.id\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"product.pic\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"info flex1\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{product.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"f3\"><text style=\"font-weight:bold;\">￥{{product.sell_price}}</text></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"product.cid  ==2\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">乘车日期</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\">{{product.yy_date}} </view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"product.cid  ==3\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">预约时间</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\">{{product.yy_date}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">价格</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">¥{{product.sell_price}} <text v-if=\"product.cid !='2'\">/天</text> </text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"bcontent2\">\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"userinfo.leveldk_money>0 && ykset.discount\">\r\n\t\t\t\t\t\t\t<text class=\"f1\">{{t('会员')}}折扣({{userinfo.discount}}折)</text>\r\n\t\t\t\t\t\t\t<text class=\"f2\">-¥{{userinfo.leveldk_money}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"product.is_coupon==1\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">{{t('优惠券')}}</view>\r\n\t\t\t\t\t\t\t<view v-if=\"couponCount > 0\" class=\"f2\" @tap=\"showCouponList\">\r\n\t\t\t\t\t\t\t\t<text style=\"color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx\"\r\n\t\t\t\t\t\t\t\t\t:style=\"{background:t('color1')}\">{{couponrid!=0?couponList[couponkey].couponname:couponCount+'张可用'}}</text><text\r\n\t\t\t\t\t\t\t\t\tclass=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"f2\" v-else style=\"color:#999\">无可用{{t('优惠券')}}</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- \t<view class=\"price\" v-if=\"product.cid ==3\">\r\n\t\t\t\t\t\t\t<view class=\"f1\" style=\"flex: 2;\">包车天数</view>\r\n\t\t\t\t\t\t\t<view class=\"addnum f2\">\r\n\t\t\t\t\t\t\t\t<view class=\"minus\" @tap=\"gwcminus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-minus.png'\"/></view>\r\n\t\t\t\t\t\t\t\t<input class=\"input\" type=\"number\" :value=\"gwcnum\" @input=\"gwcinput\"></input>\r\n\t\t\t\t\t\t\t\t<view class=\"plus\" @tap=\"gwcplus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-plus.png'\"/></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"product.cid ==3\">\r\n\t\t\t\t\t\t\t<view class=\"f1\" style=\"flex: 2;\">包车数量</view>\r\n\t\t\t\t\t\t\t<view class=\"addnum f2\">\r\n\t\t\t\t\t\t\t\t<view class=\"minus\" @tap=\"buynumminus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-minus.png'\"/></view>\r\n\t\t\t\t\t\t\t\t<input class=\"input\" type=\"number\" :value=\"buynum\" @input=\"buynuminput\"></input>\r\n\t\t\t\t\t\t\t\t<view class=\"plus\" @tap=\"buynumplus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-plus.png'\"/></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price\" v-if=\"product.cid ==1\">\r\n\t\t\t\t\t\t\t<view class=\"f1\" style=\"flex: 1;\">租车天数</view>\r\n\t\t\t\t\t\t\t<view class=\"choose f2\">\r\n\t\t\t\t\t\t\t\t<view class=\"choosetime\"  @tap=\"toStartYuyue\"><text v-if=\"start_time !=''\">{{start_time}}</text><text v-else>开始时间</text></view>\r\n\t\t\t\t\t\t\t\t<text class=\"difftext\" v-if=\"difftext\">{{difftext}} </text><text class=\"difftext\" v-else>请选时间</text>\r\n\t\t\t\t\t\t\t\t<view class=\"choosetime\"  @tap=\"toEndYuyue\"><text v-if=\"end_time !=''\">{{end_time}}</text><text v-else>结束时间</text></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\" v-for=\"(item,idx) in formdata\" :key=\"item.id\">\r\n\t\t\t\t\t\t\t<view class=\"label\">{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='price'\">\r\n\t\t\t\t\t\t\t\t<text class=\"f2\"  style=\"color:#999\">￥{{item.val2}}</text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='input'\">\r\n\t\t\t\t\t\t\t\t<input type=\"text\" :name=\"'form_'+idx\" class=\"input\" :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"/>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='textarea'\">\r\n\t\t\t\t\t\t\t\t<textarea :name=\"'form_'+idx\" class='textarea' :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"/>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='radio'\">\r\n\t\t\t\t\t\t\t\t<radio-group class=\"radio-group\" :name=\"'form_'+idx\">\r\n\t\t\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<radio class=\"radio\" :value=\"item1\"/>{{item1}}\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='checkbox'\">\r\n\t\t\t\t\t\t\t\t<checkbox-group :name=\"'form_'+idx\" class=\"checkbox-group\">\r\n\t\t\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<checkbox class=\"checkbox\" :value=\"item1\"/>{{item1}}\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='selector'\">\r\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" :name=\"'form_'+idx\" :value=\"item.valdata\" :range=\"item.val2\" @change=\"selectorPickerChange\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx] || editorFormdata[idx]===0\"> {{item.val2[editorFormdata[idx]]}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='time'\">\r\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"time\" :name=\"'form_'+idx\" value=\"\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx]\">{{editorFormdata[idx]}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='date'\">\r\n\t\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"date\" :name=\"'form_'+idx\" value=\"\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx]\">{{editorFormdata[idx]}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.key=='upload'\">\r\n\t\t\t\t\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form_'+idx\" :value=\"editorFormdata[idx]\"/>\r\n\t\t\t\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"form-imgbox\" v-if=\"editorFormdata[idx]\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" style=\"z-index: 2;\" @tap=\"removeimg\" :data-idx=\"idx\"><image style=\"display:block\" :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"form-imgbox-img\"><image class=\"image\" :src=\"editorFormdata[idx]\" @click=\"previewImage\" :data-url=\"editorFormdata[idx]\" mode=\"aspectFit\"/></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else class=\"form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}\" @click=\"editorChooseImage\" :data-idx=\"idx\"></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"scoredk flex\" v-if=\"userinfo.score2money > 0\">\r\n\t\t\t\t\t\t<checkbox-group @change=\"scoredk\" class=\"flex\" style=\"width:100%\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t\t<view>{{userinfo.score*1}} {{t('积分')}}可抵扣 <text style=\"color:#e94745\">{{userinfo.scoredk_money*1}}</text> 元</view>\r\n\t\t\t\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\" v-if=\"userinfo.scoredkmaxpercent > 0 && userinfo.scoredkmaxpercent<100\">最多可抵扣订单金额的{{userinfo.scoredkmaxpercent}}%</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\">使用{{t('积分')}}抵扣\r\n\t\t\t\t\t\t\t\t<checkbox value=\"1\" style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t<view style=\"width: 100%; height:182rpx;\"></view>\r\n\t\t\t\t<view class=\"footer flex\">\r\n\t\t\t\t\t<view class=\"text1 flex1\">总计：\r\n\t\t\t\t\t\t<text style=\"font-weight:bold;font-size:36rpx\">￥{{totalprice}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button v-if=\"issubmit\" class=\"op\" style=\"background: #999;\">确认提交</button>\r\n\t\t\t\t\t<button v-else class=\"op\" form-type=\"submit\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">确认提交</button>\r\n\t\t\t\t</view>\r\n\t\t\t</form>\r\n\r\n\t\t\t<view v-if=\"couponvisible\" class=\"popup__container\">\r\n\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text class=\"popup__title-text\">请选择{{t('优惠券')}}</text>\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\r\n\t\t\t\t\t\t\**********=\"handleClickMask\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<couponlist :couponlist=\"couponList\" :choosecoupon=\"true\"\r\n\t\t\t\t\t\t\t:selectedrid=\"couponrid\" @chooseCoupon=\"chooseCoupon\">\r\n\t\t\t\t\t\t</couponlist>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t<view v-if=\"yuyuevisible\" class=\"picker-time\">\r\n\t\t\t<view @tap=\"yuyuevisible=false\" class=\"picker-hide\"></view>\r\n\t\t\t<view class=\"picker-module\">\r\n\t\t\t\t<picker-view :value=\"value\" @change=\"bindChange\" class=\"picker-view\">\r\n\t\t\t\t    <picker-view-column>\r\n\t\t\t\t        <view class=\"flex-xy-center\" v-for=\"(item,index) in years\" :key=\"index\">{{item}}</view>\r\n\t\t\t\t    </picker-view-column>\r\n\t\t\t\t\t\r\n\t\t\t\t    <picker-view-column>\r\n\t\t\t\t        <view class=\"flex-xy-center\" v-for=\"(item,index) in hour\" :key=\"index\">{{item}}</view>\r\n\t\t\t\t    </picker-view-column>\r\n\t\t\t\t    <picker-view-column>\r\n\t\t\t\t        <view class=\"flex-xy-center\" v-for=\"(item,index) in min\" :key=\"index\">{{item}}</view>\r\n\t\t\t\t   </picker-view-column>\r\n\t\t\t\t</picker-view>\t\r\n\t\t\t\t<view class=\"picker-opt flex-xy-center\">\r\n\t\t\t\t\t<view class=\"picker-btn\" @tap=\"yuyuevisible=false\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">取消</view>\r\n\t\t\t\t\t<view class=\"picker-btn\" @tap=\"getTime\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">确定</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\r\n\t\t</block>\r\n\t\t\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\t\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\ttest:'test',\r\n\t\t\t\ttotalprice: '0.00',\r\n\t\t\t\tcouponvisible: false,\r\n\t\t\t\tlinkman: '',\r\n\t\t\t\ttel: '',\r\n\t\t\t\tuserinfo: {},\r\n\t\t\t\teditorFormdata:[],\r\n\t\t\t\tissubmit:false,\r\n\t\t\t\tdateIndex:0,\r\n\t\t\t\tproid:'',\r\n\t\t\t\tykset:{},\r\n\t\t\t\tcouponrid:'',\r\n\t\t\t\tcouponkey:-1,\r\n\t\t\t\tcoupontype:1,\r\n\t\t\t\tcoupon_money:0,\r\n\t\t\t\tcouponList:[],\r\n\t\t\t\tcouponCount:0,\r\n\t\t\t\tproduct:{},\r\n\t\t\t\tworkerinfo:{},\r\n\t\t\t\tformdata:[],\r\n\t\t\t\tgwcnum:1,\r\n\t\t\t\tleveldk_money: 0,\r\n\t\t\t\tformdata_money:0,\r\n\t\t\t\tformdata_label:[],\r\n\t\t\t\tpriceAry:[],\r\n\t\t\t\tyears:[],\r\n\t\t\t\tendyears:[],\r\n\t\t\t\tstartyears:[],\r\n\t\t\t\thour:[],\r\n\t\t\t\tmin:[],\r\n\t\t\t\tyuyuevisible:false,\r\n\t\t\t\tvalue: [0, 0, 0],//时间组件选中的值\r\n\t\t\t\tyytime:[],//接口返回的数据\r\n\t\t\t\topen_type:0,//打开类型 1：打开开始时间 2：打开结束时间\r\n\t\t\t\tstart_time:'',//选中的开始时间\r\n\t\t\t\tend_time:'',//选中的结束时间\r\n\t\t\t\tdifftime:0,//天数差\r\n\t\t\t\tdifftext:'',//天数小时拼接\r\n\t\t\t\tstart_value:[],//开始时间的选中\r\n\t\t\t\tend_value:[],//结束时间的选中\r\n\t\t\t\tusescore: 0,\r\n\t\t\t\tscoredk_money:0,\r\n\t\t\t\tbuynum:1\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tif(this.opt && this.opt.dateIndex) this.dateIndex = this.opt.dateIndex;\r\n\t\t\tif(this.opt && this.opt.proid) this.proid = this.opt.proid;\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetTime(){\r\n\t\t\t\r\n\t\t\t\tvar time = this.years[this.value[0]] + ' ' + this.hour[this.value[1]] + ':' + this.min[this.value[2]]\r\n\t\t\t\t//开始时间选择时\r\n\t\t\t\tif(this.open_type==1){\r\n\t\t\t\t\tthis.start_time = time;\t\r\n\r\n\t\t\t\t\tvar difftime = this.getDateDiff(this.start_time,this.end_time,'day');\r\n\t\t\t\t\tif(difftime < 0){\r\n\t\t\t\t\t\tapp.error('开始时间必须小于结束时间');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t//保存 开始时间的选择的位置\r\n\t\t\t\t\tthis.start_value = this.value;\r\n\t\t\t\t\tif(this.years[this.value[0]+2]){\r\n\t\t\t\t\t\tthis.value = [this.value[0]+2,this.value[1],this.value[2]];\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.value = [this.value[0],this.value[1],this.value[2]];\r\n\t\t\t\t\t}\t\r\n\t\t\t\t\t\r\n\t\t\t\t\tconsole.log(this.start_value,'starut');\r\n\t\t\t\t}\r\n\t\t\t\t//结束时间选择时\r\n\t\t\t\tif(this.open_type==2){\r\n\t\t\t\t\tthis.end_time = time;\r\n\t\t\t\t\tconsole.log(this.end_time);\r\n\t\t\t\t\tvar difftime = this.getDateDiff(this.start_time,this.end_time,'day');\r\n\t\t\t\t\tif(difftime < 0){\r\n\t\t\t\t\t\tapp.error('结束时间必须大于开始时间');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.end_value = this.value;\r\n\t\t\t\t\tconsole.log(this.end_value,'this.end_value');\r\n\t\t\t\t}\r\n\t\t\t\tthis.difftime =  difftime;\r\n\t\t\t\tconsole.log(difftime)\r\n\t\t\t\tthis.computetime();\r\n\t\t\t\tthis.yuyuevisible = false;\r\n\t\t\t},\r\n\t\t\tcomputetime(){\r\n\t\t\t\t//如果天数差大于0时，拼接天数 和计算 是天数 大于4天为一天\r\n\t\t\t\tvar difftime = this.difftime;\r\n\t\t\t\tif(difftime){\r\n\t\t\t\t\tvar diffarr = difftime.toString().split(\".\");\r\n\t\t\t\t\tvar tian = diffarr[0] > 0?diffarr[0]+'天':'';\r\n\t\t\t\t\tvar xiaoshi =diffarr[1] >0?(diffarr[1]/100*24).toFixed(0):''; \r\n\t\t\t\t\tvar xiaoshi_txt = xiaoshi>0?xiaoshi+'时':'';\r\n\t\t\t\t\tthis.difftext =tian+xiaoshi_txt;\r\n\t\t\t\t\tvar zc_hour_day = this.ykset.zc_hour_day?this.ykset.zc_hour_day:4;\r\n\t\t\t\t\tif( xiaoshi >=zc_hour_day && xiaoshi > 0){\r\n\t\t\t\t\t\tthis.gwcnum = parseInt(diffarr[0])+1;\r\n\t\t\t\t\t}else if(xiaoshi < zc_hour_day && xiaoshi > 0){\r\n\t\t\t\t\t\tthis.gwcnum = parseInt(diffarr[0])+0.5;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.gwcnum = diffarr[0];\r\n\t\t\t\t\t}\t\r\n\t\t\t\t\tconsole.log(this.gwcnum,'this.gwcnum');\r\n\t\t\t\t\tthis.calculatePrice();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tbindChange(e){\r\n\t\t\t\t const val = e.detail.value;\r\n\t\t\t\t\t console.log(val);\r\n\t\t\t\t var dateindex = val[0];\r\n\t\t\t\tdateindex = dateindex >=1?1:dateindex;\r\n\r\n\t\t\t\t if(dateindex==0 && val[1] ==0){\r\n\t\t\t\t\tthis.min = this.yytime[0]['min1'];\r\n\t\t\t\t\tthis.hour = this.yytime[0]['hour'];\r\n\t\t\t\t }else{\r\n\t\t\t\t\t this.min = this.yytime[dateindex]['min']; \r\n\t\t\t\t\t this.hour = this.yytime[dateindex]['hour'];\r\n\t\t\t\t }\r\n\t\t\t\t this.value = val;\r\n\t\t\t},\r\n\t\t\ttoStartYuyue(){\r\n\t\t\t\tthis.years =this.startyears;\r\n\t\t\t\tthis.open_type =1;\r\n\t\t\t\tthis.value = this.start_value.length>0?this.start_value:this.value;\r\n\t\t\t\tif(this.value[0] ==0){//是年的第一条数据时 使用0\r\n\t\t\t\t\tthis.min = this.yytime[0]['min1'];\r\n\t\t\t\t\tthis.hour = this.yytime[0]['hour'];\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.min = this.yytime[1]['min'];\r\n\t\t\t\t\tthis.hour = this.yytime[1]['hour'];\r\n\t\t\t\t}\r\n\t\t\t\tthis.yuyuevisible = true;\r\n\t\t\t},\r\n\t\t\ttoEndYuyue(){\r\n\t\t\t\tthis.years = this.endyears;\r\n\t\t\t\tconsole.log(this.years,'years');\r\n\t\t\t\tif(this.start_time ==''){\r\n\t\t\t\t\tapp.error('请先选择开始时间');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.open_type =2;\r\n\t\t\t\tthis.value = this.end_value.length>0?this.end_value:this.value;\r\n\t\t\t\tif(this.value[0] ==0){//是年的第一条数据时 使用0\r\n\t\t\t\t\tthis.min = this.yytime[0]['min1'];\r\n\t\t\t\t\tthis.hour = this.yytime[0]['hour'];\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.min = this.yytime[1]['min'];\r\n\t\t\t\t\tthis.hour = this.yytime[1]['hour'];\r\n\t\t\t\t}\r\n\t\t\t\tthis.yuyuevisible = true;\r\n\t\t\t},\r\n\t\t\tgetdata: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tapp.get('ApiCarHailing/buy', {proid: that.proid,dateIndex:that.dateIndex}, function(res) {\r\n\t\t\t\t\tif(res.status ==0){\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\tif(res.msg =='可供车辆不足'){\r\n\t\t\t\t\t\t\tif(res.tourl){\r\n\t\t\t\t\t\t\t\tconsole.log(res.tourl);\r\n\t\t\t\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\t\t\t\tapp.goto(res.tourl);\r\n\t\t\t\t\t\t\t\t},2000)\r\n\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\t\tapp.goback();\r\n\t\t\t\t\t\t},2000)\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(!that.linkman ){\r\n\t\t\t\t\t\tthat.linkman = res.linkman;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(!that.tel ){\r\n\t\t\t\t\t\tthat.tel = res.tel;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.userinfo = res.userinfo;\r\n\t\t\t\t\tthat.ykset = res.ykset;\r\n\t\t\t\t\tthat.product = res.product;\r\n\t\t\t\t\tthat.workerinfo = res.workerinfo;\r\n\t\t\t\t\tthat.couponList = res.couponList;\r\n\t\t\t\t\tthat.couponCount = res.couponCount;\r\n\t\t\t\t\tthat.formdata = res.formdata;\r\n\t\t\t\t\tthat.yytime = res.yytime;\r\n\t\t\t\t\tthat.years = that.startyears = res.years;\r\n\t\t\t\t\tthat.endyears = res.endyears;\r\n\t\t\t\t\tthat.scoredk_money = that.userinfo.scoredk_money;\r\n\t\t\t\t\tif(that.yytime.length > 0 && that.product.cid==1){\r\n\t\t\t\t\t\tthat.hour = that.yytime[0]['hour']\r\n\t\t\t\t\t\tthat.min= that.yytime[0]['min1']\r\n\t\t\t\t\t\t//计算默认的租车天数\r\n\t\t\t\t\t\tthat.open_type=1;\r\n\t\t\t\t\t\tthat.getTime();\r\n\t\t\t\t\t\tthat.open_type=2;\r\n\t\t\t\t\t\tthat.getTime();\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar leveldk_money = 0;\r\n\t\t\t\t\tvar sell_price = res.product.sell_price;\r\n\t\t\t\t\tvar userinfo = res.userinfo;\r\n\t\t\t\t\tif (that.ykset.discount && userinfo.discount > 0 && userinfo.discount < 10) {\r\n\t\t\t\t\t\tleveldk_money = sell_price * (1 - userinfo.discount * 0.1);\r\n\t\t\t\t\t\tleveldk_money = leveldk_money.toFixed(2);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.leveldk_money = leveldk_money;\r\n\t\t\t\t\tthat.calculatePrice();\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tchooseCoupon: function(e) {\r\n\t\t\t\tvar couponrid = e.rid;\r\n\t\t\t\tvar couponkey = e.key;\r\n\t\t\t\tif (couponrid == this.couponrid) {\r\n\t\t\t\t\tthis.couponkey = -1;\r\n\t\t\t\t\tthis.couponrid = '';\r\n\t\t\t\t\tthis.coupontype = 1;\r\n\t\t\t\t\tthis.coupon_money = 0;\r\n\t\t\t\t\tthis.couponvisible = false;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar couponList = this.couponList;\r\n\t\t\t\t\tvar coupon_money = couponList[couponkey]['money'];\r\n\t\t\t\t\tvar coupontype = couponList[couponkey]['type'];\r\n\t\t\t\t\tif(coupontype == 10){\r\n\t\t\t\t\t\tcoupon_money = this.product.sell_price * (100 - couponList[couponkey]['discount']) * 0.01;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.couponkey = couponkey;\r\n\t\t\t\t\tthis.couponrid = couponrid;\r\n\t\t\t\t\tthis.coupontype = coupontype;\r\n\t\t\t\t\tthis.coupon_money = coupon_money;\r\n\t\t\t\t\tthis.couponvisible = false;\r\n\t\t\t\t}\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\tshowCouponList: function(e) {\r\n\t\t\t\tthis.couponvisible = true;\r\n\t\t\t},\r\n\t\t\thandleClickMask: function() {\r\n\t\t\t\tthis.couponvisible = false;\r\n\t\t\t},\r\n\t\t\t//计算价格\r\n\t\t\tcalculatePrice: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar product_price = parseFloat(that.product.sell_price*this.gwcnum) ;\r\n\t\t\t\tproduct_price = parseFloat(product_price * this.buynum)\r\n\t\t\t\tvar coupon_money = parseFloat(that.coupon_money); //-优惠券抵扣 \r\n\t\t\t\tvar leveldk_money = parseFloat(that.leveldk_money); //-会员折扣\r\n\t\t\t\tvar formdata_money =  parseFloat(that.formdata_money); //表单价格\r\n\t\t\t\tif(that.coupontype==3) coupon_money = product_price\r\n\t\t\t\tif (that.usescore) {\r\n\t\t\t\t  var scoredk_money = parseFloat(that.scoredk_money); //-积分抵扣\r\n\t\t\t\t} else {\r\n\t\t\t\t  var scoredk_money = 0;\r\n\t\t\t\t}\r\n\t\t\t\tvar totalprice = product_price - coupon_money - leveldk_money\r\n\t\t\t\tif(formdata_money > 0){\r\n\t\t\t\t\ttotalprice =parseFloat( totalprice + formdata_money);\r\n\t\t\t\t}\r\n\t\t\t\tif (totalprice < 0) totalprice = 0;\r\n\t\t\t\tvar scoredkmaxpercent = parseFloat(that.userinfo.scoredkmaxpercent); //最大抵扣比例\r\n\t\t\t\t\r\n\t\t\t\tif (scoredk_money > 0 && scoredkmaxpercent > 0 && scoredkmaxpercent < 100 && scoredk_money > totalprice * scoredkmaxpercent * 0.01) {\r\n\t\t\t\t  scoredk_money = totalprice * scoredkmaxpercent * 0.01 ;\r\n\t\t\t\t  totalprice = totalprice - scoredk_money;\r\n\t\t\t\t}\r\n\t\t\t\ttotalprice = totalprice.toFixed(2);\r\n\t\t\t\tthat.totalprice = totalprice;\r\n\t\t\t},\r\n\t\t\t//减\r\n\t\t\tgwcminus: function (e) {\r\n\t\t\t\tthis.gwcnum =this.gwcnum <= 1?this.gwcnum: this.gwcnum - 1;\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\t//加\r\n\t\t\tgwcplus: function (e) {\r\n\t\t\t\tthis.gwcnum = this.gwcnum + 1;\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\t//输入\r\n\t\t\tgwcinput: function (e) {\r\n\t\t\t\tvar gwcnum = parseInt(e.detail.value);\r\n\t\t\t\tif (gwcnum < 1) return 1;\r\n\t\t\t\tthis.gwcnum = gwcnum;\r\n\t\t\t\tif(this.gwcnum >=1){\r\n\t\t\t\t\tthis.calculatePrice();\r\n\t\t\t\t}\t\r\n\t\t\t},\r\n\t\t\t//减\r\n\t\t\tbuynumminus: function (e) {\r\n\t\t\t\tconsole.log(e);\r\n\t\t\t\tthis.buynum =this.buynum <= 1?this.buynum: this.buynum - 1;\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\t//加\r\n\t\t\tbuynumplus: function (e) {\r\n\t\t\t\tvar buynum = this.buynum + 1;\r\n\t\t\t\tif(buynum > this.product.car_num){\r\n\t\t\t\t\tbuynum = this.product.car_num;\r\n\t\t\t\t}\r\n\t\t\t\tthis.buynum = buynum;\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t},\r\n\t\t\t//输入\r\n\t\t\tbuynuminput: function (e) {\r\n\t\t\t\tvar buynum = parseInt(e.detail.value);\r\n\t\t\t\tif (buynum < 1) return 1;\r\n\t\t\t\tthis.buynum = buynum;\r\n\t\t\t\tif(this.buynum >=1){\r\n\t\t\t\t\tthis.calculatePrice();\r\n\t\t\t\t}\t\r\n\t\t\t},\r\n\t\t\t//积分抵扣\r\n\t\t\tscoredk: function (e) {\r\n\t\t\t  var usescore = e.detail.value[0];\r\n\t\t\t  if (!usescore) usescore = 0;\r\n\t\t\t  this.usescore = usescore;\r\n\t\t\t  this.calculatePrice();\r\n\t\t\t},\r\n\t\t\t//提交并支付\r\n\t\t\ttopay: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar linkman = this.linkman;\r\n\t\t\t\tvar tel = this.tel;\r\n\t\t\t\tvar proid = this.proid;\r\n\t\t\t\tvar couponrid = this.couponrid;\r\n\t\t\t\tvar dateIndex = this.dateIndex;\r\n\t\t\t\tif(!linkman || !tel) {\r\n\t\t\t\t\tapp.error('请填写联系人及联系电话');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar formdata_fields = that.formdata;\r\n\t\t\t\tvar formdata = e.detail.value;\r\n\t\t\t\tvar formdata_money = that.formdata_money;\r\n\t\t\t\tvar formdata_title = that.formdata_title;\r\n\t\t\t\tvar start_time = that.start_time;\r\n\t\t\t\tvar end_time = that.end_time;\r\n\t\t\t\tif(that.product.cid ==1 && (start_time =='' || end_time =='' || that.gwcnum<1) ){\r\n\t\t\t\t\tapp.error('租车时间不得少于4个小时');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar newformdata = {};\r\n\t\t\t\tfor (var j = 0; j < formdata_fields.length;j++){\r\n\t\t\t\t\tvar thisfield = 'form_' + j;\r\n\t\t\t\t\tif (formdata_fields[j].val3 == 1 && (formdata[thisfield] === '' || formdata[thisfield] === undefined || formdata[thisfield].length==0)){\r\n\t\t\t\t\t\t\tapp.alert(formdata_fields[j].val1+' 必填');return;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (formdata_fields[j].key == 'selector') {\r\n\t\t\t\t\t\tconsole.log(formdata[thisfield],'---');\r\n\t\t\t\t\t\t\tformdata[thisfield] = formdata_fields[j].valdata[formdata[thisfield]]\r\n\t\t\t\t\t}\r\n\t\t\t\t\tnewformdata['form'+j] = formdata[thisfield];\r\n\t\t\t\t}\r\n\t\t\t\t var usescore = this.usescore;\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiCarHailing/createOrder', {linkman: linkman,tel: tel,formdata:newformdata,proid:proid,dateIndex:dateIndex,couponrid:couponrid,num:that.gwcnum,start_time:start_time,end_time:end_time,usescore: usescore,buynum:that.buynum}, function(res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif(res.status==1 && res.payorderid){\r\n\t\t\t\t\t\t\tthat.issubmit = true\t\r\n\t\t\t\t\t\t\tapp.goto('/pagesExt/pay/pay?id=' + res.payorderid);\r\n\t\t\t\t\t}else if(res.status==1 && !res.payorderid){\r\n\t\t\t\t\t\t\tapp.alert('预约成功',function(){\r\n\t\t\t\t\t\t\t\tapp.goto('orderlist');\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t}else\tif(res.status == 0){\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\tconsole.log(res.msg);\r\n\t\t\t\t\t\tif(res.msg =='可供车辆不足'){\r\n\t\t\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\t\t\tif(that.ykset.tourl){\r\n\t\t\t\t\t\t\t\t\tapp.goto(that.ykset.tourl)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\teditorChooseImage: function (e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar editorFormdata = that.editorFormdata;\r\n\t\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\t\tapp.chooseImage(function(data){\r\n\t\t\t\t\teditorFormdata[idx] = data[0];\r\n\t\t\t\t\tthat.editorFormdata = editorFormdata\r\n\t\t\t\t\tthat.test = Math.random();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tremoveimg:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar pics = that.editorFormdata\r\n\t\t\t\tpics.splice(idx,1);\r\n\t\t\t\tthat.editorFormdata = pics;\r\n\t\t\t},\r\n\t\t\teditorBindPickerChange:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar val = e.detail.value;\r\n\t\t\t\tvar editorFormdata = that.editorFormdata;\r\n\t\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\t\teditorFormdata[idx] = val;\r\n\t\t\t\tthat.editorFormdata = editorFormdata;\r\n\t\t\t\tthat.test = Math.random();\r\n\t\t\t},\r\n\t\t\tselectorPickerChange:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar val = e.detail.value;\r\n\t\t\t\tvar editorFormdata = that.editorFormdata;\r\n\t\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\t\teditorFormdata[idx] = val;\r\n\t\t\t\tthat.editorFormdata = editorFormdata;\r\n\t\t\t\tthat.test = Math.random();\r\n\t\t\t\tthat.formdata_money = 0;\r\n\t\t\t\tvar valdata = that.formdata[idx].valdata;\r\n\t\t\t\t\r\n\t\t\t\tthat.priceAry[idx] = valdata[val];\r\n\t\t\t\tlet ary = [];\r\n\t\t\t\tfor(let i=0;i<that.priceAry.length;i++){\r\n\t\t\t\t\tif(that.priceAry[i]){\r\n\t\t\t\t\t\tary.push(parseFloat(that.priceAry[i].value))\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tvar totalPrice = that.getSum(ary);\r\n\t\t\t\tthat.formdata_money = totalPrice? parseFloat(totalPrice):0;\r\n\t\t\t\tthat.calculatePrice();\r\n\t\t\t},\r\n\t\t\tgetSum(arr) {\r\n\t\t\t  var s = 0;\r\n\t\t\t  for (var i=arr.length-1; i>=0; i--) {\r\n\t\t\t\tif(!isNaN(parseFloat(arr[i]))){\r\n\t\t\t\t\t s += arr[i];\r\n\t\t\t\t}   \r\n\t\t\t  }\r\n\t\t\t  return s;\r\n\t\t\t},\r\n\t\t\tinputLinkman: function (e) {\r\n\t\t\t\tthis.linkman = e.detail.value\r\n\t\t\t},\r\n\t\t\tinputTel: function (e) {\r\n\t\t\t\tthis.tel = e.detail.value\r\n\t\t\t},\r\n\t\t\tgetDateDiff:function(startTime, endTime, diffType) {\r\n\t\t\t\tif(endTime ==''){\r\n\t\t\t\t\tendTime = startTime;\r\n\t\t\t\t}\r\n\t\t\t    //将xxxx-xx-xx的时间格式，转换为 xxxx/xx/xx的格式 \r\n\t\t\t    startTime = startTime.replace(/\\-/g, \"/\");\r\n\t\t\t    endTime = endTime.replace(/\\-/g, \"/\");\r\n\t\t\t    //将计算间隔类性字符转换为小写\r\n\t\t\t    diffType = diffType.toLowerCase();\r\n\t\t\t    var sTime =new Date(startTime); //开始时间\r\n\t\t\t    var eTime =new Date(endTime); //结束时间\r\n\t\t\t\tconsole.log(eTime.getTime());\r\n\t\t\t    //作为除数的数字\r\n\t\t\t    var timeType =1;\r\n\t\t\t    switch (diffType) {\r\n\t\t\t        case\"second\":\r\n\t\t\t            timeType =1000;\r\n\t\t\t        break;\r\n\t\t\t        case\"minute\":\r\n\t\t\t            timeType =1000*60;\r\n\t\t\t        break;\r\n\t\t\t        case\"hour\":\r\n\t\t\t            timeType =1000*3600;\r\n\t\t\t        break;\r\n\t\t\t        case\"day\":\r\n\t\t\t            timeType =1000*3600*24;\r\n\t\t\t        break;\r\n\t\t\t        default:\r\n\t\t\t        break;\r\n\t\t\t    }\r\n\t\t\t    return parseFloat((eTime.getTime() - sTime.getTime()) / parseInt(timeType)).toFixed(2);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.redBg{color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx; width: auto; display: inline-block; margin-top: 4rpx;}\r\n.address-add {width: 94%;margin: 20rpx 3%;background: #fff;border-radius: 20rpx;padding: 20rpx 3%;min-height: 140rpx;}\r\n.address-add .f1 {margin-right: 20rpx}\r\n.address-add .f1 .img {width: 66rpx;height: 66rpx;}\r\n.address-add .f2 {color: #666;}\r\n.address-add .f3 {width: 26rpx;height: 26rpx;}\r\n.linkitem {width: 100%;padding: 1px 0;background: #fff;display: flex;align-items: center}.cf3 {width: 200rpx;height: 26rpx;display: block;\r\n    text-align: right;}\r\n.linkitem .f1 {width: 160rpx;color: #111111}\r\n.linkitem .input {height: 50rpx;padding-left: 10rpx;color: #222222;font-weight: bold;font-size: 28rpx;flex: 1}\r\n.buydata {width: 94%;margin: 0 3%;margin-bottom: 20rpx;}\r\n.btitle {width: 100%;padding: 20rpx 20rpx;display: flex;align-items: center;color: #111111;font-weight: bold;font-size: 30rpx}\r\n.btitle .img {width: 34rpx;height: 34rpx;margin-right: 10rpx}\r\n.bcontent {width: 100%;padding: 0 20rpx;background: #fff;border-radius: 20rpx;}\r\n.bcontent2 {width: 100%;padding: 0 20rpx; margin-top: 30rpx;background: #fff;border-radius: 20rpx;}\r\n.product {width: 100%;border-bottom: 1px solid #f4f4f4}\r\n.product .item {width: 100%;padding: 20rpx 0;background: #fff;border-bottom: 1px #ededed dashed;}\r\n.product .item:last-child {border: none}\r\n.product .info {padding-left: 20rpx;}\r\n.product .info .f1 {color: #222222;font-weight: bold;font-size: 26rpx;line-height: 36rpx;margin-bottom: 10rpx;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}\r\n.product .info .f2 {color: #999999;font-size: 24rpx}\r\n.product .info .f3 {color: #FF4C4C;font-size: 28rpx;display: flex;align-items: center;margin-top: 10rpx}\r\n.product image {width: 140rpx;height: 140rpx}\r\n.freight {width: 100%;padding: 20rpx 0;background: #fff;display: flex;flex-direction: column;}\r\n.freight .f1 {color: #333;margin-bottom: 10rpx}\r\n.freight .f2 {color: #111111;text-align: right;flex: 1}\r\n.freight .f3 {width: 24rpx;height: 28rpx;}\r\n.freighttips {color: red;font-size: 24rpx;}\r\n.freight-ul {width: 100%;display: flex;}\r\n.freight-li {flex-shrink: 0;display: flex;background: #F5F6F8;border-radius: 24rpx;color: #6C737F;font-size: 24rpx;text-align: center;height: 48rpx;line-height: 48rpx;padding: 0 28rpx;margin: 12rpx 10rpx 12rpx 0}\r\n\r\n.price {width: 100%;padding: 20rpx 0;background: #fff;display: flex;align-items: center}\r\n.price .f1 {color: #333}\r\n.price .f2 {color: #111;font-weight: bold;text-align: right;flex: 1}\r\n.price .f3 {width: 24rpx;height: 24rpx;}\r\n/* 购买数量*/\r\n.price .addnum {font-size: 30rpx;color: #666;width:auto;display:flex;align-items:center}\r\n.price .addnum .plus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n.price .addnum .minus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n.price .addnum .img{width:24rpx;height:24rpx}\r\n.price .addnum .input{flex:1;width:50rpx;border:0;text-align:center;color:#2B2B2B;font-size:28rpx;margin: 0 15rpx;}\r\n\r\n.scoredk {width: 100%;margin-bottom: 20rpx;border-radius: 20rpx;padding: 24rpx 20rpx;background: #fff;display: flex;align-items: center;margin-top: 20rpx;}\r\n.scoredk .f1 {color: #333333}\r\n.scoredk .f2 {color: #999999;text-align: right;flex: 1}\r\n.remark {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center}\r\n.remark .f1 {color: #333;width: 200rpx}\r\n.remark input {border: 0px solid #eee;height: 70rpx;padding-left: 10rpx;text-align: right}\r\n.footer {width: 100%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;padding: 0 20rpx;display: flex;align-items: center;z-index: 8}\r\n.footer .text1 {height: 110rpx;line-height: 110rpx;color: #2a2a2a;font-size: 30rpx;}\r\n.footer .text1 text {color: #e94745;font-size: 32rpx;}\r\n.footer .op {width: 200rpx;height: 80rpx;line-height: 80rpx;color: #fff;text-align: center;font-size: 30rpx;border-radius: 44rpx}\r\n.storeitem {width: 100%;padding: 20rpx 0;display: flex;flex-direction: column;color: #333}\r\n.storeitem .panel {width: 100%;height: 60rpx;line-height: 60rpx;font-size: 28rpx;color: #333;margin-bottom: 10rpx;display: flex}\r\n.storeitem .panel .f1 {color: #333}\r\n.storeitem .panel .f2 {color: #111;font-weight: bold;text-align: right;flex: 1}\r\n.storeitem .radio-item {display: flex;width: 100%;color: #000;align-items: center;background: #fff;border-bottom: 0 solid #eee;padding: 8rpx 20rpx;}\r\n.storeitem .radio-item:last-child {border: 0}\r\n.storeitem .radio-item .f1 {color: #666;flex: 1}\r\n.storeitem .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-left: 30rpx}\r\n.storeitem .radio .radio-img {width: 100%;height: 100%}\r\n.pstime-item {display: flex;border-bottom: 1px solid #f5f5f5;padding: 20rpx 30rpx;}\r\n.pstime-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}\r\n.pstime-item .radio .radio-img {width: 100%;height: 100%}\r\n.cuxiao-desc {width: 100%}\r\n.cuxiao-item {display: flex;padding: 0 40rpx 20rpx 40rpx;}\r\n.cuxiao-item .type-name {font-size: 28rpx;color: #49aa34;margin-bottom: 10rpx;flex: 1}\r\n.cuxiao-item .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}\r\n.cuxiao-item .radio .radio-img {width: 100%;height: 100%}\r\n\r\n.form-item {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center;justify-content:space-between}\r\n.form-item .label {color: #333;width: 200rpx;flex-shrink:0}\r\n.form-item .radio{transform:scale(.7);}\r\n.form-item .checkbox{transform:scale(.7);}\r\n.form-item .input {border:0px solid #eee;height: 70rpx;padding-left: 10rpx;text-align: right;flex:1}\r\n.form-item .textarea{height:140rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}\r\n.form-item .radio-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\r\n.form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n.form-item .radio2{display:flex;align-items:center;}\r\n.form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}\r\n.form-item .checkbox-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\r\n.form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n.form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}\r\n.form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}\r\n.form-item .picker{height: 70rpx;line-height:70rpx;flex:1;text-align:right}\r\n\r\n.form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}\r\n.form-imgbox-close .image{width:100%;height:100%}\r\n.form-imgbox-img{display: block;width:180rpx;height:180rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.form-imgbox-img>.image{width:100%;height:100%}\r\n.form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.form-uploadbtn{position:relative;height:180rpx;width:180rpx;margin-right: 16rpx;margin-bottom:10rpx;}\r\n\r\n.member_search{width:100%;padding:0 40rpx;display:flex;align-items:center}\r\n.searchMemberButton{height:60rpx;background-color: #007AFF;border-radius: 10rpx;width: 160rpx;line-height: 60rpx;color: #fff;text-align: center;font-size: 28rpx;display: block;}\r\n.memberlist{width:100%;padding:0 40rpx;height: auto;margin:20rpx auto;}\r\n.memberitem{display:flex;align-items:center;border-bottom:1px solid #f5f5f5;padding:20rpx 0}\r\n.memberitem image{display: block;height:100rpx;width:100rpx;margin-right:20rpx;}\r\n.memberitem .t1{color:#333;font-weight:bold}\r\n.memberitem .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right: 30rpx}\r\n.memberitem .radio .radio-img {width: 100%;height: 100%}\r\n\r\n.checkMem{ display: inline-block; }\r\n.checkMem p{ height: 30px; width: 100%; display: inline-block; }\r\n.placeholder{  font-size: 26rpx;line-height: 80rpx;}\r\n.selected-item span{ font-size: 26rpx !important;}\r\n.orderinfo{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}\r\n.orderinfo .item:last-child{ border-bottom: 0;}\r\n.orderinfo .item .t1{width:200rpx;flex-shrink:0}\r\n.orderinfo .item .t2{flex:1;text-align:right}\r\n.orderinfo .item .red{color:red}\r\n\r\n.price .choose {color: #666;width:auto;display:flex;align-items:center}\r\n.price .choose .choosetime {width:215rpx;height:48rpx;display:flex;align-items:center;justify-content:center; font-size: 24rpx;font-weight: normal;}\r\n.price .choose .difftext{flex:1;border:0;text-align:center;color:#2B2B2B;font-size:24rpx;margin: 0 15rpx;width: 95rpx;padding: 0;margin: 0;}\r\n.picker-time{\r\n\t\tposition: fixed;\r\n\t\theight: 100%;\r\n\t\twidth: 100%;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tbackground: rgba(0, 0, 0, 0.7);\r\n\t\tz-index: 1000;\r\n\t}\r\n\t.picker-hide{\r\n\t\tposition: absolute;\r\n\t\theight: 100%;\r\n\t\twidth: 100%;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t}\r\n\t.picker-module{\r\n\t\tposition:absolute;\r\n\t\tbottom: 0;\r\n\t\tpadding: 50rpx 0;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\t.picker-view {\r\n\t\twidth: 750rpx;\r\n\t\theight: 600rpx;\r\n\t}\r\n\t.picker-opt{\r\n\t\tposition: relative;\r\n\t\tpadding: 30rpx 0 0 0;\r\n\t}\r\n\t.picker-btn {\r\n\t\twidth: 200rpx;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 30rpx;\r\n\t\tborder-radius: 44rpx;\r\n\t\tmargin: 0 50rpx;\r\n\t}\r\n/*时间范围*/\r\n.datetab{ display: flex; border:1px solid red; width: 200rpx; text-align: center;}\r\n.order-tab{ }\r\n.order-tab2{display:flex;width:auto;min-width:100%}\r\n.order-tab2 .item{width:auto;font-size:28rpx;font-weight:bold;text-align: center; color:#999999;overflow: hidden;flex-shrink:0;flex-grow: 1; display: flex; flex-direction: column; justify-content: center; align-items: center;}\r\n.order-tab2 .item .datetext{ line-height: 60rpx; height:60rpx;}\r\n.order-tab2 .item .datetext2{ line-height: 60rpx; height:60rpx;font-size: 22rpx;}\r\n.order-tab2 .on{color:#222222;}\r\n.order-tab2 .after{display:none;margin-left:-10rpx;bottom:5rpx;height:6rpx;border-radius:1.5px;width:70rpx}\r\n.order-tab2 .on .after{display:block}\r\n.daydate{ padding:20rpx; flex-wrap: wrap; overflow-y: scroll; height:400rpx; }\r\n.daydate .date{ width:20%;text-align: center;line-height: 60rpx;height: 60rpx; margin-top: 30rpx;}\r\n.daydate .on{ background:red; color:#fff;}\r\n.daydate .hui{ border:1px solid #f0f0f0; background:#f0f0f0;border-radius: 5rpx;}\r\n.tobuy{flex:1;height: 72rpx; line-height: 72rpx; color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold;width:90%;margin:20rpx 5%;border-radius:36rpx;}\r\n\r\n</style>\r\n", "import mod from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213030864\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}