{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/orderdetail.vue?1eeb", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/orderdetail.vue?82b4", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/orderdetail.vue?4973", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/orderdetail.vue?73b1", "uni-app:///carhailing/orderdetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/orderdetail.vue?c1af", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/carhailing/orderdetail.vue?4296"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "prodata", "djs", "iscommentdp", "detail", "workerinfo", "storeinfo", "lefttime", "selectExpressShow", "express_content", "isagree", "sysset", "showdesc", "desc", "desc_status", "url", "onLoad", "onPullDownRefresh", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "id", "interval", "showdescClick", "canceldescClick", "hidedescClick", "isagreeChange", "console", "torefund", "getdjs", "todel", "orderid", "setTimeout", "toclose", "orderCollect", "showhxqr", "closeHxqr", "openLocation", "uni", "latitude", "longitude", "name", "scale", "openMendian", "logistics", "hideSelectExpressDialog"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AAC6M;AAC7M,gBAAgB,iNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrFA;AAAA;AAAA;AAAA;AAA0zB,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiO90B;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA,oCACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEAA;QAEA;UACAG;YACAH;YACAA;UACA;QACA;QACAA;MACA;IACA;IACAI;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;MACAC;IACA;IACAC;MACA;MACA;MACA;MACAD;MACA;QACA;UACA;UACA;QACA;MACA;MACAP;IACA;IACAS;MACA;MACA;MAEA;QACAV;MACA;QACA;QACA;QACA;QACA;QACAA;MACA;IACA;IACAW;MACA;MACA;MACAV;QACAA;QACAA;UAAAW;QAAA;UACAX;UACAA;UACAY;YACAZ;UACA;QACA;MACA;IACA;IACAa;MACA;MACA;MACAb;QACAA;QACAA;UAAAW;QAAA;UACAX;UACAA;UACAY;YACAb;UACA;QACA;MACA;IACA;IACAe;MACA;MACA;MACAd;QACAA;QACAA;UAAAW;QAAA;UACAX;UACAA;UACAY;YACAb;UACA;QACA;MACA;IACA;IAEAgB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;MACAvB;IACA;IACAwB;MACA;MACA;MACAxB;IACA;IACAyB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1ZA;AAAA;AAAA;AAAA;AAAiqC,CAAgB,smCAAG,EAAC,C;;;;;;;;;;;ACArrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "carhailing/orderdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './carhailing/orderdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderdetail.vue?vue&type=template&id=74e2b8d0&\"\nvar renderjs\nimport script from \"./orderdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./orderdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"carhailing/orderdetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=template&id=74e2b8d0&\"", "var components\ntry {\n  components = {\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.detail.formdata.length : null\n  var m0 = _vm.isload && _vm.detail.leveldk_money > 0 ? _vm.t(\"会员\") : null\n  var m1 = _vm.isload && _vm.detail.coupon_money > 0 ? _vm.t(\"优惠券\") : null\n  var m2 = _vm.isload && _vm.detail.scoredk_money > 0 ? _vm.t(\"积分\") : null\n  var m3 =\n    _vm.isload && _vm.detail.status == 0 && _vm.detail.paytypeid != 5\n      ? _vm.t(\"color1\")\n      : null\n  var m4 =\n    _vm.isload && _vm.detail.status == 0 && _vm.detail.paytypeid == 5\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.isload &&\n    _vm.detail.status == 2 &&\n    _vm.detail.balance_pay_status == 0 &&\n    _vm.detail.balance_price > 0\n      ? _vm.t(\"color1\")\n      : null\n  var m6 = _vm.isload && _vm.showdesc ? _vm.t(\"color1\") : null\n  var m7 = _vm.isload && _vm.showdesc ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"ordertop flex\" :style=\"'background:url('+pre_url+'/static/img/orderbg.png);background-size:100%'\">\n\t\t\t<view class=\"f1 \" v-if=\"detail.status==0 && detail.refund_status ==0\">\n\t\t\t\t<view class=\"t1\">等待买家付款</view>\n\t\t\t\t<view class=\"t2\" v-if=\"djs\">剩余时间：{{djs}}</view>\t\t\t\t\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==1 && detail.refund_status ==0\">\n\t\t\t\t<view class=\"t2\">订单已付款</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==3 && detail.refund_status ==0\">\n\t\t\t\t<view class=\"t1\">订单已完成</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==4 && detail.refund_status ==0\">\n\t\t\t\t<view class=\"t1\">订单已取消</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.refund_status ==1\">\n\t\t\t\t<view class=\"t1\">退款审核中</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.refund_status ==2\">\n\t\t\t\t<view class=\"t1\">订单已退款</view>\n\t\t\t</view>\n\t\t\t<view class=\"orderx\"><image :src=\"pre_url+'/static/img/orderx.png'\"></view>\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo orderinfotop\">\n\t\t\t<view class=\"title\">订单信息</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单编号</text>\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{detail.ordernum}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.cid =='2'\">\n\t\t\t\t<text class=\"t1\">乘车日期</text>\n\t\t\t\t<text class=\"t2\">{{detail.yy_date}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.cid =='1'\">\n\t\t\t\t<text class=\"t1\" style=\"flex: 0.6;\">租车时间</text>\n\t\t\t\t<text class=\"t2\" style=\"flex: 2;\">{{detail.zc_start_time}} 至 {{detail.zc_end_time}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.cid =='3'\">\n\t\t\t\t<text class=\"t1\">包车时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.yy_date}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.cid =='1'\">\n\t\t\t\t<text class=\"t1\">租车天数</text>\n\t\t\t\t<text class=\"t2\">x {{detail.num}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.cid =='3'\">\n\t\t\t\t<text class=\"t1\">包车数量</text>\n\t\t\t\t<text class=\"t2\">x {{detail.num}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\" v-if=\"(detail.formdata).length > 0\">\n\t\t\t<view class=\"item\" v-for=\"item in detail.formdata\" :key=\"index\">\n\t\t\t\t<text class=\"t1\">{{item[0]}}</text>\n\t\t\t\t<view class=\"t2\" v-if=\"item[2]=='upload'\"><image :src=\"item[1]\" style=\"width:400rpx;height:auto\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"item[1]\"/></view>\n\t\t\t\t<text class=\"t2\" v-else user-select=\"true\" selectable=\"true\">{{item[1]}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- <view class=\"btitle flex-y-center\" v-if=\"detail.bid>0\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id=' + detail.bid\">\n\t\t\t<image :src=\"detail.binfo.logo\" style=\"width:36rpx;height:36rpx;\"></image>\n\t\t\t<view class=\"flex1\" decode=\"true\" space=\"true\" style=\"padding-left:16rpx\">{{detail.binfo.name}}</view>\n\t\t</view> -->\n\t\t<view class=\"product\">\n\t\t\t<view class=\"title\">约车信息</view>\n\t\t\t<view class=\"content\">\n\t\t\t\t<view @tap=\"goto\" :data-url=\"'product?id=' + detail.proid\">\n\t\t\t\t\t<image :src=\"detail.propic\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t<text class=\"t1\">{{detail.proname}}</text>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"t3\"><text class=\"x1 flex1\">￥{{detail.product_price}} <text v-if=\"detail.cid =='2'\"> /人</text><text v-if=\"detail.cid =='1' || detail.cid =='3'\"> /天</text> </text></view>\n\t\t\t\t\t<!-- <view class=\"t4 flex flex-x-bottom\">\n\t\t\t\t\t\t<view class=\"btn3\" v-if=\"detail.status==3 && prolist.iscomment==0\" @tap.stop=\"goto\" :data-url=\"'comment?oid=' + prolist.id\">去评价</view>\n\t\t\t\t\t\t<view class=\"btn3\" v-if=\"detail.status==3 && prolist.iscomment==1\" @tap.stop=\"goto\" :data-url=\"'comment?oid=' + prolist.id\">查看评价</view>\n\t\t\t\t\t</view> -->\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">应付金额</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.product_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.leveldk_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}折扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.leveldk_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.manjian_money > 0\">\n\t\t\t\t<text class=\"t1\">满减活动</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.manjian_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.freight_type==1 && detail.freightprice > 0\">\n\t\t\t\t<text class=\"t1\">服务费</text>\n\t\t\t\t<text class=\"t2 red\">+¥{{detail.freight_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.freight_time\">\n\t\t\t\t<text class=\"t1\">{{detail.freight_type!=1?'配送':'提货'}}时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.freight_time}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.coupon_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('优惠券')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.coupon_money}}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"item\" v-if=\"detail.scoredk_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('积分')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.scoredk_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">实付款</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.totalprice}}</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0\">未付款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==1\">已付款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==2\">服务中</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==3\">已完成</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==4\">已关闭</text>\n\t\t\t\t<text class=\"\" v-if=\"detail.refundCount\" style=\"margin-left: 8rpx;\">有退款({{detail.refundCount}})</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\n\t\t\t\t<text class=\"t1\">退款状态</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==1\">审核中,¥{{detail.refund_money}}</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==2\">已退款,¥{{detail.refund_money}}</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==3\">已驳回,¥{{detail.refund_money}}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"item\" v-if=\"detail.balance_price>0\">\n\t\t\t\t<text class=\"t1\">尾款</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.balance_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.balance_price>0\">\n\t\t\t\t<text class=\"t1\">尾款状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.balance_pay_status==1\">已支付</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.balance_pay_status==0\">未支付</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytypeid!='4' && detail.paytime\">\n\t\t\t\t<text class=\"t1\">支付时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.paytypeid\">\n\t\t\t\t<text class=\"t1\">支付方式</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytype}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>1 && detail.send_time\">\n\t\t\t\t<text class=\"t1\">派单时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.send_time}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>1 && detail.addmoney>0\">\n\t\t\t\t<text class=\"t1\">补差价</text>\n\t\t\t\t<text class=\"t2 red\">￥{{detail.addmoney}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status==3 && detail.collect_time\">\n\t\t\t\t<text class=\"t1\">完成时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.collect_time}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"title\">客户信息</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">姓名</text>\n\t\t\t\t<text class=\"t2\">{{detail.linkman}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">手机号</text>\n\t\t\t\t<text class=\"t2\">{{detail.tel}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view style=\"width:100%;height:180rpx\"></view>\n\t\t<view class=\"desc\" v-if=\"desc_status\">\n\t\t\t<checkbox-group @change=\"isagreeChange\" style=\"display: inline-block;\">\n\t\t\t    <checkbox style=\"transform: scale(0.6)\"  value=\"1\" :checked=\"isagree\"/>\n\t\t\t\t我已阅读\n\t\t\t    <text style=\"color: #3388FF\" @tap=\"showdescClick\">《退款说明》</text>\n\t\t\t</checkbox-group>\n\t\t</view>\n\t\t<view class=\"bottom\">\n\t\t\t<block v-if=\"detail.status==0\">\n\t\t\t\t<view class=\"btn2\" @tap=\"toclose\" :data-id=\"detail.id\">关闭订单</view>\n\t\t\t\t<view class=\"btn1\" v-if=\"detail.paytypeid != 5\" :style=\"{background:t('color1')}\" @tap=\"goto\" :data-url=\"'/pagesExt/pay/pay?id=' + detail.payorderid\">去付款</view>\n\t\t\t\t<view class=\"btn1\" v-if=\"detail.paytypeid == 5\" :style=\"{background:t('color1')}\" @tap=\"goto\" :data-url=\"'/pages/pay/transfer?id=' + detail.payorderid\">上传付款凭证</view>\n\t\t\t</block>\n\t\t\t<block v-if=\"detail.status==1 && detail.totalprice>0\">\n\t\t\t\t<block v-if=\"detail.paytypeid!='4' && detail.refund_status==0 || detail.refund_status==3\">\n\t\t\t\t\t<view class=\"btn2\" @tap=\"torefund\" :data-id=\"detail.id\">退款</view>\n\t\t\t\t</block>\n\t\t\t</block>\n\t\t\t<block v-if=\"detail.status==2\">\n\t\t\t\t<!-- <view class=\"btn2\" v-if=\"detail.paytypeid=='4'\">{{codtxt}}</view> -->\n\t\t\t\t<view v-if=\"detail.balance_pay_status == 0 && detail.balance_price > 0\" class=\"btn1\" :style=\"{background:t('color1')}\" @tap.stop=\"goto\" :data-url=\"'/pagesExt/pay/pay?id=' + detail.balance_pay_orderid\">支付尾款</view>\n\t\t\t</block>\n\t\t\t<block v-if=\"detail.status==3 || detail.status==4\">\n\t\t\t\t<view class=\"btn2\" @tap=\"todel\" :data-id=\"detail.id\">删除订单</view>\n\t\t\t</block>\n\t\t</view>\n\t\t<view v-if=\"showdesc\" class=\"xieyibox\">\n\t\t\t<view class=\"xieyibox-content\">\n\t\t\t\t<view style=\"overflow:scroll;height:100%;\">\n\t\t\t\t\t<parse :content=\"sysset.refund_desc\" @navigate=\"navigate\"></parse>\n\t\t\t\t</view>\n\t\t\t\t<view style=\"position:absolute;z-index:9999;bottom:10px;left:7%;margin:0 auto;text-align:center; width: 30%;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;background: #bdbdbd;\"   @tap=\"canceldescClick\">取消</view>\n\t\t\t\t<view style=\"position:absolute;z-index:9999;bottom:10px;right:7%;margin:0 auto;text-align:center; width: 50%;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @tap=\"hidedescClick\">已阅读并同意</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nvar interval = null;\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tpre_url:app.globalData.pre_url,\n      prodata: '',\n      djs: '',\n      iscommentdp: \"\",\n      detail: \"\",\n\t\t\tworkerinfo:{},\n      storeinfo: \"\",\n      lefttime: \"\",\n\t\t\tselectExpressShow:false,\n\t\t\texpress_content:'',\n\t\t\tisagree:0,\n\t\t\tsysset:[],\n\t\t\tshowdesc:false,//弹窗显示\n\t\t\tdesc:'',//租车等内容\n\t\t\tdesc_status:1,//是否显示租车说明\n\t\t\turl:''\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onUnload: function () {\n    clearInterval(interval);\n  },\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiCarHailing/orderdetail', {id: that.opt.id}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.iscommentdp = res.iscommentdp,\n\t\t\t\tthat.detail = res.detail;\n\t\t\t\tthat.workerinfo = res.workerinfo;\n\t\t\t\tthat.storeinfo = res.storeinfo;\n\t\t\t\tthat.lefttime = res.lefttime;\n\t\t\t\tthat.payorder = res.payorder;\n\t\t\t\tthat.sysset = res.sysset\n\t\t\n\t\t\t\tthat.desc_status = that.sysset.refund_desc_status;\n\t\t\n\t\t\t\tif (res.lefttime > 0) {\n\t\t\t\t\tinterval = setInterval(function () {\n\t\t\t\t\t\tthat.lefttime = that.lefttime - 1;\n\t\t\t\t\t\tthat.getdjs();\n\t\t\t\t\t}, 1000);\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n\t\tshowdescClick: function () {\n\t\t  this.showdesc = true;\n\t\t},\n\t\tcanceldescClick:function(){\n\t\t\tthis.showdesc = false;\n\t\t},\n\t\thidedescClick:function(){\n\t\t\tthis.showdesc = false;\n\t\t\tthis.isagree = 1;\n\t\t\t\n\t\t},\n\t\tisagreeChange: function (e) {\n\t\t  var val = e.detail.value;\n\t\t  if (val.length > 0) {\n\t\t    this.isagree = true;\n\t\t  } else {\n\t\t    this.isagree = false;\n\t\t  }\n\t\t  console.log(this.isagree);\n\t\t},\n\t\ttorefund:function(e){\n\t\t\tvar id = e.currentTarget.dataset.id;\n\t\t\tvar url = 'refund?orderid=' + id;\n\t\t\tthis.url = url;\n\t\t\tconsole.log(url);\n\t\t\tif(this.sysset.refund_desc_status ==1){\n\t\t\t\tif(!this.isagree){\n\t\t\t\t\tthis.showdesc = true;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\tapp.goto(url);\n\t\t},\n    getdjs: function () {\n      var that = this;\n      var totalsec = that.lefttime;\n\n      if (totalsec <= 0) {\n        that.djs = '00时00分00秒';\n      } else {\n        var houer = Math.floor(totalsec / 3600);\n        var min = Math.floor((totalsec - houer * 3600) / 60);\n        var sec = totalsec - houer * 3600 - min * 60;\n        var djs = (houer < 10 ? '0' : '') + houer + '时' + (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';\n        that.djs = djs;\n      }\n    },\n    todel: function (e) {\n      var that = this;\n      var orderid = e.currentTarget.dataset.id;\n      app.confirm('确定要删除该订单吗?', function () {\n\t\t\t\tapp.showLoading('删除中');\n        app.post('ApiCarHailing/delOrder', {orderid: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n          app.success(data.msg);\n          setTimeout(function () {\n            app.goback(true);\n          }, 1000);\n        });\n      });\n    },\n    toclose: function (e) {\n      var that = this;\n      var orderid = e.currentTarget.dataset.id;\n      app.confirm('确定要关闭该订单吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n        app.post('ApiCarHailing/closeOrder', {orderid: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n          app.success(data.msg);\n          setTimeout(function () {\n            that.getdata();\n          }, 1000);\n        });\n      });\n    },\n    orderCollect: function (e) {\n      var that = this;\n      var orderid = e.currentTarget.dataset.id;\n      app.confirm('确定已完成服务吗?', function () {\n\t\t\t\tapp.showLoading('确认中');\n        app.post('ApiCarHailing/orderCollect', {orderid: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n          app.success(data.msg);\n          setTimeout(function () {\n            that.getdata();\n          }, 1000);\n        });\n      });\n    },\n\n\t\tshowhxqr:function(){\n\t\t\tthis.$refs.dialogHxqr.open();\n\t\t},\n\t\tcloseHxqr:function(){\n\t\t\tthis.$refs.dialogHxqr.close();\n\t\t},\n\t\topenLocation:function(e){\n\t\t\tvar latitude = parseFloat(e.currentTarget.dataset.latitude);\n\t\t\tvar longitude = parseFloat(e.currentTarget.dataset.longitude);\n\t\t\tvar address = e.currentTarget.dataset.address;\n\t\t\tuni.openLocation({\n\t\t\t latitude:latitude,\n\t\t\t longitude:longitude,\n\t\t\t name:address,\n\t\t\t scale: 13\n\t\t\t});\n\t\t},\n\t\topenMendian: function(e) {\n\t\t\tvar storeinfo = e.currentTarget.dataset.storeinfo;\n\t\t\tapp.goto('/pages/shop/mendian?id=' + storeinfo.id);\n\t\t},\n\t\tlogistics:function(e){\n\t\t\tvar express_com = e.currentTarget.dataset.express_com\n\t\t\tvar express_no = e.currentTarget.dataset.express_no\n\t\t\tapp.goto('/activity/yuyue/logistics?express_no=' + express_no);\n\t\t},\n\t\thideSelectExpressDialog:function(){\n\t\t\tthis.$refs.dialogSelectExpress.close();\n\t\t}\n  }\n};\n</script>\n<style>\n\t.text-min { font-size: 24rpx; color: #999;}\n.ordertop{width:100%;height:452rpx;padding:50rpx 0 0 70rpx; justify-content: space-between;}\n.ordertop .f1{color:#fff}\n.ordertop .f1 .t1{font-size:40rpx;height:60rpx;line-height:60rpx;}\n.ordertop .f1 .t2{font-size:26rpx; margin-top: 20rpx;}\n\n.container .orderinfotop{ position: relative; margin-top: -200rpx;}\n\n.address{ display:flex;width: 100%; padding: 20rpx 3%; background: #FFF;}\n.address .img{width:40rpx}\n.address image{width:40rpx; height:40rpx;}\n.address .info{flex:1;display:flex;flex-direction:column;}\n.address .info .t1{font-size:28rpx;font-weight:bold;color:#333}\n.address .info .t2{font-size:24rpx;color:#999}\n\n.product{width:96%;margin:0 2%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\n.product .content{display:flex;position:relative;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;}\n.product .content:last-child{ border-bottom: 0; }\n.product .content image{ width: 140rpx; height: 140rpx;}\n.product .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\n\n.product .content .detail .t1{font-size:26rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.product .content .detail .t2{color: #999;font-size: 26rpx;margin-top: 10rpx;}\n.product .content .detail .t3{display:flex;color: #ff4246;margin-top: 10rpx;}\n.product .content .detail .t4{margin-top: 10rpx;}\n\n.product .content .detail .x1{ flex:1}\n.product .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\n.product .content .comment{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc702 solid; border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.product .content .comment2{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc7c2 solid; border-radius:10rpx;background:#fff; color: #ffc7c2;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.orderinfo{width:96%;margin:0 2%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\n.orderinfo .title,.product .title{ font-weight: bold; font-size: 30rpx; line-height: 60rpx; margin-bottom: 15rpx;}\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}\n.orderinfo .item:last-child{ border-bottom: 0;}\n.orderinfo .item .t1{width:200rpx;flex-shrink:0}\n.orderinfo .item .t2{flex:1;text-align:right}\n.orderinfo .item .t3{ margin-top: 3rpx;}\n.orderinfo .item .red{color:red}\n\n.bottom{ width: 100%; padding: 16rpx 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}\n\n.btn1{margin-left:20rpx;min-width:160rpx;padding: 0 20rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;}\n.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;}\n.btn3{font-size:24rpx;width:120rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n\n.btitle{ width:100%;height:100rpx;background:#fff;padding:0 20rpx;border-bottom:1px solid #f5f5f5}\n.btitle .comment{border: 1px #ffc702 solid;border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.btitle .comment2{border: 1px #ffc7c0 solid;border-radius:10rpx;background:#fff; color: #ffc7c0;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\n.hxqrbox .img{width:400rpx;height:400rpx}\n.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\n.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\n\n.orderx image{ width:124rpx ; height: 124rpx; margin-right: 60rpx;}\n.desc{width: 100%;line-height: 80rpx;font-size: 24rpx;overflow: hidden;position: fixed;bottom: 93rpx;padding-left: 30rpx;background-color: #fff;text-align: center;}\n\n.xieyibox{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)}\n.xieyibox-content{width:90%;margin:0 auto;height:80%;margin-top:20%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px}\n\n</style>", "import mod from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213030885\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}