{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-show/buydialog-show.vue?287e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-show/buydialog-show.vue?6952", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-show/buydialog-show.vue?7fc9", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-show/buydialog-show.vue?b901", "uni-app:///components/buydialog-show/buydialog-show.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-show/buydialog-show.vue?4aca", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/buydialog-show/buydialog-show.vue?256b"], "names": ["data", "ks", "product", "gui<PERSON>", "gui<PERSON><PERSON>", "ggselected", "nowguige", "jlprice", "gwcnum", "isload", "loading", "canaddcart", "shopset", "totalprice", "pre_url", "props", "btntype", "default", "menuindex", "controller", "proid", "mounted", "uni", "that", "<PERSON><PERSON><PERSON><PERSON>", "methods", "getdata", "app", "id", "num", "ggid", "stock", "sell_price", "showLinkChange", "ggchange", "gwcplus", "gwcminus", "gwcinput"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACqC;;;AAGlG;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAA40B,CAAgB,4yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmCh2B;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAAC;IAAA;IACAC;MAAAD;IAAA;IACAE;MAAAF;IAAA;IACAG;EACA;EACAC;IACA;IACAC;MACAC;IACA;IACAA;EACA;EACAC;IACAF;EACA;EACAG;IACAC;MACA;MAeAH;MACAI;QAAAC;MAAA;QACAL;QACAA;QACAA;QACA;UACAA;QACA;QACAA;QACAA;QACA;QACA;QACA;UACAlB;QACA;QACAkB;QACAA;QACAA;QACA,mCACAA,6CAEAA;QACAA;QACA;UAAA;UACAA;QACA;QACAA;UAAAH;UAAAS;UAAAC;UAAAC;UAAAC;QAAA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA7B;MACA;MACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;MACA;MAAA;MACA;MACA;MACA;MACA;MACA;MACAkB;QAAAH;QAAAS;QAAAC;QAAAC;QAAAC;MAAA;IACA;IACA;IACAG;MACA;MACA;MACA;MACA;QACAR;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAJ;QAAAH;QAAAS;QAAAC;QAAAC;QAAAC;MAAA;IACA;IACA;IACAI;MACA;MACA;MACA;MACA;QACA;UACA;YACAT;UACA;UACA;QACA;MACA;QACA;UACA;YACAA;UACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAJ;QAAAH;QAAAS;QAAAC;QAAAC;QAAAC;MAAA;IACA;IACA;IACAK;MACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;QACA;UACA;YACAV;UACA;UACAnB;QACA;MACA;QACA;UACA;YACAmB;UACA;UACAnB;QACA;MACA;MACA;QACAmB;QACAnB;MACA;MACA;MACA;MACA;MACA;MACA;MACAe;QAAAH;QAAAS;QAAAC;QAAAC;QAAAC;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnOA;AAAA;AAAA;AAAA;AAAitC,CAAgB,ioCAAG,EAAC,C;;;;;;;;;;;ACAruC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/buydialog-show/buydialog-show.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./buydialog-show.vue?vue&type=template&id=6f09c878&scoped=true&\"\nvar renderjs\nimport script from \"./buydialog-show.vue?vue&type=script&lang=js&\"\nexport * from \"./buydialog-show.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buydialog-show.vue?vue&type=style&index=0&id=6f09c878&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6f09c878\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/buydialog-show/buydialog-show.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-show.vue?vue&type=template&id=6f09c878&scoped=true&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.nowguige.balance_price ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.product.price_type == 1 ? _vm.t(\"color2\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-show.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-show.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<view v-if=\"isload\">\r\n\t\t<view class=\"buydialog-current\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\r\n\t\t\t<view style=\"padding-top: 20rpx;padding-left: 20rpx;font-weight: bold;\">{{shopset.guige_name?shopset.guige_name:'请选择规格和数量'}}</view>\r\n\t\t\t<view v-if=\"nowguige.balance_price\" style=\"width:94%;margin:10rpx 3%;font-size:24rpx;\" :style=\"{color:t('color1')}\">首付款金额：{{nowguige.advance_price}}元，尾款金额：{{nowguige.balance_price}}元</view>\r\n\t\t\t<view style=\"max-height:50vh;overflow:scroll\">\r\n\t\t\t\t<view v-for=\"(item, index) in guigedata\" :key=\"index\" class=\"guigelist flex-col\">\r\n\t\t\t\t\t<view class=\"name\">{{item.title}}</view>\r\n\t\t\t\t\t<view class=\"item flex flex-y-center\">\r\n\t\t\t\t\t\t<block v-for=\"(item2, index2) in item.items\" :key=\"index2\">\r\n\t\t\t\t\t\t\t<view :data-itemk=\"item.k\" :data-idx=\"item2.k\" :class=\"'item2 ' + (ggselected[item.k]==item2.k ? 'on':'')\" @tap=\"ggchange\">{{item2.title}}</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<block v-if=\"product.price_type == 1\">\r\n\t\t\t\t<button class=\"addcart\" :style=\"{backgroundColor:t('color2')}\" @tap=\"showLinkChange\">{{product.xunjia_text?product.xunjia_text:'联系TA'}}</button>\r\n\t\t\t</block>\r\n\t\t\t<block v-else>\r\n\t\t\t\t<view class=\"buynum flex flex-y-center\">\r\n\t\t\t\t\t<view class=\"flex1\">购买数量：</view>\r\n\t\t\t\t\t<view class=\"addnum\">\r\n\t\t\t\t\t\t<view class=\"minus\" @tap=\"gwcminus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-minus.png'\" /></view>\r\n\t\t\t\t\t\t<input class=\"input\" type=\"number\" :value=\"gwcnum\" @input=\"gwcinput\"></input>\r\n\t\t\t\t\t\t<view class=\"plus\" @tap=\"gwcplus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-plus.png'\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t</view>\r\n\t<loading v-if=\"loading\"></loading>\r\n</view>\r\n</template>\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tks:'',\r\n\t\t\t\tproduct:{},\r\n\t\t\t\tguigelist:{},\r\n\t\t\t\tguigedata:{},\r\n\t\t\t\tggselected:{},\r\n\t\t\t\tnowguige:{},\r\n\t\t\t\tjlprice:0,\r\n\t\t\t\tgwcnum:1,\r\n\t\t\t\tisload:false,\r\n\t\t\t\tloading:false,\r\n\t\t\t\tcanaddcart:true,\r\n\t\t\t\tshopset:{},\r\n\t\t\t\ttotalprice:0,\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tbtntype:{default:0},\r\n\t\t\tmenuindex:{default:-1},\r\n\t\t\tcontroller:{default:'ApiShop'},\r\n\t\t\tproid:{}\r\n\t\t},\r\n\t\tmounted:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.$on('getglassrecord', function(data) {\r\n\t\t\t\t that.getglassrecord()\r\n\t\t\t});\r\n\t\t\tthat.getdata();\r\n\t\t},\r\n\t\tbeforeDestroy(){\r\n\t\t\tuni.$off('getglassrecord')\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tgetdata:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t// #ifdef MP-TOUTIAO\r\n\t\t\t\tif(this.controller == 'ApiShop' && app.globalData.isdouyin == 1){\r\n\t\t\t\t\tapp.showLoading('加载中');\r\n\t\t\t\t\tapp.post('ApiShop/getDouyinProductId',{proid:that.proid},function(res){\r\n\t\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\t\tif(res.status == 1){\r\n\t\t\t\t\t\t\ttt.openEcGood({promotionId:res.douyin_product_id});\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tapp.alert(res.msg)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post(this.controller+'/getproductdetail',{id:that.proid},function(res){\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.product = res.product;\r\n\t\t\t\t\tthat.shopset = res.shopset;\r\n\t\t\t\t\tif(!that.product.limit_start){\r\n\t\t\t\t\t\tthat.product.limit_start = 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.guigelist = res.guigelist;\r\n\t\t\t\t\tthat.guigedata = res.guigedata;\r\n\t\t\t\t\tvar guigedata = res.guigedata;\r\n\t\t\t\t\tvar ggselected = [];\r\n\t\t\t\t\tfor (var i = 0; i < guigedata.length; i++) {\r\n\t\t\t\t\t\tggselected.push(0);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.ks = ggselected.join(','); \r\n\t\t\t\t\tthat.nowguige = that.guigelist[that.ks];\r\n\t\t\t\t\tthat.ggselected = ggselected;\r\n\t\t\t\t\tif(that.nowguige.limit_start > 0)\r\n\t\t\t\t\t\tthat.gwcnum = that.nowguige.limit_start;\r\n\t\t\t\t\telse\r\n\t\t\t\t\t\tthat.gwcnum = that.product.limit_start;\r\n\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t\tif(that.product.freighttype==3 || that.product.freighttype==4){ //虚拟商品不能加入购物车\r\n\t\t\t\t\t\tthat.canaddcart = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.$emit('changeGuige',{proid: that.proid,num: that.gwcnum,ggid:that.nowguige.id,stock:that.nowguige.stock,sell_price:that.nowguige.sell_price});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshowLinkChange:function () {\r\n\t\t\t\tthis.$emit('showLinkChange');\r\n\t\t\t},\r\n\t\t\t//选择规格\r\n\t\t\tggchange: function (e){\r\n\t\t\t\tvar that=this\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar itemk = e.currentTarget.dataset.itemk;\r\n\t\t\t\tvar ggselected = this.ggselected;\r\n\t\t\t\tggselected[itemk] = idx;\r\n\t\t\t\tvar ks = ggselected.join(',');\r\n\t\t\t\tthis.ggselected = ggselected;\r\n\t\t\t\tthis.ks = ks;\r\n\t\t\t\tthis.nowguige = this.guigelist[this.ks];\r\n\t\t\t\tif(this.nowguige.limit_start > 0) {\r\n\t\t\t\t\tif (this.gwcnum < this.nowguige.limit_start) {\r\n\t\t\t\t\t\tthis.gwcnum = this.nowguige.limit_start;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.totalprice = parseFloat( parseFloat(this.nowguige.sell_price) +this.jlprice).toFixed(2); ;\r\n\t\t\t\tvar proid = that.product.id;\r\n\t\t\t\tvar num = that.gwcnum\r\n\t\t\t\tvar ggid =this.nowguige.id\r\n\t\t\t\tvar stock = this.nowguige.stock\r\n\t\t\t\tvar sell_price = this.nowguige.sell_price\r\n\t\t\t\tthat.$emit('changeGuige',{proid: proid,num: num,ggid:ggid,stock:stock,sell_price:sell_price});\r\n\t\t\t},\r\n\t\t\t//加\r\n\t\t\tgwcplus: function (e) {\r\n\t\t\t\tvar that=this\r\n\t\t\t\tvar gwcnum = this.gwcnum + 1;\r\n\t\t\t\tvar ks = this.ks;\r\n\t\t\t\tif (gwcnum > this.guigelist[ks].stock) {\r\n\t\t\t\t\tapp.error('库存不足');\r\n\t\t\t\t\treturn 1;\r\n\t\t\t\t}\r\n\t\t\t\tif (this.product.perlimitdan > 0 && gwcnum > this.product.perlimitdan) {\r\n\t\t\t\t\tapp.error('每单限购'+this.product.perlimitdan+'件');\r\n\t\t\t\t\treturn 1;\r\n\t\t\t\t}\r\n\t\t\t\tthis.gwcnum = this.gwcnum + 1;\r\n\t\t\t\tvar proid = that.product.id;\r\n\t\t\t\tvar ggid =this.guigelist[ks].id\r\n\t\t\t\tvar stock = this.guigelist[ks].stock\r\n\t\t\t\tvar sell_price = this.guigelist[ks].sell_price\r\n\t\t\t\tthat.$emit('changeGuige',{proid: proid,num: gwcnum,ggid:ggid,stock:stock,sell_price:sell_price});\r\n\t\t\t},\r\n\t\t\t//减\r\n\t\t\tgwcminus: function (e) {\r\n\t\t\t\tvar that=this\r\n\t\t\t\tvar gwcnum = this.gwcnum - 1;\r\n\t\t\t\tvar ks = this.ks;\r\n\t\t\t\tif(this.nowguige.limit_start > 0) {\r\n\t\t\t\t\tif (gwcnum <= this.nowguige.limit_start - 1) {\r\n\t\t\t\t\t\tif(this.nowguige.limit_start > 1){\r\n\t\t\t\t\t\t\tapp.error('该规格' + this.nowguige.limit_start + '件起售');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif (gwcnum <= this.product.limit_start - 1) {\r\n\t\t\t\t\t\tif(this.product.limit_start > 1){\r\n\t\t\t\t\t\t\tapp.error('该商品' + this.product.limit_start + '件起售');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.gwcnum = this.gwcnum - 1;\r\n\t\t\t\tvar proid = that.product.id;\r\n\t\t\t\tvar ggid =this.guigelist[ks].id\r\n\t\t\t\tvar stock = this.guigelist[ks].stock\r\n\t\t  \tvar sell_price = this.guigelist[ks].sell_price\r\n\t\t\t\tthat.$emit('changeGuige',{proid: proid,num: gwcnum,ggid:ggid,stock:stock,sell_price:sell_price});\r\n\t\t\t},\r\n\t\t\t//输入\r\n\t\t\tgwcinput: function (e) {\r\n\t\t\t\tvar that=this\r\n\t\t\t\tvar ks = this.ks;\r\n\t\t\t\tvar gwcnum = parseInt(e.detail.value);\r\n\t\t\t\tif (gwcnum < 1) return 1;\r\n\t\t\t\tif (gwcnum > this.guigelist[ks].stock) {\r\n\t\t\t\t\treturn this.guigelist[ks].stock > 0 ? this.guigelist[ks].stock : 1;\r\n\t\t\t\t}\r\n\t\t\t\tif(this.nowguige.limit_start > 0) {\r\n\t\t\t\t\tif (gwcnum <= this.nowguige.limit_start - 1) {\r\n\t\t\t\t\t\tif(this.nowguige.limit_start > 1){\r\n\t\t\t\t\t\t\tapp.error('该规格' + this.nowguige.limit_start + '件起售');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tgwcnum = this.nowguige.limit_start;\r\n\t\t\t\t\t}\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif (gwcnum <= this.product.limit_start - 1) {\r\n\t\t\t\t\t\tif(this.product.limit_start > 1){\r\n\t\t\t\t\t\t\tapp.error('该商品' + this.product.limit_start + '件起售');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tgwcnum = this.product.limit_start;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (this.product.perlimitdan > 0 && gwcnum > this.product.perlimitdan) {\r\n\t\t\t\t\tapp.error('每单限购'+this.product.perlimitdan+'件');\r\n\t\t\t\t\tgwcnum = this.product.perlimitdan;\r\n\t\t\t\t}\r\n\t\t\t\tthis.gwcnum = gwcnum;\r\n\t\t\t\tvar proid = that.product.id;\r\n\t\t\t\tvar ggid =this.guigelist[ks].id\r\n\t\t\t\tvar stock = this.guigelist[ks].stock\r\n\t\t\t\tvar sell_price = this.guigelist[ks].sell_price\r\n\t\t\t\tthat.$emit('changeGuige',{proid: proid,num: gwcnum,ggid:ggid,stock:stock,sell_price:sell_price});\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style scoped>\r\n.buydialog-current{ background: #fff;z-index:11;border-radius:20rpx 20rpx 0px 0px;margin-top: 10rpx;}\r\n.buydialog-current .guigelist{ width: 94%; position: relative; margin: 0 3%;  border-bottom: 0; }\r\n.buydialog-current .guigelist .name{ height:70rpx; line-height: 70rpx;}\r\n.buydialog-current .guigelist .item{ font-size: 30rpx;color: #333;flex-wrap:wrap}\r\n.buydialog-current .guigelist .item2{ height:60rpx;line-height:60rpx;margin-bottom:4px;border:0; border-radius:4rpx; padding:0 40rpx;color:#666666; margin-right: 10rpx; font-size:26rpx;background:#F4F4F4}\r\n.buydialog-current .guigelist .on{color:#FC4343;background:rgba(252,67,67,0.1);font-weight:bold}\r\n.buydialog-current .buynum{ width: 94%; position: relative; margin: 0 3%; padding:10px 0px 10px 0px; }\r\n.buydialog-current .addnum {font-size: 30rpx;color: #666;width:auto;display:flex;align-items:center}\r\n.buydialog-current .addnum .plus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n.buydialog-current .addnum .minus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n.buydialog-current .addnum .img{width:24rpx;height:24rpx}\r\n.buydialog-current .addnum .input{flex:1;width:50rpx;border:0;text-align:center;color:#2B2B2B;font-size:28rpx;margin: 0 15rpx;}\r\n.buydialog-current .addcart{flex:1;height:72rpx; line-height: 72rpx; color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-show.vue?vue&type=style&index=0&id=6f09c878&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buydialog-show.vue?vue&type=style&index=0&id=6f09c878&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754212980620\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}