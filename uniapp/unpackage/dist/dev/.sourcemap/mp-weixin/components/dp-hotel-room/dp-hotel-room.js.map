{"version": 3, "sources": ["webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/mobile-calendar-simple/Calendar.vue?0f29", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/mobile-calendar-simple/Calendar.vue?e842", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/mobile-calendar-simple/Calendar.vue?f9ae", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/mobile-calendar-simple/Calendar.vue?8b35", "uni-app:///hotel/mobile-calendar-simple/Calendar.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/mobile-calendar-simple/Calendar.vue?2b02", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/hotel/mobile-calendar-simple/Calendar.vue?c15d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-hotel-room/dp-hotel-room.vue?7bb8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-hotel-room/dp-hotel-room.vue?94c4", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-hotel-room/dp-hotel-room.vue?4b5b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-hotel-room/dp-hotel-room.vue?6ee3", "uni-app:///components/dp-hotel-room/dp-hotel-room.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-hotel-room/dp-hotel-room.vue?0dc0", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/components/dp-hotel-room/dp-hotel-room.vue?2920"], "names": ["props", "isShow", "type", "default", "isFixed", "transition", "title", "mode", "startDate", "dayroomprice", "showstock", "maxdays", "text", "endDate", "betweenStart", "betweenEnd", "initMonth", "themeColor", "selectedColor", "tipData", "ysNum", "chooseType", "data", "startDates", "endDates", "betweenStarts", "betweenEnds", "calendar", "weekList", "thisday", "thismonth", "watch", "mounted", "computed", "getBetweenColor", "hex", "methods", "init", "date", "month", "day", "startStr", "createDayList", "_week", "list", "getDayNum", "day<PERSON>um", "createClendar", "yearTemp", "monthTemp", "dayList", "year", "_monthData", "console", "scrollTop", "setTimeout", "wrap", "addClassName", "className", "_date", "addClassBg", "themeOpacityBg", "themeBg", "resetTime", "setTip", "tip", "setOrderTip", "value", "isCurrent", "dateFormat", "recent", "dateStr", "week", "chooseDate", "choose", "params", "isload", "dayCount", "startWeek", "endWeek", "starttime", "calendarvisible", "pre_url", "maxen<PERSON>ate", "minday", "minstock", "bannerindex", "bannerList", "room", "totalprice", "yajin", "service_money", "qystatus", "fwstatus", "pagecontent", "roomids", "roomlist", "hotel", "sortby", "roomstyle", "btnstyle", "rooms", "that", "getdata", "app", "endtime", "daycount", "uni", "getdetail", "selectDate", "handleClickMask", "getDate", "popupClose", "hotelDetail", "id", "popupdetailClose", "swiper<PERSON><PERSON>e", "calculatePrice", "tobuy"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwC11B;AAAA,gBACA;EACAA;IACAC;MAAA;MACAC;MACAC;QACA;MACA;IACA;IACAC;MAAA;MACAF;MACAC;QACA;MACA;IACA;IACAE;MAAA;MACAH;MACAC;QACA;MACA;IACA;IACAG;MAAA;MACAJ;MACAC;QACA;MACA;IACA;IACAI;MAAA;MACAL;MACAC;QACA;MACA;IACA;IACAK;MAAA;MACAN;IACA;IACAO;MACAP;IACA;IACAQ;MACAR;MACAC;QACA;MACA;IACA;IACAQ;MACAT;MACAC;QACA;MACA;IACA;IACAS;MACAV;IACA;IACAW;MAAA;MACAX;IACA;IACAY;MAAA;MACAZ;MACAC;QACA;MACA;IACA;IACAY;MAAA;MACAb;MACAC;QACA;MACA;IACA;IACAa;MAAA;MACAd;MACAC;QACA;MACA;IACA;IACAc;MAAA;MACAf;MACAC;IACA;IACAe;MAAA;MACAhB;MACAC;IACA;IACAgB;MACAjB;MACAC;QACA;MACA;IACA;IACAiB;MACAlB;MACAC;QACA;MACA;IACA;IACAkB;MACAnB;MACAC;QACA;MACA;IACA;EACA;EACAmB;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA9B;MACA;IACA;IACAa;MACA;IACA;IACAC;MACA;IACA;EACA;EACAiB;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;QACAC;MACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACAC;MACA;QACA;UACAA;QACA;UACAA;QACA;MACA;MAAA;QACA;UACAA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;UACAC;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;MACA;MACA;QACA;QACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;IACA;IACA;IACAC;MACA;QACAC;MACA;MACA;QACAC;MACA;MACA;QACAA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACAC;QACAC;MACA;MACA;MACA;QACA;QACA;QACA;UACAC;UACAX;UACAY;QACA;QACA;QACA;UACAA;QACA;UACAA;QACA;QACA;UACAZ;QACA;QACA;UACAA;QACA;QACAa;QACAA;QACAA;QACAC;QACA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACAC;QACA;QACA;QACA;QACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;QACA;UACAA;QACA;MACA;QACA;UACAA;QACA;QAEA;QACA;UACA;YACAA;UACA;QACA;MAEA;QACA;UACAA;QACA;MACA;MACA;QACAC;MACA;QACAA;MACA;;MAEA;QACA;UACAD;QACA;MACA;QACA;UACAA;QACA;MACA;MAEAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAF;MACA;MACA;IACA;IACA;IACAG;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;QACA;UACA;QACA;MACA;QACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACAzB;MACAA;MACAA;MACAA;MACA;IACA;IACA;IACA0B;MACA;MACA;MACA;MACA;QACA;UACAC;QACA;UACAA;QACA;UACAA;QACA;QACA;MACA;QACA;UACA;YACAA;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MAEA;QAAA5B;QAAA6B;MAAA;QAAA7B;QAAA6B;MAAA;MACA;QACA;UACAF;QACA;MACA;MACA;IACA;IACA;IACAG;MACA;QACA;MACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;QACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACAC;MACA;QACAA;MACA;QACAA;MACA;MACA;MACA;MACA;MACA;QACAC;QACAC;QACAF;MACA;IACA;IACAG;MACA;MACA;MACA;QACA;UACA;QACA;MACA;QACA;UACA;QACA;MACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;QACAhC;MACA;MACA;QACA;MACA;QACAiC;QAEA;UACA;QACA;QACAA;QAEA;MACA;QACA;UACAA;UACAA;QACA;UACAA;UACAA;QACA;QACA;MACA;QACA;UACAA;UACAA;QACA;UACAA;UACAA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACjjBA;AAAA;AAAA;AAAA;AAA6hD,CAAgB,85CAAG,EAAC,C;;;;;;;;;;;ACAjjD;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yGAEN;AACP,KAAK;AACL;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpJA;AAAA;AAAA;AAAA;AAA20B,CAAgB,2yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACkT/1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eACA;EACA1E;IACA2E;IACArD;EACA;EACAA;IACA;MACAsD;MACApE;MACAK;MACAgE;MACAC;MACAC;MACAC;MACAC;MACArE;MACAsE;MACAC;MACA1E;MACA2E;MACAC;MACA1E;MACA2E;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACApE;IACA;IACA;IACA;IACA;IACAqE;MACAN;IACA;IACAO;IACAA;EACA;EACAlE;IACAmE;MACA;MACA;MACA;MACAC;QAAAxB;QAAAyB;MAAA;QACA;UACAH;UACAA;UACA;UACA;YACAtB;YACAwB;YACAC;YACAD;YACAE;YACAF;UACA;UACAnD;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACAiD;UACAA;UACAA;UACAE;UACAA;UACAF;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAjD;UACAsD;YACArG;UACA;UACAgG;UACAA;QACA;MACA;IACA;IACAM;MACA;MACA;QACA;QACA;MACA;MACAN;MACAA;MACAA;MACAE;QAAAN;QAAAH;QAAAf;QAAAyB;QAAAC;MAAA;QACAJ;QACAK;QACA;UACA;UACAL;QACA;MACA;IACA;IACAO;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACAP;QACAF;MACA;MACA;QACA;QACAE;QACA;QACAA;QAEAF;QACAA;QACAA;MACA;MACA;QACA;QACAE;QACA;QACAA;QACAF;QACAA;QACAA;MACA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACAA;IACA;IACAU;MACA;MACA;IACA;IACAC;MACA;MACA;MACAT;QAAAU;QAAA1G;QAAAK;QAAAgE;MAAA;QACAxB;QACA;UACAiD;UAEA;UACAA;UACAA;UAEAA;UACAA;QACA;MACA;MACA;MACA;IACA;IACAa;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACAhE;MACA;MACA;MACA;QACAsC;MACA;MACAW;MACA;MACA;QACAZ;MACA;QACAA;MACA;QACA;UACAA;QACA;UACAA;QACA;MACA;MACAY;MACA;MACA;QACAb;MACA;QACAA;MACA;MACAa;MAAA;IACA;IACAgB;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;MACAd;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9hBA;AAAA;AAAA;AAAA;AAAwrC,CAAgB,wmCAAG,EAAC,C;;;;;;;;;;;ACA5sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/dp-hotel-room/dp-hotel-room.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./Calendar.vue?vue&type=template&id=052beb44&scoped=true&\"\nvar renderjs\nimport script from \"./Calendar.vue?vue&type=script&lang=js&\"\nexport * from \"./Calendar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Calendar.vue?vue&type=style&index=0&id=052beb44&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"052beb44\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"hotel/mobile-calendar-simple/Calendar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Calendar.vue?vue&type=template&id=052beb44&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isShow\n    ? _vm.__map(_vm.weekList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 =\n          (index == 0 || index == _vm.weekList.length - 1) && _vm.themeColor\n        return {\n          $orig: $orig,\n          g0: g0,\n        }\n      })\n    : null\n  var l2 = _vm.isShow\n    ? _vm.__map(_vm.calendar, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var l1 = _vm.__map(item.dayList, function (day, idx) {\n          var $orig = _vm.__get_orig(day)\n          var m0 = _vm.addClassBg(day, item.month, item.year)\n          var m1 = _vm.themeOpacityBg(day, item.month, item.year)\n          var m2 =\n            _vm.mode != 4 ? _vm.addClassName(day, item.month, item.year) : null\n          var m3 =\n            _vm.mode != 4 ? _vm.themeBg(day, item.month, item.year) : null\n          var m4 =\n            _vm.mode != 4 ? _vm.setTip(day, item.month, item.year, 2) : null\n          var m5 =\n            _vm.mode != 4 && m4\n              ? _vm.setTip(day, item.month, item.year, 2)\n              : null\n          var m6 = !(_vm.mode != 4)\n            ? _vm.addClassName(day, item.month, item.year)\n            : null\n          var m7 = !(_vm.mode != 4)\n            ? _vm.setOrderTip(day, item.month, item.year)\n            : null\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n            m3: m3,\n            m4: m4,\n            m5: m5,\n            m6: m6,\n            m7: m7,\n          }\n        })\n        return {\n          $orig: $orig,\n          l1: l1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l2: l2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Calendar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Calendar.vue?vue&type=script&lang=js&\"", "<template>\n    <transition :name=\"transition\">\n        <div class=\"calendar-tz\" v-if=\"isShow\" :class=\"isFixed&&'fixed'\">\n            <slot name=\"header\"></slot>\n            <div class=\"week-number\">\n                <span v-for=\"(item,index) in weekList\" :style=\"{color:(index==0||index==weekList.length-1)&&themeColor}\" :key=\"index\">{{item}}</span>\n            </div>\n            <p class=\"tips\" v-if=\"title\">{{title}}</p>\n            <div class=\"content\" id=\"scrollWrap\">\n                <div class=\"con\" v-for=\"(item,index) in calendar\" :key=\"index\"  :id=\"item.year+''+item.month\">\r\n\t\t\t\t\t\t\t\t\t\n                    <h3 v-text=\"item.year + '年' + item.month + '月'\"></h3>\n                    <span class=\"month-bg\"  :style=\"{color:getBetweenColor}\">{{item.month}}</span>\n                    <ul class=\"each-month\">\n                        <li class=\"each-day\" v-for=\"(day,idx) in item.dayList\" :key=\"idx\" :class=\"[addClassBg(day, item.month, item.year)]\" :style=\"{background:themeOpacityBg(day, item.month, item.year)}\" @click=\"chooseDate(day, item.month, item.year)\">\n                            <div :class=\"[addClassName(day, item.month, item.year)]\" :style=\"{background:themeBg(day, item.month, item.year)}\" v-if=\"mode !=4\">\n                                <!-- <p class=\"day-tip\" :style=\"{color:themeColor}\"><i v-text=\"setTip(day, item.month, item.year,0)\"></i></p> -->\n                                <p class=\"day\">{{day?day:''}}</p>\n                                <p class=\"recent\" v-if=\"setTip(day, item.month, item.year,2)\"><i v-text=\"setTip(day, item.month, item.year,2)\"></i></p>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p class=\"recent\" v-else-if=\"showstock==1 && day>0 \" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"dayroomprice[item.year+'-'+(item.month<=9?'0':'')+item.month+'-'+((day>0 && day<=9)?'0':'')+(day?day:'')] && dayroomprice[item.year+'-'+(item.month<=9?'0':'')+item.month+'-'+((day>0 && day<=9)?'0':'')+(day?day:'')]['status']==1 \">{{dayroomprice[item.year+'-'+(item.month<=9?'0':'')+item.month+'-'+(day<=9?'0':'')+(day?day:'')]['stock']}}{{text['间']}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</p>\n                            </div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div :class=\"[addClassName(day, item.month, item.year)]\" v-else>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<!-- <p class=\"day-tip\" :style=\"{color:themeColor}\"><i v-text=\"setTip(day, item.month, item.year,0)\"></i></p> -->\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p class=\"day\">{{day?day:''}}</p>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p class=\"recent\" >1<i v-text=\"setOrderTip(day, item.month, item.year)\"></i></p>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n                        </li>\n                    </ul>\n                </div>\n            </div>\r\n\t\t\t\t\t\t<div style='height:80px'></div>\n            <slot name=\"footer\"></slot>\n        </div>\n    </transition>\n</template>\n\n<script>\r\nvar app = getApp();\nexport default {\n  props: {\n    isShow: {//是否显示\n        type: [Boolean],\n        default() {\n            return false;\n        }\n    },\n    isFixed: {//是否定位全屏\n        type: [Boolean],\n        default() {\n            return true;\n        }\n    },\n    transition: {//动画类型slide\n        type: [String],\n        default() {\n            return \"\";\n        }\n    },\n    title: {//头部的一段文本\n        type: [String, Object],\n        default() {\n            return \"\";\n        }\n    },\n    mode: {//模式：1普通日历，2酒店，3飞机往返\n        type: [String, Number],\n        default() {\n            return 1;\n        }\n    },\n    startDate: {//开始日期\n        type: [String, Object, Date]\n    },\r\n\t\tdayroomprice:{\r\n\t\t\ttype:[Object,Array],\r\n\t\t},\r\n\t\tshowstock: {\r\n\t\t    type: [String],\r\n\t\t    default() {\r\n\t\t        return \"0\";\r\n\t\t    }\r\n\t\t},\t\r\n\t\tmaxdays: {\r\n\t\t    type: [String],\r\n\t\t    default() {\r\n\t\t        return \"0\";\r\n\t\t    }\r\n\t\t},\r\n\t\ttext:{\r\n\t\t\ttype:[Object,Array],\r\n\t\t},\n    endDate: {//结束日期\n        type: [String, Object, Date]\n    },\n    betweenStart: {//日历可选范围开始\n        type: [String, Object, Date],\n        default() {\n            return \"\";\n        }\n    },\n    betweenEnd: { //日历可选结束日期\n        type: [String, Object, Date],\n        default() {\n            return \"\";\n        }\n    },\n    initMonth: {//初始化的月数\n        type: [String, Number],\n        default() {\n            return 6;\n        }\n    },\n    themeColor: {//主题色\n        type: [String],\n        default: \"#1C75FF\"\n    },\r\n\tselectedColor: {//选中色\r\n\t    type: [String],\r\n\t    default: \"#f44336\"\r\n\t},\r\n\ttipData:{\r\n\t\ttype:[String,Object,Array],\r\n\t\tdefault() {\r\n\t\t    return [];\r\n\t\t}\r\n\t},\r\n\tysNum:{\r\n\t\ttype:[String],\r\n\t\tdefault() {\r\n\t\t    return '0';\r\n\t\t}\r\n\t},\r\n\tchooseType:{\r\n\t\ttype:[String],\r\n\t\tdefault() {\r\n\t\t    return '';\r\n\t\t}\r\n\t}\n  },\n  data() {\n    return {\n        startDates: \"\",\n        endDates: \"\",\n        betweenStarts: \"\",\n        betweenEnds: \"\",\n        calendar: [],\n        weekList: [\"日\", \"一\", \"二\", \"三\", \"四\", \"五\", \"六\"],\r\n\t\t\t\tthisday:'',\r\n\t\t\t\tthismonth:''\n    };\n  },\n  watch: {\n    isShow() {\n        this.init();\n    },\n    betweenStart() {\n        this.init();\n    },\n    betweenEnd() {\n        this.init();\n    }\n  },\n  mounted() {\n    this.init();\n  },\n  computed: {\n    //设置主题色入住离开之间的背景色\n    getBetweenColor() {\n        if (!this.themeColor) return;\n        var hex = this.themeColor;\n        if (hex.length == 4) {\n            hex = `#${hex[1]}${hex[1]}${hex[2]}${hex[2]}${hex[3]}${hex[3]}`;\n        }\n        var str = \"rgba(\" + parseInt(\"0x\" + hex.slice(1, 3)) + \",\" + parseInt(\"0x\" + hex.slice(3, 5)) + \",\" + parseInt(\"0x\" + hex.slice(5, 7)) + \",0.1)\";\n        return str;\n    }\n  },\n  methods: {\n    init() {\r\n\t\t//1默认 2工作日 3周末 4隔天\r\n\t\tvar addDate = new Date();\r\n        var date = new Date(addDate);\r\n\t\tdate.setDate(addDate.getDate() + parseInt(this.ysNum));\r\n\t\tif(this.chooseType=='2'){\r\n\t\t\tif(date.getDay()=='0'){\r\n\t\t\t\tdate.setDate(date.getDate() + 1);\r\n\t\t\t}else if(date.getDay()=='6'){\r\n\t\t\t\tdate.setDate(date.getDate() + 2);\r\n\t\t\t}\r\n\t\t}if(this.chooseType=='3'){\r\n\t\t\tif(date.getDay()!='0'||date.getDay()!='6'){\r\n\t\t\t\tdate.setDate(date.getDate() + (6 - date.getDay()));\r\n\t\t\t}\r\n\t\t}\n        this.year = date.getFullYear();\n        this.month = date.getMonth() + 1;\n        this.day = date.getDate();\n        this.today = new Date(this.year + \"/\" + this.month + \"/\" + this.day) * 1;\r\n\t\t\t\tthis.thisday  =this.day\r\n\t\t\t\tthis.thismonth = this.month\n        if (!this.startDate) {\n            const year = date.getFullYear(),\n                month = date.getMonth() + 1,\n                day = date.getDate();\n            this.startDates = this.resetTime(year + \"/\" + month + \"/\" + day);\n            this.startYear = year;\n            this.startMonth = month;\n        } else {\n            this.startDates = this.resetTime(this.startDate);\n            var dd = this.startDate.replace(/-/g, \"/\").split(\"/\");\n            this.startYear = dd[0];\n            this.startMonth = dd[1];\n        }\n        if (this.endDate) {\n            this.endDates = this.resetTime(this.endDate);\r\n            var dd = this.endDate.replace(/-/g, \"/\").split(\"/\");\r\n            this.endYear = dd[0];\r\n            this.endMonth = dd[1];\n        }\n        this.betweenStarts = this.resetTime(this.betweenStart);\n        this.betweenEnds = this.resetTime(this.betweenEnd);\r\n\t\t\t\t//console.log(this.betweenEnds);\n        this.createClendar();\r\n\t\t//默认返回 当前时间\r\n\t\tconst choose = {\r\n\t\t\tstartStr: this.dateFormat(this.startDates)\r\n\t\t};\r\n\t\t this.$emit(\"callback\", choose);\r\n    },\n    //创建每个月日历数据，传入月份1号前面用null填充\n    createDayList(month, year) {\n        const count = this.getDayNum(month, year),\n        _week = new Date(year + \"/\" + month + \"/1\").getDay();\n        let list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28];\n        for (let i = 29; i <= count; i++) {\n            list.push(i);\n        }\n        for (let i = 0; i < _week; i++) {\n            list.unshift(null);\n        }\n        return list;\n    },\n    //计算传入月份有多少天\n    getDayNum(month, year) {\n        let dayNum = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\n        if ((year % 4 === 0 && year % 100 !== 0) || year % 400 === 0) {\n            dayNum[1] = 29;\n        }\n        return dayNum[month - 1];\n    },\n    //根据当天和结束日期创建日历数据\n    createClendar() {\n        var yearTemp = this.year;\n        var monthTemp = this.month;\n        if (!!this.betweenStarts) {\n            //如果有范围起始日期，可选范围从betweenStart开始\n            yearTemp = new Date(this.betweenStarts).getFullYear();\n            monthTemp = new Date(this.betweenStarts).getMonth() + 1;\n        }\n        this.calendar = [];\n        for (let i = 0; i < this.initMonth; i++) {\n            let year = yearTemp;\n            let month = monthTemp + i;\n            let _monthData = {\n                dayList: [],\n                month: \"\",\n                year: \"\"\n            };\n            var m = Math.ceil(month / 12);\n            if (m > 0) {\n                year += m - 1;\n            } else {\n                year += m - 1;\n            }\n            if (month > 12) {\n                month = month % 12 == 0 ? 12 : month % 12;\n            }\n            if (month <= 0) {\n                month = 12 + month % 12;\n            }\n            _monthData.year = year;\n            _monthData.month = month;\n            _monthData.dayList = this.createDayList(month, year);\r\n\t\t\t\t\t\tconsole.log(_monthData);\n            this.calendar.push(_monthData);\n        }\n        //h5默认页面加载到当前日期start-date的位置\n        if (document) {\n            this.scrollTop(this.startYear, this.startMonth);\n        }\n    },\n    scrollTop(year, month) {\n        var id = year + \"\" + parseInt(month)\n        setTimeout(() => {\n            var obj = document.getElementById(id)\n            if(!obj) return\n            var wrap = document.getElementById(\"scrollWrap\");\n            wrap.scrollTop = obj.offsetTop - 40;\n        }, 0);\n    },\n    //添加日历样式\n    addClassName(day, month, year) {\n        if (!day) return;\n        const _date = new Date(year + \"/\" + month + \"/\" + day);\n        let className = [];\n        // if (_date.getDay() == 0 || _date.getDay() == 6) { //周末或周六样式\n        //     className.push('weekend')\n        // }\n        if (_date * 1 == this.today) {\n            className.push(\"today\");\n        }\n        if (this.mode == 1) {\n            if (_date * 1 == this.startDates) {\n                className.push(\"trip-time\");\n            }\n        }else if(this.mode == 4){\r\n\t\t\tif (_date * 1 == this.startDates) {\r\n\t\t\t    className.push(\"trip-time\");\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tvar orderDate =this.tipData;\r\n\t\t\tfor(var i=0; i< orderDate.length;i++){\r\n\t\t\t\tif (_date  * 1 == orderDate[i]['date']) {\r\n\t\t\t\t   className.push(\"trip-time-order\");\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t} else {\n            if (_date * 1 == this.startDates || _date * 1 == this.endDates) {\n                className.push(\"trip-time\");\n            }\n        }\n        if (this.betweenStarts) {\n            _date * 1 < this.betweenStarts && className.push(\"disabled\");\n        } else {\n            _date * 1 < this.today && className.push(\"disabled\"); //当天和结束日期之外不可选\n        }\r\n\t\t\r\n\t\tif(this.chooseType=='2'){\r\n\t\t\tif(_date.getDay()=='0' || _date.getDay()=='6'){\r\n\t\t\t\tclassName.push(\"disabled\");\r\n\t\t\t}\r\n\t\t}else if(this.chooseType=='3'){\r\n\t\t\tif(_date.getDay()=='1' || _date.getDay()=='2' || _date.getDay()=='3' || _date.getDay()=='4' || _date.getDay()=='5'){\r\n\t\t\t\tclassName.push(\"disabled\");\r\n\t\t\t}\r\n\t\t}\r\n\t\t\n        _date * 1 > this.betweenEnds && className.push(\"disabled\");\n        return className.join(\" \");\n    },\n    //入住离开的区间背景色\n    addClassBg(day, month, year) {\n        if (!day) return;\n        const _date = this.resetTime(year + \"/\" + month + \"/\" + day);\n        let className = [];\n        if (_date >= this.startDates && _date <= this.endDates && this.mode > 1) {\n            className.push(\"between\");\n        }\n        return className.join(\" \");\n    },\n    //theme入住离开的区间背景色\n    themeOpacityBg(day, month, year) {\n        if (!this.themeColor) return;\n        if (!day) return;\n        const _date = this.resetTime(year + \"/\" + month + \"/\" + day);\n        if (_date >= this.startDates && _date <= this.endDates && this.mode > 1) {\n            return this.getBetweenColor;\n        }\n    },\n    //theme获取普通日期选中样式背景\n    themeBg(day, month, year) {\n        if (!this.themeColor) return;\n        const _date = this.resetTime(year + \"/\" + month + \"/\" + day);\n        //正常模式\n        if (this.mode == 1 ) {\n            if (_date == this.startDates) {\n                return this.themeColor;\n            }\n        }else if(this.mode ==4){\r\n\t\t\tif(_date == '1661529600000'){\r\n\t\t\t\treturn this.selectedColor;\r\n\t\t\t}\r\n\t\t} else {\n            //酒店和往返模式\n            if (_date == this.startDates || _date == this.endDates) {\n                return this.themeColor;\n            }\n        }\n    },\n    //清除时间 时 分 秒 毫秒\n    resetTime(dateStr) {\n        var date = new Date(dateStr.replace(/-/g, \"/\"));\n        date.setHours(0);\n        date.setMinutes(0);\n        date.setSeconds(0);\n        date.setMilliseconds(0);\n        return date * 1;\n    },\n    //flag==1（返回今天，明天，后天)，flag==2（返回入住，离开，去返)\n    setTip(day, month, year,flag) {\n        if (!day) return\n        var tip = \"\"\n        var _date = this.resetTime(year + \"/\" + month + \"/\" + day);\n        if(flag==1){\n            if (_date == this.today) {\n                tip = \"今天\";\n            } else if (_date - this.today == 24 * 3600 * 1000) {\n                tip = \"明天\";\n            } else if (_date - this.today == 2 * 24 * 3600 * 1000) {\n                tip = \"后天\";\n            }\n            return tip\n        }else{\n            if (this.mode == 2) {\n                if (_date == this.endDates) {\n                    tip = \"离开\";\n                } else if (_date == this.startDates) {\n                    tip = \"入住\";\n                }\n            } else if (this.mode == 3) {\n                if (_date == this.startDates && !this.endDates) {\n                    tip = \"去/返\";\n                } else {\n                    if (_date == this.endDates) {\n                        tip = \"返程\";\n                    } else if (_date == this.startDates) {\n                        tip = \"去程\";\n                    }\n                }\n            }\n            return tip;\n        }\n    },\r\n\tsetOrderTip(day, month, year,flag) {\r\n\t    if (!day) return\r\n\t    var tip = \"\"\r\n\t    var _date = this.resetTime(year + \"/\" + month + \"/\" + day);\r\n\t\t\r\n\t\tvar orderDate =[{date:\"1661529600000\",value:'待收货'},{date:\"1661702400000\",value:'待派送'}];\r\n\t\tfor(var i=0; i< orderDate.length;i++){\r\n\t\t\tif (_date == orderDate[i]['date']) {\r\n\t\t\t   tip = orderDate[i]['value'];\r\n\t\t\t}\r\n\t\t}\r\n\t    return tip;\r\n\t},\n    //是否是选中当天，或者入住离开当天\n    isCurrent(day, month, year) {\n      if (!day) {\n        return false;\n      }\n      const _date = this.resetTime(year + \"/\" + month + \"/\" + day);\n      //正常模式\n      if (this.mode == 1 || this.mode == 4 ) {\n        if (_date == this.startDates) {\n          return true;\n        }\n      } else {\n        //酒店和往返模式\n        if (_date == this.startDates || _date == this.endDates) {\n          return true;\n        }\n      }\n    },\n    dateFormat(times) {\n        let date = new Date(times);\n        let recent = \"\";\n        if (times == this.today) {\n            recent = \"今天\";\n        } else if (times - this.today === 24 * 3600 * 1000) {\n            recent = \"明天\";\n        } else if (times - this.today === 2 * 24 * 3600 * 1000) {\n            recent = \"后天\";\n        }\n        var year = date.getFullYear()\n        var month = parseInt(date.getMonth() + 1) > 9 ? parseInt(date.getMonth() + 1) : '0' + parseInt(date.getMonth() + 1)\n        var day =  date.getDate() > 9 ? date.getDate() : '0' + date.getDate()\n        return {\n            dateStr: year + \"-\" + month + \"-\" + day,\n            week: \"周\" + this.weekList[date.getDay()],\n            recent\n        };\n    },\n    chooseDate(day, month, year) {\n        const _date = this.resetTime(year + \"/\" + month + \"/\" + day);\n        const week = this.weekList[new Date(_date).getDay()];\r\n\t\tif(this.chooseType=='2'){\r\n\t\t\tif(week=='六' || week=='日'){\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t}else if(this.chooseType=='3'){\r\n\t\t\tif(week=='一' || week=='二' || week=='三' || week=='四' || week=='五'){\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t}\n        //判断日期区域是否可点击\n        if (!day) return;\n        if (this.betweenStarts) {\n            if (_date * 1 < this.betweenStarts) return;\n        } else {\n            if (_date < this.today) return;\n        }\n        if (_date > this.betweenEnds) return;\n        //判断酒店或者往返模式的选择逻辑\n        if (this.startDates && this.endDates && _date > this.endDates) {\n            this.startDates = _date;\n            this.endDates = \"\";\n        } else if (this.endDates && _date > this.endDates) {\n            this.endDates = _date;\n        } else if (_date >= this.startDates && _date <= this.endDates) {\n            this.startDates = _date;\n            this.endDates = \"\";\n        } else if (_date < this.startDates) {\n            this.startDates = _date;\n            this.endDates = \"\";\n        } else if (_date > this.startDates) {\n            if (this.mode == 1 || this.mode == 4) {\n                this.startDates = _date;\n            } else {\n                this.endDates = _date;\n            }\n        }\n        const choose = {\n            startStr: this.dateFormat(this.startDates)\n        };\n        if (this.mode == 1 || this.mode == 4) {\n            this.$emit(\"callback\", choose);\n        } else if (this.mode == 2 && this.startDates && this.endDates) {\n            choose.dayCount = (this.endDates - this.startDates) / 24 / 3600 / 1000;\n         \r\n\t\t\t\t\t\tif( choose.dayCount>this.maxdays){\r\n\t\t\t\t\t\t\treturn app.error(\"最多可预定\"+this.maxdays+'晚');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tchoose.endStr = this.dateFormat(this.endDates);\r\n\t\t\t\t\t\t\n            this.$emit(\"callback\", choose);\n        } else if (this.mode == 3) {\n            if (this.startDates && this.endDates) {\n                choose.dayCount = (this.endDates - this.startDates) / 24 / 3600 / 1000;\n                choose.endStr = this.dateFormat(this.endDates);\n            } else if (this.startDates && !this.endDates) {\n                choose.dayCount = 0;\n                choose.endStr = this.dateFormat(this.startDates);\n            }\n            this.$emit(\"callback\", choose);\n        }else if(this.startDates && this.endDates){\r\n\t\t\tif (this.startDates && this.endDates) {\r\n\t\t\t    choose.dayCount = (this.endDates - this.startDates) / 24 / 3600 / 1000;\r\n\t\t\t    choose.endStr = this.dateFormat(this.endDates);\r\n\t\t\t} else if (this.startDates && !this.endDates) {\r\n\t\t\t    choose.dayCount = 0;\r\n\t\t\t    choose.endStr = this.dateFormat(this.startDates);\r\n\t\t\t}\r\n\t\t\tthis.$emit(\"callback\", choose);\r\n\t\t}\n    }\n  }\n};\n</script>\n\n<style lang=\"less\" scoped>\r\n\n@color: #1C75FF;\n.calendar-tz {\n    width: 100%;\n    height: 50vh;\n    background: #fff;\n    display: -webkit-box;\n    display: flex;\n    -webkit-flex-direction: column;\n    flex-direction: column;\n    &.fixed{\n        position: fixed;\n        width:100%;\n        height:100%;\n        left:0;\n        top:0;\n        z-index: 900;\n    }\n    .week-number {\n        background: #fff;\n        padding: 0 1%;\n        box-shadow: 0 2px 15px rgba(100, 100, 100, 0.1);\n        span {\n            display: inline-block;\n            text-align: center;\n            padding: 12px 0;\n            font-size: 14px;\n            width: 14.28%;\n            &:first-child,\n            &:last-child {\n                color: @color;\n            }\n        }\n    }\n    .tips {\n        padding: 6px 10px;\n        background: #fff7dc;\n        font-size: 12px;\n        color: #9e8052;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n    }\n    .content{\n        -webkit-box-flex: 1;\n        flex:1;\n        overflow-y: scroll;\n        -webkit-overflow-scrolling: touch;\r\n\t\tpadding-bottom: 100rpx;\n         .con {\n            color: #333;\n            padding-top: 10px;\n            position: relative;\n            h3 {\n                width: 100%;\n                font-weight: normal;\n                text-align: center;\n                font-size: 16px;\n                padding: 10px 0;\n            }\n            .month-bg{\n                position: absolute;\n                text-align: center;\n                opacity: 0.4;\n                left:0;\n                right:0;\n                bottom:0;\n                top:20%;\n                font-size:220px;\n                font-weight: bold;\n                color:#f8f8f8;\n            }\n            .each-month {\n                display: block;\n                width: 98%;\n                font-size: 0;\n                margin: 0 auto;\n                padding-left: 0;\n                padding-bottom: 10px;\n                border-bottom: 1px solid #eee;\n                .each-day {\n                    position: relative;\n                    display: inline-block;\n                    text-align: center;\n                    vertical-align: middle;\n                    width: 14.28%;\n                    font-size: 16px;\n                    height: 50px;\n                    margin:2px auto;\n                    div {\n                        display: inline-block;\n                        font-size: 14px;\n                        width:98%;\n                        height:100%;\n                        justify-content: space-around;\n                        display: -webkit-box;\n                        display: flex;\n                        -webkit-flex-direction: column;\n                        flex-direction: column;\n                        border-radius: 4px;\n                    }\n                    &.between {\n                        background: rgba(75, 217, 173, 0.1);\n                    }\n                    .day{\n                        font-size: 16px;\n                    }\n                    .day-tip,.recent{\n                        font-size:10px;\n                        height:14px;\n                        i{\n                            font-size:10px;\n                        }\n                    }\n                    .recent {\n                        color: #999;\n                    }\n                    .disabled {\n                        color: #ccc !important;\n                        .day-tip{\n                            color: #ccc !important;\n                        }\n                    }\n                    .today {\n                        background: rgba(100,100,100,0.1);\n                    }\n                    .trip-time {\n                        background: @color;\n                        color: #fff !important;\n                        .recent,.day-tip{\n                            color: #fff!important;\n                        }\n                    }\r\n\t\t\t\t\t.trip-time-order {\r\n\t\t\t\t\t    background: #f44336;\r\n\t\t\t\t\t    color: #fff !important;\r\n\t\t\t\t\t    .recent,.day-tip{\r\n\t\t\t\t\t        color: #fff!important;\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t}\n                    .weekend {\n                        color: @color;\n                    }\n                }\n            }\n        }\n    }\n}\n/***右侧进入动画***/\n.slide-enter-active,\n.slide-leave-active {\n  -webkit-transition: all 0.2s ease;\n  transition: all 0.2s ease;\n}\n.slide-enter,\n.slide-leave-to {\n  -webkit-transform: translateX(100%);\n  transform: translateX(100%);\n}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Calendar.vue?vue&type=style&index=0&id=052beb44&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Calendar.vue?vue&type=style&index=0&id=052beb44&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754212986213\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./dp-hotel-room.vue?vue&type=template&id=1cf37070&\"\nvar renderjs\nimport script from \"./dp-hotel-room.vue?vue&type=script&lang=js&\"\nexport * from \"./dp-hotel-room.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dp-hotel-room.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/dp-hotel-room/dp-hotel-room.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-hotel-room.vue?vue&type=template&id=1cf37070&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n    calendar: function () {\n      return import(\n        /* webpackChunkName: \"components/calendar/calendar\" */ \"@/components/calendar/calendar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = _vm.isload\n    ? _vm.__map(_vm.roomlist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 =\n          _vm.params.showtag == 1\n            ? _vm.roomstyle == 1 && item.tag.length > 0\n            : null\n        var l0 =\n          _vm.params.showtag == 1 && g0\n            ? _vm.__map(item.tag, function (items, indexs) {\n                var $orig = _vm.__get_orig(items)\n                var m0 = _vm.t(\"color1rgb\")\n                var m1 = _vm.t(\"color1\")\n                return {\n                  $orig: $orig,\n                  m0: m0,\n                  m1: m1,\n                }\n              })\n            : null\n        var m2 =\n          _vm.params.showprice == 1 && item.min_daymoney\n            ? _vm.t(\"color1\")\n            : null\n        var m3 =\n          _vm.params.showprice == 1 && item.min_daymoney\n            ? _vm.t(\"余额单位\")\n            : null\n        var m4 =\n          _vm.params.showprice == 1 && !item.min_daymoney\n            ? _vm.t(\"color1\")\n            : null\n        var m5 =\n          _vm.btnstyle == 1 && item.stock > 0 && !item.isbooking\n            ? _vm.t(\"color1rgb\")\n            : null\n        var m6 =\n          _vm.btnstyle == 2 && item.stock > 0 && !item.isbooking\n            ? _vm.t(\"color1\")\n            : null\n        var m7 =\n          _vm.btnstyle == 2 && item.stock > 0 && !item.isbooking\n            ? _vm.t(\"color1\")\n            : null\n        var m8 =\n          _vm.btnstyle == 2 && item.stock > 0 && !item.isbooking\n            ? _vm.t(\"color1\")\n            : null\n        var m9 =\n          _vm.btnstyle == 2 && item.stock > 0 && !item.isbooking\n            ? _vm.t(\"color1\")\n            : null\n        return {\n          $orig: $orig,\n          g0: g0,\n          l0: l0,\n          m2: m2,\n          m3: m3,\n          m4: m4,\n          m5: m5,\n          m6: m6,\n          m7: m7,\n          m8: m8,\n          m9: m9,\n        }\n      })\n    : null\n  var g1 = _vm.isload ? _vm.bannerList.length : null\n  var g2 = _vm.isload && g1 ? _vm.bannerList.length : null\n  var m10 = _vm.isload && _vm.room.isdaymoney == 1 ? _vm.t(\"余额单位\") : null\n  var m11 = _vm.isload && _vm.room.isdaymoney == 1 ? _vm.t(\"余额单位\") : null\n  var m12 = _vm.isload && _vm.room.isdaymoney == 1 ? _vm.t(\"余额\") : null\n  var m13 = _vm.isload && _vm.room.isdaymoney == 1 ? _vm.t(\"余额\") : null\n  var m14 = _vm.isload ? _vm.t(\"color1\") : null\n  var m15 = _vm.isload && _vm.room.isdaymoney == 1 ? _vm.t(\"余额单位\") : null\n  var m16 =\n    _vm.isload && _vm.minstock > 0 && !_vm.room.isbooking\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m17 = _vm.isload && _vm.calendarvisible ? _vm.t(\"color1\") : null\n  var m18 = _vm.isload && _vm.calendarvisible ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n        g1: g1,\n        g2: g2,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-hotel-room.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-hotel-room.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"dp-hotel\" :style=\"{\r\n\tbackgroundColor:params.bgcolor,\r\n\tmargin:(params.margin_y*2.2)+'rpx '+params.margin_x*2.2+'rpx 0',\r\n\tpadding:(params.padding_y*2.2)+'rpx '+params.padding_x*2.2+'rpx'\r\n}\">\r\n<block v-if=\"isload\">\r\n\t<view class=\"dp-hotel-item\">\r\n\t\t\r\n\t\t<view class=\"time-view flex flex-y-center flex-bt\" @tap=\"selectDate\">\r\n\t\t\t<view class=\"time-options flex flex-y-center flex-bt\">\r\n\t\t\t\t<view class=\"month-tetx\">{{startDate}}</view>\r\n\t\t\t\t<view class=\"day-tetx\">{{startWeek}}入住</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content-text\">\r\n\t\t\t\t<view class=\"content-decorate left-c-d\"></view>\r\n\t\t\t\t共{{dayCount}}晚\r\n\t\t\t\t<view class=\"content-decorate right-c-d\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"time-options flex flex-y-center flex-bt\">\r\n\t\t\t\t<view class=\"month-tetx\">{{endDate}}</view>\r\n\t\t\t\t<view class=\"day-tetx\">{{endWeek}}离店</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"hotels-list\">\r\n\t\t\t<block v-for=\"(item,index) in roomlist\" >\r\n\t\t\t\t<view class=\"hotels-options\" @tap.stop=\"hotelDetail\" :data-id='item.id'>\r\n\t\t\t\t\t<view :class=\"(roomstyle==1?'hotel-img':'fangxing')\">\r\n\t\t\t\t\t\t<image :src=\"item.pic\" ></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"hotel-info\">\r\n\t\t\t\t\t\t<view class=\"hotel-title\" v-if=\"params.showname==1\">{{item.name}}</view>\r\n\t\t\t\t\t\t<block v-if=\"params.showtag==1\">\r\n\t\t\t\t\t\t\t<block v-if=\"roomstyle ==1 && item.tag.length>0\">\r\n\t\t\t\t\t\t\t\t<view class=\"hotel-characteristic\" >\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(items,indexs) in item.tag\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"characteristic-options\" :style=\"'background:rgba('+t('color1rgb')+',0.05);color:'+t('color1')\t\">{{items}}</view>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</view> \r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<view class=\"hotel-characteristic \" v-else>\r\n\t\t\t\t\t\t\t\t<view class=\"under_title\" v-if=\"item.bedxing!='不显示'\">\r\n\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{item.bedxing}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"under_title\">\r\n\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{item.square}}m²</view>\r\n\t\t\t\t\t\t\t\t</view>\t\r\n\t\t\t\t\t\t\t\t<view class=\"under_title\" v-if=\"item.ischuanghu!='不显示'\">\r\n\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{item.ischuanghu}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"under_title\" v-if=\"item.breakfast!='不显示'\">\r\n\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t<view class=\"options-title\">{{item.breakfast}}早餐</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\r\n\t\t\t\t\t\t<view class=\"hotel-but-view\">\r\n\t\t\t\t\t\t\t<view class=\"make-info\" >\r\n\t\t\t\t\t\t\t\t<block v-if=\"params.showprice==1\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"hotel-price\" :style=\"{color:t('color1')}\" v-if=\"item.min_daymoney\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"hotel-price-num\">{{item.min_daymoney}}{{t('余额单位')}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view>/晚起</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"hotel-price\" :style=\"{color:t('color1')}\" v-else>\r\n\t\t\t\t\t\t\t\t\t\t<view>￥</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"hotel-price-num\" >{{item.sell_price}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view>起</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"params.showsales==1\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"hotel-text\" v-if=\"roomstyle == 1\">\r\n\t\t\t\t\t\t\t\t\t\t{{item.sales}}人已预定 | 剩{{item.stock}}{{text['间']}}可订</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"hotel-text\" v-else>{{item.sales}}人已预定</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\t\t\t\r\n\t\t\t\t\t\t\t<block  v-if=\"btnstyle==1\">\r\n\t\t\t\t\t\t\t\t<view class=\"hotel-make\" v-if=\"item.stock>0 && !item.isbooking\"  :style=\"'background:rgba('+t('color1rgb')+',0.8);color:#FFF'\">预约</view>\r\n\t\t\t\t\t\t\t\t<view class=\"hotel-make\" v-else-if=\"item.isbooking\" style=\"background:#999999; color:#fff\">不可订</view>\r\n\t\t\t\t\t\t\t\t<view class=\"hotel-make\" v-else style=\"background:#999999; color:#fff\">已满</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\t<block v-if=\"btnstyle==2\">\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.stock>0 && !item.isbooking\" class=\"hotel-make-new\" :style=\"{borderColor:t('color1')}\">\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t<view class=\"make-new-view\" :style=\"'background:'+t('color1')+''\">订</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"make-new-view2\" :style=\"{color:t('color1'),borderColor:t('color1')}\">剩{{item.stock}}{{text['间']}}</view>\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view  v-else-if=\"item.isbooking\"  class=\"hotel-make-new\" style=\"background:#999999;border: 1px #999999 solid; color:#fff\">\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t<view class=\"make-new-view\" style=\"background:#999999; color:#fff\">不可订</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"make-new-view2\" style=\"background:#999999;border-bottom: 1px #999999 solid; color:#fff\">剩{{item.stock}}{{text['间']}}</view>\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view  v-else class=\"hotel-make-new\" style=\"background:#999999;border: 1px #999999 solid; color:#fff\">\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t<view class=\"make-new-view\" style=\"background:#999999; color:#fff\">已满</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"make-new-view2\" style=\"background:#999999;border-bottom: 1px #999999 solid; color:#fff\">剩{{item.stock}}{{text['间']}}</view>\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\t\r\n\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t</view>\r\n\t\r\n\t\r\n\t<!-- 详情弹窗 -->\r\n\t<uni-popup id=\"popup\" ref=\"popup\" type=\"bottom\" >\r\n\t\t<view class=\"popup__content\" style=\"bottom: 0;padding-top:0;padding-bottom:0; max-height: 86vh; \">\r\n\t\t\t<view class=\"popup-close\" @click=\"popupdetailClose\">\r\n\t\t\t\t<image :src=\"`${pre_url}/static/img/hotel/popupClose.png`\"></image>\r\n\t\t\t</view>\r\n\t\t\t<scroll-view scroll-y style=\"height: auto;max-height: 90vh;\">\r\n\t\t\t\t<view class=\"popup-banner-view\" style=\"height: 450rpx;\">\r\n\t\t\t\t\t<swiper class=\"dp-banner-swiper\" :autoplay=\"true\" :indicator-dots=\"false\" :current=\"0\" :circular=\"true\" :interval=\"3000\" @change='swiperChange'>\r\n\t\t\t\t\t\t<block v-for=\"(item,index) in room.pics\" :key=\"index\"> \r\n\t\t\t\t\t\t\t<swiper-item>\r\n\t\t\t\t\t\t\t\t<view @click=\"viewPicture(item)\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item\" class=\"dp-banner-swiper-img\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</swiper>\r\n\t\t\t\t\t<view class=\"popup-numstatistics flex flex-xy-center\" v-if='bannerList.length'>\r\n\t\t\t\t\t\t{{bannerindex}} / {{bannerList.length}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"hotel-details-view flex flex-col\">\r\n\t\t\t\t\t<view class=\"hotel-title\">{{room.name}}</view>\r\n\t\t\t\t\t<view class=\"introduce-view flex \">\r\n\t\t\t\t\t\t<view class=\"options-intro flex flex-y-center\" v-if=\"room.bedxing!='不显示'\">\r\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/dachuang.png'\"></image>\r\n\t\t\t\t\t\t\t<view class=\"options-title\">{{room.bedxing}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"options-intro flex flex-y-center\">\r\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/pingfang.png'\"></image>\r\n\t\t\t\t\t\t\t<view class=\"options-title\">{{room.square}}m²</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"options-intro flex flex-y-center\">\r\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/dachuang.png'\"></image>\r\n\t\t\t\t\t\t\t<view class=\"options-title\">{{room.bedwidth}}米</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\r\n\t\t\t\t\t\t<view class=\"options-intro flex flex-y-center\" v-if=\"room.ischuanghu!='不显示'\">\r\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/youchuang.png'\"></image>\r\n\t\t\t\t\t\t\t<view class=\"options-title\">{{room.ischuanghu}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"options-intro flex flex-y-center\" v-if=\"room.breakfast!='不显示'\">\r\n\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/hotel/zaocan.png'\"></image>\r\n\t\t\t\t\t\t\t<view class=\"options-title\">{{room.breakfast}}早餐</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"other-view flex flex-y-center\">\r\n\t\t\t\t\t\t<view class=\"other-title\">特色</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"other-text\" style=\"white-space: pre-line;\">{{room.tese}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"other-view flex flex-y-center\">\r\n\t\t\t\t\t\t<view class=\"other-title\">房型详情</view>\r\n\t\t\t\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 酒店权益 -->\r\n\t\t\t\t<view class=\"hotel-equity-view flex flex-col\" v-if=\"qystatus == 1\">\r\n\t\t\t\t\t<view class=\"equity-title-view flex\">\r\n\t\t\t\t\t\t<view class=\"equity-title\">{{text['酒店']}}权益</view>\r\n\t\t\t\t\t\t<!--<view class=\"equity-title-tisp\">填写订单时兑换</view>-->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t\t<view class=\"equity-options flex flex-col\">\r\n\t\t\t\t\t\t\t<parse :content=\"hotel.hotelquanyi\" @navigate=\"navigate\"></parse>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 政策服务 -->\r\n\t\t\t\t<view class=\"hotel-equity-view flex flex-col\"  v-if=\"fwstatus == 1\">\r\n\t\t\t\t\t<view class=\"equity-title-view flex\">\r\n\t\t\t\t\t\t<view class=\"equity-title\">政策服务</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"equity-options flex flex-col\">\r\n\t\t\t\t\t\t\t<parse :content=\"hotel.hotelfuwu\" @navigate=\"navigate\"></parse>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t<!-- 费用明细 -->\r\n\t\t\t\t<view class=\"hotel-equity-view flex flex-col\">\r\n\t\t\t\t\t<view class=\"equity-title-view flex\">\r\n\t\t\t\t\t\t<view class=\"equity-title\">费用明细</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"cost-details flex flex-col\">\r\n\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\">\r\n\t\t\t\t\t\t\t<view class=\"price-text\">押金（可退）</view>\r\n\t\t\t\t\t\t\t<view class=\"price-num\">￥{{yajin}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\">\r\n\t\t\t\t\t\t\t<view class=\"price-text\">{{text['服务费']}}</view>\r\n\t\t\t\t\t\t\t<view class=\"price-num\">￥{{service_money}}/天</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\" v-if=\"room.isdaymoney==1\">\r\n\t\t\t\t\t\t\t<view class=\"price-text\">房费</view>\r\n\t\t\t\t\t\t\t<view class=\"price-num\">{{room.daymoney}}{{t('余额单位')}}/晚</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"price-view flex flex-bt flex-y-center\" v-else>\r\n\t\t\t\t\t\t\t<view class=\"price-text\">房费</view>\r\n\t\t\t\t\t\t\t<view class=\"price-num\">￥{{room.price}}/晚</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\r\n\t\t\t\t\t\t<view class=\"price-view flex flex-y-center\" style=\"justify-content: flex-end;margin-top: 30rpx;margin-bottom: 0rpx;align-items: center;padding-bottom: 0rpx;\">\r\n\t\t\t\t\t\t\t<view class=\"price-text\" style=\"font-size: 28rpx;margin-right: 15rpx;\">每日金额</view>\r\n\t\t\t\t\t\t\t<view class=\"price-num flex flex-y-center\"  v-if=\"room.isdaymoney==1\">\r\n\t\t\t\t\t\t\t\t<view style=\"font-size: 24rpx;font-weight: none;margin-top: 5rpx;\">￥</view>\r\n\t\t\t\t\t\t\t\t<view style=\"font-size: 44rpx;\">{{totalprice}}+{{room.daymoney}}{{t('余额单位')}}</view>\t\t\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"price-num flex flex-y-center\"  v-else>\r\n\t\t\t\t\t\t\t\t<view style=\"font-size: 24rpx;font-weight: none;margin-top: 5rpx;\">￥</view>\r\n\t\t\t\t\t\t\t\t<view style=\"font-size: 44rpx;\">{{totalprice}}</view>\t\t\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view v-if=\"room.isdaymoney==1\" class=\"tips\">未有旅居{{t('余额')}},支付{{room.price}}/晚或去获取旅居{{t('余额')}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view style=\"height:260rpx\"></view>\r\n\t\t\t\t\r\n\t\t\t</scroll-view>\r\n\t\t\t<!-- 预定 -->\r\n\t\t\t<view class=\"popup-but-view flex flex-col\" style=\"bottom: 0;\">\r\n\t\t\t\t<view class=\"price-statistics flex flex-y-center\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t<view class=\"title-text\">每日金额：</view>\r\n\t\t\t\t\t<view class=\"price-text flex\" v-if=\"room.isdaymoney==1\">\r\n\t\t\t\t\t\t<view style=\"font-size: 22rpx;margin-top: 8rpx;\">￥</view>\r\n\t\t\t\t\t\t<view style=\"font-weight: bold;font-size: 36rpx;\">{{totalprice}}+{{room.daymoney}}{{t('余额单位')}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"price-text flex\" v-else>\r\n\t\t\t\t\t\t<view style=\"font-size: 22rpx;margin-top: 8rpx;\">￥</view>\r\n\t\t\t\t\t\t<view style=\"font-weight: bold;font-size: 36rpx;\">{{totalprice}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!--<view class=\"title-text\">共减：</view>\r\n\t\t\t\t\t<view class=\"price-text flex\">\r\n\t\t\t\t\t\t<view style=\"font-size: 22rpx;margin-top: 8rpx;\">￥</view>\r\n\t\t\t\t\t\t<view style=\"font-weight: bold;font-size: 36rpx;\">123.00</view>\r\n\t\t\t\t\t</view>-->\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"detail_but-class\" v-if=\"minstock>0 && !room.isbooking\" @tap=\"tobuy\" :style=\"'background:rgba('+t('color1rgb')+',1);color:#FFF'\">预定</view>\r\n\t\t\t\t<view class=\"but-class\" v-else-if=\"room.isbooking && minstock>0\" :style=\"'background: #999999;color:#fff'\">不可订</view>\r\n\t\t\t\t<view class=\"but-class\" v-else :style=\"'background: #999999;color:#fff'\">已订满</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</uni-popup>\r\n\t\r\n\t\r\n\t\r\n\t<!-- 选择日期弹窗 -->\r\n\t<view v-if=\"calendarvisible\" class=\"popup__container\">\r\n\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t<view class=\"popup__modal\">\r\n\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t<text class=\"popup__title-text\">选择日期</text>\r\n\t\t\t\t<image :src=\"`${pre_url}/static/img/hotel/popupClose2.png`\" class=\"popup__close\" style=\"width:56rpx;height:56rpx;top:20rpx;right:20rpx\" @tap.stop=\"handleClickMask\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t<view class=\"reserve-time-view\" style=\"border-bottom: 0;\">\r\n\t\t\t\t\t<view class=\"time-view\">\r\n\t\t\t\t\t\t<view class='time-title'>入住</view>\r\n\t\t\t\t\t\t<view class=\"flex flex-y-center\" style=\"margin-top: 15rpx;align-items: flex-end;\">\r\n\t\t\t\t\t\t\t<view class=\"date-time\">{{startDate}}</view>\r\n\t\t\t\t\t\t\t<view class='time-title'>{{startWeek}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='statistics-view'>\r\n\t\t\t\t\t\t<view class=\"statistics-date\">\r\n\t\t\t\t\t\t\t<view class=\"content-decorate left-c-d\"></view>\r\n\t\t\t\t\t\t\t共{{dayCount}}晚\r\n\t\t\t\t\t\t\t<view class=\"content-decorate right-c-d\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"color-line\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"time-view\">\r\n\t\t\t\t\t\t<view class='time-title'>离店</view>\r\n\t\t\t\t\t\t\t<view class=\"flex flex-y-center\" style=\"margin-top: 15rpx;align-items: flex-end;\">\r\n\t\t\t\t\t\t\t\t<view class=\"date-time\">{{endDate}}</view>\r\n\t\t\t\t\t\t\t\t<view class='time-title'>{{endWeek}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"calendar-view\">\r\n\t\t\t\t\t<calendar :is-show=\"true\" :isFixed='false' showstock='1':text=\"text\"  :dayroomprice=\"dayroomprice\" :start-date=\"starttime\" :end-date=\"endtime\" mode=\"2\"  @callback=\"getDate\"  :maxdays='maxdays'  :themeColor=\"t('color1')\" :between-end=\"maxenddate\">\r\n\t\r\n\t\t\t\t\t</calendar>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"choose-but-class\" :style=\"{background:'rgba('+t('color1rgb')+',1)'}\" @tap=\"popupClose\">\r\n\t\t\t\t\t确认{{dayCount}}晚\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</block>\t\r\n</view>\r\n</template>\r\n<script>\r\n\timport calendar from '@/hotel/mobile-calendar-simple/Calendar.vue'\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tparams:{},\r\n\t\t\tdata:{},\r\n\t\t},\r\n\t\tdata(){\r\n\t\t\treturn{\r\n\t\t\t\tisload: false,\r\n\t\t\t\tstartDate:'',\r\n\t\t\t\tendDate:'',\r\n\t\t\t\tdayCount:1,\r\n\t\t\t\tstartWeek:'',\r\n\t\t\t\tendWeek:'',\r\n\t\t\t\tstarttime:'',\r\n\t\t\t\tcalendarvisible:false,\r\n\t\t\t\ttext:[],\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\tmaxenddate:'',\r\n\t\t\t\tdayroomprice:[],\r\n\t\t\t\tminday:0,\r\n\t\t\t\tminstock:0,\r\n\t\t\t\tmaxdays:0,\r\n\t\t\t\tbannerindex:1,\r\n\t\t\t\tbannerList:[],\r\n\t\t\t\troom:[],\r\n\t\t\t\ttotalprice:0,\r\n\t\t\t\tyajin:0,\r\n\t\t\t\tservice_money:0,\r\n\t\t\t\tqystatus:0,\r\n\t\t\t\tfwstatus:0,\r\n\t\t\t\tpagecontent: \"\",\r\n\t\t\t\troomids:[],\r\n\t\t\t\troomlist:[],\r\n\t\t\t\thotel:[],\r\n\t\t\t\tsortby:'',\r\n\t\t\t\troomstyle:1,\r\n\t\t\t\tbtnstyle:1,\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthis.getdata();\r\n\t\t\t\tvar rooms = that.data\r\n\t\t\t\tvar roomids = [];\r\n\t\t\t\trooms.forEach(item => {\r\n\t\t\t\t\troomids.push(item.roomid)\r\n\t\t\t\t})\r\n\t\t\t\tthat.roomids = roomids;\r\n\t\t\t\tthat.sortby = this.params.sortby\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetdata:function(e){\r\n\t\t\t\tvar that=this\r\n\t\t\t\tvar starttime = app.getCache('startTime');\r\n\t\t\t\tvar endtime = app.getCache('endTime');\r\n\t\t\t\tapp.post('ApiHotel/getsysset', { starttime:starttime,endtime:endtime }, function (res) { \r\n\t\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\t\tthat.set=res.set\r\n\t\t\t\t\t\t\tthat.catelist = res.catelist\r\n\t\t\t\t\t\t\tvar daycount = app.getCache('dayCount');\r\n\t\t\t\t\t\t\tif(res.isshownow || !starttime){\r\n\t\t\t\t\t\t\t\t\tstarttime = res.startday\r\n\t\t\t\t\t\t\t\t\tapp.setCache('startTime',starttime);\r\n\t\t\t\t\t\t\t\t\tendtime = res.endday\r\n\t\t\t\t\t\t\t\t\tapp.setCache('endTime',endtime);\r\n\t\t\t\t\t\t\t\t\tdaycount = 1;\r\n\t\t\t\t\t\t\t\t\tapp.setCache('dayCount',daycount);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tconsole.log(starttime);\r\n\t\t\t\t\t\t\tvar weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\r\n\t\t\t\t\t\t\tvar day = new Date(starttime).getDay();\r\n\t\t\t\t\t\t\tvar day2 = new Date(endtime).getDay();\r\n\t\t\t\t\t\t\tvar startWeek = weekdays[day];\r\n\t\t\t\t\t\t\tvar endWeek = weekdays[day2];\r\n\t\t\t\t\t\t\tvar startDate = starttime.substr(5).replace('-', '月');\r\n\t\t\t\t\t\t\tvar endDate = endtime.substr(5).replace('-', '月');\t\t\r\n\t\t\t\t\t\t\tthat.starttime = starttime;\r\n\t\t\t\t\t\t\tthat.endtime = endtime;\r\n\t\t\t\t\t\t\tthat.startDate = startDate\r\n\t\t\t\t\t\t\tapp.setCache('startDate',startDate);\r\n\t\t\t\t\t\t\tapp.setCache('endDate',endDate);\r\n\t\t\t\t\t\t\tthat.endDate = endDate\r\n\t\t\t\t\t\t\tthat.startWeek = startWeek\r\n\t\t\t\t\t\t\tthat.endWeek = endWeek\r\n\t\t\t\t\t\t\tthat.dayCount = daycount\t\r\n\t\t\t\t\t\t\tthat.text = res.text\r\n\t\t\t\t\t\t\tthat.roomstyle = res.set.roomstyle;\r\n\t\t\t\t\t\t\tthat.btnstyle = res.set.btnstyle;\r\n\t\t\t\t\t\t\tconsole.log(that.btnstyle);\r\n\t\t\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\t\t\ttitle:res.text['酒店']+'详情'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tthat.loaded();\r\n\t\t\t\t\t\t\tthat.getdetail()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t })\r\n\t\t\t\t},\r\n\t\t\t\tgetdetail:function(loadmore){\r\n\t\t\t\t\tvar that=this\r\n\t\t\t\t\tif(!loadmore){\r\n\t\t\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\t\t\tthis.datalist = [];\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.loading = true;\r\n\t\t\t\t\tthat.nodata = false;\r\n\t\t\t\t\tthat.nomore = false;\r\n\t\t\t\t\tapp.post('ApiHotel/getRoomList', {sortby:that.sortby, roomids:that.roomids,starttime:that.starttime,endtime:that.endtime,daycount:that.dayCount}, function (res) {\r\n\t\t\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\t\t\tvar data = res.datalist;\r\n\t\t\t\t\t\t\t\tthat.roomlist = res.datalist;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t},\r\n\t\t\t\tselectDate:function(){\r\n\t\t\t\t\t// 选择日期弹窗-------------------------------------------------------------------------------------------\r\n\t\t\t\t\t//this.$refs.popupTime.open();\r\n\t\t\t\t\tthis.calendarvisible = true;\r\n\t\t\t\t},\r\n\t\t\t\thandleClickMask:function(){\r\n\t\t\t\t\tthis.calendarvisible = false;\r\n\t\t\t\t},\r\n\t\t\t\tgetDate(date){\r\n\t\t\t\t\tvar that=this\r\n\t\t\t\t\tif(date.dayCount){\r\n\t\t\t\t\t\t\tapp.setCache('dayCount',date.dayCount)\r\n\t\t\t\t\t\t\tthat.dayCount = date.dayCount;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(date.startStr){\r\n\t\t\t\t\t\t\tvar starttime =  date.startStr.dateStr\r\n\t\t\t\t\t\t\tapp.setCache('startTime',starttime);\r\n\t\t\t\t\t\t\tvar startDate = starttime.substr(5).replace('-', '月');\r\n\t\t\t\t\t\t\tapp.setCache('startDate',startDate);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tthat.starttime = starttime\r\n\t\t\t\t\t\t\tthat.startDate = startDate\r\n\t\t\t\t\t\t\tthat.startWeek = date.startStr.week\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(date.endStr){\r\n\t\t\t\t\t\tvar endtime =  date.endStr.dateStr\r\n\t\t\t\t\t\tapp.setCache('endTime',endtime);\r\n\t\t\t\t\t\tvar endDate = endtime.substr(5).replace('-', '月');\r\n\t\t\t\t\t\tapp.setCache('endDate',endDate);\r\n\t\t\t\t\t\tthat.endtime = endtime\r\n\t\t\t\t\t\tthat.endDate = endDate\r\n\t\t\t\t\t\tthat.endWeek = date.endStr.week\r\n\t\t\t\t\t}\t\t\r\n\t\t\t\t\t/*var minday = this.dayroomprice[starttime]['stock'];\r\n\t\t\t\t\tvar timestamp = new Date(starttime).getTime();\r\n\t\t\t\t\tfor(var i=0;i<date.dayCount;i++){\r\n\t\t\t\t\t\tvar daystr = this.dateFormat((timestamp/1000)+86400*i,'Y-m-d')\r\n\t\t\t\t\t\tif(minday>this.dayroomprice[daystr]['stock']){\r\n\t\t\t\t\t\t\tminday = this.dayroomprice[daystr]['stock'];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.minday = minday;*/\r\n\t\t\t\t\tthat.getdetail();\r\n\t\t\t\t },\r\n\t\t\t\tpopupClose(){\r\n\t\t\t\t\t//this.$refs.popupTime.close();\r\n\t\t\t\t\tthis.calendarvisible = false;\r\n\t\t\t\t},\r\n\t\t\t\thotelDetail:function(e){\r\n\t\t\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\t\t\tvar that = this\r\n\t\t\t\t\tapp.post('ApiHotel/getroomDetail', {id:id,startDate:that.starttime,endDate:that.endtime,dayCount:that.dayCount}, function (res) {\r\n\t\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\t\t\tthat.room=res.room\r\n\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\tvar pagecontent = JSON.parse(res.room.detail);\r\n\t\t\t\t\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t\t\t\t\t\tthat.hotel = res.hotel \r\n\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\tthat.minstock = res.minstock\r\n\t\t\t\t\t\t\t\tthat.calculatePrice();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// 房型详情-------------------------------------------------------------------------------------------\r\n\t\t\t\t\tthis.$refs.popup.open();\r\n\t\t\t\t},\r\n\t\t\t\tpopupdetailClose(){\r\n\t\t\t\t\tthis.$refs.popup.close();\r\n\t\t\t\t},\r\n\t\t\t\tswiperChange(event){\r\n\t\t\t\t\tthis.bannerindex = event.detail.current;\r\n\t\t\t\t},\r\n\t\t\t\t//计算价格\r\n\t\t\t\tcalculatePrice: function() {\r\n\t\t\t\t\tvar that = this;\r\n\t\t\t\t\tvar dayCount = that.dayCount;\r\n\t\t\t\t\tvar room = that.room\r\n\t\t\t\t\tconsole.log(room);\r\n\t\t\t\t\tvar totalprice = 0;\r\n\t\t\t\t\tvar service_money = 0;\r\n\t\t\t\t\tif(room.isservice_money==1){\r\n\t\t\t\t\t\tservice_money = room.service_money;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.service_money = service_money;\t\r\n\t\t\t\t\tvar yajin=0;\r\n\t\t\t\t\tif(room.isyajin==1 || room.isyajin==2){\r\n\t\t\t\t\t\t\tyajin = room.yajin_money\r\n\t\t\t\t\t}else if(room.isyajin==-1){\r\n\t\t\t\t\t\t\tyajin=0;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tif(that.hotel.isyajin==1 || that.hotel.isyajin==2){\r\n\t\t\t\t\t\t\t\tyajin = that.hotel.yajin_money\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tyajin = 0\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\t\t\t\t\r\n\t\t\t\t\tthat.yajin =parseFloat(yajin) ;\r\n\t\t\t\t\t//是否使用余额定价\r\n\t\t\t\t\tif(room.isdaymoney==1){\r\n\t\t\t\t\t\t\ttotalprice = parseFloat(service_money);\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\ttotalprice = parseFloat(service_money) + parseFloat(room.price);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.totalprice =parseFloat(totalprice).toFixed(2);;\r\n\t\t\t\t},\r\n\t\t\t\ttobuy: function (e) {\r\n\t\t\t\t\tvar that = this;\r\n\t\t\t\t\tvar roomid = that.room.id;\r\n\t\t\t\t\tvar daycount = that.dayCount;\r\n\t\t\t\t\tvar starttime = app.getCache('startTime');\r\n\t\t\t\t\tvar endtime = app.getCache('endTime');\r\n\t\t\t\t\tif(!starttime || !endtime){\r\n\t\t\t\t\t\t\treturn app.error(\"请选择入离时间\");\r\n\t\t\t\t\t}\r\n\t\t\t\t\t//var timestamp = Date.parse(str2);\r\n\t\t\t\t\tapp.goto('/hotel/index/buy?roomid=' + roomid+'&daycount='+daycount);\r\n\t\t\t\t},\r\n\t\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\t\r\n\t/*  */\r\n\t.choose-but-class{width: 94%;background: linear-gradient(90deg, #06D470 0%, #06D4B9 100%);color: #FFFFFF;font-size: 32rpx;font-weight: bold;padding: 24rpx;\r\n\tborder-radius: 60rpx;position: fixed;bottom:10rpx;left: 50%;transform: translateX(-50%);margin-bottom: env(safe-area-inset-bottom);text-align: center;}\r\n\t.calendar-view{width: 100%;position: relative;max-height: 60vh;padding-top: 30rpx;height: auto;overflow: hidden;padding-bottom: env(safe-area-inset-bottom);}\r\n\t/*  */\r\n\t.popup__content{ background: #fff;overflow:hidden; height: auto;  margin-bottom: 110rpx;}\r\n\t.popup__content .reserve-time-view{width: 88%;height:130rpx;margin:30rpx auto 0;border-bottom: 1px  #f0f0f0 solid;display: flex;align-items: center;\r\n\tjustify-content: space-between;}\r\n\t.popup__content .reserve-time-view .time-view{display: flex;flex-direction: column;align-items: flex-start;}\r\n\t.popup__content .reserve-time-view .time-view .time-title{color: #7B8085;line-height: 24rpx;}\r\n\t.popup__content .reserve-time-view .time-view .date-time{color: #111111;font-size: 32rpx;font-weight: bold;padding-right: 20rpx;}\r\n\t.popup__content .reserve-time-view .statistics-view{display: flex;flex-direction: column;align-items: center;justify-content: center;}\r\n\t.popup__content .reserve-time-view .statistics-view .statistics-date{width: 88rpx;height: 32rpx;border-radius: 20px;font-size: 20rpx;\r\n\tcolor: #000;border: 1rpx solid #000;box-sizing: border-box;display: flex;align-items: center;justify-content: center;position: relative;}\r\n\t.statistics-view .statistics-date .content-decorate{width: 13rpx;height: 2rpx;background: red;position: absolute;top: 50%;background: #000;}\r\n\t.statistics-view .statistics-date .left-c-d{left: -13rpx;}\r\n\t.statistics-view .statistics-date .right-c-d{right: -13rpx;}\r\n\t.popup__content .reserve-time-view .statistics-view .color-line{border-top: 1rpx solid #f0f0f0;width: 130rpx;margin-top: 25rpx;}\r\n\t.uni-popup__wrapper-box{background: #f7f8fa;border-radius: 40rpx 40rpx 0rpx 0rpx;overflow: hidden;}\r\n\t/*  */\r\n\t.popup__content .popup-but-view{width: 100%;position: sticky;bottom: 0rpx;padding: 20rpx 40rpx;background: #fff;box-shadow: 0rpx 0rpx 10rpx 5rpx #ebebeb;}\r\n\t.popup__content .popup-but-view .but-class{width: 100%;padding: 22rpx;text-align: center;color: #FFFFFF;font-size: 32rpx;font-weight: bold;border-radius: 60rpx;background: linear-gradient(90deg, #06D470 0%, #06D4B9 100%); }\r\n\t.popup__content .popup-but-view .price-statistics{padding-bottom: 15rpx;}\r\n\t.popup__content .popup-but-view .price-statistics .title-text{font-size: 24rpx;}\r\n\t.popup__content .popup-but-view .price-statistics .price-text{padding: 0rpx 10rpx;align-items: center;}\r\n\t/*  */\r\n\t.popup__content{width: 100%;height:auto;position: relative;}\r\n\t/*  */\r\n\t\r\n\t\r\n\t.popup__content .popup-close{position: fixed;right: 20rpx;top: 20rpx;width: 56rpx;height: 56rpx;z-index: 11;}\r\n\t.popup__content .popup-close image{width: 100%;height: 100%;}\r\n\t/*  */\r\n\t.popup__content .popup-banner-view{width: 100%;height: 500rpx;position: relative;}\r\n\t.popup__content .popup-banner-view .popup-numstatistics{position: absolute;right: 20rpx;bottom: 20rpx;background: rgba(0, 0, 0, 0.3);\r\n\tborder-radius: 28px;width: 64px;height: 28px;text-align: center;line-height: 28px;color: #fff;font-size: 20rpx;}\r\n\t.popup__content .hotel-details-view{width: 100%;padding: 30rpx 40rpx;background: #fff;}\r\n\t.popup__content .hotel-details-view\t.hotel-title{color: #1E1A33;font-size: 35rpx;}\r\n\t.popup__content .hotel-details-view\t.introduce-view{width: 100%;align-items: center;flex-wrap: wrap;justify-content: flex-start;padding: 20rpx 10rpx;}\r\n\t.popup__content .hotel-details-view\t.introduce-view .options-intro{padding: 15rpx 0rpx;margin-right: 20rpx;width: auto;}\r\n\t.hotel-details-view\t.introduce-view .options-intro image{width: 32rpx;height: 32rpx;}\r\n\t.hotel-details-view\t.introduce-view .options-intro .options-title{color: #1E1A33;font-size: 24rpx;margin-left: 15rpx;}\r\n\t.hotel-details-view .other-view{width: 100%;justify-content: flex-start;padding: 12rpx 0rpx;}\r\n\t.hotel-details-view .other-view .other-title{color: #A5A3AD;font-size: 24rpx;margin-right: 40rpx;}\r\n\t.hotel-details-view .other-view .other-text{color: #1E1A33;font-size: 24rpx;}\r\n\t/*  */\r\n\t.popup__content .hotel-equity-view{width: 100%;padding:30rpx 40rpx 40rpx;background: #fff;margin-top: 20rpx;}\r\n\t.hotel-equity-view .equity-title-view{align-items: center;justify-content: flex-start;}\r\n\t.hotel-equity-view .equity-title-view .equity-title{color: #1E1A33;font-size: 32rpx;font-weight: bold;}\r\n\t.hotel-equity-view .equity-title-view .equity-title-tisp{color: #A5A3AD;font-size: 24rpx;margin-left: 28rpx;}\r\n\t.hotel-equity-view .equity-options{margin-top: 40rpx;}\r\n\t.hotel-equity-view .equity-options .options-title-view{align-items: center;justify-content: flex-start;}\r\n\t.hotel-equity-view .equity-options .options-title-view image{width: 28rpx;height: 28rpx;margin-right: 20rpx;}\r\n\t.hotel-equity-view .equity-options .options-title-view  .title-text{color: #1E1A33;font-size: 28rpx;font-weight: bold;}\r\n\t.hotel-equity-view .equity-options .options-text{color: rgba(30, 26, 51, 0.8);font-size: 24rpx;padding: 15rpx 0rpx;line-height: 40rpx;margin-left: 50rpx;margin-right: 50rpx;}\r\n\t/*  */\r\n\t.hotel-equity-view .promotion-options{width: 100%;justify-content: space-between;padding: 12rpx 0rpx;}\r\n\t.hotel-equity-view .promotion-options image{width: 20rpx;height: 20rpx;}\r\n\t.hotel-equity-view .promotion-options .left-view{justify-content: flex-start;}\r\n\t.hotel-equity-view .promotion-options .left-view .logo-view{width: 80px;height: 20px;text-align: center;line-height: 18px;border-radius: 8rpx;border:1px solid;font-size: 20rpx;}\r\n\t.hotel-equity-view .promotion-options .left-view .logo-view-text{color: rgba(30, 26, 51, 0.8);font-size: 20rpx;padding-left: 30rpx;}\r\n\t/*  */\r\n\t.hotel-equity-view  .cost-details{background: #F4F5F9;width: 100%;border-radius:6px;padding: 40rpx;}\r\n\t.hotel-equity-view  .cost-details .price-view{padding-bottom: 30rpx;}\r\n\t.hotel-equity-view  .cost-details .price-view .price-text{color: rgba(30, 26, 51, 0.8);font-size: 24rpx;}\r\n\t.hotel-equity-view  .cost-details .price-view .price-num{color: #1E1A33;font-size: 28rpx;font-weight: bold;}\r\n\t/*  */\r\n\t.position-view{width: 100%;height: auto;position: relative;top:-125rpx;}\r\n\t.position-view .banner-tab-view{display: flex;align-items: center;justify-content: flex-start;height: 60rpx;border-radius: 20px;background: rgba(0, 0, 0, 0.3);padding: 0rpx 8rpx;width: max-content;margin-left: 30rpx;}\r\n\t.position-view .banner-tab-view .tab-options-banner{font-size: 20rpx;color: #FFFFFF;height: 46rpx;line-height: 46rpx;text-align: center;border-radius: 30rpx;padding: 0rpx 23rpx;margin-right: 2rpx;}\r\n\t.position-view .banner-tab-view\t.tab-options-banner-active{background: linear-gradient(90deg, #FFFFFF 0%, rgba(255, 255, 255, 0.8) 100%);color: #000000;}\r\n\t.content-view{border-radius: 40rpx 40rpx 0rpx 0rpx;background: #fff;padding: 0rpx 40rpx;width: 100%;height: auto;margin-top: 30rpx;}\r\n\t.content-view .title-view{color: #1E1A33;font-size: 40rpx;padding: 40rpx 0 0rpx 0 ;font-weight: bold;}\r\n\t.content-view .hotspot-view{width: 100%;display: flex;align-items: center;justify-content: space-between;}\r\n\t.content-view .hotspot-view .hotspot-view-left{display: flex;align-items: center;justify-content: flex-start;width: 100%; flex-wrap: wrap;}\r\n\t.content-view .hotspot-view .hotspot-view-left .hotspot-options{display: inline-block;background: #F6F7F8;padding: 5px 10px;border-radius: 6px;color: #4A4950; margin-top: 20rpx;\r\n\tfont-size: 20rpx;margin-right: 13rpx;}\r\n\t.content-view .hotspot-view .hotspot-view-left .hotspot-options-active{background: #FFF4D5;color: #EF8E32;}\r\n\t.content-view .hotspot-view .hotspot-more{color: #4A4950;font-size: 18rpx;display: flex;align-items: center;}\r\n\t.content-view .hotspot-view .hotspot-more image{width: 7px;height: 7px;margin-left: 10rpx;}\r\n\t.content-view .address-view{display: flex;align-items: center;justify-content: space-between;margin: 60rpx 0rpx;}\r\n\t.content-view .address-view .address-text{color: #1E1A33;font-size: 34rpx;font-weight: bold;}\r\n\t.content-view .address-traffic{color: rgba(30, 26, 51, 0.4);font-size: 26rpx;margin-top: 20rpx;display: flex;align-items: center;}\r\n\t.content-view .address-traffic image{width: 24rpx;height: 24rpx;margin-right: 10rpx;}\r\n\t.content-view .address-view .fangshi-view{display: flex;align-items: center;justify-content: space-between;}\r\n\t.content-view .address-view .fangshi-view .fagnshi-options{display: flex;flex-direction: column;align-items: center;justify-content: center;color: #06D470;font-size: 18rpx;}\r\n\t.content-view .address-view .fangshi-view .fagnshi-options image{width: 65rpx;height: 65rpx;margin-bottom: 10rpx;}\r\n\t.content-view .time-view{width: 100%;padding: 30rpx 0rpx;}\r\n\t.content-view .time-view .time-options{}\r\n\t.content-view .time-view .time-options .month-tetx{color: #1E1A33;font-size: 32rpx;font-weight: 500;}\r\n\t.content-view .time-view .time-options .day-tetx{color: rgba(30, 26, 51, 0.4);font-size: 26rpx;margin-left: 20rpx;}\r\n\t.content-view .time-view .content-text{box-sizing: border-box;border: 0.5px solid #000;height: 40rpx;line-height: 40rpx;text-align: center;border-radius: 20px; padding:0 10rpx;\r\n\tcolor: #000;font-size: 26rpx;}\r\n\t/*  */\r\n\t.screen-view{width: 100%;display: flex;align-items: center;justify-content: space-between;padding:20rpx; overflow-x: auto;}\r\n\t.screen-view .screen-view-left{flex:1;display: flex;align-items: center;justify-content: flex-start;margin-right: 30rpx;}\r\n\t.screen-view .screen-view-left .screen-options{display: flex;align-items: center;justify-content: space-between;background: #F4F4F4;border-radius: 6px;color: #212121;  font-size: 24rpx;padding: 12rpx 18rpx;margin-right: 20rpx;background: #fff; white-space: nowrap;}\r\n\t.screen-view .screen-view-left .screen-options image{width: 16rpx;height: 16rpx;margin-left: 16rpx;}\r\n\t.screen-view .right-screen{display: flex;align-items: center;color: #212121;font-size: 24rpx;background: #fff;padding: 12rpx 18rpx;border-radius: 6px;}\r\n\t.screen-view .right-screen image{width: 24rpx;height: 24rpx;margin-left: 10rpx;}\r\n\t\r\n\t\r\n\t\r\n\t.dp-hotel .time-view{width: 100%;padding: 30rpx; background: #fff;}\r\n\t.dp-hotel .time-view .time-options{}\r\n\t.dp-hotel .time-view .time-options .month-tetx{color: #1E1A33;font-size: 32rpx;font-weight: 500;}\r\n\t.dp-hotel .time-view .time-options .day-tetx{color: rgba(30, 26, 51, 0.4);font-size: 26rpx;margin-left: 20rpx;}\r\n\t.dp-hotel .time-view .content-text{box-sizing: border-box;border: 1px solid #000;text-align: center;border-radius: 20px; padding:0 10rpx;\r\n\tcolor: #000;font-size: 24rpx;position: relative}\r\n\t.dp-hotel .time-view .content-text .content-decorate{width: 13rpx;height: 2rpx;background: red;position: absolute;top: 50%;}\r\n\t.dp-hotel .time-view .content-text .left-c-d{left: -13rpx;background: #000;}\r\n\t.dp-hotel .time-view .content-text .right-c-d{right: -13rpx;background: #000;}\r\n\t\r\n\t\r\n\t/*  */\r\n\t.hotels-list{width: 96%;margin: 20rpx auto 0rpx;display: flex;align-items: center;justify-content: space-between;flex-direction:column;}\r\n\t.hotels-list .hotels-options{width: 100%;padding: 20rpx;display: flex;align-items: center;justify-content: space-between;border-radius: 8px;background: #FFFFFF;margin-bottom: 20rpx;}\r\n\t.hotels-list .hotels-options .hotel-img{width: 98px;height: 130px;border-radius: 15rpx;overflow: hidden;}\r\n\t.hotels-list .hotels-options .hotel-img image,.hotels-list .hotels-options .fangxing image{width: 100%;height: 100%;}\r\n\t.hotels-list .hotels-options .hotel-info{flex: 1;padding-left: 20rpx; position: relative;}\r\n\t.hotels-list .hotels-options .hotel-info .hotel-title{width: 100%;color: #343536;font-size: 30rpx;}\r\n\t.hotels-list .hotels-options .hotel-info .hotel-address{width: 100%;color: #7B8085;font-size: 24rpx;margin-top: 7rpx;}\r\n\t.hotels-list .hotels-options .hotel-info .hotel-characteristic{width: 100%;display: flex; flex-wrap: wrap; align-items: center;justify-content: flex-start;margin-top: 7rpx;}\r\n\t.hotels-list .hotels-options .hotel-info .hotel-characteristic .characteristic-options{font-size: 20rpx;padding: 7rpx 13rpx;flex-wrap: wrap;margin-right: 20rpx; margin-top: 6rpx;}\r\n\t.hotels-list .hotels-options .hotel-info .hotel-but-view{width: 100%;display: flex;align-items: center;justify-content: space-between;margin-top: 15rpx;}\r\n\t.hotels-list .hotels-options .hotel-info .hotel-but-view .make-info{display: flex;flex-direction: column;justify-content: flex-start;}\r\n\t.hotels-options .hotel-info .hotel-but-view .make-info .hotel-price{display: flex;align-items: center;justify-content: flex-start;font-size: 24rpx;}\r\n\t.hotel-info .hotel-but-view .make-info .hotel-price .hotel-price-num{font-size: 40rpx;font-weight: bold;padding: 0rpx 3rpx;}\r\n\t.hotels-options .hotel-info .hotel-but-view .make-info .hotel-text{color: #7B8085;font-size: 24rpx;margin-top: 15rpx;}\r\n\t.hotels-list .hotels-options .hotel-info .hotel-but-view .hotel-make{background: linear-gradient(90deg, #06D470 0%, #06D4B9 100%);width: 72px;height: 32px;line-height: 32px;\r\n\ttext-align: center;border-radius: 36px;color: #FFFFFF;font-size: 28rpx;font-weight: bold;}\r\n\t\r\n\t/*  */\r\n\t.dp-banner{width: 100%;height: 250px;}\r\n\t.dp-banner-swiper{width:100%;height:100%;}\r\n\t.dp-banner-swiper-img{width:100%;height:auto}\r\n\t.banner-poster{width: 82%;margin: 30rpx auto 0rpx;display: flex;flex-direction:column;align-items: flex-end;}\r\n\t.banner-poster .poster-title{color: #FFFFFF;font-size: 56rpx;font-weight: 900;padding: 30rpx 0rpx;}\r\n\t.banner-poster .poster-text{color: #FFFFFF;font-size: 26rpx;opacity: 0.6;padding: 10rpx 0rpx;}\r\n\t.banner-poster .poster-but{width: 108px;height: 36px;color: #FFFFFF;text-align: center;line-height: 36px;font-size: 28rpx;font-weight: bold;margin: 40rpx 0rpx;border-radius: 36px;}\r\n\t/*  */\r\n\t.navigation {width: 100%;padding-bottom:10px;overflow: hidden;position: fixed;top: 0;z-index: 2;}\r\n\t.navcontent {display: flex;align-items: center;padding-left: 10px;}\r\n\t.header-location-top{position: relative;display: flex;justify-content: center;align-items: center;flex:1;}\r\n\t.header-back-but{position: absolute;left:0;display: flex;align-items: center;width: 40rpx;height: 45rpx;overflow: hidden;}\r\n\t.header-back-but image{width: 40rpx;height: 45rpx;} \r\n\t.header-page-title{font-size: 36rpx;}\r\n\t\r\n\t\r\n\t/*查看详情里的按钮*/\r\n\t.detail_but-class{width: 100%;padding: 22rpx;text-align: center;color: #FFFFFF;font-size: 32rpx;font-weight: bold;border-radius: 60rpx;background: linear-gradient(90deg, #06D470 0%, #06D4B9 100%);   }\r\n\t.tips{ margin-top: 20rpx; color: #999;}\r\n\t\r\n\t/*方形样式按钮*/\r\n\t.hotels-list .hotels-options .fangxing{width: 98px;height: 98px;border-radius: 15rpx;overflow: hidden; \r\n\tdisplay: flex;align-items: center;justify-content: center;}\r\n\t.hotel-make-new{width: 90rpx;height: 95rpx;display: flex;align-items: center;justify-content: space-between;flex-direction: column;border: 1px red solid;border-radius: 10rpx;overflow: hidden; position: absolute;\r\n\tright: 20rpx;top: 50rpx;}\r\n\t.hotel-make-new .make-new-view{width: 100%;padding:7rpx 0rpx;text-align: center;background: red;color: #fff;font-size: 30rpx;font-weight: bold;}\r\n\t.hotel-make-new .make-new-view2{width: 100%;text-align: center;font-size: 20rpx;border-bottom: 1px red solid;color: red;padding-bottom: 5rpx;}\r\n\t\r\n\t.hotel-nature{display: flex;align-items: center;justify-content: flex-start;margin-top: 10rpx;}\r\n\t.hotel-nature .star-view{display: flex;align-items: center;justify-content: flex-start;}\r\n\t.hotel-nature .star-view image{width: 13px; height: 13px;margin-right: 10rpx;}\r\n\t.hotel-nature .hotspot-nature-text{font-size: 24rpx;color: #4A4950;margin-left: 10rpx;}\r\n\t  .hotel-characteristic .under_title{color: #7B8085;font-size: 24rpx;margin-top: 7rpx; margin-left: 7rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-hotel-room.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dp-hotel-room.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754212979925\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}