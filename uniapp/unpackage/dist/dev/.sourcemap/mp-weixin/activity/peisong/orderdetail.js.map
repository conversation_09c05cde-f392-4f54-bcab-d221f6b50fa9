{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/peisong/orderdetail.vue?ed9e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/peisong/orderdetail.vue?7dbc", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/peisong/orderdetail.vue?ae58", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/peisong/orderdetail.vue?e600", "uni-app:///activity/peisong/orderdetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/peisong/orderdetail.vue?f4a4", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/peisong/orderdetail.vue?d01b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "orderinfo", "prolist", "psuser", "binfo", "psorder", "shop_tel", "interval1", "onLoad", "onUnload", "clearInterval", "onPullDownRefresh", "methods", "getdata", "that", "app", "id", "updatemylocation", "longitude", "latitude", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "setst", "st", "openLocation", "uni", "name", "scale", "da<PERSON><PERSON>", "itemList", "success", "console", "address", "call", "phoneNumber"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1FA;AAAA;AAAA;AAAA;AAAy0B,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+K71B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAC;UAAA;QACA;QACAD;QACAA;QACAA;QACAA;QACAA;QACA;UACAA;QACA;QACAA;QACAJ;QACAI;UACAA;QACA;MACA;IACA;IACAG;MACA;MACAF;QACA;QACA;QACAA;UAAAG;UAAAC;QAAA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACAL;QACAA;QACAA;UAAAC;QAAA;UACAD;UACAA;UACAM;YACAP;UACA;QACA;MACA;IACA;IACAQ;MACA;MACA;MACA;MACA;QACA;MACA;MAAA;QACA;MACA;MAAA;QACA;MACA;MACAP;QACAA;QACAA;UAAAC;UAAAO;QAAA;UACAR;UACAA;UACAM;YACAP;UACA;QACA;MACA;IACA;IACAU;MACA;MACA;MACA;MACAC;QACAN;QACAD;QACAQ;QACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;MAEAH;QACAI;QACAC;UACA;YACA;cACA;cACA;cACA;cACA;YACA;cACA;cACA;cACA;cACA;YACA;YACAC;YACAA;YACAA;YACAN;cACAN;cACAD;cACAQ;cACAM;cACAL;YACA;UACA;QACA;MACA;IACA;IACAM;MACA;MACAR;QACAS;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChVA;AAAA;AAAA;AAAA;AAAsrC,CAAgB,smCAAG,EAAC,C;;;;;;;;;;;ACA1sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/peisong/orderdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/peisong/orderdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderdetail.vue?vue&type=template&id=6366b972&\"\nvar renderjs\nimport script from \"./orderdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./orderdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/peisong/orderdetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=template&id=6366b972&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload && _vm.psorder.status != 0\n      ? _vm.dateFormat(_vm.psorder.starttime)\n      : null\n  var m1 =\n    _vm.isload && _vm.psorder.status != 0 && _vm.psorder.daodiantime\n      ? _vm.dateFormat(_vm.psorder.daodiantime)\n      : null\n  var m2 =\n    _vm.isload && _vm.psorder.status != 0 && _vm.psorder.quhuotime\n      ? _vm.dateFormat(_vm.psorder.quhuotime)\n      : null\n  var m3 =\n    _vm.isload && _vm.psorder.status != 0 && _vm.psorder.endtime\n      ? _vm.dateFormat(_vm.psorder.endtime)\n      : null\n  var m4 = _vm.isload ? _vm.dateFormat(_vm.orderinfo.createtime) : null\n  var m5 = _vm.isload ? _vm.dateFormat(_vm.orderinfo.paytime) : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.orderinfo.formdata, function (item, __i0__) {\n        var $orig = _vm.__get_orig(item)\n        var g0 = _vm.orderinfo.formdata.length\n        return {\n          $orig: $orig,\n          g0: g0,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\r\n\t\t<map v-if=\"psorder.status!=4\" class=\"map\" :longitude=\"binfo.longitude\" :latitude=\"binfo.latitude\" scale=\"14\" :markers=\"[{\r\n\t\t\tid:0,\r\n\t\t\tlatitude:binfo.latitude,\r\n\t\t\tlongitude:binfo.longitude,\r\n\t\t\ticonPath: `${pre_url}/static/img/peisong/marker_business.png`,\r\n\t\t\twidth:'44',\r\n\t\t\theight:'54'\r\n\t\t},{\r\n\t\t\tid:1,\r\n\t\t\tlatitude:orderinfo.latitude,\r\n\t\t\tlongitude:orderinfo.longitude,\r\n\t\t\ticonPath: `${pre_url}/static/img/peisong/marker_kehu.png`,\r\n\t\t\twidth:'44',\r\n\t\t\theight:'54'\r\n\t\t},{\r\n\t\t\tid:2,\r\n\t\t\tlatitude:psuser.latitude,\r\n\t\t\tlongitude:psuser.longitude,\r\n\t\t\ticonPath: `${pre_url}/static/img/peisong/marker_qishou.png`,\r\n\t\t\twidth:'44',\r\n\t\t\theight:'54'\r\n\t\t}]\"></map>\r\n\t\t<map v-else class=\"map\" :longitude=\"binfo.longitude\" :latitude=\"binfo.latitude\" scale=\"14\" :markers=\"[{\r\n\t\t\tid:0,\r\n\t\t\tlatitude:binfo.latitude,\r\n\t\t\tlongitude:binfo.longitude,\r\n\t\t\ticonPath: `${pre_url}/static/img/peisong/marker_business.png`,\r\n\t\t\twidth:'44',\r\n\t\t\theight:'54'\r\n\t\t},{\r\n\t\t\tid:1,\r\n\t\t\tlatitude:orderinfo.latitude,\r\n\t\t\tlongitude:orderinfo.longitude,\r\n\t\t\ticonPath: `${pre_url}/static/img/peisong/marker_kehu.png`,\r\n\t\t\twidth:'44',\r\n\t\t\theight:'54'\r\n\t\t}]\"></map>\r\n\r\n\t\t<view class=\"order-box\">\r\n\t\t\t<view class=\"head\">\r\n\t\t\t\t<view class=\"f1\" v-if=\"psorder.status==4\"><image :src=\"pre_url+'/static/img/peisong/ps_time.png'\" class=\"img\"/>已送达</view>\r\n\t\t\t\t<view class=\"f1\" v-else-if=\"psorder.leftminute>0\"><image :src=\"pre_url+'/static/img/peisong/ps_time.png'\" class=\"img\"/><text class=\"t1\">{{psorder.leftminute}}分钟内</text> 送达</view>\r\n\t\t\t\t<view class=\"f1\" v-else><image :src=\"pre_url+'/static/img/peisong/ps_time.png'\" class=\"img\"/>已超时<text class=\"t1\" style=\"margin-left:10rpx\">{{-psorder.leftminute}}分钟</text></view>\r\n\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t<text class=\"t1\">{{psorder.ticheng}}</text>元\r\n\t\t\t\t\t\t<text v-if=\"psorder.tip_fee && psorder.tip_fee>0\">\r\n\t\t\t\t\t\t\t\t+{{psorder.tip_fee}}元小费\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content\" style=\"border-bottom:0\">\r\n\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t<view class=\"t1\"><text class=\"x1\">{{psorder.juli}}</text><text class=\"x2\">{{psorder.juli_unit}}</text></view><!-- 配送员距商家距离 -->\r\n\t\t\t\t\t<view class=\"t2\"><image :src=\"pre_url+'/static/img/peisong/ps_juli.png'\" class=\"img\"/></view>\r\n\t\t\t\t\t<view class=\"t3\"><text class=\"x1\">{{psorder.juli2}}</text><text class=\"x2\">{{psorder.juli2_unit}}</text></view><!-- 配送员距会员距离 -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t<view class=\"t1\">{{binfo.name}}</view>\r\n\t\t\t\t\t<view class=\"t2\">{{binfo.address}}</view>\r\n\t\t\t\t\t<view class=\"t3\">{{orderinfo.address}}</view>\r\n\t\t\t\t\t\t<view class=\"t2\">{{orderinfo.area}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f3\" @tap.stop=\"daohang\"><image :src=\"pre_url+'/static/img/peisong/ps_daohang.png'\" class=\"img\"/></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"orderinfo\">\r\n\t\t\t<view class=\"box-title\">商品清单({{orderinfo.procount}})</view>\r\n\t\t\t<view v-for=\"(item, idx) in prolist\" :key=\"idx\" class=\"item\">\r\n\t\t\t<block v-if=\"psorder.type!='paotui_order'\">\r\n\t\t\t\t\t<text class=\"t1 flex1\">{{item.name}} {{item.ggname}}</text>\r\n\t\t\t\t\t<text class=\"t2 flex0\">￥{{item.sell_price}} ×{{item.num}} </text>\r\n\t\t\t</block>\r\n\t\t\t<block v-else>\r\n\t\t\t\t\t<text class=\"t1 flex1\">{{item.name}} </text>\r\n\t\t\t\t\t<text class=\"t2 flex0\">x{{item.num}} </text>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"orderinfo\" v-if=\"psorder.status!=0\">\r\n\t\t\t<view class=\"box-title\">配送信息</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">接单时间</text>\r\n\t\t\t\t<text class=\"t2\">{{dateFormat(psorder.starttime)}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"psorder.daodiantime\">\r\n\t\t\t\t<text class=\"t1\">到店时间</text>\r\n\t\t\t\t<text class=\"t2\">{{dateFormat(psorder.daodiantime)}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"psorder.quhuotime\">\r\n\t\t\t\t<text class=\"t1\">取货时间</text>\r\n\t\t\t\t<text class=\"t2\">{{dateFormat(psorder.quhuotime)}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"psorder.endtime\">\r\n\t\t\t\t<text class=\"t1\">送达时间</text>\r\n\t\t\t\t<text class=\"t2\">{{dateFormat(psorder.endtime)}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"orderinfo\">\r\n\t\t\t<view class=\"box-title\">订单信息</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">订单编号</text>\r\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{orderinfo.ordernum}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">下单时间</text>\r\n\t\t\t\t<text class=\"t2\">{{dateFormat(orderinfo.createtime)}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">支付时间</text>\r\n\t\t\t\t<text class=\"t2\">{{dateFormat(orderinfo.paytime)}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">支付方式</text>\r\n\t\t\t\t<text class=\"t2\">{{orderinfo.paytype}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"orderinfo.expect_take_time\">\r\n\t\t\t\t<text class=\"t1\">取件时间</text>\r\n\t\t\t\t<text class=\"t2\">{{orderinfo.expect_take_time}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"orderinfo.pic\">\r\n\t\t\t\t<text class=\"t1\">物品图片</text>\r\n\t\t\t\t<view class=\"t2\" ><image :src=\"orderinfo.pic\" style=\"width:400rpx;height:auto\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"orderinfo.pic\"/></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"psorder.type!='paotui_order'\">\r\n\t\t\t\t<text class=\"t1\">商品金额</text>\r\n\t\t\t\t<text class=\"t2 red\">¥{{orderinfo.product_price}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">实付款</text>\r\n\t\t\t\t<text class=\"t2 red\">¥{{orderinfo.totalprice}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">备注</text>\r\n\t\t\t\t<text class=\"t2 red\">{{orderinfo.message ? orderinfo.message : '无'}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-for=\"item in orderinfo.formdata\" :key=\"index\" v-if=\"(orderinfo.formdata).length > 0\">\r\n\t\t\t\t<text class=\"t1\">{{item[0]}}</text>\r\n\t\t\t\t<view class=\"t2\" v-if=\"item[2]=='upload'\"><image :src=\"item[1]\" style=\"width:400rpx;height:auto\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"item[1]\"/></view>\r\n\t\t\t\t<text class=\"t2\" v-else user-select=\"true\" selectable=\"true\">{{item[1]}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view style=\"width:100%;height:180rpx\"></view>\r\n\t\t<view class=\"bottom notabbarbot\" v-if=\"psorder.status!=4\">\r\n\t\t\t\r\n\t\t\t<block v-if=\"psorder.type!='paotui_order'\">\r\n\t\t\t\t\t<view class=\"f1\" v-if=\"psorder.status!=0\" @tap=\"call\" :data-tel=\"orderinfo.tel\"><image :src=\"pre_url+'/static/img/peisong/tel1.png'\" class=\"img\"/>联系顾客</view>\r\n\t\t\t\t\t<view class=\"f2\" v-if=\"psorder.status!=0\" @tap=\"call\" :data-tel=\"binfo.tel\"><image :src=\"pre_url+'/static/img/peisong/tel2.png'\" class=\"img\"/>联系商家</view>\r\n\t\t\t</block>\r\n\t\t\t<block v-else>\r\n\t\t\t\t\t<view class=\"f1\" v-if=\"psorder.status!=0\" @tap=\"call\" :data-tel=\"orderinfo.take_tel\"><image :src=\"pre_url+'/static/img/peisong/tel1.png'\" class=\"img\"/>取货顾客</view>\r\n\t\t\t\t\t<view class=\"f2\" v-if=\"psorder.status!=0\" @tap=\"call\" :data-tel=\"orderinfo.send_tel\"><image :src=\"pre_url+'/static/img/peisong/tel2.png'\" class=\"img\"/>收货顾客</view>\r\n\t\t\t\t\t<view class=\"f2\" v-if=\"psorder.status!=0\" @tap=\"call\" :data-tel=\"shop_tel\"><image :src=\"pre_url+'/static/img/peisong/tel2.png'\" class=\"img\"/>联系商家</view>\r\n\t\t\t</block>\r\n\t\t\t<view class=\"btn1\" @tap=\"qiangdan\" :data-id=\"psorder.id\" v-if=\"psorder.status==0\">立即抢单</view>\r\n\t\t\t<view class=\"btn1\" @tap=\"setst\" :data-id=\"psorder.id\" data-st=\"2\" v-if=\"psorder.status==1\">我已到店</view>\r\n\t\t\t<view class=\"btn1\" @tap=\"setst\" :data-id=\"psorder.id\" data-st=\"3\" v-if=\"psorder.status==2\">我已取货</view>\r\n\t\t\t<view class=\"btn1\" @tap=\"setst\" :data-id=\"psorder.id\" data-st=\"4\" v-if=\"psorder.status==3\">我已送达</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t<wxxieyi></wxxieyi>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\r\n      orderinfo: {},\r\n      prolist: [],\r\n\t\t\tpsuser:{},\r\n      binfo: {},\r\n      psorder: {},\r\n      shop_tel:'',\r\n\t\t\tinterval1:null,\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n\t\tthis.updatemylocation(false);\r\n  },\r\n\tonUnload:function(){\r\n\t\tclearInterval(this.interval1);\r\n\t},\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiPeisong/orderdetail', {id: that.opt.id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif(res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg);return;\r\n\t\t\t\t}\r\n\t\t\t\tthat.orderinfo = res.orderinfo;\r\n\t\t\t\tthat.prolist = res.prolist;\r\n\t\t\t\tthat.binfo = res.binfo;\r\n\t\t\t\tthat.psorder = res.psorder;\r\n\t\t\t\tthat.psuser = res.psuser;\r\n\t\t\t\tif(res.shop_tel){\r\n\t\t\t\t\t\tthat.shop_tel = res.shop_tel;\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded();\r\n\t\t\t\tclearInterval(that.interval1);\r\n\t\t\t\tthat.interval1 = setTimeout(function(){\r\n\t\t\t\t\tthat.updatemylocation(true);\r\n\t\t\t\t},30000)\r\n\t\t\t});\r\n\t\t},\r\n\t\tupdatemylocation:function(needload){\r\n\t\t\tvar that = this;\r\n\t\t\tapp.getLocation(function(res){\r\n\t\t\t\tvar longitude = res.longitude;\r\n\t\t\t\tvar latitude = res.latitude;\r\n\t\t\t\tapp.post('ApiPeisong/updatemylocation',{longitude:longitude,latitude:latitude},function(){\r\n\t\t\t\t\tif(needload) that.getdata();\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n    qiangdan: function (e) {\r\n      var that = this;\r\n      var id = e.currentTarget.dataset.id;\r\n      app.confirm('确定要接单吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n        app.post('ApiPeisong/qiangdan', {id: id}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            that.getdata();\r\n          }, 1000);\r\n        });\r\n      });\r\n    },\r\n    setst: function (e) {\r\n      var that = this;\r\n      var id = e.currentTarget.dataset.id;\r\n      var st = e.currentTarget.dataset.st;\r\n\t\t\tif(st == 2){\r\n\t\t\t\tvar tips = '确定改为已到店吗?';\r\n\t\t\t}if(st == 3){\r\n\t\t\t\tvar tips = '确定改为已取货吗?';\r\n\t\t\t}if(st == 4){\r\n\t\t\t\tvar tips = '确定改为已送达吗?';\r\n\t\t\t}\r\n      app.confirm(tips, function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n        app.post('ApiPeisong/setst', {id: id,st:st}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            that.getdata();\r\n          }, 1000);\r\n        });\r\n      });\r\n    },\r\n\t\topenLocation:function(e){\r\n\t\t\tvar latitude = parseFloat(e.currentTarget.dataset.latitude)\r\n\t\t\tvar longitude = parseFloat(e.currentTarget.dataset.longitude)\r\n\t\t\tvar address = e.currentTarget.dataset.address\r\n\t\t\tuni.openLocation({\r\n\t\t\t latitude:latitude,\r\n\t\t\t longitude:longitude,\r\n\t\t\t name:address,\r\n\t\t\t scale: 13\r\n\t\t })\r\n\t\t},\r\n\t\tdaohang:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar datainfo = that.psorder;\r\n\t\t\tvar binfo = that.binfo\r\n\t\t\tvar orderinfo = that.orderinfo;\r\n            if(datainfo.type == 'paotui_order'){\r\n                var address1 = '导航到取货地址';\r\n                var address2 = '导航到收货地址';\r\n            }else{\r\n                var address1 = '导航到商家';\r\n                var address2 = '导航到用户';\r\n            }\r\n            \r\n\t\t\tuni.showActionSheet({\r\n            itemList: [address1, address2],\r\n            success: function (res) {\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tif (res.tapIndex == 0) {\r\n\t\t\t\t\t\t\tvar longitude = datainfo.longitude\r\n\t\t\t\t\t\t\tvar latitude = datainfo.latitude\r\n\t\t\t\t\t\t\tvar name = binfo.name\r\n\t\t\t\t\t\t\tvar address = binfo.address\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tvar longitude = datainfo.longitude2\r\n\t\t\t\t\t\t\tvar latitude = datainfo.latitude2\r\n\t\t\t\t\t\t\tvar name = orderinfo.address\r\n\t\t\t\t\t\t\tvar address = orderinfo.address\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tconsole.log(longitude);\r\n\t\t\t\t\t\tconsole.log(latitude);\r\n\t\t\t\t\t\tconsole.log(address);\r\n\t\t\t\t\t\tuni.openLocation({\r\n\t\t\t\t\t\t latitude:parseFloat(latitude),\r\n\t\t\t\t\t\t longitude:parseFloat(longitude),\r\n\t\t\t\t\t\t name:name,\r\n\t\t\t\t\t\t address:address,\r\n\t\t\t\t\t\t scale: 13\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tcall:function(e){\r\n\t\t\tvar tel = e.currentTarget.dataset.tel;\r\n\t\t\tuni.makePhoneCall({\r\n\t\t\t\tphoneNumber: tel\r\n\t\t\t});\r\n\t\t}\r\n  }\r\n}\r\n</script>\r\n<style>\r\n\r\n.map{width:100%;height:500rpx;overflow:hidden}\r\n.ordertop{width:100%;height:220rpx;padding:50rpx 0 0 70rpx}\r\n.ordertop .f1{color:#fff}\r\n.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}\r\n.ordertop .f1 .t2{font-size:24rpx}\r\n\r\n.order-box{ width: 94%;margin:20rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}\r\n.order-box .head{ display:flex;width:100%; border-bottom: 1px #f5f5f5 solid; height:88rpx; line-height:88rpx; overflow: hidden; color: #999;}\r\n.order-box .head .f1{display:flex;align-items:center;color:#222222}\r\n.order-box .head .f1 .img{width:24rpx;height:24rpx;margin-right:4px}\r\n.order-box .head .f1 .t1{color:#06A051;margin-right:10rpx}\r\n.order-box .head .f2{color:#FF6F30}\r\n.order-box .head .f2 .t1{font-size:36rpx;margin-right:4rpx}\r\n\r\n.order-box .content{display:flex;justify-content:space-between;width: 100%; padding:16rpx 0px;border-bottom: 1px solid #f5f5f5;position:relative}\r\n.order-box .content .f1{width:100rpx;display:flex;flex-direction:column;align-items:center}\r\n.order-box .content .f1 .t1{display:flex;flex-direction:column;align-items:center}\r\n.order-box .content .f1 .t1 .x1{color:#FF6F30;font-size:28rpx;font-weight:bold}\r\n.order-box .content .f1 .t1 .x2{color:#999999;font-size:24rpx;margin-bottom:8rpx}\r\n.order-box .content .f1 .t2 .img{width:12rpx;height:36rpx; margin: 10rpx 0;}\r\n\r\n.order-box .content .f1 .t3{display:flex;flex-direction:column;align-items:center}\r\n.order-box .content .f1 .t3 .x1{color:#FF6F30;font-size:28rpx;font-weight:bold}\r\n.order-box .content .f1 .t2 .img{width:12rpx;height:36rpx; margin: 10rpx 0;}\r\n.order-box .content .f2{padding:0 10rpx;}\r\n.order-box .content .f2 .t1{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-bottom:6rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.order-box .content .f2 .t2{font-size:28rpx;color:#222222;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:3;overflow:hidden;}\r\n.order-box .content .f2 .t3{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-top:30rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.order-box .content .f3 .img{width:72rpx;height:168rpx}\r\n\r\n.orderinfo{width: 94%;margin:20rpx 3%;margin-top:10rpx;padding: 14rpx 3%;background: #FFF;border-radius:8px}\r\n.orderinfo .box-title{color:#161616;font-size:30rpx;height:80rpx;line-height:80rpx;font-weight:bold}\r\n.orderinfo .item{display:flex;width:100%;padding:10rpx 0;}\r\n.orderinfo .item .t1{width:200rpx;color:#161616}\r\n.orderinfo .item .t2{flex:1;text-align:right;color:#222222}\r\n.orderinfo .item .red{color:red}\r\n\r\n.bottom{ width: 100%;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}\r\n.bottom .f1{width:188rpx;display:flex;align-items:center;flex-direction:column;font-size:20rpx;color:#373C55;border-right:1px solid #EAEEED}\r\n.bottom .f1 .img{width:44rpx;height:44rpx}\r\n.bottom .f2{width:188rpx;display:flex;align-items:center;flex-direction:column;font-size:20rpx;color:#373C55}\r\n.bottom .f2 .img{width:44rpx;height:44rpx}\r\n.bottom .btn1{flex:1;background:linear-gradient(-90deg, #06A051 0%, #03B269 100%);height:100rpx;line-height:100rpx;color:#fff;text-align:center;font-size:32rpx}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754212967714\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}