{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/toupiao/baoming.vue?7e6c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/toupiao/baoming.vue?9ecd", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/toupiao/baoming.vue?c87f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/toupiao/baoming.vue?eaf7", "uni-app:///activity/toupiao/baoming.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/toupiao/baoming.vue?766e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/toupiao/baoming.vue?2770"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "pic", "pics", "info", "<PERSON><PERSON><PERSON><PERSON>", "headimg", "latitude", "longitude", "videos", "toupiao_type", "editorFormdata", "formvaldata", "items", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "id", "uni", "url", "method", "header", "success", "setTimeout", "formSubmit", "formdata", "value", "uploadpic", "removepic", "uploadimg", "removeimg", "uploadvideo", "sourceType", "maxDuration", "filePath", "name", "fail", "console", "removeVideo", "video", "setfield", "editorBind<PERSON>icker<PERSON><PERSON><PERSON>", "onchange"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yRAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1FA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8Jz1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAA;UAEAA;UACA;YACAC;cACAD;cACAA;YACA;UACA;UAEA;UACA;YACAf;UACA;YACAA;UACA;UACAe;UAEAA;UAEA;YACAA;YACA;cACA;cACA;gBACAT;cACA;gBACAA;cACA;cACAS;YACA;UACA;;UAEA;UACA;YACAA;YACAA;YAEAG;cACAC;cACA1B;cACA2B;cACAC;gBAAA;cAAA;cACAC;gBACAP;cACA;YACA;UACA;UACAA;QACA;UACAC;UACAO;YACAP;UACA;QACA;MACA;IACA;IACAQ;MACA;MACA;MACAC;MACA;QACAT;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACAS;MACAA;MACA;MACA;QACA;QACA;QACA;UACA;UACA;YACAT;YAAA;UACA;UACA;YACA;cACAU;YACA;UACA;UACAD;QACA;MACA;MACA;MACAT;MACAA;QACAA;QACA;UACAA;UACAO;YACAP;UACA;QACA;UACAA;QACA;MACA;IACA;IACAW;MACA;MACA;MACA;MACAX;QACA;QACA;UACA;UACA;YACA;YACA;cACAhB;YACA;YACA;cACAA;YACA;YACAe;YACA;YACAA;UACA;YACAA;YAEA;YACAA;UACA;QAEA;UACAA;QACA;MACA;IACA;IACAa;MACA;MACAb;IACA;IACAc;MACA;MACA;MACA;MACA;MACAb;QACA;UACAhB;QACA;QACA;MACA;IACA;IACA8B;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;UACA;UACA9B;UACAe;UACAA;QACA;UACA;UACA;UACAf;UACAe;UACAA;UACA;QACA;MACA;MACA;QACA;QACAf;QACAe;MACA;IACA;IACAgB;MACA;MACAb;QACAc;QACAC;QACAX;UACA;UACAN;UACAE;YACAC;YACAe;YACAC;YACAb;cACAN;cACA;cACA;gBACAD;cACA;gBACAC;cACA;YACA;YACAoB;cACApB;cACAA;YACA;UACA;QACA;QACAoB;UACAC;QACA;MACA;IACA;;IACAC;MACA;MACA;MACA;MACAC;MACAxB;IACA;IACAyB;MACA;MACA;MACA;IACA;IACAC;MACAJ;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;IACA;IACAK;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvbA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/toupiao/baoming.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/toupiao/baoming.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./baoming.vue?vue&type=template&id=81a230b6&\"\nvar renderjs\nimport script from \"./baoming.vue?vue&type=script&lang=js&\"\nexport * from \"./baoming.vue?vue&type=script&lang=js&\"\nimport style0 from \"./baoming.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/toupiao/baoming.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./baoming.vue?vue&type=template&id=81a230b6&\"", "var components\ntry {\n  components = {\n    uniDataPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-data-picker/uni-data-picker\" */ \"@/components/uni-data-picker/uni-data-picker.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = _vm.isload\n    ? _vm.__map(_vm.toupiao.formdata, function (item, idx) {\n        var $orig = _vm.__get_orig(item)\n        var l0 =\n          item.key == \"checkbox\"\n            ? _vm.__map(item.val2, function (item1, idx1) {\n                var $orig = _vm.__get_orig(item1)\n                var m0 =\n                  _vm.editorFormdata[idx] &&\n                  _vm.inArray(item1, _vm.editorFormdata[idx])\n                return {\n                  $orig: $orig,\n                  m0: m0,\n                }\n              })\n            : null\n        return {\n          $orig: $orig,\n          l0: l0,\n        }\n      })\n    : null\n  var g0 = _vm.isload && _vm.toupiao_type == 0 ? _vm.pics.join(\",\") : null\n  var g1 =\n    _vm.isload && !(_vm.toupiao_type == 0) && _vm.toupiao_type == 1\n      ? _vm.videos.join(\",\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./baoming.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./baoming.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\" style=\"padding:20rpx 0\" :style=\"{background:toupiao.color1}\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view style=\"color:red;padding:30rpx 30rpx;margin-top:20rpx;background:#fff;margin:0 28rpx;border-radius:8rpx\" v-if=\"info.id && info.status==2\">审核不通过：{{info.reason}}，请修改后再提交</view>\r\n\t\t<view style=\"color:red;padding:30rpx 30rpx;margin-top:20rpx;background:#fff;margin:0 28rpx;border-radius:8rpx\" v-if=\"info.id && info.status==1\">您已成功参与报名</view>\r\n\t\t<view style=\"color:green;padding:30rpx 30rpx;margin-top:20rpx;background:#fff;margin:0 28rpx;border-radius:8rpx\" v-if=\"info.id && info.status==0\">您已提交申请，请等待审核</view>\r\n\t\t<form @submit=\"formSubmit\" @reset=\"formReset\">\r\n\r\n\t\t\r\n\t\t<view class=\"apply_box\">\r\n\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t<view>名称<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"name\" :value=\"info.name\" placeholder=\"请输入选手名称\"/></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"apply_item\">\r\n\t\t\t\t<view>联系方式<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t<view class=\"flex-y-center\"><input type=\"text\" name=\"weixin\" :value=\"info.weixin\" placeholder=\"请输入联系方式\"></input></view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 报名自定义字段 -->\r\n\t\t\t<view :class=\"{'apply_box': item.key === 'textarea' || item.key === 'upload_pics','apply_item': item.key !== 'textarea' && item.key !== 'upload_pics','aic':item.key === 'upload' || item.key === 'region'}\"  v-for=\"(item, idx) in toupiao.formdata\"  :key=\"item.id\">\r\n\t\t\t\t<block v-if=\"item.key=='input'\">\r\n\t\t\t\t\t<view>{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<input type=\"text\" :name=\"'form'+idx\" class=\"input\" :value=\"editorFormdata[idx]\" :placeholder=\"item.val2\" @input=\"setfield\" :data-formidx=\"'form'+idx\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"item.key=='textarea'\">\r\n\t\t\t\t\t<view class=\"apply_item flex-col\">\r\n\t\t\t\t\t\t<view><text class=\"title\">{{item.val1}}</text><text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t<view class=\"flex-y-center\"><textarea type=\"textarea\" :name=\"'form'+idx\" @input=\"setfield\" :data-formidx=\"'form'+idx\" :placeholder=\"item.val2\" placeholder-style=\"font-size:24rpx\" style=\"height:100rpx;background:#F8F8F8;padding:10rpx 20rpx\" maxlength=\"-1\" :value=\"editorFormdata[idx]\"></textarea></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"item.key=='radio'\">\r\n\t\t\t\t\t<view>{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t\t<radio-group class=\"radio-group\" :name=\"'form'+idx\" @change=\"setfield\" :data-formidx=\"'form'+idx\">\r\n\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\">\r\n\t\t\t\t\t\t\t\t<radio class=\"radio\" :value=\"item1\" :checked=\"editorFormdata[idx] && editorFormdata[idx]==item1 ? true : false\"/>{{item1}}\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"item.key=='checkbox'\">\r\n\t\t\t\t\t<view>{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<checkbox-group :name=\"'form'+idx\" class=\"checkbox-group\" @change=\"setfield\" :data-formidx=\"'form'+idx\">\r\n\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\">\r\n\t\t\t\t\t\t\t<checkbox class=\"checkbox\" :value=\"item1\" :checked=\"editorFormdata[idx] && inArray(item1,editorFormdata[idx]) ? true : false\"/>{{item1}}\r\n\t\t\t\t\t\t</label>\r\n\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"item.key=='selector'\">\r\n\t\t\t\t\t<view>{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" :name=\"'form'+idx\" :value=\"editorFormdata[idx] ? editorFormdata[idx] : '' \" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\">\r\n\t\t\t\t\t\t<view v-if=\"editorFormdata[idx] || editorFormdata[idx]===0\">{{item.val2[editorFormdata[idx]]}}</view>\r\n\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"item.key=='time'\">\r\n\t\t\t\t\t<view>{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<picker class=\"picker\" mode=\"time\" :name=\"'form'+idx\" :value=\"editorFormdata[idx] ? editorFormdata[idx] : '' \" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\">\r\n\t\t\t\t\t\t<view v-if=\"editorFormdata[idx]\">{{editorFormdata[idx]}}</view>\r\n\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"item.key=='date'\">\r\n\t\t\t\t\t<view>{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<picker class=\"picker\" mode=\"date\" :name=\"'form'+idx\" :value=\"editorFormdata[idx] ? editorFormdata[idx] : ''\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\">\r\n\t\t\t\t\t\t<view v-if=\"editorFormdata[idx]\">{{editorFormdata[idx]}}</view>\r\n\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"item.key=='region'\">\r\n\t\t\t\t\t<view>{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<uni-data-picker style=\"width: 80%;\" :localdata=\"items\" popup-title=\"请选择省市区\" :placeholder=\"editorFormdata[idx] || '请选择省市区'\" @change=\"onchange($event,idx)\"></uni-data-picker>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"item.key=='upload'\">\r\n\t\t\t\t\t<view>{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form'+idx\" :value=\"editorFormdata[idx]\"/>\r\n\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding:20rpx 0;\">\r\n\t\t\t\t\t\t<view v-if=\"editorFormdata[idx]\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\"><image :src=\"pre_url+'/static/img/ico-del.png'\" style=\"display: block;\"></image></view>\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"editorFormdata[idx]\" @click=\"previewImage\" :data-url=\"editorFormdata[idx]\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-else class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @click=\"uploadpic\" :data-formidx=\"'form'+idx\" :data-idx=\"idx\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\t\r\n\t\t\t\t<block v-if=\"item.key=='upload_pics'\">\r\n\t\t\t\t\t<view class=\"apply_item\" style=\"border-bottom:none;\">\r\n\t\t\t\t\t\t<view><text>{{item.val1}}</text><text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n\t\t\t\t\t\t<view v-for=\"(item2, index2) in editorFormdata[idx]\" :key=\"index2\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index2\" data-type=\"pics\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\"><image :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item2\" @tap=\"previewImage\" :data-url=\"item2\" mode=\"widthFix\" :data-idx=\"idx\"></image></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @click=\"uploadpic\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\" data-type=\"pics\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t\t<!-- 报名自定义字段 end -->\r\n\t\t\t\r\n\t\t</view>\r\n\t\t<view class=\"apply_box\">\r\n\t\t\t<view class=\"apply_item\" style=\"border-bottom:0\"><view>首图<text style=\"color:red\"> *</text></view></view>\r\n\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n\t\t\t\t<view v-if=\"pic\" class=\"layui-imgbox\">\r\n\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removepic\"><image :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"pic\" @tap=\"previewImage\" :data-url=\"pic\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadpic\" v-if=\"!pic\"></view>\r\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"pic\" :value=\"pic\" maxlength=\"-1\"></input>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"apply_box\" v-if=\"toupiao_type == 0\">\r\n\t\t\t<view class=\"apply_item\" style=\"border-bottom:0\"><text>详情图片</text></view>\r\n\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n\t\t\t\t<view v-for=\"(item, index) in pics\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pics\"><image :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pics\"></view>\r\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"pics\" :value=\"pics.join(',')\" maxlength=\"-1\"></input>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"apply_box\" v-else-if=\"toupiao_type == 1\">\r\n\t\t\t<view class=\"apply_item\" style=\"border-bottom:0\"><text>详情视频</text></view>\r\n\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-bottom:20rpx;\">\r\n\t\t\t\t<view v-for=\"(val, key) in videos\" :key=\"key\" class=\"layui-videobox\">\r\n\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeVideo\" :data-index=\"key\" data-field=\"videos\"><image :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t<view class=\"layui-videobox-video\">\r\n\t\t\t\t\t\t<video :src=\"val\" style=\"width: 100%;\"></video>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uploadvideobtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-position:center;background-color:#F3F3F3;'\" @tap=\"uploadvideo\" data-field=\"videos\"></view>\r\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"videos\" :value=\"videos.join(',')\" maxlength=\"-1\"></input>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"apply_box\" style=\"padding-bottom:20rpx\">\r\n\t\t\t<view class=\"apply_item flex-col\">\r\n\t\t\t\t<view><text class=\"title\">详情文字</text></view>\r\n\t\t\t\t<view class=\"flex-y-center\"><textarea type=\"text\" name=\"detail_txt\" :value=\"info.detail_txt\" placeholder=\"请输入详情文字~\" placeholder-style=\"font-size:24rpx\" style=\"height:100rpx;background:#F8F8F8;padding:10rpx 20rpx\" maxlength=\"-1\"></textarea></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<button class=\"set-btn\" form-type=\"submit\" :style=\"{color:toupiao.color2}\" v-if=\"!info.id || info.status==2\">提 交</button>\r\n\t\t</form>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t<wxxieyi></wxxieyi>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\r\n\t\t\tpic:'',\r\n\t\t\tpics:[],\r\n\t\t\tinfo:{},\r\n\t\t\ttoupiao:{},\r\n      headimg:[],\r\n\t\t\tlatitude:'',\r\n\t\t\tlongitude:'',\r\n\t\t\tvideos:[],\r\n\t\t\ttoupiao_type:0,\r\n\t\t\teditorFormdata:[],\r\n\t\t\tformvaldata:[],\r\n\t\t\titems:[]\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiToupiao/baoming', {id:that.opt.id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif(res.status==1){\r\n\t\t\t\t\tthat.info = res.info;\r\n\t\t\t\t\t\r\n\t\t\t\t\tthat.toupiao = res.toupiao;\r\n\t\t\t\t\tif (that.toupiao.fanwei == 1) {\r\n\t\t\t\t\t\tapp.getLocation(function(res2) {\r\n\t\t\t\t\t\t\tthat.latitude = res2.latitude;\r\n\t\t\t\t\t\t\tthat.longitude = res2.longitude;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tvar pics = res.info ? res.info.pics : '';\r\n\t\t\t\t\tif (pics) {\r\n\t\t\t\t\t\tpics = pics.split(',');\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tpics = [];\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.pics = pics;\r\n\t\t\t\t\t\r\n\t\t\t\t\tthat.pic = res.info.pic ? res.info.pic : '';\r\n\t\t\t\t\t\r\n\t\t\t\t\tif(res.toupiao && res.toupiao.toupiao_type){\r\n\t\t\t\t\t\tthat.toupiao_type = res.toupiao.toupiao_type;\r\n\t\t\t\t\t\tif(that.toupiao_type == 1){\r\n\t\t\t\t\t\t\tvar videos = res.info ? res.info.videos : '';\r\n\t\t\t\t\t\t\tif (videos) {\r\n\t\t\t\t\t\t\t\tvideos = videos.split(',');\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tvideos = [];\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthat.videos = videos;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t/* 报名自定义字段 */\r\n\t\t\t\t\tif(res.toupiao && res.toupiao.editorFormdata){\r\n\t\t\t\t\t\tthat.editorFormdata = res.toupiao.editorFormdata;\r\n\t\t\t\t\t\tthat.formvaldata = res.toupiao.formvaldata;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.request({\r\n\t\t\t\t\t\t\turl: app.globalData.pre_url+'/static/area.json',\r\n\t\t\t\t\t\t\tdata: {},\r\n\t\t\t\t\t\t\tmethod: 'GET',\r\n\t\t\t\t\t\t\theader: { 'content-type': 'application/json' },\r\n\t\t\t\t\t\t\tsuccess: function(res2) {\r\n\t\t\t\t\t\t\t\tthat.items = res2.data\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.alert(res.msg)\r\n\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\tapp.goback();\r\n\t\t\t\t\t},1000)\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n    formSubmit: function (e) {\r\n\t\t\tvar that = this;\r\n      var formdata = e.detail.value;\r\n\t\t\tformdata.id = that.opt.id;\r\n      if (formdata.name == '') {\r\n        app.alert('请输入名称');return;\r\n      }\r\n      if (formdata.weixin == '') {\r\n        app.alert('请输入联系方式');return;\r\n      }\r\n\t\t\tif (formdata.pic == '') {\r\n\t\t\t  app.alert('请上传首图');return;\r\n\t\t\t}\r\n\t\t\tformdata[\"latitude\"] = that.latitude\r\n\t\t\tformdata[\"longitude\"] = that.longitude\r\n\t\t\t/* 报名自定义字段验证 */\r\n\t\t\tif(that.toupiao.formdata){\r\n\t\t\t\tvar formdataSet = that.toupiao.formdata;\r\n\t\t\t\tvar formvaldata = that.formvaldata;\r\n\t\t\t\tfor (var i = 0; i < formdataSet.length;i++){\r\n\t\t\t\t\tvar value = formvaldata['form' + i];\r\n\t\t\t\t\tif (formdataSet[i].val3 == 1 && (formvaldata['form' + i] === '' || formvaldata['form' + i] === undefined || formvaldata['form' + i].length==0)){\r\n\t\t\t\t\t\t\tapp.alert(formdataSet[i].val1+' 必填');return;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (formdataSet[i].key == 'selector') {\r\n\t\t\t\t\t\tif(formdataSet[i].val2[formvaldata['form' + i]]){\r\n\t\t\t\t\t\t\tvalue = formdataSet[i].val2[formvaldata['form' + i]]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tformdata['form' + i] = value;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t/* 报名自定义字段验证 end */\r\n\t\t\tapp.showLoading('提交中');\r\n      app.post(\"ApiToupiao/baoming\",formdata, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n        if (data.status == 1) {\r\n          app.success(data.msg);\r\n          setTimeout(function () {\r\n            app.goto('index?id='+that.opt.id);\r\n          }, 1000);\r\n        } else {\r\n          app.error(data.msg);\r\n        }\r\n      });\r\n    },\r\n\t\tuploadpic:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\tvar type = e.currentTarget.dataset.type;\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\t/* 报名自定义字段 */\r\n\t\t\t\tif(idx >= 0){\r\n\t\t\t\t\tif(!that.editorFormdata) that.editorFormdata = [];\r\n\t\t\t\t\tif(type == 'pics'){\r\n\t\t\t\t\t\tvar pics = that.editorFormdata[idx];\r\n\t\t\t\t\t\tif(!pics){\r\n\t\t\t\t\t\t  pics = [];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tfor(var i=0;i<urls.length;i++){\r\n\t\t\t\t\t\t\tpics.push(urls[i]);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.$set(that.editorFormdata, idx, pics);\r\n\t\t\t\t\t\tvar field = e.currentTarget.dataset.formidx;\r\n\t\t\t\t\t\tthat.formvaldata[field] = pics;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.$set(that.editorFormdata, idx, urls[0]);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tvar field = e.currentTarget.dataset.formidx;\r\n\t\t\t\t\t\tthat.formvaldata[field] = urls[0];\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.pic = urls[0];\r\n\t\t\t\t}\r\n\t\t\t},1)\r\n\t\t},\r\n\t\tremovepic:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tthat.pic = '';\r\n\t\t},\r\n\t\tuploadimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tvar pics = that[field]\r\n\t\t\tif(!pics) pics = [];\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tfor(var i=0;i<urls.length;i++){\r\n\t\t\t\t\tpics.push(urls[i]);\r\n\t\t\t\t}\r\n\t\t\t\tif(field == 'pics') that.pics = pics;\r\n\t\t\t},1)\r\n\t\t},\r\n\t\tremoveimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t/* 报名自定义字段 */\r\n\t\t\tif(idx >= 0){\r\n\t\t\t\tvar type  = e.currentTarget.dataset.type;\r\n\t\t\t\tif(!that.editorFormdata) that.ditorFormdata = [];\r\n\t\t\t\tif(type == 'pics'){\r\n\t\t\t\t\tvar pics = that.editorFormdata[idx];\r\n\t\t\t\t\tpics.splice(index,1);\r\n\t\t\t\t\tthat.editorFormdata[idx] = pics;\r\n\t\t\t\t\tthat.formvaldata[formidx] = pics;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tvar pics = that.editorFormdata\r\n\t\t\t\t\tvar formidx = e.currentTarget.dataset.formidx;\r\n\t\t\t\t\tpics.splice(idx,1);\r\n\t\t\t\t\tthat.editorFormdata = pics;\r\n\t\t\t\t\tthat.formvaldata[formidx] = '';\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(field == 'pics'){\r\n\t\t\t\tvar pics = that.pics\r\n\t\t\t\tpics.splice(index,1);\r\n\t\t\t\tthat.pics = pics;\r\n\t\t\t}\r\n\t\t},\r\n\t\tuploadvideo:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.chooseVideo({\r\n\t\t\t  sourceType: ['album', 'camera'],\r\n\t\t\t  maxDuration: 60,\r\n\t\t\t  success: function (res) {\r\n\t\t\t    var tempFilePath = res.tempFilePath;\r\n\t\t\t    app.showLoading('上传中');\r\n\t\t\t    uni.uploadFile({\r\n\t\t\t      url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id,\r\n\t\t\t      filePath: tempFilePath,\r\n\t\t\t      name: 'file',\r\n\t\t\t      success: function (res) {\r\n\t\t\t        app.showLoading(false);\r\n\t\t\t        var data = JSON.parse(res.data);\r\n\t\t\t        if (data.status == 1) {\r\n\t\t\t\t\t\t\t\tthat.videos.push(data.url);\r\n\t\t\t        } else {\r\n\t\t\t          app.alert(data.msg);\r\n\t\t\t        }\r\n\t\t\t      },\r\n\t\t\t      fail: function (res) {\r\n\t\t\t        app.showLoading(false);\r\n\t\t\t        app.alert(res.errMsg);\r\n\t\t\t      }\r\n\t\t\t    });\r\n\t\t\t  },\r\n\t\t\t  fail: function (res) {\r\n\t\t\t    console.log(res); //alert(res.errMsg);\r\n\t\t\t  }\r\n\t\t\t});\r\n\t\t},\r\n\t\tremoveVideo:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\tvar video = that.videos\r\n\t\t\tvideo.splice(index,1);\r\n\t\t\tthat.videos = video;\r\n\t\t},\r\n\t\tsetfield:function(e){\r\n\t\t\tvar field = e.currentTarget.dataset.formidx;\r\n\t\t\tvar value = e.detail.value;\r\n\t\t\tthis.formvaldata[field] = value;\r\n\t\t},\r\n\t\teditorBindPickerChange:function(e){\r\n\t\t\tconsole.log('editorBindPickerChange');\r\n\t\t\tvar that = this;\r\n\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\tvar val = e.detail.value;\r\n\t\t\tif(!this.editorFormdata) this.editorFormdata = [];\r\n\t\t\tthis.$set(this.editorFormdata, idx, val);\r\n\t\t\t\r\n\t\t\tvar field = e.currentTarget.dataset.formidx;\r\n\t\t\tthis.formvaldata[field] = val;\r\n\t\t},\r\n\t\tonchange(e,idx) {\r\n\t\t  const value = e.detail.value;\r\n\t\t\tvar regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text;\r\n\t\t\tthis.$set(this.editorFormdata, idx, regiondata);\r\n\t\t\tthis.formvaldata['form'+idx] = regiondata;\r\n\t\t},\r\n  }\r\n};\r\n</script>\r\n<style>\r\nradio{transform: scale(0.6);}\r\ncheckbox{transform: scale(0.6);}\r\n.apply_box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}\r\n.apply_title { background: #fff}\r\n.apply_title .qr_goback{ width:18rpx;height:32rpx; margin-left:24rpx;     margin-top: 34rpx;}\r\n.apply_title .qr_title{ font-size: 36rpx; color: #242424;   font-weight:bold;margin: 0 auto; line-height: 100rpx;}\r\n\r\n.apply_item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }\r\n.apply_box .apply_box{margin: 0;padding: 0;}\r\n.apply_box .apply_item:last-child{ border:none}\r\n.apply_box .picker{flex:1;text-align: right;}\r\n.apply_item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}\r\n.apply_item input::placeholder{ color:#999999}\r\n.apply_item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}\r\n.apply_item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }\r\n.apply_item .upload_pic image{ width: 32rpx;height: 32rpx; }\r\n.set-btn{width: 90%;margin:0 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-size:30rpx;font-weight:bold;background:#fff}\r\n\r\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.layui-imgbox-img>image{max-width:100%;}\r\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.layui-videobox{position: relative;width: 100%;}\r\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n.uploadvideobtn{height:200rpx;width:100%}\r\n.aic{align-items: center;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./baoming.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./baoming.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754212967683\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}