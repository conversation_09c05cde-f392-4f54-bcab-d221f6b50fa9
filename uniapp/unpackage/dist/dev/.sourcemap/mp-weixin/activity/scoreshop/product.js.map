{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/product.vue?770d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/product.vue?0041", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/product.vue?4dec", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/product.vue?c1e0", "uni-app:///activity/scoreshop/product.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/product.vue?3ba2", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/product.vue?204e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "isfavorite", "current", "sysset", "business", "product", "cartnum", "pagecontent", "shopset", "title", "sharepic", "sharetypevisible", "showposter", "posterpic", "scrolltopshow", "kfurl", "buydialogShow", "btntype", "gui<PERSON>", "gui<PERSON><PERSON>", "ggselected", "nowguige", "gwcnum", "onLoad", "onPullDownRefresh", "onShareAppMessage", "pic", "onShareTimeline", "imageUrl", "query", "methods", "getdata", "that", "app", "id", "uni", "swiper<PERSON><PERSON>e", "buydialogChange", "ggchange", "gwcplus", "gwcminus", "gwcinput", "addcart", "proid", "ggid", "num", "tobuy", "addfavorite", "type", "onPageScroll", "shareClick", "handleClickMask", "showPoster", "posterDialogClose", "showcuxiaodetail", "hidecuxiaodetail", "getcoupon", "itemList", "success", "scene", "sharedata", "sharelink"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5JA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqLz1B;AAAA,eACA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,qDACA,+CACA;EAEA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MAAAhB;MAAAiB;IAAA;EACA;EACAC;IACA;MAAAlB;MAAAiB;IAAA;IACA;IACA;MACAjB;MACAmB;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAC;UACA;QACA;QACA;QACA;QACAD;QACAA;QAEAA;QACAA;QACA;QACA;UACAZ;QACA;QACAY;QACAA;QACAA;QAEAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAG;UACA1B;QACA;QACAuB;QACA;UACAA;QACA;QACAA;UAAAvB;UAAAiB;QAAA;MACA;IACA;IACAU;MACA;MACAJ;IACA;IACAK;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAlB;MACA;MACA;MACA;MACA;IACA;IACA;IACAmB;MACA;MACA;MACA;QACAN;QACA;MACA;MACA;IACA;IACA;IACAO;MACA;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAnB;MACA;MACA;IACA;IACAoB;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;QACAT;QACA;MACA;MACAD;MACAC;QAAAU;QAAAC;QAAAC;MAAA;QACAb;QACA;UACAC;UACAD;UACAA;QACA;UACAC;QACA;MACA;IACA;IACAa;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAb;IACA;IACA;IACAc;MACA;MACA;MACAd;QAAAU;QAAAK;MAAA;QACA;UACAhB;QACA;QACAC;MACA;IACA;IACAgB;MACA;MACA;MACA;QACAjB;MACA;MACA;QACAA;MACA;IACA;IACAkB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACApB;MACAA;MACAC;MAEAA;QAAAU;MAAA;QACAV;QACA;UACAA;QACA;UACAD;QACA;MACA;IACA;IACAqB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;EAAA,2DACA;IACA;IACA;IACA;MACAxB;IACA;IACA;MACAA;IACA;EACA,uDACA;IACAC;IACA;EACA,wDACA;IACA;IACAD;IACAG;MACAsB;MACAC;QACA;UACA;UACA;YACAC;UACA;UACA;UACAC;UACAA;UACAA;UACAA;UACA;UACAA;UACAA;UACA;UACA;YACA;cACA;gBACAA;gBACAA;gBACAA;gBACA;kBACA;kBACA;oBACAC;kBACA;kBACA;oBACAA;kBACA;kBACAD;gBACA;cACA;YACA;UACA;UACAzB;QACA;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC7dA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/scoreshop/product.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/scoreshop/product.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./product.vue?vue&type=template&id=af052914&\"\nvar renderjs\nimport script from \"./product.vue?vue&type=script&lang=js&\"\nexport * from \"./product.vue?vue&type=script&lang=js&\"\nimport style0 from \"./product.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/scoreshop/product.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=template&id=af052914&\"", "var components\ntry {\n  components = {\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    scrolltop: function () {\n      return import(\n        /* webpackChunkName: \"components/scrolltop/scrolltop\" */ \"@/components/scrolltop/scrolltop.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.product.pics.length : null\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"积分\") : null\n  var l0 =\n    _vm.isload && _vm.product.show_lvprice == 1\n      ? _vm.__map(_vm.product.new_lvprice_data, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m2 = _vm.t(\"积分\")\n          return {\n            $orig: $orig,\n            m2: m2,\n          }\n        })\n      : null\n  var m3 =\n    _vm.isload &&\n    _vm.shopset.showcommission == 1 &&\n    (_vm.product.commission > 0 || _vm.product.commission_score > 0)\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m4 =\n    _vm.isload &&\n    _vm.shopset.showcommission == 1 &&\n    (_vm.product.commission > 0 || _vm.product.commission_score > 0)\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.isload &&\n    _vm.shopset.showcommission == 1 &&\n    (_vm.product.commission > 0 || _vm.product.commission_score > 0)\n      ? _vm.t(\"佣金\")\n      : null\n  var m6 = _vm.isload && _vm.shopset.showjd == 1 ? _vm.t(\"color1\") : null\n  var m7 = _vm.isload && _vm.shopset.showjd == 1 ? _vm.t(\"color1rgb\") : null\n  var m8 = _vm.isload && _vm.product.status == 1 ? _vm.t(\"color2\") : null\n  var m9 = _vm.isload && _vm.product.status == 1 ? _vm.t(\"color1\") : null\n  var m10 = _vm.isload && _vm.sharetypevisible ? _vm.getplatform() : null\n  var m11 =\n    _vm.isload && _vm.sharetypevisible && !(m10 == \"app\")\n      ? _vm.getplatform()\n      : null\n  var m12 =\n    _vm.isload && _vm.sharetypevisible && !(m10 == \"app\") && !(m11 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  var m13 = _vm.isload && _vm.buydialogShow ? _vm.t(\"color1\") : null\n  var m14 = _vm.isload && _vm.buydialogShow ? _vm.t(\"积分\") : null\n  var m15 =\n    _vm.isload &&\n    _vm.buydialogShow &&\n    !(_vm.nowguige.stock <= 0) &&\n    _vm.btntype == 0 &&\n    _vm.canaddcart\n      ? _vm.t(\"color2\")\n      : null\n  var m16 =\n    _vm.isload &&\n    _vm.buydialogShow &&\n    !(_vm.nowguige.stock <= 0) &&\n    _vm.btntype == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m17 =\n    _vm.isload &&\n    _vm.buydialogShow &&\n    !(_vm.nowguige.stock <= 0) &&\n    _vm.btntype == 1\n      ? _vm.t(\"color2\")\n      : null\n  var m18 =\n    _vm.isload &&\n    _vm.buydialogShow &&\n    !(_vm.nowguige.stock <= 0) &&\n    _vm.btntype == 2\n      ? _vm.t(\"color1\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        l0: l0,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"swiper-container\">\r\n\t\t\t<swiper class=\"swiper\" :indicator-dots=\"false\" :autoplay=\"true\" :interval=\"5000\" @change=\"swiperChange\">\r\n\t\t\t\t<block v-for=\"(item, index) in product.pics\" :key=\"index\">\r\n\t\t\t\t\t<swiper-item class=\"swiper-item\">\r\n\t\t\t\t\t\t<view class=\"swiper-item-view\"><image class=\"img\" :src=\"item\" mode=\"widthFix\"/></view>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</block>\r\n\t\t\t</swiper>\r\n\t\t\t<view class=\"imageCount\">{{current+1}}/{{(product.pics).length}}</view>\r\n\t\t</view>\r\n\t\t<view class=\"header\">\r\n\t\t\t<view class=\"price_share\">\r\n\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\">{{product.score_price}}{{t('积分')}}<text v-if=\"product.money_price*1>0\">+{{product.money_price}}元</text></view>\r\n\t\t\t\t\t<view class=\"f2\">￥{{product.sell_price}} </view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"price2\" v-if=\"product.show_lvprice==1\">\r\n\t\t\t\t\t<view v-for=\"(item,index) in product.new_lvprice_data\" :class=\"item.is_select==1?'f1':'f2'\">\r\n\t\t\t\t\t\t{{item.name}}价:{{item.score}}{{t('积分')}}<text v-if=\"item.money*1>0\">+{{item.money}}元</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"share\" @tap=\"shareClick\"><image class=\"img\" :src=\"pre_url+'/static/img/share.png'\"/><text class=\"txt\">分享</text></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"title\">{{product.name}}</view>\r\n\t\t\t<view class=\"sales_stock\">\r\n\t\t\t\t<view class=\"f1\">已兑换{{product.sales}}件</view>\r\n\t\t\t\t<view class=\"f2\">库存：{{product.stock}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"commission\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" v-if=\"shopset.showcommission==1 && (product.commission > 0 || product.commission_score > 0)\">\r\n\t\t\t分享好友购买预计可得{{t('佣金')}}：\r\n\t\t\t<block v-if=\"product.commission > 0\"><text style=\"font-weight:bold;padding:0 2px\">{{product.commission}}</text>{{product.commission_desc}}</block>\r\n\t\t\t<text v-if=\"product.commission > 0 && product.commission_score > 0\">+</text>\r\n\t\t\t<block v-if=\"product.commission_score > 0\"><text style=\"font-weight:bold;padding:0 2px\">{{product.commission_score}}</text>{{product.commission_score_desc}}</block>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"shop\" v-if=\"shopset.showjd==1\">\r\n\t\t\t<image :src=\"business.logo\" class=\"p1\"/>\r\n\t\t\t<view class=\"p2 flex1\">\r\n\t\t\t\t<view class=\"t1\">{{business.name}}</view>\r\n\t\t\t\t<view class=\"t2\">{{business.desc}}</view>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"p4\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"goto\" :data-url=\"product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid\" data-opentype=\"reLaunch\">进入店铺</button>\r\n\t\t</view>\r\n\t\t<view class=\"detail_title\"><view class=\"t1\"></view><view class=\"t2\"></view><view class=\"t0\">商品描述</view><view class=\"t2\"></view><view class=\"t1\"></view></view>\r\n\t\t<view class=\"detail\">\r\n\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\r\n\t\t</view>\r\n\r\n\t\t<view style=\"width:100%;height:140rpx;\"></view>\r\n\t\t<view class=\"bottombar flex-row\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\" v-if=\"product.status==1\">\r\n\t\t\t<view class=\"f1\">\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"kfurl\" v-if=\"kfurl!='contact::'\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/kefu.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"item\" v-else open-type=\"contact\" show-message-card=\"true\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/kefu.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t\t</button>\r\n\t\t\t\t<view class=\"item flex1\" @tap=\"goto\" data-url=\"cart\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/gwc.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">购物车</view>\r\n\t\t\t\t\t<view class=\"cartnum\" v-if=\"cartnum>0\">{{cartnum}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"addfavorite\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/shoucang.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">{{isfavorite?'已收藏':'收藏'}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"op\">\r\n\t\t\t\t<view class=\"tocart flex-x-center flex-y-center\" @tap=\"buydialogChange\" data-btntype=\"1\" :style=\"{background:t('color2')}\">加入购物车</view>\r\n\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" @tap=\"buydialogChange\" data-btntype=\"2\" :style=\"{background:t('color1')}\">立即兑换</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<scrolltop :isshow=\"scrolltopshow\"></scrolltop>\r\n\r\n\t\t<view v-if=\"sharetypevisible\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t<view class=\"popup__modal\" style=\"height:320rpx;min-height:320rpx\">\r\n\t\t\t\t<!-- <view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请选择分享方式</text>\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\"/>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<view class=\"sharetypecontent\">\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- <view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<button class=\"f1\" open-type=\"share\" v-else-if=\"getplatform() != 'h5'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"showPoster\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharepic.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">生成分享图片</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"posterDialog\" v-if=\"showposter\">\r\n\t\t\t<view class=\"main\">\r\n\t\t\t\t<view class=\"close\" @tap=\"posterDialogClose\"><image class=\"img\" :src=\"pre_url+'/static/img/close.png'\"/></view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"posterpic\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"posterpic\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\r\n\r\n\t\t<view v-if=\"buydialogShow\">\r\n\t\t\t<view class=\"buydialog-mask\" @tap=\"buydialogChange\"></view>\r\n\t\t\t<view class=\"buydialog\" :class=\"menuindex>-1?'tabbarbot':'notabbarbot'\">\r\n\t\t\t\t<view class=\"close\" @tap=\"buydialogChange\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"image\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"title flex\">\r\n\t\t\t\t\t<image :src=\"nowguige.pic || product.pic\" class=\"img\" @tap=\"previewImage\" :data-url=\"nowguige.pic || product.pic\"/>\r\n\t\t\t\t\t<view class=\"flex1\">\r\n\t\t\t\t\t\t<view  class=\"price\" :style=\"{color:t('color1')}\">\r\n\t\t\t\t\t\t\t{{nowguige.score_price}}{{t('积分')}}<text v-if=\"nowguige.money_price*1>0\">+{{nowguige.money_price}}元</text>\r\n\t\t\t\t\t\t\t<text v-if=\"nowguige.market_price > 0\" class=\"t2\">￥{{nowguige.market_price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"stock\" v-if=\"!shopset || shopset.hide_stock!=1\">库存：{{nowguige.stock}}</view>\r\n\t\t\t\t\t\t<view class=\"choosename\" v-if=\"product.guigeset==1\">已选规格: {{nowguige.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"max-height:50vh;overflow:scroll\" v-if=\"product.guigeset==1\">\r\n\t\t\t\t\t<view v-for=\"(item, index) in guigedata\" :key=\"index\" class=\"guigelist flex-col\">\r\n\t\t\t\t\t\t<view class=\"name\">{{item.title}}</view>\r\n\t\t\t\t\t\t<view class=\"item flex flex-y-center\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item2, index2) in item.items\" :key=\"index2\">\r\n\t\t\t\t\t\t\t\t<view :data-itemk=\"item.k\" :data-idx=\"item2.k\" :class=\"'item2 ' + (ggselected[item.k]==item2.k ? 'on':'')\" @tap=\"ggchange\">{{item2.title}}</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"buynum flex flex-y-center\">\r\n\t\t\t\t\t<view class=\"flex1\">购买数量：</view>\r\n\t\t\t\t\t<view class=\"addnum\">\r\n\t\t\t\t\t\t<view class=\"minus\" @tap=\"gwcminus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-minus.png'\" /></view>\r\n\t\t\t\t\t\t<input class=\"input\" type=\"number\" :value=\"gwcnum\" @input=\"gwcinput\"></input>\r\n\t\t\t\t\t\t<view class=\"plus\" @tap=\"gwcplus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-plus.png'\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"op\">\r\n\t\t\t\t\t<block v-if=\"nowguige.stock <= 0\">\r\n\t\t\t\t\t\t<button class=\"nostock\">库存不足</button>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t<button class=\"addcart\" :style=\"{backgroundColor:t('color2')}\" @tap=\"addcart\" v-if=\"btntype==0 && canaddcart\">加入购物车</button>\r\n\t\t\t\t\t\t<button class=\"tobuy\" :style=\"{backgroundColor:t('color1')}\" @tap=\"tobuy\" v-if=\"btntype==0\">立即购买</button>\r\n\t\t\t\t\t\t<button class=\"addcart\" :style=\"{backgroundColor:t('color2')}\" @tap=\"addcart\" v-if=\"btntype==1\">确 定</button>\r\n\t\t\t\t\t\t<button class=\"tobuy\" :style=\"{backgroundColor:t('color1')}\" @tap=\"tobuy\" v-if=\"btntype==2\">确 定</button>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\tisfavorite: false,\r\n\t\t\tcurrent: 0,\r\n\t\t\tsysset: {},\r\n\t\t\tbusiness:{},\r\n\t\t\tproduct: [],\r\n\t\t\tcartnum: \"\",\r\n\t\t\tpagecontent: \"\",\r\n\t\t\tshopset: \"\",\r\n\t\t\ttitle: \"\",\r\n\t\t\tsharepic: \"\",\r\n\t\t\tsharetypevisible: false,\r\n\t\t\tshowposter: false,\r\n\t\t\tposterpic: \"\",\r\n\t\t\tscrolltopshow: false,\r\n\t\t\tkfurl:'',\r\n\t\t\t\r\n\t\t\tbuydialogShow: false,\r\n\t\t\tbtntype:2,\r\n\t\t\tguigelist:{},\r\n\t\t\tguigedata:{},\r\n\t\t\tggselected:{},\r\n\t\t\tnowguige:{},\r\n\t\t\tgwcnum:1,\r\n\t\t\tggselected:[],\r\n\t\t\tks:'',\r\n\t\t}\r\n\t},\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonShareAppMessage:function(){\r\n\t\treturn this._sharewx({title:this.product.name,pic:this.product.pic});\r\n\t},\r\n\tonShareTimeline:function(){\r\n\t\tvar sharewxdata = this._sharewx({title:this.product.name,pic:this.product.pic});\r\n\t\tvar query = (sharewxdata.path).split('?')[1]+'&seetype=circle';\r\n\t\treturn {\r\n\t\t\ttitle: sharewxdata.title,\r\n\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\tquery: query\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = this.opt.id || 0;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.post('ApiScoreshop/product', {id: id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar product = res.product;\r\n\t\t\t\tvar pagecontent = JSON.parse(product.detail);\r\n\t\t\t\tthat.sysset = res.sysset;\r\n\t\t\t\tthat.product = product;\r\n\r\n\t\t\t\tthat.guigelist = res.guigelist;\r\n\t\t\t\tthat.guigedata = res.guigedata;\r\n\t\t\t\tvar ggselected = [];\r\n\t\t\t\tfor (var i = 0; i < (res.guigedata).length; i++) {\r\n\t\t\t\t\tggselected.push(0);\r\n\t\t\t\t}\r\n\t\t\t\tthat.ks = ggselected.join(','); \r\n\t\t\t\tthat.nowguige = that.guigelist[that.ks];\r\n\t\t\t\tthat.ggselected = ggselected;\r\n\r\n\t\t\t\tthat.cartnum = res.cartnum;\r\n\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t\tthat.shopset = res.shopset;\r\n\t\t\t\tthat.business = res.business;\r\n\t\t\t\tthat.title = product.name;\r\n\t\t\t\tthat.isfavorite = res.isfavorite;\r\n\t\t\t\tthat.sharepic = product.pics[0];\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: product.name\r\n\t\t\t\t});\r\n\t\t\t\tthat.kfurl = '/pages/kefu/index';\r\n\t\t\t\tif(app.globalData.initdata.kfurl != ''){\r\n\t\t\t\t\tthat.kfurl = app.globalData.initdata.kfurl;\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded({title:res.product.name,pic:res.product.pic});\r\n\t\t\t});\r\n\t\t},\r\n\t\tswiperChange: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.current = e.detail.current\r\n\t\t},\r\n\t\tbuydialogChange: function (e) {\r\n\t\t\tif(!this.buydialogShow){\r\n\t\t\t\tthis.btntype = e.currentTarget.dataset.btntype\r\n\t\t\t}\r\n\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t},\r\n\t\tggchange: function (e){\r\n\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\tvar itemk = e.currentTarget.dataset.itemk;\r\n\t\t\tvar ggselected = this.ggselected;\r\n\t\t\tggselected[itemk] = idx;\r\n\t\t\tvar ks = ggselected.join(',');\r\n\t\t\tthis.ggselected = ggselected;\r\n\t\t\tthis.ks = ks;\r\n\t\t\tthis.nowguige = this.guigelist[this.ks];\r\n\t\t},\r\n\t\t//加\r\n\t\tgwcplus: function (e) {\r\n\t\t\tvar gwcnum = this.gwcnum + 1;\r\n\t\t\tvar ks = this.ks;\r\n\t\t\tif (gwcnum > this.guigelist[ks].stock) {\r\n\t\t\t\tapp.error('库存不足');\r\n\t\t\t\treturn 1;\r\n\t\t\t}\r\n\t\t\tthis.gwcnum = this.gwcnum + 1;\r\n\t\t},\r\n\t\t//减\r\n\t\tgwcminus: function (e) {\r\n\t\t\tvar gwcnum = this.gwcnum - 1;\r\n\t\t\tif (gwcnum <= 0) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tthis.gwcnum = this.gwcnum - 1;\r\n\t\t},\r\n\t\t//输入\r\n\t\tgwcinput: function (e) {\r\n\t\t\tvar ks = this.ks;\r\n\t\t\tvar gwcnum = parseInt(e.detail.value);\r\n\t\t\tif (gwcnum < 1) gwcnum = 1;\r\n\t\t\tif (gwcnum > this.guigelist[ks].stock) {\r\n\t\t\t\tgwcnum = this.guigelist[ks].stock > 0 ? this.guigelist[ks].stock : 1;\r\n\t\t\t}\r\n\t\t\tthis.gwcnum = gwcnum;\r\n\t\t},\r\n\t\taddcart:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar ks = that.ks;\r\n\t\t\tvar num = that.gwcnum;\r\n\t\t\tvar proid = that.product.id;\r\n\t\t\tvar ggid = that.guigelist[ks].id;\r\n\t\t\tvar stock = that.guigelist[ks].stock;\r\n\t\t\t\r\n\t\t\tif (num < 1) num = 1;\r\n\t\t\tif (stock < num) {\r\n\t\t\t\tapp.error('库存不足');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.post('ApiScoreshop/addcart', {proid:proid,ggid:ggid,num:num}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\tthat.cartnum+=num;\r\n\t\t\t\t\tthat.buydialogShow = false;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\ttobuy:function(){\r\n\t\t\tvar ks = this.ks;\r\n\t\t\tvar num = this.gwcnum;\r\n\t\t\tvar proid = this.product.id;\r\n\t\t\tvar ggid = this.guigelist[ks].id;\r\n\t\t\tvar proid = this.product.id;\r\n\t\t\tvar num = this.gwcnum;\r\n\t\t\tif (num < 1) num = 1;\r\n\t\t\tvar prodata = proid + ',' + num + ',' + (ggid == undefined ? '':ggid);\r\n\t\t\tapp.goto('buy?prodata='+prodata);\r\n\t\t},\r\n\t\t//收藏操作\r\n\t\taddfavorite: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tvar proid = that.product.id;\r\n\t\t\tapp.post('ApiScoreshop/addfavorite', {proid: proid,type: 'scoreshop'}, function (data) {\r\n\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\tthat.isfavorite = !that.isfavorite;\r\n\t\t\t\t}\r\n\t\t\t\tapp.success(data.msg);\r\n\t\t\t});\r\n\t\t},\r\n\t\tonPageScroll: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar scrollY = e.scrollTop;     \r\n\t\t\tif (scrollY > 200) {\r\n\t\t\t\tthat.scrolltopshow = true;\r\n\t\t\t}\r\n\t\t\tif(scrollY < 150) {\r\n\t\t\t\tthat.scrolltopshow = false\r\n\t\t\t}\r\n\t\t},\r\n\t\tshareClick: function () {\r\n\t\t\tthis.sharetypevisible = true;\r\n\t\t},\r\n\t\thandleClickMask: function () {\r\n\t\t\tthis.sharetypevisible = false\r\n\t\t},\r\n\t\tshowPoster: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.showposter = true;\r\n\t\t\tthat.sharetypevisible = false;\r\n\t\t\tapp.showLoading('生成海报中');\r\n\r\n\t\t\tapp.post('ApiScoreshop/getposter', {proid: that.product.id}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\tapp.alert(data.msg);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.posterpic = data.poster;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tposterDialogClose: function () {\r\n\t\t\tthis.showposter = false;\r\n\t\t},\r\n\t\tshowcuxiaodetail: function () {\r\n\t\t\tthis.showcuxiaodialog = true;\r\n\t\t},\r\n\t\thidecuxiaodetail: function () {\r\n\t\t\tthis.showcuxiaodialog = false\r\n\t\t},\r\n\t\tgetcoupon:function(){\r\n\t\t\tthis.showcuxiaodialog = false;\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPageScroll: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar scrollY = e.scrollTop;     \r\n\t\t\tif (scrollY > 200) {\r\n\t\t\t\tthat.scrolltopshow = true;\r\n\t\t\t}\r\n\t\t\tif(scrollY < 150) {\r\n\t\t\t\tthat.scrolltopshow = false\r\n\t\t\t}\r\n\t\t},\r\n\t\tsharemp:function(){\r\n\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\r\n\t\t\tthis.sharetypevisible = false\r\n\t\t},\r\n\t\tshareapp:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tthat.sharetypevisible = false;\r\n\t\t\tuni.showActionSheet({\r\n        itemList: ['发送给微信好友', '分享到微信朋友圈'],\r\n        success: function (res){\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tvar scene = 'WXSceneSession';\r\n\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tvar sharedata = {};\r\n\t\t\t\t\t\tsharedata.provider = 'weixin';\r\n\t\t\t\t\t\tsharedata.type = 0;\r\n\t\t\t\t\t\tsharedata.scene = scene;\r\n\t\t\t\t\t\tsharedata.title = that.product.name;\r\n\t\t\t\t\t\t//sharedata.summary = app.globalData.initdata.desc;\r\n\t\t\t\t\t\tsharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/activity/scoreshop/product?scene=id_'+that.product.id+'-pid_' + app.globalData.mid;\r\n\t\t\t\t\t\tsharedata.imageUrl = that.product.pic;\r\n\t\t\t\t\t\tvar sharelist = app.globalData.initdata.sharelist;\r\n\t\t\t\t\t\tif(sharelist){\r\n\t\t\t\t\t\t\tfor(var i=0;i<sharelist.length;i++){\r\n\t\t\t\t\t\t\t\tif(sharelist[i]['indexurl'] == '/activity/scoreshop/product'){\r\n\t\t\t\t\t\t\t\t\tsharedata.title = sharelist[i].title;\r\n\t\t\t\t\t\t\t\t\tsharedata.summary = sharelist[i].desc;\r\n\t\t\t\t\t\t\t\t\tsharedata.imageUrl = sharelist[i].pic;\r\n\t\t\t\t\t\t\t\t\tif(sharelist[i].url){\r\n\t\t\t\t\t\t\t\t\t\tvar sharelink = sharelist[i].url;\r\n\t\t\t\t\t\t\t\t\t\tif(sharelink.indexOf('/') === 0){\r\n\t\t\t\t\t\t\t\t\t\t\tsharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tif(app.globalData.mid>0){\r\n\t\t\t\t\t\t\t\t\t\t\t sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tsharedata.href = sharelink;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.share(sharedata);\r\n\t\t\t\t\t}\r\n        }\r\n      });\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n<style>\r\n.swiper-container{position:relative}\r\n.swiper {width: 100%;height: 750rpx;overflow: hidden;}\r\n.swiper-item-view{width: 100%;height: 750rpx;}\r\n.swiper .img {width: 100%;height: 750rpx;overflow: hidden;}\r\n\r\n.imageCount {width:100rpx;height:50rpx;background-color: rgba(0, 0, 0, 0.3);border-radius:40rpx;line-height:50rpx;color:#fff;text-align:center;font-size:26rpx;position:absolute;right:13px;bottom:20rpx;}\r\n\r\n.provideo{background:rgba(255,255,255,0.7);width:160rpx;height:54rpx;padding:0 20rpx 0 4rpx;border-radius:27rpx;position:absolute;bottom:30rpx;left:50%;margin-left:-80rpx;display:flex;align-items:center;justify-content:space-between}\r\n.provideo image{width:50rpx;height:50rpx;}\r\n.provideo .txt{flex:1;text-align:center;padding-left:10rpx;font-size:24rpx;color:#333}\r\n\r\n.videobox{width:100%;height:750rpx;text-align:center;background:#000}\r\n.videobox .video{width:100%;height:650rpx;}\r\n.videobox .parsevideo{margin:0 auto;margin-top:20rpx;height:40rpx;line-height:40rpx;color:#333;background:#ccc;width:140rpx;border-radius:25rpx;font-size:24rpx}\r\n\r\n.header {width: 100%;padding: 20rpx 3%;background: #fff;}\r\n.header .price_share{width:100%;min-height:100rpx;display:flex;align-items:center;justify-content:space-between}\r\n.header .price_share .price{display:flex;align-items:flex-end}\r\n.header .price_share .price .f1{font-size:36rpx;color:#51B539;font-weight:bold}\r\n.header .price_share .price .f2{font-size:26rpx;color:#C2C2C2;text-decoration:line-through;margin-left:30rpx;padding-bottom:2px}\r\n.header .price_share .share{display:flex;flex-direction:column;align-items:center;justify-content:center}\r\n.header .price_share .share .img{width:32rpx;height:32rpx;margin-bottom:2px}\r\n.header .price_share .share .txt{color:#333333;font-size:20rpx}\r\n.header .title {color:#000000;font-size:32rpx;line-height:42rpx;font-weight:bold;}\r\n.header .sales_stock{display:flex;justify-content:space-between;height:60rpx;line-height:60rpx;margin-top:30rpx;font-size:24rpx;color:#777777}\r\n.header .commission{display:inline-block;margin-top:20rpx;margin-bottom:10rpx;border-radius:10rpx;font-size:20rpx;height:44rpx;line-height:44rpx;padding:0 20rpx}\r\n\r\n.shop{display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; padding: 20rpx 3%;position: relative; min-height: 100rpx;}\r\n.shop .p1{width:90rpx;height:90rpx;border-radius:6rpx;flex-shrink:0}\r\n.shop .p2{padding-left:10rpx}\r\n.shop .p2 .t1{width: 100%;height:40rpx;line-height:40rpx;overflow: hidden;color: #111;font-weight:bold;font-size:30rpx;}\r\n.shop .p2 .t2{width: 100%;height:30rpx;line-height:30rpx;overflow: hidden;color: #999;font-size:24rpx;margin-top:8rpx}\r\n.shop .p4{height:64rpx;line-height:64rpx;color:#FFFFFF;border-radius:32rpx;margin-left:20rpx;flex-shrink:0;padding:0 30rpx;font-size:24rpx;font-weight:bold}\r\n\r\n.detail{min-height:200rpx;}\r\n\r\n.detail_title{width:100%;display:flex;align-items:center;justify-content:center;margin-top:60rpx;margin-bottom:30rpx}\r\n.detail_title .t0{font-size:28rpx;font-weight:bold;color:#222222;margin:0 20rpx}\r\n.detail_title .t1{width:12rpx;height:12rpx;background:rgba(253, 74, 70, 0.2);transform:rotate(45deg);margin:0 4rpx;margin-top:6rpx}\r\n.detail_title .t2{width:18rpx;height:18rpx;background:rgba(253, 74, 70, 0.4);transform:rotate(45deg);margin:0 4rpx}\r\n\r\n.bottombar{ width: 94%; position: fixed;bottom: 0px; left: 0px; background: #fff;display:flex;height:100rpx;padding:0 4% 0 2%;align-items:center;box-sizing:content-box}\r\n.bottombar .f1{flex:1;display:flex;align-items:center;margin-right:30rpx}\r\n.bottombar .f1 .item{display:flex;flex-direction:column;align-items:center;width:80rpx;position:relative}\r\n.bottombar .f1 .item .img{ width:44rpx;height:44rpx}\r\n.bottombar .f1 .item .t1{font-size:18rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}\r\n.bottombar .op{width:60%;border-radius:36rpx;overflow:hidden;display:flex;}\r\n.bottombar .tocart{flex:1;height:72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none; font-size:28rpx;font-weight:bold}\r\n.bottombar .tobuy{flex:1;height: 72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;}\r\n.bottombar .cartnum{position:absolute;right:4rpx;top:-4rpx;background:rgba(253, 74, 70,0.8);color:#fff;border-radius:50%;width:32rpx;height:32rpx;line-height:32rpx;text-align:center;font-size:22rpx;}\r\n\r\n.buydialog-mask{ position: fixed; top: 0px; left: 0px; width: 100%; background: rgba(0,0,0,0.5); bottom: 0px;z-index:10}\r\n.buydialog{ position: fixed; width: 100%; left: 0px; bottom: 0px; background: #fff;z-index:11;border-radius:20rpx 20rpx 0px 0px}\r\n.buydialog .close{ position: absolute; top: 0; right: 0;padding:20rpx;z-index:12}\r\n.buydialog .close .image{ width: 30rpx; height:30rpx; }\r\n.buydialog .title{ width: 94%;position: relative; margin: 0 3%; padding:20rpx 0px; border-bottom:0; height: 190rpx;}\r\n.buydialog .title .img{ width: 160rpx; height: 160rpx; position: absolute; top: 20rpx; border-radius: 10rpx; border: 0 #e5e5e5 solid;background-color: #fff}\r\n.buydialog .title .price{ padding-left:180rpx;width:100%;font-size: 36rpx;height:70rpx; color: #FC4343;overflow: hidden;}\r\n.buydialog .title .price .t1{ font-size:26rpx}\r\n.buydialog .title .price .t2{ font-size:26rpx;text-decoration:line-through;color:#aaa;margin-left:10rpx}\r\n.buydialog .title .choosename{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}\r\n.buydialog .title .stock{ padding-left:180rpx;width: 100%;font-size: 24rpx;height: 42rpx;line-height:42rpx;color:#888888}\r\n\r\n.buydialog .guigelist{ width: 94%; position: relative; margin: 0 3%; padding:0px 0px 10px 0px; border-bottom: 0; }\r\n.buydialog .guigelist .name{ height:70rpx; line-height: 70rpx;}\r\n.buydialog .guigelist .item{ font-size: 30rpx;color: #333;flex-wrap:wrap}\r\n.buydialog .guigelist .item2{ height:60rpx;line-height:60rpx;margin-bottom:4px;border:0; border-radius:4rpx; padding:0 40rpx;color:#666666; margin-right: 10rpx; font-size:26rpx;background:#F4F4F4}\r\n.buydialog .guigelist .on{color:#FC4343;background:rgba(252,67,67,0.1);font-weight:bold}\r\n.buydialog .buynum{ width: 94%; position: relative; margin: 0 3%; padding:10px 0px 10px 0px; }\r\n.buydialog .addnum {font-size: 30rpx;color: #666;width:auto;display:flex;align-items:center}\r\n.buydialog .addnum .plus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n.buydialog .addnum .minus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n.buydialog .addnum .img{width:24rpx;height:24rpx}\r\n.buydialog .addnum .input{flex:1;width:50rpx;border:0;text-align:center;color:#2B2B2B;font-size:28rpx;margin: 0 15rpx;}\r\n\r\n.buydialog .op{width:90%;margin:20rpx 5%;border-radius:36rpx;overflow:hidden;display:flex;margin-top:100rpx;}\r\n.buydialog .addcart{flex:1;height:72rpx; line-height: 72rpx; color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}\r\n.buydialog .tobuy{flex:1;height: 72rpx; line-height: 72rpx; color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}\r\n.buydialog .nostock{flex:1;height: 72rpx; line-height: 72rpx; background:#aaa; color: #fff; border-radius: 0px; border: none;}\r\n\r\n.header .price_share .price2{display:flex;align-items:flex-start;flex-direction: column;}\r\n.header .price_share .price2 .f1{font-size:36rpx;color:#FC4343;font-weight:bold}\r\n.header .price_share .price2 .f2{font-size:26rpx;color:#C2C2C2;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213322463\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}