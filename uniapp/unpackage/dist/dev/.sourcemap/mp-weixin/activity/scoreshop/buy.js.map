{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/buy.vue?e84f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/buy.vue?f660", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/buy.vue?5fb4", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/buy.vue?608d", "uni-app:///activity/scoreshop/buy.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/buy.vue?ab2b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/scoreshop/buy.vue?10ae"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "editorFormdata", "test", "prolist", "freightList", "address", "needaddress", "linkman", "tel", "freightkey", "freight_price", "pstimetext", "freight_time", "totalmoney", "totalscore", "totalweight", "totalnum", "totalprice", "storedata", "storename", "latitude", "longitude", "pstimeDialogShow", "pstimeIndex", "havetongcheng", "storeshowall", "allbuydata", "nowbid", "contact_require", "othermid", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "prodata", "inputLinkman", "inputTel", "<PERSON><PERSON><PERSON><PERSON>", "changeAddress", "calculatePrice", "alltotalprice", "allfreight_price", "changeFreight", "choosePstime", "itemlist", "pstimeRadioChange", "hidePstimeDialog", "choosestore", "topay", "formdata", "newformdata", "bid", "freight_id", "storeid", "buydata", "addressid", "openLocation", "uni", "name", "scale", "openMendian", "editorChooseImage", "console", "editor<PERSON><PERSON>ose<PERSON><PERSON>s", "pics", "editorBind<PERSON>icker<PERSON><PERSON><PERSON>", "doStoreShowAll", "removeimg"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxMA;AAAA;AAAA;AAAA;AAAi0B,CAAgB,iyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2Pr1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;QAAAP;MAAA;QACAK;QACA;UACAC;YACAA;UACA;UACA;QACA;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAC;YACA;YACA;YACAD;YACAA;YACA;YACA;cACA;cACA;gBACA;kBACA;kBACA;oBACA;sBACA;wBACA;wBACAhB;sBACA;oBACA;oBACAA;sBACA;oBACA;oBACA;sBACA;wBACAA;sBACA;oBACA;oBACAQ;kBACA;gBACA;cACA;YACA;YACAQ;UACA;QACA;MACA;IACA;IACAG;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACAJ;IACA;IACA;IACAK;MACA;MACAN;IACA;IACA;IACAO;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;UACAnC;QACA;QACA;QACA;QACAoC;QACAC;QAEAjB;QACAA;MACA;MACA;MACAQ;MACAA;MACAA;IACA;IACAU;MACA;MACA;MACA;MACA;MACA;MACA;QACAT;QAAA;MACA;MACA;QACAA;QAAA;MACA;MACAT;MACAQ;MACAA;MACAA;IACA;IACAW;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;QACAX;QACA;MACA;MACAD;MACAA;MACAA;IACA;IACAa;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACArB;MACAA;MACAQ;MACAA;IACA;IACAc;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAvB;MACA;IACA;IACA;IACAwB;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;QACAf;QACA;MACA;MAEA;MACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACA;UACA;QACA;UACA;QACA;QACA;QACA;QACA;QACA;UACA;UACA;YACAA;YAAA;UACA;UACA;YACAgB;UACA;UACAC;QACA;QAEA;UACAC;UACAjB;UACAkB;UACA1C;UACA2C;UACAJ;QACA;QACAK;MACA;MACArB;MACAA;QAAAqB;QAAAC;QAAAlD;QAAAC;QAAAqB;MAAA;QACAM;QACA;UACAA;UACA;QACA;QACAA;MACA;IACA;IACAuB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;QACAvC;QACAC;QACAuC;QACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA3B;IACA;IACA4B;MACA;MACA;MACA;MACA;MACA;MAAA;MACA;MACA5B;QACAlC;QACA+D;QACA9B;QACAA;QACAA;MACA;IACA;IACA;IACA+B;MACA;MACA;MACA;MACA;MAAA;MACA;MACA;MACA9B;QACA;QACA;UACA+B;QACA;QACA;UACAA;QACA;QACAjE;QACAiC;QACAA;MACA;IACA;IACAiC;MACA;MACA;MACA;MACA;MACA;MACA;MACAlE;MACA;MACAiC;MACAA;IACA;IACAkC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACAH;QACAjE;QACAiC;QACAA;MACA;QACA;QACAgC;QACAhC;QACAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7nBA;AAAA;AAAA;AAAA;AAA8qC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAlsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/scoreshop/buy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/scoreshop/buy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./buy.vue?vue&type=template&id=7024ede6&\"\nvar renderjs\nimport script from \"./buy.vue?vue&type=script&lang=js&\"\nexport * from \"./buy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buy.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/scoreshop/buy.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=template&id=7024ede6&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l4 = _vm.isload\n    ? _vm.__map(_vm.allbuydata, function (buydata, index) {\n        var $orig = _vm.__get_orig(buydata)\n        var m0 = _vm.t(\"color1\")\n        var m1 = _vm.t(\"积分\")\n        var l0 = _vm.__map(buydata.freightList, function (item, idx2) {\n          var $orig = _vm.__get_orig(item)\n          var m2 = buydata.freightkey == idx2 ? _vm.t(\"color1\") : null\n          var m3 = buydata.freightkey == idx2 ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m2: m2,\n            m3: m3,\n          }\n        })\n        var g0 =\n          buydata.freightList[buydata.freightkey].minpriceset == 1 &&\n          buydata.freightList[buydata.freightkey].minprice > 0 &&\n          buydata.freightList[buydata.freightkey].minprice * 1 >\n            buydata.product_price * 1\n            ? (\n                buydata.freightList[buydata.freightkey].minprice -\n                buydata.product_price\n              ).toFixed(2)\n            : null\n        var l1 =\n          buydata.freightList[buydata.freightkey].pstype == 1 &&\n          buydata.freightList[buydata.freightkey].isbusiness != 1\n            ? _vm.__map(\n                buydata.freightList[buydata.freightkey].storedata,\n                function (item, idx) {\n                  var $orig = _vm.__get_orig(item)\n                  var m4 =\n                    (idx < 5 || _vm.storeshowall == true) &&\n                    buydata.freightList[buydata.freightkey].storekey == idx\n                      ? _vm.t(\"color1\")\n                      : null\n                  return {\n                    $orig: $orig,\n                    m4: m4,\n                  }\n                }\n              )\n            : null\n        var g1 =\n          buydata.freightList[buydata.freightkey].pstype == 1 &&\n          buydata.freightList[buydata.freightkey].isbusiness != 1\n            ? _vm.storeshowall == false &&\n              buydata.freightList[buydata.freightkey].storedata.length > 5\n            : null\n        var g2 =\n          buydata.freightList[buydata.freightkey].pstype == 1 &&\n          buydata.freightList[buydata.freightkey].isbusiness == 1\n            ? _vm.storeshowall == false &&\n              buydata.freightList[buydata.freightkey].storedata.length > 5\n            : null\n        var l2 =\n          buydata.freightList[buydata.freightkey].pstype == 5\n            ? _vm.__map(\n                buydata.freightList[buydata.freightkey].storedata,\n                function (item, idx) {\n                  var $orig = _vm.__get_orig(item)\n                  var m5 =\n                    (idx < 5 || _vm.storeshowall == true) &&\n                    buydata.freightList[buydata.freightkey].storekey == idx\n                      ? _vm.t(\"color1\")\n                      : null\n                  return {\n                    $orig: $orig,\n                    m5: m5,\n                  }\n                }\n              )\n            : null\n        var g3 =\n          buydata.freightList[buydata.freightkey].pstype == 5\n            ? _vm.storeshowall == false &&\n              buydata.freightList[buydata.freightkey].storedata.length > 5\n            : null\n        var m6 = _vm.t(\"积分\")\n        var l3 = _vm.__map(\n          buydata.freightList[buydata.freightkey].formdata,\n          function (item, idx) {\n            var $orig = _vm.__get_orig(item)\n            var g4 =\n              item.key == \"upload_pics\" &&\n              buydata.editorFormdata &&\n              buydata.editorFormdata[idx]\n                ? buydata.editorFormdata[idx].join(\",\")\n                : null\n            return {\n              $orig: $orig,\n              g4: g4,\n            }\n          }\n        )\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          l0: l0,\n          g0: g0,\n          l1: l1,\n          g1: g1,\n          g2: g2,\n          l2: l2,\n          g3: g3,\n          m6: m6,\n          l3: l3,\n        }\n      })\n    : null\n  var m7 = _vm.isload && _vm.totalprice * 1 > 0 ? _vm.t(\"color1\") : null\n  var m8 = _vm.isload && _vm.totalprice * 1 > 0 ? _vm.t(\"积分\") : null\n  var m9 = _vm.isload && !(_vm.totalprice * 1 > 0) ? _vm.t(\"color1\") : null\n  var m10 = _vm.isload && !(_vm.totalprice * 1 > 0) ? _vm.t(\"积分\") : null\n  var m11 = _vm.isload ? _vm.t(\"color1\") : null\n  var m12 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var l5 =\n    _vm.isload && _vm.pstimeDialogShow\n      ? _vm.__map(\n          _vm.allbuydata[_vm.nowbid].freightList[\n            _vm.allbuydata[_vm.nowbid].freightkey\n          ].pstimeArr,\n          function (item, index) {\n            var $orig = _vm.__get_orig(item)\n            var m13 =\n              _vm.allbuydata[_vm.nowbid].freight_time == item.value\n                ? _vm.t(\"color1\")\n                : null\n            return {\n              $orig: $orig,\n              m13: m13,\n            }\n          }\n        )\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l4: l4,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        l5: l5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<form @submit=\"topay\">\n\t\t\t<view v-if=\"needaddress==0\" class=\"address-add\">\n\t\t\t\t<view class=\"linkitem\">\n\t\t\t\t\t<label style=\"color: red;\" v-if=\"contact_require==1\"> * </label><text class=\"f1\">联 系 人：</text>\n\t\t\t\t\t<input type=\"text\" class=\"input\" :value=\"linkman\" placeholder=\"请输入您的姓名\" @input=\"inputLinkman\" placeholder-style=\"color:#626262;font-size:28rpx\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"linkitem\">\n\t\t\t\t\t<label style=\"color: red;\" v-if=\"contact_require==1\"> * </label><text class=\"f1\">联系电话：</text>\n\t\t\t\t\t<input type=\"text\" class=\"input\" :value=\"tel\" placeholder=\"请输入您的手机号\" @input=\"inputTel\" placeholder-style=\"color:#626262;font-size:28rpx\"/>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view v-else class=\"address-add flex-y-center\" @tap=\"goto\" :data-url=\"'/pagesB/address/address?fromPage=buy&type=' + (havetongcheng==1?'1':'0')\">\n\t\t\t\t<view class=\"f1\"><image class=\"img\" :src=\"pre_url+'/static/img/address.png'\" /></view>\n\t\t\t\t<view class=\"f2 flex1\" v-if=\"address.name\">\n\t\t\t\t\t<view style=\"font-weight:bold;color:#111111;font-size:30rpx\">{{address.name}} {{address.tel}} <text v-if=\"address.company\">{{address.company}}</text></view>\n\t\t\t\t\t<view style=\"font-size:24rpx\">{{address.area}} {{address.address}}</view>\n\t\t\t\t</view>\n\t\t\t\t<view v-else class=\"f2 flex1\">请选择收货地址</view>\n\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"f3\"/>\n\t\t\t</view>\n\t\t\t<view v-for=\"(buydata, index) in allbuydata\" :key=\"index\" class=\"buydata\">\n\t\t\t\t<view class=\"btitle\">\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/ico-shop.png'\" />{{buydata.business.name}}\n\t\t\t\t</view>\n\t\t\t\t<view class=\"bcontent\">\n\t\t\t\t\t<view class=\"product\">\n\t\t\t\t\t\t<view v-for=\"(item, index2) in buydata.prodata\" :key=\"index2\" class=\"item flex\">\n\t\t\t\t\t\t\t<view class=\"img\"><image class=\"img\" :src=\"item.product.ggpic || item.product.pic\"/></view>\n\t\t\t\t\t\t\t<view class=\"info flex1\">\n\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.product.name}}</view>\n\t\t\t\t\t\t\t\t<view class=\"f2\" v-if=\"item.product.ggname\" style=\"color:#666\">已选规格: {{item.product.ggname}}</view>\n\t\t\t\t\t\t\t\t<view class=\"f2\" v-else>市场价: ￥{{item.product.sell_price}}</view>\n\t\t\t\t\t\t\t\t<view class=\"f3\" :style=\"{color:t('color1')}\">{{item.product.score_price}}{{t('积分')}}<text v-if=\"item.product.money_price>0\">+{{item.product.money_price}}元</text><text style=\"padding-left:10rpx\"> × {{item.num}}</text></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"freight\">\n\t\t\t\t\t\t<view class=\"f1\">配送方式</view>\n\t\t\t\t\t\t<view class=\"freight-ul\">\n\t\t\t\t\t\t\t<view class=\"flex\" style=\"width:100%;overflow-y:hidden;overflow-x:scroll;\">\n\t\t\t\t\t\t\t\t<block v-for=\"(item, idx2) in buydata.freightList\" :key=\"idx2\">\n\t\t\t\t\t\t\t\t\t<view class=\"freight-li\"\n\t\t\t\t\t\t\t\t\t\t:style=\"buydata.freightkey==idx2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''\"\n\t\t\t\t\t\t\t\t\t\t@tap=\"changeFreight\" :data-bid=\"buydata.bid\" :data-index=\"idx2\">{{item.name}}\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"freighttips\"\n\t\t\t\t\t\t\tv-if=\"buydata.freightList[buydata.freightkey].minpriceset==1 && buydata.freightList[buydata.freightkey].minprice > 0 && buydata.freightList[buydata.freightkey].minprice*1 > buydata.product_price*1\">\n\t\t\t\t\t\t\t满{{buydata.freightList[buydata.freightkey].minprice}}元起送，还差{{(buydata.freightList[buydata.freightkey].minprice - buydata.product_price).toFixed(2)}}元\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"freighttips\" v-if=\"buydata.freightList[buydata.freightkey].isoutjuli==1\">超出配送范围</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"price\" v-if=\"buydata.freightList[buydata.freightkey].pstimeset==1\">\n\t\t\t\t\t\t<view class=\"f1\">{{buydata.freightList[buydata.freightkey].pstype==1?'取货':'配送'}}时间</view>\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"choosePstime\" :data-bid=\"buydata.bid\">\n\t\t\t\t\t\t\t{{buydata.pstimetext==''?'请选择时间':buydata.pstimetext}}<text class=\"iconfont iconjiantou\"\n\t\t\t\t\t\t\t\tstyle=\"color:#999;font-weight:normal\"></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"storeitem\" v-if=\"buydata.freightList[buydata.freightkey].pstype==1 && buydata.freightList[buydata.freightkey].isbusiness!=1\">\n\t\t\t\t\t\t<view class=\"panel\">\n\t\t\t\t\t\t\t<view class=\"f1\">取货地点</view>\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"openMendian\" :data-bid=\"buydata.bid\" \n\t\t\t\t\t\t\t\t:data-freightkey=\"buydata.freightkey\"\n\t\t\t\t\t\t\t\t:data-storekey=\"buydata.freightList[buydata.freightkey].storekey\"><text\n\t\t\t\t\t\t\t\t\tclass=\"iconfont icondingwei\"></text>{{buydata.freightList[buydata.freightkey].storedata[buydata.freightList[buydata.freightkey].storekey].name}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<block v-for=\"(item, idx) in buydata.freightList[buydata.freightkey].storedata\" :key=\"idx\">\n\t\t\t\t\t\t\t<view class=\"radio-item\" @tap.stop=\"choosestore\" :data-bid=\"buydata.bid\" :data-index=\"idx\" v-if=\"idx<5 || storeshowall==true\">\n\t\t\t\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t\t\t\t<view>{{item.name}}</view>\n\t\t\t\t\t\t\t\t\t<view v-if=\"item.address\" class=\"flex-y-center\" style=\"text-align:left;font-size:24rpx;color:#aaaaae;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:1;overflow: hidden;\">{{item.address}}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<text style=\"color:#f50;\">{{item.juli}}</text>\n\t\t\t\t\t\t\t\t<view class=\"radio\" :style=\"buydata.freightList[buydata.freightkey].storekey==idx ? 'background:'+t('color1')+';border:0' : ''\">\n\t\t\t\t\t\t\t\t\t<image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\" />\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<view v-if=\"storeshowall==false && (buydata.freightList[buydata.freightkey].storedata).length > 5\" class=\"storeviewmore\" @tap=\"doStoreShowAll\">- 查看更多 - </view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"storeitem\" v-if=\"buydata.freightList[buydata.freightkey].pstype==1 && buydata.freightList[buydata.freightkey].isbusiness==1\">\n\t\t\t\t\t\t<view class=\"panel\">\n\t\t\t\t\t\t\t<view class=\"f1\">取货地点</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<block v-for=\"(item, idx) in buydata.freightList[buydata.freightkey].storedata\" :key=\"idx\">\n\t\t\t\t\t\t\t<view class=\"radio-item\" v-if=\"idx<5 || storeshowall==true\" @tap=\"openLocation\" :data-freightkey=\"buydata.freightkey\" :data-storekey=\"idx\" :data-bid=\"buydata.bid\" :data-index=\"idx\">\n\t\t\t\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t\t\t\t<view>{{item.name}}</view>\n\t\t\t\t\t\t\t\t\t<view v-if=\"item.address\" class=\"flex-y-center\" style=\"text-align:left;font-size:24rpx;color:#aaaaae;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:1;overflow: hidden;\">{{item.address}}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<text style=\"color:#f50;\">{{item.juli}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<view v-if=\"storeshowall==false && (buydata.freightList[buydata.freightkey].storedata).length > 5\" class=\"storeviewmore\" @tap=\"doStoreShowAll\">- 查看更多 - </view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- 门店配送 -->\n\t\t\t\t\t<view class=\"storeitem\" v-if=\"buydata.freightList[buydata.freightkey].pstype==5\">\n\t\t\t\t\t\t<view class=\"panel\">\n\t\t\t\t\t\t\t<view class=\"f1\">配送门店</view>\n\t\t\t\t\t\t\t<view class=\"f2\" @tap=\"openMendian\" :data-bid=\"buydata.bid\"\n\t\t\t\t\t\t\t\t:data-freightkey=\"buydata.freightkey\"\n\t\t\t\t\t\t\t\t:data-storekey=\"buydata.freightList[buydata.freightkey].storekey\"><text\n\t\t\t\t\t\t\t\t\tclass=\"iconfont icondingwei\"></text>{{buydata.freightList[buydata.freightkey].storedata[buydata.freightList[buydata.freightkey].storekey].name}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<block v-for=\"(item, idx) in buydata.freightList[buydata.freightkey].storedata\" :key=\"idx\">\n\t\t\t\t\t\t\t<view class=\"radio-item\" @tap.stop=\"choosestore\" :data-bid=\"buydata.bid\" :data-index=\"idx\" v-if=\"idx<5 || storeshowall==true\">\n\t\t\t\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t\t\t\t<view>{{item.name}}</view>\n\t\t\t\t\t\t\t\t\t<view v-if=\"item.address\" style=\"text-align:left;font-size:24rpx;color:#aaaaae;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:1;overflow: hidden;\">{{item.address}}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<text style=\"color:#f50;\">{{item.juli}}</text>\n\t\t\t\t\t\t\t\t<view class=\"radio\"\n\t\t\t\t\t\t\t\t\t:style=\"buydata.freightList[buydata.freightkey].storekey==idx ? 'background:'+t('color1')+';border:0' : ''\">\n\t\t\t\t\t\t\t\t\t<image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\" />\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<view v-if=\"storeshowall==false && (buydata.freightList[buydata.freightkey].storedata).length > 5\" class=\"storeviewmore\" @tap=\"doStoreShowAll\">- 查看更多 - </view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"price\">\n\t\t\t\t\t\t<text class=\"f1\">商品金额</text>\n\t\t\t\t\t\t<text class=\"f2\">¥{{buydata.product_price}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"price\">\n\t\t\t\t\t\t<text class=\"f1\">所需{{t('积分')}}</text>\n\t\t\t\t\t\t<text class=\"f2\">{{buydata.product_score}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"price\">\n\t\t\t\t\t\t<view class=\"f1\">{{buydata.freightList[buydata.freightkey].freight_price_txt || '运费'}}<text v-if=\"buydata.freightList[buydata.freightkey].pstype!=1 && buydata.freightList[buydata.freightkey].freeset==1\" style=\"color:#aaa;font-size:24rpx;\">（满{{buydata.freightList[buydata.freightkey].free_price}}元包邮）</text></view>\n\t\t\t\t\t\t<text class=\"f2\">+¥{{buydata.freightList[buydata.freightkey].freight_price}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view style=\"display:none\">{{test}}</view>\n\t\t\t\t\t<view class=\"form-item\" v-for=\"(item,idx) in buydata.freightList[buydata.freightkey].formdata\" :key=\"item.id\">\n\t\t\t\t\t\t<view class=\"label\">{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\n\t\t\t\t\t\t<block v-if=\"item.key=='input'\">\n\t\t\t\t\t\t\t<input type=\"text\" :name=\"'form'+buydata.bid+'_'+idx\" class=\"input\" :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"/>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-if=\"item.key=='textarea'\">\n\t\t\t\t\t\t\t<textarea :name=\"'form'+buydata.bid+'_'+idx\" class='textarea' :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"/>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-if=\"item.key=='radio'\">\n\t\t\t\t\t\t\t<radio-group class=\"radio-group\" :name=\"'form'+buydata.bid+'_'+idx\">\n\t\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\n\t\t\t\t\t\t\t\t\t<radio class=\"radio\" :value=\"item1\"/>{{item1}}\n\t\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t</radio-group>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-if=\"item.key=='checkbox'\">\n\t\t\t\t\t\t\t<checkbox-group :name=\"'form'+buydata.bid+'_'+idx\" class=\"checkbox-group\">\n\t\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\n\t\t\t\t\t\t\t\t\t<checkbox class=\"checkbox\" :value=\"item1\"/>{{item1}}\n\t\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t</checkbox-group>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-if=\"item.key=='selector'\">\n\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" :name=\"'form'+buydata.bid+'_'+idx\" :value=\"item.val2[buydata.editorFormdata[idx]]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-bid=\"buydata.bid\" :data-idx=\"idx\">\n\t\t\t\t\t\t\t\t<view v-if=\"buydata.editorFormdata && (buydata.editorFormdata[idx] || buydata.editorFormdata[idx]===0)\"> {{item.val2[buydata.editorFormdata[idx]]}}</view>\n\t\t\t\t\t\t\t\t<view v-else>请选择</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-if=\"item.key=='time'\">\n\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"time\" :name=\"'form'+buydata.bid+'_'+idx\" :value=\"buydata.editorFormdata[idx]\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-bid=\"buydata.bid\" :data-idx=\"idx\">\n\t\t\t\t\t\t\t\t<view v-if=\"buydata.editorFormdata && buydata.editorFormdata[idx]\">{{buydata.editorFormdata[idx]}}</view>\n\t\t\t\t\t\t\t\t<view v-else>请选择</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-if=\"item.key=='date'\">\n\t\t\t\t\t\t\t<picker class=\"picker\" mode=\"date\" :name=\"'form'+buydata.bid+'_'+idx\" :value=\"buydata.editorFormdata[idx]\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-bid=\"buydata.bid\" :data-idx=\"idx\">\n\t\t\t\t\t\t\t\t<view v-if=\"buydata.editorFormdata && buydata.editorFormdata[idx]\">{{buydata.editorFormdata[idx]}}</view>\n\t\t\t\t\t\t\t\t<view v-else>请选择</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-if=\"item.key=='upload' && buydata.editorFormdata\">\n\t\t\t\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form'+buydata.bid+'_'+idx\" :value=\"buydata.editorFormdata[idx]\"/>\n\t\t\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\n\t\t\t\t\t\t\t\t<view class=\"form-imgbox\" v-if=\"buydata.editorFormdata[idx]\">\n\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" style=\"z-index: 2;\" @tap=\"removeimg\" :data-bid=\"buydata.bid\" :data-idx=\"idx\"><image style=\"display:block\" :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\n\t\t\t\t\t\t\t\t\t<view class=\"form-imgbox-img\"><image class=\"image\" :src=\"buydata.editorFormdata[idx]\" @click=\"previewImage\" :data-url=\"buydata.editorFormdata[idx]\" mode=\"aspectFit\"/></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view v-else class=\"form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}\" @click=\"editorChooseImage\" :data-bid=\"buydata.bid\" :data-idx=\"idx\"></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</block>\n            <block v-if=\"item.key=='upload_pics'\">\n              <input type=\"text\" style=\"display:none\" :name=\"'form'+buydata.bid+'_'+idx\" :value=\"buydata.editorFormdata && buydata.editorFormdata[idx]?buydata.editorFormdata[idx].join(','):''\" maxlength=\"-1\"/>\n              <view class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\n                <view v-for=\"(item2, index2) in buydata.editorFormdata[idx]\" :key=\"index2\" class=\"form-imgbox\" >\n                  <view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-bid=\"buydata.bid\" :data-index=\"index2\" data-type=\"pics\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\"><image :src=\"pre_url+'/static/img/ico-del.png'\" class=\"image\"></image></view>\n                  <view class=\"form-imgbox-img\" style=\"margin-bottom: 10rpx;\"><image class=\"image\" :src=\"item2\" @click=\"previewImage\" :data-url=\"item2\" mode=\"aspectFit\" :data-idx=\"idx\"/></view>\n                </view>\n                <view class=\"form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}\" @click=\"editorChooseImages\" :data-bid=\"buydata.bid\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\" data-type=\"pics\"></view>\n              </view>\n            </block>\n\t\t\t\t\t</view>\n\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view style=\"width:100%;height:110rpx;\"></view>\n\t\t\t<view class=\"footer flex notabbarbot\">\n\t\t\t\t<view class=\"f1 flex1\">总计：\n\t\t\t\t\t<text class=\"txt\" style=\"font-weight:bold\" :style=\"{color:t('color1')}\" v-if=\"totalprice*1 > 0\">￥{{totalprice}} + {{totalscore}}{{t('积分')}}</text>\n\t\t\t\t\t<text class=\"txt\" :style=\"{color:t('color1')}\" v-else>{{totalscore}}{{t('积分')}}</text>\n\t\t\t\t</view>\n\t\t\t\t<button class=\"op\" form-type=\"submit\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">提交订单</button>\n\t\t\t</view>\n\t\t</form>\n\n\t\t<view v-if=\"pstimeDialogShow\" class=\"popup__container\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidePstimeDialog\"></view>\n\t\t\t<view class=\"popup__modal\">\n\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t<text\n\t\t\t\t\t\tclass=\"popup__title-text\">请选择{{allbuydata[nowbid].freightList[allbuydata[nowbid].freightkey].pstype==1?'取货':'配送'}}时间</text>\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\n\t\t\t\t\t\**********=\"hidePstimeDialog\" />\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t<view class=\"pstime-item\"\n\t\t\t\t\t\tv-for=\"(item, index) in allbuydata[nowbid].freightList[allbuydata[nowbid].freightkey].pstimeArr\"\n\t\t\t\t\t\t:key=\"index\" @tap=\"pstimeRadioChange\" :data-index=\"index\">\n\t\t\t\t\t\t<view class=\"flex1\">{{item.title}}</view>\n\t\t\t\t\t\t<view class=\"radio\"\n\t\t\t\t\t\t\t:style=\"allbuydata[nowbid].freight_time==item.value ? 'background:'+t('color1')+';border:0' : ''\">\n\t\t\t\t\t\t\t<image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<wxxieyi></wxxieyi>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\n\t\t\tpre_url:app.globalData.pre_url,\n\t\t\teditorFormdata:[],\n\t\t\ttest:'test',\n\n      prolist: [],\n      freightList: [],\n      address: [],\n      needaddress: 1,\n      linkman: '',\n      tel: '',\n      freightkey: 0,\n      freight_price: 0,\n      pstimetext: '',\n      freight_time: '',\n      totalmoney: 0,\n      totalscore: 0,\n      totalweight: 0,\n      totalnum: 1,\n\t\t\ttotalprice:'0.00',\n      storedata: [],\n      storename: '',\n      latitude: '',\n      longitude: '',\n      pstimeDialogShow: false,\n      pstimeIndex: -1,\n      havetongcheng: \"\",\n\t\t\tstoreshowall:false,\n\t\t\tallbuydata: {},\n\t\t\tnowbid: 0,\n\t\t\tcontact_require:0,\n      othermid:0,\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n    this.othermid = app.globalData.othermid || 0;\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this; //获取产品信息\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiScoreshop/buy', {prodata: that.opt.prodata,othermid:that.othermid}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 0) {\n\t\t\t\t\tapp.alert(res.msg, function(){\n\t\t\t\t\t\tapp.goback()\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthat.totalmoney = res.totalmoney;\n\t\t\t\tthat.totalscore = res.totalscore;\n\t\t\t\tthat.havetongcheng = res.havetongcheng;\n\t\t\t\tthat.address = res.address;\n\t\t\t\tthat.linkman = res.linkman;\n\t\t\t\tthat.tel = res.tel;\n\t\t\t\tthat.allbuydata = res.allbuydata;\n\t\t\t\tthat.contact_require = res.contact_require;\n\t\t\t\tthat.calculatePrice();\n\t\t\t\tthat.loaded();\n\t\t\t\tif (res.needLocation == 1) {\n\t\t\t\t\tapp.getLocation(function(res) {\n\t\t\t\t\t\tvar latitude = res.latitude;\n\t\t\t\t\t\tvar longitude = res.longitude;\n\t\t\t\t\t\tthat.latitude = latitude;\n\t\t\t\t\t\tthat.longitude = longitude;\n\t\t\t\t\t\tvar allbuydata = that.allbuydata;\n\t\t\t\t\t\tfor (var i in allbuydata) {\n\t\t\t\t\t\t\tvar freightList = allbuydata[i].freightList;\n\t\t\t\t\t\t\tfor (var j in freightList) {\n\t\t\t\t\t\t\t\tif (freightList[j].pstype == 1 || freightList[j].pstype == 5) {\n\t\t\t\t\t\t\t\t\tvar storedata = freightList[j].storedata;\n\t\t\t\t\t\t\t\t\tif (storedata) {\n\t\t\t\t\t\t\t\t\t\tfor (var x in storedata) {\n\t\t\t\t\t\t\t\t\t\t\tif (latitude && longitude && storedata[x].latitude && storedata[x].longitude) {\n\t\t\t\t\t\t\t\t\t\t\t\tvar juli = that.getDistance(latitude, longitude,storedata[x].latitude, storedata[x].longitude);\n\t\t\t\t\t\t\t\t\t\t\t\tstoredata[x].juli = juli;\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tstoredata.sort(function(a, b) {\n\t\t\t\t\t\t\t\t\t\t\treturn a[\"juli\"] - b[\"juli\"];\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\tfor (var x in storedata) {\n\t\t\t\t\t\t\t\t\t\t\tif (storedata[x].juli) {\n\t\t\t\t\t\t\t\t\t\t\t\tstoredata[x].juli = storedata[x].juli + '千米';\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tallbuydata[i].freightList[j].storedata = storedata;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthat.allbuydata = allbuydata;\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n    inputLinkman: function (e) {\n      this.linkman = e.detail.value;\n    },\n    inputTel: function (e) {\n      this.tel = e.detail.value;\n    },\n    //选择收货地址\n    chooseAddress: function () {\n      app.goto('/pagesB/address/address?fromPage=buy&type=' + (this.havetongcheng == 1 ? '1' : '0'));\n    },\n    //改变收货地址\n    changeAddress: function () {\n      var that = this;\n      that.onLoad();\n    },\n\t\t//计算价格\n\t\tcalculatePrice: function() {\n\t\t\tvar that = this;\n\t\t\tvar address = that.address;\n\t\t\tvar allbuydata = that.allbuydata;\n\t\t\tvar alltotalprice = 0;\n\t\t\tvar allfreight_price = 0;\n\t\t\tvar needaddress = 0;\n\t\t\tfor (var k in allbuydata) {\n\t\t\t\tvar product_price = parseFloat(allbuydata[k].product_price);\n\t\t\t\t//算运费\n\t\t\t\tvar freightdata = allbuydata[k].freightList[allbuydata[k].freightkey];\n\t\t\t\tvar freight_price = freightdata['freight_price'];\n\t\t\t\tif (freightdata.pstype != 1 && freightdata.pstype != 3 && freightdata.pstype != 4) {\n\t\t\t\t\tneedaddress = 1;\n\t\t\t\t}\n\t\t\t\tvar totalprice = product_price + freight_price;\n\t\t\t\tif (totalprice < 0) totalprice = 0;\n\t\t\t\talltotalprice += totalprice;\n\t\t\t\tallfreight_price += freight_price;\n\n\t\t\t\tallbuydata[k].totalprice = totalprice.toFixed(2);\n\t\t\t\tallbuydata[k].freight_price = freight_price.toFixed(2);\n\t\t\t}\n\t\t\tif (alltotalprice < 0) alltotalprice = 0;\n\t\t\tthat.needaddress = needaddress;\n\t\t\tthat.allbuydata = allbuydata;\n\t\t\tthat.totalprice = alltotalprice.toFixed(2);\n\t\t},\n\t\tchangeFreight: function(e) {\n\t\t\tvar that = this;\n\t\t\tvar allbuydata = that.allbuydata;\n\t\t\tvar bid = e.currentTarget.dataset.bid;\n\t\t\tvar index = e.currentTarget.dataset.index;\n\t\t\tvar freightList = allbuydata[bid].freightList;\n\t\t\tif(freightList[index].pstype==1 && freightList[index].storedata.length < 1) {\n\t\t\t\tapp.error('无可自提门店');return;\n\t\t\t}\n\t\t\tif(freightList[index].pstype==5 && freightList[index].storedata.length < 1) {\n\t\t\t\tapp.error('无可配送门店');return;\n\t\t\t}\n\t\t\tallbuydata[bid].freightkey = index;\n\t\t\tthat.allbuydata = allbuydata;\n\t\t\tthat.calculatePrice();\n\t\t\tthat.allbuydata[bid].editorFormdata = [];\n\t\t},\n\t\tchoosePstime: function(e) {\n\t\t\tvar that = this;\n\t\t\tvar allbuydata = that.allbuydata;\n\t\t\tvar bid = e.currentTarget.dataset.bid;\n\t\t\tvar freightkey = allbuydata[bid].freightkey;\n\t\t\tvar freightList = allbuydata[bid].freightList;\n\t\t\tvar freight = freightList[freightkey];\n\t\t\tvar pstimeArr = freightList[freightkey].pstimeArr;\n\t\t\tvar itemlist = [];\n\t\t\tfor (var i = 0; i < pstimeArr.length; i++) {\n\t\t\t\titemlist.push(pstimeArr[i].title);\n\t\t\t}\n\t\t\tif (itemlist.length == 0) {\n\t\t\t\tapp.alert('当前没有可选' + (freightList[freightkey].pstype == 1 ? '取货' : '配送') + '时间段');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthat.nowbid = bid;\n\t\t\tthat.pstimeDialogShow = true;\n\t\t\tthat.pstimeIndex = -1;\n\t\t},\n\t\tpstimeRadioChange: function(e) {\n\t\t\tvar that = this;\n\t\t\tvar allbuydata = that.allbuydata;\n\t\t\tvar pstimeIndex = e.currentTarget.dataset.index;\n\t\t\t// console.log(pstimeIndex)\n\t\t\tvar nowbid = that.nowbid;\n\t\t\tvar freightkey = allbuydata[nowbid].freightkey;\n\t\t\tvar freightList = allbuydata[nowbid].freightList;\n\t\t\tvar freight = freightList[freightkey];\n\t\t\tvar pstimeArr = freightList[freightkey].pstimeArr;\n\t\t\tvar choosepstime = pstimeArr[pstimeIndex];\n\t\t\tallbuydata[nowbid].pstimetext = choosepstime.title;\n\t\t\tallbuydata[nowbid].freight_time = choosepstime.value;\n\t\t\tthat.allbuydata = allbuydata\n\t\t\tthat.pstimeDialogShow = false;\n\t\t},\n    hidePstimeDialog: function () {\n      this.pstimeDialogShow = false\n    },\n\t\tchoosestore: function(e) {\n\t\t\tvar bid = e.currentTarget.dataset.bid;\n\t\t\tvar storekey = e.currentTarget.dataset.index;\n\t\t\tvar allbuydata = this.allbuydata;\n\t\t\tvar buydata = allbuydata[bid];\n\t\t\tvar freightkey = buydata.freightkey\n\t\t\tallbuydata[bid].freightList[freightkey].storekey = storekey\n\t\t\tthis.allbuydata = allbuydata;\n\t\t},\n    //提交并支付\n    topay: function (e) {\n      var that = this;\n      var addressid = this.address.id;\n      var linkman = this.linkman;\n      var tel = this.tel;\n\t\t\tvar allbuydata = that.allbuydata;\n\n      if(this.contact_require == 1 && (linkman.trim() == '' || tel.trim() == '')){\n        return app.error(\"请填写联系人信息\");\n      }\n      if(tel.trim()!= '' && !app.isPhone(tel)){\n        return app.error(\"请填写正确的手机号\");\n      }\n      var needaddress = that.needaddress;\n      if (needaddress == 0) addressid = 0;\n      if (needaddress == 1 && addressid == undefined) {\n        app.error('请选择收货地址');\n        return;\n      }\n      \n\t\t\tvar buydata = [];\n\t\t\tfor (var i in allbuydata) {\n\t\t\t\tvar freightkey = allbuydata[i].freightkey;\n\t\t\t\tif (allbuydata[i].freightList[freightkey].pstimeset == 1 && allbuydata[i].freight_time == '') {\n\t\t\t\t\tapp.error('请选择' + (allbuydata[i].freightList[freightkey].pstype == 1 ? '取货' : '配送') + '时间');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (allbuydata[i].freightList[freightkey].pstype == 1 || allbuydata[i].freightList[freightkey].pstype == 5) {\n\t\t\t\t\tvar storekey = allbuydata[i].freightList[freightkey].storekey;\n\t\t\t\t\tvar storeid = allbuydata[i].freightList[freightkey].storedata[storekey].id;\n\t\t\t\t} else {\n\t\t\t\t\tvar storeid = 0;\n\t\t\t\t}\n\t\t\t\tvar formdata_fields = allbuydata[i].freightList[freightkey].formdata;\n\t\t\t\tvar formdata = e.detail.value;\n\t\t\t\tvar newformdata = {};\n\t\t\t\tfor (var j = 0; j < formdata_fields.length;j++){\n\t\t\t\t\tvar thisfield = 'form'+allbuydata[i].bid + '_' + j;\n\t\t\t\t\tif (formdata_fields[j].val3 == 1 && (formdata[thisfield] === '' || formdata[thisfield] === undefined || formdata[thisfield].length==0)){\n\t\t\t\t\t\t\tapp.alert(formdata_fields[j].val1+' 必填');return;\n\t\t\t\t\t}\n\t\t\t\t\tif (formdata_fields[j].key == 'selector') {\n\t\t\t\t\t\t\tformdata[thisfield] = formdata_fields[j].val2[formdata[thisfield]]\n\t\t\t\t\t}\n\t\t\t\t\tnewformdata['form'+j] = formdata[thisfield];\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tvar buydatatemp = {\n\t\t\t\t\tbid: allbuydata[i].bid,\n\t\t\t\t\tprodata: allbuydata[i].prodatastr,\n\t\t\t\t\tfreight_id: allbuydata[i].freightList[freightkey].id,\n\t\t\t\t\tfreight_time: allbuydata[i].freight_time,\n\t\t\t\t\tstoreid: storeid,\n\t\t\t\t\tformdata:newformdata\n\t\t\t\t};\n\t\t\t\tbuydata.push(buydatatemp);\n\t\t\t}\n\t\t\tapp.showLoading('提交中');\n      app.post('ApiScoreshop/createOrder',{buydata:buydata,addressid:addressid,linkman:linkman,tel:tel,othermid:that.othermid}, function (res) {\n\t\t\t\tapp.showLoading(false);\n        if (res.status == 0) {\n          app.error(res.msg);\n          return;\n        }\n        app.goto('/pagesExt/pay/pay?id=' + res.payorderid,'redirectTo');\n      });\n    },\n\t\topenLocation:function(e){\n\t\t\tvar freightkey = e.currentTarget.dataset.freightkey;\n\t\t\tvar storekey = e.currentTarget.dataset.storekey;\n\t\t\tvar frightinfo = this.freightList[freightkey]\n\t\t\tvar storeinfo = frightinfo.storedata[storekey];\n\t\t\tvar latitude = parseFloat(storeinfo.latitude);\n\t\t\tvar longitude = parseFloat(storeinfo.longitude);\n\t\t\tvar address = storeinfo.name;\n\t\t\tuni.openLocation({\n\t\t\t latitude:latitude,\n\t\t\t longitude:longitude,\n\t\t\t name:address,\n\t\t\t scale: 13\n\t\t\t})\n\t\t},\n\t\topenMendian: function(e) {\n\t\t\tvar allbuydata = this.allbuydata\n\t\t\tvar bid = e.currentTarget.dataset.bid;\n\t\t\tvar freightkey = e.currentTarget.dataset.freightkey;\n\t\t\tvar storekey = e.currentTarget.dataset.storekey;\n\t\t\tvar frightinfo = allbuydata[bid].freightList[freightkey]\n\t\t\tvar storeinfo = frightinfo.storedata[storekey];\n\t\t\t// console.log(storeinfo)\n\t\t\tapp.goto('/pages/shop/mendian?id=' + storeinfo.id);\n\t\t},\n\t\teditorChooseImage: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar bid = e.currentTarget.dataset.bid;\n\t\t\tvar idx = e.currentTarget.dataset.idx;\n\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\n      var editorFormdata = that.allbuydata[bid].editorFormdata;;\n\t\t\tif(!editorFormdata) editorFormdata = [];\n\t\t\tapp.chooseImage(function(data){\n\t\t\t\teditorFormdata[idx] = data[0];\n\t\t\t\tconsole.log(editorFormdata)\n\t\t\t\tthat.editorFormdata = editorFormdata\n\t\t\t\tthat.allbuydata[bid].editorFormdata = that.editorFormdata;\n\t\t\t\tthat.test = Math.random();\n\t\t\t})\n\t\t},\n    //多图上传，一次最多选8个\n    editorChooseImages: function (e) {\n      var that = this;\n      var idx = e.currentTarget.dataset.idx;\n      var bid = e.currentTarget.dataset.bid;\n      var editorFormdata = that.allbuydata[bid].editorFormdata;;\n      if(!editorFormdata) editorFormdata = [];\n      var type = e.currentTarget.dataset.type;\n      app.chooseImage(function(data){\n        var pics = editorFormdata[idx];\n        if(!pics){\n          pics = [];\n        }\n        for(var i=0;i<data.length;i++){\n          pics.push(data[i]);\n        }\n        editorFormdata[idx] = pics;\n        that.allbuydata[bid].editorFormdata = editorFormdata\n        that.test = Math.random();\n      },8)\n    },\n\t\teditorBindPickerChange:function(e){\n\t\t\tvar that = this;\n\t\t\tvar bid = e.currentTarget.dataset.bid;\n\t\t\tvar idx = e.currentTarget.dataset.idx;\n\t\t\tvar val = e.detail.value;\n\t\t\tvar editorFormdata = that.allbuydata[bid].editorFormdata;\n\t\t\tif(!editorFormdata) editorFormdata = [];\n\t\t\teditorFormdata[idx] = val;\n\t\t\t// console.log(editorFormdata)\n\t\t\tthat.allbuydata[bid].editorFormdata = editorFormdata;\n\t\t\tthat.test = Math.random();\n\t\t},\n\t\tdoStoreShowAll:function(){\n\t\t\tthis.storeshowall = true;\n\t\t},\n\t\tremoveimg:function(e){\n\t\t\tvar that = this;\n\t\t\tvar bid = e.currentTarget.dataset.bid;\n\t\t\tvar idx = e.currentTarget.dataset.idx;\n      var editorFormdata = this.editorFormdata;\n      if(!editorFormdata) editorFormdata = [];\n      var type  = e.currentTarget.dataset.type;\n      var index = e.currentTarget.dataset.index;\n      if(type == 'pics'){\n        var pics = editorFormdata[idx]\n        pics.splice(index,1);\n        editorFormdata[idx] = pics;\n        that.allbuydata[bid].editorFormdata = editorFormdata\n        that.test = Math.random();\n      }else {\n        var pics = that.editorFormdata\n        pics.splice(idx,1);\n        that.editorFormdata = pics;\n        that.allbuydata[bid].editorFormdata = that.editorFormdata;\n      }\n\t\t},\n  }\n}\n</script>\n<style>\n.address-add{ width:94%;margin:20rpx 3%;background:#fff;border-radius:20rpx;padding: 20rpx 3%;min-height:140rpx;}\n.address-add .f1{margin-right:20rpx}\n.address-add .f1 .img{ width: 66rpx; height: 66rpx; }\n.address-add .f2{ color: #666; }\n.address-add .f3{ width: 26rpx; height: 26rpx;}\n\n.linkitem{width: 100%;padding:1px 0;background: #fff;display:flex;align-items:center}\n.linkitem .f1{width:160rpx;color:#111111}\n.linkitem .input{height:50rpx;padding-left:10rpx;color:#222222;font-weight:bold;font-size:28rpx;flex:1}\n\n.buydata{width:94%;margin:0 3%;background:#fff;margin-bottom:20rpx;border-radius:20rpx;}\n\n.btitle{width:100%;padding:20rpx 20rpx;display:flex;align-items:center;color:#111111;font-weight:bold;font-size:30rpx}\n.btitle .img{width:34rpx;height:34rpx;margin-right:10rpx}\n\n.bcontent{width:100%;padding:0 20rpx}\n\n.product{width:100%;border-bottom:1px solid #f4f4f4} \n.product .item{width:100%; padding:20rpx 0;background:#fff;border-bottom:1px #ededed dashed;}\n.product .item:last-child{border:none}\n.product .info{padding-left:20rpx;}\n.product .info .f1{color: #222222;font-weight:bold;font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.product .info .f2{color: #999999; font-size:24rpx}\n.product .info .f3{color: #FF4C4C; font-size:28rpx;display:flex;align-items:center;margin-top:10rpx}\n.product .img{ width:140rpx;height:140rpx}\n.collage_icon{ color:#fe7203;border:1px solid #feccaa;display:flex;align-items:center;font-size:20rpx;padding:0 6rpx;margin-left:6rpx}\n\n.freight{width:100%;padding:20rpx 0;background:#fff;display:flex;flex-direction:column;}\n.freight .f1{color:#333;margin-bottom:10rpx}\n.freight .f2{color: #111111;text-align:right;flex:1}\n.freight .f3{width: 24rpx;height:28rpx;}\n.freighttips{color:red;font-size:24rpx;}\n\n.freight-ul{width:100%;display:flex;}\n.freight-li{flex-shrink:0;display:flex;background:#F5F6F8;border-radius:24rpx;color:#6C737F;font-size:24rpx;text-align: center;height:48rpx; line-height:48rpx;padding:0 28rpx;margin:12rpx 10rpx 12rpx 0}\n\n\n.price{width:100%;padding:20rpx 0;background:#fff;display:flex;align-items:center}\n.price .f1{color:#333}\n.price .f2{ color:#111;font-weight:bold;text-align:right;flex:1}\n.price .f3{width: 24rpx;height:24rpx;}\n\n.scoredk{width:94%;margin:0 3%;margin-bottom:20rpx;border-radius:20rpx;padding:24rpx 20rpx; background: #fff;display:flex;align-items:center}\n.scoredk .f1{color:#333333}\n.scoredk .f2{ color: #999999;text-align:right;flex:1}\n\n.remark{width: 100%;padding:16rpx 0;background: #fff;display:flex;align-items:center}\n.remark .f1{color:#333;width:200rpx}\n.remark input{ border:0px solid #eee;height:70rpx;padding-left:10rpx;text-align:right}\n\n.footer {width: 96%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;padding: 0 2%;display: flex;align-items: center;z-index: 8;box-sizing:content-box}\n.footer .f1 {height:110rpx;line-height:110rpx;color: #2a2a2a;font-size: 30rpx;}\n.footer .f1 .text{color: #e94745;font-size: 32rpx;}\n.footer .op{width: 200rpx;height:80rpx;line-height:80rpx;color: #fff;text-align: center;font-size: 30rpx;border-radius:44rpx}\n\n.storeitem{width: 100%;padding:20rpx 0;display:flex;flex-direction:column;color:#333}\n.storeitem .panel{width: 100%;height:60rpx;line-height:60rpx;font-size:28rpx;color:#333;margin-bottom:10rpx;display:flex}\n.storeitem .panel .f1{color:#333}\n.storeitem .panel .f2{ color:#111;font-weight:bold;text-align:right;flex:1}\n.storeitem .radio-item{display:flex;width:100%;color:#000;align-items: center;background:#fff;border-bottom:0 solid #eee;padding:8rpx 20rpx;}\n.storeitem .radio-item:last-child{border:0}\n.storeitem .radio-item .f1{color:#666;flex:1}\n.storeitem .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-left:30rpx}\n.storeitem .radio .radio-img{width:100%;height:100%}\n\n.pstime-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\n.pstime-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\n.pstime-item .radio .radio-img{width:100%;height:100%}\n\n.cuxiao-desc{width:100%}\n.cuxiao-item{display: flex;padding:0 40rpx 20rpx 40rpx;}\n.cuxiao-item .type-name{font-size:28rpx; color: #49aa34;margin-bottom: 10rpx;flex:1}\n.cuxiao-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\n.cuxiao-item .radio .radio-img{width:100%;height:100%}\n\n.form-item {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center;justify-content:space-between}\n.form-item .label {color: #333;width: 200rpx;flex-shrink:0}\n.form-item .radio{transform:scale(.7);}\n.form-item .checkbox{transform:scale(.7);}\n.form-item .input {border:0px solid #eee;height: 70rpx;padding-left: 10rpx;font-size:30rpx;text-align: right;flex:1}\n.form-item .textarea{height:140rpx;line-height:40rpx;overflow: hidden;flex:1;font-size:30rpx;border:1px solid #eee;border-radius:2px;padding:8rpx}\n.form-item .radio-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\n.form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\n.form-item .radio2{display:flex;align-items:center;}\n.form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}\n.form-item .checkbox-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\n.form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\n.form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}\n.form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}\n.form-item .picker{height: 70rpx;line-height:70rpx;flex:1;text-align:right}\n\n.form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\n.form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}\n.form-imgbox-close .image{width:100%;height:100%}\n.form-imgbox-img{display: block;width:180rpx;height:180rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\n.form-imgbox-img>.image{width: 100%;height: 100%;}\n.form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\n.form-uploadbtn{position:relative;height:180rpx;width:180rpx;margin-right: 16rpx;margin-bottom:10rpx;}\n.storeviewmore{width:100%;text-align:center;color:#889;height:40rpx;line-height:40rpx;margin-top:10rpx}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213322426\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}