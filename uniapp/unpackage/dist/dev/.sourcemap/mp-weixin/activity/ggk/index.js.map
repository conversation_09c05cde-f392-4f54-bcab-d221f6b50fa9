{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/ggk/index.vue?8aa5", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/ggk/index.vue?e54b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/ggk/index.vue?0b2e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/ggk/index.vue?8097", "uni-app:///activity/ggk/index.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/ggk/index.vue?7be8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/ggk/index.vue?7718", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/ggk/index.vue?7f2c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/ggk/index.vue?ec1e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "isStart", "name", "jxmc", "detect", "error", "info", "member", "jxarr", "remaindaytimes", "remaintimes", "zj<PERSON>", "register", "award_name", "jxshow", "showmaskrule", "latitude", "longitude", "r", "lastX", "lastY", "minX", "minY", "maxX", "maxY", "canvasWidth", "canvasHeight", "isScroll", "award", "jx", "windowWidth", "windowHeight", "backimg", "backcolor", "showqrpopup", "showdoneqrcode", "isinfo", "maskshow", "formdata", "picker_tmer", "picker_date", "picker_region", "items", "showggk", "onLoad", "onReady", "that", "onPullDownRefresh", "onShareAppMessage", "title", "desc", "link", "pic", "callback", "onShareTimeline", "imageUrl", "query", "methods", "getdata", "app", "id", "res", "uni", "getcode", "choujiang_id", "code", "showqrmodal", "hideqrmodal", "sharecallback", "hid", "setTimeout", "changemaskrule", "init", "console", "scratch", "onStart", "drawRect", "clearArc", "ctx", "a", "touchStart", "touchMove", "changemaskshow", "touchEnd", "op", "timeStamp", "nonceStr", "package", "signType", "paySign", "success", "fail", "complete", "formsub", "subdata", "formcontent"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;AACxB;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yRAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3EA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwKv1B;AACA;AACA;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACAC;IACAA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAP;MACA;IACA;EACA;EACAQ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACAL;MACAC;MACAC;MACAC;MACAC;QACAP;MACA;IACA;IACA;IACA;MACAG;MACAM;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAZ;MACAa;QACAC;MACA;QACAd;QACA;UACAa;UACA;QACA;QACAE;QACAf;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAA;QACA;QACAgB;UACAb;QACA;QACA;UACAH;QACA;QACAA;QACA;UACAa;YACA;YACA;YACAb;YACAA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAA;UACAG;UACAC;UACAC;UACAC;UACAC;YACAP;UACA;QACA;MACA;IACA;IACAiB;MACA;MACAJ;QACAK;QACAC;MACA;QACA;UACAN;QACA;QACAb;MACA;IACA;IAEAoB;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAT;QACAU;MACA;QACA;UACAC;YACAxB;UACA;QACA;QAAA;MAEA;IACA;IACAyB;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA1B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA2B;MACA3B;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACA;MACA4B;MACAA;MACAA;MACAD;;MAEA;MACA;MACA3B;MACAA;MACAA;MACA;IACA;;IACA6B;MACA;MACA;MACA;MACA;MACA;MACAL;QACAxB;MACA;IACA;IACA8B;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;QACAC;QACAC;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;UACArB;UAAA;QACA;QACA;UACAA;UAAA;QACA;QACAb;QACAA;QACA;MACA;MACA;MACA;QACAa;MACA;IACA;IACAsB;MACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACApC;MACAA;IACA;IACAqC;MAGA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;UACArC;UACAa;YACAC;YACAwB;YACAnE;YACAD;UACA;YACA8B;YACA;cACAa;cACAb;YACA;cACAA;cACAA;cACAA;cACAwB;gBACAxB;gBACAA;gBACAA;cACA;cAEA;gBAEAgB;kBACAuB;kBACA;kBACAC;kBACA;kBACAC;kBACA;kBACAC;kBACA;kBACAC;kBACA;kBACAC;oBACAjB;kBACA;kBACAkB;oBACAlB;kBACA;kBACAmB;oBACAnB;kBACA;gBACA;cAEA;YACA;UACA;QACA;MACA;IACA;IACAoB;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;UACAlC;UACA;QACA;QACA;UACA;YACAmC;UACA;YACAA;UACA;QACA;QACA;UACAA;QACA;QACA;QACAxD;MACA;MACAqB;QACAU;QACA0B;MACA;QACA;UACApC;QACA;UACAb;UACAa;UACAb;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChlBA;AAAA;AAAA;AAAA;AAAwsC,CAAgB,wnCAAG,EAAC,C;;;;;;;;;;;ACA5tC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/ggk/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/ggk/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0890455c&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=0890455c&scoped=true&lang=css&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0890455c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/ggk/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=0890455c&scoped=true&\"", "var components\ntry {\n  components = {\n    uniDataPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-data-picker/uni-data-picker\" */ \"@/components/uni-data-picker/uni-data-picker.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload && _vm.info.use_type == 1 && _vm.info.usescore > 0\n      ? _vm.t(\"积分\")\n      : null\n  var m1 =\n    _vm.isload && _vm.info.use_type == 1 && _vm.info.usescore > 0\n      ? _vm.t(\"积分\")\n      : null\n  var m2 =\n    _vm.isload && _vm.info.use_type == 2 && _vm.info.usemoney > 0\n      ? _vm.t(\"余额\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\" :style=\"{ background:backcolor==''?'#c40004':backcolor}\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<img :src=\"backimg\" v-if=\"backimg!=''\" class=\"back_img\" mode=\"widthFix\" alt=\"\" />\r\n\t\t\t<view class=\"body\">\r\n\t\t\t\t<view class=\"wrap\">\r\n\t\t\t\t\t<view class=\"header clearfix\">\r\n\t\t\t\t\t\t<view class=\"rule\" @tap=\"changemaskrule\">活动规则</view>\r\n\t\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'myprize?hid=' + info.id\" class=\"my\">我的奖品</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"title\" :style=\"'background-image:url(' + info.banner + ');background-size:100% 100%;'\">\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"scratch-bg\">\r\n\t\t\t\t\t\t<view style=\"position:relative\">\r\n\t\t\t\t\t\t\t<image class=\"scratch-bg-1\" :src=\"pre_url+'/static/img/ggk/scratch_bg.png'\"></image>\r\n\t\t\t\t\t\t\t<image class=\"scratch-bg-2\" id=\"frame\" :src=\"pre_url+'/static/img/ggk/scratch_kuang.png'\">\r\n\t\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t\t<view class=\"scratch-award\">\r\n\t\t\t\t\t\t\t\t<view class=\"scratch-award-a\">\r\n\t\t\t\t\t\t\t\t\t<block v-if=\"isStart && !showmaskrule && !jxshow\"><canvas @touchend=\"touchEnd\"\r\n\t\t\t\t\t\t\t\t\t\t\t@touchmove=\"touchMove\" @touchstart=\"touchStart\" canvasId=\"scratch\"\r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"scratch-canvas\" :disableScroll=\"isScroll\" id=\"scratch\"\r\n\t\t\t\t\t\t\t\t\t\t\tstyle=\"position:absolute;left:0;z-index:888\"></canvas></block>\r\n\t\t\t\t\t\t\t\t\t<view class=\"scratch-bg-text\">\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"award_name\"><text class=\"scratch-text-1\">{{jxmc||'刮开图层'}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"(remaindaytimes > 0 && award_name)\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view @tap=\"onStart\" class=\"scratch-bg-text-2\">再刮一次</view>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"(remaindaytimes <= 0 && award_name)\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"scratch-bg-text-3\">再刮一次</view>\r\n\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"border\" v-if=\"info.use_type != 2\">您今日还有 <text id=\"change\">{{remaindaytimes}}</text>\r\n\t\t\t\t\t\t次抽奖机会</view>\r\n\t\t\t\t\t<view class=\"border2\" v-if=\"info.use_type == 1 && info.usescore>0\"><text\r\n\t\t\t\t\t\t\tv-if=\"!info.is_tr\">每次</text><text v-else>本次</text>抽奖将消耗 <text>{{info.usescore}}</text>\r\n\t\t\t\t\t\t{{t('积分')}}，您共有 <text id=\"myscore\">{{member.score}}</text> {{t('积分')}}</view>\r\n\t\t\t\t\t<view class=\"border2\" v-if=\"info.use_type == 2 && info.usemoney>0\">每次抽奖将消耗\r\n\t\t\t\t\t\t<text>{{t('余额')}}</text>{{info.usemoney}}元 ，您共有 <text id=\"mymoney\">{{member.money}}</text> 元\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap=\"goto\" data-url='/pages/index/index' style=\"text-align:center;margin-top: 40rpx; line-height: 60rpx;color: #fff;\"><text>返回首页</text></view>\r\n\t\t\t\t\t<!--滚动信息-->\r\n\t\t\t\t\t<view class=\"scroll\">\r\n\t\t\t\t\t\t<view class=\"p\"\r\n\t\t\t\t\t\t\t:style=\"'background-image:url('+pre_url+'/static/img/dzp/list.png);background-size:100% 100%;'\">\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"sideBox\">\r\n\t\t\t\t\t\t\t<swiper class=\"bd\" autoplay=\"true\" current=\"0\" vertical=\"true\" circular=\"true\">\r\n\t\t\t\t\t\t\t\t<swiper-item v-for=\"(item, index) in zjlist\" :key=\"index\" class=\"sitem\" v-if=\"index%2==0\">\r\n\t\t\t\t\t\t\t\t\t<view>恭喜{{item.nickname}} 获得<text class=\"info\">{{item.jxmc}}</text></view>\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"zjlist[index+1]\">恭喜{{zjlist[index+1].nickname}} 获得<text class=\"info\">{{zjlist[index+1].jxmc}}</text></view>\r\n\t\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t\t</swiper>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view id=\"mask-rule\" v-if=\"showmaskrule\">\r\n\t\t\t\t\t\t<view class=\"box-rule\">\r\n\t\t\t\t\t\t\t<view class=\"h2\">活动规则说明</view>\r\n\t\t\t\t\t\t\t<view id=\"close-rule\" @tap=\"changemaskrule\"\r\n\t\t\t\t\t\t\t\t:style=\"'background-image:url('+pre_url+'/static/img/dzp/close.png);background-size:100%'\">\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"con\">\r\n\t\t\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t\t\t<text decode=\"true\" space=\"true\">{{info.guize}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view v-if=\"showqrpopup\" class=\"popup__container\">\r\n\t\t\t\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hideqrmodal\"></view>\r\n\t\t\t\t\t\t<view class=\"popup__modal\" style=\"\">\r\n\t\t\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t\t\t<view><image :data-url=\"info.qrcode\" :src=\"info.qrcode\" @tap=\"previewImage\" ></image></view>\r\n\t\t\t\t\t\t\t\t<view class=\"txt\" v-if=\"info.qrcode_tip\">{{info.qrcode_tip}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view id=\"mask-rule1\" v-if=\"maskshow && !formdata\">\r\n\t\t\t\t\t\t<view class=\"box-rule\" style=\"height:640rpx\">\r\n\t\t\t\t\t\t\t<view class=\"h2\">请填写兑奖信息</view>\r\n\t\t\t\t\t\t\t<view id=\"close-rule1\" :style=\"'background: no-repeat center / contain;background-image: url('+pre_url+'/static/img/dzp/close.png);'\" @tap=\"changemaskshow\"></view>\r\n\t\t\t\t\t\t\t<view class=\"con\">\r\n\t\t\t\t\t\t\t\t<form class @submit=\"formsub\">\r\n\t\t\t\t\t\t\t\t<view class=\"pay-form\" style=\"margin-top:0.18rem\">\r\n\t\t\t\t\t\t\t\t\t<view v-for=\"(item, idx) in info.formcontent\" :key=\"idx\" class=\"item flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.val1}}：</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f2 flex flex1\">\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='input'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" :name=\"'form' + idx\" class=\"input\" :placeholder=\"item.val2\"></input>\r\n\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='textarea'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<textarea :name=\"'form' + idx\" class=\"textarea\" :placeholder=\"item.val2\"></textarea>\r\n\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='radio'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<radio-group class=\"radio-group\" :name=\"'form' + idx\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<label v-for=\"(item1, index) in item.val2\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<radio :value=\"item1\"></radio>{{item1}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='checkbox'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<checkbox-group :name=\"'form' + idx\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<label v-for=\"(item1, index) in item.val2\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<checkbox :value=\"item1\" class=\"xyy-zu\"></checkbox>{{item1}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='switch'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<switch class=\"xyy-zu\" value=\"1\" :name=\"'form' + idx\"></switch>\r\n\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='selector'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<picker mode=\"selector\" :name=\"'form' + idx\" class=\"xyy-pic\" :range=\"item.val2\" @change=\"selector_editorBindPickerChange\" :data-idx=\"idx\" data-tplindex=\"0\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"picker\" v-if=\"item.val2[selectIndex]\"> {{item.val2[selectIndex]}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view v-else>请选择</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='time'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<picker mode=\"time\" :name=\"'form' + idx\" class=\"xyy-pic\" @change=\"time_editorBindPickerChange\" :data-idx=\"idx\" data-tplindex=\"0\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"picker\" v-if=\"picker_tmer\">{{picker_tmer}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view v-else>选择时间</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='date'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<picker mode=\"date\" :name=\"'form' + idx\" class=\"xyy-pic\" @change=\"date_editorBindPickerChange\" :data-idx=\"idx\" data-tplindex=\"0\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"picker\" v-if=\"picker_date\"> {{picker_date}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view v-else>选择日期</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='region'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<uni-data-picker style=\"color: #333;\" class=\"flex1\" :localdata=\"items\" popup-title=\"请选择省市区\"  @change=\"region_editorBindPickerChange\"\t:data-idx=\"idx\" data-tplindex=\"0\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" :name=\"'form'+idx\" :value=\"picker_region\" placeholder=\"请选择省市区\" placeholder-style=\"color:#fff;font-size:28rpx\" style=\"border: none;font-size:28rpx;padding: 0rpx;color:#fff;\"/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</uni-data-picker>\r\n\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view >\r\n\t\t\t\t\t\t\t\t\t\t<button class=\"subbtn\" form-type=\"submit\">确 定</button>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</form>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t\t<wxxieyi></wxxieyi>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar dot_inter, bool;\r\n\tvar interval;\r\n\tvar app = getApp();\r\n\tvar windowWidth = uni.getSystemInfoSync().windowWidth;\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt: {},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex: -1,\r\n\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\tisStart: 1,\r\n\t\t\t\tname: \"\",\r\n\t\t\t\tjxmc: \"\",\r\n\t\t\t\tdetect: 1,\r\n\t\t\t\terror: \"\",\r\n\t\t\t\tinfo: {},\r\n\t\t\t\tmember: {},\r\n\t\t\t\tjxarr: [],\r\n\t\t\t\tremaindaytimes: 0,\r\n\t\t\t\tremaintimes: 0,\r\n\t\t\t\tzjlist: [],\r\n\t\t\t\tregister: 1,\r\n\t\t\t\taward_name: 0,\r\n\t\t\t\tjxshow: false,\r\n\t\t\t\tshowmaskrule: false,\r\n\t\t\t\tlatitude: \"\",\r\n\t\t\t\tlongitude: \"\",\r\n\t\t\t\tr: 0,\r\n\t\t\t\tlastX: \"\",\r\n\t\t\t\tlastY: \"\",\r\n\t\t\t\tminX: \"\",\r\n\t\t\t\tminY: \"\",\r\n\t\t\t\tmaxX: \"\",\r\n\t\t\t\tmaxY: \"\",\r\n\t\t\t\tcanvasWidth: \"\",\r\n\t\t\t\tcanvasHeight: \"\",\r\n\t\t\t\tisScroll: false,\r\n\t\t\t\taward: 0,\r\n\t\t\t\tjx: \"\",\r\n\t\t\t\twindowWidth: 0,\r\n\t\t\t\twindowHeight: 0,\r\n\t\t\t\tbackimg: \"\",\r\n\t\t\t\tbackcolor: \"\",\r\n\t\t\t\tshowqrpopup:false,\r\n\t\t\t\tshowdoneqrcode:false,\r\n\t\t\t\tisinfo:false,\r\n\t\t\t\tmaskshow: false,\r\n\t\t\t\tformdata: \"\",\r\n\t\t\t\tpicker_tmer:'',\r\n\t\t\t\tpicker_date:'',\r\n\t\t\t\tpicker_region:'',\r\n\t\t\t\titems: [],\r\n\t\t\t\tshowggk:true\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad: function(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tif(this.opt.code){\r\n\t\t\t\tthis.getcode();\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReady: function() {\r\n\t\t\tvar that = this;\r\n\t\t\tvar res = uni.getSystemInfoSync();\r\n\t\t\tthat.windowWidth = res.windowWidth;\r\n\t\t\tthat.windowHeight = res.windowHeight;\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonShareAppMessage: function() {\r\n\t\t\tvar that = this;\r\n\t\t\tvar title = that.info.name;\r\n\t\t\tif (that.info.sharetitle) title = that.info.sharetitle;\r\n\t\t\tvar sharepic = that.info.sharepic ? that.info.sharepic : '';\r\n\t\t\tvar sharelink = that.info.sharelink ? that.info.sharelink : '';\r\n\t\t\tvar sharedesc = that.info.sharedesc ? that.info.sharedesc : '';\r\n\t\t\treturn this._sharewx({\r\n\t\t\t\ttitle: title,\r\n\t\t\t\tdesc: sharedesc,\r\n\t\t\t\tlink: sharelink,\r\n\t\t\t\tpic: sharepic,\r\n\t\t\t\tcallback: function() {\r\n\t\t\t\t\tthat.sharecallback();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tonShareTimeline: function() {\r\n\t\t\tvar that = this;\r\n\t\t\tvar title = that.info.name;\r\n\t\t\tif (that.info.sharetitle) title = that.info.sharetitle;\r\n\t\t\tvar sharepic = that.info.sharepic ? that.info.sharepic : '';\r\n\t\t\tvar sharelink = that.info.sharelink ? that.info.sharelink : '';\r\n\t\t\tvar sharedesc = that.info.sharedesc ? that.info.sharedesc : '';\r\n\t\t\tvar sharewxdata = this._sharewx({\r\n\t\t\t\ttitle: title,\r\n\t\t\t\tdesc: sharedesc,\r\n\t\t\t\tlink: sharelink,\r\n\t\t\t\tpic: sharepic,\r\n\t\t\t\tcallback: function() {\r\n\t\t\t\t\tthat.sharecallback();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tvar query = (sharewxdata.path).split('?')[1]+'&seetype=circle';\r\n\t\t\treturn {\r\n\t\t\t\ttitle: sharewxdata.title,\r\n\t\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\t\tquery: query\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetdata: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar id = that.opt.id;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.get('ApiChoujiang/index', {\r\n\t\t\t\t\tid: id\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tres.info.formcontent = JSON.parse(res.info.formcontent);\r\n\t\t\t\t\tthat.info = res.info;\r\n\t\t\t\t\tthat.jxarr = res.jxarr;\r\n\t\t\t\t\tthat.member = res.member;\r\n\t\t\t\t\tthat.remaindaytimes = res.remaindaytimes;\r\n\t\t\t\t\tthat.remaintimes = res.remaintimes;\r\n\t\t\t\t\tthat.zjlist = res.zjlist;\r\n\t\t\t\t\tthat.backimg = res.info.bgpic;\r\n\t\t\t\t\tthat.backcolor = res.info.bgcolor;\r\n\t\t\t\t\tthat.isinfo = res.isinfo\r\n\t\t\t\t\tif(\tthat.isinfo && res.record && res.record.formdata){\r\n\t\t\t\t\t\t\tthat.formdata = res.record.formdata\r\n\t\t\t\t\t}\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: res.info.name\r\n\t\t\t\t\t});\r\n\t\t\t\t\tif(that.info.qrcode){\r\n\t\t\t\t\t\tthat.showqrpopup = true\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.onStart();\r\n\t\t\t\t\tif (that.info.fanwei == 1) {\r\n\t\t\t\t\t\tapp.getLocation(function(res) {\r\n\t\t\t\t\t\t\tvar latitude = res.latitude;\r\n\t\t\t\t\t\t\tvar longitude = res.longitude;\r\n\t\t\t\t\t\t\tthat.latitude = latitude;\r\n\t\t\t\t\t\t\tthat.longitude = longitude;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar title = that.info.name;\r\n\t\t\t\t\tif (that.info.sharetitle) title = that.info.sharetitle;\r\n\t\t\t\t\tvar sharepic = that.info.sharepic ? that.info.sharepic : '';\r\n\t\t\t\t\tvar sharelink = that.info.sharelink ? that.info.sharelink : '';\r\n\t\t\t\t\tvar sharedesc = that.info.sharedesc ? that.info.sharedesc : '';\r\n\t\t\t\t\tthat.loaded({\r\n\t\t\t\t\t\ttitle: title,\r\n\t\t\t\t\t\tdesc: sharedesc,\r\n\t\t\t\t\t\tlink: sharelink,\r\n\t\t\t\t\t\tpic: sharepic,\r\n\t\t\t\t\t\tcallback: function() {\r\n\t\t\t\t\t\t\tthat.sharecallback();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetcode: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tapp.get('ApiChoujiang/qrcode_addtimes', {\r\n\t\t\t\t\tchoujiang_id: that.opt.id,\r\n\t\t\t\t\tcode: that.opt.code,\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\t\tif (res.status != 1 ) {\r\n\t\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\tthat.getdata();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tshowqrmodal:function(){\r\n\t\t\t\tif(this.info.qrcode){\r\n\t\t\t\t\tthis.showqrpopup = true;\r\n\t\t\t\t}\r\n\t\t\t\tthis.showdoneqrcode = true;\r\n\t\t\t},\r\n\t\t\thideqrmodal:function(){\r\n\t\t\t\tthis.showqrpopup = false;\r\n\t\t\t},\r\n\t\t\tsharecallback: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tapp.post(\"ApiChoujiang/share\", {\r\n\t\t\t\t\thid: that.info.id\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t} else if (res.status == 0) { //dialog(res.msg);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tchangemaskrule: function() {\r\n\t\t\t\tthis.showmaskrule = !this.showmaskrule;\r\n\t\t\t},\r\n\t\t\tinit: function() {\r\n\t\t\t\tvar windowWidth = this.windowWidth;\r\n\t\t\t\tvar windowHeight = this.windowHeight;\r\n\t\t\t\t//var query = uni.createSelectorQuery();\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.award_name = 0;\r\n\t\t\t\t//query.select(\"#frame\").boundingClientRect();\r\n\t\t\t\t//query.exec(function (res) {\r\n\t\t\t\t//\tconsole.log(res)\r\n\t\t\t\t//var width = res[0].width;\r\n\t\t\t\t//var height = res[0].height;\r\n\t\t\t\tvar width = windowWidth / 750 * 600;\r\n\t\t\t\tvar height = windowWidth / 750 * 320;\r\n\t\t\t\tconsole.log(height)\r\n\t\t\t\tthat.r = 16;\r\n\t\t\t\tthat.lastX = \"\";\r\n\t\t\t\tthat.lastY = \"\";\r\n\t\t\t\tthat.minX = \"\";\r\n\t\t\t\tthat.minY = \"\";\r\n\t\t\t\tthat.maxX = \"\";\r\n\t\t\t\tthat.maxY = \"\";\r\n\t\t\t\tthat.canvasWidth = width;\r\n\t\t\t\tthat.canvasHeight = height;\r\n\t\t\t\tvar scratch = uni.createCanvasContext(\"scratch\");\r\n\t\t\t\tscratch.setFillStyle('#D3D3D3');\r\n\t\t\t\tscratch.fillRect(0, 0, width, height);\r\n\t\t\t\tscratch.draw();\r\n\t\t\t\tconsole.log(scratch);\r\n\r\n\t\t\t\t//scratch.drawImage(\"@/static/img/scratch_hide_2.png\", 0, 0, width, height);\r\n\t\t\t\t//scratch.draw();\r\n\t\t\t\tthat.ctx = scratch;\r\n\t\t\t\tthat.isStart = 1;\r\n\t\t\t\tthat.isScroll = true;\r\n\t\t\t\t//});\r\n\t\t\t},\r\n\t\t\tonStart: function() {\r\n\t\t\t\tthis.jxmc = '';\r\n\t\t\t\tthis.isStart = 1;\r\n\t\t\t\tthis.award = 0;\r\n\t\t\t\tthis.award_name = 0;\r\n\t\t\t\tvar that = this\r\n\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\tthat.init();\r\n\t\t\t\t}, 100)\r\n\t\t\t},\r\n\t\t\tdrawRect: function(t, e) {\r\n\t\t\t\tvar a = this.r / 2;\r\n\t\t\t\tvar i = 0 < t - a ? t - a : 0;\r\n\t\t\t\tvar s = 0 < e - a ? e - a : 0;\r\n\t\t\t\tif (\"\" !== this.minX) {\r\n\t\t\t\t\tthis.minX = this.minX > i ? i : this.minX;\r\n\t\t\t\t\tthis.minY = this.minY > s ? s : this.minY;\r\n\t\t\t\t\tthis.maxX = this.maxX > i ? this.maxX : i;\r\n\t\t\t\t\tthis.maxY = this.maxY > s ? this.maxY : s;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.minX = i;\r\n\t\t\t\t\tthis.minY = s;\r\n\t\t\t\t\tthis.maxX = i;\r\n\t\t\t\t\tthis.maxY = s;\r\n\t\t\t\t}\r\n\t\t\t\tthis.lastX = i;\r\n\t\t\t\tthis.lastY = s;\r\n\t\t\t\t[i, s, 2 * a];\r\n\t\t\t},\r\n\t\t\tclearArc: function(x, y, a) {\r\n\t\t\t\tvar r = this.r;\r\n\t\t\t\tvar ctx = this.ctx;\r\n\t\t\t\tvar x2 = r - a;\r\n\t\t\t\tvar y2 = Math.sqrt(r * r - x2 * x2);\r\n\t\t\t\tvar c = x - x2;\r\n\t\t\t\tvar n = y - y2;\r\n\t\t\t\tvar d = 2 * x2;\r\n\t\t\t\tvar p = 2 * y2;\r\n\r\n\t\t\t\tif (a <= r) {\r\n\t\t\t\t\tctx.clearRect(c, n, d, p);\r\n\t\t\t\t\ta += 1;\r\n\t\t\t\t\tthis.clearArc(x, y, a);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttouchStart: function(t) {\r\n\t\t\t\tvar that=this\r\n\t\t\t\tif(that.isinfo){\r\n\t\t\t\t\tif(that.info.isbegin){\r\n\t\t\t\t\t\t\tapp.error('活动未开始');return;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.info.isend){\r\n\t\t\t\t\t\t\tapp.error('活动已结束');return;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.showggk=false\r\n\t\t\t\t\tthat.maskshow = true;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.award_name = 1\r\n\t\t\t\tif (this.isStart && this.error) {\r\n\t\t\t\t\tapp.alert(this.error);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttouchMove: function(t) {\r\n\t\t\t\tif (this.isStart && !this.error) {\r\n\t\t\t\t\tthis.drawRect(t.touches[0].x, t.touches[0].y);\r\n\t\t\t\t\tthis.clearArc(t.touches[0].x, t.touches[0].y, 1);\r\n\t\t\t\t\tthis.ctx.draw(true);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tchangemaskshow: function () {\r\n\t\t\t  var that = this;\r\n\t\t\t  that.maskshow = !that.maskshow;\r\n\t\t\t\tthat.showggk=true\r\n\t\t\t},\r\n\t\t\ttouchEnd: function(t) {\t\r\n\r\n\t\t\t\t\r\n\t\t\t\tif (this.isStart && !this.error) {\r\n\t\t\t\t\tvar that = this;\r\n\t\t\t\t\tvar canvasWidth = this.canvasWidth;\r\n\t\t\t\t\tvar canvasHeight = this.canvasHeight;\r\n\t\t\t\t\tvar minX = this.minX;\r\n\t\t\t\t\tvar minY = this.minY;\r\n\t\t\t\t\tvar maxX = this.maxX;\r\n\t\t\t\t\tvar maxY = this.maxY;\r\n\r\n\t\t\t\t\tif (0.4 * canvasWidth < maxX - minX && 0.4 * canvasHeight < maxY - minY && this.detect) {\r\n\t\t\t\t\t\tthat.detect = 0;\r\n\t\t\t\t\t\tapp.post('ApiChoujiang/index', {\r\n\t\t\t\t\t\t\tid: that.info.id,\r\n\t\t\t\t\t\t\top: 'getjx',\r\n\t\t\t\t\t\t\tlongitude: that.longitude,\r\n\t\t\t\t\t\t\tlatitude: that.latitude\r\n\t\t\t\t\t\t}, function(res) {\r\n\t\t\t\t\t\t\tthat.info = res.info;\r\n\t\t\t\t\t\t\tif (res.status != 1) {\r\n\t\t\t\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\t\t\t\tthat.onStart();\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthat.jxmc = res.jxmc;\r\n\t\t\t\t\t\t\t\tthat.jx = res.jx;\r\n\t\t\t\t\t\t\t\tthat.remaindaytimes = that.remaindaytimes - 1;\r\n\t\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\t\tthat.detect = 1;\r\n\t\t\t\t\t\t\t\t\tthat.isStart = 0;\r\n\t\t\t\t\t\t\t\t\tthat.isScroll = true;\r\n\t\t\t\t\t\t\t\t}, 1000);\r\n\r\n\t\t\t\t\t\t\t\tif (res.jxtp == 2 && res.spdata) {\r\n\t\t\t\t\t\t\t\t\t//#ifdef MP-WEIXIN\r\n\t\t\t\t\t\t\t\t\tuni.sendBizRedPacket({\r\n\t\t\t\t\t\t\t\t\t\ttimeStamp: res.spdata.timeStamp,\r\n\t\t\t\t\t\t\t\t\t\t// 支付签名时间戳，\r\n\t\t\t\t\t\t\t\t\t\tnonceStr: res.spdata.nonceStr,\r\n\t\t\t\t\t\t\t\t\t\t// 支付签名随机串，不长于 32 位\r\n\t\t\t\t\t\t\t\t\t\tpackage: res.spdata.package,\r\n\t\t\t\t\t\t\t\t\t\t//扩展字段，由商户传入\r\n\t\t\t\t\t\t\t\t\t\tsignType: res.spdata.signType,\r\n\t\t\t\t\t\t\t\t\t\t// 签名方式，\r\n\t\t\t\t\t\t\t\t\t\tpaySign: res.spdata.paySign,\r\n\t\t\t\t\t\t\t\t\t\t// 支付签名\r\n\t\t\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tcomplete: function(res) {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t//#endif\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tformsub: function (e) {\r\n\t\t\t  var that = this;\r\n\t\t\t  var subdata = e.detail.value;\r\n\t\t\t  var formcontent = that.info.formcontent;\r\n\t\t\t  var record = that.record;\r\n\t\t\t  var formdata = {};\r\n\t\t\t  for (var i = 0; i < formcontent.length; i++) {\r\n\t\t\t    //console.log(subdata['form' + i]);\r\n\t\t\t    if (formcontent[i].val3 == 1 && (subdata['form' + i] === '' || subdata['form' + i] === undefined || subdata['form' + i].length == 0)) {\r\n\t\t\t      app.alert(formcontent[i].val1 + ' 必填');\r\n\t\t\t      return;\r\n\t\t\t    }\r\n\t\t\t    if (formcontent[i].key == 'switch') {\r\n\t\t\t      if (subdata['form' + i] == false) {\r\n\t\t\t        subdata['form' + i] = '否';\r\n\t\t\t      } else {\r\n\t\t\t        subdata['form' + i] = '是';\r\n\t\t\t      }\r\n\t\t\t    }\r\n\t\t\t    if (formcontent[i].key == 'selector') {\r\n\t\t\t      subdata['form' + i] = formcontent[i].val2[subdata['form' + i]];\r\n\t\t\t    }\r\n\t\t\t    var nowformdata = {};\r\n\t\t\t    formdata[formcontent[i].val1] = subdata['form' + i];\r\n\t\t\t  }\r\n\t\t\t  app.post(\"ApiChoujiang/savememberinfo\", {\r\n\t\t\t\t\thid:that.info.id,\r\n\t\t\t    formcontent: formdata\r\n\t\t\t  }, function (res) {\r\n\t\t\t    if (res.status == 0) {\r\n\t\t\t      app.alert(res.msg);\r\n\t\t\t    } else {\r\n\t\t\t      that.changemaskshow();\r\n\t\t\t      app.success(res.msg);\r\n\t\t\t      that.getdata();\r\n\t\t\t    }\r\n\t\t\t  });\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style scoped>\r\n\t.container{\r\n\t\tposition: absolute;\r\n\t\theight: 100%;\r\n\t\twidth: 100%;\r\n\t}\r\n</style>\r\n<style>\r\n\t.scratch-center {\r\n\t\tposition: relative;\r\n\t\tpadding-top: 380rpx;\r\n\t}\r\n\r\n\t.scratch-bg {\r\n\t\tpadding-top: 30rpx;\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 80rpx\r\n\t}\r\n\r\n\t.scratch-bg-1 {\r\n\t\twidth: 640rpx;\r\n\t\theight: 360rpx;\r\n\t}\r\n\r\n\t.scratch-bg-2 {\r\n\t\tposition: absolute;\r\n\t\ttop: 20rpx;\r\n\t\tleft: 50%;\r\n\t\t-webkit-transform: translate(-50%, -50%);\r\n\t\ttransform: translate(-50%, 0);\r\n\t\twidth: 600rpx;\r\n\t\theight: 320rpx;\r\n\t}\r\n\r\n\t.scratch-bg-3 {\r\n\t\tposition: absolute;\r\n\t\ttop: 150rpx;\r\n\t\tleft: 50%;\r\n\t\t-webkit-transform: translate(-50%, -50%);\r\n\t\ttransform: translate(-50%, 0);\r\n\t\tline-height: 80rpx;\r\n\t\tbackground: #f05525;\r\n\t\tborder-radius: 40rpx;\r\n\t\tpadding: 0 48rpx;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.scratch-award {\r\n\t\tposition: absolute;\r\n\t\ttop: 20rpx;\r\n\t\tleft: 50%;\r\n\t\t-webkit-transform: translate(-50%, -50%);\r\n\t\ttransform: translate(-50%, 0);\r\n\t\twidth: 600rpx;\r\n\t\theight: 320rpx;\r\n\t}\r\n\r\n\t.scratch-canvas {\r\n\t\tz-index: 999;\r\n\t\twidth: 600rpx;\r\n\t\theight: 320rpx;\r\n\t}\r\n\r\n\t.scratch-award-a {\r\n\t\tposition: relative;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 600rpx;\r\n\t\theight: 320rpx;\r\n\t}\r\n\r\n\t.scratch-bg-text {\r\n\t\tposition: absolute;\r\n\t\ttop: 60rpx;\r\n\t\tleft: 50%;\r\n\t\t-webkit-transform: translate(-50%, -50%);\r\n\t\ttransform: translate(-50%, 0);\r\n\t}\r\n\r\n\t.scratch-text-1 {\r\n\t\tfont-size: 18pt;\r\n\t\tcolor: #f05525;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-line-clamp: 1;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\twidth: 410rpx;\r\n\t}\r\n\r\n\t.scratch-bg-text-2 {\r\n\t\twidth: 400rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tmargin-top: 40rpx;\r\n\t\tborder-radius: 40rpx;\r\n\t\tbackground: #f05525;\r\n\t}\r\n\r\n\t.scratch-bg-text-3 {\r\n\t\twidth: 400rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tmargin-top: 40rpx;\r\n\t\tborder-radius: 40rpx;\r\n\t\tbackground: #cdcdcd;\r\n\t}\r\n\r\n\t.wrap {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.header {\r\n\t\twidth: 100%;\r\n\t\tpadding: 22rpx 37rpx 0 37rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between\r\n\t}\r\n\r\n\t.rule,\r\n\t.my {\r\n\t\twidth: 140rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder: 1px solid #f58d40;\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 60rpx;\r\n\t\ttext-align: center;\r\n\t\tcolor: #f58d40;\r\n\t\tborder-radius: 5rpx;\r\n\t}\r\n\r\n\t.title {\r\n\t\twidth: 640rpx;\r\n\t\theight: 316rpx;\r\n\t\tmargin: auto;\r\n\t\tmargin-top: -60rpx;\r\n\t}\r\n\r\n\t/*次数*/\r\n\t.border {\r\n\t\twidth: 380rpx;\r\n\t\theight: 64rpx;\r\n\t\tmargin: 0 auto 25rpx;\r\n\t\tbackground: #fb3a13;\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 64rpx;\r\n\t\ttext-align: center;\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 45rpx\r\n\t}\r\n\r\n\t.border2 {\r\n\t\twidth: 600rpx;\r\n\t\theight: 50rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tbackground: #dbaa83;\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 50rpx;\r\n\t\ttext-align: center;\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 10rpx\r\n\t}\r\n\r\n\t.scroll {\r\n\t\twidth: 550rpx;\r\n\t\theight: 185rpx;\r\n\t\tmargin: 75rpx auto 0 auto;\r\n\t}\r\n\r\n\t.scroll .p {\r\n\t\twidth: 372rpx;\r\n\t\theight: 24rpx;\r\n\t\tmargin: auto;\r\n\t}\r\n\r\n\t.sideBox {\r\n\t\twidth: 100%;\r\n\t\theight: 100rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t\tpadding: 10rpx 0 10rpx 0;\r\n\t\tbackground-color: rgba(255, 255, 255, 0.2);\r\n\t\tborder-radius: 10rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.sideBox .bd {\r\n\t\twidth: 100%;\r\n\t\theight: 80rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.sideBox .sitem {\r\n\t\toverflow: hidden;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 20rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t/*规则弹窗*/\r\n\t#mask-rule,\r\n\t#mask {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\tz-index: 999;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.85);\r\n\t}\r\n\r\n\t#mask-rule .box-rule {\r\n\t\tposition: relative;\r\n\t\tmargin: 30% auto;\r\n\t\tpadding-top: 40rpx;\r\n\t\twidth: 90%;\r\n\t\theight: 675rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #f58d40;\r\n\t}\r\n\r\n\t#mask-rule .box-rule .star {\r\n\t\tposition: absolute;\r\n\t\tleft: 50%;\r\n\t\ttop: -100rpx;\r\n\t\tmargin-left: -130rpx;\r\n\t\twidth: 259rpx;\r\n\t\theight: 87rpx;\r\n\t}\r\n\r\n\t#mask-rule .box-rule .h2 {\r\n\t\twidth: 100%;\r\n\t\ttext-align: center;\r\n\t\tline-height: 34rpx;\r\n\t\tfont-size: 34rpx;\r\n\t\tfont-weight: normal;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t#mask-rule #close-rule {\r\n\t\tposition: absolute;\r\n\t\tright: 34rpx;\r\n\t\ttop: 38rpx;\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t}\r\n\r\n\t/*内容盒子*/\r\n\t#mask-rule .con {\r\n\t\toverflow: auto;\r\n\t\tposition: relative;\r\n\t\tmargin: 40rpx auto;\r\n\t\tpadding-right: 15rpx;\r\n\t\twidth: 580rpx;\r\n\t\theight: 82%;\r\n\t\tline-height: 48rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t#mask-rule .con .text {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: inherit;\r\n\t\theight: auto;\r\n\t}\r\n\r\n\t.back_img {\r\n\t\tposition: fixed;\r\n\t\twidth: 100%;\r\n\t\ttop: 0;\r\n\t}\r\n\t.body{\r\n\t\tposition: relative;\r\n\t}\r\n\t.popup__container{width: 80%; margin: 10% auto;}\r\n\t.popup__modal{position: fixed;top:400rpx;width: 660rpx;height: 720rpx;margin: 0 auto;border-radius: 20rpx;left: 46rpx;}\r\n\t.popup__content{text-align: center;padding: 20rpx 0 ;}\r\n\t.popup__content image{height: 600rpx;width: 600rpx;}\r\n\t\r\n\t\r\n\t\r\n\t\r\n\t#mask-rule1{position: fixed;top: 0;z-index: 10;width: 100%;max-width:640px;height: 100%;background-color: rgba(0, 0, 0, 0.85);}\r\n\t#mask-rule1 .box-rule {background-color: #f58d40;position: relative;margin: 30% auto;padding-top:40rpx;width: 90%;height:700rpx;border-radius:20rpx;}\r\n\t#mask-rule1 .box-rule .h2{width: 100%;text-align: center;line-height:34rpx;font-size: 34rpx;font-weight: normal;color: #fff;}\r\n\t#mask-rule1 #close-rule1{position: absolute;right:34rpx;top: 38rpx;width: 40rpx;height: 40rpx;}\r\n\t#mask-rule1 .con {overflow: auto;position: relative;margin: 40rpx auto;padding-right: 15rpx;width:580rpx;height: 82%;line-height: 48rpx;font-size: 26rpx;color: #fff;}\r\n\t#mask-rule1 .con .text {position: absolute;top: 0;left: 0;width: inherit;height: auto;}\r\n\t\r\n\t#mask-rule2{position: fixed;top: 0;z-index: 10;width: 100%;max-width:640px;height: 100%;background-color: rgba(0, 0, 0, 0.85);}\r\n\t#mask-rule2 .box-rule {background-color: #f58d40;position: relative;margin: 30% auto;padding-top:40rpx;width: 90%;height:700rpx;border-radius:20rpx;}\r\n\t#mask-rule2 .box-rule .h2{width: 100%;text-align: center;line-height:34rpx;font-size: 34rpx;font-weight: normal;color: #fff;}\r\n\t#mask-rule2 #close-rule2{position: absolute;right:34rpx;top: 38rpx;width: 40rpx;height: 40rpx;}\r\n\t#mask-rule2 .con {overflow: auto;position: relative;margin: 20rpx auto;padding-right: 15rpx;width:580rpx;height:90%;line-height: 48rpx;font-size: 26rpx;color: #fff;}\r\n\t#mask-rule2 .con .text {position: absolute;top: 0;left: 0;width: inherit;height: auto;}\r\n\t.pay-form .item{width:100%;padding:0 0 10px 0;color:#fff;}\r\n\t.pay-form .item:last-child{border-bottom:0}\r\n\t.pay-form .item .f1{width:80px;text-align:right;padding-right:10px}\r\n\t.pay-form .item .f2 input[type=text]{width:100%;height:35px;padding:2px 5px;border:1px solid #ddd;border-radius:2px}\r\n\t.pay-form .item .f2 textarea{width:100%;height:60px;padding:2px 5px;border:1px solid #ddd;border-radius:2px}\r\n\t.pay-form .item .f2 select{width:100%;height:35px;padding:2px 5px;border:1px solid #ddd;border-radius:2px}\r\n\t.pay-form .item .f2 label{height:35px;line-height:35px;} \r\n\t.subbtn{width:100%;background:#fb3a13;font-size: 30rpx;padding:0 22rpx;border-radius: 8rpx;color:#FFF;margin-top: 30rpx;}\r\n</style>\r\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=0890455c&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=0890455c&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754212967658\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754212967673\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}