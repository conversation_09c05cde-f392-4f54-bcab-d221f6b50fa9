{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/yuyue/jdorderlist.vue?afb4", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/yuyue/jdorderlist.vue?015c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/yuyue/jdorderlist.vue?3e57", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/yuyue/jdorderlist.vue?037a", "uni-app:///activity/yuyue/jdorderlist.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/yuyue/jdorderlist.vue?08b4", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/yuyue/jdorderlist.vue?0525"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "st", "datalist", "pagenum", "nomore", "nodata", "keyword", "interval1", "timestamp", "showform", "showtabbar", "showaddmoney", "showmodal", "addprice", "showpaycodes", "paycode", "add<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "cancancel", "needstartpic", "startpic", "needend<PERSON>", "endpic", "ischangeprice", "totalprice", "changeid", "onLoad", "onUnload", "clearInterval", "onPullDownRefresh", "onReachBottom", "methods", "changetab", "uni", "scrollTop", "duration", "getdata", "that", "app", "mid", "updatemylocation", "console", "thisdata", "longitude", "latitude", "t", "getdistance", "juli", "unit", "setst", "id", "setTimeout", "da<PERSON><PERSON>", "itemList", "success", "name", "address", "scale", "fail", "searchConfirm", "addmoney", "cancel", "<PERSON><PERSON><PERSON>", "addconfirm", "price", "addmoneyPayorderid", "showpaycode", "update", "changeprice", "changepriceCancel", "changepriceSub"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/EA;AAAA;AAAA;AAAA;AAAy0B,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmO71B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;MACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;QACAC;QACAC;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAC;QAAArC;QAAAE;QAAAG;QAAAiC;MAAA;QACA;UACAD;UACA;QACA;QACA;QACA;UACAD;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACA;YACAA;YACA;cACAA;YACA;YACA;cACAA;YACA;YACA;cACAA;YACA;UACA;UAEAA;UACAA;UACAT;UACAS;YACAA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAG;MACA;MACAF;QACA;QACA;QACA;QACAG;QACA;UACA;UACA;UACAC;UACAA;UACAA;UACAxC;QACA;QACAmC;QACAA;QACAC;UAAAK;UAAAC;UAAAC;QAAA;UACA;QAAA,CACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;QACAC;MACA;MACAD;MACA;QAAAA;QAAAC;MAAA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;MACAX;QACAA;QACAA;UAAAY;UAAAjD;QAAA;UACAqC;UACA;YACAA;YACAa;cACAd;YACA;UACA;YACAC;UACA;QAEA;MACA;IACA;IACAc;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;MACA;QACA;MACA;MACAnB;QACAoB;QACAC;UACA;YACA;cACA;gBACA;gBACA;gBACA;gBACA;gBACA;cACA;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;cACA;YACA;YACArB;cACAW;cACAD;cACAY;cACAC;cACAC;cACAH;gBACAb;cACA;cACAiB;gBACAjB;cACA;YACA;UACA;QACA;MACA;IACA;IACAkB;MACA;MACA;MACAtB;MACAA;IACA;IACAuB;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACAzB;IACA;IACA0B;MACA;MACA;QACAzB;QACA;MACA;MACAA;QAAAY;QAAAc;QAAAC;MAAA;QACA3B;QACAA;QACA;UACAD;UACAA;QACA;MACA;IACA;IACA6B;MACA;MACA;MACA;MACA7B;MACA;MACA;MACA;MACA;MACA;IACA;IACA8B;MACA;MACA;MACA;MACA;MACA9B;IAEA;IACA+B;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAhC;MACAA;QAAAY;QAAA1B;MAAA;QACAc;QACA;UACAD;UACAc;YACAd;UACA;QACA;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9iBA;AAAA;AAAA;AAAA;AAAsrC,CAAgB,smCAAG,EAAC,C;;;;;;;;;;;ACA1sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/yuyue/jdorderlist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/yuyue/jdorderlist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./jdorderlist.vue?vue&type=template&id=11b8e9c4&\"\nvar renderjs\nimport script from \"./jdorderlist.vue?vue&type=script&lang=js&\"\nexport * from \"./jdorderlist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./jdorderlist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/yuyue/jdorderlist.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jdorderlist.vue?vue&type=template&id=11b8e9c4&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.showmodal ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.showmodal ? _vm.t(\"color1rgb\") : null\n  var m2 =\n    _vm.isload && _vm.showpaycodes && _vm.addmoneystatus == 0\n      ? _vm.t(\"color1\")\n      : null\n  var m3 =\n    _vm.isload && _vm.showpaycodes && _vm.addmoneystatus == 0\n      ? _vm.t(\"color1rgb\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jdorderlist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jdorderlist.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view>\n\t\t\t<view class=\"search-container\">\n\t\t\t\t<view class=\"search-box\">\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\"></image>\n\t\t\t\t\t<input class=\"search-text\" placeholder=\"搜索商家\" placeholder-style=\"color:#aaa;font-size:24rpx\" @confirm=\"searchConfirm\"/>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"item.id\">\n\t\t\t<view class=\"order-box\" @tap=\"goto\" :data-url=\"'jdorderdetail?id=' + item.id\">\n\t\t\t\t<view class=\"head\">\n\t\t\t\t\t<view class=\"fwtype1\" v-if=\"item.fwtype != 2\">到店</view><view v-else class=\"fwtype2\">上门</view>\n\t\t\t\t\t<view v-if=\"item.fwtype==1 || item.fwtype==3\">\n\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.status==3\"><image :src=\"pre_url+'/static/img/peisong/ps_time.png'\" class=\"img\"/>已完成</view>\n\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.status==1\"><image :src=\"pre_url+'/static/img/peisong/ps_time.png'\" class=\"img\"/><text class=\"t1\">等待客户上门</text> </view>\n\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.status==2\"><image :src=\"pre_url+'/static/img/peisong/ps_time.png'\" class=\"img\"/><text class=\"t1\" style=\"margin-left:10rpx\">服务中</text></view>\n\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.status==10\"><image :src=\"pre_url+'/static/img/peisong/ps_time.png'\" class=\"img\"/><text class=\"t1\" style=\"margin-left:10rpx\">已取消</text></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-else-if=\"item.fwtype==2\">\n\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.status==3\"><image :src=\"pre_url+'/static/img/peisong/ps_time.png'\" class=\"img\"/>已完成</view>\n\t\t\t\t\t\t<view class=\"f1\" v-else-if=\"item.status==1\"><image :src=\"pre_url+'/static/img/peisong/ps_time.png'\" class=\"img\"/>期望上门时间<text class=\"t1\">{{item.orderinfo.yydate}}</text> </view>\n\t\t\t\t\t\t<block v-else-if=\"item.status==2\">\n\t\t\t\t\t\t\t\t<view class=\"f1\" v-if=\"showaddmoney\">\n\t\t\t\t\t\t\t\t\t<block v-if=\"!item.sign_status\">\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/peisong/ps_time.png'\" class=\"img\"/>已到达，等待服务\n\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t<block v-else=\"!item.sign_status\">\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/peisong/ps_time.png'\" class=\"img\"/>已到达，正在服务\n\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"f1\" v-else>\t<image :src=\"pre_url+'/static/img/peisong/ps_time.png'\" class=\"img\"/>已到达，服务中</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<view class=\"f1\" v-if=\"item.status==1\"><image :src=\"pre_url+'/static/img/peisong/ps_time.png'\" class=\"img\"/>已取消</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t<view class=\"t1\" v-if=\"item.showprice\">\n\t\t\t\t\t\t\t<text>￥{{item.order_totalprice}}</text>\n\t\t\t\t\t\t\t<text class=\"t11\" v-if=\"item.ticheng>0\">(￥{{item.ticheng}})</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<block v-else><text class=\"t1\">{{item.ticheng}}</text>元</block>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"content\">\n          <block v-if=\"!item.orderinfo.protype\">\n            <view class=\"f1\" v-if=\"item.fwtype == 2\">\n              <view class=\"t1\"><text class=\"x1\">{{item.juli}}</text><text class=\"x2\">{{item.juli_unit}}</text></view>\n              <view class=\"t2\"><image :src=\"pre_url+'/static/img/peisong/ps_juli.png'\" class=\"img\"/></view>\n              <view class=\"t3\"><text class=\"x1\">{{item.juli2}}</text><text class=\"x2\">{{item.juli2_unit}}</text></view>\n            </view>\n            <view class=\"f2\">\n              <view class=\"t1\">{{item.binfo.name}}</view>\n              <view class=\"t2\">{{item.binfo.address}}</view>\n              <view class=\"t3\">{{item.orderinfo.address}}</view>\n              <view class=\"t2\">{{item.orderinfo.area}}</view>\n            </view>\n            <view class=\"f3\"  @tap.stop=\"daohang\" :data-index=\"index\" data-protype=\"0\" :data-fwtype=\"item.fwtype\"><image :src=\"pre_url+'/static/img/peisong/ps_daohang.png'\" class=\"img\"/></view>\n          </block>\n          <block v-else>\n            <view class=\"f1\" style=\"margin-top: 38rpx;\">\n              <view class=\"t3\"><text class=\"x1\">{{item.juli2}}</text><text class=\"x2\">{{item.juli2_unit}}</text></view>\n            </view>\n            <view class=\"f2\">\n              <view class=\"t3\">{{item.orderinfo.address}}</view>\n              <view class=\"t2\">{{item.orderinfo.area}}</view>\n            </view>\n            <view class=\"f3\" @tap.stop=\"daohang\" :data-index=\"index\" data-protype=\"1\"><image :src=\"pre_url+'/static/img/peisong/ps_daohang.png'\" class=\"img\"/></view>\n          </block>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"item.fwtype==1 || item.fwtype==3\" class=\"op\">\n\t\t\t\t\t<view class=\"t3\" v-if=\"item.order_status==0\">用户待支付</view>\n\t\t\t\t\t<block v-if=\"item.order_status!=0\">\n\t\t\t\t\t\t<view class=\"t1\" v-if=\"item.status==1\">已接单，待顾客上门</view>\n\t\t\t\t\t\t<view class=\"t1\" v-if=\"item.status==2\">顾客已到达</view>\n\t\t\t\t\t\t<view class=\"t1\" v-if=\"item.status==3\">已完成</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t\t<view class=\"btn3\" v-if=\"item.order_status==0\" @tap.stop=\"changeprice\" :data-id=\"item.id\" :data-price=\"item.order_totalprice\">\n\t\t\t\t\t\t\t改 价\n\t\t\t\t\t</view>\n\t\t\t\t\t<block v-if=\"item.order_status!=0\">\n\t\t\t\t\t\t<view class=\"btn1\" @tap.stop=\"setst\" :data-id=\"item.id\" data-st=\"2\" v-if=\"item.status==1\">顾客已到达</view>\n\t\t\t\t\t\t<view class=\"btn1\" @tap.stop=\"setst\" :data-id=\"item.id\" data-st=\"3\" v-if=\"item.status==2\">我已完成</view>\n\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t\t<view v-else-if=\"item.fwtype==2\" class=\"op\">\n\t\t\t\t\t\t<view class=\"t3\" v-if=\"item.order_status==0\">用户待支付</view>\n\t\t\t\t\t\t<block v-if=\"item.order_status!=0\">\n\t\t\t\t\t\t\t<view class=\"t1\" v-if=\"item.status==1\">已接单，等待上门</view>\n\t\t\t\t\t\t\t<view class=\"t1\" v-if=\"item.status==2\">已到达，共用时{{item.useminute}}分钟</view>\n\t\t\t\t\t\t\t<view class=\"t1\" v-if=\"item.status==3\">已完成</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t\t\t<!-- 订单未支付 -->\n\t\t\t\t\t\t<view class=\"btn3\" v-if=\"item.order_status==0\" @tap.stop=\"changeprice\" :data-id=\"item.id\" :data-price=\"item.order_totalprice\">\n\t\t\t\t\t\t\t\t改 价\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<block v-if=\"item.order_status!=0\">\n\t\t\t\t\t\t\t<block v-if=\"showaddmoney\">\n\t\t\t\t\t\t\t\t\t<view class=\"btn1 btn2\" @tap.stop=\"addmoney\" v-if=\"item.sign_status==1 && item.status==2 && item.addprice<=0\" :data-id=\"item.id\">补差价</view>\t\t\t\t\t\t\t<view class=\"btn1\" @tap.stop=\"setst\" :data-id=\"item.id\" data-st=\"2\" v-if=\"item.status==1\">出发</view>\n\t\t\t\t\t\t\t\t\t<view class=\"btn1 btn2\" @tap.stop=\"showpaycode\" v-if=\"item.addprice>0\" :data-id=\"item.id\" :data-key=\"index\">查看补余款</view>\n\t\t\t\t\t\t\t\t\t<view class=\"btn1\" @tap.stop=\"setst\" :data-id=\"item.id\" data-st=\"5\" v-if=\"!item.sign_status && item.status==2\">开始服务</view>\n\t\t\t\t\t\t\t\t\t<view class=\"btn1\" @tap.stop=\"setst\" :data-id=\"item.id\" data-st=\"3\" v-if=\"item.sign_status==1 && item.status==2\">服务完成</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t<block v-if=\"item.status==1\">\n\t\t\t\t\t\t\t\t\t<view class=\"btn1\" @tap.stop=\"setst\" :data-id=\"item.id\" data-st=\"10\" v-if=\"yuyuecar && item.protype && cancancel\" style=\"margin-right: 20rpx;background: #fff;color: #222;border: 2rpx solid #222;\">取消</view>\n\t\t\t\t\t\t\t\t\t<block v-if=\"yuyuecar && item.protype && needstartpic\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn1\" @tap.stop=\"goto\" :data-url=\"'/pagesA/yuyuecar/uppic?id='+item.id+'&st=2'\">我已到达</view>\n\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn1\" @tap.stop=\"setst\" :data-id=\"item.id\" data-st=\"2\" v-if=\"item.status==1\">我已到达</view>\n\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<block v-if=\"item.status==2\">\n\t\t\t\t\t\t\t\t\t<block v-if=\"yuyuecar && item.protype && needendpic\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn1\" @tap.stop=\"goto\" :data-url=\"'/pagesA/yuyuecar/uppic?id='+item.id+'&st=3'\">我已完成</view>\n\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn1\" @tap.stop=\"setst\" :data-id=\"item.id\" data-st=\"3\">我已完成</view>\n\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t</block>\n\t\t\n\t\t\t<nomore v-if=\"nomore\"></nomore>\n\t\t\t<nodata v-if=\"nodata\"></nodata>\n\t\t</view>\n\t\t<!-- <view style=\"width:100%;height:120rpx\"></view> -->\n\t\t<!-- <view class=\"bottom\">\n\t\t\t<view class=\"my\">\n\t\t\t\t<image src=\"/static/img/my.png\" class=\"img\"/>\n\t\t\t\t<text>我的</text>\n\t\t\t</view>\n\t\t\t<view class=\"btn1\" @tap=\"setpsst\" data-st=\"1\" v-if=\"psuser.status==0\">暂停接单中</view>\n\t\t\t<view class=\"btn2\" :style=\"{background:t('color1')}\" @tap=\"setpsst\" data-st=\"0\" v-if=\"psuser.status==1\">开启接单中</view>\n\t\t</view> -->\n\t\t\n\t\t<view class=\"tabbar\" v-if=\"showtabbar\">\n\t\t\t<view class=\"tabbar-bot\"></view>\n\t\t\t<view class=\"tabbar-bar\" style=\"background-color:#ffffff\">\n\t\t\t\t<view @tap=\"goto\" data-url=\"dating\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/home.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\">大厅</view>\n\t\t\t\t</view>\n\t\t\t\t<view @tap=\"goto\" data-url=\"jdorderlist\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/order'+(st!=3?'2':'')+'.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\" :class=\"st!=3?'active':''\">订单</view>\n\t\t\t\t</view>\n\t\t\t\t<view @tap=\"goto\" data-url=\"jdorderlist?st=3\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/orderwc'+(st==3?'2':'')+'.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\" :class=\"st==3?'active':''\">已完成</view>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"showform\" @tap=\"goto\" data-url=\"formlog\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/dangan.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\">档案</view>\n\t\t\t\t</view>\n\t\t\t\t<view @tap=\"goto\" data-url=\"my\" data-opentype=\"reLaunch\" class=\"tabbar-item\">\n\t\t\t\t\t<view class=\"tabbar-image-box\">\n\t\t\t\t\t\t<image class=\"tabbar-icon\" :src=\"pre_url+'/static/img/peisong/my.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tabbar-text\">我的</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"modal\" v-if=\"showmodal\">\n\t\t\t<view class=\"addmoney\">\n\t\t\t\t\t<view class=\"title\">{{addprice>0?'修改':'创建'}}补余款</view>\n\t\t\t\t\t<view class=\"item\">\n\t\t\t\t\t\t<label class=\"label\">金额：</label><input type=\"text\" @input=\"bindMoney\" name=\"blance_price\" :value=\"addprice\" placeholder=\"请输入补余款金额\"  placeholder-style=\"font-size:24rpx\"/>元\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"btn\"><button class=\"btn-cancel\" @tap=\"cancel\">取消</button><button class=\"confirm\"  :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\"  @tap.stop=\"addconfirm\">确定</button></view>\n\t\t\t</view>\n\t\t</view>\t\n\t\t\n\t\t<view class=\"modal\" v-if=\"showpaycodes\">\n\t\t\t<view class=\"addmoney\">\n\t\t\t\t\t<view class=\"title\">查看补余款</view>\n\t\t\t\t\t<view class=\"item\" >\n\t\t\t\t\t\t<label>金额：</label><text class=\"price\">{{addprice}}</text>元\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item\" style=\"padding-top: 0;\">\n\t\t\t\t\t\t<label>支付状态：</label> <text class=\"t2\" v-if=\"addmoneystatus==1\"> 已支付</text> \n\t\t\t\t\t\t<text class=\"t2\" v-if=\"addmoneystatus==0\" style=\"color:red;\"> 待支付</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"qrcode\"><image :src=\"paycode\"></view>\n\t\t\t\t\t<view class=\"btn\"><button class=\"btn-cancel\" @tap=\"cancel\">关闭</button> \n\t\t\t\t\t\n\t\t\t\t\t<button class=\"btn-update\" v-if=\"addmoneystatus==0\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\"  @tap=\"update\" :data-key=\"index\" :data-id=\"id\"  >修改差价</button></view>\n\t\t\t</view>\n\t\t</view>\t\n\t\t<!-- 未付款订单改价 -->\n\t\t<view class=\"modal\" v-if=\"ischangeprice\">\n\t\t\t<view class=\"addmoney changeprice\">\n\t\t\t\t\t<view class=\"title\">订单改价</view>\n\t\t\t\t\t<view class=\"item flex-y-center\">\n\t\t\t\t\t\t<label class=\"label\">订单金额：</label><input type=\"text\" name=\"blance_price\" v-model=\"totalprice\" placeholder=\"订单金额\"  placeholder-style=\"font-size:24rpx\"/>元\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex-xy-center\"><button class=\"cbtn\" @tap=\"changepriceCancel\">取消</button><button class=\"cbtn\"  @tap.stop=\"changepriceSub\">确定</button></view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<view style=\"display:none\">{{timestamp}}</view>\n\t<wxxieyi></wxxieyi>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tpre_url:app.globalData.pre_url,\n\n      st: '11',\n      datalist: [],\n      pagenum: 1,\n      nomore: false,\n      nodata: false,\n\t\t\tkeyword:'',\n\t\t\tinterval1:null,\n\t\t\ttimestamp:'',\n\t\t\tshowform:0,\n\t\t\tshowtabbar:false,\n\t\t\tshowaddmoney:false,\n\t\t\tshowmodal:false,\n\t\t\taddprice:0,\n\t\t\tshowpaycodes:false,\n\t\t\tpaycode:'',\n\t\t\taddmoneystatus:0,\n      \n      yuyuecar:false,\n      cancancel:false,\n      needstartpic:false,\n      startpic:'',\n      needendpic:false,\n      endpic:'',\n\t\t\tischangeprice:false,\n\t\t\ttotalprice:0,\n\t\t\tchangeid:0\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.st = this.opt.st || '11';\n\t\tif(this.opt.mid){\n\t\t\tthis.showtabbar = false;\n\t\t}else{\n\t\t\tthis.showtabbar = true;\n\t\t}\n\t\tthis.getdata();\n  },\n\tonUnload:function(){\n\t\tclearInterval(this.interval1);\n\t},\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata(true);\n    }\n  },\n  methods: {\n    changetab: function (st) {\n      this.st = st;\n      uni.pageScrollTo({\n        scrollTop: 0,\n        duration: 0\n      });\n      this.getdata();\n    },\n    getdata: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var st = that.st;\n      var pagenum = that.pagenum;\n      var keyword = that.keyword;\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n      app.post('ApiYuyueWorker/orderlist', {st: st,pagenum: pagenum,keyword:keyword,mid:this.opt.mid}, function (res) {\n\t\t\t\tif(res.status==0){\n\t\t\t\t\tapp.alert(res.msg);\n\t\t\t\t\treturn;\n\t\t\t\t}\n        var data = res.datalist;\n        if (pagenum == 1) {\n\t\t\t\t\tthat.datalist = data;\n\t\t\t\t\tthat.nowtime = res.nowtime\n\t\t\t\t\tthat.showform = res.showform;\n\t\t\t\t\tthat.showaddmoney = res.addmoney\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n          if(res.yuyuecar){\n            that.yuyuecar = true\n            if(res.cancancel){\n              that.cancancel = res.cancancel;\n            }\n            if(res.needstartpic){\n              that.needstartpic = res.needstartpic;\n            }\n            if(res.needendpic){\n              that.needendpic = res.needendpic;\n            }\n          }\n\n\t\t\t\t\tthat.loaded();\n\t\t\t\t\tthat.updatemylocation();\n\t\t\t\t\tclearInterval(that.interval1);\n\t\t\t\t\tthat.interval1 = setInterval(function(){\n\t\t\t\t\t\tthat.updatemylocation(true);\n\t\t\t\t\t\tthat.nowtime = that.nowtime + 10;\n\t\t\t\t\t},10000)\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n\t\tupdatemylocation:function(){\n\t\t\tvar that = this;\n\t\t\tapp.getLocation(function(res){\n\t\t\t\tvar longitude = res.longitude;\n\t\t\t\tvar latitude = res.latitude;\n\t\t\t\tvar datalist = that.datalist;\n\t\t\t\tconsole.log(datalist);\n\t\t\t\tfor(var i in datalist){\n\t\t\t\t\tvar thisdata = datalist[i];\n\t\t\t\t\tvar rs = that.getdistance(thisdata.longitude2,thisdata.latitude2,longitude,latitude,1);\n\t\t\t\t\tthisdata.juli2 = rs.juli;\n\t\t\t\t\tthisdata.juli2_unit = rs.unit;\n\t\t\t\t\tthisdata.leftminute = parseInt((thisdata.yujitime - that.nowtime) / 60);\n\t\t\t\t\tdatalist[i] = thisdata;\n\t\t\t\t}\n\t\t\t\tthat.datalist = datalist;\n\t\t\t\tthat.timestamp = parseInt((new Date().getTime())/1000);\n\t\t\t\tapp.get('ApiYuyueWorker/updatemylocation',{longitude:longitude,latitude:latitude,t:that.timestamp},function(){\n\t\t\t\t\t//if(needload) that.getdata();\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\tgetdistance: function (lng1, lat1, lng2, lat2) {\n\t\t\tif(!lat1 || !lng1 || !lat2 || !lng2) return '';\n\t\t\tvar rad1 = lat1 * Math.PI / 180.0;\n\t\t\tvar rad2 = lat2 * Math.PI / 180.0;\n\t\t\tvar a = rad1 - rad2;\n\t\t\tvar b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;\n\t\t\tvar r = 6378137;\n\t\t\tvar juli = r * 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(rad2) * Math.pow(Math.sin(b / 2), 2)));\n\t\t\tvar unit = 'm';\n\t\t\tif(juli> 1000){\n\t\t\t\tjuli = juli/1000;\n\t\t\t\tunit = 'km';\n\t\t\t}\n\t\t\tjuli = juli.toFixed(1);\n\t\t\treturn {juli:juli,unit:unit}\n\t\t},\n    setst: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      var st = e.currentTarget.dataset.st;\n\t\t\tif(st == 2){\n\t\t\t\tif(that.showaddmoney){\n\t\t\t\t\t\tvar tips = '确定已经出发吗?';\n\t\t\t\t}else{\n\t\t\t\t\t\tvar tips = '确定改为已到达吗?';\n\t\t\t\t}\n\t\t\t}else if(st == 3){\n\t\t\t\tvar tips = '确定改为已完成吗?';\n\t\t\t}else if(st == 5){\n\t\t\t\tvar tips = '确定开始服务吗?';\n\t\t\t}else if(st == 10){\n        var tips = '确定要取消订单吗?';\n      }\n      app.confirm(tips, function () {\n\t\t\t\tapp.showLoading('提交中');\n        app.post('ApiYuyueWorker/setst', {id: id,st:st}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tif(data.status==1){\n\t\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t  that.getdata();\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tapp.error(data.msg);\n\t\t\t\t\t}\n         \n        });\n      });\n    },\n\t\tdaohang:function(e){\n\t\t\tvar that = this;\n\t\t\tvar index = e.currentTarget.dataset.index;\n\t\t\tvar datainfo = that.datalist[index];\n      var protype = e.currentTarget.dataset.protype;\n      var fwtype = e.currentTarget.dataset.fwtype;\n\n      if(protype==1 || fwtype == 2){\n      \tvar list = ['导航到用户'];\n      }else{\n      \tvar list = ['导航到商家'];\n      }\n\t\t\tuni.showActionSheet({\n        itemList: list,\n        success: function (res) {\n\t\t\t\t\tif(res.tapIndex >= 0){\n\t\t\t\t\t\tif (res.tapIndex == 0) {\n\t\t\t\t\t\t\tif(protype==1){\n\t\t\t\t\t\t\t\t// 用户\n\t\t\t\t\t\t\t\tvar longitude = datainfo.longitude2\n\t\t\t\t\t\t\t\tvar latitude = datainfo.latitude2\n\t\t\t\t\t\t\t\tvar name = datainfo.orderinfo.address\n\t\t\t\t\t\t\t\tvar address = datainfo.orderinfo.address\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tif(fwtype != 2){\n\t\t\t\t\t\t\t\t\t// 到店\n\t\t\t\t\t\t\t\t\tvar longitude = datainfo.longitude\n\t\t\t\t\t\t\t\t\tvar latitude = datainfo.latitude\n\t\t\t\t\t\t\t\t\tvar name = datainfo.binfo.name\n\t\t\t\t\t\t\t\t\tvar address = datainfo.binfo.address\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t// 上门\n\t\t\t\t\t\t\t\t\tvar longitude = datainfo.longitude2\n\t\t\t\t\t\t\t\t\tvar latitude = datainfo.latitude2\n\t\t\t\t\t\t\t\t\tvar name = datainfo.orderinfo.address\n\t\t\t\t\t\t\t\t\tvar address = datainfo.orderinfo.address\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tuni.openLocation({\n\t\t\t\t\t\t\tlatitude:parseFloat(latitude),\n\t\t\t\t\t\t\tlongitude:parseFloat(longitude),\n\t\t\t\t\t\t\tname:name,\n\t\t\t\t\t\t\taddress:address,\n\t\t\t\t\t\t\tscale: 13,\n\t\t\t\t\t\t\tsuccess: function () {\n                console.log('success');\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail:function(res){\n\t\t\t\t\t\t\t\tconsole.log(res);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n    searchConfirm: function (e) {\n      var that = this;\n      var keyword = e.detail.value;\n      that.keyword = keyword\n      that.getdata();\n    },\n\t\taddmoney:function(e){\n\t\t\tvar that=this\n\t\t\tthis.showmodal=true\n\t\t\tthis.id= e.currentTarget.dataset.id\n\t\t},\n\t\tcancel:function(e){\n\t\t\tvar that=this\n\t\t\tthis.showmodal=false\n\t\t\tthis.showpaycodes=false\n\t\t},\n\t\tbindMoney:function(e){\n\t\t\tvar that=this\n\t\t\tthat.addprice = e.detail.value\n\t\t},\n\t\taddconfirm:function(e){\n\t\t\tvar that = this\n\t\t\tif(!that.addprice){\n\t\t\t\tapp.error('请输入金额');\n\t\t\t\treturn;\n\t\t\t} \n\t\t\tapp.post('ApiYuyueWorker/addmoney', {id:that.id,price:that.addprice,addmoneyPayorderid:that.addmoneyPayorderid}, function (data) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tapp.success(data.msg);\n\t\t\t\tif(data.payorderid){\n\t\t\t\t\t\tthat.showmodal=false\n\t\t\t\t\t\tthat.getdata()\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tshowpaycode:function(e){\n\t\t\tvar that=this\n\t\t\tthis.showpaycodes=true\n\t\t\tvar index= e.currentTarget.dataset.key\n\t\t\tthat.index = index\n\t\t\tthis.addprice = \tthat.datalist[index].addprice\n\t\t\tthis.paycode = \tthat.datalist[index].paycode\n\t\t\tthis.addmoneystatus = \tthat.datalist[index].addmoneystatus\n\t\t\tthis.addmoneyPayorderid = \tthat.datalist[index].addmoneyPayorderid\n\t\t\tthis.id= e.currentTarget.dataset.id\n\t\t},\n\t\tupdate:function(e){\n\t\t\tvar that=this\n\t\t\tthis.showmodal=true\n\t\t\tthis.showpaycodes=false\n\t\t\tvar index= e.currentTarget.dataset.key\n\t\t\tthat.addprice = that.datalist[index].addprice\n\t\t\t\n\t\t},\n\t\tchangeprice:function(e){\n\t\t\tthis.changeid = e.currentTarget.dataset.id;\n\t\t\tthis.totalprice = e.currentTarget.dataset.price;\n\t\t\tthis.ischangeprice = true;\n\t\t},\n\t\tchangepriceCancel:function(){\n\t\t\tthis.ischangeprice = false;\n\t\t},\n\t\tchangepriceSub:function(){\n\t\t\tvar that = this;\n\t\t\tapp.showLoading('提交中...');\n\t\t\tapp.post('ApiYuyueWorker/changeprice', {id:that.changeid,totalprice:that.totalprice}, function (data) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tif(data.status==1){\n\t\t\t\t\tthat.ischangeprice = false;\n\t\t\t\t\tsetTimeout(function(){\n\t\t\t\t\t\tthat.getdata()\n\t\t\t\t\t},1000)\n\t\t\t\t}else{\n\t\t\t\t\tapp.error(data.msg);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n  }\n};\n</script>\n<style>\n@import \"./common.css\";\n.container{ width:100%;display:flex;flex-direction:column}\n.search-container {width: 100%;height:100rpx;padding: 20rpx 23rpx 20rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;border-bottom:1px solid #f5f5f5}\n.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\n.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}\n.search-box .search-text {font-size:24rpx;color:#222;width: 100%;}\n\n.order-box{ width: 94%;margin:20rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}\n.order-box .head{ display:flex;width:100%; border-bottom: 1px #f5f5f5 solid; height:88rpx; line-height:88rpx; overflow: hidden; color: #999;}\n.order-box .head .f1{display:flex;align-items:center;color:#222222;font-size: 26rpx;}\n.order-box .head .f1 .img{width:24rpx;height:24rpx;margin-right:4px}\n.order-box .head .f1 .t1{color:#06A051;margin-right:10rpx}\n.order-box .head .f2{color:#FF6F30}\n.order-box .head .f2 .t1{font-size:36rpx;margin-right:4rpx}\n.order-box .head .f2 .t11{font-size:30rpx;color: #999;}\n\n.order-box .content{display:flex;justify-content:space-between;width: 100%; padding:16rpx 0px;border-bottom: 1px solid #f5f5f5;position:relative}\n.order-box .content .f1{width:100rpx;display:flex;flex-direction:column;align-items:center}\n.order-box .content .f1 .t1{display:flex;flex-direction:column;align-items:center}\n.order-box .content .f1 .t1 .x1{color:#FF6F30;font-size:28rpx;font-weight:bold}\n.order-box .content .f1 .t1 .x2{color:#999999;font-size:24rpx;margin-bottom:8rpx}\n.order-box .content .f1 .t2 .img{width:12rpx;height:36rpx}\n\n.order-box .content .f1 .t3{display:flex;flex-direction:column;align-items:center}\n.order-box .content .f1 .t3 .x1{color:#FF6F30;font-size:28rpx;font-weight:bold}\n.order-box .content .f1 .t3 .x2{color:#999999;font-size:24rpx}\n.order-box .content .f2{flex:1;padding:0 20rpx}\n.order-box .content .f2 .t1{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-bottom:6rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n.order-box .content .f2 .t2{font-size:24rpx;color:#222222;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n.order-box .content .f2 .t3{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-top:30rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n.order-box .content .f3 .img{width:72rpx;height:168rpx}\n\n.order-box .op{display:flex;justify-content:flex-end;align-items:center;width:100%; padding:20rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}\n.order-box .op .t1{color:#06A051;font-weight:bold}\n.order-box .op .t3{color:#ff711d;}\n.order-box .op .btn1{background:linear-gradient(-90deg, #06A051 0%, #03B269 100%);height:70rpx;line-height:70rpx;color:#fff;border-radius:10rpx;text-align:center;font-size:28rpx; padding: 0 20rpx; font-size: 24rpx;}\n.order-box .op .btn2{ margin-right: 20rpx; font-size: 24rpx; background:rgb(5,162,83,0.1) ; color: #06A152; border: 1rpx solid #1AB271; }\n.order-box .op .btn3{font-size: 24rpx; background: #ff8f4d; color:#ffffff; height:60rpx;border-radius: 10rpx;min-width: 110rpx;display: flex;align-items: center;justify-content: center;}\n\n.modal{ position: fixed; width: 100%; height: 100%; bottom: 0; background: rgb(0,0,0,0.4); z-index: 100; display: flex; justify-content: center;}\n.modal .addmoney{ width: 100%; background: #fff; width: 80%; position: absolute; top: 30%; border-radius: 10rpx; }\n.modal .title{ height: 80rpx; ;line-height: 80rpx; text-align: center; font-weight: bold; border-bottom: 1rpx solid #f5f5f5; font-size: 32rpx; }\n.modal .item{ display: flex; padding: 30rpx;}\n.modal .item input{ width: 200rpx;}\n.modal .item label{ width:200rpx; text-align: right; font-weight: bold;}\n.modal .item .t2{ color: #008000; font-weight: bold;}\n.modal .btn{ display: flex; margin: 30rpx 20rpx; }\n.modal .btn .btn-cancel{  background-color: #F2F2F2; width: 150rpx; border-radius: 10rpx;}\n.modal .btn .confirm{ width: 150rpx; border-radius: 10rpx; color: #fff;}\n.modal .btn .btn-update{ width: 150rpx; border-radius: 10rpx; color: #fff; }\n.modal .addmoney .price{ color: red; font-size: 32rpx; font-weight: bold;}\n.modal .qrcode{ display: flex; align-items: center;}\n.modal .qrcode image{width: 300rpx; height: 300rpx; margin: auto;}\n.changeprice{height: 400rpx;}\n.changeprice .item{height: 160rpx;}\n.changeprice .item input{border-bottom: 1px solid #e1e1e1;padding:14rpx;line-height: 70rpx;height: 70rpx;text-align: center;}\n.changeprice .title{font-weight: normal;font-size: 28rpx;}\n.changeprice .cbtn{border: 1px solid #e1e1e1;width: 35%;text-align: center;border-radius: 8rpx;margin:20rpx 10rpx;}\n.changeprice .cbtn:last-child{background: #ff8f4d; color:#ffffff;}\n.fwtype1{background: linear-gradient(-90deg, #06A051 0%, #03B269 100%);color: #fff;border-radius: 8rpx;\n\ttext-align: center;margin: 24rpx 10rpx;padding: 5rpx 8rpx;line-height: 33rpx;font-size: 22rpx;}\n.fwtype2{background: #FF6F30;color: #fff;border-radius: 8rpx;text-align: center;margin: 24rpx 8rpx;padding: 5rpx 10rpx;line-height: 33rpx;font-size: 22rpx;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jdorderlist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jdorderlist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213579936\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}