{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/yuyue/peolist.vue?a705", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/yuyue/peolist.vue?c887", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/yuyue/peolist.vue?e626", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/yuyue/peolist.vue?704d", "uni-app:///activity/yuyue/peolist.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/yuyue/peolist.vue?9898", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/yuyue/peolist.vue?154a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "keyword", "datalist", "type", "nodata", "curTopIndex", "index", "curCid", "nomore", "pagenum", "clist", "pre_url", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "cid", "bid", "isget", "getdatalist", "field", "order", "longitude", "latitude", "switchTopTab", "searchChange", "searchConfirm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzFA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8Cz1B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACAC;MACAC;QAAAC;QAAAC;MAAA;QACAH;QACA;QACAA;QACA;QACA;UACA;YACA;cACAA;cACAA;cACA;YACA;YACA;YACA;YACA;cACA;gBACAA;gBACAA;gBACAA;gBACAI;gBACA;cACA;YACA;YACA;UACA;QACA;QACAJ;QACA;QACA;QACA;QACA;QACA;QACAA;QACA;QACA;QACA;QACA;MAEA;IAEA;;IACAK;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAL;MACAA;MACAA;MACA;MACA;MACAC;QAAAT;QAAAR;QAAAsB;QAAAC;QAAAL;QAAAC;QAAAjB;QAAAsB;QAAAC;MAAA;QACAT;QACA;QACA;UACAA;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UAEA;QACA;MACA;IAEA;IACAU;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAZ;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC7LA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/yuyue/peolist.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/yuyue/peolist.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./peolist.vue?vue&type=template&id=5c6bd1e4&\"\nvar renderjs\nimport script from \"./peolist.vue?vue&type=script&lang=js&\"\nexport * from \"./peolist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./peolist.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/yuyue/peolist.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./peolist.vue?vue&type=template&id=5c6bd1e4&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.clist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m1 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m1: m1,\n        }\n      })\n    : null\n  var l1 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m2 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m2: m2,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        l0: l0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./peolist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./peolist.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"topsearch flex-y-center\">\r\n\t\t\t<view class=\"f1 flex-y-center\">\r\n\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\"></image>\r\n\t\t\t\t<input :value=\"keyword\" placeholder=\"输入姓名/手机号搜索\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\" @input=\"searchChange\"></input>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"order-tab\">\r\n\t\t\t<view class=\"order-tab2\">\r\n\t\t\t\t<view :class=\"'item ' + (curTopIndex == -1 ? 'on' : '')\" @tap=\"switchTopTab\" :data-index=\"-1\" :data-id=\"0\">全部<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t<block v-for=\"(item, index) in clist\" :key=\"index\">\r\n\t\t\t\t\t<view :class=\"'item ' + (curTopIndex == index ? 'on' : '')\" @tap=\"switchTopTab\" :data-index=\"index\" :data-id=\"item.id\">{{item.name}}<view class=\"after\" :style=\"{background:t('color1')}\"></view></view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\r\n\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"content flex\" :data-id=\"item.id\">\r\n\t\t\t<view class=\"f1\" @click=\"goto\" :data-url=\"'peodetail?id='+item.id\" >\r\n\t\t\t\t<view class=\"headimg\"><image :src=\"item.headimg\" /></view>\r\n\t\t\t\t<view class=\"text1\">\t\r\n\t\t\t\t\t<text class=\"t1\">{{item.realname}} </text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"item.typename\" >{{item.typename}} </text>\r\n\t\t\t\t\t<view class=\"text2\">{{item.jineng}}</view>\r\n\t\t\t\t\t<view class=\"text3 flex\">\r\n\t\t\t\t\t\t<view class=\"t4\">服务<text> {{item.totalnum}}</text> 次</view> <view class=\"t5\">评分 <text>{{item.comment_score}}</text></view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\t\r\n\t\t\t</view>\r\n\t\t\t<view>\r\n\t\t\t\t<view class=\"yuyue\"  @click=\"goto\" :data-url=\"'/yuyue/yuyue/peodetail?id='+item.id\" :style=\"{background:t('color1')}\">预约</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<nodata v-if=\"nodata\"></nodata>\r\n\t\t<nomore v-if=\"nomore\"></nomore>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t<wxxieyi></wxxieyi>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n\t  opt:{},\r\n\t  loading:false,\r\n      isload: false,\r\n\t  menuindex:-1,\r\n      keyword: '',\r\n      datalist: [],\r\n      type: \"\",\r\n\t\tnodata:false,\r\n\t\t curTopIndex: -1,\r\n\t\t index:0,\r\n\t\t curCid:0,\r\n\t\t nomore: false,\r\n\t\t pagenum: 1,\r\n\t\t clist:[],\r\n\t\t pre_url:app.globalData.pre_url\r\n    }\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.type = this.opt.type || '';\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonReachBottom: function () {\r\n\t  if (!this.nodata && !this.nomore) {\r\n\t    this.pagenum = this.pagenum + 1;\r\n\t    this.getdatalist(true);\r\n\t  }\r\n\t},\r\n  methods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar nowcid = that.opt.cid;\r\n\t\t\tvar bid = that.opt.bid || 0;\r\n\t\t\tif (!nowcid) nowcid = '';\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiYuyue/peocategory', {cid: nowcid,bid:bid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tvar data = res.data;\r\n\t\t\t\tthat.clist = data;\r\n\t\t\t\t//that.curCid = data[0]['id'];\r\n\t\t\t\tif (nowcid) {\r\n\t\t\t\t\tfor (var i = 0; i < data.length; i++) {\r\n\t\t\t\t\t\tif (data[i]['id'] == nowcid) {\r\n\t\t\t\t\t\t\tthat.curTopIndex = i;\r\n\t\t\t\t\t\t\tthat.curCid = nowcid;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tvar downcdata = data[i]['child'];\r\n\t\t\t\t\t\tvar isget = 0;\r\n\t\t\t\t\t\tfor (var j = 0; j < downcdata; j++) {\r\n\t\t\t\t\t\t\tif (downcdata[j]['id'] == nowcid) {\r\n\t\t\t\t\t\t\t\tthat.curIndex = i;\r\n\t\t\t\t\t\t\t\tthat.curIndex2 = j;\r\n\t\t\t\t\t\t\t\tthat.curCid = nowcid;\r\n\t\t\t\t\t\t\t\tisget = 1;\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (isget) break;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded();\r\n\t\t\t\t//app.getLocation(function (res) {\r\n\t\t\t\t\t//var latitude = res.latitude;\r\n\t\t\t\t\t//var longitude = res.longitude;\r\n\t\t\t\t\t//that.longitude = longitude;\r\n\t\t\t\t\t//that.latitude = latitude;\r\n\t\t\t\t\tthat.getdatalist();\r\n\t\t\t//\t},\r\n\t\t\t//\tfunction () {\r\n\t\t\t\t//\tthat.getdatalist();\r\n\t\t\t//\t});\r\n\r\n\t\t\t});\r\n\t\t\t\r\n\t\t},\r\n\t\tgetdatalist: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n\t\t\tvar that = this;\r\n\t\t\tvar pagenum = that.pagenum;\r\n\t\t\tvar cid = that.curCid;\r\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\r\n\t\t\tvar order = that.order;\r\n\t\t    var keyword = that.keyword;\r\n\t\t\tvar field = that.field; \r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tvar latitude = that.latitude;\r\n\t\t\tvar longitude = that.longitude;\r\n\t\t\tapp.post('ApiYuyue/selectpeople', {pagenum: pagenum,keyword: keyword,field: field,order: order,cid: cid,bid:bid,type:'list',longitude: longitude,latitude: latitude}, function (res) { \r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tvar data = res.data;\r\n\t\t\t\tif (pagenum == 1) {\r\n          that.datalist = data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n        }else{\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n\r\n          }\r\n        }\r\n\t\t\t});\r\n\t\t \r\n\t\t},\r\n\t\tswitchTopTab: function (e) {\r\n\t\t   var that = this;\r\n\t\t   var id = e.currentTarget.dataset.id;\r\n\t\t   var index = parseInt(e.currentTarget.dataset.index);\r\n\t\t   this.curTopIndex = index;\r\n\t\t   this.curIndex = -1;\r\n\t\t   this.curIndex2 = -1;\r\n\t\t   this.prolist = [];\r\n\t\t   this.nopro = 0;\r\n\t\t   this.curCid = id;\r\n\t\t   this.getdatalist();\r\n\t\t}, \r\n\t\tsearchChange: function (e) {\r\n\t\t  this.keyword = e.detail.value;\r\n\t\t},\r\n\t\tsearchConfirm: function (e) {\r\n\t\t  var that = this;\r\n\t\t  var keyword = e.detail.value;\r\n\t\t  that.keyword = keyword;\r\n\t\t  that.getdata();\r\n\t\t}\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.topsearch{width:94%;margin:16rpx 3%;}\r\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\r\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\r\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\r\n\r\n.search-navbar-item .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\r\n.search-navbar-item .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\r\n.search-navbar-item .iconshaixuan{margin-left:10rpx;font-size:22rpx;color:#7d7d7d}\r\n.search-history {padding: 24rpx 34rpx;}\r\n.search-history .search-history-title {color: #666;}\r\n.search-history .delete-search-history {float: right;padding: 15rpx 20rpx;margin-top: -15rpx;}\r\n.search-history-list {padding: 24rpx 0 0 0;}\r\n.search-history-list .search-history-item {display: inline-block;height: 50rpx;line-height: 50rpx;padding: 0 20rpx;margin: 0 10rpx 10rpx 0;background: #ddd;border-radius: 10rpx;font-size: 26rpx;}\r\n\r\n\r\n.order-tab{display:flex;width:100%;overflow-x:scroll;border-bottom: 1px #f5f5f5 solid;background: #fff;padding:0 10rpx}\r\n.order-tab2{display:flex;width:auto;min-width:100%}\r\n.order-tab2 .item{width:20%;padding:0 20rpx;font-size:28rpx;font-weight:bold;text-align: center; color:#999999; height:80rpx; line-height:80rpx; overflow: hidden;position:relative;flex-shrink:0;flex-grow: 1;}\r\n.order-tab2 .on{color:#222222;}\r\n.order-tab2 .after{display:none;position:absolute;left:50%;margin-left:-20rpx;bottom:10rpx;height:6rpx;border-radius:1.5px;width:40rpx}\r\n.order-tab2 .on .after{display:block}\r\n\r\n\r\n.content{width:94%;margin:20rpx 3%;background:#fff;border-radius:5px;padding:20rpx 40rpx; justify-content: space-between;}\r\n.content .f1{display:flex;align-items:center}\r\n.content .f1 image{ width: 140rpx; height: 140rpx; border-radius: 10rpx;}\r\n.content .f1 .t1{color:#2B2B2B;font-weight:bold;font-size:32rpx;margin-left:10rpx;}\r\n.content .f1 .t2{color:#999999;font-size:28rpx; background: #E8E8F7;color:#7A83EC; margin-left: 10rpx; padding:3rpx 20rpx; font-size: 20rpx; border-radius: 18rpx;}\r\n.content .f1 .t3{ margin-left:10rpx;display: block; height: 40rpx;line-height: 40rpx;}\r\n.content .f2{color:#2b2b2b;font-size:26rpx;line-height:42rpx;padding-bottom:20rpx;}\r\n.content .f3{height:96rpx;display:flex;align-items:center}\r\n.content .radio{flex-shrink:0;width: 36rpx;height: 36rpx;background: #FFFFFF;border: 3rpx solid #BFBFBF;border-radius: 50%;}\r\n.content .radio .radio-img{width:100%;height:100%}\r\n.content .mrtxt{color:#2B2B2B;font-size:26rpx;margin-left:10rpx}\r\n\r\n\r\n.text2{ margin-left: 10rpx; color:#999999; font-size: 20rpx;margin-top: 10rpx;}\r\n.text3{ margin-left: 10rpx; color:#999999; font-size: 20rpx;margin-top: 10rpx;}\r\n.text3 .t5{ margin-left: 20rpx;}\r\n.text3 .t5 text{ color:#7A83EC}\r\n.text3 .t4 text{ color:#7A83EC}\r\n.yuyue{ background: #7A83EC; height: 40rpx; line-height: 40rpx; padding: 0 10rpx; color:#fff; border-radius:28rpx; width: 80rpx; font-size: 20rpx; text-align: center; margin-top: 20rpx;}\r\n.text1{ margin-left: 10rpx;}\r\n.container .btn-add{width:90%;max-width:700px;margin:0 auto;height:96rpx;line-height:96rpx;text-align:center;color:#fff;font-size:30rpx;font-weight:bold;border-radius:40rpx;position: fixed;left:0px;right:0;bottom:20rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./peolist.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./peolist.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213579719\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}