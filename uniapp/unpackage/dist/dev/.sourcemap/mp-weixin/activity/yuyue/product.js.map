{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/yuyue/product.vue?fe97", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/yuyue/product.vue?c802", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/yuyue/product.vue?674c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/yuyue/product.vue?9a11", "uni-app:///activity/yuyue/product.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/yuyue/product.vue?8cbc", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/yuyue/product.vue?c4ee"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "textset", "pre_url", "onLoad", "onShow", "console", "that", "onPullDownRefresh", "onShareAppMessage", "title", "pic", "onShareTimeline", "imageUrl", "query", "onUnload", "clearInterval", "methods", "getdata", "app", "id", "workerid", "uni", "swiper<PERSON><PERSON>e", "payvideo", "parsevideo", "buydialogChange", "currgg", "switchTopTab", "date", "proid", "key", "switchDateTab", "selectDate", "addfavorite", "type", "shareClick", "handleClickMask", "showPoster", "posterDialogClose", "showfuwudetail", "hidefuwudetail", "showcuxiaodetail", "hidecuxiaodetail", "getcoupon", "onPageScroll", "chooseTime", "hidetimeDialog", "setTimeout", "yydates", "sorts"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClMA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkYz1B;AACA;AAAA,eAEA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,iDACA,6DACA,uDACA,sDACA,uDACA,kDACA,4DACA,8DACA,wDACA,oDACA,oDACA,wDACA,yDACA,uDACA,uDACA,qDACA,wDACA,oDACA,mDACA,kDACA,qDACA,qDACA,6DACA,0DACA,yDACA,0DACA,qDACA,mDACA,sDACA,0DACA,wDACA,oDACA,wDACA,iDACA,+CACA,iDACA,yDACA,gDACA,qDACA,sDACA,uDACA,qDACA,wDAEA,wDACA,yDACA,uDACA,sDACA,mDACA,+DACA,gEAEA,sDACA,qDACA,uDAEA,+DACA,yDACA,mDACA,yDACA,qDACA,mDACA,gDACA;EAEA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;MACAC;MACAC;QAAA;QAAA;QAAA;MAAA;MACAA;IACA;EACA;EACAC;IACA;EACA;EACAC;IAkBA;MAAAC;MAAAC;IAAA;EACA;EACAC;IACA;MAAAF;MAAAC;IAAA;IACA;IACAL;IACAA;IACA;MACAI;MACAG;MACAC;IACA;EACA;EACAC;IACAC;EACA;EAEAC;IACAC;MACA;MACA;MACAX;MACAY;QAAAC;QAAAC;MAAA;QAEA;UACAF;UACA;QACA;QACAZ;QACA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;QACAe;UACAZ;QACA;QACA;UACAH;UACA,yCACA;YACAA;YACAA;UACA;UACAA;UACAA;UACAA;QACA;QACAA;QACAA;QACAA;QACA;UACAA;QACA;QACA;UACAA;QACA;QAEA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;UACAA;UACAA;QACA;QACA;UACAA;UACA;YACAA;UACA;QACA;QACAA;QACAA;UAAAG;UAAAC;QAAA;QACA;UACA;UACAQ;YACAA;UACA;UAAA;QACA;MACA;IACA;IACAI;MACA;MACAhB;IACA;IACAiB;MACA;MACAF;IACA;IACAG;MACA;MACAH;IACA;IACAI;MACA;QACA;MACA;MACA;IACA;IACAC;MACArB;MACA;MACA;MACAC;MACAA;MACAA;IACA;IACAqB;MACA;MACA;MACA;MACA;MACArB;MACAA;MACA;MACA;MACA;MACA;MACAA;MACAY;QAAAU;QAAAC;QAAAC;MAAA;QACAxB;QACAA;MACA;IAEA;IACAyB;MACA;MACA;MACA;MACA;MACA;QACAb;QAAA;MACA;MACAZ;MACAA;MACAA;MACA;QAAAA;MAAA;MACAA;MACAA;IACA;IACA0B;MACA;MACA;QACA1B;MACA;MACA;QACAY;QAAA;MACA;MACA;QACAZ;MACA;QACAA;MACA;IACA;IACA;IACA2B;MACA;MACA;MACAf;QAAAW;QAAAK;MAAA;QACA;UACA5B;QACA;QACAY;MACA;IACA;IACAiB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA/B;MACAA;MACAY;MACAA;QAAAW;MAAA;QACAX;QACA;UACAA;QACA;UACAZ;QACA;MACA;IACA;IAEAgC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACAtC;MACA;MACA;QACAA;MACA;IACA;IAEA;IACAuC;MACA;MACAxC;MACA;QACAC;MACA;QACAA;MACA;MACAA;MACA;MACAA;MACAA;MACAY;QAAAU;QAAAC;QAAAC;QAAAV;MAAA;QACAd;QACAA;MACA;IAEA;IACAwC;MACA;MACA;QACAxC;MACA;QACAA;MACA;IACA;EAAA,6DACA;IACA;EACA,8DACA;IACA;EACA,uDACA,oBAKA,wDACA,qBA6CA,6DACA;IACA;EACA,8DACA;IACA;EACA,qDACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAD;IACA;IACA;MACA;MACA;IACA;IACA;MACAa;MACA6B;QACAzC;MACA;MACA;IACA;IAEA;MACA;MACA;QACAA;MACA;QACAA;MACA;MACA;IACA;IACA;MACA;QACAY;QACA;MACA;IACA;IACA;IACA;IACAA;IACA;IACA;IACA;MACA8B;IACA;MACAA;IACA;IACA9B;EACA,0DACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAA;EACA,2DACA;IACA;IACAZ;IACAA;IACA;IACAA;EACA,6DACA;IACA;IACA;IACA;IACAA;IACAA;IACAA;IACAA;IACAA;EACA,8DACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;MACAY;MAAA;IACA;IAEA;IACA;IACA;IACA;IAEA;IACA;IACA;MACA+B;MACA3C;MACA0C;IACA;MACA;MACA;QACAC;QACA;QACA;QACA;QACA;UACA/B;UAAA;QACA;QACAZ;MACA;QACAA;MACA;MACA2C;MAEA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;MAAA;MACAD;IACA;IACA1C;IACAA;IACAA;IACA;IACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;QACA;UACA;YACAA;UACA;YACAA;UACA;QACA;MACA;QACAA;MACA;IACA;MACAA;IACA;EACA,+DACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC/9BA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/yuyue/product.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/yuyue/product.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./product.vue?vue&type=template&id=36cb32ca&\"\nvar renderjs\nimport script from \"./product.vue?vue&type=script&lang=js&\"\nexport * from \"./product.vue?vue&type=script&lang=js&\"\nimport style0 from \"./product.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/yuyue/product.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=template&id=36cb32ca&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    dpGuanggao: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-guanggao/dp-guanggao\" */ \"@/components/dp-guanggao/dp-guanggao.vue\"\n      )\n    },\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    yybuydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/yybuydialog/yybuydialog\" */ \"@/components/yybuydialog/yybuydialog.vue\"\n      )\n    },\n    scrolltop: function () {\n      return import(\n        /* webpackChunkName: \"components/scrolltop/scrolltop\" */ \"@/components/scrolltop/scrolltop.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.sysset.showgzts ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.sysset.showgzts ? _vm.t(\"color1\") : null\n  var g0 = _vm.isload ? _vm.bboglist.length : null\n  var g1 = _vm.isload && _vm.isplay == 0 ? _vm.product.pics.length : null\n  var g2 =\n    _vm.isload && _vm.isplay == 0 && g1 > 1 ? _vm.product.pics.length : null\n  var m2 =\n    _vm.isload &&\n    _vm.set.show_free &&\n    _vm.product.min_price <= 0 &&\n    _vm.product.max_price <= 0\n      ? _vm.t(\"color1\")\n      : null\n  var m3 =\n    _vm.isload &&\n    !(\n      _vm.set.show_free &&\n      _vm.product.min_price <= 0 &&\n      _vm.product.max_price <= 0\n    )\n      ? _vm.t(\"color1\")\n      : null\n  var g3 = _vm.isload ? _vm.fuwulist.length : null\n  var g4 = _vm.isload && g3 > 0 ? _vm.fuwulist.length : null\n  var g5 = _vm.isload ? _vm.couponlist.length : null\n  var l0 =\n    _vm.isload && g5 > 0\n      ? _vm.__map(_vm.couponlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m4 = _vm.t(\"color1rgb\")\n          var m5 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m4: m4,\n            m5: m5,\n          }\n        })\n      : null\n  var m6 = _vm.isload && _vm.commentcount > 0 ? _vm.t(\"color1\") : null\n  var g6 = _vm.isload && _vm.commentcount > 0 ? _vm.commentlist.length : null\n  var m7 = _vm.isload && _vm.shopset.showjd == 1 ? _vm.t(\"color1\") : null\n  var m8 = _vm.isload && _vm.shopset.showjd == 1 ? _vm.t(\"color1rgb\") : null\n  var m9 =\n    _vm.isload && _vm.product.status == 1 && _vm.isfuwu ? _vm.t(\"color1\") : null\n  var m10 =\n    _vm.isload && _vm.product.status == 1 && !_vm.isfuwu\n      ? _vm.t(\"color1\")\n      : null\n  var m11 = _vm.isload && _vm.sharetypevisible ? _vm.getplatform() : null\n  var m12 =\n    _vm.isload && _vm.sharetypevisible && !(m11 == \"app\")\n      ? _vm.getplatform()\n      : null\n  var m13 =\n    _vm.isload && _vm.sharetypevisible && !(m11 == \"app\") && !(m12 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  var l1 =\n    _vm.isload && _vm.timeDialogShow\n      ? _vm.__map(_vm.datelist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m14 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m14: m14,\n          }\n        })\n      : null\n  var m15 = _vm.isload && _vm.timeDialogShow ? _vm.t(\"color1\") : null\n  var l2 =\n    _vm.isload && _vm.timeDialogShow2\n      ? _vm.__map(_vm.datelist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m16 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m16: m16,\n          }\n        })\n      : null\n  var m17 = _vm.isload && _vm.timeDialogShow2 ? _vm.t(\"color1\") : null\n  var l3 =\n    _vm.isload && _vm.timeDialogShow3\n      ? _vm.__map(_vm.datetimes, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m18 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m18: m18,\n          }\n        })\n      : null\n  var m19 = _vm.isload && _vm.timeDialogShow3 ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        m2: m2,\n        m3: m3,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        l0: l0,\n        m6: m6,\n        g6: g6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        l1: l1,\n        m15: m15,\n        l2: l2,\n        m17: m17,\n        l3: l3,\n        m19: m19,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<block v-if=\"sysset.showgzts\">\r\n\t\t\t<view style=\"width:100%;height:88rpx\"> </view>\r\n\t\t\t<view class=\"follow_topbar\">\r\n\t\t\t\t<view class=\"headimg\"><image :src=\"sysset.logo\"/></view>\r\n\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t<view class=\"i\">欢迎进入 <text :style=\"{color:t('color1')}\">{{sysset.name}}</text></view>\r\n\t\t\t\t\t<view class=\"i\">关注公众号享更多专属服务</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"sub\" @tap=\"showsubqrcode\" :style=\"{'background-color':t('color1')}\">立即关注</view>\r\n\t\t\t</view>\r\n\t\t\t<uni-popup id=\"qrcodeDialog\" ref=\"qrcodeDialog\" type=\"dialog\">\r\n\t\t\t\t<view class=\"qrcodebox\">\r\n\t\t\t\t\t<image :src=\"sysset.qrcode\" @tap=\"previewImage\" :data-url=\"sysset.qrcode\" class=\"img\"/>\r\n\t\t\t\t\t<view class=\"txt\">长按识别二维码关注</view>\r\n\t\t\t\t\t<view class=\"close\" @tap=\"closesubqrcode\">\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/close2.png'\" style=\"width:100%;height:100%\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\t\t</block>\r\n\t\t\r\n\t\t<dp-guanggao :guanggaopic=\"guanggaopic\" :guanggaourl=\"guanggaourl\"></dp-guanggao>\r\n\r\n\t\t<view style=\"position:fixed;top:15vh;left:20rpx;z-index:9;background:rgba(0,0,0,0.6);border-radius:20rpx;color:#fff;padding:0 10rpx\" v-if=\"bboglist.length>0\">\r\n\t\t\t<swiper style=\"position:relative;height:54rpx;width:350rpx;\" :autoplay=\"true\" :interval=\"5000\" :vertical=\"true\">\r\n\t\t\t\t<swiper-item v-for=\"(item, index) in bboglist\" :key=\"index\" @tap=\"goto\" :data-url=\"'/yuyue/yuyue/product?id=' + item.proid\" class=\"flex-y-center\">\r\n\t\t\t\t\t<image :src=\"item.headimg\" style=\"width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px\"/>\r\n\t\t\t\t\t<view style=\"width:300rpx;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;font-size:22rpx\">{{item.nickname}} {{item.showtime}}购买了该商品</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\t\t<!-- <view class=\"topbox\"><image :src=\"pre_url+'/static/img/goback.png'\" class=\"goback\" /></view> -->\r\n\t\t<view class=\"swiper-container\" v-if=\"isplay==0\" >\r\n\t\t\t<swiper class=\"swiper\" :indicator-dots=\"false\" :autoplay=\"true\" :interval=\"500000\" @change=\"swiperChange\">\r\n\t\t\t\t<block v-for=\"(item, index) in product.pics\" :key=\"index\">\r\n\t\t\t\t\t<swiper-item class=\"swiper-item\">\r\n\t\t\t\t\t\t<view class=\"swiper-item-view\"><image class=\"img\" :src=\"item\" mode=\"widthFix\"/></view>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</block>\r\n\t\t\t</swiper>\r\n\t\t\t<view class=\"imageCount\" v-if=\"(product.pics).length > 1\">{{current+1}}/{{(product.pics).length}}</view>\r\n\t\t\t<view v-if=\"product.video\" class=\"provideo\" @tap=\"payvideo\"><image :src=\"pre_url+'/static/img/video.png'\"/><view class=\"txt\">{{product.video_duration}}</view></view>\r\n\t\t</view>\r\n\t\r\n\t\t<view class=\"header\"> \r\n\t\t\t<view class=\"price_share\">\r\n\t\t\t\t<view class=\"title\">{{product.name}}</view>\r\n\t\t\t\t<view class=\"share\" @tap=\"shareClick\"><image class=\"img\" :src=\"pre_url+'/static/img/share.png'\"/><text class=\"txt\">分享</text></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"pricebox flex\">\r\n\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"set.show_free && product.min_price <= 0 && product.max_price <= 0\">免费</view>\r\n\t\t\t\t\t<view class=\"f1\" :style=\"{color:t('color1')}\" v-else>\r\n\t\t\t\t\t\t{{product.min_price}}<text v-if=\"product.max_price!=product.min_price\">-{{product.max_price}}</text><text style=\"font-size:24rpx;font-weight:normal;padding-left:6rpx\">元/{{product.danwei}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2\" v-if=\"product.market_price*1 > product.sell_price*1\">￥{{product.market_price}}<text v-if=\"product.max_price!=product.min_price\">起</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"sales_stock\">\r\n\t\t\t\t\t<view class=\"f1\" >已售：{{product.sales}} </view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"sellpoint\" v-if=\"product.sellpoint\">{{product.sellpoint}}</view>\r\n\t\t\r\n\t\t\t<view style=\"margin:20rpx 0;color:red;font-size:22rpx\" v-if=\"product.balance_price > 0\">定金金额：{{product.advance_price}}元，尾款金额：{{product.balance_price}}元</view>\r\n\t\t\t\r\n\t\t\t<view class=\"cuxiaodiv\" v-if=\"fuwulist.length>0\">\r\n\t\t\t\t<view class=\"fuwupoint\" v-if=\"fuwulist.length>0\">\r\n\t\t\t\t\t<view class=\"f1\" @tap=\"showfuwudetail\">\r\n\t\t\t\t\t\t<view class=\"t\" v-for=\"(item, index) in fuwulist\" :key=\"index\">{{item.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2\" @tap=\"showfuwudetail\">\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrow-point.png'\" mode=\"widthFix\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"cuxiaopoint\" v-if=\"couponlist.length>0\">\r\n\t\t\t<view class=\"f0\">优惠</view>\r\n\t\t\t<view class=\"f1\" @tap=\"showcuxiaodetail\">\r\n\t\t\t\t<view v-for=\"(item, index) in couponlist\" :key=\"index\" class=\"t\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\"><text class=\"t0\" style=\"padding:0 6px\">券</text><text class=\"t1\">{{item.name}}</text></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f2\" @tap=\"showcuxiaodetail\">\r\n\t\t\t\t<image :src=\"pre_url+'/static/img/arrow-point.png'\" mode=\"widthFix\"/>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"choosebox\">\r\n      <view v-if=\"showselectpeople && product.fwpeople==1\" @tap=\"gotopeople\"  class=\"choosedate\" style=\"border-bottom: 2rpx solid #eee;\">\r\n      \t<view class=\"f0\">服务人员</view>\r\n      \t<view class=\"f1 flex1\">{{worker?worker.realname:'请选择人员'}}</view>\r\n      \t<image class=\"f2\" :src=\"pre_url+'/static/img/arrowright.png'\"/>\r\n      </view>\r\n\t\t\t<view class=\"choose\" @tap=\"buydialogChange\" data-btntype=\"2\">\r\n\t\t\t\t<view class=\"f0\">选择服务</view>\r\n\t\t\t\t<view class=\"flex1 flex-y-center\">\r\n\t\t\t\t\t<text class=\"xuanzefuwu-text\">{{ggname}}</text> \r\n\t\t\t\t\t<text v-if=\"num\">× {{num}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<image class=\"f2\" :src=\"pre_url+'/static/img/arrowright.png'\"/>\r\n\t\t\t</view>\r\n      <block v-if=\"!isfuwu\">\r\n        <block v-if=\"!selmoretime\">\r\n          <view class=\"choosedate\" @tap=\"chooseTime\">\r\n          \t<view class=\"f0\">服务时间</view>\r\n          \t<view class=\"f1 flex1\">{{yydate}}</view>\r\n          \t<image class=\"f2\" :src=\"pre_url+'/static/img/arrowright.png'\"/>\r\n          </view>\r\n        </block>\r\n        <block v-else>\r\n          <view class=\"choosedate\" @tap=\"chooseTime2\">\r\n          \t<view class=\"f0\">服务时间</view>\r\n          \t<view class=\"f1 flex1\">{{yydate}}</view>\r\n          \t<image class=\"f2\" :src=\"pre_url+'/static/img/arrowright.png'\"/>\r\n          </view>\r\n          <view class=\"choosedate\">\r\n          \t<view class=\"f0\">已选数量</view>\r\n          \t<view class=\"f1 flex1\">{{product.timejg}}分钟 X {{sortsnum}}</view>\r\n          </view>\r\n        </block>\r\n      </block>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"commentbox\" v-if=\"commentcount > 0\">\r\n\t\t\t<view class=\"title\">\r\n\t\t\t\t<view class=\"f1\">评价({{commentcount}})</view>\r\n\t\t\t\t<view class=\"f2\" @tap=\"goto\" :data-url=\"'commentlist?proid=' + product.id\">好评率 <text :style=\"{color:t('color1')}\">{{product.comment_haopercent}}%</text><image style=\"width:32rpx;height:32rpx;\" :src=\"pre_url+'/static/img/arrowright.png'\"/></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"comment\">\r\n\t\t\t\t<view class=\"item\" v-if=\"commentlist.length>0\">\r\n\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t<image class=\"t1\" :src=\"commentlist[0].headimg\"/>\r\n\t\t\t\t\t\t<view class=\"t2\">{{commentlist[0].nickname}}</view>\r\n\t\t\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t\t\t<view class=\"t3\"><image class=\"img\" v-for=\"(item2,index2) in [0,1,2,3,4]\" :key=\"index2\"  :src=\"pre_url+'/static/img/star' + (commentlist[0].score>item2?'2native':'') + '.png'\"/></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t<text class=\"t1\">{{commentlist[0].content}}</text>\r\n\t\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t\t<block v-if=\"commentlist[0].content_pic!=''\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(itemp, index) in commentlist[0].content_pic\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t<view @tap=\"previewImage\" :data-url=\"itemp\" :data-urls=\"commentlist[0].content_pic\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"itemp\" mode=\"widthFix\"/>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f3\" @tap=\"goto\" :data-url=\"'commentlist?proid=' + product.id\">查看全部评价</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else class=\"nocomment\">暂无评价~</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"shop\" v-if=\"shopset.showjd==1\">\r\n\t\t\t<image :src=\"business.logo\" class=\"p1\"/>\r\n\t\t\t<view class=\"p2 flex1\">\r\n\t\t\t\t<view class=\"t1\">{{business.name}}</view>\r\n\t\t\t\t<view class=\"t2\">{{business.desc}}</view>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"p4\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @tap=\"goto\" :data-url=\"product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid\" data-opentype=\"reLaunch\">进入店铺</button>\r\n\t\t</view>\r\n\t\t<view class=\"detail_title\"><view class=\"t1\"></view><view class=\"t2\"></view><view class=\"t0\">服务详情</view><view class=\"t2\"></view><view class=\"t1\"></view></view>\r\n\t\t<view class=\"detail\">\r\n\t\t\t<dp :pagecontent=\"pagecontent\"></dp>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- #ifdef MP-TOUTIAO -->\r\n\t\t<view class=\"dp-cover\" v-if=\"video_status\">\r\n\t\t\t<button open-type=\"share\" data-channel=\"video\" class=\"dp-cover-cover\" :style=\"{\r\n\t\t\t\tzIndex:10,\r\n\t\t\t\ttop:'60vh',\r\n\t\t\t\tleft:'80vw',\r\n\t\t\t\twidth:'110rpx',\r\n\t\t\t\theight:'110rpx'\r\n\t\t\t}\">\r\n\t\t\t\t<image :src=\"pre_url+'/static/img/uploadvideo2.png'\" :style=\"{width:'110rpx',height:'110rpx'}\"/>\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\r\n\t\t<view style=\"width:100%;height:140rpx;\"></view>\r\n\t\t<view class=\"bottombar flex-row\" :class=\"menuindex>-1?'tabbarbot':''\" v-if=\"product.status==1\">\r\n\t\t\t<view class=\"f1\">\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"'prolist?bid=' + product.bid\" >\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/shou.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">首页</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\" @tap=\"goto\" :data-url=\"kfurl\" v-if=\"kfurl!='contact::'\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/kefu.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"item\" v-else open-type=\"contact\" show-message-card=\"true\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/kefu.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">客服</view>\r\n\t\t\t\t</button>\r\n\t\t\t\t<view class=\"item\" @tap=\"addfavorite\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/shoucang.png'\"/>\r\n\t\t\t\t\t<view class=\"t1\">{{isfavorite?'已收藏':'收藏'}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"op\">\r\n\t\t\t\t<view class=\"tobuy flex-x-center flex-y-center\" :style=\"{background:t('color1')}\" v-if=\"isfuwu\" @tap=\"buydialogChange\" data-btntype=\"2\">立即预约</view>\r\n\t\t\t\t<view v-else class=\"tobuy flex-x-center flex-y-center\" @tap=\"tobuy\" :style=\"{background:t('color1')}\">立即预约</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<yybuydialog v-if=\"buydialogShow\" :proid=\"product.id\" :btntype=\"btntype\"  @currgg=\"currgg\" @buydialogChange=\"buydialogChange\" :menuindex=\"menuindex\" @addcart=\"addcart\" :isfuwu=\"isfuwu\"  @tobuy=\"tobuy\"></yybuydialog>\r\n\t\t<scrolltop :isshow=\"scrolltopshow\"></scrolltop>\r\n\r\n\t\t\r\n\t\t<view v-if=\"sharetypevisible\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t<view class=\"popup__modal\" style=\"height:320rpx;min-height:320rpx\">\r\n\t\t\t\t<!-- <view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请选择分享方式</text>\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\"/>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<view class=\"sharetypecontent\">\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- \t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<button class=\"f1\" open-type=\"share\" v-else-if=\"getplatform() != 'h5'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"showPoster\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharepic.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">生成分享图片</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"posterDialog\" v-if=\"showposter\">\r\n\t\t\t<view class=\"main\">\r\n\t\t\t\t<view class=\"close\" @tap=\"posterDialogClose\"><image class=\"img\" :src=\"pre_url+'/static/img/close.png'\"/></view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"posterpic\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"posterpic\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view v-if=\"timeDialogShow\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidetimeDialog\"></view>\r\n\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请选择时间</text>\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\r\n\t\t\t\t\t\**********=\"hidetimeDialog\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"order-tab\">\r\n\t\t\t\t\t<view class=\"order-tab2\">\r\n\t\t\t\t\t\t<block v-for=\"(item, index) in datelist\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view  :class=\"'item ' + (curTopIndex == index ? 'on' : '')\" @tap=\"switchTopTab\" :data-index=\"index\" :data-id=\"item.id\" >\r\n\t\t\t\t\t\t\t\t<view class=\"datetext\">{{item.weeks}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"datetext2\">{{item.date}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"after\"  :style=\"{background:t('color1')}\"></view>\r\n\t\t\t\t\t\t\t</view>\t\t\t\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex daydate\">\r\n\t\t\t\t\t<block v-for=\"(item,index2) in timelist\" :key=\"index2\">\r\n\t\t\t\t\t\t<view :class=\"'date ' + ((timeindex==index2 && item.status==1) ? 'on' : '') + (item.status==0 ?'hui' : '') \"  @tap=\"switchDateTab\" :data-index2=\"index2\" :data-status=\"item.status\" :data-time=\"item.timeint\"> {{item.time}}</view>\t\t\t\t\t\t\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"op\">\r\n\t\t\t\t\t<button class=\"tobuy on\" :style=\"{backgroundColor:t('color1')}\" @tap=\"selectDate\" >确 定</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<view v-if=\"timeDialogShow2\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidetimeDialog\"></view>\r\n\t\t\t<view class=\"popup__modal\"  style=\"height: 1000rpx;\">\r\n\t\t\t\t<view class=\"popup__title2\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/biao.png'\" class=\"popup_close2\" style=\"width:36rpx;height:36rpx\"\r\n\t\t\t\t\t\**********=\"hidetimeDialog\" />\r\n\t\t\t\t\t<text class=\"popup__title-text\">选择时间</text>\r\n\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"order-tab\">\r\n\t\t\t\t\t<view class=\"order-tab3\">\r\n\t\t\t\t\t\t<block v-for=\"(item, index) in datelist\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view  :class=\"'item ' + (curTopIndex == index ? 'on' : '')\" @tap.stop=\"switchTopTab\" :data-index=\"index\" :data-id=\"item.id\" >\r\n\t\t\t\t\t\t\t\t<view class=\"datetext\">{{item.weeks}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"datetext2\">{{item.date}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"after\"  :style=\"{background:t('color1')}\"></view>\r\n\t\t\t\t\t\t\t</view>\t\t\t\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"daydate2\">\r\n\t\t\t\t\t<view class=\"datebox\">\r\n\t\t\t\t\t\t<block v-for=\"(item,index2) in timelist\" :key=\"index2\">\r\n\t\t\t\t\t\t\t<view :class=\"'date ' + ((timeindex==index2 && item.status==1) ? 'on' : '') + (item.status==0 ?'hui' : '') \"  @tap.stop=\"switchDateTab\" :data-index2=\"index2\" :data-status=\"item.status\" :data-time=\"item.timeint\"> \r\n\t\t\t\t\t\t\t\t<text class=\"t1\">{{item.time}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"product.fwpeople==0\" >剩余 {{item.stock}}</text>\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</view>\t\t\t\t\t\t\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"op\">\r\n\t\t\t\t\t<button class=\"tobuy on\" :style=\"{backgroundColor:t('color1')}\" @tap=\"selectDate\" >确 定</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n    <view v-if=\"timeDialogShow3\" class=\"popup__container\">\r\n    \t<view class=\"popup__overlay\" @tap.stop=\"hidetimeDialog2\"></view>\r\n    \t<view class=\"popup__modal\">\r\n    \t\t<view class=\"popup__title\">\r\n    \t\t\t<text class=\"popup__title-text\">请选择时间</text>\r\n    \t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\r\n    \t\t\t\**********=\"hidetimeDialog2\" />\r\n    \t\t</view>\r\n    \t\t<view class=\"order-tab\">\r\n    \t\t\t<view class=\"order-tab2\">\r\n    \t\t\t\t<block v-for=\"(item, index) in datetimes\" :key=\"index\">\r\n    \t\t\t\t\t<view  :class=\"'item ' + (curTopIndex == index ? 'on' : '')\" @tap=\"switchTopTab2\" :data-index=\"index\" :data-id=\"item.id\" >\r\n    \t\t\t\t\t\t<view class=\"datetext\">{{item.weeks}}</view>\r\n    \t\t\t\t\t\t<view class=\"datetext2\">{{item.date}}</view>\r\n    \t\t\t\t\t\t<view class=\"after\"  :style=\"{background:t('color1')}\"></view>\r\n    \t\t\t\t\t</view>\t\t\t\r\n    \t\t\t\t</block>\r\n    \t\t\t</view>\r\n    \t\t</view>\r\n        <block v-for=\"(item2,index2) in datetimes\" :key=\"index3\">\r\n          <view v-if=\"curTopIndex == index2\" class=\"flex daydate\" style=\"justify-content: space-around;font-size: 26rpx;\">\r\n            <block v-for=\"(item3,index3) in item2.times\" :key=\"index3\">\r\n              <view :class=\"'date ' + ((item3.issel && item3.status==1) ? 'on' : '') + (item3.status==0 ?'hui' : '') \"  @tap=\"switchDateTab2\" :data-sort=\"item3.sort\" :data-index=\"index2\" :data-index2=\"index3\" :data-status=\"item3.status\" :data-year=\"item2.year\" :data-date=\"item2.date\" :data-time=\"item3.time\" :data-time2=\"item3.time2\" :data-timeint=\"item3.timeint\" style=\"min-width: 19%;width: auto;\"> {{item3.timerange}}</view>\t\t\t\t\t\t\r\n            </block>\r\n          </view>\r\n        </block>\r\n        <view class=\"selsortsnum\">\r\n          <view>已选数量(最少选择{{product.datetype1_modelselnum}}个连续时间段)</view>\r\n          <view>{{product.timejg}}分钟 X {{sortsnum}}</view>\r\n        </view>\r\n    \t\t<view class=\"op\">\r\n    \t\t\t<button class=\"tobuy on\" :style=\"{backgroundColor:t('color1')}\" @tap=\"hidetimeDialog2\">确定</button>\r\n    \t\t</view>\r\n    \t</view>\r\n    </view>\r\n\r\n\t\t<view v-if=\"showfuwudialog\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidefuwudetail\"></view>\r\n\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t\t<text class=\"popup__title-text\">服务</text>\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidefuwudetail\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in fuwulist\" :key=\"index\" class=\"service-item\">\r\n\t\t\t\t\t\t\t<view class=\"fuwudialog-content\">\r\n\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t<text class=\"f2\">{{item.desc}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar interval = null;\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n\t\t\tisload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\ttextset:{},\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\tisload:false,\r\n\t\t\tbuydialogShow: false,\r\n\t\t\tbtntype:1,\r\n\t\t\tisfavorite: false,\r\n\t\t\tcurrent: 0,\r\n\t\t\tisplay: 0,\r\n\t\t\tshowcuxiaodialog: false,\r\n\t\t\tshowfuwudialog:false,\r\n\t\t\tbusiness: \"\",\r\n\t\t\tproduct: [],\r\n\t\t\tcartnum: \"\",\r\n\t\t\tcommentlist: \"\",\r\n\t\t\tcommentcount: \"\",\r\n\t\t\tcuxiaolist: \"\",\r\n\t\t\tcouponlist: \"\",\r\n\t\t\tfuwulist: [],\r\n\t\t\tpagecontent: \"\",\r\n\t\t\tshopset: {},\r\n\t\t\tsysset:{},\r\n\t\t\ttitle: \"\",\r\n\t\t\tbboglist: \"\",\r\n\t\t\tsharepic: \"\",\r\n\t\t\tsharetypevisible: false,\r\n\t\t\tshowposter: false,\r\n\t\t\tposterpic: \"\",\r\n\t\t\tscrolltopshow: false,\r\n\t\t\tkfurl:'',\r\n\t\t\tggname:'请选择服务',\r\n      ggid:0,\r\n\t\t\ttimeDialogShow: false,\r\n\t\t\tdatelist:[],\r\n\t\t\tdaydate:[],\r\n\t\t\tcurTopIndex: 0,\r\n\t\t\tindex:0,\r\n\t\t\tday: -1,\r\n\t\t\tdays:'请选择服务时间',\r\n\t\t\tdates:'',\r\n\t\t\tnum:0,\r\n\t\t\ttimeindex:-1,\r\n\t\t\tstartTime:0,\r\n\t\t\tselectDates:'',\r\n\t\t\ttimelist:[],\r\n\t\t\t\r\n\t\t\tguanggaopic: \"\",\r\n\t\t\tguanggaourl: \"\",\r\n\t\t\tvideo_status:0,\r\n\t\t\tvideo_title:'',\r\n\t\t\tvideo_tag:[],\r\n\t\t\tisfuwu:false,\r\n\t\t\ttimeDialogShow2:false,\r\n      \r\n      showselectpeople:false,\r\n      worker:'',\r\n      workerid:0,\r\n      \r\n      selmoretime:false,//是否需要多选\r\n      timeDialogShow3:false,\r\n      datetimes:[],\r\n      yydate :'请选择服务时间',\r\n      sorts  :[],\r\n      sortsnum:0,\r\n      yydates:[],\r\n      set:[]\r\n\t\t};\r\n\t},\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n    this.workerid = this.opt.workerid || 0;\r\n\t\tthis.getdata();\r\n  },\r\n  onShow:function(){\r\n    var that = this;\r\n    var pages = getCurrentPages(); //获取加载的页面\r\n    var currentPage = pages[pages.length - 1]; //获取当前页面的对象\r\n    if(currentPage && currentPage.$vm.workerid && currentPage.$vm.realname){\r\n        console.log(currentPage.$vm.workerid)\r\n        that.worker    = {'id': currentPage.$vm.workerid,'realname':currentPage.$vm.realname,'tel':currentPage.$vm.tel};\r\n        that.workerid =  currentPage.$vm.workerid;\r\n    }\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonShareAppMessage:function(shareOption){\r\n\t\t//#ifdef MP-TOUTIAO\r\n\t\tconsole.log(shareOption);\r\n\t\t\treturn {\r\n\t\t\t\ttitle: this.video_title,\r\n\t\t\t\tchannel: \"video\",\r\n\t\t\t\textra: {\r\n\t\t\t\t\thashtag_list: this.video_tag,\r\n\t\t\t\t},\r\n\t\t\t\tsuccess: () => {\r\n\t\t\t\t\tconsole.log(\"分享成功\");\r\n\t\t\t\t},\r\n\t\t\t\t fail: (res) => {\r\n\t\t\t\t    console.log(res);\r\n\t\t\t\t    // 可根据 res.errCode 处理失败case\r\n\t\t\t\t  },\r\n\t\t\t};\r\n\t\t//#endif\r\n\t\treturn this._sharewx({title:this.product.name,pic:this.product.pic});\r\n\t},\r\n\tonShareTimeline:function(){\r\n\t\tvar sharewxdata = this._sharewx({title:this.product.name,pic:this.product.pic});\r\n\t\tvar query = (sharewxdata.path).split('?')[1]+'&seetype=circle';\r\n\t\tconsole.log(sharewxdata)\r\n\t\tconsole.log(query)\r\n\t\treturn {\r\n\t\t\ttitle: sharewxdata.title,\r\n\t\t\timageUrl: sharewxdata.imageUrl,\r\n\t\t\tquery: query\r\n\t\t}\r\n\t},\r\n\tonUnload: function () {\r\n\t\tclearInterval(interval);\r\n\t},\r\n\r\n\tmethods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = this.opt.id || 0;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiYuyue/product', {id: id,workerid:that.workerid}, function (res) {\r\n\t\t\t\t\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthat.textset = app.globalData.textset;\r\n\t\t\t\tvar product = res.product;\r\n\t\t\t\tvar pagecontent = JSON.parse(product.detail);\r\n\t\t\t\tthat.business = res.business;\r\n\t\t\t\tthat.product = product;\r\n\t\t\t\tthat.cartnum = res.cartnum;\r\n\t\t\t\tthat.commentlist = res.commentlist;\r\n\t\t\t\tthat.commentcount = res.commentcount;\r\n\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t\tthat.shopset = res.shopset;\r\n\t\t\t\tthat.sysset = res.sysset;\r\n\t\t\t\tthat.title = product.name;\r\n\t\t\t\tthat.isfavorite = res.isfavorite;\r\n\t\t\t\tthat.fuwulist = res.fuwulist2;\r\n\t\t\t\tthat.sharepic = product.pics[0];\r\n\t\t\t\tthat.isfuwu = res.isfuwu\r\n\t\t\t\t//that.couponlist = res.couponlist\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: product.name\r\n\t\t\t\t});\r\n\t\t\t\tif(res.set){\r\n\t\t\t\t\tthat.set = res.set;\r\n\t\t\t\t\tif(res.set.ad_status && res.set.ad_pic)\r\n\t\t\t\t\t{\t\r\n\t\t\t\t\t\tthat.guanggaopic = res.set.ad_pic;\r\n\t\t\t\t\t\tthat.guanggaourl = res.set.ad_link;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.video_status = res.set.video_status;\r\n\t\t\t\t\tthat.video_title = res.set.video_title;\r\n\t\t\t\t\tthat.video_tag = res.set.video_tag;\r\n\t\t\t\t}\r\n\t\t\t\tthat.datelist = res.datelist;\r\n\t\t\t\tthat.daydate = res.daydate;\r\n\t\t\t\tthat.kfurl = '/pages/kefu/index?bid='+product.bid;\r\n\t\t\t\tif(app.globalData.initdata.kfurl != ''){\r\n\t\t\t\t\tthat.kfurl = app.globalData.initdata.kfurl;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.business && that.business.kfurl){\r\n\t\t\t\t\tthat.kfurl = that.business.kfurl;\r\n\t\t\t\t}\r\n        \r\n        if(res.showselectpeople){\r\n          that.showselectpeople = true;\r\n        }\r\n        if(res.worker){\r\n          that.worker    = res.worker;\r\n          that.workerid  = res.worker['id'];\r\n        }\r\n        if(res.ggarr){\r\n          that.ggname = res.ggarr.ggname;\r\n          that.ggid   = res.ggarr.ggid\r\n          that.proid  = res.ggarr.proid\r\n          that.num    = res.ggarr.num\r\n        }\r\n        if(res.selmoretime){\r\n          that.selmoretime = true;\r\n          if(res.datetimes){\r\n            that.datetimes = res.datetimes;\r\n          }\r\n        }\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.loaded({title:product.name,pic:product.pic});\r\n        if(!product.is_open || product.is_open != 1){\r\n          var tipmsg = product.noopentip?product.noopentip:'停业中';\r\n          app.alert(tipmsg,function(){\r\n            app.goback();\r\n          });return;\r\n        }\r\n\t\t\t});\r\n\t\t},\r\n\t\tswiperChange: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.current = e.detail.current\r\n\t\t},\r\n\t\tpayvideo: function () {\r\n\t\t\tthis.isplay = 1;\r\n\t\t\tuni.createVideoContext('video').play();\r\n\t\t},\r\n\t\tparsevideo: function () {\r\n\t\t\tthis.isplay = 0;\r\n\t\t\tuni.createVideoContext('video').stop();\r\n\t\t},\r\n\t\tbuydialogChange: function (e) {\r\n\t\t\tif(!this.buydialogShow){\r\n\t\t\t\tthis.btntype = e.currentTarget.dataset.btntype;\r\n\t\t\t}\r\n\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t},\r\n\t\tcurrgg: function (e) {\r\n\t\t\tconsole.log(e);\r\n\t\t\tvar that = this\r\n\t\t\tthis.ggname = e.ggname;\r\n\t\t\tthat.ggid = e.ggid\r\n\t\t\tthat.proid = e.proid\r\n\t\t\tthat.num = e.num\r\n\t\t},\r\n\t\tswitchTopTab: function (e) {\r\n\t\t  var that = this;\r\n\t\t  var id = e.currentTarget.dataset.id;\r\n\t\t  var index = parseInt(e.currentTarget.dataset.index);\r\n\t\t  this.curTopIndex = index;\r\n\t\t  that.days = that.datelist[index].year+that.datelist[index].date\r\n\t\t  that.nowdate = that.datelist[index].nowdate\r\n\t\t   // if(!that.dates ){ that.dates = that.daydate[0] }\r\n\t\t  this.curIndex = -1;\r\n\t\t  this.curIndex2 = -1;\r\n\t\t  //检测服务时间是否可预约\r\n\t\t  that.loading = true;\r\n\t\t  app.get('ApiYuyue/isgetTime', { date: that.days,proid:this.opt.id, key:that.datelist[index].key}, function (res) {\r\n\t\t\t  that.loading = false;\r\n\t\t\t  that.timelist = res.data;\r\n\t\t  })\r\n\t\t\t\r\n\t\t},\r\n\t\tswitchDateTab: function (e) {\r\n\t\t  var that = this;\r\n\t\t  var index2 = parseInt(e.currentTarget.dataset.index2);\r\n\t\t  var timeint = e.currentTarget.dataset.time\r\n\t\t  var status = e.currentTarget.dataset.status\r\n\t\t  if(status==0){\r\n\t\t\t\tapp.error('此时间不可选择');return;\t\r\n\t\t  }\r\n      that.timeint   = timeint\r\n      that.timeindex = index2\r\n      that.starttime1= that.timelist[index2].time\r\n      if(!that.days || that.days=='请选择服务时间'){ that.days = that.datelist[0].year + that.datelist[0].date }\r\n      that.selectDates = that.starttime1;\r\n      that.yydate = that.days+' '+that.selectDates;\r\n\t\t},\r\n\t\tselectDate:function(e){\r\n\t\t\tvar that=this\r\n\t\t\tif(that.timeindex >= 0 && that.timelist[that.timeindex].status==0){\r\n\t\t\t\t\tthat.starttime1='';\r\n\t\t\t}\r\n\t\t\tif(!that.starttime1){\r\n\t\t\t\tapp.error('请选择服务时间');return;\r\n\t\t\t}\r\n\t\t\tif(that.product.showdatetype==1){\r\n\t\t\t\t\tthat.timeDialogShow2 = false;\r\n\t\t\t}else{\r\n\t\t\t\t\tthat.timeDialogShow = false;\r\n\t\t\t}\r\n\t\t},\r\n\t\t//收藏操作\r\n\t\taddfavorite: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tvar proid = that.product.id;\r\n\t\t\tapp.post('ApiYuyue/addfavorite', {proid: proid,type: 'yuyue'}, function (data) {\r\n\t\t\t\tif (data.status == 1) {\r\n\t\t\t\t\tthat.isfavorite = !that.isfavorite;\r\n\t\t\t\t}\r\n\t\t\t\tapp.success(data.msg);\r\n\t\t\t});\r\n\t\t},\r\n\t\tshareClick: function () {\r\n\t\t\tthis.sharetypevisible = true;\r\n\t\t},\r\n\t\thandleClickMask: function () {\r\n\t\t\tthis.sharetypevisible = false\r\n\t\t},\r\n\t\tshowPoster: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.showposter = true;\r\n\t\t\tthat.sharetypevisible = false;\r\n\t\t\tapp.showLoading('生成海报中');\r\n\t\t\tapp.post('ApiYuyue/getposter', {proid: that.product.id}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tif (data.status == 0) {\r\n\t\t\t\t\tapp.alert(data.msg);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.posterpic = data.poster;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tposterDialogClose: function () {\r\n\t\t\tthis.showposter = false;\r\n\t\t},\r\n\t\tshowfuwudetail: function () {\r\n\t\t\tthis.showfuwudialog = true;\r\n\t\t},\r\n\t\thidefuwudetail: function () {\r\n\t\t\tthis.showfuwudialog = false\r\n\t\t},\r\n\t\tshowcuxiaodetail: function () {\r\n\t\t\tthis.showcuxiaodialog = true;\r\n\t\t},\r\n\t\thidecuxiaodetail: function () {\r\n\t\t\tthis.showcuxiaodialog = false\r\n\t\t},\r\n\t\tgetcoupon:function(){\r\n\t\t\tthis.showcuxiaodialog = false;\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPageScroll: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar scrollY = e.scrollTop;     \r\n\t\t\tif (scrollY > 200) {\r\n\t\t\t\tthat.scrolltopshow = true;\r\n\t\t\t}\r\n\t\t\tif(scrollY < 150) {\r\n\t\t\t\tthat.scrolltopshow = false\r\n\t\t\t}\r\n\t\t},\t\r\n\t\t\r\n\t\t//选择时间 \r\n\t\tchooseTime: function(e) {\r\n\t\t\tvar that = this;\r\n\t\t\tconsole.log(that.product.showdatetype);\r\n\t\t\tif(that.product.showdatetype==1){\r\n\t\t\t\t\tthat.timeDialogShow2 = true;\r\n\t\t\t}else{\r\n\t\t\t\t\tthat.timeDialogShow = true;\r\n\t\t\t}\r\n\t\t\tthat.timeIndex = -1;\r\n\t\t\tvar curTopIndex = that.datelist[0];\r\n\t\t\tthat.nowdate = that.datelist[that.curTopIndex].year+that.datelist[that.curTopIndex].date;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiYuyue/isgetTime', { date: that.nowdate,proid:this.opt.id,key:that.datelist[that.curTopIndex].key,workerid:that.workerid}, function (res) {\r\n\t\t\t  that.loading = false;\r\n\t\t\t  that.timelist = res.data;\r\n\t\t\t})\r\n\t\t\t\r\n\t\t},\r\n    hidetimeDialog: function() {\r\n    \tvar that=this\r\n    \tif(that.product.showdatetype==1){\r\n    \t\t\tthat.timeDialogShow2 = false;\r\n    \t}else{\r\n    \t\t\tthat.timeDialogShow = false;\r\n    \t}\r\n    },\r\n\t\tshowfuwudetail: function () {\r\n\t\t\tthis.showfuwudialog = true;\r\n\t\t},\r\n\t\thidefuwudetail: function () {\r\n\t\t\tthis.showfuwudialog = false\r\n\t\t},\r\n\t\tsharemp:function(){\r\n\t\t\t// #ifdef H5\r\n\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\r\n\t\t\tthis.sharetypevisible = false\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tshareapp:function(){\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tvar that = this;\r\n\t\t\tthat.sharetypevisible = false;\r\n\t\t\tuni.showActionSheet({\r\n        itemList: ['发送给微信好友', '分享到微信朋友圈'],\r\n        success: function (res){\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tvar scene = 'WXSceneSession';\r\n\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tvar sharedata = {};\r\n\t\t\t\t\t\tsharedata.provider = 'weixin';\r\n\t\t\t\t\t\tsharedata.type = 0;\r\n\t\t\t\t\t\tsharedata.scene = scene;\r\n\t\t\t\t\t\tsharedata.title = that.product.name;\r\n\t\t\t\t\t\t//sharedata.summary = app.globalData.initdata.desc;\r\n\t\t\t\t\t\tsharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/yuyue/yuyue/product?scene=id_'+that.product.id+'-pid_' + app.globalData.mid;\r\n\t\t\t\t\t\tsharedata.imageUrl = that.product.pic;\r\n\t\t\t\t\t\tvar sharelist = app.globalData.initdata.sharelist;\r\n\t\t\t\t\t\tif(sharelist){\r\n\t\t\t\t\t\t\tfor(var i=0;i<sharelist.length;i++){\r\n\t\t\t\t\t\t\t\tif(sharelist[i]['indexurl'] == '/yuyue/yuyue/product'){\r\n\t\t\t\t\t\t\t\t\tsharedata.title = sharelist[i].title;\r\n\t\t\t\t\t\t\t\t\tsharedata.summary = sharelist[i].desc;\r\n\t\t\t\t\t\t\t\t\tsharedata.imageUrl = sharelist[i].pic;\r\n\t\t\t\t\t\t\t\t\tif(sharelist[i].url){\r\n\t\t\t\t\t\t\t\t\t\tvar sharelink = sharelist[i].url;\r\n\t\t\t\t\t\t\t\t\t\tif(sharelink.indexOf('/') === 0){\r\n\t\t\t\t\t\t\t\t\t\t\tsharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tif(app.globalData.mid>0){\r\n\t\t\t\t\t\t\t\t\t\t\t sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tsharedata.href = sharelink;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.share(sharedata);\r\n\t\t\t\t\t}\r\n        }\r\n      });\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tshowsubqrcode:function(){\r\n\t\t\tthis.$refs.qrcodeDialog.open();\r\n\t\t},\r\n\t\tclosesubqrcode:function(){\r\n\t\t\tthis.$refs.qrcodeDialog.close();\r\n\t\t},\r\n\t\ttobuy: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar ks = that.ks;\r\n\t\t\tvar proid = that.product.id;\r\n\t\t\tvar ggid = that.ggid;\r\n\t\t\tvar num = that.num;\r\n\t\t\tvar yydate = that.yydate;\r\n      console.log(yydate)\r\n\t\t\tvar prodata = proid + ',' + ggid + ',' + num;\r\n\t\t\tif(!ggid || ggid==undefined){\r\n\t\t\t\t\tthis.buydialogShow = !this.buydialogShow;\r\n\t\t\t\t\treturn;\r\n\t\t\t}\r\n      if(that.showselectpeople && that.product.fwpeople==1&& !that.workerid){\r\n        app.error('请选择服务人员');\r\n        setTimeout(function() {\r\n            that.gotopeople();\r\n        }, 600);\r\n        return;\r\n      }\r\n\r\n\t\t\tif(!yydate || yydate=='请选择服务时间' ){\r\n        //app.error('请选择服务时间');\r\n        if(!that.selmoretime){\r\n          that.chooseTime();\r\n        }else{\r\n          that.chooseTime2();\r\n        }\r\n        return;\r\n\t\t\t}\r\n      if(that.selmoretime){\r\n        if(that.sortsnum<that.product.datetype1_modelselnum){\r\n          app.error('服务时间最少选择'+that.product.datetype1_modelselnum+'个连续时间段');\r\n          return;\r\n        }\r\n      }\r\n\t\t\t//var str2 = yydate.replace('年', '/');\r\n\t\t\t//var str2 = str2.replace('月', '/');\r\n\t\t\tapp.setCache('yydate',yydate);\r\n\t\t\t//var timestamp = Date.parse(str2);\r\n      var yydates = that.yydates;\r\n      if(yydates && yydates.length>0){\r\n        yydates = JSON.stringify(yydates)\r\n      }else{\r\n        yydates = '';\r\n      }\r\n\t\t\tapp.goto('/yuyue/yuyue/buy?prodata=' + prodata+'&workerid=' + that.workerid+'&yydate=' + yydate+'&yydates=' + yydates);\r\n\t\t},\r\n    gotopeople:function(e){\r\n    \t\tvar that=this\r\n    \t\tvar yydate = that.yydate;\r\n    \t\t// if(!yydate || yydate=='请选择服务时间 ' ){\r\n    \t\t// \t//app.error('请选择服务时间');\r\n    \t\t//  \tthat.chooseTime();\r\n    \t\t// \treturn;\r\n    \t\t// }\r\n    \t\tapp.goto('selectpeople?prodata='+that.product.id+'&gotype=2');\r\n    },\r\n    chooseTime2: function(e) {\r\n    \tvar that = this;\r\n    \tthat.timeDialogShow3 = true;\r\n    \tthat.timeIndex  = -1;\r\n    \tvar curTopIndex = that.datetimes[0];\r\n    \tthat.nowdate    = that.datetimes[that.curTopIndex].year+that.datetimes[that.curTopIndex].date;\r\n    },\r\n    switchTopTab2: function (e) {\r\n      var that = this;\r\n      var id = e.currentTarget.dataset.id;\r\n      var index = parseInt(e.currentTarget.dataset.index);\r\n      that.curTopIndex = index;\r\n      that.days        = that.datetimes[index].year+that.datetimes[index].date\r\n      that.nowdate     = that.datetimes[index].nowdate\r\n      that.curIndex    = -1;\r\n      that.curIndex2   = -1;\r\n    },\r\n    switchDateTab2: function (e) {\r\n      var that = this;\r\n      var index2 = parseInt(e.currentTarget.dataset.index2);\r\n      var sort   = parseInt(e.currentTarget.dataset.sort);\r\n      var year    = e.currentTarget.dataset.year\r\n      var date    = e.currentTarget.dataset.date\r\n      var time    = e.currentTarget.dataset.time\r\n      var time2   = e.currentTarget.dataset.time2\r\n      var timeint = e.currentTarget.dataset.timeint\r\n\r\n      var status = e.currentTarget.dataset.status\r\n      if(status==0){\r\n    \t\tapp.error('此时间不可选择');return;\t\r\n      }\r\n    \r\n      var sorts       = that.sorts;\r\n      var yydates     = that.yydates;\r\n      var curTopIndex = that.curTopIndex;\r\n      var datetimes   = that.datetimes\r\n      \r\n      var pos = sorts.indexOf(sort);\r\n      //存在则删除\r\n      if(pos>=0){\r\n        sorts.splice(pos, 1);\r\n        that.datetimes[curTopIndex].times[index2].issel = false;\r\n        yydates.splice(pos, 1);\r\n      }else{\r\n        var len = sorts.length;\r\n        if(len>=1){\r\n          sorts.sort();\r\n          //查询选择的是否是相邻的数据;\r\n          var min = sorts[0] - sort;\r\n          var max = sort - sorts[len-1];\r\n          if(min>1 || max>1){\r\n            app.error('只能选择相邻的连续的时间段');return;  \r\n          }\r\n          that.datetimes[curTopIndex].times[index2].issel = true;\r\n        }else{\r\n          that.datetimes[curTopIndex].times[index2].issel = true;\r\n        }\r\n        sorts.push(sort);\r\n\r\n        var yydate = {'sort':sort,'year':year,'date':date ,'time':time ,'time2':time2 ,'timeint':timeint};\r\n        yydates.push(yydate);\r\n      }\r\n      that.sorts     = sorts;\r\n      that.sortsnum  = sorts.length;\r\n      that.yydates   = yydates;\r\n      var ylen  = yydates.length;\r\n      if(ylen>=1){\r\n        var ssort = yydates[0].sort;\r\n        var syear = yydates[0].year;\r\n        var sdate = yydates[0].date;\r\n        var stime = yydates[0].time;\r\n        var stime2 = yydates[0].time2;\r\n        if(ylen>1){\r\n          var esort  = yydates[ylen-1].sort;\r\n          var eyear  = yydates[ylen-1].year;\r\n          var edate  = yydates[ylen-1].date;\r\n          var etime  = yydates[ylen-1].time;\r\n          var etime2 = yydates[ylen-1].time2;\r\n          if(syear == eyear){\r\n            if(sdate == edate){\r\n              if(ssort<=esort){\r\n                that.yydate = syear+sdate+' '+stime+'-'+etime2;\r\n              }else{\r\n                that.yydate = eyear+edate+' '+etime+'-'+stime2;\r\n              }\r\n            }else{\r\n              if(ssort<=esort){\r\n                that.yydate = syear+sdate+' '+stime+'-'+edate+' '+etime2;\r\n              }else{\r\n                that.yydate = eyear+edate+' '+etime+'-'+sdate+' '+stime2;\r\n              }\r\n            }\r\n          }else{\r\n            if(ssort<=esort){\r\n              that.yydate = syear+sdate+' '+stime+'-'+eyear+edate+' '+etime2;\r\n            }else{\r\n              that.yydate = eyear+edate+' '+etime+'-'+syear+sdate+' '+stime2;\r\n            }\r\n          }\r\n        }else{\r\n          that.yydate = syear+sdate+' '+stime;\r\n        }\r\n      }else{\r\n        that.yydate = '请选择服务时间'\r\n      }\r\n    },\r\n    hidetimeDialog2: function() {\r\n    \tthis.timeDialogShow3 = false;\r\n    },\r\n\t}\r\n};\r\n</script>\r\n<style>\r\n\t.dp-cover{height: auto; position: relative;}\r\n\t.dp-cover-cover{position:fixed;z-index:99999;cursor:pointer;display:flex;align-items:center;justify-content:center;overflow:hidden;background-color: inherit;}\r\n\t\r\n.follow_topbar {height:88rpx; width:100%;max-width:640px; background:rgba(0,0,0,0.8); position:fixed; top:0; z-index:13;}\r\n.follow_topbar .headimg {height:64rpx; width:64rpx; margin:6px; float:left;}\r\n.follow_topbar .headimg image {height:64rpx; width:64rpx;}\r\n.follow_topbar .info {height:56rpx; padding:16rpx 0;}\r\n.follow_topbar .info .i {height:28rpx; line-height:28rpx; color:#ccc; font-size:24rpx;}\r\n.follow_topbar .info {height:80rpx; float:left;}\r\n.follow_topbar .sub {height:48rpx; width:auto; background:#FC4343; padding:0 20rpx; margin:20rpx 16rpx 20rpx 0; float:right; font-size:24rpx; color:#fff; line-height:52rpx; border-radius:6rpx;}\r\n.qrcodebox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\r\n.qrcodebox .img{width:400rpx;height:400rpx}\r\n.qrcodebox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\r\n.qrcodebox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\r\n\r\n.goback{ position: absolute; top:0 ;width:64rpx ; height: 64rpx;z-index: 10000; margin: 30rpx;}\r\n.goback img{ width:64rpx ; height: 64rpx;}\r\n\r\n.swiper-container{position:relative}\r\n.swiper {width: 100%;height: 750rpx;overflow: hidden;}\r\n.swiper-item-view{width: 100%;height: 750rpx;}\r\n.swiper .img {width: 100%;height: 750rpx;overflow: hidden;}\r\n\r\n.imageCount {width:100rpx;height:50rpx;background-color: rgba(0, 0, 0, 0.3);border-radius:40rpx;line-height:50rpx;color:#fff;text-align:center;font-size:26rpx;position:absolute;right:13px;bottom:80rpx;}\r\n\r\n.provideo{background:rgba(255,255,255,0.7);width:160rpx;height:54rpx;padding:0 20rpx 0 4rpx;border-radius:27rpx;position:absolute;bottom:30rpx;left:50%;margin-left:-80rpx;display:flex;align-items:center;justify-content:space-between}\r\n.provideo image{width:50rpx;height:50rpx;}\r\n.provideo .txt{flex:1;text-align:center;padding-left:10rpx;font-size:24rpx;color:#333}\r\n\r\n.videobox{width:100%;height:750rpx;text-align:center;background:#000}\r\n.videobox .video{width:100%;height:650rpx;}\r\n.videobox .parsevideo{margin:0 auto;margin-top:20rpx;height:40rpx;line-height:40rpx;color:#333;background:#ccc;width:140rpx;border-radius:25rpx;font-size:24rpx;}\r\n\r\n.header {padding: 20rpx 3%;background: #fff; width: 94%; border-radius:10rpx; margin: auto; margin-bottom: 20rpx; margin-top: -60rpx; position: relative;}\r\n.header .pricebox{ width: 100%;border:1px solid #fff; justify-content: space-between;}\r\n.header .pricebox .price{display:flex;align-items:flex-end}\r\n.header .pricebox .price .f1{font-size:36rpx;color:#51B539;font-weight:bold}\r\n.header .pricebox .price .f2{font-size:26rpx;color:#C2C2C2;text-decoration:line-through;margin-left:30rpx;padding-bottom:5px}\r\n.header .price_share{width:100%;height:100rpx;display:flex;align-items:center;justify-content:space-between}\r\n.header .price_share .share{display:flex;flex-direction:column;align-items:center;justify-content:center}\r\n.header .price_share .share .img{width:32rpx;height:32rpx;margin-bottom:2px}\r\n.header .price_share .share .txt{color:#333333;font-size:20rpx}\r\n.header .title {color:#000000;font-size:32rpx;line-height:42rpx;font-weight:bold;}\r\n.header .sellpoint{font-size:28rpx;color: #666;padding-top:20rpx;}\r\n.header .sales_stock{height:60rpx;line-height:60rpx;font-size:24rpx;color:#777777; }\r\n.header .commission{display:inline-block;margin-top:20rpx;margin-bottom:10rpx;border-radius:10rpx;font-size:20rpx;height:44rpx;line-height:44rpx;padding:0 20rpx}\r\n\r\n.choosebox{margin: auto;width: 94%; border-radius:10rpx; background: #fff;  }\r\n\r\n.choose{ width: 100%;display:flex;align-items:center;justify-content: center; margin: auto; height: 88rpx;line-height: 88rpx;padding: 0 3%; color: #333; border-bottom:1px solid #eee }\r\n.choose .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:30rpx;display:flex;justify-content:center;align-items:center;min-width:150rpx ;}\r\n.choose .f2{ width: 32rpx; height: 32rpx;}\r\n.choose .xuanzefuwu-text{display: inline-block;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;max-width: 385rpx;}\r\n\r\n.choosedate{ display:flex;align-items:center;justify-content: center;   margin:auto; height: 88rpx;padding: 0 3%; color: #333; }\r\n.choosedate .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:30rpx;display:flex;justify-content:center;align-items:center;min-width:150rpx ;}\r\n.choosedate .f2{ width: 32rpx; height: 32rpx;}\r\n\r\n.cuxiaodiv{background:#fff;margin-top:20rpx;}\r\n\r\n.fuwupoint{width:100%;font-size:24rpx;color:#333;height:88rpx;line-height:88rpx;padding:12rpx 0;display:flex;align-items:center}\r\n.fuwupoint .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:30rpx;display:flex;justify-content:center;align-items:center}\r\n.fuwupoint .f1{margin-right:20rpx;flex:1;display:flex;flex-wrap:nowrap;overflow:hidden}\r\n.fuwupoint .f1 .t{ padding:4rpx 20rpx 4rpx 0;color:#777;flex-shrink:0}\r\n.fuwupoint .f1 .t:before{content: \"\";display: inline-block;vertical-align: middle;\tmargin-top: -4rpx;margin-right: 10rpx;\twidth: 24rpx;\theight: 24rpx;\tbackground: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;background-size: 24rpx auto;}\r\n.fuwupoint .f2{flex-shrink:0;display:flex;align-items:center;width:32rpx;height: 32rpx;}\r\n.fuwupoint .f2 .img{width:32rpx;height:32rpx;}\r\n.fuwudialog-content{font-size:24rpx}\r\n.fuwudialog-content .f1{color:#333;height:80rpx;line-height:80rpx;font-weight:bold}\r\n.fuwudialog-content .f1:before{content: \"\";display: inline-block;vertical-align: middle;\tmargin-top: -4rpx;margin-right: 10rpx;\twidth: 24rpx;\theight: 24rpx;\tbackground: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTOU5O+Q5POU5POQ4O+U4PN80P+M4O+Q4O+Q4POQ5POQ4OuQ4O+Q4O+I4PuQ5PJxkAycAAAAPdFJOUwAf+VSoeAvzws7ka7miLboUzckAAADJSURBVBjTY2BgYGCMWVR5VIABDBid/gPBFwjP/JOzQKKtfjGIzf3fEUSJ/N8AJO21Iao3fQbqqA+AcLi/CzCwfGGAAn8HBnlFMIttBoP4R4b4C2BOzk8G3q8M5w3AnPsLGZj/MKwHW8b6/QED4y8G/QQQx14ZSHwCcWYkMOtvAHOAyvqnPf8KcuMvkAGZP9eDjAQaEO/AwDb/D0gj0GiQpRnTQIYIfUR1DopDGexVIZygz8ieC4B6WyzRBOJtBkZ/pAABBZUWOKgAispF5e7ibycAAAAASUVORK5CYII=') no-repeat;background-size: 24rpx auto;}\r\n.fuwudialog-content .f2{color:#777}\r\n\r\n.cuxiaopoint{width:100%;font-size:24rpx;color:#333;height:88rpx;line-height:88rpx;padding:12rpx 0;display:flex;align-items:center}\r\n.cuxiaopoint .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:20rpx;display:flex;justify-content:center;align-items:center}\r\n.cuxiaopoint .f1{margin-right:20rpx;flex:1;display:flex;flex-wrap:nowrap;overflow:hidden}\r\n.cuxiaopoint .f1 .t{margin-left:10rpx;border-radius:3px;font-size:24rpx;height:40rpx;line-height:40rpx;padding-right:10rpx;flex-shrink:0;overflow:hidden}\r\n.cuxiaopoint .f1 .t0{display:inline-block;padding:0 5px;}\r\n.cuxiaopoint .f1 .t1{padding:0 4px}\r\n.cuxiaopoint .f2{flex-shrink:0;display:flex;align-items:center;width:32rpx;height: 32rpx;}\r\n.cuxiaopoint .f2 .img{width:32rpx;height:32rpx;}\r\n.cuxiaodiv .cuxiaopoint{border-bottom:1px solid #E6E6E6;}\r\n.cuxiaodiv .cuxiaopoint:last-child{border-bottom:0}\r\n\r\n.popup__container{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height:auto;z-index:10;background:#fff}\r\n.popup__overlay{position: fixed;bottom: 0;left: 0;right: 0;width:100%;height: 100%;z-index: 11;opacity:0.3;background:#000}\r\n.popup__modal{width: 100%;position: absolute;bottom: 0;color: #3d4145;overflow-x: hidden;overflow-y: hidden;opacity:1;padding-bottom:20rpx;background: #fff;border-radius:20rpx 20rpx 0 0;z-index:12;min-height:600rpx;max-height:1600rpx;}\r\n.popup__title{text-align: center;padding:30rpx;position: relative;position:relative}\r\n.popup__title-text{font-size:32rpx}\r\n.popup__close{position:absolute;top:34rpx;right:34rpx}\r\n.popup__content{width:100%;max-height:880rpx;overflow-y:scroll;padding:20rpx 0;}\r\n.service-item{display: flex;padding:0 40rpx 20rpx 40rpx;}\r\n.service-item .prefix{padding-top: 2px;}\r\n.service-item .suffix{padding-left: 10rpx;}\r\n.service-item .suffix .type-name{font-size:28rpx; color: #49aa34;margin-bottom: 10rpx;}\r\n\r\n\r\n.shop{display:flex;align-items:center;width: 100%; background: #fff;  margin-top: 20rpx; padding: 20rpx 3%;position: relative; min-height: 100rpx;}\r\n.shop .p1{width:90rpx;height:90rpx;border-radius:6rpx;flex-shrink:0}\r\n.shop .p2{padding-left:10rpx}\r\n.shop .p2 .t1{width: 100%;height:40rpx;line-height:40rpx;overflow: hidden;color: #111;font-weight:bold;font-size:30rpx;}\r\n.shop .p2 .t2{width: 100%;height:30rpx;line-height:30rpx;overflow: hidden;color: #999;font-size:24rpx;margin-top:8rpx}\r\n.shop .p4{height:64rpx;line-height:64rpx;color:#FFFFFF;border-radius:32rpx;margin-left:20rpx;flex-shrink:0;padding:0 30rpx;font-size:24rpx;font-weight:bold}\r\n\r\n.detail{min-height:200rpx; width: 94%; margin: auto; border-radius: 10rpx;}\r\n\r\n.detail_title{width:100%;display:flex;align-items:center;justify-content:center;margin-top:40rpx;margin-bottom:30rpx}\r\n.detail_title .t0{font-size:28rpx;font-weight:bold;color:#222222;margin:0 20rpx}\r\n.detail_title .t1{width:12rpx;height:12rpx;background:rgba(253, 74, 70, 0.2);transform:rotate(45deg);margin:0 4rpx;margin-top:6rpx}\r\n.detail_title .t2{width:18rpx;height:18rpx;background:rgba(253, 74, 70, 0.4);transform:rotate(45deg);margin:0 4rpx}\r\n\r\n.commentbox{width:90%;background:#fff;padding:0 3%;border-radius:10rpx;margin: auto;margin-top:20rpx; }\r\n.commentbox .title{height:90rpx;line-height:90rpx;border-bottom:1px solid #DDDDDD;display:flex}\r\n.commentbox .title .f1{flex:1;color:#111111;font-weight:bold;font-size:30rpx}\r\n.commentbox .title .f2{color:#333;font-weight:bold;font-size:28rpx;display:flex;align-items:center}\r\n.commentbox .nocomment{height:100rpx;line-height:100rpx}\r\n\r\n.comment{display:flex;flex-direction:column;min-height:200rpx;}\r\n.comment .item{background-color:#fff;padding:10rpx 20rpx;display:flex;flex-direction:column;}\r\n.comment .item .f1{display:flex;width:100%;align-items:center;padding:10rpx 0;}\r\n.comment .item .f1 .t1{width:70rpx;height:70rpx;border-radius:50%;}\r\n.comment .item .f1 .t2{padding-left:10rpx;color:#333;font-weight:bold;font-size:30rpx;}\r\n.comment .item .f1 .t3{text-align:right;}\r\n.comment .item .f1 .t3 .img{width:24rpx;height:24rpx;margin-left:10rpx}\r\n.comment .item .score{ font-size: 24rpx;color:#f99716;}\r\n.comment .item .score image{ width: 140rpx; height: 50rpx; vertical-align: middle;  margin-bottom:6rpx; margin-right: 6rpx;}\r\n.comment .item .f2{display:flex;flex-direction:column;width:100%;padding:10rpx 0;}\r\n.comment .item .f2 .t1{color:#333;font-size:28rpx;}\r\n.comment .item .f2 .t2{display:flex;width:100%}\r\n.comment .item .f2 .t2 image{width:100rpx;height:100rpx;margin:10rpx;}\r\n.comment .item .f2 .t3{color:#aaa;font-size:24rpx;}\r\n.comment .item .f3{margin:20rpx auto;padding:0 30rpx;height:60rpx;line-height:60rpx;border:1px solid #E6E6E6;border-radius:30rpx;color:#111111;font-weight:bold;font-size:26rpx}\r\n\r\n.bottombar{ width: 100%; position: fixed;bottom: 0px; left: 0px; background: #fff;display:flex;height:100rpx;padding:0 30rpx 0 10rpx;align-items:center;}\r\n.bottombar .f1{flex:1;display:flex;align-items:center;margin-right:30rpx}\r\n.bottombar .f1 .item{display:flex;flex-direction:column;align-items:center;width:50%;position:relative}\r\n.bottombar .f1 .item .img{ width:44rpx;height:44rpx}\r\n.bottombar .f1 .item .t1{font-size:18rpx;color:#222222;height:30rpx;line-height:30rpx;margin-top:6rpx}\r\n.bottombar .op{width:60%;border-radius:36rpx;overflow:hidden;display:flex;}\r\n.bottombar .tocart{flex:1;height:72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold}\r\n.bottombar .tobuy{flex:1;height: 72rpx; line-height: 72rpx;color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold; background: #FD4A46; border-top-right-radius:20rpx;border-bottom-left-radius:20rpx;}\r\n.bottombar .cartnum{position:absolute;right:4rpx;top:-4rpx;color:#fff;border-radius:50%;width:32rpx;height:32rpx;line-height:32rpx;text-align:center;font-size:22rpx;}\r\n\r\n/*时间范围*/\r\n.datetab{ display: flex; border:1px solid red; width: 200rpx; text-align: center;}\r\n\r\n.popup__title2{ height:80rpx; display: flex;align-items: center; }\r\n.popup__title2 .popup_close2{ margin:0 10rpx; }\r\n.order-tab2{display:flex;width:auto;min-width:100%;overflow-x: scroll;}\r\n.order-tab2 .item{width:auto;font-size:28rpx;font-weight:bold;text-align: center; color:#999999;overflow: hidden;flex-shrink:0;flex-grow: 1; display: flex; flex-direction: column; justify-content: center; align-items: center; width: 20%;}\r\n.order-tab2 .item .datetext{ line-height: 60rpx; height:60rpx;}\r\n.order-tab2 .item .datetext2{ line-height: 60rpx; height:60rpx;font-size: 22rpx;}\r\n.order-tab2 .on{color:#222222;}\r\n.order-tab2 .after{display:none;margin-left:-10rpx;bottom:5rpx;height:6rpx;border-radius:1.5px;width:70rpx}\r\n.order-tab2 .on .after{display:block}\r\n.daydate{ padding:20rpx; flex-wrap: wrap; overflow-y: scroll; height:400rpx;}\r\n.daydate .date{width: 20%;text-align: center;line-height: 60rpx;height: 60rpx; margin-top: 30rpx;}\r\n.daydate .on{ background:red; color:#fff;}\r\n.daydate .hui{ border:1px solid #f0f0f0; background:#f0f0f0;border-radius: 5rpx;}\r\n.tobuy{flex:1;height: 72rpx; line-height: 72rpx; color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold;width:90%;margin:20rpx 5%;border-radius:36rpx;}\r\n\r\n.order-tab .order-tab3{\tdisplay:flex;width:auto;min-width:100%;overflow-x: scroll;}\r\n.order-tab3 .item{width:auto;font-size:28rpx;font-weight:bold;text-align: center; color:#999999;overflow: hidden;flex-shrink:0;flex-grow: 1; display: flex; flex-direction: column; justify-content: center; align-items: center; width: 120rpx; height: 120rpx; border:1rpx solid #E9E9E9; margin-left: 20rpx; border-radius: 10rpx;}\r\n.order-tab3 .item .datetext{ color: #4F9BF2;}\r\n.order-tab3 .on{color:#222222; background:#EBF1DE; border:1rpx solid #49C54E}\r\n.order-tab3 .on .after{display:block}\r\n\r\n.daydate2{ padding:20rpx; overflow-y: scroll; height:660rpx; }\r\n.daydate2 .datebox{ width:100%; display: flex; flex-wrap: wrap;}\r\n.daydate2 .date{ width:30%;text-align: center; margin-top: 30rpx; margin-left: 10rpx; border-radius: 10rpx;border:1px solid #E9E9E9; display: flex; flex-direction: column; padding:20rpx 0;max-height: 120rpx; height: auto; } \r\n.daydate2 .date .t2{ color: #3D91F1;}\r\n.daydate2 .on{ background:#EBF1DE;border: 0.5px solid #49C54E; }\r\n.daydate2 .hui{ border:1px solid #E9E9E9; border-radius: 5rpx; color:#999}\r\n.daydate2 .date.hui .t2{ color: #999;}\r\n.tobuy{flex:1;height: 72rpx; line-height: 72rpx; color: #fff; border-radius: 0px; border: none;font-size:28rpx;font-weight:bold;width:90%;margin:20rpx 5%;border-radius:36rpx;}\r\n\r\n\r\n\r\n.cuxiaopoint{width:100%;font-size:24rpx;color:#333;height:88rpx;line-height:88rpx;padding:12rpx 0;display:flex;align-items:center}\r\n.cuxiaopoint .f0{color:#555;font-weight:bold;height:32rpx;font-size:24rpx;padding-right:20rpx;display:flex;justify-content:center;align-items:center}\r\n.cuxiaopoint .f1{margin-right:20rpx;flex:1;display:flex;flex-wrap:nowrap;overflow:hidden}\r\n.cuxiaopoint .f1 .t{margin-left:10rpx;border-radius:3px;font-size:24rpx;height:40rpx;line-height:40rpx;padding-right:10rpx;flex-shrink:0;overflow:hidden}\r\n.cuxiaopoint .f1 .t0{display:inline-block;padding:0 5px;}\r\n.cuxiaopoint .f1 .t1{padding:0 4px}\r\n.cuxiaopoint .f2{flex-shrink:0;display:flex;align-items:center;width:32rpx;height: 32rpx;}\r\n.cuxiaopoint .f2 .img{width:32rpx;height:32rpx;}\r\n.cuxiaodiv .cuxiaopoint{border-bottom:1px solid #E6E6E6;}\r\n.cuxiaodiv .cuxiaopoint:last-child{border-bottom:0}\r\n.selsortsnum{display: flex;justify-content: space-between;padding: 20rpx;background:#f0f0f0;border-radius: 4rpx;width: 98%;margin: 0 auto;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213579881\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}