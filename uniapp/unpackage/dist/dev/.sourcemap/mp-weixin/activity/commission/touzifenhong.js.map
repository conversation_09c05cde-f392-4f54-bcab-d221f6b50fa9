{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/touzifenhong.vue?330d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/touzifenhong.vue?3d7d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/touzifenhong.vue?524a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/touzifenhong.vue?96bb", "uni-app:///activity/commission/touzifenhong.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/touzifenhong.vue?264a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/touzifenhong.vue?1b5b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "st", "count", "commissionyj", "pagenum", "datalist", "nodata", "nomore", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "changetab", "uni", "scrollTop", "duration"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxGA;AAAA;AAAA;AAAA;AAA00B,CAAgB,0yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiE91B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;QAAAb;QAAAG;MAAA;QACAS;QACA;QACA;UACA;YACAA;YACAA;UACA;UACAA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAE;MACA;MACA;MACA;MACAC;QACAC;QACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/IA;AAAA;AAAA;AAAA;AAAurC,CAAgB,umCAAG,EAAC,C;;;;;;;;;;;ACA3sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/commission/touzifenhong.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/commission/touzifenhong.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./touzifenhong.vue?vue&type=template&id=108195d0&\"\nvar renderjs\nimport script from \"./touzifenhong.vue?vue&type=script&lang=js&\"\nexport * from \"./touzifenhong.vue?vue&type=script&lang=js&\"\nimport style0 from \"./touzifenhong.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/commission/touzifenhong.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./touzifenhong.vue?vue&type=template&id=108195d0&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.datalist && _vm.datalist.length > 0 : null\n  var l0 =\n    _vm.isload && g0 && _vm.st == 1\n      ? _vm.__map(_vm.datalist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.t(\"color1\")\n          var m1 = _vm.dateFormat(item.createtime)\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n          }\n        })\n      : null\n  var l2 =\n    _vm.isload && g0 && _vm.st == 2\n      ? _vm.__map(_vm.datalist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m2 = _vm.dateFormat(item.createtime)\n          var m3 = _vm.t(\"color1\")\n          var l1 = _vm.__map(item.oglist, function (item2, index) {\n            var $orig = _vm.__get_orig(item2)\n            var m4 = _vm.dateFormat(item2.createtime)\n            return {\n              $orig: $orig,\n              m4: m4,\n            }\n          })\n          return {\n            $orig: $orig,\n            m2: m2,\n            m3: m3,\n            l1: l1,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        l2: l2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./touzifenhong.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./touzifenhong.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"topfix\">\r\n\t\t\t<view class=\"toplabel\">\r\n\t\t\t\t<text class=\"t1\">分红订单（{{count}}）</text>\r\n\t\t\t\t<text class=\"t2\">预计：+{{commissionyj}}元</text>\r\n\t\t\t</view>\r\n\t\t\t<dd-tab :itemdata=\"['待结算','已结算']\" :itemst=\"['1','2']\" :st=\"st\" :isfixed=\"false\" @changetab=\"changetab\"></dd-tab>\r\n\t\t</view>\r\n\t\t<view style=\"margin-top:190rpx\"></view>\r\n\t\t<block v-if=\"datalist && datalist.length>0\">\r\n\t\t<view class=\"content\">\r\n\t\t\t<block v-if=\"st==1\">\r\n\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"item\">\r\n\t\t\t\t<view class=\"f1\"><text>{{item.ordernum}}</text></view>\r\n\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t<view class=\"t1\">\r\n\t\t\t\t\t\t<text class=\"x1\">{{item.name}} ×{{item.num}}</text>\r\n\t\t\t\t\t\t<text class=\"x2\" style=\"font-size:28rpx\" :style=\"{color:t('color1')}\">￥{{item.real_totalprice}}</text>\r\n\t\t\t\t\t\t<text class=\"x2\">{{dateFormat(item.createtime)}}</text>\r\n\t\t\t\t\t\t<view class=\"x3\"><image :src=\"item.headimg\"></image>{{item.nickname}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t<text class=\"x1\">+{{item.commission}}</text>\r\n\t\t\t\t\t\t<text class=\"dior-sp6 yfk\" v-if=\"item.status==1 || item.status==2\">已付款</text>\r\n\t\t\t\t\t\t<text class=\"dior-sp6 dfk\" v-if=\"item.status==0\">待付款</text>\r\n\t\t\t\t\t\t<text class=\"dior-sp6 ywc\" v-if=\"item.status==3\">已完成</text>\r\n\t\t\t\t\t\t<text class=\"dior-sp6 ygb\" v-if=\"item.status==4\">已关闭</text>\r\n\t\t\t\t\t\t<text class=\"dior-sp6 ygb\" v-if=\"item.refund_money > 0\">退款/售后</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"st==2\">\r\n\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"item\">\r\n\t\t\t\t<view class=\"f1\"><text>{{item.remark}} {{dateFormat(item.createtime)}}</text></view>\r\n\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t<view class=\"t1\">\r\n\t\t\t\t\t\t<view v-for=\"(item2, index) in item.oglist\" :key=\"index\" class=\"item2\">\r\n\t\t\t\t\t\t\t<text class=\"x1\">{{item2.name}} ×{{item2.num}}</text>\r\n\t\t\t\t\t\t\t<text class=\"x2\" style=\"font-size:28rpx\" :style=\"{color:t('color1')}\">￥{{item2.real_totalprice}}</text>\r\n\t\t\t\t\t\t\t<text class=\"x2\">{{dateFormat(item2.createtime)}}</text>\r\n\t\t\t\t\t\t\t<view class=\"x3\"><image :src=\"item2.headimg\"></image>{{item2.nickname}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t<text class=\"x1\">+{{item.commission}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t\t</block>\r\n\t\t<view style=\"width:100%;height:20rpx\"></view>\r\n\t</block>\r\n\t<nodata v-if=\"nodata\"></nodata>\r\n\t<nomore v-if=\"nomore\"></nomore>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\r\n      st: '1',\r\n\t\t\tcount:0,\r\n      commissionyj: 0,\r\n      pagenum: 1,\r\n      datalist: [],\r\n      nodata: false,\r\n      nomore: false\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.pagenum = 1;\r\n\t\tthis.datalist = [];\r\n\t\tthis.getdata();\r\n\t},\r\n  onReachBottom: function () {\r\n    if (!this.nodata && !this.nomore && this.st !=1) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getdata();\r\n    }\r\n  },\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n      var pagenum = that.pagenum;\r\n      var st = that.st;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n      that.nomore = false;\r\n\t\t\tapp.get('ApiAgent/touzifenhong',{st:st,pagenum: pagenum},function(res){\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tvar data = res.datalist;\r\n        if (pagenum == 1) {\r\n\t\t\t\t\tif(st == 1){\r\n\t\t\t\t\t\tthat.commissionyj = res.commissionyj;\r\n\t\t\t\t\t\tthat.count = res.count;\r\n\t\t\t\t\t}\r\n          that.datalist = data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n\t\t\t\t\tthat.loaded();\r\n        }else{\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n          }\r\n        }\r\n\t\t\t});\r\n\t\t},\r\n    changetab: function (st) {\r\n      this.pagenum = 1;\r\n      this.st = st;\r\n      this.datalist = [];\r\n      uni.pageScrollTo({\r\n        scrollTop: 0,\r\n        duration: 0\r\n      });\r\n      this.getdata();\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.topfix{width: 100%;position:relative;position:fixed;background: #f9f9f9;top:var(--window-top);z-index:11;}\r\n.toplabel{width: 100%;background: #f9f9f9;padding: 20rpx 20rpx;border-bottom: 1px #e3e3e3 solid;display:flex;}\r\n.toplabel .t1{color: #666;font-size:30rpx;flex:1}\r\n.toplabel .t2{color: #666;font-size:30rpx;text-align:right}\r\n\r\n.content{ width:100%;}\r\n.content .item{width:94%;margin-left:3%;border-radius:10rpx;background: #fff;margin-bottom:16rpx;}\r\n.content .item .f1{width:100%;padding: 16rpx 20rpx;color: #666;border-bottom: 1px #f5f5f5 solid;}\r\n.content .item .f2{display:flex;padding:20rpx;align-items:center}\r\n.content .item .f2 .t1{display:flex;flex-direction:column;flex:auto}\r\n.content .item .f2 .t1 .item2{display:flex;flex-direction:column;flex:auto;margin:10rpx 0;padding:10rpx 0;border-bottom:1px dotted #f5f5f5}\r\n.content .item .f2 .t1 .x2{color:#999;font-size:24rpx;height:40rpx;line-height:40rpx}\r\n.content .item .f2 .t1 .x3{display:flex;align-items:center}\r\n.content .item .f2 .t1 .x3 image{width:40rpx;height:40rpx;border-radius:50%;margin-right:4px}\r\n.content .item .f2 .t2{ width:360rpx;text-align:right;display:flex;flex-direction:column;}\r\n.content .item .f2 .t2 .x1{color: #000;height:44rpx;line-height: 44rpx;overflow: hidden;font-size:36rpx;}\r\n.content .item .f2 .t2 .x2{height:44rpx;line-height: 44rpx;overflow: hidden;}\r\n\r\n.dfk{color: #ff9900;}\r\n.yfk{color: red;}\r\n.ywc{color: #ff6600;}\r\n.ygb{color: #aaaaaa;}\r\n\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./touzifenhong.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./touzifenhong.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754212971775\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}