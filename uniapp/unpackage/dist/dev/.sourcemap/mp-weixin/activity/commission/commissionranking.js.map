{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/commissionranking.vue?dfae", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/commissionranking.vue?af4e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/commissionranking.vue?8459", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/commissionranking.vue?ae31", "uni-app:///activity/commission/commissionranking.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/commissionranking.vue?830e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/commissionranking.vue?fc9b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "nodata", "nomore", "datalist", "textset", "pagenum", "totalcommission", "commission", "rank_type", "sysset", "ranktype", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "uni", "title", "toranktype"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,0BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACqE;AACL;AACa;;;AAG7E;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvFA;AAAA;AAAA;AAAA;AAA+0B,CAAgB,+yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2Dn2B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACA;MAEAC;QAAAP;QAAAL;MAAA;QACAW;QACA;QACAA;QACAA;QACAA;QACAA;QACA;UACAC;QACA;QACA;UACAD;UACAE;YACAC;UACA;UACAH;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAI;MACA;MACA;MACAJ;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnJA;AAAA;AAAA;AAAA;AAA4rC,CAAgB,4mCAAG,EAAC,C;;;;;;;;;;;ACAhtC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/commission/commissionranking.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/commission/commissionranking.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./commissionranking.vue?vue&type=template&id=b7c5f8ae&\"\nvar renderjs\nimport script from \"./commissionranking.vue?vue&type=script&lang=js&\"\nexport * from \"./commissionranking.vue?vue&type=script&lang=js&\"\nimport style0 from \"./commissionranking.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/commission/commissionranking.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./commissionranking.vue?vue&type=template&id=b7c5f8ae&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.sysset.rank_type.length : null\n  var l0 =\n    _vm.isload && g0 > 1\n      ? _vm.__map(_vm.sysset.rank_type, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = item == _vm.ranktype ? _vm.t(\"color1\") : null\n          var m1 = item == _vm.ranktype ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n          }\n        })\n      : null\n  var l1 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m2 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m2: m2,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./commissionranking.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./commissionranking.vue?vue&type=script&lang=js&\"", "<template>\r\n<block v-if=\"isload\">\r\n<view class=\"container\"  >\r\n\t<view class=\"contentbox\">\r\n\t\t<image :src=\"pre_url + '/static/imgsrc/rank_notext.png'\" mode=\"widthFix\">\r\n\t\t\t<view class=\"title\">\r\n\t\t\t\t<view class=\"t1\">{{sysset.rank_title||'业绩排行榜'}}</view>\r\n\t\t\t\t<view class=\"t2\">{{sysset.rank_desc||'历史累积榜单'}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"top\">\r\n\t\t\t\t<view class=\"t1\">\r\n\t\t\t\t\t<view class=\"label\">我的佣金</view>\t\r\n\t\t\t\t\t<view class=\"value\">{{commission}}</view>\t\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t<text style=\"color: #9e9e9e;\">\t累计佣金：</text>{{totalcommission}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\r\n\t\t\t\t<view class=\"tab1\" v-if=\"sysset.rank_type.length > 1\">\r\n\t\t\t\t\t<block v-for=\"(item, index) in sysset.rank_type\" :key=\"index\" >\r\n\t\t\t\t\t\t<view :class=\"'t1 '+(item==ranktype?'on':'')\" :style=\"{color:(item==ranktype?t('color1'):'')}\" :data-ranktype = \"item\" @tap=\"toranktype\">{{item==1?'累计佣金':(item==2?'自购订单金额':'')}}\t\t\r\n\t\t\t\t\t\t<view class=\"before\" v-if=\"item==ranktype\" :style=\"'border-bottom:1rpx solid '+t('color1')\"></view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"tab\">\r\n\t\t\t\t\t<view class=\"t1\">排名</view>\r\n\t\t\t\t\t<view class=\"t2\">姓名</view>\r\n\t\t\t\t\t<view class=\"t3\" >{{ranktype==1?'累计佣金':(ranktype==2?'自购订单金额':'')}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t<view class=\"itembox\">\t\r\n\t\t\t\t\t<block v-for=\"(item, index) in datalist\" :key=\"index\" >\r\n\t\t\t\t\t<view class=\"item\" :style=\"index < 3 ?'margin-top:20rpx':''\">\r\n\t\t\t\t\t\t\t<view class=\"t1\" v-if=\"index<3\"><image :src=\"pre_url+ '/static/img/comrank'+index+'.png'\"></view>\r\n\t\t\t\t\t\t\t<view class=\"t1\" v-else>{{index+1}}</view>\r\n\t\t\t\t\t\t\t<view class=\"t2\"><image :src=\"item.headimg\">{{item.nickname}}</view>\r\n\t\t\t\t\t\t\t<text class=\"t3\" :style=\"{color:t('color1')}\"> {{ranktype==1?item.sumcommission:(ranktype==2?item.sumtotalprice:'')}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t</view>\r\n\r\n\t<nodata v-if=\"nodata\"></nodata>\r\n\t<nomore v-if=\"nomore\"></nomore>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</block>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n      nodata: false,\r\n      nomore: false,\r\n      datalist: [],\r\n\t\t\ttextset:{},\r\n      pagenum: 1,\r\n\t\t\ttotalcommission:0,\r\n\t\t\tcommission:0,\r\n\t\t\trank_type:[],\r\n\t\t\tsysset:[],\r\n\t\t\tranktype:1\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tvar that = this;\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  onReachBottom: function () {\r\n    if (!this.nodata && !this.nomore) {\r\n      this.pagenum = this.pagenum + 1;\r\n      this.getdata(true);\r\n    }\r\n  },\r\n  methods: {\r\n    getdata: function (loadmore) {\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n      var that = this;\r\n      var pagenum = that.pagenum;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n      that.nomore = false;\r\n\t\t\tvar ranktype = that.ranktype;\r\n\t\t\t\r\n      app.post('ApiAgent/commissionrank', {ranktype:ranktype,pagenum: pagenum}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n        var data = res.data.data;\r\n\t\t\t\tthat.totalcommission = res.data.totalcommission\r\n\t\t\t\tthat.commission = res.data.commission\r\n\t\t\t\tthat.rank_type = res.data.rank_type\r\n\t\t\t\tthat.sysset = res.data.sysset\r\n\t\t\t\tif(res.data.status==0){\r\n\t\t\t\t\t\tapp.alert('排行榜未开启')\r\n\t\t\t\t}\r\n        if (pagenum == 1) {\r\n\t\t\t\t\tthat.textset = app.globalData.textset;\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: that.t('佣金') + '排行'\r\n\t\t\t\t\t});\r\n          that.datalist = data;\r\n          if (data.length == 0) {\r\n            that.nodata = true;\r\n          }\r\n\t\t\t\t\tthat.loaded();\r\n        }else{\r\n          if (data.length == 0) {\r\n            that.nomore = true;\r\n          } else {\r\n            var datalist = that.datalist;\r\n            var newdata = datalist.concat(data);\r\n            that.datalist = newdata;\r\n          }\r\n        }\r\n      });\r\n    },\r\n\t\ttoranktype:function (e) {\r\n\t\t\tvar that=this\r\n\t\t\tvar ranktype = e.currentTarget.dataset.ranktype;\r\n\t\t\tthat.ranktype = ranktype\r\n\t\t\tthat.getdata()\r\n\t\t}\r\n  }\r\n};\r\n</script>\r\n<style>\r\n/* .container{ padding: 20rpx; background: #FC3B36;} */\r\n.contentbox{ border-radius: 20rpx; width: 100%;}\r\n.contentbox image{ border-top-left-radius: 10rpx; border-top-right-radius: 10rpx; width: 100%; border:none; display: block;}\r\n\r\n.content{ background: #fff; display: flex; align-items: center; flex-direction: column; }\r\n\r\n.top{ margin-top: -45rpx; border-radius: 10rpx; display: flex;  display: flex;flex-direction: column;background-color: #fff;border-radius: 50rpx 50rpx 0 0;padding: 40rpx 50rpx 40rpx 50rpx;box-shadow: 0 -20rpx 32rpx rgb(144 78 43 / 0.15);position: relative;z-index: 10;}\r\n.top .t1 .label{color: #9e9e9e;}\r\n.top .t1 .value{font-size: 42rpx;color: #FF5D1D;font-weight: 700;}\r\n.top .t1 {line-height: 50rpx;}\r\n.top .t2{line-height: 50rpx;margin-top: 20rpx;}\r\n.content .tab{ display: flex; width: 90%; text-align: left;  line-height: 70rpx; margin-top: 20rpx; color: #666;}\r\n.content .tab .t1{ width: 25%;}\r\n.content .tab .t2{ width: 50%;padding-left: 20rpx;}\r\n.content .tab .t3{ width: 30%;}\r\n\r\n.content .tab1{ display: flex; border-bottom: 1rpx solid #dedede; width: 90%; height: 100rpx; line-height: 100rpx;}\r\n.content .tab1 .t1{ text-align: center;margin: 0 30rpx; }\r\n.content .tab1 .t1.on{ color:red;}\r\n\r\n\r\n.content .itembox{width:94%;}\r\n.content .item{width:100%; display:flex;padding:40rpx 20rpx;border-radius:8px;margin-top: 6rpx;align-items:center;}\r\n.itembox .item:first-child{  background-image: linear-gradient(to right , #FFF3E5, #FFFFFC)}\r\n.itembox .item:nth-child(2){ background-image: linear-gradient(to right , #DDECFF, #FFFFFC)}\r\n.itembox .item:nth-child(3){background-image: linear-gradient(to right , #FFE1DE, #FFFFFC)}\r\n\r\n\r\n\r\n.content .item image{ width: 80rpx; height: 80rpx; border-radius: 50%; margin-right: 20rpx; }\r\n.content .item .t1{color:#000000;font-size:30rpx;width: 25%;font-weight: 700;}\r\n.content .item .t2{color:#666666;font-size:24rpx; width: 60%; display: flex; align-items: center;}\r\n.content .item .t3{ width: 30%; font-weight: bold;}\r\n\r\n.data-empty{background:#fff}\r\n.title{position: absolute;left: 8%;top: 6%;}\r\n.title .t1{font-size: 48rpx;font-weight: 700;line-height: 80rpx;color: #9C6238;}\r\n.title .t2{font-size: 32rpx;line-height: 60rpx;color: #9C6238;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./commissionranking.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./commissionranking.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754212971669\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}