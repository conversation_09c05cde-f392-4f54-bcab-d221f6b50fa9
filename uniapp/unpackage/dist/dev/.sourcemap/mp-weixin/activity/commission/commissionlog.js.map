{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/commissionlog.vue?3dd6", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/commissionlog.vue?16ef", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/commissionlog.vue?3292", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/commissionlog.vue?0fe5", "uni-app:///activity/commission/commissionlog.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/commissionlog.vue?1be1", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/commission/commissionlog.vue?62a9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "nodata", "nomore", "st", "type", "datalist", "textset", "pagenum", "set", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "uni", "title", "changetab", "scrollTop", "duration", "confirm_shoukuan", "a", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpFA;AAAA;AAAA;AAAA;AAA20B,CAAgB,2yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuD/1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;;MAGAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;;EAGAC;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;QAAAZ;QAAAI;QAAAH;MAAA;QACAU;QACA;QACA;UACAA;UACAA;UACAE;YACAC;UACA;UACAH;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAI;MACA;MACAF;QACAG;QACAC;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAP;gBAAA;gBAAA,OACAA;cAAA;gBAAAQ;gBACAC;gBACAT;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;;;;;;;;;;;;;;;AChJA;AAAA;AAAA;AAAA;AAAwrC,CAAgB,wmCAAG,EAAC,C;;;;;;;;;;;ACA5sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/commission/commissionlog.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/commission/commissionlog.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./commissionlog.vue?vue&type=template&id=6db3c937&\"\nvar renderjs\nimport script from \"./commissionlog.vue?vue&type=script&lang=js&\"\nexport * from \"./commissionlog.vue?vue&type=script&lang=js&\"\nimport style0 from \"./commissionlog.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/commission/commissionlog.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./commissionlog.vue?vue&type=template&id=6db3c937&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"佣金\") : null\n  var l0 =\n    _vm.isload && _vm.st == 1\n      ? _vm.__map(_vm.datalist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m1 =\n            item.status == 4 &&\n            (item.wx_state == \"WAIT_USER_CONFIRM\" ||\n              item.wx_state == \"TRANSFERING\")\n              ? _vm.t(\"color1\")\n              : null\n          return {\n            $orig: $orig,\n            m1: m1,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./commissionlog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./commissionlog.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<dd-tab :itemdata=\"[t('佣金')+'明细','提现记录']\" :itemst=\"['0','1']\" :st=\"st\" :isfixed=\"false\" @changetab=\"changetab\"></dd-tab>\n\t\t<view class=\"content\">\n\t\t\t<block v-if=\"st==0\">\n\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"item\">\n\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t<text class=\"t1\">{{item.remark}}</text>\n\t\t\t\t\t\t<text class=\"t2\">{{item.createtime}}</text>\n\t\t\t\t\t\t<text class=\"t3\">变更后余额: {{item.after}}</text>\r\n\t\t\t\t\t\t<text class=\"t3\" v-if=\"set.commission_service_fee_show && set.commission_service_fee_show == 1\">平台服务费：{{item.service_fee}}</text>\r\n\t\t\t\t\t\t<block v-if=\"item.order\">\r\n\t\t\t\t\t\t\t<text class=\"t3\">订单金额: {{item.order.totalprice}}</text>\r\n\t\t\t\t\t\t\t<text class=\"t3\" v-if=\"item.order.member\">下单人: <image :src=\"item.order.member.headimg\"></image>{{item.order.member.nickname}}</text>\r\n\t\t\t\t\t\t\t<view class=\"t3\" v-if=\"item.order.business\"><image :src=\"item.order.business.logo\"></image>{{item.order.business.name}}</view>\r\n\t\t\t\t\t\t</block>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t<text class=\"t1\" v-if=\"item.money>0\">+{{item.money}}</text>\n\t\t\t\t\t\t<text class=\"t2\" v-else>{{item.money}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t</block>\n\t\t\t<block v-if=\"st==1\">\n\t\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"item\">\n\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t<text class=\"t1\">提现金额：{{item.money}}</text>\n\t\t\t\t\t\t<text class=\"t2\">{{item.createtime}}</text>\r\n\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.status==2\">驳回原因：{{item.reason || '无'}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"f3\">\n\t\t\t\t\t\t<text class=\"t1\" v-if=\"item.status==0\">审核中</text>\n\t\t\t\t\t\t<text class=\"t1\" v-if=\"item.status==1\">已审核</text>\n\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.status==2\">已驳回</text>\n\t\t\t\t\t\t<text class=\"t1\" v-if=\"item.status==3\">已打款</text>\n\t\t\t\t\t\t<block v-if=\"item.status==4\">\r\n\t\t\t\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color1')}\" @click=\"confirm_shoukuan(item.id)\" v-if=\"item.wx_state=='WAIT_USER_CONFIRM' || item.wx_state=='TRANSFERING'\">确认收款</view>\r\n\t\t\t\t\t\t\t<view class=\"t1\" v-else-if=\"item.wx_state=='FAIL'\">转账失败</view>\r\n\t\t\t\t\t\t\t<view class=\"t1\" v-else>处理中</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t</block>\n\t\t</view>\n\t</block>\n\t<nodata v-if=\"nodata\"></nodata>\n\t<nomore v-if=\"nomore\"></nomore>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\r\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n      nodata: false,\n      nomore: false,\n      st: 0,\r\n\t\t\ttype:0,\n      datalist: [],\n\t\t\ttextset:{},\n      pagenum: 1,\n\t\t\tset:{},\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.st = this.opt.st || 0;\r\n\t\tthis.type = this.opt.type || 0;\r\n\t\tvar that = this;\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata(true);\n    }\n  },\n  methods: {\n    getdata: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var st = that.st;\r\n\t\t\tvar type = that.type;\n      var pagenum = that.pagenum;\n\t\t\tthat.loading = true;\n\t\t\tthat.nodata = false;\n      that.nomore = false;\n      app.post('ApiAgent/commissionlog', {st: st,pagenum: pagenum,type:type}, function (res) {\n\t\t\t\tthat.loading = false;\n        var data = res.data;\n        if (pagenum == 1) {\n\t\t\t\t\tthat.textset = app.globalData.textset;\n\t\t\t\t\tthat.set = res.set;\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: that.t('佣金') + '明细'\n\t\t\t\t\t});\n          that.datalist = data;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n\t\t\t\t\tthat.loaded();\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n    changetab: function (st) {\n      this.st = st;\n      uni.pageScrollTo({\n        scrollTop: 0,\n        duration: 0\n      });\n      this.getdata();\n    },\r\n\tasync confirm_shoukuan(id){\r\n\t\tvar that = this;\r\n\t\tvar a = await that.shoukuan(id,'member_commission_withdrawlog','');\r\n\t\tconsole.log(a);\r\n\t\tthat.getdata();\r\n\t}\n  }\n};\r\n</script>\r\n<style>\r\n\r\n.content{ width:94%;margin:20rpx 3%;}\r\n.content .item{width:100%;background:#fff;margin:20rpx 0;padding:40rpx 30rpx;border-radius:8px;display:flex;align-items:center}\r\n.content .item:last-child{border:0}\r\n.content .item .f1{width:500rpx;display:flex;flex-direction:column}\r\n.content .item .f1 .t1{color:#000000;font-size:30rpx;word-break:break-all;overflow:hidden;text-overflow:ellipsis;}\r\n.content .item .f1 .t2{color:#666666;font-size:24rpx;margin-top:10rpx}\r\n.content .item .f1 .t3{color:#666666;font-size:24rpx;margin-top:10rpx;display: flex;}\r\n.content .item .f1 .t3 image{width:40rpx;height:40rpx;border-radius:50%;margin-right:4px;align-content: center;}\r\n.content .item .f2{ flex:1;width:200rpx;font-size:36rpx;text-align:right}\r\n.content .item .f2 .t1{color:#03bc01}\r\n.content .item .f2 .t2{color:#000000}\r\n.content .item .f3{ flex:1;width:200rpx;font-size:32rpx;text-align:right}\r\n.content .item .f3 .t1{color:#03bc01}\r\n.content .item .f3 .t2{color:#000000}\r\n\r\n.data-empty{background:#fff}\r\n.btn1{height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;padding: 0 15rpx;float: right;font-size: 25rpx;margin-left: 10rpx}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./commissionlog.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./commissionlog.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754212972474\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}