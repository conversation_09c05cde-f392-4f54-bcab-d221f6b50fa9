{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/seckill/comment.vue?ff76", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/seckill/comment.vue?5b2e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/seckill/comment.vue?0253", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/seckill/comment.vue?7d1d", "uni-app:///activity/seckill/comment.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/seckill/comment.vue?a3fe", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/seckill/comment.vue?e827"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "og", "comment", "score", "content_pic", "tempFilePaths", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "orderid", "formSubmit", "content", "setTimeout", "handleClick", "handleTouchMove", "setIndex", "uploadimg", "pics"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpDA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoDz1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACA;UACAA;UACAA;UACA;UACAA;QACA;QACAA;MACA;IACA;IACAG;MACA;MACA;MACA;MACA;MACA;MACA;QACAF;QACA;MACA;MACA;QACAA;QACA;MACA;MACAA;MACAA;QAAAC;QAAAE;QAAAV;QAAAD;MAAA;QACAQ;QACAA;QACAI;UACAJ;QACA;MACA;IACA;IACAK;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAR;QACA;UACAS;QACA;QACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtJA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/seckill/comment.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/seckill/comment.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./comment.vue?vue&type=template&id=0e839e8d&\"\nvar renderjs\nimport script from \"./comment.vue?vue&type=script&lang=js&\"\nexport * from \"./comment.vue?vue&type=script&lang=js&\"\nimport style0 from \"./comment.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/seckill/comment.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment.vue?vue&type=template&id=0e839e8d&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? !_vm.comment.id && _vm.content_pic.length < 5 : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<form @submit=\"formSubmit\">\n\t\t\t<view class=\"form-item1\">\n\t\t\t\t<view class=\"label\">商品信息</view>\n\t\t\t\t<view class=\"product flex\">\n\t\t\t\t\t<view class=\"img\"><image :src=\"og.propic\"></image></view>\n\t\t\t\t\t<view class=\"info flex1\">\n\t\t\t\t\t\t<view class=\"f1\">{{og.proname}}</view>\n\t\t\t\t\t\t<view class=\"f2\">{{og.ggname}}</view>\n\t\t\t\t\t\t<view class=\"f3\">￥{{og.sell_price}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"form-item2 flex flex-y-center\">\n\t\t\t\t<view class=\"label\">您的打分</view>\n\t\t\t\t<view class=\"i-rate\" @touchmove=\"handleTouchMove\">\n\t\t\t\t\t<input type=\"text\" name=\"score\" :value=\"score\" class=\"i-rate-hide-input\"></input>\n\t\t\t\t\t<view v-for=\"(item, index) in 5\" :key=\"index\" class=\"i-rate-star\" :class=\"( index < score ? 'i-rate-current':'' )\" :data-index=\"index\" @tap=\"handleClick\">\n\t\t\t\t\t\t\t<image v-if=\"index < score\" :src=\"pre_url+'/static/img/star2native.png'\"></image>\n\t\t\t\t\t\t\t<image v-else :src=\"pre_url+'/static/img/star.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"i-rate-text\"></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"form-item3 flex-col\">\n\t\t\t\t<view class=\"label\">您的评价</view>\n\t\t\t\t<textarea placeholder=\"输入您的评价内容\" placeholder-style=\"color:#ccc;\" name=\"content\" :value=\"comment.content\" style=\"height:200rpx\" :disabled=\"comment.id?true:false\"></textarea>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"form-item4 flex-col\">\n\t\t\t\t<view class=\"label\">上传图片</view>\n\t\t\t\t<view id=\"content_picpreview\" class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\n\t\t\t\t\t<view v-for=\"(item, index) in content_pic\" :key=\"index\" class=\"layui-imgbox\">\n\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"content_pic\" v-if=\"!comment.id\"><image :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\n\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\n\t\t\t\t\t\t<!-- <view class=\"layui-imgbox-repeat\" bindtap=\"xuanzhuan\" data-index=\"{{index}}\" data-field=\"content_pic\" wx:if=\"{{!comment.id}}\"><text class=\"fa fa-repeat\"></text></view> -->\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"content_pic\" v-if=\"!comment.id && content_pic.length<5\"></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<button class=\"subbtn\" form-type=\"submit\" v-if=\"!comment.id\">确定</button>\n\t\t</form>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tpre_url:app.globalData.pre_url,\n\t\t\tog:{},\n\t\t\tcomment:{},\n      score: 0,\n      content_pic: [],\n      tempFilePaths: \"\"\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tvar orderid = that.opt.orderid;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiSeckill/comment', {orderid: orderid}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.og = res.og;\n\t\t\t\tif (res.comment){\n\t\t\t\t\tthat.comment = res.comment;\n\t\t\t\t\tthat.score = res.comment.score;\n\t\t\t\t\tvar content_pic = res.comment.content_pic;\n\t\t\t\t\tthat.content_pic = content_pic.split(',');\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    formSubmit: function (e) {\n      var that = this;\n      var orderid = that.opt.orderid;\n      var score = e.detail.value.score;\n      var content = e.detail.value.content;\n      var content_pic = that.content_pic;\n      if (score == 0) {\n        app.error('请打分');\n        return;\n      }\n      if (content == '') {\n        app.error('请填写评价内容');\n        return;\n      }\n\t\t\tapp.showLoading('提交中');\n      app.post('ApiSeckill/comment', {orderid: orderid,content: content,content_pic: content_pic.join(','),score: score}, function (data) {\n\t\t\t\tapp.showLoading(false);\n        app.success(data.msg);\n        setTimeout(function () {\n          app.goback(true);\n        }, 2000);\n      });\n    },\n    handleClick: function (e) {\n      if (this.comment && this.comment.id) return;\n      var index = e.currentTarget.dataset.index;\n      this.score = index + 1;\n    },\n    handleTouchMove: function (e) {\n      if (this.comment && this.comment.id) return;\n      var clientWidth = uni.getSystemInfoSync().windowWidth;\n      if (!e.changedTouches[0]) return;\n      var movePageX = e.changedTouches[0].pageX;\n      var space = movePageX - 150 / 750 * clientWidth;\n      if (space <= 0) return;\n      var starwidth = 60 / 750 * clientWidth;\n      var setIndex = Math.ceil(space / starwidth);\n      setIndex = setIndex > 5 ? 5 : setIndex;\n      this.score = setIndex;\n    },\n\t\tuploadimg:function(e){\n\t\t\tvar that = this;\n\t\t\tvar field= e.currentTarget.dataset.field\n\t\t\tvar pics = that[field]\n\t\t\tif(!pics) pics = [];\n\t\t\tapp.chooseImage(function(urls){\n\t\t\t\tfor(var i=0;i<urls.length;i++){\n\t\t\t\t\tpics.push(urls[i]);\n\t\t\t\t}\n\t\t\t\tif(field == 'pic') that.pic = pics;\n\t\t\t\tif(field == 'pics') that.pics = pics;\n\t\t\t\tif(field == 'zhengming') that.zhengming = pics;\n\t\t\t},1)\n\t\t},\n  }\n};\n</script>\n<style>\n.form-item1{ width:100%;background: #fff; padding: 8rpx 20rpx;}\n.form-item1 .label{ width:100%;height:60rpx;line-height:60rpx}\n.product{ width: 100%; background: #fff; }\n.product .info{padding-left:20rpx;}\n.product .info .f2{color: #a4a4a4; font-size:24rpx}\n.product .info .f3{color: #ff0d51; font-size:28rpx}\n.product image{ width:140rpx;height:140rpx}\n\n.form-item2{width:100%;background: #fff; padding: 8rpx 20rpx;margin-top:1px}\n.form-item2 .label{ width:150rpx;height:60rpx;line-height:60rpx}\n\n.form-item3{width:100%;background: #fff; padding: 8rpx 20rpx;margin-top:1px}\n.form-item3 .label{ width:100%;height:60rpx;line-height:60rpx}\n.form-item3 textarea{width: 100%;border: 1px #dedede solid; border-radius: 10rpx; padding: 10rpx;height: 120rpx;}\n\n\n.form-item4{width:100%;background: #fff; padding: 20rpx 20rpx;margin-top:1px}\n.form-item4 .label{ width:150rpx;}\n/*.form-item4 image{ width: 100rpx; height: 100rpx;background:#eee;margin-right:6rpx}\n.form-item4 .imgbox{height:100rpx}*/\n\n.subbtn{ width: 90%; margin: 0 5%;margin-top:40rpx; height: 40px; line-height: 40px; color: #fff; background: #e94745; }\n\n.i-rate{margin:0;padding:0;display:inline-block;vertical-align:middle;}\n.i-rate-hide-input{display:none}\n.i-rate-star{display:inline-block;color:#e9e9e9;padding:0 10rpx}\n.i-rate-star image{width:50rpx;height:50rpx}\n.i-rate-current{color:#f5a623}\n.i-rate-text{display:inline-block;vertical-align:middle;margin-left:6px;font-size:14px}\n\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\n.layui-imgbox-img>image{max-width:100%;}\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213529923\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}