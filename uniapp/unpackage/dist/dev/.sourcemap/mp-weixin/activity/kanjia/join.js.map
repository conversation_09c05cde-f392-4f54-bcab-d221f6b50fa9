{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/join.vue?d159", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/join.vue?d699", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/join.vue?6dfb", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/join.vue?c654", "uni-app:///activity/kanjia/join.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/join.vue?4d1c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/join.vue?14ed"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "product", "mid", "iskan", "nowtime", "djs", "<PERSON><PERSON><PERSON><PERSON>", "helpinfo", "joininfo", "joinuserinfo", "cut_percent", "sharetypevisible", "showposter", "posterpic", "onLoad", "onPullDownRefresh", "onUnload", "clearInterval", "onShareAppMessage", "console", "title", "imageUrl", "path", "onShareTimeline", "query", "methods", "getdata", "that", "app", "proid", "joinid", "interval", "desc", "pic", "link", "getdjs", "<PERSON><PERSON><PERSON>", "hideKanjiaDialog", "shareClick", "handleClickMask", "showPoster", "posterDialogClose", "sharemp", "shareapp", "uni", "itemList", "success", "scene", "sharedata"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAAk0B,CAAgB,kyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6Ht1B;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;IACA;IACA;IACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACAJ;IACA;MACAC;MACAC;MACAG;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACAT;MACAU;MACAC;QAAAC;QAAAC;MAAA;QACAH;QACA;UACAC;UACA;QACA;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;QAEAA;QACAI;UACAJ;UACAA;QACA;QACA;UACAA;QACA;QACA;QACAA;UAAAP;UAAAY;UAAAC;UAAAC;QAAA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAR;IACA;IACAS;MACA;MACA;MACAR;QAAAE;MAAA;QACA;UACAF;UACA;QACA;QACA;QACAD;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAU;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAb;MACAA;MACAC;MACAA;QAAAC;QAAAC;MAAA;QACAF;QACA;UACAA;QACA;UACAD;QACA;MACA;IACA;IACAc;MACA;IACA;IACAC;MACAd;MACA;IACA;IACAe;MACA;MACAC;QACAC;QACAC;UACA;YACA;YACA;cACAC;YACA;YACA;YACAC;YACAA;YACAA;YACAA;YACA;;YAEAA;YACAA;YAEAJ;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/SA;AAAA;AAAA;AAAA;AAA+qC,CAAgB,+lCAAG,EAAC,C;;;;;;;;;;;ACAnsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/kanjia/join.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/kanjia/join.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./join.vue?vue&type=template&id=48cec7fd&\"\nvar renderjs\nimport script from \"./join.vue?vue&type=script&lang=js&\"\nexport * from \"./join.vue?vue&type=script&lang=js&\"\nimport style0 from \"./join.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/kanjia/join.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./join.vue?vue&type=template&id=48cec7fd&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.sharetypevisible ? _vm.getplatform() : null\n  var m1 =\n    _vm.isload && _vm.sharetypevisible && !(m0 == \"app\")\n      ? _vm.getplatform()\n      : null\n  var m2 =\n    _vm.isload && _vm.sharetypevisible && !(m0 == \"app\") && !(m1 == \"mp\")\n      ? _vm.getplatform()\n      : null\n  var m3 =\n    _vm.isload &&\n    _vm.showkanjia &&\n    !(_vm.joininfo.mid == _vm.helpinfo.mid) &&\n    _vm.helpinfo.givescore > 0\n      ? _vm.t(\"积分\")\n      : null\n  var m4 =\n    _vm.isload &&\n    _vm.showkanjia &&\n    !(_vm.joininfo.mid == _vm.helpinfo.mid) &&\n    _vm.helpinfo.givemoney > 0\n      ? _vm.t(\"余额\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./join.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./join.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"container\" :style=\"'background:url(' + pre_url + '/static/img/kanjia-bg.png) no-repeat;background-size:100% auto;'\">\r\n\t\t\t<view class=\"topcontent\">\r\n\t\t\t\t<image :src=\"pre_url + '/static/img/kanjia-hb.png'\" class=\"hongbao\"></image>\r\n\t\t\t\t<view class=\"userinfo\">\r\n\t\t\t\t\t<view class=\"f1\"><image :src=\"joinuserinfo.headimg\"></image></view>\r\n\t\t\t\t\t<view class=\"f2\">{{joinuserinfo.nickname}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<view class=\"title\">〝我看中这个宝贝, 快帮我砍一刀〞</view>\r\n\t\t\t\t\t<view class=\"proinfo\">\r\n\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t<image :src=\"product.pic\" @tap=\"goto\" :data-url=\"'product?id=' + product.id\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t\t<view class=\"t1\">{{product.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"t2\">已砍走 <text style=\"color:#FF4C00\">{{product.sales}}</text> 件</view>\r\n\t\t\t\t\t\t\t<view class=\"t3\">¥<text class=\"x1\">{{product.min_price}}</text> <text class=\"x2\">¥{{product.sell_price}}</text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"progressinfo\">\r\n\t\t\t\t\t\t<view class=\"t0\">已砍 <text class=\"x1\">￥{{joininfo.yikan_price}}</text>，剩余 <text class=\"x2\">￥{{joininfo.now_price}}</text></view>\r\n\t\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t\t<progress :percent=\"cut_percent\" border-radius=\"3\" activeColor=\"#FF3143\" backgroundColor=\"#FFD1C9\" active=\"true\"></progress>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"t3\">\r\n\t\t\t\t\t\t\t<view class=\"x1\">原价：<text style=\"color:#FF3143\">￥{{product.sell_price}}</text></view>\r\n\t\t\t\t\t\t\t<view class=\"x2\">最低砍至：<text style=\"color:#FF3143\">￥{{product.min_price}}</text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"op\">\r\n\t\t\t\t\t\t<button @tap=\"shareClick\" class=\"btn1\" v-if=\"mid==joininfo.mid && joininfo.status==0\" style=\"background: linear-gradient(90deg, #FF3143 0%, #FE6748 100%);\">召唤好友帮我砍价</button>\r\n\t\t\t\t\t\t<button @tap=\"goto\" class=\"btn1\" style=\"background:#FC4343;width:320rpx\" :data-url=\"'buy?joinid=' + joininfo.id\" v-if=\"mid==joininfo.mid && (joininfo.status==1) && joininfo.isbuy==0\">前去下单</button>\r\n\t\t\t\t\t\t<button @tap=\"goto\" data-url=\"orderlist\" class=\"btn1\" style=\"background:#FC4343;width:320rpx\" v-if=\"mid==joininfo.mid && joininfo.isbuy==1\">查看订单</button>\r\n\t\t\t\t\t\t<button @tap=\"doKan\" class=\"btn1\" style=\"background:#FC4343;width:320rpx\" v-if=\"mid!=joininfo.mid && iskan==0\">帮他砍价</button>\r\n\t\t\t\t\t\t<button @tap=\"goto\" :data-url=\"'product?id=' + product.id\" class=\"btn1\" style=\"background:#FC4343;width:320rpx\" v-if=\"mid!=joininfo.mid && iskan==1\">我也要参与</button>\r\n\t\t\t\t\t\t<button @tap=\"goto\" :data-url=\"'helplist?joinid=' + joininfo.id\" class=\"btn1\">帮砍记录</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"op\" v-if=\"mid==joininfo.mid && (joininfo.status==0 && product.directbuy==1) && joininfo.isbuy==0\" style=\"margin-top:20rpx\">\r\n\t\t\t\t\t\t<button @tap=\"goto\" class=\"btn1\" style=\"background:linear-gradient(90deg,#FE6748  0%, #FF3143 100%);width:560rpx\" :data-url=\"'buy?joinid=' + joininfo.id\">现在就去下单</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"lefttime\">\r\n\t\t\t\t\t\t<view class=\"t1\">距活动结束还剩：</view>\r\n\t\t\t\t\t\t<view class=\"t2\">{{djs}}</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t\r\n\t\t<view v-if=\"sharetypevisible\" class=\"popup__container\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"handleClickMask\"></view>\r\n\t\t\t<view class=\"popup__modal\" style=\"height:320rpx;min-height:320rpx\">\r\n\t\t\t\t<!-- <view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请选择分享方式</text>\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\"/>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<view class=\"sharetypecontent\">\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"shareapp\" v-if=\"getplatform() == 'app'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'mp'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- \t<view class=\"f1\" @tap=\"sharemp\" v-else-if=\"getplatform() == 'h5'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<button class=\"f1\" open-type=\"share\" v-else-if=\"getplatform() != 'h5'\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharefriends.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">分享给好友</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"showPoster\">\r\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/sharepic.png'\"/>\r\n\t\t\t\t\t\t\t<text class=\"t1\">生成分享图片</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"posterDialog\" v-if=\"showposter\">\r\n\t\t\t<view class=\"main\">\r\n\t\t\t\t<view class=\"close\" @tap=\"posterDialogClose\"><image class=\"img\" :src=\"pre_url+'/static/img/close.png'\"/></view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"posterpic\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"posterpic\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"kanjiaDialog\" v-if=\"showkanjia\">\r\n\t\t\t<view class=\"main\">\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<view class=\"bargainIco\">\r\n\t\t\t\t\t\t\t<!-- <text class=\"bargainIcoPrice\">{{helpinfo.cut_price}}</text> -->\r\n\t\t\t\t\t\t\t<image class=\"bargainIcoImg\" :src=\"pre_url + '/static/img/bargainbg.png'\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<block v-if=\"joininfo.mid == helpinfo.mid\">\r\n\t\t\t\t\t\t<view class=\"bargainPrice\">砍掉{{helpinfo.cut_price}}元</view>\r\n\t\t\t\t\t\t<text class=\"bargainText\">您自己砍了第一刀</text>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t<view class=\"bargainPrice\">帮好友砍掉{{helpinfo.cut_price}}元</view>\r\n\t\t\t\t\t\t<view class=\"bargainText\" v-if=\"helpinfo.givescore > 0\">奖励您{{helpinfo.givescore}}{{t('积分')}}<text v-if=\"product.helpgive_ff==1\">，好友买单后发放</text></view>\r\n\t\t\t\t\t\t<view class=\"bargainText\" v-if=\"helpinfo.givemoney > 0\">奖励您{{helpinfo.givemoney}}{{t('余额')}}<text v-if=\"product.helpgive_ff==1\">，好友买单后发放</text></view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<form @submit=\"hideKanjiaDialog\" reportSubmit=\"true\">\r\n\t\t\t\t\t\t<button class=\"bargainBtn SysBtn\" form-type=\"submit\" type=\"default\">确定</button>\r\n\t\t\t\t\t</form>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar interval = null;\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\t\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\tproduct:{},\r\n\t\t\tmid:'',\r\n\t\t\tiskan:0,\r\n      nowtime: \"\",\r\n      djs: \"\",\r\n      showkanjia: false,\r\n      helpinfo: {},\r\n      joininfo: {},\r\n\t\t\tjoinuserinfo:{},\r\n      cut_percent: \"\",\r\n      sharetypevisible: false,\r\n      showposter: false,\r\n      posterpic: \"\"\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  onUnload: function () {\r\n    clearInterval(interval);\r\n  },\r\n  onShareAppMessage: function () {\r\n    var that = this;\r\n    this.sharetypevisible = false;\r\n\t\tvar thisurl = '/activity/kanjia/join?scene=pid_'+app.globalData.mid+'-proid_'+that.product.id+'-joinid_' + that.joininfo.id;\r\n    console.log(thisurl);\r\n    return {\r\n      title: '快来帮我砍一刀~ ' + that.product.name,\r\n      imageUrl: that.product.pics[0],\r\n      path: thisurl\r\n    };\r\n  },\r\n\tonShareTimeline:function(){\r\n    var that = this;\r\n\t\tvar query = 'scene=pid_'+app.globalData.mid+'-proid_'+that.product.id+'-joinid_' + that.joininfo.id+'-seetype_circle';\r\n\t\tconsole.log(query);\r\n\t\treturn {\r\n\t\t\ttitle: '快来帮我砍一刀~ ' + that.product.name,\r\n\t\t\timageUrl: that.product.pics[0],\r\n\t\t\tquery: query\r\n\t\t}\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tvar proid = that.opt.proid;\r\n\t\t\tvar joinid = that.opt.joinid ? that.opt.joinid : '';\r\n\t\t\tclearInterval(interval);\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiKanjia/join', {proid: proid,joinid: joinid}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthat.mid = res.mid;\r\n\t\t\t\tthat.product = res.product;\r\n\t\t\t\tthat.joininfo = res.joininfo;\r\n\t\t\t\tthat.joinuserinfo = res.joinuserinfo;\r\n\t\t\t\tthat.nowtime = res.nowtime;\r\n\t\t\t\tthat.cut_percent = res.cut_percent;\r\n\t\t\t\tthat.iskan = res.iskan;\r\n\t\t\t\tvar pagecontent = JSON.parse(res.product.detail);\r\n\r\n\t\t\t\tthat.pagecontent = pagecontent\r\n\t\t\t\tinterval = setInterval(function () {\r\n\t\t\t\t\tthat.nowtime = that.nowtime + 1;\r\n\t\t\t\t\tthat.getdjs();\r\n\t\t\t\t}, 1000);\r\n\t\t\t\tif (res.joininfo.helpnum == 0) {\r\n\t\t\t\t\tthat.doKan();\r\n\t\t\t\t}\r\n\t\t\t\tvar sharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/activity/kanjia/join?scene=pid_'+app.globalData.mid+'-proid_'+that.product.id+'-joinid_' + that.joininfo.id + '&t='+parseInt((new Date().getTime())/1000);\r\n\t\t\t\tthat.loaded({title:that.product.name,desc:'快来帮我砍一刀~ ',pic:that.product.pics[0],link:sharelink});\r\n\t\t\t});\r\n\t\t},\r\n    getdjs: function () {\r\n      var that = this;\r\n      var totalsec = that.product.endtime * 1 - that.nowtime * 1;\r\n      if (totalsec <= 0) {\r\n        var djs = '00时00分00秒';\r\n      } else {\r\n        var date = Math.floor(totalsec / 86400);\r\n        var houer = Math.floor((totalsec - date * 86400) / 3600);\r\n        var min = Math.floor((totalsec - date * 86400 - houer * 3600) / 60);\r\n        var sec = totalsec - date * 86400 - houer * 3600 - min * 60;\r\n        var djs = (date > 0 ? date + '天' : '') + (houer < 10 ? '0' : '') + houer + '时' + (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';\r\n      }\r\n      that.djs = djs;\r\n    },\r\n    doKan: function () {\r\n      var that = this;\r\n      var product = that.product;\r\n      app.post('ApiKanjia/kanjiaKan', {joinid: that.joininfo.id}, function (res) {\r\n        if (res.status == 0) {\r\n          app.alert(res.msg);\r\n          return;\r\n        }\r\n        var cut_percent = Math.round((product.sell_price * 1 - res.joininfo.now_price * 1) / (product.sell_price * 1 - product.min_price * 1) * 100);\r\n        that.showkanjia = true;\r\n        that.helpinfo = res.helpinfo;\r\n        that.joininfo = res.joininfo;\r\n        that.cut_percent = cut_percent;\r\n        that.getdata();\r\n      });\r\n    },\r\n    hideKanjiaDialog: function (e) {\r\n      this.showkanjia = !this.showkanjia;\r\n    },\r\n    shareClick: function () {\r\n      this.sharetypevisible = true;\r\n    },\r\n    handleClickMask: function () {\r\n      this.sharetypevisible = false;\r\n    },\r\n    showPoster: function () {\r\n      var that = this;\r\n      that.showposter = true;\r\n      that.sharetypevisible = false;\r\n\t\t\tapp.showLoading('努力生成中');\r\n      app.post('ApiKanjia/getJoinPoster', {proid: that.product.id,joinid: that.joininfo.id}, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n        if (data.status == 0) {\r\n          app.alert(data.msg);\r\n        } else {\r\n          that.posterpic = data.poster;\r\n        }\r\n      });\r\n    },\r\n    posterDialogClose: function () {\r\n      this.showposter = false;\r\n    },\r\n\t\tsharemp:function(){\r\n\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\r\n\t\t\tthis.sharetypevisible = false\r\n\t\t},\r\n\t\tshareapp:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.showActionSheet({\r\n        itemList: ['发送给微信好友', '分享到微信朋友圈'],\r\n        success: function (res){\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tvar scene = 'WXSceneSession';\r\n\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tvar sharedata = {};\r\n\t\t\t\t\t\tsharedata.provider = 'weixin';\r\n\t\t\t\t\t\tsharedata.type = 0;\r\n\t\t\t\t\t\tsharedata.scene = scene;\r\n\t\t\t\t\t\tsharedata.title = '快来帮我砍一刀~ ' + that.product.name;\r\n\t\t\t\t\t\t//sharedata.summary = app.globalData.initdata.desc;\r\n\r\n\t\t\t\t\t\tsharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/activity/kanjia/join?scene=pid_'+app.globalData.mid+'-proid_'+that.product.id+'-joinid_' + that.joininfo.id + '&t='+parseInt((new Date().getTime())/1000);\r\n\t\t\t\t\t\tsharedata.imageUrl = that.product.pic;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.share(sharedata);\r\n\t\t\t\t\t}\r\n        }\r\n      });\r\n\t\t}\r\n  }\r\n};\r\n</script>\r\n<style>\r\npage{background: linear-gradient(0deg, #DF3939 0%, #FF2B49 100%);}\r\n.container{padding-top:280rpx;display:flex;flex-direction:column}\r\n.topcontent{background:#FFDCB4;border-radius:20rpx;width:94%;margin:0 3%;position:relative;padding:12rpx;margin-bottom:20rpx}\r\n.topcontent .hongbao{position:absolute;top:-32rpx;right:20rpx;width:128rpx;height:88rpx}\r\n.topcontent .userinfo{width:100%;display:flex;flex-direction:column;align-items:center;position:absolute;top:-60rpx}\r\n.topcontent .userinfo .f1{width:126rpx;height:126rpx;background:#FA6251;border-radius:50%;padding:8rpx}\r\n.topcontent .userinfo .f1 image{width:100%;height:100%;border-radius:50%;}\r\n.topcontent .userinfo .f2{font-size:28rpx;color:#999999;margin-top:8rpx}\r\n.topcontent .content{background:#fff;padding-top:92rpx;padding-bottom:20rpx;border-radius:20rpx;}\r\n.topcontent .title{font-size:40rpx;color:#514544;font-weight:bold;padding:32rpx 0;text-align:center}\r\n.topcontent .proinfo{width:100%;background:#F5F5F5;padding:16rpx;display:flex;}\r\n.topcontent .proinfo .f1{width:240rpx;height:240rpx}\r\n.topcontent .proinfo .f1 image{width:100%;height:100%}\r\n.topcontent .proinfo .f2{flex:1;padding-left:20rpx}\r\n.topcontent .proinfo .f2 .t1{width:100%;height:80rpx;line-height:40rpx;font-size:30rpx;color:#5A4742;font-weight:bold;overflow:hidden}\r\n.topcontent .proinfo .f2 .t2{color:#999999;font-size:26rpx;margin-top:10rpx}\r\n.topcontent .proinfo .f2 .t3{color:#FF4C00;margin-top:30rpx}\r\n.topcontent .proinfo .f2 .t3 .x1{font-size:48rpx;font-weigth:bold;padding-right:10rpx}\r\n.topcontent .proinfo .f2 .t3 .x2{font-size:24rpx;color:#999999;text-decoration: line-through;}\r\n\r\n.topcontent .progressinfo{width:100%;padding:80rpx 40rpx;display:flex;flex-direction:column}\r\n.topcontent .progressinfo .t0{width:100%;color:#222;font-size:30rpx;font-weight:bold;display:flex;text-align:center;justify-content:center}\r\n.topcontent .progressinfo .t0 .x1{color:#FF1324}\r\n.topcontent .progressinfo .t0 .x2{color:#FF3143}\r\n.topcontent .progressinfo .t1{width:100%;color:#f60;font-size:36rpx;flex:auto;display:flex;}\r\n.topcontent .progressinfo .t2{width:100%;padding:20rpx 0}\r\n.topcontent .progressinfo .t3{width:100%;color:#222;font-size:28rpx;display:flex;}\r\n.topcontent .progressinfo .t3 .x1{width:50%}\r\n.topcontent .progressinfo .t3 .x2{width:50%;text-align:right}\r\n.weui-progress{width:100%;}\r\n.weui-progress__bar{height:16rpx;background-color:#FFD1C9;border-radius: 8rpx;}\r\n.weui-progress__inner-bar{background-color:#FF3143;border-radius: 8rpx;}\r\n\r\n.topcontent .op{margin:0 20rpx 40rpx 20rpx;display:flex;align-items:center;justify-content:center}\r\n.topcontent .op button{height:80rpx;line-height:80rpx;font-size:30rpx;padding:0 40rpx;border-radius:40rpx;background:#FC9144;width:auto;color:#fff;margin:0 20rpx}\r\n.topcontent .op button:after{border:0}\r\n\r\n.topcontent .lefttime{margin-top:20rpx;border-top:1px solid #f5f5f5;color:#222;font-weight:bold;display:flex;text-align:center;justify-content:center;padding:20rpx 20rpx 0 20rpx}\r\n.topcontent .lefttime .t2{color:#FF3143}\r\n\r\n\r\n.op{margin:40rpx;display:flex;align-items:center;justify-content:center}\r\n.op button{height:80rpx;line-height:80rpx;font-size:32rpx;padding:0 40rpx;border-radius:10rpx;background:#FC9144;width:auto;color:#fff}\r\n.op button:after{border:0}\r\n\r\n.kanjiaDialog{ position:fixed;z-index:901;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:999;top:0;left:0;align-items:center}\r\n.kanjiaDialog .main{ width:70%;margin:auto;margin-top:50%;background:#fff;position:relative;border-radius:20rpx}\r\n.kanjiaDialog .content{ width:100%;padding:30rpx 20rpx 30rpx 20rpx;color:#333;font-size:30rpx;text-align:center}\r\n.kanjiaDialog .content .bargainIco {width: 200rpx;height: 200rpx;color: #ca2428;margin: 20rpx auto;position: relative;}\r\n.kanjiaDialog .content .bargainIcoImg {width: 200rpx;height: 200rpx;}\r\n.kanjiaDialog .content .bargainIcoPrice {width: 120rpx;height: 100rpx;transform: rotate(-16deg);position: absolute;top: 70rpx;left: 44rpx;}\r\n.kanjiaDialog .content .bargainPrice {color: #ca2428;font-size: 14px;}\r\n.kanjiaDialog .content .bargainText {font-size: 12px;margin: 4rpx 0 12rpx;}\r\n.kanjiaDialog .content .bargainBtn {background: #f60;margin: 0 20rpx;color: #fff;padding: 10rpx 0;margin-top: 20rpx;height: 80rpx;line-height: 60rpx;border-radius: 0;}\r\n.kanjiaDialog .content .bargainBtn::after {border: 0;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./join.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./join.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754212970011\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}