{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/orderdetail.vue?c709", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/orderdetail.vue?5ee0", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/orderdetail.vue?65e6", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/orderdetail.vue?68fd", "uni-app:///activity/kanjia/orderdetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/orderdetail.vue?6c52", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/orderdetail.vue?91d3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "prodata", "djs", "iscommentdp", "detail", "prolist", "shopset", "storeinfo", "lefttime", "codtxt", "binfo", "onLoad", "onPullDownRefresh", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "id", "interval", "getdjs", "todel", "orderid", "setTimeout", "toclose", "orderCollect", "showhxqr", "closeHxqr", "openMendian"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3EA;AAAA;AAAA;AAAA;AAAy0B,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6N71B;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAA,oCACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACA;YACAG;cACAH;cACAA;YACA;UACA;UACAA;QACA;UACA;YACAC;cACA;YACA;UACA;YACAA;UACA;YACAA;UACA;QACA;MACA;IACA;IACAG;MACA;MACA;MAEA;QACAJ;MACA;QACA;QACA;QACA;QACA;QACAA;MACA;IACA;IACAK;MACA;MACA;MACAJ;QACAA;QACAA;UAAAK;QAAA;UACAL;UACAA;UACAM;YACAN;UACA;QACA;MACA;IACA;IACAO;MACA;MACA;MACAP;QACAA;QACAA;UAAAK;QAAA;UACAL;UACAA;UACAM;YACAP;UACA;QACA;MACA;IACA;IACAS;MACA;MACA;MACAR;QACAA;QACAA;UAAAK;QAAA;UACAL;UACAA;UACAM;YACAP;UACA;QACA;MACA;IACA;IACAU;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAX;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACvWA;AAAA;AAAA;AAAA;AAAsrC,CAAgB,smCAAG,EAAC,C;;;;;;;;;;;ACA1sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/kanjia/orderdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/kanjia/orderdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderdetail.vue?vue&type=template&id=14395ac8&\"\nvar renderjs\nimport script from \"./orderdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./orderdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/kanjia/orderdetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=template&id=14395ac8&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.detail.disprice > 0 ? _vm.t(\"会员\") : null\n  var m1 = _vm.isload && _vm.detail.couponmoney > 0 ? _vm.t(\"优惠券\") : null\n  var m2 = _vm.isload && _vm.detail.scoredk > 0 ? _vm.t(\"积分\") : null\n  var g0 =\n    _vm.isload && _vm.detail.isfuwu && _vm.detail.fuwuendtime > 0\n      ? _vm._.dateFormat(_vm.detail.fuwuendtime, \"Y-m-d H:i\")\n      : null\n  var g1 = _vm.isload ? _vm.detail.formdata.length : null\n  var m3 = _vm.isload && _vm.detail.status == 0 ? _vm.t(\"color1\") : null\n  var m4 =\n    _vm.isload && _vm.detail.status == 2 && _vm.detail.paytypeid != \"4\"\n      ? _vm.t(\"color1\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        g0: g0,\n        g1: g1,\n        m3: m3,\n        m4: m4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"ordertop\" :style=\"'background:url(' + pre_url + '/static/img/ordertop.png);background-size:100%'\">\n\t\t\t<view class=\"f1\" v-if=\"detail.status==0\">\n\t\t\t\t<view class=\"t1\">等待买家付款</view>\n\t\t\t\t<view class=\"t2\" v-if=\"djs\">剩余时间：{{djs}}</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==1\">\n\t\t\t\t<view class=\"t1\">{{detail.paytypeid==4 ? '已选择'+detail.paytype : '已成功付款'}}</view>\n\t\t\t\t<view class=\"t2\" v-if=\"detail.freight_type!=1\">我们会尽快为您发货</view>\n\t\t\t\t<view class=\"t2\" v-if=\"detail.freight_type==1\">请尽快前往自提地点取货</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==2\">\n\t\t\t\t<view class=\"t1\">订单已发货</view>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type!=3\" user-select=\"true\" selectable=\"true\">发货信息：{{detail.express_com}} {{detail.express_no}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==3\">\n\t\t\t\t<view class=\"t1\">订单已完成</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==4\">\n\t\t\t\t<view class=\"t1\">订单已取消</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"address\">\n\t\t\t<view class=\"img\">\n\t\t\t\t<image :src=\"pre_url+'/static/img/address3.png'\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"info\">\n\t\t\t\t<text class=\"t1\" user-select=\"true\" selectable=\"true\">{{detail.linkman}} {{detail.tel}}</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type!=1 && detail.freight_type!=3\" user-select=\"true\" selectable=\"true\">地址：{{detail.area}}{{detail.address}}</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type==1\" @tap=\"openMendian\" :data-storeinfo=\"storeinfo\" :data-latitude=\"storeinfo.latitude\" :data-longitude=\"storeinfo.longitude\" user-select=\"true\" selectable=\"true\">取货地点：{{storeinfo.name}} - {{storeinfo.address}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"btitle flex-y-center\" v-if=\"detail.bid>0\">\n\t\t\t<image :src=\"binfo.logo\" style=\"width:36rpx;height:36rpx;\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id=' + detail.bid\"></image>\n\t\t\t<text class=\"flex1\" decode=\"true\" space=\"true\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id=' + detail.bid\" style=\"padding-left:16rpx\">{{binfo.name}}</text>\n\t\t</view>\n\t\t<view class=\"product\">\n\t\t\t<view class=\"content\">\n\t\t\t\t<view @tap=\"goto\" :data-url=\"'/activity/kanjia/product?id=' + detail.proid\">\n\t\t\t\t\t<image :src=\"detail.propic\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t<text class=\"t1\">{{detail.proname}}</text>\n\t\t\t\t\t<view class=\"t3\"><text class=\"x1 flex1\">￥{{detail.sell_price}}</text><text class=\"x2\">×{{detail.num}}</text></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo\" v-if=\"(detail.status==3 || detail.status==2) && (detail.freight_type==3 || detail.freight_type==4)\">\n\t\t\t<view class=\"item flex-col\">\n\t\t\t\t<view class=\"flex-bt order-info-title\">\n\t\t\t\t\t<text class=\"t1\" style=\"color:#111\">发货信息</text>\n\t\t\t\t\t<view class=\"btn-class\" @click=\"copy\" :data-text='detail.freight_content'>复制</view>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"t2\" style=\"text-align:left;margin-top:10rpx;padding:0 10rpx\" user-select=\"true\" selectable=\"true\">{{detail.freight_content}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item flex-bt\">\n\t\t\t\t<text class=\"t1\">订单编号</text>\n\t\t\t\t<view class=\"ordernum-info flex-bt\">\n\t\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{detail.ordernum}}</text>\n\t\t\t\t\t<view class=\"btn-class\" style=\"margin-left: 20rpx;\" @click=\"copy\" :data-text='detail.ordernum'>复制</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytypeid!=4 && detail.paytime\">\n\t\t\t\t<text class=\"t1\">支付时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytime\">\n\t\t\t\t<text class=\"t1\">支付方式</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytype}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>1 && detail.send_time\">\n\t\t\t\t<text class=\"t1\">发货时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.send_time}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status==3 && detail.collect_time\">\n\t\t\t\t<text class=\"t1\">收货时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.collect_time}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">商品金额</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.product_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.disprice > 0\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}折扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.leveldk_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.jianmoney > 0\">\n\t\t\t\t<text class=\"t1\">满减活动</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.manjian_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">配送方式</text>\n\t\t\t\t<text class=\"t2\">{{detail.freight_text}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.freight_type==1 && detail.freightprice > 0\">\n\t\t\t\t<text class=\"t1\">服务费</text>\n\t\t\t\t<text class=\"t2 red\">+¥{{detail.freight_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.freight_time\">\n\t\t\t\t<text class=\"t1\">{{detail.freight_type!=1?'配送':'提货'}}时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.freight_time}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.couponmoney > 0\">\n\t\t\t\t<text class=\"t1\">{{t('优惠券')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.coupon_money}}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"item\" v-if=\"detail.scoredk > 0\">\n\t\t\t\t<text class=\"t1\">{{t('积分')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.scoredk_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">实付款</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.totalprice}}</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0\">未付款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==1\">已付款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==2\">已发货</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==3\">已收货</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==4\">已关闭</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\n\t\t\t\t<text class=\"t1\">退款状态</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==1\">审核中,¥{{detail.refund_money}}</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==2\">已退款,¥{{detail.refund_money}}</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==3\">已驳回,¥{{detail.refund_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\n\t\t\t\t<text class=\"t1\">退款原因</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_reason}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_checkremark\">\n\t\t\t\t<text class=\"t1\">审核备注</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_checkremark}}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"item\" v-if=\"detail.isfuwu && detail.fuwuendtime > 0\">\n\t\t\t\t<text class=\"t1\">到期时间</text>\n\t\t\t\t<text class=\"t2 red\">{{_.dateFormat(detail.fuwuendtime,'Y-m-d H:i')}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo\" v-if=\"(detail.formdata).length > 0\">\n\t\t\t<view class=\"item\" v-for=\"item in detail.formdata\" :key=\"index\" style=\"display: block\">\n\t\t\t\t<text class=\"t1\">{{item[0]}}</text>\n\t\t\t\t<view class=\"t2\" v-if=\"item[2]=='upload'\"><image :src=\"item[1]\" style=\"width:400rpx;height:auto\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"item[1]\"/></view>\n        <view class=\"t2\" v-else-if=\"item[2]=='upload_pics'\" v-for=\"picurl in item[1]\">\n          <image :src=\"picurl\" style=\"width:400rpx;height:auto\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"picurl\"/>\n        </view>\n\t\t\t\t<text class=\"t2\" v-else user-select=\"true\" selectable=\"true\">{{item[1]}}</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view style=\"width:100%;height:calc(160rpx + env(safe-area-inset-bottom));\"></view>\n\n\t\t<view class=\"bottom notabbarbot\">\n\t\t\t<block v-if=\"detail.status==0\">\n\t\t\t\t<view class=\"btn2\" @tap=\"toclose\" :data-id=\"detail.id\">关闭订单</view>\n\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color1')}\" @tap=\"goto\" :data-url=\"'/pagesExt/pay/pay?id=' + detail.payorderid\">去付款</view>\n\t\t\t</block>\n\t\t\t<block v-if=\"detail.status==1\">\n\t\t\t\t<block v-if=\"detail.paytypeid!='4'\">\n\t\t\t\t\t<view v-if=\"detail.refund_status==0 || detail.refund_status==3\" class=\"btn2\" @tap=\"goto\" :data-url=\"'refund?orderid=' + detail.id + '&price=' + detail.totalprice\">申请退款</view>\n\t\t\t\t</block>\n\t\t\t\t<block v-else>\n\t\t\t\t\t<!-- <view class=\"btn ref\">{{codtxt}}</view> -->\n\t\t\t\t</block>\n\t\t\t</block>\n\t\t\t\n\t\t\t<block v-if=\"detail.status==2 || detail.status==3\">\n\t\t\t\t<view class=\"btn2\" @tap=\"goto\" :data-url=\"'/pagesExt/order/logistics?express_com=' + detail.express_com + '&express_no=' + detail.express_no\" v-if=\"detail.freight_type!=3 && detail.freight_type!=4\">查看物流</view>\n\t\t\t</block>\n\n\t\t\t<block v-if=\"detail.status==2\">\n\t\t\t\t<block v-if=\"detail.paytypeid!='4'\">\n\t\t\t\t\t<view v-if=\"detail.refund_status==0 || detail.refund_status==3\" class=\"btn2\" @tap=\"goto\" :data-url=\"'refund?orderid=' + detail.id + '&price=' + detail.totalprice\">申请退款</view>\n\t\t\t\t</block>\n\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color1')}\" @tap=\"orderCollect\" :data-id=\"detail.id\" v-if=\"detail.paytypeid!='4'\">确认收货</view>\n\t\t\t\t<block v-else>\n\t\t\t\t\t<!-- <view class=\"btn2\">{{codtxt}}</view> -->\n\t\t\t\t</block>\n\t\t\t</block>\n\t\t\t<block v-if=\"(detail.status==1 || detail.status==2) && detail.freight_type==1\">\n\t\t\t\t<view class=\"btn2\" @tap=\"showhxqr\">核销码</view>\n\t\t\t</block>\n\t\t\t<block v-if=\"detail.status==3 || detail.status==4\">\n\t\t\t\t<view class=\"btn2\" @tap=\"todel\" :data-id=\"detail.id\">删除订单</view>\n\t\t\t</block>\n\t\t</view>\n\t\t<uni-popup id=\"dialogHxqr\" ref=\"dialogHxqr\" type=\"dialog\">\n\t\t\t<view class=\"hxqrbox\">\n\t\t\t\t<image :src=\"detail.hexiao_qr\" @tap=\"previewImage\" :data-url=\"detail.hexiao_qr\" class=\"img\"/>\n\t\t\t\t<view class=\"txt\">请出示核销码给核销员进行核销</view>\n\t\t\t\t<view class=\"close\" @tap=\"closeHxqr\">\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close2.png'\" style=\"width:100%;height:100%\"/>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nvar interval = null;\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tpre_url:app.globalData.pre_url,\n      prodata: '',\n      djs: '',\n      iscommentdp: \"\",\n      detail: \"\",\n      prolist: \"\",\n      shopset: \"\",\n      storeinfo: \"\",\n      lefttime: \"\",\n      codtxt: \"\",\n\t\t\tbinfo:{}\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onUnload: function () {\n    clearInterval(interval);\n  },\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiKanjia/orderdetail', {id: that.opt.id}, function (res) {\n\t\t\t\tthat.loading = false;\n        if(res.status == 1){\n          that.iscommentdp = res.iscommentdp,\n          that.detail = res.detail;\n          that.prolist = res.prolist;\n          that.shopset = res.shopset;\n          that.storeinfo = res.storeinfo;\n          that.binfo = res.binfo;\n          that.lefttime = res.lefttime;\n          that.codtxt = res.codtxt;\n          that.isload = 1;\n          if (res.lefttime > 0) {\n            interval = setInterval(function () {\n              that.lefttime = that.lefttime - 1;\n              that.getdjs();\n            }, 1000);\n          }\n          that.loaded();\n        } else {\n          if (res.msg) {\n            app.alert(res.msg, function() {\n              if (res.url) app.goto(res.url);\n            });\n          } else if (res.url) {\n            app.goto(res.url);\n          } else {\n            app.alert('您无查看权限');\n          }\n        }\n\t\t\t});\n\t\t},\n    getdjs: function () {\n      var that = this;\n      var totalsec = that.lefttime;\n\n      if (totalsec <= 0) {\n        that.djs = '00时00分00秒';\n      } else {\n        var houer = Math.floor(totalsec / 3600);\n        var min = Math.floor((totalsec - houer * 3600) / 60);\n        var sec = totalsec - houer * 3600 - min * 60;\n        var djs = (houer < 10 ? '0' : '') + houer + '时' + (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';\n        that.djs = djs;\n      }\n    },\n    todel: function (e) {\n      var that = this;\n      var orderid = e.currentTarget.dataset.id;\n      app.confirm('确定要删除该订单吗?', function () {\n\t\t\t\tapp.showLoading('删除中');\n        app.post('ApiKanjia/delOrder', {orderid: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n          app.success(data.msg);\n          setTimeout(function () {\n            app.goback(true);\n          }, 1000);\n        });\n      });\n    },\n    toclose: function (e) {\n      var that = this;\n      var orderid = e.currentTarget.dataset.id;\n      app.confirm('确定要关闭该订单吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n        app.post('ApiKanjia/closeOrder', {orderid: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n          app.success(data.msg);\n          setTimeout(function () {\n            that.getdata();\n          }, 1000);\n        });\n      });\n    },\n    orderCollect: function (e) {\n      var that = this;\n      var orderid = e.currentTarget.dataset.id;\n      app.confirm('确定要收货吗?', function () {\n\t\t\t\tapp.showLoading('收货中');\n        app.post('ApiKanjia/orderCollect', {orderid: orderid}, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n          app.success(data.msg);\n          setTimeout(function () {\n            that.getdata();\n          }, 1000);\n        });\n      });\n    },\n\t\tshowhxqr:function(){\n\t\t\tthis.$refs.dialogHxqr.open();\n\t\t},\n\t\tcloseHxqr:function(){\n\t\t\tthis.$refs.dialogHxqr.close();\n\t\t},\n\t\topenMendian: function(e) {\n\t\t\tvar storeinfo = e.currentTarget.dataset.storeinfo;\n\t\t\tapp.goto('/pages/shop/mendian?id=' + storeinfo.id);\n\t\t}\n  }\n};\n</script>\n<style>\n.ordertop{width:100%;height:220rpx;padding:50rpx 0 0 70rpx}\n.ordertop .f1{color:#fff}\n.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}\n.ordertop .f1 .t2{font-size:24rpx}\n\n.address{ display:flex;width: 100%; padding: 20rpx 3%; background: #FFF;}\n.address .img{width:40rpx}\n.address image{width:40rpx; height:40rpx;}\n.address .info{flex:1;display:flex;flex-direction:column;}\n.address .info .t1{font-size:28rpx;font-weight:bold;color:#333}\n.address .info .t2{font-size:24rpx;color:#999}\n\n.product{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\n.product .content{display:flex;position:relative;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;}\n.product .content:last-child{ border-bottom: 0; }\n.product .content image{ width: 140rpx; height: 140rpx;}\n.product .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\n.product .content .detail .t1{height: 80rpx;line-height: 40rpx;color: #000;overflow:hidden}\n.product .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\n.product .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\n.product .content .detail .x1{ flex:1}\n.product .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\n.product .content .comment{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc702 solid; border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.product .content .comment2{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc7c2 solid; border-radius:10rpx;background:#fff; color: #ffc7c2;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.orderinfo{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}\n.orderinfo .item:last-child{ border-bottom: 0;}\n.orderinfo .item .t1{width:200rpx;}\n.orderinfo .item .t2{flex:1;text-align:right}\n.orderinfo .item .red{color:red}\n.order-info-title{align-items: center;}\n.btn-class{height:45rpx;line-height:45rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;padding: 0 15rpx;flex-shrink: 0;margin: 0 0 0 10rpx;font-size:24rpx;}\n.ordernum-info{align-items: center;}\n.bottom{ width: 100%;height:calc(92rpx + env(safe-area-inset-bottom));padding: 0 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}\n\n.btn1{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}\n.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n.btn3{position:absolute;top:60rpx;right:10rpx;font-size:24rpx;width:120rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n\n.btitle{ width:100%;height:100rpx;background:#fff;padding:0 20rpx;border-bottom:1px solid #f5f5f5}\n.btitle .comment{border: 1px #ffc702 solid;border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.btitle .comment2{border: 1px #ffc7c0 solid;border-radius:10rpx;background:#fff; color: #ffc7c0;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\n.hxqrbox .img{width:400rpx;height:400rpx}\n.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\n.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754212967708\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}