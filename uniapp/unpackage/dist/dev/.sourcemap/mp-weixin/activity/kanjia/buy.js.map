{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/buy.vue?5f8d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/buy.vue?5334", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/buy.vue?8fa8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/buy.vue?4fc0", "uni-app:///activity/kanjia/buy.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/buy.vue?3d30", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/activity/kanjia/buy.vue?1706"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "editorFormdata", "test", "productList", "freightList", "couponList", "couponrid", "coupontype", "address", "needaddress", "linkman", "tel", "freightkey", "freight_price", "pstimetext", "freight_time", "usescore", "totalprice", "product_price", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "joinid", "storedata", "inputLinkman", "inputTel", "<PERSON><PERSON><PERSON><PERSON>", "calculatePrice", "scoredk_money", "scoredk", "changeFreight", "chooseCoupon", "coupon_money", "choosePstime", "itemlist", "uni", "itemList", "success", "pstimeRadioChange", "hidePstimeDialog", "choosestore", "topay", "formdata", "newformdata", "freightid", "storeid", "addressid", "showCouponList", "handleClickMask", "openLocation", "latitude", "longitude", "name", "scale", "openMendian", "editorChooseImage", "console", "editor<PERSON><PERSON>ose<PERSON><PERSON>s", "pics", "removeimg", "editorBind<PERSON>icker<PERSON><PERSON><PERSON>", "doStoreShowAll"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9IA;AAAA;AAAA;AAAA;AAAi0B,CAAgB,iyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgMr1B;AAAA,eAEA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,iDACA,qDACA,oDACA,sDACA,qDACA,sDACA,wDACA,yDACA,gEACA,2DACA,oDACA,qDACA,qDACA,mDACA,qDACA,sDACA,0DACA,mDACA,sDACA,wDACA,+DACA;EAEA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAC;QACA;QACA;QACA;QACA;QACA;QACA;QACAD;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;QACA;UACAA;QACA;QAEA;UACAC;YACA;YACA;YAEA;cACA;gBACA;gBAEA;kBACA;oBACA;sBACA;sBACAE;oBACA;kBACA;kBAEAA;oBACA;kBACA;kBAEA;oBACA;sBACAA;oBACA;kBACA;kBAEAtB;gBACA;cACA;YACA;YACAmB;UACA;QACA;MACA;IACA;IACAI;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACAL;IACA;IACA;IACAM;MACA;MACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;MACAP;MACA;MAEA;MACA;QAAA;QACA;QACA;QACA;UACAQ;QACA;QACA;UAAA;UACAA;QACA;MACA;QACA;MACA;MACA;;MAEA;MACAlB;MACAI;MACAM;MACAA;IACA;IACA;IACAS;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAV;IACA;IACAW;MACA;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;UACAC;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;QACAC;MACA;MACA;QACAb;QACA;MACA;MACA;QACAD;QACAA;MACA;QACAe;UACAC;UACAC;YACA;cACA;cACAjB;cACAA;YACA;UACA;QACA;MACA;IACA;IACAkB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACAvC;MACA;IACA;IACA;IACAwC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;QACA;MACA;MACA;MACA;MACA;MACA;QACApB;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACAA;QACA;MACA;MAEA;MACA;MACA;MACA;QACA;UACAA;UAAA;QACA;QACA;UACAqB;QACA;QACAC;MACA;MAEAtB;MACAA;QAAAC;QAAAsB;QAAAhC;QAAAiC;QAAAC;QAAAvC;QAAAC;QAAAK;QAAA6B;MAAA;QACArB;QACA;UACAA;UACA;QACA;QACAA;MACA;IACA;IACA0B;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAd;QACAe;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAjC;IACA;IACAkC;MACA;MACA;MACA;MACA;MACA;MACAlC;QACAvB;QACA0D;QACApC;QACAA;MACA;IACA;IACA;IACAqC;MACA;MACA;MACA;MAAA;MACA;MACApC;QACA;QACA;UACAqC;QACA;QACA;UACAA;QACA;QACA5D;QACAsB;QACAA;MACA;IACA;IACAuC;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACAD;QACA5D;QACAsB;QACAA;MACA;QACA;QACAsC;QACAtC;MACA;IACA;IACAwC;MACA;MACA;MACA;MACA;MACA;MACA9D;MACA0D;MACA;MACA;IACA;IACAK;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChmBA;AAAA;AAAA;AAAA;AAA8qC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAlsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "activity/kanjia/buy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './activity/kanjia/buy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./buy.vue?vue&type=template&id=8e82e4fa&\"\nvar renderjs\nimport script from \"./buy.vue?vue&type=script&lang=js&\"\nexport * from \"./buy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buy.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"activity/kanjia/buy.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=template&id=8e82e4fa&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.freightList, function (item, idx2) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.freightkey == idx2 ? _vm.t(\"color1\") : null\n        var m1 = _vm.freightkey == idx2 ? _vm.t(\"color1rgb\") : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  var g0 =\n    _vm.isload &&\n    _vm.freightList[_vm.freightkey].minpriceset == 1 &&\n    _vm.freightList[_vm.freightkey].minprice > 0 &&\n    _vm.freightList[_vm.freightkey].minprice * 1 > _vm.product_price * 1\n      ? (_vm.freightList[_vm.freightkey].minprice - _vm.product_price).toFixed(\n          2\n        )\n      : null\n  var l1 =\n    _vm.isload && _vm.freightList[_vm.freightkey].pstype == 1\n      ? _vm.__map(\n          _vm.freightList[_vm.freightkey].storedata,\n          function (item, idx) {\n            var $orig = _vm.__get_orig(item)\n            var m2 =\n              (idx < 5 || _vm.storeshowall == true) &&\n              _vm.freightList[_vm.freightkey].storekey == idx\n                ? _vm.t(\"color1\")\n                : null\n            return {\n              $orig: $orig,\n              m2: m2,\n            }\n          }\n        )\n      : null\n  var g1 =\n    _vm.isload && _vm.freightList[_vm.freightkey].pstype == 1\n      ? _vm.storeshowall == false &&\n        _vm.freightList[_vm.freightkey].storedata.length > 5\n      : null\n  var l2 = _vm.isload\n    ? _vm.__map(_vm.freightList[_vm.freightkey].formdata, function (item, idx) {\n        var $orig = _vm.__get_orig(item)\n        var g2 =\n          item.key == \"upload_pics\" &&\n          _vm.editorFormdata &&\n          _vm.editorFormdata[idx]\n            ? _vm.editorFormdata[idx].join(\",\")\n            : null\n        return {\n          $orig: $orig,\n          g2: g2,\n        }\n      })\n    : null\n  var m3 = _vm.isload && _vm.userinfo.score2money > 0 ? _vm.t(\"积分\") : null\n  var m4 = _vm.isload && _vm.userinfo.score2money > 0 ? _vm.t(\"积分\") : null\n  var m5 = _vm.isload ? _vm.t(\"color1\") : null\n  var m6 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var l3 =\n    _vm.isload && _vm.pstimeDialogShow\n      ? _vm.__map(\n          _vm.freightList[_vm.freightkey].pstimeArr,\n          function (item, index) {\n            var $orig = _vm.__get_orig(item)\n            var m7 = _vm.freight_time == item.value ? _vm.t(\"color1\") : null\n            return {\n              $orig: $orig,\n              m7: m7,\n            }\n          }\n        )\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        l1: l1,\n        g1: g1,\n        l2: l2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        l3: l3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<form @submit=\"topay\">\n\t\t<view v-if=\"needaddress==0\" class=\"address-add\">\n\t\t\t<view class=\"linkitem\">\n\t\t\t\t<label style=\"color: red;\" v-if=\"contact_require==1\"> * </label><text class=\"f1\">联 系 人：</text>\n\t\t\t\t<input type=\"text\" class=\"input\" :value=\"linkman\" placeholder=\"请输入您的姓名\" @input=\"inputLinkman\" placeholder-style=\"color:#626262;font-size:28rpx\"/>\n\t\t\t</view>\n\t\t\t<view class=\"linkitem\">\n\t\t\t\t<label style=\"color: red;\" v-if=\"contact_require==1\"> * </label><text class=\"f1\">联系电话：</text>\n\t\t\t\t<input type=\"text\" class=\"input\" :value=\"tel\" placeholder=\"请输入您的手机号\" @input=\"inputTel\" placeholder-style=\"color:#626262;font-size:28rpx\"/>\n\t\t\t</view>\n\t\t</view>\n\t\t<view v-else class=\"address-add flex-y-center\" @tap=\"goto\" :data-url=\"'/pagesB/address/address?fromPage=buy&type=' + (havetongcheng==1?'1':'0')\">\n\t\t\t<view class=\"f1\"><image class=\"img\" :src=\"pre_url+'/static/img/address.png'\" /></view>\n\t\t\t<view class=\"f2 flex1\" v-if=\"address.name\">\n\t\t\t\t<view style=\"font-weight:bold;color:#111111;font-size:30rpx\">{{address.name}} {{address.tel}} <text v-if=\"address.company\">{{address.company}}</text></view>\n\t\t\t\t<view style=\"font-size:24rpx\">{{address.area}} {{address.address}}</view>\n\t\t\t</view>\n\t\t\t<view v-else class=\"f2 flex1\">请选择收货地址</view>\n\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"f3\"/>\n\t\t</view>\n\t\t<view class=\"buydata\">\n\t\t\t<view class=\"bcontent\">\n\t\t\t\t<view class=\"product\">\n\t\t\t\t\t<view class=\"item flex\">\n\t\t\t\t\t\t<view class=\"img\"><image class=\"img\" :src=\"product.pic\"/></view>\n\t\t\t\t\t\t<view class=\"info flex1\">\n\t\t\t\t\t\t\t<view class=\"f1\">{{product.name}}</view>\n\t\t\t\t\t\t\t<view class=\"f2\">原价: ￥{{product.sell_price}}</view>\n\t\t\t\t\t\t\t<view class=\"f3\">￥{{joininfo.now_price}}<text class=\"kanjia_icon\">砍后价</text> × 1</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"freight\">\n\t\t\t\t\t<view class=\"f1\">配送方式</view>\n\t\t\t\t\t<view class=\"freight-ul\">\n\t\t\t\t\t\t<view class=\"flex\" style=\"width:100%;overflow-y:hidden;overflow-x:scroll;\">\n\t\t\t\t\t\t <block v-for=\"(item, idx2) in freightList\" :key=\"idx2\">\n\t\t\t\t\t\t <view class=\"freight-li\" :style=\"freightkey==idx2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''\" @tap=\"changeFreight\" :data-index=\"idx2\">{{item.name}}</view>\n\t\t\t\t\t\t </block>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"freighttips\" v-if=\"freightList[freightkey].minpriceset==1 && freightList[freightkey].minprice > 0 && freightList[freightkey].minprice*1 > product_price*1\">满{{freightList[freightkey].minprice}}元起送，还差{{(freightList[freightkey].minprice - product_price).toFixed(2)}}元</view>\n\t\t\t\t\t<view class=\"freighttips\" v-if=\"freightList[freightkey].isoutjuli==1\">超出配送范围</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"price\" v-if=\"freightList[freightkey].pstimeset==1\">\n\t\t\t\t\t<view class=\"f1\">{{freightList[freightkey].pstype==1?'取货':'配送'}}时间</view>\n\t\t\t\t\t<view class=\"f2\" @tap=\"choosePstime\">{{pstimetext==''?'请选择时间':pstimetext}}<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"storeitem\" v-if=\"freightList[freightkey].pstype==1\">\n\t\t\t\t\t<view class=\"panel\">\n\t\t\t\t\t\t<view class=\"f1\">取货地点</view>\n\t\t\t\t\t\t<view class=\"f2\" @tap=\"openMendian\" :data-freightkey=\"freightkey\" :data-storekey=\"freightList[freightkey].storekey\"><text class=\"iconfont icondingwei\"></text>{{freightList[freightkey].storedata[freightList[freightkey].storekey].name}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<block v-for=\"(item, idx) in freightList[freightkey].storedata\" :key=\"idx\">\n\t\t\t\t\t\t<view class=\"radio-item\" @tap.stop=\"choosestore\" :data-index=\"idx\" v-if=\"idx<5 || storeshowall==true\">\n\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}} </view>\n\t\t\t\t\t\t\t<text style=\"color:#f50;\">{{item.juli}}</text>\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"freightList[freightkey].storekey==idx ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t<view v-if=\"storeshowall==false && (freightList[freightkey].storedata).length > 5\" class=\"storeviewmore\" @tap=\"doStoreShowAll\">- 查看更多 - </view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"price\">\n\t\t\t\t\t<text class=\"f1\">商品金额</text>\n\t\t\t\t\t<text class=\"f2\">¥{{product_price}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"price\" v-if=\"leadermoney*1>0\">\n\t\t\t\t\t<text class=\"f1\">团长优惠</text>\n\t\t\t\t\t<text class=\"f2\">-¥{{leadermoney}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"price\">\n\t\t\t\t\t<view class=\"f1\"><text v-if=\"freightList[freightkey].pstype==1\">服务费</text><text v-else>运费</text></view>\n\t\t\t\t\t<text class=\"f2\">+¥{{freight_price}}</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view style=\"display:none\">{{test}}</view>\n\t\t\t\t<view class=\"form-item\" v-for=\"(item,idx) in freightList[freightkey].formdata\" :key=\"item.id\">\n\t\t\t\t\t<view class=\"label\">{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\n\t\t\t\t\t<block v-if=\"item.key=='input'\">\n\t\t\t\t\t\t<input type=\"text\" :name=\"'form'+idx\" class=\"input\" :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"/>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-if=\"item.key=='textarea'\">\n\t\t\t\t\t\t<textarea :name=\"'form'+idx\" class='textarea' :placeholder=\"item.val2\" placeholder-style=\"font-size:28rpx\"/>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-if=\"item.key=='radio'\">\n\t\t\t\t\t\t<radio-group class=\"radio-group\" :name=\"'form'+idx\">\n\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\n\t\t\t\t\t\t\t\t<radio class=\"radio\" :value=\"item1\"/>{{item1}}\n\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t</radio-group>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-if=\"item.key=='checkbox'\">\n\t\t\t\t\t\t<checkbox-group :name=\"'form'+idx\" class=\"checkbox-group\">\n\t\t\t\t\t\t\t<label v-for=\"(item1,idx1) in item.val2\" :key=\"item1.id\" class=\"flex-y-center\">\n\t\t\t\t\t\t\t\t<checkbox class=\"checkbox\" :value=\"item1\"/>{{item1}}\n\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t</checkbox-group>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-if=\"item.key=='selector'\">\n\t\t\t\t\t\t<picker class=\"picker\" mode=\"selector\" :name=\"'form'+idx\" value=\"\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\">\n\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx] || editorFormdata[idx]===0\"> {{item.val2[editorFormdata[idx]]}}</view>\n\t\t\t\t\t\t\t<view v-else>请选择</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-if=\"item.key=='time'\">\n\t\t\t\t\t\t<picker class=\"picker\" mode=\"time\" :name=\"'form'+idx\" value=\"\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\">\n\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx]\">{{editorFormdata[idx]}}</view>\n\t\t\t\t\t\t\t<view v-else>请选择</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-if=\"item.key=='date'\">\n\t\t\t\t\t\t<picker class=\"picker\" mode=\"date\" :name=\"'form'+idx\" value=\"\" :start=\"item.val2[0]\" :end=\"item.val2[1]\" :range=\"item.val2\" @change=\"editorBindPickerChange\" :data-idx=\"idx\">\n\t\t\t\t\t\t\t<view v-if=\"editorFormdata[idx]\">{{editorFormdata[idx]}}</view>\n\t\t\t\t\t\t\t<view v-else>请选择</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-if=\"item.key=='upload'\">\n\t\t\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form'+idx\" :value=\"editorFormdata[idx]\"/>\n\t\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\n\t\t\t\t\t\t\t<view class=\"form-imgbox\" v-if=\"editorFormdata[idx]\">\n\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" style=\"z-index: 2;\" @tap=\"removeimg\" :data-idx=\"idx\"><image style=\"display:block\" :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\n\t\t\t\t\t\t\t\t<view class=\"form-imgbox-img\"><image class=\"image\" :src=\"editorFormdata[idx]\" @click=\"previewImage\" :data-url=\"editorFormdata[idx]\" mode=\"aspectFit\"/></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view v-else class=\"form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}\" @click=\"editorChooseImage\" :data-idx=\"idx\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n          <block v-if=\"item.key=='upload_pics'\">\n            <input type=\"text\" style=\"display:none\" :name=\"'form'+idx\" :value=\"editorFormdata && editorFormdata[idx]?editorFormdata[idx].join(','):''\" maxlength=\"-1\"/>\n            <view class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\n              <view v-for=\"(item2, index2) in editorFormdata[idx]\" :key=\"index2\" class=\"form-imgbox\" >\n                <view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index2\" data-type=\"pics\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\"><image :src=\"pre_url+'/static/img/ico-del.png'\" class=\"image\" data-type=\"pics\"></image></view>\n                <view class=\"form-imgbox-img\" style=\"margin-bottom: 10rpx;\"><image class=\"image\" :src=\"item2\" @click=\"previewImage\" :data-url=\"item2\" mode=\"aspectFit\" :data-idx=\"idx\"/></view>\n              </view>\n              <view class=\"form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}\" @click=\"editorChooseImages\" :data-idx=\"idx\" :data-formidx=\"'form'+idx\" data-type=\"pics\"></view>\n            </view>\n          </block>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"scoredk\" v-if=\"userinfo.score2money>0\">\n\t\t\t<checkbox-group @change=\"scoredk\" class=\"flex\" style=\"width:100%\">\n\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t<view>{{userinfo.score*1}} {{t('积分')}}可抵扣 <text style=\"color:#e94745\">{{userinfo.scoredk_money*1}}</text> 元</view>\n\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\" v-if=\"userinfo.scoredkmaxpercent > 0 && userinfo.scoredkmaxpercent<100\">最多可抵扣订单金额的{{userinfo.scoredkmaxpercent}}%</view>\n\t\t\t\t\t<view style=\"font-size:22rpx;color:#999\" v-else-if=\"userinfo.scoremaxtype==1\">最多可抵扣{{userinfo.scoredkmaxmoney}}元</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"f2\">使用{{t('积分')}}抵扣\n\t\t\t\t\t<checkbox value=\"1\" style=\"margin-left:6px;transform:scale(.8)\"></checkbox>\n\t\t\t\t</view>\n\t\t\t</checkbox-group>\n\t\t</view>\n\n\t\t<view style=\"width: 100%; height:182rpx;\"></view>\n\t\t<view class=\"footer flex notabbarbot\">\n\t\t\t<view class=\"text1 flex1\">总计：\n\t\t\t\t<text style=\"font-weight:bold;font-size:36rpx\">￥{{totalprice}}</text>\n\t\t\t</view>\n\t\t\t<button class=\"op\" form-type=\"submit\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">提交订单</button>\n\t\t</view>\n\t\t</form>\n\n\t\t<view v-if=\"pstimeDialogShow\" class=\"popup__container\">\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"hidePstimeDialog\"></view>\n\t\t\t<view class=\"popup__modal\">\n\t\t\t\t<view class=\"popup__title\">\n\t\t\t\t\t<text class=\"popup__title-text\">请选择{{freightList[freightkey].pstype==1?'取货':'配送'}}时间</text>\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"hidePstimeDialog\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup__content\">\n\t\t\t\t\t<view class=\"pstime-item\" v-for=\"(item, index) in freightList[freightkey].pstimeArr\" :key=\"index\" @tap=\"pstimeRadioChange\" :data-index=\"index\">\n\t\t\t\t\t\t<view class=\"flex1\">{{item.title}}</view>\n\t\t\t\t\t\t<view class=\"radio\" :style=\"freight_time==item.value ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<wxxieyi></wxxieyi>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\n\t\t\tpre_url:app.globalData.pre_url,\n\t\t\teditorFormdata:[],\n\t\t\ttest:'test',\n\t\t\n      productList: [],\n      freightList: [],\n      couponList: [],\n      couponrid: 0,\n      coupontype: 1,\n      address: [],\n      needaddress: 1,\n      linkman: '',\n      tel: '',\n      freightkey: 0,\n      freight_price: 0,\n      pstimetext: '',\n      freight_time: '',\n      usescore: 0,\n      totalprice: '0.00',\n      product_price: 0,\n      isload: 0,\n      storedata: [],\n      storeid: '',\n      storename: '',\n      latitude: '',\n      longitude: '',\n      leadermoney: 0,\n      couponvisible: false,\n      pstimeDialogShow: false,\n      pstimeIndex: -1,\n      product: \"\",\n      userinfo: \"\",\n      joininfo: \"\",\n      weight: \"\",\n      goodsnum: 0,\n      scorebdkyf: \"\",\n      havetongcheng: \"\",\n      beizhu: \"\",\n      couponkey: 0,\n\t\t\tstoreshowall:false,\n\t\t\tcontact_require:0,\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this; //获取产品信息\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiKanjia/buy', {joinid: that.opt.joinid}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 0) {\n\t\t\t\t\tapp.alert(res.msg);\n\t\t\t\t}\n\t\t\t\tvar product = res.product;\n\t\t\t\tvar freightList = res.freightList;\n\t\t\t\tvar userinfo = res.userinfo;\n\t\t\t\tvar couponList = res.couponList;\n\t\t\t\tvar joininfo = res.joininfo;\n\t\t\t\tthat.product = product;\n\t\t\t\tthat.freightList = freightList;\n\t\t\t\tthat.userinfo = userinfo;\n\t\t\t\tthat.couponList = couponList;\n\t\t\t\tthat.joininfo = joininfo;\n\t\t\t\tthat.product_price = joininfo['now_price'];\n\t\t\t\tthat.weight = product.weight;\n\t\t\t\tthat.goodsnum = 1;\n\t\t\t\tthat.address = res.address;\n\t\t\t\tthat.scorebdkyf = res.scorebdkyf;\n\t\t\t\tthat.havetongcheng = res.havetongcheng;\n\t\t\t\tthat.linkman = res.linkman;\n\t\t\t\tthat.tel = res.tel;\n\t\t\t\tthat.calculatePrice();\n\t\t\t\tthat.loaded();\n\t\t\t\t//根据商品信息，更新联系人填写要求\n\t\t\t\tif((product.freighttype == 3|| product.freighttype == 4) && product.contact_require==1){\n\t\t\t\t\tthat.contact_require = 1;\n\t\t\t\t}\n\n\t\t\t\tif (res.needLocation == 1) {\n\t\t\t\t\tapp.getLocation(function (res) {\n\t\t\t\t\t\tvar latitude = res.latitude;\n\t\t\t\t\t\tvar longitude = res.longitude;\n\n\t\t\t\t\t\tfor (var j in freightList) {\n\t\t\t\t\t\t\tif (freightList[j].pstype == 1) {\n\t\t\t\t\t\t\t\tvar storedata = freightList[j].storedata;\n\n\t\t\t\t\t\t\t\tif (storedata) {\n\t\t\t\t\t\t\t\t\tfor (var x in storedata) {\n\t\t\t\t\t\t\t\t\t\tif (latitude && longitude && storedata[x].latitude && storedata[x].longitude) {\n\t\t\t\t\t\t\t\t\t\t\tvar juli = that.getDistance(latitude, longitude, storedata[x].latitude, storedata[x].longitude);\n\t\t\t\t\t\t\t\t\t\t\tstoredata[x].juli = juli;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\tstoredata.sort(function (a, b) {\n\t\t\t\t\t\t\t\t\t\treturn a[\"juli\"] - b[\"juli\"];\n\t\t\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\t\t\tfor (var x in storedata) {\n\t\t\t\t\t\t\t\t\t\tif (storedata[x].juli) {\n\t\t\t\t\t\t\t\t\t\t\tstoredata[x].juli = storedata[x].juli + '千米';\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\tfreightList[j].storedata = storedata;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthat.freightList = freightList;\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n    inputLinkman: function (e) {\n      this.linkman = e.detail.value\n    },\n    inputTel: function (e) {\n      this.tel = e.detail.value\n    },\n    //选择收货地址\n    chooseAddress: function () {\n      app.goto('/pagesB/address/address?fromPage=buy&type=' + (this.havetongcheng == 1 ? '1' : '0'));\n    },\n    //计算价格\n    calculatePrice: function () {\n      var that = this;\n      var product_price = parseFloat(that.product_price); //+商品总价\n      var address = that.address; //算运费\n      var freightdata = that.freightList[that.freightkey];\n      if (freightdata.pstype != 1 && freightdata.pstype != 3 && freightdata.pstype != 4) {\n        var needaddress = 1;\n      } else {\n        var needaddress = 0;\n      }\n      that.needaddress = needaddress;\n      var freight_price = freightdata.freight_price;\n\n\t\t\tvar totalprice = product_price + freight_price;\n      if (that.usescore) { //使用积分抵扣\n        var scoredk_money = parseFloat(that.userinfo.scoredk_money);\n\t\t\t\tvar scoredkmaxpercent = parseFloat(that.userinfo.scoredkmaxpercent); //最大抵扣比例\n\t\t\t\tif (scoredkmaxpercent > 0 && scoredkmaxpercent < 100 && scoredk_money > totalprice * scoredkmaxpercent * 0.01) {\n\t\t\t\t\tscoredk_money = totalprice * scoredkmaxpercent * 0.01;\n\t\t\t\t}\n\t\t\t\tif(that.scorebdkyf=='1' && scoredk_money > 0 && totalprice - scoredk_money < freight_price){ //积分不抵扣运费\n\t\t\t\t\tscoredk_money = totalprice - freight_price;\n\t\t\t\t}\n      } else {\n        var scoredk_money = 0;\n      }\n      var totalprice = totalprice - scoredk_money; //-积分抵扣\n\t\t\t\n      if (totalprice < 0) totalprice = 0;\n      freight_price = freight_price.toFixed(2);\n      totalprice = totalprice.toFixed(2);\n      that.totalprice = totalprice;\n      that.freight_price = freight_price;\n    },\n\t\t//积分抵扣\n\t\tscoredk: function (e) {\n\t\t\tvar usescore = e.detail.value[0];\n\t\t\tif (!usescore) usescore = 0\n\t\t\tthis.usescore = usescore;\n\t\t\tthis.calculatePrice();\n\t\t},\n    changeFreight: function (e) {\n      var that = this;\n      var index = e.currentTarget.dataset.index;\n\t\t\tthis.freightkey = index;\n\t\t\tthat.calculatePrice();\n    },\n    chooseCoupon: function (e) {\n\t\t\tvar couponrid = e.rid;\n      var couponkey = e.key;\n\n      if (couponrid == this.couponrid) {\n        this.couponkey = 0;\n        this.couponrid = 0;\n        this.coupontype = 1;\n        this.coupon_money = 0;\n        this.couponvisible = false;\n      } else {\n        var couponList = this.couponList;\n        var coupon_money = couponList[couponkey]['money'];\n        var coupontype = couponList[couponkey]['type'];\n        if (coupontype == 4) {\n          coupon_money = this.freightprice;\n        }\n        this.couponkey = couponkey;\n        this.couponrid = couponrid;\n        this.coupontype = coupontype;\n        this.coupon_money = coupon_money;\n        this.couponvisible = false;\n      }\n      this.calculatePrice();\n    },\n    choosePstime: function () {\n      var that = this;\n      var freightkey = this.freightkey;\n      var freightList = this.freightList;\n      var freight = freightList[freightkey];\n      var pstimeArr = freightList[freightkey].pstimeArr;\n      var itemlist = [];\n\n      for (var i = 0; i < pstimeArr.length; i++) {\n        itemlist.push(pstimeArr[i].title);\n      }\n      if (itemlist.length == 0) {\n        app.alert('当前没有可选' + (freightList[freightkey].pstype == 1 ? '取货' : '配送') + '时间段');\n        return;\n      }\n      if (itemlist.length > 6) {\n        that.pstimeDialogShow = true;\n        that.pstimeIndex = -1;\n      } else {\n        uni.showActionSheet({\n          itemList: itemlist,\n          success: function (res) {\n\t\t\t\t\t\tif(res.tapIndex >= 0){\n\t\t\t\t\t\t\tvar choosepstime = pstimeArr[res.tapIndex];\n\t\t\t\t\t\t\tthat.pstimetext = choosepstime.title;\n\t\t\t\t\t\t\tthat.freight_time = choosepstime.value;\n\t\t\t\t\t\t}\n          }\n        });\n      }\n    },\n    pstimeRadioChange: function (e) {\n      var pstimeIndex = e.currentTarget.dataset.index;\n\t\t\tvar freightkey = this.freightkey;\n      var freightList = this.freightList;\n      var freight = freightList[freightkey];\n      var pstimeArr = freightList[freightkey].pstimeArr;\n      var choosepstime = pstimeArr[pstimeIndex];\n      this.pstimetext = choosepstime.title;\n\t\t\tthis.freight_time = choosepstime.value;\n      this.pstimeDialogShow = false;\n    },\n    hidePstimeDialog: function () {\n      this.pstimeDialogShow = false\n    },\n    choosestore: function (e) {\n      var storekey = e.currentTarget.dataset.index;\n\t\t\tvar freightkey = this.freightkey\n\t\t\tvar freightList = this.freightList\n\t\t\tfreightList[freightkey].storekey = storekey\n      this.freightList = freightList;\n    },\n    //提交并支付\n    topay: function (e) {\n      var that = this;\n      var freightkey = this.freightkey;\n      var freightid = this.freightList[freightkey].id;\n      var joinid = this.opt.joinid;\n      var addressid = this.address.id;\n      var linkman = this.linkman;\n      var tel = this.tel;\n\t\t\tvar usescore = this.usescore\n      if(this.freightList[freightkey].pstype==1){\n\t\t\t\tvar storekey = this.freightList[freightkey].storekey\n\t\t\t\tvar storeid = this.freightList[freightkey].storedata[storekey].id;\n\t\t\t}else{\n\t\t\t\tvar storeid = 0;\n\t\t\t}\n      var freight_time = that.freight_time;\n      var needaddress = that.needaddress;\n      if (needaddress == 0) addressid = 0;\n      if (needaddress == 1 && addressid == undefined) {\n        app.error('请选择收货地址');\n        return;\n      }\n\t\t\tif(this.contact_require == 1 && (linkman.trim() == '' || tel.trim() == '')){\n\t\t\t\treturn app.error(\"请填写联系人信息\");\n\t\t\t}\n\t\t\tif(tel.trim()!= '' && !app.isPhone(tel)){\n\t\t\t\treturn app.error(\"请填写正确的手机号\");\n\t\t\t}\n      if (this.freightList[freightkey].pstimeset == 1 && freight_time == '') {\n        app.error('请选择' + (this.freightList[freightkey].pstype == 0 ? '配送' : '提货') + '时间');\n        return;\n      }\n\n\t\t\tvar formdataSet = this.freightList[freightkey].formdata;\n      var formdata = e.detail.value;\n\t\t\tvar newformdata = {};\n\t\t\tfor (var i = 0; i < formdataSet.length;i++){\n\t\t\t\tif (formdataSet[i].val3 == 1 && (formdata['form' + i] === '' || formdata['form' + i] === undefined || formdata['form' + i].length==0)){\n\t\t\t\t\t\tapp.alert(formdataSet[i].val1+' 必填');return;\n\t\t\t\t}\n\t\t\t\tif (formdataSet[i].key == 'selector') {\n\t\t\t\t\t\tformdata['form' + i] = formdataSet[i].val2[formdata['form' + i]]\n\t\t\t\t}\n\t\t\t\tnewformdata['form'+i] = formdata['form' + i];\n\t\t\t}\n\n\t\t\tapp.showLoading('提交中');\n      app.post('ApiKanjia/createOrder', {joinid: joinid,freightid: freightid,freight_time: freight_time,storeid: storeid,addressid: addressid,linkman: linkman,tel: tel,usescore:usescore,formdata:newformdata}, function (data) {\n\t\t\t\tapp.showLoading(false);\n        if (data.status == 0) {\n          app.error(data.msg);\n          return;\n        }\n        app.goto('/pagesExt/pay/pay?id=' + data.payorderid,'redirectTo');\n      });\n    },\n    showCouponList: function () {\n      this.couponvisible = true;\n    },\n    handleClickMask: function () {\n      this.couponvisible = false;\n    },\n\t\topenLocation:function(e){\n\t\t\tvar freightkey = e.currentTarget.dataset.freightkey;\n\t\t\tvar storekey = e.currentTarget.dataset.storekey;\n\t\t\tvar frightinfo = this.freightList[freightkey]\n\t\t\tvar storeinfo = frightinfo.storedata[storekey];\n\t\t\tvar latitude = parseFloat(storeinfo.latitude);\n\t\t\tvar longitude = parseFloat(storeinfo.longitude);\n\t\t\tvar address = storeinfo.name;\n\t\t\tuni.openLocation({\n\t\t\t latitude:latitude,\n\t\t\t longitude:longitude,\n\t\t\t name:address,\n\t\t\t scale: 13\n\t\t\t})\n\t\t},\n\t\topenMendian: function(e) {\n\t\t\tvar freightkey = e.currentTarget.dataset.freightkey;\n\t\t\tvar storekey = e.currentTarget.dataset.storekey;\n\t\t\tvar frightinfo = this.freightList[freightkey]\n\t\t\tvar storeinfo = frightinfo.storedata[storekey];\n\t\t\tapp.goto('/pages/shop/mendian?id=' + storeinfo.id);\n\t\t},\n\t\teditorChooseImage: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar idx = e.currentTarget.dataset.idx;\n\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\n\t\t\tvar editorFormdata = this.editorFormdata;\n\t\t\tif(!editorFormdata) editorFormdata = [];\n\t\t\tapp.chooseImage(function(data){\n\t\t\t\teditorFormdata[idx] = data[0];\n\t\t\t\tconsole.log(editorFormdata)\n\t\t\t\tthat.editorFormdata = editorFormdata\n\t\t\t\tthat.test = Math.random();\n\t\t\t})\n\t\t},\n    //多图上传，一次最多选8个\n    editorChooseImages: function (e) {\n      var that = this;\n      var idx = e.currentTarget.dataset.idx;\n      var editorFormdata = that.editorFormdata;;\n      if(!editorFormdata) editorFormdata = [];\n      app.chooseImage(function(data){\n        var pics = editorFormdata[idx];\n        if(!pics){\n          pics = [];\n        }\n        for(var i=0;i<data.length;i++){\n          pics.push(data[i]);\n        }\n        editorFormdata[idx] = pics;\n        that.editorFormdata = editorFormdata\n        that.test = Math.random();\n      },8)\n    },\n\t\tremoveimg:function(e){\n\t\t\tvar that = this;\n\t\t\tvar idx = e.currentTarget.dataset.idx;\n      var type  = e.currentTarget.dataset.type;\n      if(type == 'pics'){\n        var editorFormdata = this.editorFormdata;\n        var index = e.currentTarget.dataset.index;\n        var pics = editorFormdata[idx]\n        pics.splice(index,1);\n        editorFormdata[idx] = pics;\n        that.editorFormdata = editorFormdata\n        that.test = Math.random();\n      }else{\n        var pics = that.editorFormdata\n        pics.splice(idx,1);\n        that.editorFormdata = pics;\n      }\n\t\t},\n\t\teditorBindPickerChange:function(e){\n\t\t\tvar idx = e.currentTarget.dataset.idx;\n\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\n\t\t\tvar val = e.detail.value;\n\t\t\tvar editorFormdata = this.editorFormdata;\n\t\t\tif(!editorFormdata) editorFormdata = [];\n\t\t\teditorFormdata[idx] = val;\n\t\t\tconsole.log(editorFormdata)\n\t\t\tthis.editorFormdata = editorFormdata\n\t\t\tthis.test = Math.random();\n\t\t},\n\t\tdoStoreShowAll:function(){\n\t\t\tthis.storeshowall = true;\n\t\t},\n  }\n}\n</script>\n<style>\n.address-add{ width:94%;margin:20rpx 3%;background:#fff;border-radius:20rpx;padding: 20rpx 3%;min-height:140rpx;}\n.address-add .f1{margin-right:20rpx}\n.address-add .f1 .img{ width: 66rpx; height: 66rpx; }\n.address-add .f2{ color: #666; }\n.address-add .f3{ width: 26rpx; height: 26rpx;}\n\n.linkitem{width: 100%;padding:1px 0;background: #fff;display:flex;align-items:center}\n.linkitem .f1{width:160rpx;color:#111111}\n.linkitem .input{height:50rpx;padding-left:10rpx;color:#222222;font-weight:bold;font-size:28rpx;flex:1}\n\n.buydata{width:94%;margin:0 3%;background:#fff;margin-bottom:20rpx;border-radius:20rpx;}\n\n.btitle{width:100%;padding:20rpx 20rpx;display:flex;align-items:center;color:#111111;font-weight:bold;font-size:30rpx}\n.btitle .img{width:34rpx;height:34rpx;margin-right:10rpx}\n\n.bcontent{width:100%;padding:0 20rpx}\n\n.product{width:100%;border-bottom:1px solid #f4f4f4} \n.product .item{width:100%; padding:20rpx 0;background:#fff;border-bottom:1px #ededed dashed;}\n.product .item:last-child{border:none}\n.product .info{padding-left:20rpx;}\n.product .info .f1{color: #222222;font-weight:bold;font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.product .info .f2{color: #999999; font-size:24rpx}\n.product .info .f3{color: #FF4C4C; font-size:28rpx;display:flex;align-items:center;margin-top:10rpx}\n.product .img{ width:140rpx;height:140rpx}\n.collage_icon{ color:#fe7203;border:1px solid #feccaa;display:flex;align-items:center;font-size:20rpx;padding:0 6rpx;margin-left:6rpx}\n\n.freight{width:100%;padding:20rpx 0;background:#fff;display:flex;flex-direction:column;}\n.freight .f1{color:#333;margin-bottom:10rpx}\n.freight .f2{color: #111111;text-align:right;flex:1}\n.freight .f3{width: 24rpx;height:28rpx;}\n.freighttips{color:red;font-size:24rpx;}\n\n.freight-ul{width:100%;display:flex;}\n.freight-li{flex-shrink:0;display:flex;background:#F5F6F8;border-radius:24rpx;color:#6C737F;font-size:24rpx;text-align: center;height:48rpx; line-height:48rpx;padding:0 28rpx;margin:12rpx 10rpx 12rpx 0}\n\n\n.price{width:100%;padding:20rpx 0;background:#fff;display:flex;align-items:center}\n.price .f1{color:#333}\n.price .f2{ color:#111;font-weight:bold;text-align:right;flex:1}\n.price .f3{width: 24rpx;height:24rpx;}\n\n.scoredk{width:94%;margin:0 3%;margin-bottom:20rpx;border-radius:20rpx;padding:24rpx 20rpx; background: #fff;display:flex;align-items:center}\n.scoredk .f1{color:#333333}\n.scoredk .f2{ color: #999999;text-align:right;flex:1}\n\n.remark{width: 100%;padding:16rpx 0;background: #fff;display:flex;align-items:center}\n.remark .f1{color:#333;width:200rpx}\n.remark input{ border:0px solid #eee;height:70rpx;padding-left:10rpx;text-align:right}\n\n.footer {width: 96%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;padding: 0 2%;display: flex;align-items: center;z-index: 8;box-sizing:content-box}\n.footer .text1 {height:110rpx;line-height:110rpx;color: #2a2a2a;font-size: 30rpx;}\n.footer .text1  text{color: #e94745;font-size: 32rpx;}\n.footer .op{width: 200rpx;height:80rpx;line-height:80rpx;color: #fff;text-align: center;font-size: 30rpx;border-radius:44rpx}\n\n.storeitem{width: 100%;padding:20rpx 0;display:flex;flex-direction:column;color:#333}\n.storeitem .panel{width: 100%;height:60rpx;line-height:60rpx;font-size:28rpx;color:#333;margin-bottom:10rpx;display:flex}\n.storeitem .panel .f1{color:#333}\n.storeitem .panel .f2{ color:#111;font-weight:bold;text-align:right;flex:1}\n.storeitem .radio-item{display:flex;width:100%;color:#000;align-items: center;background:#fff;border-bottom:0 solid #eee;padding:8rpx 20rpx;}\n.storeitem .radio-item:last-child{border:0}\n.storeitem .radio-item .f1{color:#666;flex:1}\n.storeitem .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-left:30rpx}\n.storeitem .radio .radio-img{width:100%;height:100%}\n\n.pstime-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\n.pstime-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\n.pstime-item .radio .radio-img{width:100%;height:100%}\n\n.cuxiao-desc{width:100%}\n.cuxiao-item{display: flex;padding:0 40rpx 20rpx 40rpx;}\n.cuxiao-item .type-name{font-size:28rpx; color: #49aa34;margin-bottom: 10rpx;flex:1}\n.cuxiao-item .radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\n.cuxiao-item .radio .radio-img{width:100%;height:100%}\n\n\n.form-item {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center;justify-content:space-between}\n.form-item .label {color: #333;width: 200rpx;flex-shrink:0}\n.form-item .radio{transform:scale(.7);}\n.form-item .checkbox{transform:scale(.7);}\n.form-item .input {border:0px solid #eee;height: 70rpx;padding-left: 10rpx;text-align: right;flex:1}\n.form-item .textarea{height:140rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}\n.form-item .radio-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\n.form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\n.form-item .radio2{display:flex;align-items:center;}\n.form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}\n.form-item .checkbox-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\n.form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\n.form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}\n.form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}\n.form-item .picker{height: 70rpx;line-height:70rpx;flex:1;text-align:right}\n\n.form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\n.form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}\n.form-imgbox-close .image{width:100%;height:100%}\n.form-imgbox-img{display: block;width:180rpx;height:180rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\n.form-imgbox-img>.image{width:100%;height:100%}\n.form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\n.form-uploadbtn{position:relative;height:180rpx;width:180rpx;margin-right: 16rpx;margin-bottom:10rpx;}\n\n.storeviewmore{width:100%;text-align:center;color:#889;height:40rpx;line-height:40rpx;margin-top:10rpx}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754212967626\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}