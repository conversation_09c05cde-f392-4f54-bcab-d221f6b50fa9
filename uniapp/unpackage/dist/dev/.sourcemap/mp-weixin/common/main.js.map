{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/App.vue?4970", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/App.vue?1588", "uni-app:///App.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/App.vue?ac3d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/App.vue?bea3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "config", "productionTip", "mixin", "onShareAppMessage", "_sharewx", "onShareTimeline", "sharewxdata", "query", "path", "split", "title", "imageUrl", "onNavigationBarButtonTap", "e", "console", "log", "app", "getApp", "type", "nowurl", "_url", "indexOf", "goto", "globalData", "indexurl", "methods", "currentTarget", "dataset", "url", "opentype", "goback", "getmenuindex", "menuindex", "loaded", "obj", "desc", "that", "uni", "stopPullDownRefresh", "isinit", "get", "isload", "_sharemp", "getdata", "getplatform", "platform", "pages", "getCurrentPages", "currentPage", "length", "<PERSON><PERSON><PERSON>", "route", "__route__", "opt", "id", "cid", "gid", "bid", "currentfullurl", "sharelist", "initdata", "i", "pic", "link", "scene", "sharepath", "scenes", "mid", "push", "join", "parseInt", "Date", "getTime", "tolink", "name", "imgUrl", "callback", "currentpath", "t", "text", "color1", "color2", "color1rgb", "color2rgb", "textset", "inArray", "search", "array", "isNull", "param", "isObject", "isEmptyObject", "undefined", "isEmpty", "list", "JSON", "stringify", "dateFormat", "time", "format", "date", "Y", "getFullYear", "m", "getMonth", "d", "getDate", "H", "getHours", "getMinutes", "s", "getSeconds", "replace", "getDistance", "lat1", "lng1", "lat2", "lng2", "rad1", "Math", "PI", "rad2", "a", "b", "r", "juli", "asin", "sqrt", "pow", "sin", "cos", "toFixed", "showMap", "latitude", "parseFloat", "longitude", "scale", "address", "openLocation", "previewImage", "imgurl", "imgurls", "urls", "current", "copy", "setClipboardData", "data", "success", "error", "subscribeMessage", "tmplids", "requestSubscribeMessage", "tmplIds", "res", "post", "tmplid", "fail", "shou<PERSON><PERSON>", "redirect_url", "Promise", "resolve", "reject", "loading", "detail", "appinfo", "canIUse", "requestMerchantTransfer", "mchId", "wxpay_mchid", "appId", "getAccountInfoSync", "miniProgram", "package", "wx_package_info", "status", "setTimeout", "msg", "j<PERSON><PERSON>", "require", "ready", "checkJsApi", "jsApiList", "checkResult", "WeixinJSBridge", "invoke", "appid", "err_msg", "alert", "App", "mpType", "store", "$mount", "pre_url", "baseurl", "session_id", "aid", "pid", "needAuth", "platform2", "<PERSON><PERSON><PERSON><PERSON>", "sysset", "menudata", "menu2data", "currentIndex", "socketOpen", "socket_token", "socketMsgQueue", "socketConnecttimes", "socketInterval", "homeNavigationCustom", "usercenterNavigationCustom", "businessindexNavigationCustom", "navigationBarBackgroundColor", "navigationBarTextStyle", "rewardedVideoAd", "seetype", "trid", "hide_home_button", "indextipstatus", "qrcode", "priceRate", "maidan_bid", "wcode", "regbid", "othermid", "wxregyqcode", "onLaunch", "needpopup", "onShow", "qrcode_url", "livePlayer", "isPhone", "isIdCard", "getopts", "showCancel", "content", "confirm", "icon", "duration", "showLoading", "mask", "parseJSON", "<PERSON><PERSON><PERSON>", "params", "thispage", "tablist", "tourl", "extInfo", "corpId", "phoneNumber", "location", "tourlArr", "sendmid", "extraData", "card_id", "complete", "jwd", "showToast", "hid", "adUnitId", "tel", "tmpl", "finderUserName", "system", "wifi_name", "wifi_password", "SSID", "password", "mv", "urlpath", "args", "num", "urlbase", "str", "arr", "value", "page", "toMiniProgram", "prePage", "request", "method", "authlogin", "ali_appid", "delta", "baselogin", "code", "yqcode", "lang", "userinfo", "setinitdata", "openSocket", "clearInterval", "sendSocketMessage", "chooseImage", "otherParam", "count", "sizeType", "sourceType", "imageUrls", "filePath", "uploadednum", "chooseFile", "up_url", "getMapZoom", "distance", "zoom", "getLocation", "showModalTitle", "confirmText", "getLocationCache", "setLocationCache", "locationCache", "setLocationCacheData", "setCache", "expire_time", "getCache", "expiretime", "removeCache", "_<PERSON>url", "checkUpdateVersion", "updateManager", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "timer", "fn", "<PERSON>hrottle", "last"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAAuC;AAAA;AAHvC;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1D;AACA;AACA;;AAEAC,YAAG,CAACC,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCF,YAAG,CAACG,KAAK,CAAC;EACTC,iBAAiB,EAAC,6BAAU;IAC3B,OAAO,IAAI,CAACC,QAAQ,EAAE;EACvB,CAAC;EACDC,eAAe,EAAC,2BAAU;IACzB,IAAIC,WAAW,GAAG,IAAI,CAACF,QAAQ,EAAE;IACjC,IAAIG,KAAK,GAAID,WAAW,CAACE,IAAI,CAAEC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAC,iBAAiB;IAC9D,OAAO;MACNC,KAAK,EAAEJ,WAAW,CAACI,KAAK;MACxBC,QAAQ,EAAEL,WAAW,CAACK,QAAQ;MAC9BJ,KAAK,EAAEA;IACR,CAAC;EACF,CAAC;EACDK,wBAAwB,oCAACC,CAAC,EAAE;IAC3BC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;IACd,IAAIG,GAAG,GAAGC,MAAM,EAAE;IAClB,IAAGJ,CAAC,CAACK,IAAI,IAAI,MAAM,EAAC;MACnB,IAAIC,MAAM,GAAGH,GAAG,CAACI,IAAI,EAAE;MACvB,IAAGD,MAAM,CAACE,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAC;QAClCL,GAAG,CAACM,IAAI,CAAC,oBAAoB,EAAC,UAAU,CAAC;MAC1C,CAAC,MAAI;QACJN,GAAG,CAACM,IAAI,CAACN,GAAG,CAACO,UAAU,CAACC,QAAQ,EAAC,UAAU,CAAC;MAC7C;IACD;EACA,CAAC;EACFC,OAAO,EAAE;IACRH,IAAI,EAAC,cAAST,CAAC,EAAC;MACfI,MAAM,EAAE,CAACK,IAAI,CAACT,CAAC,CAACa,aAAa,CAACC,OAAO,CAACC,GAAG,EAACf,CAAC,CAACa,aAAa,CAACC,OAAO,CAACE,QAAQ,CAAC;IAC5E,CAAC;IACDC,MAAM,EAAC,kBAAU;MAChBb,MAAM,EAAE,CAACa,MAAM,EAAE;IAClB,CAAC;IACDC,YAAY,EAAC,sBAASC,SAAS,EAAC;MAC/B,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC3B,CAAC;IACDC,MAAM,EAAC,gBAASC,GAAG,EAAC;MACnB,IAAGA,GAAG,IAAIA,GAAG,CAACxB,KAAK,IAAI,CAACwB,GAAG,CAACC,IAAI,EAAED,GAAG,CAACC,IAAI,GAAGD,GAAG,CAACxB,KAAK;MACtD,IAAI0B,IAAI,GAAG,IAAI;MACfC,GAAG,CAACC,mBAAmB,EAAE;MACzB,IAAItB,GAAG,GAAGC,MAAM,EAAE;MAClB,IAAGD,GAAG,CAACO,UAAU,CAACgB,MAAM,IAAI,KAAK,EAAC;QACjCvB,GAAG,CAACwB,GAAG,CAAC,iBAAiB,EAAC,CAAC,CAAC,EAAC,YAAU;UACtCJ,IAAI,CAACK,MAAM,GAAG,IAAI;UAClBL,IAAI,CAACM,QAAQ,CAACR,GAAG,CAAC;QACnB,CAAC,CAAC;MACH,CAAC,MAAI;QACJ,IAAI,CAACO,MAAM,GAAG,IAAI;QAClB,IAAI,CAACC,QAAQ,CAACR,GAAG,CAAC;MACnB;IACD,CAAC;IACDS,OAAO,EAAC,mBAAU;MACjB,IAAIP,IAAI,GAAG,IAAI;MACfnB,MAAM,EAAE,CAACuB,GAAG,CAAC,iBAAiB,EAAC,CAAC,CAAC,EAAC,YAAU;QAC3CJ,IAAI,CAACH,MAAM,EAAE;MACd,CAAC,CAAC;IACH,CAAC;IACDW,WAAW,EAAC,uBAAU;MACrB,OAAO3B,MAAM,EAAE,CAACM,UAAU,CAACsB,QAAQ;IACpC,CAAC;IACDH,QAAQ,EAAC,kBAASR,GAAG,EAAC,CA6DtB,CAAC;IACD9B,QAAQ,EAAC,kBAAS8B,GAAG,EAAC;MACrB,IAAG,CAACA,GAAG,EAAEA,GAAG,GAAG,CAAC,CAAC;MACjB,IAAIlB,GAAG,GAAGC,MAAM,EAAE;MAClB,IAAI6B,KAAK,GAAGC,eAAe,EAAE,CAAC,CAAC;MAC/B,IAAIC,WAAW,GAAGF,KAAK,CAACA,KAAK,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;MAC3C,IAAIC,UAAU,GAAG,GAAG,IAAIF,WAAW,CAACG,KAAK,GAAGH,WAAW,CAACG,KAAK,GAAGH,WAAW,CAACI,SAAS,CAAC,CAAC,CAAC;MACxF,IAAI7C,KAAK,GAAG,EAAE;MAEd,IAAI8C,GAAG,GAAG,IAAI,CAACA,GAAG;MAClB,IAAG,IAAI,CAACA,GAAG,IAAI,IAAI,CAACA,GAAG,CAACC,EAAE,EAAC;QAC1B/C,KAAK,IAAE,MAAM,GAAC,IAAI,CAAC8C,GAAG,CAACC,EAAE;MAC1B,CAAC,MAAK,IAAG,IAAI,CAACD,GAAG,IAAI,IAAI,CAACA,GAAG,CAACE,GAAG,EAAC;QACjChD,KAAK,IAAE,OAAO,GAAC,IAAI,CAAC8C,GAAG,CAACE,GAAG;MAC5B,CAAC,MAAK,IAAG,IAAI,CAACF,GAAG,IAAI,IAAI,CAACA,GAAG,CAACG,GAAG,EAAC;QACjCjD,KAAK,IAAE,OAAO,GAAC,IAAI,CAAC8C,GAAG,CAACG,GAAG;MAC5B,CAAC,MAAK,IAAG,IAAI,CAACH,GAAG,IAAI,IAAI,CAACA,GAAG,CAACI,GAAG,EAAC;QACjClD,KAAK,IAAE,OAAO,GAAC,IAAI,CAAC8C,GAAG,CAACI,GAAG;MAC5B;MACA,IAAIC,cAAc,GAAGR,UAAU,GAAC3C,KAAK;MACrC,IAAIoD,SAAS,GAAG3C,GAAG,CAACO,UAAU,CAACqC,QAAQ,CAACD,SAAS;MACjD,IAAGA,SAAS,EAAC;QACZ,KAAI,IAAIE,CAAC,GAAC,CAAC,EAACA,CAAC,GAACF,SAAS,CAACV,MAAM,EAACY,CAAC,EAAE,EAAC;UAClC,IAAIF,SAAS,CAACE,CAAC,CAAC,CAAC,aAAa,CAAC,IAAE,CAAC,IAAIF,SAAS,CAACE,CAAC,CAAC,CAAC,UAAU,CAAC,IAAIX,UAAU,IAAM,CAACS,SAAS,CAACE,CAAC,CAAC,CAAC,aAAa,CAAC,IAAIF,SAAS,CAACE,CAAC,CAAC,CAAC,UAAU,CAAC,IAAIH,cAAe,EAAC;YAC7JxB,GAAG,CAACxB,KAAK,GAAGiD,SAAS,CAACE,CAAC,CAAC,CAACnD,KAAK;YAC9BwB,GAAG,CAACC,IAAI,GAAGwB,SAAS,CAACE,CAAC,CAAC,CAAC1B,IAAI;YAC5BD,GAAG,CAAC4B,GAAG,GAAGH,SAAS,CAACE,CAAC,CAAC,CAACC,GAAG;YAC1B5B,GAAG,CAAC6B,IAAI,GAAGJ,SAAS,CAACE,CAAC,CAAC,CAACjC,GAAG;UAC5B;QACD;MACD;MACA;MACA,IAAIoC,KAAK,GAAG,EAAE;MACd,IAAG9B,GAAG,CAAC6B,IAAI,EAAC;QACX,IAAIE,SAAS,GAAG/B,GAAG,CAAC6B,IAAI;QACxB,IAAIG,MAAM,GAAG,EAAE;QACf,IAAGhC,GAAG,CAAC6B,IAAI,CAAC1C,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAC;UAChC,IAAGL,GAAG,CAACO,UAAU,CAAC4C,GAAG,EAAC;YACrBH,KAAK,CAACI,IAAI,CAAC,MAAM,GAACpD,GAAG,CAACO,UAAU,CAAC4C,GAAG,CAAC;YACrCD,MAAM,GAAGF,KAAK,CAACK,IAAI,CAAC,GAAG,CAAC;UACzB;QACD;QACA,IAAGJ,SAAS,IAAIA,SAAS,CAAC5C,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAC;UAC1C4C,SAAS,GAAGA,SAAS,CAACxD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACpC;QACA,IAAGyD,MAAM,IAAKhC,GAAG,CAAC6B,IAAI,CAAC1C,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAE,EAAC;UAC9C,IAAGa,GAAG,CAAC6B,IAAI,IAAI7B,GAAG,CAAC6B,IAAI,CAAC1C,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAC;YACxC4C,SAAS,GAAGA,SAAS,GAAG,SAAS,GAACC,MAAM,GAAG,KAAK,GAACI,QAAQ,CAAE,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE,GAAE,IAAI,CAAC;UACvF,CAAC,MAAI;YACJP,SAAS,GAAGA,SAAS,GAAG,SAAS,GAACC,MAAM,GAAG,KAAK,GAACI,QAAQ,CAAE,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE,GAAE,IAAI,CAAC;UACvF;QACD;MACD,CAAC,MAAI;QACJ;QACA,IAAGtC,GAAG,CAACuC,MAAM,EAAC;UACb,IAAIP,OAAM,GAAG,EAAE;UACf,IAAGhC,GAAG,CAACuC,MAAM,CAACpD,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAC;YAClC,IAAGL,GAAG,CAACO,UAAU,CAAC4C,GAAG,EAAC;cACrBH,KAAK,CAACI,IAAI,CAAC,MAAM,GAACpD,GAAG,CAACO,UAAU,CAAC4C,GAAG,CAAC;cACrCD,OAAM,GAAGF,KAAK,CAACK,IAAI,CAAC,GAAG,CAAC;YACzB;UACD;UACA,IAAGnC,GAAG,CAACuC,MAAM,CAACpD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAC;YAC9B4C,SAAS,GAAG/B,GAAG,CAACuC,MAAM,CAAChE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,MAAI;YACJwD,SAAS,GAAG/B,GAAG,CAACuC,MAAM;UACvB;UACA,IAAGP,OAAM,IAAKhC,GAAG,CAACuC,MAAM,CAACpD,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAE,EAAC;YAChD,IAAGa,GAAG,CAACuC,MAAM,CAACpD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAC;cAC9B4C,SAAS,GAAGA,SAAS,GAAG,SAAS,GAACC,OAAM,GAAG,KAAK,GAACI,QAAQ,CAAE,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE,GAAE,IAAI,CAAC;YACvF,CAAC,MAAI;cACJP,SAAS,GAAGA,SAAS,GAAG,SAAS,GAACC,OAAM,GAAG,KAAK,GAACI,QAAQ,CAAE,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE,GAAE,IAAI,CAAC;YACvF;UACD;QACD,CAAC,MAAI;UACJ,IAAIP,SAAS,GAAG,IAAI,CAACA,SAAS,EAAE;QACjC;MACD;MACAnD,OAAO,CAACC,GAAG,CAAC,WAAW,EAACkD,SAAS,CAAC;MAClC,IAAG/B,GAAG,CAACxB,KAAK,EAAC;QACZ,IAAIA,KAAK,GAAGwB,GAAG,CAACxB,KAAK;MACtB,CAAC,MAAI;QACJ,IAAIA,KAAK,GAAGM,GAAG,CAACO,UAAU,CAACqC,QAAQ,CAACc,IAAI;MACzC;MACA,IAAGxC,GAAG,CAAC4B,GAAG,EAAC;QACV,IAAIa,MAAM,GAAGzC,GAAG,CAAC4B,GAAG;MACrB,CAAC,MAAI;QACJ,IAAIa,MAAM,GAAG,EAAE;MAChB;MACA,OAAOzC,GAAG,CAAC0C,QAAQ,IAAI,UAAU,IAAI1C,GAAG,CAAC0C,QAAQ,EAAE;MACnD,OAAO;QACNlE,KAAK,EAAEA,KAAK;QACZF,IAAI,EAAEyD,SAAS;QACftD,QAAQ,EAAEgE;MACX,CAAC;IACF,CAAC;IACDV,SAAS,EAAC,qBAAU;MACnB,IAAIjD,GAAG,GAAGC,MAAM,EAAE;MAClB,IAAIoC,GAAG,GAAG,IAAI,CAACA,GAAG;MAOlB,IAAIwB,WAAW,GAAG,GAAG,IAAI,IAAI,CAAC1B,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;;MAEpE,IAAIY,KAAK,GAAG,EAAE;MACd,KAAI,IAAIH,CAAC,IAAIR,GAAG,EAAC;QAChB,IAAGQ,CAAC,IAAI,KAAK,IAAIA,CAAC,IAAI,OAAO,EAAC;UAC7BG,KAAK,CAACI,IAAI,CAACP,CAAC,GAAC,GAAG,GAACR,GAAG,CAACQ,CAAC,CAAC,CAAC;QACzB;MACD;MACA/C,OAAO,CAACC,GAAG,CAACC,GAAG,CAACO,UAAU,CAAC4C,GAAG,CAAC;MAC/B,IAAGnD,GAAG,CAACO,UAAU,CAAC4C,GAAG,EAAC;QACrBH,KAAK,CAACI,IAAI,CAAC,MAAM,GAACpD,GAAG,CAACO,UAAU,CAAC4C,GAAG,CAAC;MACtC;MACA,IAAID,MAAM,GAAGF,KAAK,CAACK,IAAI,CAAC,GAAG,CAAC;MAC5B,IAAGH,MAAM,EAAC;QACTW,WAAW,GAAGA,WAAW,GAAG,SAAS,GAACX,MAAM,GAAG,KAAK,GAACI,QAAQ,CAAE,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE,GAAE,IAAI,CAAC;MAC3F;MACA,OAAOK,WAAW;IACnB,CAAC;IACDC,CAAC,EAAC,WAASC,IAAI,EAAC;MACf,IAAGA,IAAI,IAAE,QAAQ,EAAC;QACjB,OAAO9D,MAAM,EAAE,CAACM,UAAU,CAACqC,QAAQ,CAACoB,MAAM;MAC3C,CAAC,MAAK,IAAGD,IAAI,IAAE,QAAQ,EAAC;QACvB,OAAO9D,MAAM,EAAE,CAACM,UAAU,CAACqC,QAAQ,CAACqB,MAAM;MAC3C,CAAC,MAAK,IAAGF,IAAI,IAAE,WAAW,EAAC;QAC1B,IAAIG,SAAS,GAAGjE,MAAM,EAAE,CAACM,UAAU,CAACqC,QAAQ,CAACsB,SAAS;QACtD,OAAOA,SAAS,CAAC,KAAK,CAAC,GAAC,GAAG,GAACA,SAAS,CAAC,OAAO,CAAC,GAAC,GAAG,GAACA,SAAS,CAAC,MAAM,CAAC;MACrE,CAAC,MAAK,IAAGH,IAAI,IAAE,WAAW,EAAC;QAC1B,IAAII,SAAS,GAAGlE,MAAM,EAAE,CAACM,UAAU,CAACqC,QAAQ,CAACuB,SAAS;QACtD,OAAOA,SAAS,CAAC,KAAK,CAAC,GAAC,GAAG,GAACA,SAAS,CAAC,OAAO,CAAC,GAAC,GAAG,GAACA,SAAS,CAAC,MAAM,CAAC;MACrE,CAAC,MAAI;QACJ,OAAOlE,MAAM,EAAE,CAACM,UAAU,CAACqC,QAAQ,CAACwB,OAAO,GAAInE,MAAM,EAAE,CAACM,UAAU,CAACqC,QAAQ,CAACwB,OAAO,CAACL,IAAI,CAAC,GAAG9D,MAAM,EAAE,CAACM,UAAU,CAACqC,QAAQ,CAACwB,OAAO,CAACL,IAAI,CAAC,GAAGA,IAAI,GAAIA,IAAI;MACtJ;IACD,CAAC;IACDM,OAAO,EAAE,iBAAUC,MAAM,EAAEC,KAAK,EAAE;MACjC,KAAK,IAAI1B,CAAC,IAAI0B,KAAK,EAAE;QACpB,IAAIA,KAAK,CAAC1B,CAAC,CAAC,IAAIyB,MAAM,EAAE;UACvB,OAAO,IAAI;QACZ;MACD;MACA,OAAO,KAAK;IACb,CAAC;IACDE,MAAM,EAAC,gBAASC,KAAK,EAAC;MACrB,IAAG,IAAI,CAACC,QAAQ,CAACD,KAAK,CAAC,EAAC;QACvB,OAAO,IAAI,CAACE,aAAa,CAACF,KAAK,CAAC;MACjC;MACA,OAAQA,KAAK,IAAIG,SAAS,IAAIH,KAAK,IAAI,WAAW,IAAIA,KAAK,IAAI,IAAI,IAAIA,KAAK,IAAI,EAAE;IACnF,CAAC;IACDI,OAAO,EAAC,iBAASC,IAAI,EAAC;MACrB,IAAI,CAACA,IAAI,IAAIA,IAAI,CAAC7C,MAAM,KAAK,CAAC,EAAE;QAC/B,OAAO,IAAI;MACZ;MACA,IAAG,IAAI,CAACyC,QAAQ,CAACI,IAAI,CAAC,EAAC;QACtB,OAAO,IAAI,CAACH,aAAa,CAACG,IAAI,CAAC;MAChC;MACA,OAAQ,CAACA,IAAI,IAAIA,IAAI,CAAC7C,MAAM,KAAK,CAAC,IAAK6C,IAAI,CAAC7C,MAAM,KAAK,CAAC,KAAK,CAAC6C,IAAI,CAAE,CAAC,CAAE,IAAIA,IAAI,CAAE,CAAC,CAAE,CAAC7C,MAAM,KAAK,CAAC,CAAE;IACpG,CAAC;IACD0C,aAAa,EAAC,uBAASzD,GAAG,EAAE;MAC1B,OAAO6D,IAAI,CAACC,SAAS,CAAC9D,GAAG,CAAC,KAAK,IAAI;IACrC,CAAC;IACDwD,QAAQ,EAAC,kBAASxD,GAAG,EAAE;MACrB,OAAO,sBAAOA,GAAG,MAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI;IAChD,CAAC;IACD+D,UAAU,EAAC,oBAASC,IAAI,EAACC,MAAM,EAAC;MAC/B,IAAGA,MAAM,IAAIP,SAAS,IAAIO,MAAM,IAAI,WAAW,IAAIA,MAAM,IAAI,IAAI,IAAIA,MAAM,IAAI,EAAE,EAAEA,MAAM,GAAG,aAAa;MACzG,IAAIC,IAAI,GAAG,IAAI7B,IAAI,EAAE;MACrB,IAAG2B,IAAI,IAAI,EAAE,IAAIA,IAAI,GAAG,CAAC,EAAE;QAC1BE,IAAI,GAAG,IAAI7B,IAAI,CAAC2B,IAAI,GAAG,IAAI,CAAC;MAC7B;MAEA,IAAIG,CAAC,GAAGD,IAAI,CAACE,WAAW,EAAE;MAC1B,IAAIC,CAAC,GAAIH,IAAI,CAACI,QAAQ,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,IAAIJ,IAAI,CAACI,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAGJ,IAAI,CAACI,QAAQ,EAAE,GAAG,CAAE;MACtF,IAAIC,CAAC,GAAGL,IAAI,CAACM,OAAO,EAAE,GAAG,EAAE,GAAG,GAAG,GAAGN,IAAI,CAACM,OAAO,EAAE,GAAGN,IAAI,CAACM,OAAO,EAAE;MACnE,IAAIC,CAAC,GAAGP,IAAI,CAACQ,QAAQ,EAAE,GAAG,EAAE,GAAG,GAAG,GAAGR,IAAI,CAACQ,QAAQ,EAAE,GAAGR,IAAI,CAACQ,QAAQ,EAAE;MACtE,IAAI/C,CAAC,GAAGuC,IAAI,CAACS,UAAU,EAAE,GAAG,EAAE,GAAG,GAAG,GAAGT,IAAI,CAACS,UAAU,EAAE,GAAGT,IAAI,CAACS,UAAU,EAAE;MAC5E,IAAIC,CAAC,GAAGV,IAAI,CAACW,UAAU,EAAE,GAAG,EAAE,GAAG,GAAG,GAAGX,IAAI,CAACW,UAAU,EAAE,GAAGX,IAAI,CAACW,UAAU,EAAE;MAC5EZ,MAAM,GAAGA,MAAM,CAACa,OAAO,CAAC,GAAG,EAACX,CAAC,CAAC;MAC9BF,MAAM,GAAGA,MAAM,CAACa,OAAO,CAAC,GAAG,EAACT,CAAC,CAAC;MAC9BJ,MAAM,GAAGA,MAAM,CAACa,OAAO,CAAC,GAAG,EAACP,CAAC,CAAC;MAC9BN,MAAM,GAAGA,MAAM,CAACa,OAAO,CAAC,GAAG,EAACL,CAAC,CAAC;MAC9BR,MAAM,GAAGA,MAAM,CAACa,OAAO,CAAC,GAAG,EAACnD,CAAC,CAAC;MAC9BsC,MAAM,GAAGA,MAAM,CAACa,OAAO,CAAC,GAAG,EAACF,CAAC,CAAC;MAC9B,OAAOX,MAAM;IACd,CAAC;IACDc,WAAW,EAAE,qBAAUC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;MAC9C,IAAG,CAACH,IAAI,IAAI,CAACC,IAAI,IAAI,CAACC,IAAI,IAAI,CAACC,IAAI,EAAE,OAAO,EAAE;MAC9C,IAAIC,IAAI,GAAGJ,IAAI,GAAGK,IAAI,CAACC,EAAE,GAAG,KAAK;MACjC,IAAIC,IAAI,GAAGL,IAAI,GAAGG,IAAI,CAACC,EAAE,GAAG,KAAK;MACjC,IAAIE,CAAC,GAAGJ,IAAI,GAAGG,IAAI;MACnB,IAAIE,CAAC,GAAGR,IAAI,GAAGI,IAAI,CAACC,EAAE,GAAG,KAAK,GAAGH,IAAI,GAAGE,IAAI,CAACC,EAAE,GAAG,KAAK;MACvD,IAAII,CAAC,GAAG,OAAO;MACf,IAAIC,IAAI,GAAGD,CAAC,GAAG,CAAC,GAAGL,IAAI,CAACO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAACR,IAAI,CAACS,GAAG,CAACT,IAAI,CAACU,GAAG,CAACP,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGH,IAAI,CAACW,GAAG,CAACZ,IAAI,CAAC,GAAGC,IAAI,CAACW,GAAG,CAACT,IAAI,CAAC,GAAGF,IAAI,CAACS,GAAG,CAACT,IAAI,CAACU,GAAG,CAACN,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACtIE,IAAI,GAAGA,IAAI,GAAC,IAAI;MAChBA,IAAI,GAAGA,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC;MACtB,OAAON,IAAI;IACZ,CAAC;IACDO,OAAO,EAAC,iBAASvH,CAAC,EAAC;MAClB,IAAIwH,QAAQ,GAAGC,UAAU,CAACzH,CAAC,CAACa,aAAa,CAACC,OAAO,CAAC0G,QAAQ,CAAC;MAC3D,IAAIE,SAAS,GAAGD,UAAU,CAACzH,CAAC,CAACa,aAAa,CAACC,OAAO,CAAC4G,SAAS,CAAC;MAC7D,IAAIC,KAAK,GAAG3H,CAAC,CAACa,aAAa,CAACC,OAAO,CAAC6G,KAAK,GAAClE,QAAQ,CAACzD,CAAC,CAACa,aAAa,CAACC,OAAO,CAAC6G,KAAK,CAAC,GAAC,EAAE;MACpF,IAAI9D,IAAI,GAAG7D,CAAC,CAACa,aAAa,CAACC,OAAO,CAAC+C,IAAI;MACvC,IAAI+D,OAAO,GAAG5H,CAAC,CAACa,aAAa,CAACC,OAAO,CAAC8G,OAAO;MAC7CpG,GAAG,CAACqG,YAAY,CAAC;QAChBL,QAAQ,EAACA,QAAQ;QACjBE,SAAS,EAACA,SAAS;QACnB7D,IAAI,EAACA,IAAI;QACT+D,OAAO,EAACA,OAAO;QACfD,KAAK,EAAE;MACR,CAAC,CAAC;IACH,CAAC;IACDG,YAAY,EAAE,sBAAU9H,CAAC,EAAE;MAC1B,IAAI+H,MAAM,GAAG/H,CAAC,CAACa,aAAa,CAACC,OAAO,CAACC,GAAG;MACxC,IAAIiH,OAAO,GAAGhI,CAAC,CAACa,aAAa,CAACC,OAAO,CAACmH,IAAI;MAC1C,IAAI,CAACD,OAAO,EAAEA,OAAO,GAAGD,MAAM;MAC9B,IAAG,CAACC,OAAO,EAAE;MACb,IAAI,OAAQA,OAAQ,IAAI,QAAQ,EAAEA,OAAO,GAAGA,OAAO,CAACpI,KAAK,CAAC,GAAG,CAAC;MAC9D4B,GAAG,CAACsG,YAAY,CAAC;QAChBI,OAAO,EAAEH,MAAM;QACfE,IAAI,EAAED;MACP,CAAC,CAAC;IACH,CAAC;IACDG,IAAI,EAAE,cAASnI,CAAC,EAAE;MACjBwB,GAAG,CAAC4G,gBAAgB,CAAC;QACpBC,IAAI,EAAErI,CAAC,CAACa,aAAa,CAACC,OAAO,CAACoD,IAAI;QAClCoE,OAAO,EAAE,mBAAY;UACpBlI,MAAM,EAAE,CAACmI,KAAK,CAAC,MAAM,CAAC;QACvB;MACD,CAAC,CAAC;IACH,CAAC;IACDC,gBAAgB,EAAC,0BAASzE,QAAQ,EAAC;MAClC,IAAI5D,GAAG,GAAGC,MAAM,EAAE;MAElB,IAAImB,IAAI,GAAG,IAAI;MACf,IAAIkH,OAAO,GAAGlH,IAAI,CAACkH,OAAO;MAC1B,IAAGA,OAAO,IAAIA,OAAO,CAACrG,MAAM,GAAG,CAAC,EAAC;QAChCZ,GAAG,CAACkH,uBAAuB,CAAC;UAC3BC,OAAO,EAAEF,OAAO;UAChBH,OAAO,EAAC,iBAASM,GAAG,EAAE;YACrB,KAAI,IAAI5F,CAAC,IAAIyF,OAAO,EAAC;cACpB,IAAGG,GAAG,CAACH,OAAO,CAACzF,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAC;gBAC9B7C,GAAG,CAAC0I,IAAI,CAAC,2BAA2B,EAAC;kBAACC,MAAM,EAACL,OAAO,CAACzF,CAAC;gBAAC,CAAC,EAAC,YAAU,CAAC,CAAC,CAAC;cACvE;YACD;YACA/C,OAAO,CAACC,GAAG,CAAC0I,GAAG,CAAC;YAChB,OAAO7E,QAAQ,IAAI,UAAU,IAAIA,QAAQ,EAAE;UAC5C,CAAC;UACDgF,IAAI,EAAC,cAASH,GAAG,EAAC;YACjB3I,OAAO,CAACC,GAAG,CAAC0I,GAAG,CAAC;YAChB,OAAO7E,QAAQ,IAAI,UAAU,IAAIA,QAAQ,EAAE;UAC5C;QACD,CAAC,CAAC;MACH,CAAC,MAAI;QACJ,OAAOA,QAAQ,IAAI,UAAU,IAAIA,QAAQ,EAAE;MAC5C;IAKD,CAAC;IACDiF,QAAQ,EAAC,kBAASvG,EAAE,EAAyB;MAAA;MAAA,IAAxBpC,IAAI,uEAAC,EAAE;MAAA,IAAC4I,YAAY,uEAAC,EAAE;MAC3C;MACAhJ,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1B,OAAO,IAAIgJ,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACvC,IAAI7H,IAAI,GAAG,KAAI;QACf,IAAGpB,GAAG,CAACO,UAAU,CAACsB,QAAQ,IAAI,IAAI,IAAI7B,GAAG,CAACO,UAAU,CAACsB,QAAQ,IAAI,IAAI,EAAC;UACrE7B,GAAG,CAACoI,KAAK,CAAC,WAAW,CAAC;UACtB;QACD;QACAhH,IAAI,CAAC8H,OAAO,GAAG,IAAI;QACnBlJ,GAAG,CAAC0I,IAAI,CAAC,uBAAuB,EAAE;UAACpG,EAAE,EAACA,EAAE;UAACpC,IAAI,EAACA;QAAI,CAAC,EAAE,UAAUuI,GAAG,EAAE;UACnErH,IAAI,CAAC8H,OAAO,GAAG,KAAK;UACpB,IAAIC,MAAM,GAAGV,GAAG,CAACU,MAAM;UACvB,IAAIC,OAAO,GAAGX,GAAG,CAACW,OAAO;UACzB,IAAGD,MAAM,CAACtH,QAAQ,IAAE7B,GAAG,CAACO,UAAU,CAACsB,QAAQ,EAAC;YAC3C7B,GAAG,CAACoI,KAAK,CAAC,aAAa,CAAC;YACxB;UACD;UACA,IAAGpI,GAAG,CAACO,UAAU,CAACsB,QAAQ,IAAI,IAAI,EAAE;YACnC,IAAIjD,EAAE,CAACyK,OAAO,CAAC,yBAAyB,CAAC,EAAE;cACzCzK,EAAE,CAAC0K,uBAAuB,CAAC;gBAC5BC,KAAK,EAAEH,OAAO,CAACI,WAAW;gBAC1BC,KAAK,EAAE7K,EAAE,CAAC8K,kBAAkB,EAAE,CAACC,WAAW,CAACF,KAAK;gBAChDG,OAAO,EAAET,MAAM,CAACU,eAAe;gBAC/B1B,OAAO,EAAE,iBAACM,GAAG,EAAK;kBAChB;kBACA3I,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE0I,GAAG,CAAC;kBAC5BrH,IAAI,CAAC8H,OAAO,GAAG,IAAI;kBACnBlJ,GAAG,CAAC0I,IAAI,CAAC,6BAA6B,EAAE;oBAACpG,EAAE,EAACA,EAAE;oBAACpC,IAAI,EAACA;kBAAI,CAAC,EAAE,UAAUuI,GAAG,EAAE;oBACzErH,IAAI,CAAC8H,OAAO,GAAG,KAAK;oBACpB,IAAGT,GAAG,CAACqB,MAAM,IAAE,CAAC,EAAC;sBAChB,IAAGhB,YAAY,EAAC;wBACfiB,UAAU,CAAC,YAAY;0BACtB/J,GAAG,CAACM,IAAI,CAACwI,YAAY,CAAC;wBACvB,CAAC,EAAE,IAAI,CAAC;sBACT;sBACA9I,GAAG,CAACmI,OAAO,CAACM,GAAG,CAACuB,GAAG,CAAC;sBACtBhB,OAAO,CAAC,IAAI,CAAC;oBACZ,CAAC,MAAI;sBACJhJ,GAAG,CAACoI,KAAK,CAACK,GAAG,CAACuB,GAAG,CAAC;sBAClB,IAAGlB,YAAY,EAAC;wBACfiB,UAAU,CAAC,YAAY;0BACtB/J,GAAG,CAACM,IAAI,CAACwI,YAAY,CAAC;wBACvB,CAAC,EAAE,IAAI,CAAC;sBACT;sBACFE,OAAO,CAAC,KAAK,CAAC;oBACb;kBACD,CAAC,CAAC;gBACJ,CAAC;gBACDJ,IAAI,EAAE,cAACH,GAAG,EAAK;kBACb3I,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE0I,GAAG,CAAC;kBACzB,IAAGK,YAAY,EAAC;oBAChBiB,UAAU,CAAC,YAAY;sBACrB/J,GAAG,CAACM,IAAI,CAACwI,YAAY,CAAC;oBACxB,CAAC,EAAE,IAAI,CAAC;kBACR;kBACAE,OAAO,CAAC,KAAK,CAAC;gBAChB;cACC,CAAC,CAAC;YACJ,CAAC,MAAM;cACNhJ,GAAG,CAACoI,KAAK,CAAC,oBAAoB,CAAC;cAC/B,IAAGU,YAAY,EAAC;gBACfiB,UAAU,CAAC,YAAY;kBACtB/J,GAAG,CAACM,IAAI,CAACwI,YAAY,CAAC;gBACvB,CAAC,EAAE,IAAI,CAAC;cACT;cACAE,OAAO,CAAC,KAAK,CAAC;YACf;UACD,CAAC,MAAK,IAAGhJ,GAAG,CAACO,UAAU,CAACsB,QAAQ,IAAI,IAAI,EAAC;YACxC,IAAIoI,OAAO,GAAGC,mBAAO,CAAC,wBAAgB,CAAC;YACvCpK,OAAO,CAACC,GAAG,CAACkK,OAAO,CAAC;YACpBA,OAAO,CAACE,KAAK,CAAC,YAAY;cACxBF,OAAO,CAACG,UAAU,CAAC;gBACpBC,SAAS,EAAE,CAAC,yBAAyB,CAAC;gBACtClC,OAAO,EAAE,iBAAUM,GAAG,EAAE;kBACtB,IAAIA,GAAG,CAAC6B,WAAW,CAAC,yBAAyB,CAAC,EAAE;oBACjDC,cAAc,CAACC,MAAM,CAAC,yBAAyB,EAAE;sBAChDjB,KAAK,EAAEH,OAAO,CAACI,WAAW;sBAC1BC,KAAK,EAAEL,OAAO,CAACqB,KAAK;sBACpBb,OAAO,EAAET,MAAM,CAACU;oBACf,CAAC,EACD,UAAUpB,GAAG,EAAE;sBAChB,IAAIA,GAAG,CAACiC,OAAO,KAAK,4BAA4B,EAAE;wBAChD;wBACAtJ,IAAI,CAAC8H,OAAO,GAAG,IAAI;wBACnBlJ,GAAG,CAAC0I,IAAI,CAAC,6BAA6B,EAAE;0BAACpG,EAAE,EAACA,EAAE;0BAACpC,IAAI,EAACA;wBAAI,CAAC,EAAE,UAAUuI,GAAG,EAAE;0BACzErH,IAAI,CAAC8H,OAAO,GAAG,KAAK;0BACpB,IAAGT,GAAG,CAACqB,MAAM,IAAE,CAAC,EAAC;4BAChB,IAAGhB,YAAY,EAAC;8BACfiB,UAAU,CAAC,YAAY;gCACtB/J,GAAG,CAACM,IAAI,CAACwI,YAAY,CAAC;8BACvB,CAAC,EAAE,IAAI,CAAC;4BACT;4BACA9I,GAAG,CAACmI,OAAO,CAACM,GAAG,CAACuB,GAAG,CAAC;4BACtBhB,OAAO,CAAC,IAAI,CAAC;0BACZ,CAAC,MAAI;4BACJhJ,GAAG,CAACoI,KAAK,CAACK,GAAG,CAACuB,GAAG,CAAC;4BAClB,IAAGlB,YAAY,EAAC;8BACfiB,UAAU,CAAC,YAAY;gCACtB/J,GAAG,CAACM,IAAI,CAACwI,YAAY,CAAC;8BACvB,CAAC,EAAE,IAAI,CAAC;4BACT;4BACFE,OAAO,CAAC,KAAK,CAAC;0BACb;wBACD,CAAC,CAAC;sBACJ,CAAC,MAAI;wBACJ,IAAGF,YAAY,EAAC;0BACf9I,GAAG,CAACM,IAAI,CAACwI,YAAY,CAAC;wBACvB;sBACD;oBACC,CAAC,CACF;kBACA,CAAC,MAAM;oBACR6B,KAAK,CAAC,oBAAoB,CAAC;oBAC3B,IAAG7B,YAAY,EAAC;sBACfiB,UAAU,CAAC,YAAY;wBACtB/J,GAAG,CAACM,IAAI,CAACwI,YAAY,CAAC;sBACvB,CAAC,EAAE,IAAI,CAAC;oBACT;oBACAE,OAAO,CAAC,KAAK,CAAC;kBACb;gBACF;cACC,CAAC,CAAC;YACJ,CAAC,CAAC;UACH;QACD,CAAC,CAAC;MAEH,CAAC,CAAC;IACH;EACD;AACD,CAAC,CAAC;AAEF4B,YAAG,CAACC,MAAM,GAAG,KAAK;AAElB,IAAM7K,GAAG,GAAG,IAAIjB,YAAG,iCACZ6L,YAAG;EAACE,KAAK,EAALA;AAAK,GACd;AACF,UAAA9K,GAAG,EAAC+K,MAAM,EAAE,C;;;;;;;;;;;;;AClhBZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACa;;;AAG/D;AAC0M;AAC1M,gBAAgB,iNAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAmyB,CAAgB,iyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;eCCvzB;EACAxK;IACAyK;IACAC;IACAC;IAEAC;IACAhI;IACAiI;IACAC;IACAxJ;IACAyJ;IACAC;IACAC;IACAhL;IACAiL;IACAC;IACAC;IACA/I;IACAwB;IACA7C;IACAqK;IACAC;IACAC;IACAC;IACAC;IACAhJ;IACAiJ;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;EAEA;;EACAC;IACA;IACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;MACA;MACA;MAYA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACA;IACA;IACA;MACA;MACA7L;IACA;MACA;MACA;IAAA;IAEA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;MACA;QACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;MACAA;IACA;IAiBA;IACA;IAkBAvB;IAGA;IACAuB;MACArB;MACA;QACA;QACAA;MACA;MACAA;IACA;IACAqB;MACAvB;MACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA;UACAqN;QACA;QACA,iFACA;UAAA;UACAnL;QACA;MACA;IACA;EAEA;EACAoL;IACA;IACAtN;IACAA;IACAA;IACA;IACA;MACA;MACAuB;IACA;IACA;MACA;MACA;QACA;MACA;IACA;IACA;IACA;IAQA;MACAvB;MACAuN;IACA;IAEA;IACA;IACA;IACA;MACAvN;MACA;MACAA;MACA;MACA;QACA;QACA;QACA;UACA+M;QACA;QACA;UACAC;QACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;;IAGA;IACA;IACA;MACAhN;MACA;QACA;QACAA;QACAA;QACAwN;UACA;UACAxN;UACA;YACAsB;UACA;QACA;UACAtB;QACA;MACA;QACAA;MACA;IACA;IAIA;MACA;MACAE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;EAEA;;EACAS;IACA8M;MAAA;MACA;QACA;QAAA;QACA;QAAA;QACA;MACA;;MACA;MACA;IACA;IACAC;MACA,aACA;MACA;IACA;IACAC;MACA;QACA;QACA;UACApL;QACA;MACA;MACA;QACA;QACA;QACA;QACA;UACA;UACAA;QACA;QACA;MACA;QACA;MACA;IACA;IACAsI;MACAtJ;QACA3B;QACAgO;QACAC;QACAxF;UACA;YACA;UACA;QACA;MACA;IACA;IACAyF;MACAvM;QACA3B;QACAiO;QACAD;QACAvF;UACA;YACA;UACA;YACA;UACA;QACA;MACA;IACA;IACAA;MACA;MACA;MACA9G;QACA3B;QACAmO;QACA1F;UACA;QACA;MACA;IACA;IACAC;MACA;QACA/G;MACA;QACA;QACA;QACAA;UACA3B;UACAmO;UACAC;QACA;MACA;IACA;IACAC;MACA;QACA1M;MACA;QACA;QACAA;UACA3B;UACAsO;QACA;MACA;IACA;IACA3J;MACA;QACA;UACA;QACA;MACA;MACA;IACA;IACAG;MACA;IACA;IACAyJ;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;QACAC;MACA;MACA;IACA;IACA7N;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAN;gBACAmO;gBACArO;gBAAA,MACAqO;kBAAA;kBAAA;gBAAA;gBACAC;gBACAA;gBACAA;gBAAA;cAAA;gBAGA;kBACA;kBACAC;kBACA;oBACA;sBACA;wBACAxN;sBACA;oBACA;kBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;;gBAsBA;kBACAyN;gBACA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAtO;kBACAsO;kBACAtO;gBACA;gBAAA;cAAA;gBAAA,MAIAA;kBAAA;kBAAA;gBAAA;gBACApB;kBACA2P;oBACA3N;kBACA;kBACA4N;kBACArG;kBACAS;oBACA9I;oBACAA;oBACAE;kBACA;gBACA;gBAAA;cAAA;gBAAA,MAIAsO;kBAAA;kBAAA;gBAAA;gBAAA,MACAtO;kBAAA;kBAAA;gBAAA;gBACAA;gBAAA;cAAA;gBAEA,sCAcA;kBAEAqB;oBACA8G;sBACArI;sBACA;wBACAE;sBACA;wBACA;wBACAA;sBACA;oBACA;kBACA;gBAEA;cAAA;gBAAA;cAAA;gBAAA,MAGAsO;kBAAA;kBAAA;gBAAA;gBACA;kBACAtO;gBACA;gBACA,uCAkDA;gBAAA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,MACAsO;kBAAA;kBAAA;gBAAA;gBACA;;gBAKAA;gBACAjN;kBACAoN;gBACA;gBAAA;cAAA;gBAAA,MAGAH;kBAAA;kBAAA;gBAAA;gBACA;;gBAKAA;gBACAjN;kBACAoN;gBACA;gBAAA;cAAA;gBAGA;kBAAA;kBACA;kBACAzO;oBAAAE;oBAAAoO;kBAAA;kBACA;oBACAI;kBACA;oBACAJ;kBACA;gBACA;gBACA;kBAAA;kBACA;kBACAtO;oBAAAE;oBAAAoO;kBAAA;kBACA;kBACA;oBACA;sBACAA;oBACA;sBACA;sBACAA;sBACAA;oBACA;kBACA;kBACA;kBACA;oBACAA;kBACA;kBAEA;oBACAI;kBACA;oBACAJ;kBACA;gBACA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAA;gBACAK;gBACAC;gBACA;kBACA;oBACA;sBACAA;oBACA;kBACA;gBACA;gBACAT;kBACAG;kBACAK;gBACA;gBACA;kBACA3O;oBACA;sBACAmO;sBACAA;sBACAnO;oBACA;sBACAA;oBACA;kBACA;gBACA;kBACAA;gBACA;gBAAA;cAAA;gBAGA;kBACAF;kBACAwO;kBACAK;kBAAA7O;kBACAuB;oBACAoI;oBACAjK;oBACAqP;oBACA1G;sBACArI;oBACA;kBACA;gBACA;gBAAA,MACAwO;kBAAA;kBAAA;gBAAA;gBACA;gBACAtO;kBACA8O;gBACA;kBACA;oBACA9O;oBACA;kBACA;kBACA;oBACAqB;sBACAoI;sBACA;sBACAoF;sBACA;sBACA1G;sBACAS;sBACAmG;oBACA;kBACA;oBACAL;kBACA;gBACA;gBAAA;cAAA;gBAAA,MAGAJ;kBAAA;kBAAA;gBAAA;gBACA;gBACAA;gBACAK;gBACAK;gBACA3N;kBACAgG;kBACAE;kBACA7D;kBAAA;kBACA+D;kBAAA;kBACAD;kBACAoB;oBACA9I;kBACA;gBACA;gBAAA;cAAA;gBAGA;kBACA;kBACAwO;oBACAlD;kBACA;gBACA;gBAAA,MACAkD;kBAAA;kBAAA;gBAAA;gBACA;gBACAA;gBACAK;gBACAtN;kBACA6G;kBACA+G;kBACA9G;oBACA,6CACAnI;kBACA;kBACA4I;oBACA9I;kBACA;gBACA;gBAAA;cAAA;gBAAA,MAGAwO;kBAAA;kBAAA;gBAAA;gBACAxO;gBACAwO;gBACAK;gBACA;kBACA3O;kBACAA;oBAAAkP;kBAAA;oBACA;oBACA;sBACAlP;sBAAA;oBACA;oBACA;sBACAA;wBAAAmP;sBAAA;oBACA;oBACA;oBACA7C;sBAAAtM;sBAAAsM;oBAAA;sBAAAtM;oBAAA;oBACAsM;sBACAtM;sBACAA;sBACAF;sBACAwM;sBACAA;oBACA;oBACAA;sBACA;wBACA;wBACAtM;0BAAAkP;wBAAA;0BACA;4BACAlP;0BACA;4BACAA;4BACA;8BACAA;4BACA;0BAEA;wBACA;sBACA;wBACAF;sBACA;sBACAwM;sBACAA;oBACA;kBACA;gBACA;gBAAA,iCACA;cAAA;gBAAA,MAEAgC;kBAAA;kBAAA;gBAAA;gBACAA;gBACAK;gBACA/P;kBACAuJ;oBACArI;oBACA;sBACAE;wBACAA;wBACAA;0BAAAoP;0BAAAC;0BAAAtM;wBAAA;0BACA/C;0BACA;4BACAA;0BACA;4BACAA;0BACA;wBACA;sBACA;sBACA;oBACA;sBACAA;oBACA;kBACA;gBACA;gBAAA,iCACA;cAAA;gBAEA;kBACA;kBAEAsP;kBACA1Q;oBACA0Q;oBACAnH,gCACA;oBACAS;sBACA9I;sBACA;oBACA;kBACA;gBAEA;;gBACA;kBACA;kBAEAwP;kBACA1Q;oBACA0Q;oBACAnH,gCACA;oBACAS;sBACA9I;sBACA;oBACA;kBACA;gBAEA;;gBACA;kBACA;kBACAuB;oBACA8G;sBACArI;sBACA;sBACA;sBACA;wBACAyP;sBACA;sBACA;wBACAA;sBACA;sBACA;wBACAlO;0BACA3B;0BACAmO;wBACA;wBACA;sBACA;sBACA;wBACAxM;0BACA3B;0BACAmO;wBACA;wBACA;sBACA;oBACA;kBACA;kBAEAS;kBACAK;kBACAa;kBACAC;kBACAzP;kBACA;oBAEAqB;sBACA8G;wBACArI;sBACA;sBACA8I;wBACA5I;sBACA;oBACA;oBAEAqB;sBACAqO;sBAAA;sBACAC;sBAAA;sBACAxH;wBACAnI;sBACA;sBACA4I;wBACA9I;wBACAE;sBACA;oBACA;kBAEA,iDAmBA;gBAEA;gBACA;kBACA4P;kBACAC;kBACA;oBACAhP;kBACA;oBACAA;kBACA;gBACA;gBACAb;gBACA;kBACA8P;kBAEAlP;kBACAmP;kBACA;oBACAC;oBACAC;oBAEAC;oBAEA;sBACAH;sBAEA;wBACArM;wBACAyM;wBACAL;sBACA;oBACA;oBACAzO;sBACAT;sBACAuH;wBACA;wBACA;wBACAiI;sBACA;oBACA;kBACA;oBACA/O;sBACAT;oBACA;kBACA;gBACA;kBACAS;oBACAT;kBACA;gBACA;kBACAS;oBACAT;kBACA;gBACA;kBACAkB;kBAEA;oBACAT;sBACAT;oBACA;kBACA;oBACAS;sBACAT;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CACA;MAAA;QAAA;MAAA;MAAA;IAAA;IACAyP;MAAA;QAAA/B;QAAAK;MAAA;MACA;MACA;MACA;MACA;QACA;QACA;UACAA;QACA;UACAA;QACA;MACA;MACA,uCAqBA;QAEAtN;UACAoI;UACAjK;UACAuP,+BACA;UACA5G;YACA;cACAnI;gBAAAkP;cAAA;gBACA;kBACA;gBAAA,CACA;kBACAlP;kBACA;oBACAA;kBACA;gBACA;cACA;YACA;UACA;QACA;MAEA;IACA;IACAc;MACA;MACA;MACA;QACA;QACAwP;MACA;MACA;QACAtQ;MACA;QACAqB;UACAuH;YACA5I;UACA;QACA;MACA;IACA;IACA0I;MACA;IACA;IACAlH;MACA;IACA;IACA+O;MACA;MACA;MACA;QACA3P;QACAA,6FACAL,4FACA6K;QACA;UACAxK;QACA;QACA;UACAA;UACA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;MACA;MACAS;QACAT;QACA;QACAsH;QACAsI;QACArI;UACAnI;UAEA;YAAA;YACA;cACA;gBACAA;gBACAqB;cACA;YACA;YACArB;YACA;YACA;YACA;YACAgC;YACA;YACA;YACA;YACA;cACA;gBACAnB;cACA;gBACAA;cACA;YACA;YACA,gFACA;cACA;YACA;YACA;cACAb;gBACA;kBACA;kBACAgC;gBACA;kBACAhC,qDACA;gBACA;kBACAF;kBACAE,oDACAa;gBACA;cACA;gBAAA4P;gBAAAC;cAAA;YACA;cACA1Q;cACA;YACA;UACA;YAAA;YACAA;YACA;YACA;YACAgC;YACA;YACAhC;UACA;YAAA;YACAA;YACA;YACA;YACAgC;YACA;YACA0M;UACA;YAAA;YACA1O;YACA;YACA;YACAgC;YACA;YACAhC;UACA;YAAA;YACAA;YACA;YACA;YACAgC;YACA;YACA;cACA;cACA;cACAhC;gBACA;kBACA;kBACAA;gBACA;cACA;YACA;cACA;cACAA;gBACA;kBACAA;gBACA;cACA;YACA;UACA;YAAA;YACAA;YACA;YACA;YACAgC;YACA;YACAhC;cACAqB;gBAAAsP;cAAA;YACA;UACA;YAAA;YACA3Q;YACA;YACA;YACAgC;YACAhC;cACA;cACAA;YACA;UAEA;YACA;UACA;QACA;QACA4I;UACA;YACA;cACA;gBACA9I;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA8Q;MACA9Q;MACA;MACA;QAEAlB;UACAuJ;YACA;YACAnI;cACA6Q;cACAzF;YACA;cACA;YACA;UACA;QACA;MAEA,gDAkBA,6CAaA,kDAcA,iDAcA,6CAYA;IACA;IACAqF;MACA;MACA;MACA;QACAA;MACA;MACA;QAEA;UACA7R;YACAuJ;cACA;cACAnI;gBACA6Q;gBACAJ;gBACArF;gBACA0F;gBACA/D;cACA;gBACA;cACA;YACA;UACA;QACA;UACAnO;YACAmS;YACA5P;YACAgH;cACArI;cACA;cACAlB;gBACAuJ;kBACA;kBACAnI;oBACA6Q;oBACAG;oBACA5F;oBACA0F;oBACA/D;kBACA;oBACA;kBACA;gBACA;cACA;YACA;YACAnE;cACA9I;cACA,qDAEA;gBACA;kBACAgK;kBACAE;gBACA;cACA;YACA;UACA;QACA;MAEA,gFAsCA,8CAoCA,gDAsBA,6CA2BA,kDA4BA,iDAwBA;IACA;IACAiH;MACA;MACA;MACA;QACAjR;QACA;UACAqB;UACArB;QACA;QACA;UACAA;UACAA;UACAqB;QACA;MACA;MACA;QACArB;QACAyI;QACAzI;QACAA;QACAA;QACAqB;QACArB;QACA;UACAF;UACAE;QACA;QACA,sCA+BA;QACA;UACAA;UACAA;UACAqB;QACA;QACA;UACArB;YACA;YACA;YACA;YACA;cACA;gBACA;cACA;gBACA;cACA;cACAF;cACA;gBACAE;cACA;gBACAA;cACA;YACA;cACAgC;YACA;UACA;QACA;MACA;QACAhC;MACA;MACA;QACA;UAEApB;YACAuJ;cACA;gBACAnI;kBACA6Q;gBACA;cACA;YACA;UACA;QAEA,iDAaA,gDAeA,kDAWA;MACA;IAEA;IACA;IACAK;MACApR;MAEA;MACAE;MACAqB;MACAA;QACAT;MACA;MACAZ;QACAE;QACAgI;UACAiD;UACAhI;UACAtB;QACA;MACA;MACAsP;MACAnR;QACAA;UACAE;QACA;MACA;IAEA;IACAkR;MACA;MACA;MACA;QACAtR;QACAuB;UACA6G;UACAU;YACA9I;YACAA;YACA;cACAE;cACAA;cACAF;cACAE;YACA;UACA;UACAmI;YACArI;YACAE;UACA;QACA;MACA;QACA;MACA;IACA;IACAqR;MAAA;QAAA;UAAA;UAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBACAtR;gBA+GAqB;kBACAkQ;kBACAC;kBACAC;kBACAtJ;oBACA;sBACAuJ;oBACA;oBACA;sBACAA;sBACA1R;sBACAqB;wBACAT,oEACAL,0DACA,iBACAP;wBACA2R;wBACAjO;wBACAyE;0BACAnI;0BACA;4BACA;4BACA;0BACA;4BACA;4BACA;0BACA;0BACA;4BACA4R;4BACAF;4BAEA;8BACA;8BACA;4BACA;0BACA;4BACA1R;0BACA;wBACA;wBACA4I;0BACA5I;0BACAA;wBACA;sBACA;oBACA;kBACA;kBACA4I;kBAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAEA;MAAA;QAAA;MAAA;MAAA;IAAA;IACAiJ;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA7R;gBACA8R;gBAkCAlT;kBACA2S;kBACArR;kBACAiI;oBACA;oBACArI;oBACAE;oBACAqB;sBACAT;sBACA+Q;sBACAjO;sBACAyE;wBACAnI;wBACA;wBACA;0BACA;wBACA;0BACAA;wBACA;sBACA;sBACA4I;wBACA5I;wBACAA;sBACA;oBACA;oBACA;kBACA;kBACA+O;oBACAjP;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAEA;MAAA;QAAA;MAAA;MAAA;IAAA;IACA;IACAiS;MACA;MACAC;MACA;QACAC;QACA;MACA;QACAA;QACA;MACA;QACAA;QACA;MACA;QACAA;QACA;MACA;QACAA;QACA;MACA;QACAA;QACA;MACA;QACAA;QACA;MACA;QACAA;QACA;MACA;QACAA;QACA;MACA;QACAA;QACA;MACA;QACAA;QACA;MACA;QACAA;QACA;MACA;QACAA;QACA;MACA;QACAA;QACA;MACA;QACAA;QACA;MACA;QACAA;QACA;MACA;MACA;IACA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA9Q;gBACA,uCAqBA,kDA4BA;kBAKAC;oBACAnB;oBACAiI;sBACA/G;sBACA;oBACA;oBACAwH;sBACA;sBACA;wBACA;sBACA;wBACA;sBACA;sBACA;sBACA;wBACA;sBAAA,CACA;wBACAuJ;sBACA;wBACAA;sBACA;wBACAA;sBACA;wBACAA;sBACA;sBACA;wBAEA;0BACA9Q;4BACA3B;4BACAiO;4BACAyE;4BACAjK;8BACA;gCACA9G;kCACA8G;oCACArI;kCACA;gCACA;gCACAqS;8BACA;gCACAA;8BACA;4BACA;0BACA;0BACA;wBACA;wBAEA9Q;0BACA3B;0BACAiO;0BACAxF;4BACA;8BACAgK;4BACA;8BACAA;4BACA;0BACA;wBACA;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CACA;MAAA;QAAA;MAAA;MAAA;IAAA;IACAE;MAAA;MACA;MACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MAAA;MAAA;MACA;MACA;MACA;QACAC;QACAA;MACA;QACAA;MACA;MACAvS;IACA;IACAwS;MAAA;MACA;MACAxS;IACA;IACA;IACAyS;MACA;MACA;QACAC;MACA;MACA;MACA;QACA;QACArR;QACAA;MACA;MACA;IACA;IACAsR;MACA;MACA;MACA;QACA;QACA;QACA;QACA;UACAC;UACA;UACA;UACA;UACA;UACA;UACA;YACA;YACA;YACA5S;YACAA;YACA;UACA;YACA;YACA;YACAA;YACA;UACA;QACA;UACA;QACA;MACA;MACA;IACA;IACA6S;MACA;MACA;QACAxR;MACA;QACAA;QACAA;MACA;IACA;IACAjB;MACA;MACA;MACA;MACA;MACA;IACA;IACA0S;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;MACAhT;MACA;MACA;QACAqO;MACA;MACA;QACAvN;MACA;MACAd;MACA;IACA;IACAiT;MACA;QACA;QACA;QACA;QACA;QACAC;UACA;UACA;UACA;YACA;YACAA;cACA;cACAA;YACA;YACAA;cACA;cACA;cACA;cACA;cACA;YAAA,CACA;UACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;MAAA;IAEA;IACAhL;MACA;QACA3G;UACA6G;UACA+G;QACA;MACA;IACA;IACA;IACAgE;MACA;MACA;MACA;QAAA;QACA;QACA;UACAC;QACA;QACAC;UACAA;UACAC;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QAAA;QACA;QACA;QACA;UACAH;UACAC;YACAG;YACAF;UACA;QACA;UACAE;UACAF;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5zEA;AAAA;AAAA;AAAA;AAAooC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAxpC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import Vue from 'vue';\nimport App from './App';\nimport store from './androidPrivacy.js'\n//Vue.prototype.goto = function(e){\n//\t$.goto(e.currentTarget.dataset.url)\n//};\n\nVue.config.productionTip = false;\nVue.mixin({\n\tonShareAppMessage:function(){\n\t\treturn this._sharewx();\n\t},\n\tonShareTimeline:function(){\n\t\tvar sharewxdata = this._sharewx();\n\t\tvar query = (sharewxdata.path).split('?')[1]+'&seetype=circle';\n\t\treturn {\n\t\t\ttitle: sharewxdata.title,\n\t\t\timageUrl: sharewxdata.imageUrl,\n\t\t\tquery: query\n\t\t}\n\t},\n\tonNavigationBarButtonTap(e) {\n\t\tconsole.log(e)\n\t\tvar app = getApp();\n\t\tif(e.type == 'home'){\n\t\t\tvar nowurl = app._url();\n\t\t\tif(nowurl.indexOf('/admin/') === 0){\n\t\t\t\tapp.goto('/admin/index/index','reLaunch');\n\t\t\t}else{\n\t\t\t\tapp.goto(app.globalData.indexurl,'reLaunch');\n\t\t\t}\n\t\t}\n  },\n\tmethods: {\n\t\tgoto:function(e){\n\t\t\tgetApp().goto(e.currentTarget.dataset.url,e.currentTarget.dataset.opentype)\n\t\t},\n\t\tgoback:function(){\n\t\t\tgetApp().goback();\n\t\t},\n\t\tgetmenuindex:function(menuindex){\n\t\t\tthis.menuindex = menuindex\n\t\t},\n\t\tloaded:function(obj){\n\t\t\tif(obj && obj.title && !obj.desc) obj.desc = obj.title\n\t\t\tvar that = this;\n\t\t\tuni.stopPullDownRefresh();\n\t\t\tvar app = getApp();\n\t\t\tif(app.globalData.isinit == false){\n\t\t\t\tapp.get('ApiIndex/linked',{},function(){\n\t\t\t\t\tthat.isload = true;\n\t\t\t\t\tthat._sharemp(obj);\n\t\t\t\t});\n\t\t\t}else{\n\t\t\t\tthis.isload = true;\n\t\t\t\tthis._sharemp(obj);\n\t\t\t}\n\t\t},\n\t\tgetdata:function(){\n\t\t\tvar that = this;\n\t\t\tgetApp().get('ApiIndex/linked',{},function(){\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n\t\tgetplatform:function(){\n\t\t\treturn getApp().globalData.platform;\n\t\t},\n\t\t_sharemp:function(obj){\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\t\t},\n\t\t_sharewx:function(obj){\n\t\t\tif(!obj) obj = {};\n\t\t\tvar app = getApp();\n\t\t\tvar pages = getCurrentPages(); //获取加载的页面\n\t\t\tvar currentPage = pages[pages.length - 1]; //获取当前页面的对象\n\t\t\tvar currenturl = '/' + (currentPage.route ? currentPage.route : currentPage.__route__); //当前页面url \n\t\t\tvar query = ''\n\t\t\t\n\t\t\tvar opt = this.opt;\n\t\t\tif(this.opt && this.opt.id){\n\t\t\t\tquery+='?id='+this.opt.id\n\t\t\t}else if(this.opt && this.opt.cid){\n\t\t\t\tquery+='?cid='+this.opt.cid\n\t\t\t}else if(this.opt && this.opt.gid){\n\t\t\t\tquery+='?gid='+this.opt.gid\n\t\t\t}else if(this.opt && this.opt.bid){\n\t\t\t\tquery+='?bid='+this.opt.bid\n\t\t\t}\n\t\t\tvar currentfullurl = currenturl+query\n\t\t\tvar sharelist = app.globalData.initdata.sharelist;\n\t\t\tif(sharelist){\n\t\t\t\tfor(var i=0;i<sharelist.length;i++){\n\t\t\t\t\tif((sharelist[i]['is_rootpath']==1 && sharelist[i]['indexurl'] == currenturl) || (!sharelist[i]['is_rootpath'] && sharelist[i]['indexurl'] == currentfullurl)){\n\t\t\t\t\t\tobj.title = sharelist[i].title;\n\t\t\t\t\t\tobj.desc = sharelist[i].desc;\n\t\t\t\t\t\tobj.pic = sharelist[i].pic;\n\t\t\t\t\t\tobj.link = sharelist[i].url;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t//拼接推荐人参数\n\t\t\tvar scene = [];\n\t\t\tif(obj.link){\n\t\t\t\tvar sharepath = obj.link;\n\t\t\t\tlet scenes = '';\n\t\t\t\tif(obj.link.indexOf('pid') == -1){\n\t\t\t\t\tif(app.globalData.mid){\n\t\t\t\t\t\tscene.push('pid_'+app.globalData.mid);\n\t\t\t\t\t\tscenes = scene.join('-');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(sharepath && sharepath.indexOf('#') > 0){\n\t\t\t\t\tsharepath = sharepath.split('#')[1];\n\t\t\t\t}\n\t\t\t\tif(scenes && (obj.link.indexOf('scene') == -1)){\n\t\t\t\t\tif(obj.link && obj.link.indexOf('?') > 0){\n\t\t\t\t\t\tsharepath = sharepath + \"&scene=\"+scenes + '&t='+parseInt((new Date().getTime())/1000);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tsharepath = sharepath + \"?scene=\"+scenes + '&t='+parseInt((new Date().getTime())/1000);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}else{\n\t\t\t\t//如果有指定页面 则跳指定,没有则为本页面\n\t\t\t\tif(obj.tolink){\n\t\t\t\t\tlet scenes = '';\n\t\t\t\t\tif(obj.tolink.indexOf('pid') == -1){\n\t\t\t\t\t\tif(app.globalData.mid){\n\t\t\t\t\t\t\tscene.push('pid_'+app.globalData.mid);\n\t\t\t\t\t\t\tscenes = scene.join('-');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif(obj.tolink.indexOf('#') > 0){\n\t\t\t\t\t\tsharepath = obj.tolink.split('#')[1];\n\t\t\t\t\t}else{\n\t\t\t\t\t\tsharepath = obj.tolink\n\t\t\t\t\t}\n\t\t\t\t\tif(scenes && (obj.tolink.indexOf('scene') == -1)){\n\t\t\t\t\t\tif(obj.tolink.indexOf('?') > 0){\n\t\t\t\t\t\t\tsharepath = sharepath + \"&scene=\"+scenes + '&t='+parseInt((new Date().getTime())/1000);\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tsharepath = sharepath + \"?scene=\"+scenes + '&t='+parseInt((new Date().getTime())/1000);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}else{\n\t\t\t\t\tvar sharepath = this.sharepath();\n\t\t\t\t}\n\t\t\t}\n\t\t\tconsole.log('sharepath',sharepath);\n\t\t\tif(obj.title){\n\t\t\t\tvar title = obj.title\n\t\t\t}else{\n\t\t\t\tvar title = app.globalData.initdata.name;\n\t\t\t}\n\t\t\tif(obj.pic){\n\t\t\t\tvar imgUrl = obj.pic\n\t\t\t}else{\n\t\t\t\tvar imgUrl = '';\n\t\t\t}\n\t\t\ttypeof obj.callback == 'function' && obj.callback();\n\t\t\treturn {\n\t\t\t\ttitle: title,\n\t\t\t\tpath: sharepath, \n\t\t\t\timageUrl: imgUrl\n\t\t\t}\n\t\t},\n\t\tsharepath:function(){\n\t\t\tvar app = getApp();\n\t\t\tvar opt = this.opt;\n\n\n\n\n\n\n\t\t\tvar currentpath = '/' + (this.route ? this.route : this.__route__); //当前页面url\n\n\t\t\tvar scene = [];\n\t\t\tfor(var i in opt){\n\t\t\t\tif(i != 'pid' && i != 'scene'){\n\t\t\t\t\tscene.push(i+'_'+opt[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t\tconsole.log(app.globalData.mid);\n\t\t\tif(app.globalData.mid){\n\t\t\t\tscene.push('pid_'+app.globalData.mid);\n\t\t\t}\n\t\t\tvar scenes = scene.join('-');\n\t\t\tif(scenes){\n\t\t\t\tcurrentpath = currentpath + \"?scene=\"+scenes + '&t='+parseInt((new Date().getTime())/1000);\n\t\t\t}\n\t\t\treturn currentpath;\n\t\t},\n\t\tt:function(text){\n\t\t\tif(text=='color1'){\n\t\t\t\treturn getApp().globalData.initdata.color1;\n\t\t\t}else if(text=='color2'){\n\t\t\t\treturn getApp().globalData.initdata.color2;\n\t\t\t}else if(text=='color1rgb'){\n\t\t\t\tvar color1rgb = getApp().globalData.initdata.color1rgb;\n\t\t\t\treturn color1rgb['red']+','+color1rgb['green']+','+color1rgb['blue'];\n\t\t\t}else if(text=='color2rgb'){\n\t\t\t\tvar color2rgb = getApp().globalData.initdata.color2rgb;\n\t\t\t\treturn color2rgb['red']+','+color2rgb['green']+','+color2rgb['blue'];\n\t\t\t}else{\n\t\t\t\treturn getApp().globalData.initdata.textset ? (getApp().globalData.initdata.textset[text] ? getApp().globalData.initdata.textset[text] : text) : text;\n\t\t\t}\n\t\t},\n\t\tinArray: function (search, array) {\n\t\t\tfor (var i in array) {\n\t\t\t\tif (array[i] == search) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn false;\n\t\t},\n\t\tisNull:function(param){\n\t\t\tif(this.isObject(param)){\n\t\t\t\treturn this.isEmptyObject(param);\n\t\t\t}\n\t\t\treturn (param == undefined || param == \"undefined\" || param == null || param == \"\");\n\t\t},\n\t\tisEmpty:function(list){\n\t\t\tif (!list || list.length === 0) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tif(this.isObject(list)){\n\t\t\t\treturn this.isEmptyObject(list);\n\t\t\t}\n\t\t\treturn (!list || list.length === 0 || (list.length === 1 && (!list[(0)] || list[(0)].length === 0)))\n\t\t},\n\t\tisEmptyObject:function(obj) {\n\t\t  return JSON.stringify(obj) === '{}';\n\t\t},\n\t\tisObject:function(obj) {\n\t\t  return typeof obj === 'object' && obj !== null;\n\t\t},\n\t\tdateFormat:function(time,format){\n\t\t\tif(format == undefined || format == \"undefined\" || format == null || format == \"\") format = 'Y-m-d H:i:s';\n\t\t\tvar date = new Date();\n\t\t\tif(time != '' || time > 0) {\n\t\t\t\tdate = new Date(time * 1000);\n\t\t\t}\n\t\t\t\n\t\t\tvar Y = date.getFullYear();\n\t\t\tvar m = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) ;\n\t\t\tvar d = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();\n\t\t\tvar H = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();\n\t\t\tvar i = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();\n\t\t\tvar s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();\n\t\t\tformat = format.replace('Y',Y);\n\t\t\tformat = format.replace('m',m);\n\t\t\tformat = format.replace('d',d);\n\t\t\tformat = format.replace('H',H);\n\t\t\tformat = format.replace('i',i);\n\t\t\tformat = format.replace('s',s);\n\t\t\treturn format;\n\t\t},\n\t\tgetDistance: function (lat1, lng1, lat2, lng2) {\n\t\t\tif(!lat1 || !lng1 || !lat2 || !lng2) return '';\n\t\t\tvar rad1 = lat1 * Math.PI / 180.0;\n\t\t\tvar rad2 = lat2 * Math.PI / 180.0;\n\t\t\tvar a = rad1 - rad2;\n\t\t\tvar b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;\n\t\t\tvar r = 6378137;\n\t\t\tvar juli = r * 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(rad2) * Math.pow(Math.sin(b / 2), 2)));\n\t\t\tjuli = juli/1000\n\t\t\tjuli = juli.toFixed(2);\n\t\t\treturn juli;\n\t\t},\n\t\tshowMap:function(e){\n\t\t\tlet latitude = parseFloat(e.currentTarget.dataset.latitude);\n\t\t\tlet longitude = parseFloat(e.currentTarget.dataset.longitude);\n\t\t\tlet scale = e.currentTarget.dataset.scale?parseInt(e.currentTarget.dataset.scale):13;\n\t\t\tlet name = e.currentTarget.dataset.name;\n\t\t\tlet address = e.currentTarget.dataset.address;\n\t\t\tuni.openLocation({\n\t\t\t latitude:latitude,\n\t\t\t longitude:longitude,\n\t\t\t name:name,\n\t\t\t address:address,\n\t\t\t scale: 13\n\t\t\t})\n\t\t},\n\t\tpreviewImage: function (e) {\n\t\t\tvar imgurl = e.currentTarget.dataset.url\n\t\t\tvar imgurls = e.currentTarget.dataset.urls\n\t\t\tif (!imgurls) imgurls = imgurl;\n\t\t\tif(!imgurls) return;\n\t\t\tif (typeof (imgurls) == 'string') imgurls = imgurls.split(',');\n\t\t\tuni.previewImage({\n\t\t\t\tcurrent: imgurl, \n\t\t\t\turls: imgurls \n\t\t\t})\n\t\t},\n\t\tcopy: function(e) {\n\t\t\tuni.setClipboardData({\n\t\t\t\tdata: e.currentTarget.dataset.text,\n\t\t\t\tsuccess: function () {\n\t\t\t\t\tgetApp().error('复制成功')\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tsubscribeMessage:function(callback){\n\t\t\tvar app = getApp();\n\n\t\t\tvar that = this;\n\t\t\tvar tmplids = that.tmplids;\n\t\t\tif(tmplids && tmplids.length > 0){\n\t\t\t\tuni.requestSubscribeMessage({\n\t\t\t\t\ttmplIds: tmplids,\n\t\t\t\t\tsuccess:function(res) {\n\t\t\t\t\t\tfor(var i in tmplids){\n\t\t\t\t\t\t\tif(res[tmplids[i]] == 'accept'){\n\t\t\t\t\t\t\t\tapp.post('ApiIndex/subscribemessage',{tmplid:tmplids[i]},function(){})\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\ttypeof callback == \"function\" && callback();\n\t\t\t\t\t},\n\t\t\t\t\tfail:function(res){\n\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\ttypeof callback == \"function\" && callback();\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}else{\n\t\t\t\ttypeof callback == \"function\" && callback();\n\t\t\t}\n\n\n\n\n\t\t},\n\t\tshoukuan:function(id,type='',redirect_url=''){\n\t\t\t//id=>数据表id type=>数据表名 redirect_url=>处理完成后要跳转的链接\n\t\t\tconsole.log('进入main.js方法');\n\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\tvar that = this;\n\t\t\t\tif(app.globalData.platform != 'wx' && app.globalData.platform != 'mp'){\n\t\t\t\t\tapp.error('请在微信环境下操作');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthat.loading = true;\n\t\t\t\tapp.post('ApiMy/getwithdrawinfo', {id:id,type:type}, function (res) {\n\t\t\t\t\tthat.loading = false;\n\t\t\t\t\tvar detail = res.detail;\n\t\t\t\t\tvar appinfo = res.appinfo;\n\t\t\t\t\tif(detail.platform!=app.globalData.platform){\n\t\t\t\t\t\tapp.error('请在提现发起端操作收款');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tif(app.globalData.platform == 'wx' ){\n\t\t\t\t\t\tif (wx.canIUse('requestMerchantTransfer')) {\n\t\t\t\t\t\t  wx.requestMerchantTransfer({\n\t\t\t\t\t\t\tmchId: appinfo.wxpay_mchid,\n\t\t\t\t\t\t\tappId: wx.getAccountInfoSync().miniProgram.appId,\n\t\t\t\t\t\t\tpackage: detail.wx_package_info,\n\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t  // res.err_msg将在页面展示成功后返回应用时返回ok，并不代表付款成功\n\t\t\t\t\t\t\t  console.log('success:', res);\n\t\t\t\t\t\t\t  that.loading = true;\n\t\t\t\t\t\t\t  app.post('ApiMy/check_withdraw_result', {id:id,type:type}, function (res) {\n\t\t\t\t\t\t\t  \tthat.loading = false;\n\t\t\t\t\t\t\t  \tif(res.status==1){\n\t\t\t\t\t\t\t  \t\tif(redirect_url){\n\t\t\t\t\t\t\t  \t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\t  \t\t\t\tapp.goto(redirect_url);\n\t\t\t\t\t\t\t  \t\t\t}, 1000); \n\t\t\t\t\t\t\t  \t\t}\n\t\t\t\t\t\t\t  \t\tapp.success(res.msg);\n\t\t\t\t\t\t\t\t\tresolve(true);\n\t\t\t\t\t\t\t  \t}else{\n\t\t\t\t\t\t\t  \t\tapp.error(res.msg);\n\t\t\t\t\t\t\t  \t\tif(redirect_url){\n\t\t\t\t\t\t\t  \t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\t  \t\t\t\tapp.goto(redirect_url);\n\t\t\t\t\t\t\t  \t\t\t}, 1000); \n\t\t\t\t\t\t\t  \t\t}\n\t\t\t\t\t\t\t\t\tresolve(false);\n\t\t\t\t\t\t\t  \t}\n\t\t\t\t\t\t\t  })\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: (res) => {\n\t\t\t\t\t\t\t  console.log('fail:', res);\n\t\t\t\t\t\t\t  if(redirect_url){\n\t\t\t\t\t\t\t\t setTimeout(function () {\n\t\t\t\t\t\t\t\t   app.goto(redirect_url);\n\t\t\t\t\t\t\t\t }, 1000); \n\t\t\t\t\t\t\t  }\n\t\t\t\t\t\t\t  resolve(false);\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t  });\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tapp.error('你的微信版本过低，请更新至最新版本。');\n\t\t\t\t\t\t\tif(redirect_url){\n\t\t\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\t\t\tapp.goto(redirect_url);\n\t\t\t\t\t\t\t\t}, 1000); \n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tresolve(false);\n\t\t\t\t\t\t}\n\t\t\t\t\t}else if(app.globalData.platform == 'mp'){\n\t\t\t\t\t\tvar jweixin = require('jweixin-module');\n\t\t\t\t\t\tconsole.log(jweixin);\n\t\t\t\t\t\tjweixin.ready(function () {\n\t\t\t\t\t\t  jweixin.checkJsApi({\n\t\t\t\t\t\t\tjsApiList: ['requestMerchantTransfer'],\n\t\t\t\t\t\t\tsuccess: function (res) {\n\t\t\t\t\t\t\t  if (res.checkResult['requestMerchantTransfer']) {\n\t\t\t\t\t\t\t\tWeixinJSBridge.invoke('requestMerchantTransfer', {\n\t\t\t\t\t\t\t\t\tmchId: appinfo.wxpay_mchid,\n\t\t\t\t\t\t\t\t\tappId: appinfo.appid,\n\t\t\t\t\t\t\t\t\tpackage: detail.wx_package_info,\n\t\t\t\t\t\t\t\t  },\n\t\t\t\t\t\t\t\t  function (res) {\n\t\t\t\t\t\t\t\t\tif (res.err_msg === 'requestMerchantTransfer:ok') {\n\t\t\t\t\t\t\t\t\t  // res.err_msg将在页面展示成功后返回应用时返回success，并不代表付款成功\n\t\t\t\t\t\t\t\t\t  that.loading = true;\n\t\t\t\t\t\t\t\t\t  app.post('ApiMy/check_withdraw_result', {id:id,type:type}, function (res) {\n\t\t\t\t\t\t\t\t\t  \tthat.loading = false;\n\t\t\t\t\t\t\t\t\t  \tif(res.status==1){\n\t\t\t\t\t\t\t\t\t  \t\tif(redirect_url){\n\t\t\t\t\t\t\t\t\t  \t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\t\t\t  \t\t\t\tapp.goto(redirect_url);\n\t\t\t\t\t\t\t\t\t  \t\t\t}, 1000); \n\t\t\t\t\t\t\t\t\t  \t\t}\n\t\t\t\t\t\t\t\t\t  \t\tapp.success(res.msg);\n\t\t\t\t\t\t\t\t\t\t\tresolve(true);\n\t\t\t\t\t\t\t\t\t  \t}else{\n\t\t\t\t\t\t\t\t\t  \t\tapp.error(res.msg);\n\t\t\t\t\t\t\t\t\t  \t\tif(redirect_url){\n\t\t\t\t\t\t\t\t\t  \t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\t\t\t  \t\t\t\tapp.goto(redirect_url);\n\t\t\t\t\t\t\t\t\t  \t\t\t}, 1000); \n\t\t\t\t\t\t\t\t\t  \t\t}\n\t\t\t\t\t\t\t\t\t\t\tresolve(false);\n\t\t\t\t\t\t\t\t\t  \t}\n\t\t\t\t\t\t\t\t\t  })\n\t\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t\tif(redirect_url){\n\t\t\t\t\t\t\t\t\t\t\tapp.goto(redirect_url);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t  }\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t  } else {\n\t\t\t\t\t\t\t\talert('你的微信版本过低，请更新至最新版本。');\n\t\t\t\t\t\t\t\tif(redirect_url){\n\t\t\t\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\t\t\t\tapp.goto(redirect_url);\n\t\t\t\t\t\t\t\t\t}, 1000); \n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tresolve(false);\n\t\t\t\t\t\t\t  }\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t  });\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t});\n\t\t}\n\t}\n});\n\nApp.mpType = 'app';\n\nconst app = new Vue({\n    ...App,store\n});\napp.$mount();", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\n\texport default {\n\t\tglobalData: {\n\t\t\tpre_url: '',\n\t\t\tbaseurl: '',\n\t\t\tsession_id: '',\n\t\t\t\n\t\t\taid: 0,\n\t\t\tmid: 0,\n\t\t\tpid: 0,\n\t\t\tneedAuth: 0,\n\t\t\tplatform: 'wx',\n\t\t\tplatform2: '',\n\t\t\tisdouyin: 0,\n\t\t\tsysset: [],\n\t\t\tindexurl: '/pages/index/index',\n\t\t\tmenudata: [],\n\t\t\tmenu2data: [],\n\t\t\tcurrentIndex: -1,\n\t\t\tinitdata: {},\n\t\t\ttextset: [],\n\t\t\tisinit: false,\n\t\t\tsocketOpen: false,\n\t\t\tsocket_token: '',\n\t\t\tsocketMsgQueue: [],\n\t\t\tsocketConnecttimes: 0,\n\t\t\tsocketInterval: null,\n\t\t\tscene: 0,\n\t\t\thomeNavigationCustom: 0,\n\t\t\tusercenterNavigationCustom: 0,\n\t\t\tbusinessindexNavigationCustom: 0,\n\t\t\tnavigationBarBackgroundColor: '#ffffff',\n\t\t\tnavigationBarTextStyle: 'black',\n\t\t\trewardedVideoAd:{},\n\t\t\tseetype:'',\n\t\t\ttrid: 0,\n\t\t\thide_home_button:0,\n\t\t\tindextipstatus:false,\n\t\t\tqrcode:'',\n\t\t\tpriceRate:0,//价格倍率\n\t\t\tmaidan_bid:0,//买单页面商户id\n\t\t\twcode:'',//活码参数\n\t\t\tregbid:0,//商家推荐注册\n\t\t\tothermid:0,//本系统其他平台用户ID\n\t\t\twxregyqcode:0,//微信注册邀请码\n\t \n\t\t},\n\t\tonLaunch: function(options) {\n\t\t\tvar extConfig = uni.getExtConfigSync ? uni.getExtConfigSync() : {};\n\t\t\tif (extConfig && extConfig.aid) {\n\t\t\t\tthis.globalData.aid = extConfig.aid;\n\t\t\t\tthis.globalData.pre_url = extConfig.baseurl;\n\t\t\t\tthis.globalData.homeNavigationCustom = extConfig.homeNavigationCustom || 0;\n\t\t\t\tthis.globalData.usercenterNavigationCustom = extConfig.usercenterNavigationCustom || 0;\n\t\t\t\tthis.globalData.businessindexNavigationCustom = extConfig.businessindexNavigationCustom || 0;\n\t\t\t\tthis.globalData.navigationBarBackgroundColor = extConfig.navigationBarBackgroundColor || '#ffffff';\n\t\t\t\tthis.globalData.navigationBarTextStyle = extConfig.navigationBarTextStyle || 'black';\n\t\t\t\tthis.globalData.hide_home_button = extConfig.hideHomeButton || 0;\n\t\t\t} else {\n\t\t\t\tvar siteInfo = require(\"./siteinfo.js\");\n\t\t\t\tthis.globalData.aid = siteInfo.uniacid;\n\t\t\t\t// #ifdef H5\n\t\t\t\tvar url = (window.location.href).split('#')[0];\n\t\t\t\tvar url = url.split('?')[0];\n\t\t\t\tvar urlArr = url.split('/');\n\t\t\t\tvar htmlname = urlArr[urlArr.length - 1];\n\t\t\t\tvar uniacid = htmlname.replace('.html', '');\n\t\t\t\tvar reg = /^[0-9]+$/;\n\t\t\t\tif (uniacid && reg.test(uniacid)) {\n\t\t\t\t\tthis.globalData.aid = uniacid;\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t\tthis.globalData.pre_url = siteInfo.siteroot;\n\t\t\t\tthis.globalData.homeNavigationCustom = siteInfo.homeNavigationCustom || 0;\n\t\t\t\tthis.globalData.usercenterNavigationCustom = siteInfo.usercenterNavigationCustom || 0;\n\t\t\t\tthis.globalData.businessindexNavigationCustom = siteInfo.businessindexNavigationCustom || 0;\n\t\t\t\tthis.globalData.navigationBarBackgroundColor = siteInfo.navigationBarBackgroundColor || '#ffffff';\n\t\t\t\tthis.globalData.navigationBarTextStyle = siteInfo.navigationBarTextStyle || 'black';\n\t\t\t\tthis.globalData.hide_home_button = siteInfo.hideHomeButton || 0;\n\t\t\t}\n\t\t\tthis.globalData.baseurl = this.globalData.pre_url + '/?s=/';\n\t\t\tthis.globalData.session_id = uni.getStorageSync('session_id');\n\t\t\tvar opts = this.getopts(options.query);\n\t\t\tif (opts && opts.pid) {\n\t\t\t\tthis.globalData.pid = opts.pid;\n\t\t\t\tuni.setStorageSync('pid', this.globalData.pid);\n\t\t\t} else {\n\t\t\t\t//var pid = uni.getStorageSync('pid');\n\t\t\t\t//if (pid) this.globalData.pid = pid;\n\t\t\t}\n\t\t\tif (opts && opts.uid) {\n\t\t\t\tthis.globalData.uid = opts.uid;\n\t\t\t}\n\t\t\tif (opts && opts.priceRate) {\n\t\t\t\tthis.globalData.priceRate = opts.priceRate;\n\t\t\t}\n      if (opts && opts.trid) {\n      \tthis.globalData.trid = opts.trid;\n      }\n      if(options && options.query && options.query.seetype){\n          this.globalData.seetype = options.query.seetype;\n      }else{\n          if (opts && opts.seetype) {\n            this.globalData.seetype = opts.seetype;\n          }\n      }\n      if (opts && opts.regbid) {\n      \tthis.globalData.regbid = opts.regbid;\n      }\n\t\t\tif (opts && opts.wxregyqcode) {\n\t\t\t\tthis.globalData.wxregyqcode = opts.wxregyqcode;\n\t\t\t}\n      if (opts && opts.othermid) {\n      \tthis.globalData.othermid = opts.othermid;\n      \tuni.setStorageSync('othermid', this.globalData.othermid);\n      }\n\t\t\t// #ifdef APP-PLUS\n\t\t\tthis.globalData.platform = 'app';\n\t\t\tvar app = this;\n\t\t\tuni.getSystemInfo({\n\t\t\t\tsuccess: function(res) {\n\t\t\t\t\tapp.globalData.platform2 = res.platform;\n\t\t\t\t}\n\t\t\t})\n\t\t\t// #endif\n\t\t\t// #ifdef H5\n\t\t\tthis.globalData.platform = 'h5';\n\t\t\tif (navigator.userAgent.indexOf('MicroMessenger') > -1) {\n\t\t\t\tthis.globalData.platform = 'mp';\n\t\t\t}\n\t\t\t// #endif\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tthis.globalData.platform = 'wx';\n\t\t\tthis.checkUpdateVersion();\n\t\t\t// #endif\n\t\t\t// #ifdef MP-ALIPAY\n\t\t\tthis.globalData.platform = 'alipay';\n\t\t\t// #endif\n\t\t\t// #ifdef MP-BAIDU\n\t\t\tthis.globalData.platform = 'baidu';\n\t\t\t// #endif\n\t\t\t// #ifdef MP-TOUTIAO\n\t\t\tthis.globalData.platform = 'toutiao';\n\t\t\tvar sysinfo = tt.getSystemInfoSync();\n\t\t\tif (sysinfo.appName == 'Douyin') {\n\t\t\t\tthis.globalData.isdouyin = 1;\n\t\t\t}\n\t\t\t// #endif\n\t\t\t// #ifdef MP-QQ\n\t\t\tthis.globalData.platform = 'qq';\n\t\t\t// #endif\n\t\t\tconsole.log(this.globalData.platform);\n\n\t\t\t//#ifndef MP-TOUTIAO\n\t\t\tvar app = this;\n\t\t\tuni.onSocketOpen(function(res) {\n\t\t\t\tapp.globalData.socketOpen = true;\n\t\t\t\tfor (var i = 0; i < app.globalData.socketMsgQueue.length; i++) {\n\t\t\t\t\tif(i != 0) return;\n\t\t\t\t\tapp.sendSocketMessage(app.globalData.socketMsgQueue[i]);\n\t\t\t\t}\n\t\t\t\tapp.globalData.socketMsgQueue = [];\n\t\t\t});\n\t\t\tuni.onSocketMessage(function(res) {\n\t\t\t\tconsole.log('收到服务器内容：' + res.data);\n\t\t\t\ttry {\n\t\t\t\t\tvar data = JSON.parse(res.data);\n\t\t\t\t\tvar needpopup = true;\n\t\t\t\t\tvar pages = getCurrentPages();\n\t\t\t\t\tvar currentPage = pages[pages.length - 1];\n\t\t\t\t\tif (!currentPage) return;\n\t\t\t\t\tif (currentPage && currentPage.$vm.hasOwnProperty('receiveMessage')) {\n\t\t\t\t\t\tvar rs = currentPage.$vm.receiveMessage(data);\n\t\t\t\t\t\tneedpopup = !rs;\n\t\t\t\t\t}\n\t\t\t\t\tif (needpopup && (data.type == 'tokefu' || data.type == 'tokehu' || data.type ==\n\t\t\t\t\t\t\t'peisong' || data.type == 'notice')) { //需要弹窗提示\n\t\t\t\t\t\tcurrentPage.$vm.$refs.popmsg.open(data);\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {}\n\t\t\t});\n\t\t\t//#endif\n\t\t},\n\t\tonShow: function(options) {\n\t\t\tvar that = this;\n\t\t\tconsole.log('onShow');\n\t\t\tconsole.log(options);\n\t\t\tconsole.log('---------xxxx');\n\t\t\tvar opts = this.getopts(options.query);\n\t\t\tif (opts && opts.pid) {\n\t\t\t\tthis.globalData.pid = opts.pid;\n\t\t\t\tuni.setStorageSync('pid', this.globalData.pid);\n\t\t\t}\n\t\t\tif (options && options.scene) {\n\t\t\t\tthis.globalData.scene = options.scene;\n\t\t\t\tif(options.scene == 1154){\n\t\t\t\t\t\tthis.globalData.seetype = 'circle';//查看类型为朋友圈\n\t\t\t\t}\n\t\t\t}\n\t\t\t//解析扫描普通二维码进入小程序的bid参数 start\n\t\t\tvar qrcode_url = '';\n\t\t\t// #ifdef MP-ALIPAY\n\t\t\tif (options && options.query && options.query.qrCode) {\n\t\t\t    console.log('支付宝扫码进入')\n\t\t\t    qrcode_url = decodeURIComponent(options.query.qrCode);   \n\t\t\t}\n\t\t\t// #endif\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tif (options && options.query && options.query.q) {\n\t\t\t    console.log('微信扫码进入')\n\t\t\t    qrcode_url = decodeURIComponent(options.query.q);   \n\t\t\t}\n\t\t\t// #endif\n\t\t\tvar maidan_bid = 0;\n\t\t\tvar wcode = '';\n\t\t\t//二维码链接中解析bid参数\n\t\t\tif(qrcode_url && qrcode_url.indexOf(\"?\")>0){\n\t\t\t\tconsole.log(qrcode_url)\n\t\t\t\tvar paraString = qrcode_url.substring(qrcode_url.indexOf(\"?\")+1,qrcode_url.length).split(\"&\");\n\t\t\t\tconsole.log(paraString);\n\t\t\t\tvar paraObj = {}  \n\t\t\t\tfor (var i=0; i<paraString.length; i++){\n\t\t\t\t\tvar j = paraString[i];\n\t\t\t\t\tvar key = j.substring(0,j.indexOf(\"=\")).toLowerCase();\n\t\t\t\t\tif(key=='bid'){\n\t\t\t\t\t\tmaidan_bid = j.substring(j.indexOf(\"=\")+1,j.length)\n\t\t\t\t\t}\n\t\t\t\t\tif(key == 'a' && options.path =='pagesA/w'){\n\t\t\t\t\t\twcode = j.substring(j.indexOf(\"=\")+1,j.length)\n\t\t\t\t\t}\n\t\t\t\t}  \n\t\t\t}\n\t\t\tif(wcode){\n\t\t\t\tthis.globalData.wcode = wcode;\n\t\t\t}\n\t\t\tif(maidan_bid){\n\t\t\t\tthis.globalData.maidan_bid = maidan_bid;\n\t\t\t}\n\t\t\t//解析扫描普通二维码进入小程序的bid参数 end\n\t\t\t\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\t// 分享卡片/订阅消息/扫码二维码/广告/朋友圈等场景才能调用getShareParams接口获取以下参数\n\t\t\tconst sceneList = [1007, 1008, 1014, 1044, 1045, 1046, 1047, 1048, 1049, 1073, 1154, 1155];\n\t\t\tif (sceneList.includes(this.globalData.scene)) {\n\t\t\t\tconsole.log('---------00')\n\t\t\t\ttry {\n\t\t\t\t\tlet livePlayer = requirePlugin('live-player-plugin');\n\t\t\t\t\tconsole.log(livePlayer)\n\t\t\t\t\tconsole.log('---------11');\n\t\t\t\t\tlivePlayer.getShareParams().then(res => {\n\t\t\t\t\t\tlet custom_params = res.custom_params;\n\t\t\t\t\t\tconsole.log(custom_params)\n\t\t\t\t\t\tif (custom_params && custom_params.pid) {\n\t\t\t\t\t\t\tthat.globalData.pid = custom_params.pid;\n\t\t\t\t\t\t}\n\t\t\t\t\t}).catch(err => {\n\t\t\t\t\t\tconsole.log('get share params err ', err);\n\t\t\t\t\t});\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.log('live-player-plugin err ', e);\n\t\t\t\t}\n\t\t\t}\n\t\t\t// #endif\n\n\t\t\t//#ifndef MP-TOUTIAO\n\t\t\tif (that.globalData.mid && that.globalData.socket_token) {\n\t\t\t\tvar app = this;\n\t\t\t\tapp.openSocket();\n\t\t\t\t/*\n\t\t\t\tapp.sendSocketMessage({type: 'khinit',data: {aid: app.globalData.aid,mid: app.globalData.mid,platform:app.globalData.platform} });\n\t\t\t\tclearInterval(app.globalData.socketInterval);\n\t\t\t\tapp.globalData.socketInterval = setInterval(function () {\n\t\t\t\t\tapp.sendSocketMessage({type: 'connect'});\n\t\t\t\t}, 25000);\n\t\t\t\t*/\n\t\t\t}\n\t\t\t//#endif\n\t\t},\n\t\tmethods: {\n\t\t\tisPhone: function(param, type=1) {\n\t\t\t\tvar regs = {\n\t\t\t\t\t1:/^(86|(\\+86))?1[3-9]\\d{9}$/, //手机号\n\t\t\t\t\t2:/^(\\d{3,4}-)?\\d{7,8}$/, //座机\n\t\t\t\t\t3:/^400[16789]?-?\\d{3}-?\\d{4}$/ //400电话\n\t\t\t\t};\n\t\t\t\tvar regExp = regs[type];\n\t\t\t\treturn regExp.test(param);\n\t\t\t},\n\t\t\tisIdCard: function(param) {\n\t\t\t\tvar regExp =\n\t\t\t\t\t/^(^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$)|(^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])((\\d{4})|\\d{3}[Xx])$)$/;\n\t\t\t\treturn regExp.test(param);\n\t\t\t},\n\t\t\tgetopts: function(opt) {\n        if(opt && opt.frompage){\n          var frompagepos = opt.frompage.indexOf('amp;');\n          if ( frompagepos> -1) {\n            opt.frompage = opt.frompage.replace(\"amp;\", \"\");\n          }\n        }\n\t\t\t\tif (opt && opt.scene) {\n\t\t\t\t\tvar scene = opt.scene\n\t\t\t\t\tvar scenes = scene.split('-');\n\t\t\t\t\t//var opts = {};\n\t\t\t\t\tfor (var i in scenes) {\n\t\t\t\t\t\tvar thisscenes = scenes[i].split('_');\n\t\t\t\t\t\topt[thisscenes[0]] = thisscenes[1];\n\t\t\t\t\t}\n\t\t\t\t\treturn opt;\n\t\t\t\t} else {\n\t\t\t\t\treturn opt\n\t\t\t\t}\n\t\t\t},\n\t\t\talert: function(content, confirmfun) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示信息',\n\t\t\t\t\tshowCancel: false,\n\t\t\t\t\tcontent: content.toString(),\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\ttypeof confirmfun == 'function' && confirmfun();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tconfirm: function(content, confirmfun, cancelfun) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '操作确认',\n\t\t\t\t\tcontent: content.toString(),\n\t\t\t\t\tshowCancel: true,\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\ttypeof confirmfun == 'function' && confirmfun();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\ttypeof cancelfun == 'function' && cancelfun();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tsuccess: function(title, successfun) {\n\t\t\t\tif (undefined == title) title = '操作成功';\n\t\t\t\tvar title = title.toString();\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: title,\n\t\t\t\t\ticon: (title.length > 8 ? 'none' : 'success'),\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\ttypeof successfun == 'function' && successfun();\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\terror: function(title, duration) {\n\t\t\t\tif (title === false) {\n\t\t\t\t\tuni.hideToast();\n\t\t\t\t} else {\n\t\t\t\t\tif (this.isNull(duration)) duration = 2500;\n\t\t\t\t\tif (undefined == title) title = '操作失败';\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: title.toString(),\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: duration\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tshowLoading: function(title) {\n\t\t\t\tif (title === false) {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t} else {\n\t\t\t\t\tif (undefined == title) title = '加载中';\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: title.toString(),\n\t\t\t\t\t\tmask: true\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tinArray: function(search, array) {\n\t\t\t\tfor (var i in array) {\n\t\t\t\t\tif (array[i] == search) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tisNull: function(e) {\n\t\t\t\treturn e == undefined || e == \"undefined\" || e == null || e == \"\";\n\t\t\t},\n\t\t\tparseJSON: function(e) {\n\t\t\t\ttry {\n\t\t\t\t\treturn JSON.parse(e);\n\t\t\t\t} catch (t) {\n\t\t\t\t\treturn undefined;\n\t\t\t\t}\n\t\t\t},\n\t\t\tgetparams: function(url) {\n\t\t\t\tif (url.indexOf('?') === -1) return {};\n\t\t\t\tvar query = url.split('?')[1];\n\t\t\t\tvar vars = query.split(\"&\");\n\t\t\t\tvar params = {};\n\t\t\t\tfor (var i = 0; i < vars.length; i++) {\n\t\t\t\t\tvar pair = vars[i].split(\"=\");\n\t\t\t\t\tparams[pair[0]] = pair[1];\n\t\t\t\t}\n\t\t\t\treturn params;\n\t\t\t},\n\t\t\tgoto: async function(tourl, opentype) {\n\t\t\t\tvar app = this;\n\t\t\t\tvar params = app.getparams(tourl);\n\t\t\t\tconsole.log(params);\n\t\t\t\tif (params && params.reloadthispage == 1) {\n\t\t\t\t\tvar thispage = getCurrentPages().pop();\n\t\t\t\t\tthispage.$vm.opt = params;\n\t\t\t\t\tthispage.$vm.getdata();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (app.isNull(opentype) && tourl) {\n\t\t\t\t\t//var currentIndex = -1;\n\t\t\t\t\tvar tablist = app.globalData.menudata['list'];\n\t\t\t\t\tif (tablist) {\n\t\t\t\t\t\tfor (var i = 0; i < tablist.length; i++) {\n\t\t\t\t\t\t\tif (tablist[i]['pagePath'] == tourl) {\n\t\t\t\t\t\t\t\topentype = 'reLaunch';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (tourl.indexOf('pages/') === 0) tourl = '/' + tourl;\n\t\t\t\tif (tourl.indexOf('/pages/commission/') === 0) tourl = tourl.replace('/pages/commission/','/activity/commission/');//2.4.2\n\t\t\t\tif (tourl.indexOf('/pages/sign/') === 0) tourl = tourl.replace('/pages/sign/','/pagesExt/sign/');//2.4.3\n\t\t\t\tif (tourl.indexOf('/pages/business/') === 0) tourl = tourl.replace('/pages/business/','/pagesExt/business/');//2.4.3\n\t\t\t\tif (tourl.indexOf('/pages/lipin/') === 0) tourl = tourl.replace('/pages/lipin/','/pagesExt/lipin/');//2.4.3\n\t\t\t\tif (tourl.indexOf('/pages/order/') === 0) tourl = tourl.replace('/pages/order/','/pagesExt/order/');//2.4.4\n\t\t\t\tif (tourl.indexOf('/pages/coupon/') === 0) tourl = tourl.replace('/pages/coupon/','/pagesExt/coupon/');//2.4.8\n\t\t\t\tif (tourl.indexOf('/pages/my/') === 0 && tourl.indexOf('/pages/my/usercenter') !== 0) tourl = tourl.replace('/pages/my/','/pagesExt/my/');//2.4.6\n\t\t\t\tif (tourl.indexOf('/activity/dscj/') === 0) tourl = tourl.replace('/activity/dscj/','/pagesA/dscj/');//2.5.1\n\t\t\t\tif (tourl.indexOf('/activity/hongbaoEveryday/') === 0) tourl = tourl.replace('/activity/hongbaoEveryday/','/pagesA/hongbaoEveryday/');//2.5.1\n\t\t\t\tif (tourl.indexOf('/activity/searchmember/') === 0) tourl = tourl.replace('/activity/searchmember/','/pagesA/searchmember/');//2.5.1\n\t\t\t\tif (tourl.indexOf('/pages/money/') === 0) tourl = tourl.replace('/pages/money/','/pagesExt/money/');//2.5.4\n\t\t\t\tif (tourl.indexOf('/pages/pay/') === 0) tourl = tourl.replace('/pages/pay/','/pagesExt/pay/');//2.5.4\n\t\t\t\tif (tourl.indexOf('/pages/article/') === 0) tourl = tourl.replace('/pages/article/','/pagesExt/article/');//2.5.5\n\t\t\t\tif (tourl.indexOf('/pages/form/') === 0) tourl = tourl.replace('/pages/form/','/pagesA/form/');//2.5.5\n\t\t\t\tif (tourl.indexOf('/admin/order/maidanlog') === 0) tourl = tourl.replace('/admin/order/maidanlog/','/adminBusiness/order/maidanlog');//2.5.5\n\t\t\t\tif (tourl.indexOf('/pages/shop/buy') === 0) tourl = tourl.replace('/pages/shop/buy','/pagesB/shop/buy');//2.5.9\n\t\t\t\tif (tourl.indexOf('/activity/express/') === 0) tourl = tourl.replace('/activity/express/','/pagesB/express/');//2.6.1\n\t\t\t\tif (tourl.indexOf('/activity/workorder/') === 0) tourl = tourl.replace('/activity/workorder/','/pagesB/workorder/');//2.6.1\n\t\t\t\tif (tourl.indexOf('/pages/address/') === 0) tourl = tourl.replace('/pages/address/','/pagesB/address/');//2.6.1\n\t\t\t\tif (tourl.indexOf('/pages/kefu/index') === 0) tourl = tourl.replace('/pages/kefu/index/','/pagesB/kefu/index/');//2.6.1\n\t\t\t\tif (tourl.indexOf('/pages/index/getpwd') === 0) tourl = tourl.replace('/pages/index/getpwd/','/pagesB/index/getpwd/');//2.6.1\n\t\t\t\tif (tourl.indexOf('/pages/maidan/') === 0) tourl = tourl.replace('/pages/maidan/','/pagesB/maidan/');//2.6.2\n\t\t\t\tif (tourl.indexOf('/pages/shop/commentlist') === 0) tourl = tourl.replace('/pages/shop/commentlist','/pagesB/shop/commentlist');//2.6.2\n\t\t\t\tif (tourl.indexOf('/pages/shop/classify2') === 0) tourl = tourl.replace('/pages/shop/classify2','/pagesB/shop/classify2');//2.6.4\n\t\t\t\tif (tourl.indexOf('/activity/yuyue/') === 0) tourl = tourl.replace('/activity/yuyue/','/yuyue/yuyue/');//2.6.6\n\t\t\t\t// #ifdef MP-TOUTIAO\n\t\t\t\tif (app.globalData.isdouyin == 1 && tourl.indexOf('/pages/shop/product') === 0) {\n\t\t\t\t\tapp.showLoading('加载中');\n\t\t\t\t\tapp.post('ApiShop/getDouyinProductId', {\n\t\t\t\t\t\tproid: params.id\n\t\t\t\t\t}, function(res) {\n\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\tif (res.status == 1) {\n\t\t\t\t\t\t\ttt.openEcGood({\n\t\t\t\t\t\t\t\tpromotionId: res.douyin_product_id,\n\t\t\t\t\t\t\t\tfail: function(res2) {\n\t\t\t\t\t\t\t\t\tapp.alert(res2.errMsg)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tapp.alert(res.msg)\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t\tif (tourl.indexOf('[ID]') > 0) {\n\t\t\t\t\ttourl = tourl.replace('[ID]',app.globalData.mid);\n\t\t\t\t}\n\t\t\t\tif (tourl.indexOf('[手机号]') > 0) {\n\t\t\t\t\tapp.post('ApiMy/getmemberinfo',{},function(res){\n\t\t\t\t\t\ttourl = tourl.replace('[手机号]',res.data.tel);\n\t\t\t\t\t\tapp.goto(tourl, opentype);\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\tif (app.globalData.platform == 'wx' && tourl.indexOf('https://work.weixin.qq.com/kfid/') === 0) {\n\t\t\t\t\twx.openCustomerServiceChat({\n\t\t\t\t\t\textInfo: {\n\t\t\t\t\t\t\turl: tourl\n\t\t\t\t\t\t},\n\t\t\t\t\t\tcorpId: app.globalData.initdata.corpid,\n\t\t\t\t\t\tsuccess(res) {},\n\t\t\t\t\t\tfail(res) {\n\t\t\t\t\t\t\tconsole.log('openCustomerServiceChat');\n\t\t\t\t\t\t\tconsole.log(res);\n\t\t\t\t\t\t\tapp.alert(res.errCode+' '+res.errMsg);\n\t\t\t\t\t\t},\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t\tif (tourl == 'scan::') {\n\t\t\t\t\tif (app.globalData.platform == 'h5') {\n\t\t\t\t\t\tapp.alert('请使用微信扫一扫功能扫码');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t} else if (app.globalData.platform == 'mp') {\n\t\t\t\t\t\t//#ifdef H5\n\t\t\t\t\t\tvar jweixin = require('jweixin-module');\n\t\t\t\t\t\tjweixin.ready(function() { //需在用户可能点击分享按钮前就先调用\n\t\t\t\t\t\t\tjweixin.scanQRCode({\n\t\t\t\t\t\t\t\tneedResult: 0, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，\n\t\t\t\t\t\t\t\tscanType: [\"qrCode\", \"barCode\"], // 可以指定扫二维码还是一维码，默认二者都有\n\t\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\t\tvar content = res.resultStr; // 当needResult 为 1 时，扫码返回的结果\n\t\t\t\t\t\t\t\t\t//app.goto(content);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t\t//#endif\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// #ifndef H5\n\t\t\t\t\t\tuni.scanCode({\n\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\tconsole.log(res);\n\t\t\t\t\t\t\t\tif (res.path) {\n\t\t\t\t\t\t\t\t\tapp.goto('/' + res.path);\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tvar content = res.result;\n\t\t\t\t\t\t\t\t\tapp.goto(content);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (tourl == 'share::') {\n\t\t\t\t\tif (app.globalData.platform == 'h5' || app.globalData.platform == 'mp') {\n\t\t\t\t\t\tapp.error('点击右上角发送给好友或分享到朋友圈');\n\t\t\t\t\t}\n\t\t\t\t\tif (app.globalData.platform == 'app') {\n\t\t\t\t\t\t//#ifdef APP-PLUS\n\t\t\t\t\t\tuni.showActionSheet({\n\t\t\t\t\t\t\titemList: ['发送给微信好友', '分享到微信朋友圈'],\n\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\tif (res.tapIndex >= 0) {\n\t\t\t\t\t\t\t\t\tvar scene = 'WXSceneSession';\n\t\t\t\t\t\t\t\t\tif (res.tapIndex == 1) {\n\t\t\t\t\t\t\t\t\t\tscene = 'WXSenceTimeline';\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tvar sharedata = {};\n\t\t\t\t\t\t\t\t\tsharedata.provider = 'weixin';\n\t\t\t\t\t\t\t\t\tsharedata.type = 0;\n\t\t\t\t\t\t\t\t\tsharedata.scene = scene;\n\t\t\t\t\t\t\t\t\tsharedata.title = app.globalData.initdata.name;\n\t\t\t\t\t\t\t\t\tvar fullurl = app._fullurl();\n\t\t\t\t\t\t\t\t\tif (app.globalData.mid > 0) {\n\t\t\t\t\t\t\t\t\t\tfullurl += (fullurl.indexOf('?') === -1 ? '?' : '&') + 'pid=' + app\n\t\t\t\t\t\t\t\t\t\t\t.globalData.mid\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tsharedata.href = app.globalData.pre_url + '/h5/' + app.globalData.aid +\n\t\t\t\t\t\t\t\t\t\t'.html#' + fullurl;\n\t\t\t\t\t\t\t\t\tsharedata.imageUrl = app.globalData.initdata.logo;\n\t\t\t\t\t\t\t\t\tsharedata.summary = app.globalData.initdata.desc;\n\n\t\t\t\t\t\t\t\t\tvar sharelist = app.globalData.initdata.sharelist;\n\t\t\t\t\t\t\t\t\tif(sharelist){\n\t\t\t\t\t\t\t\t\t\tfor(var i=0;i<sharelist.length;i++){\n\t\t\t\t\t\t\t\t\t\t\tif(sharelist[i]['indexurl'] == app._fullurl()){\n\t\t\t\t\t\t\t\t\t\t\t\tsharedata.title = sharelist[i].title;\n\t\t\t\t\t\t\t\t\t\t\t\tsharedata.summary = sharelist[i].desc;\n\t\t\t\t\t\t\t\t\t\t\t\tsharedata.imageUrl = sharelist[i].pic;\n\t\t\t\t\t\t\t\t\t\t\t\tif(sharelist[i].url){\n\t\t\t\t\t\t\t\t\t\t\t\t\tvar sharelink = sharelist[i].url;\n\t\t\t\t\t\t\t\t\t\t\t\t\tif(sharelink.indexOf('/') === 0){\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\tif(app.globalData.mid>0){\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\tsharedata.href = sharelink;\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tuni.share(sharedata);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\t//#endif\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (!tourl || tourl == 'contact::' || tourl == 'share::') return;\n\t\t\t\tif (tourl.indexOf('tel::') === 0) {\n\t\t\t\t\t//打电话\n\t\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t\tlet result = await this.$store.dispatch(\"requestPermissions\",'CALL_PHONE')\n\t\t\t\t\tif (result !== 1) return;\n\t\t\t\t\t// #endif\n\t\t\t\t\ttourl = tourl.slice(5);\n\t\t\t\t\tuni.makePhoneCall({\n\t\t\t\t\t\tphoneNumber: tourl\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (tourl.indexOf('tel:') === 0) {\n\t\t\t\t\t//打电话\n\t\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t\tlet result = await this.$store.dispatch(\"requestPermissions\",'CALL_PHONE')\n\t\t\t\t\tif (result !== 1) return;\n\t\t\t\t\t// #endif\n\t\t\t\t\ttourl = tourl.slice(4);\n\t\t\t\t\tuni.makePhoneCall({\n\t\t\t\t\t\tphoneNumber: tourl\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (tourl.indexOf('url::') === 0) { //外部链接\n          //外部链接奖励\n          app.get('ApiIndex/getCustom',{type:'wurl_reward',tourl:tourl}, function (customs) {});\n\t\t\t\t\tif (app.globalData.platform == 'h5' || app.globalData.platform == 'mp') {\n\t\t\t\t\t\tlocation.href = tourl.slice(5);\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttourl = '/pages/index/webView?url=' + encodeURIComponent(tourl.slice(5));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (tourl.indexOf('https://') === 0 || tourl.indexOf('http://') === 0) { //外部链接\n          //外部链接奖励\n          app.get('ApiIndex/getCustom',{type:'wurl_reward',tourl:tourl}, function (customs) {});\n\t\t\t\t\t//判断外链参数\n\t\t\t\t\tif(app.globalData.initdata){\n\t\t\t\t\t\tif(app.globalData.initdata.encryptparamtype==1){\n\t\t\t\t\t\t\ttourl += (tourl.indexOf('?') > 0 ? '&' : '?') + 'dianda_aid=' + app.globalData.aid + '&dianda_mid=' + app.globalData.mid;\n\t\t\t\t\t\t}else if(app.globalData.initdata.encryptparamtype==2 && app.globalData.initdata.encryptparam){\n\t\t\t\t\t\t\t//加密参数「对接阶段保留明文」\n\t\t\t\t\t\t\ttourl += (tourl.indexOf('?') > 0 ? '&' : '?') + 'dianda_aid=' + app.globalData.aid + '&dianda_mid=' + app.globalData.mid;\n\t\t\t\t\t\t\ttourl += (tourl.indexOf('?') > 0 ? '&' : '?')+'dianda='+app.globalData.initdata.encryptparam\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//绑定公众号追加mid\n\t\t\t\t\tif(tourl.indexOf('ApiMpBind/mpbind') > 0){\n\t\t\t\t\t\ttourl += '&mid=' + app.globalData.mid;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (app.globalData.platform == 'h5' || app.globalData.platform == 'mp') {\n\t\t\t\t\t\tlocation.href = tourl;\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttourl = '/pages/index/webView?url=' + encodeURIComponent(tourl);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (tourl.indexOf('miniProgram::') === 0) {\n\t\t\t\t\t//其他小程序\n\t\t\t\t\ttourl = tourl.slice(13);\n\t\t\t\t\tvar tourlArr = tourl.split('|'); //console.log(tourlArr)\n          var sendmid = false;//是否传递此平台用户ID\n          if(tourlArr && tourlArr.length>=1){\n            for(var i=0;i<tourlArr.length;i++){\n              if(tourlArr[i] == 'sendmid'){\n                sendmid = true;\n              }\n            }\n          }\n          var params = {\n            tourl:tourl,\n            tourlArr:tourlArr\n          }\n          if(sendmid){\n            app.post('ApiMy/getmemberinfo',{},function(res){\n              if(res.status == 1){\n                params['sendmid'] = true;\n                params['othermid']= res.data.id;\n                app.toMiniProgram(params);\n              }else{\n                app.alert('登录失败')\n              }\n            });\n          }else{\n            app.toMiniProgram(params);\n          }\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (tourl.indexOf('embeddedMiniProgram::') === 0) {\n\t\t\t\t\tconsole.log('半屏小程序打开');\n\t\t\t\t\ttourl = tourl.slice(21);\n\t\t\t\t\tvar tourlArr = tourl.split('|'); console.log(tourlArr)\n\t\t\t\t\tuni.openEmbeddedMiniProgram({\n\t\t\t\t\t\tappId: tourlArr[0],\n\t\t\t\t\t\tpath: tourlArr[1] ? tourlArr[1] : '',\n\t\t\t\t\t\textraData: {},\n\t\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\t\tconsole.log('半屏小程序打开');\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\tif (tourl == 'getmembercard::') {\n\t\t\t\t\t//领取会员卡\n\t\t\t\t\tapp.post('ApiCoupon/getmembercardparam', {\n\t\t\t\t\t\tcard_id: ''\n\t\t\t\t\t}, function(res) {\n\t\t\t\t\t\tif (res.status == 0) {\n\t\t\t\t\t\t\tapp.alert(res.msg);\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (app.globalData.platform == 'wx') {\n\t\t\t\t\t\t\tuni.navigateToMiniProgram({\n\t\t\t\t\t\t\t\tappId: 'wxeb490c6f9b154ef9',\n\t\t\t\t\t\t\t\t// 固定为此appid，不可改动\n\t\t\t\t\t\t\t\textraData: res.extraData,\n\t\t\t\t\t\t\t\t// 包括encrypt_card_id outer_str biz三个字段，须从step3中获得的链接中获取参数\n\t\t\t\t\t\t\t\tsuccess: function() {},\n\t\t\t\t\t\t\t\tfail: function() {},\n\t\t\t\t\t\t\t\tcomplete: function() {}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tlocation.href = res.ret_url;\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (tourl.indexOf('location::') === 0) {\n\t\t\t\t\t//坐标导航\n\t\t\t\t\ttourl = tourl.slice(10);\n\t\t\t\t\tvar tourlArr = tourl.split('|');\n\t\t\t\t\tvar jwd = tourlArr[1].split(',');\n\t\t\t\t\tuni.openLocation({\n\t\t\t\t\t\tlatitude: parseFloat(jwd[1]),\n\t\t\t\t\t\tlongitude: parseFloat(jwd[0]),\n\t\t\t\t\t\tname: tourlArr[0],//支付宝必填\n\t\t\t\t\t\taddress: tourlArr[0],//支付宝必填\n\t\t\t\t\t\tscale: 13,\n\t\t\t\t\t\tfail: function (error) {\n\t\t\t\t\t\t\tconsole.log(error);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (tourl.indexOf('plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin') === 0) {\n\t\t\t\t\t//小程序直播 带参数\n\t\t\t\t\ttourl = tourl + '&custom_params=' + encodeURIComponent(JSON.stringify({\n\t\t\t\t\t\tpid: app.globalData.mid\n\t\t\t\t\t}));\n\t\t\t\t}\n\t\t\t\tif (tourl.indexOf('copy::') === 0) {\n\t\t\t\t\t//复制\n\t\t\t\t\ttourl = tourl.slice(6);\n\t\t\t\t\tvar tourlArr = tourl.split('|');\n\t\t\t\t\tuni.setClipboardData({\n\t\t\t\t\t\tdata: tourlArr[0],\n\t\t\t\t\t\tshowToast: false,\n\t\t\t\t\t\tsuccess: function () {\n\t\t\t\t\t\t\tif(tourlArr[1]) app.alert(tourlArr[1]);\n\t\t\t\t\t\t\telse app.alert('复制成功');\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: function (cres) {\n\t\t\t\t\t\t\tconsole.log(cres)\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (tourl.indexOf('rewardedVideoAd::') === 0) {\n\t\t\t\t\tconsole.log('rewardedVideoAd')\n\t\t\t\t\ttourl = tourl.slice(17);\n\t\t\t\t\tvar tourlArr = tourl.split('|');\n\t\t\t\t\tif(wx.createRewardedVideoAd){\n\t\t\t\t\t\tapp.showLoading();\n\t\t\t\t\t\tapp.post('ApiRewardVideoAd/getunitid',{hid:tourlArr[0]},function(postres){\n\t\t\t\t\t\t\t//app.showLoading(false);\n\t\t\t\t\t\t\tif(postres.status == 0){\n\t\t\t\t\t\t\t\tapp.alert(postres.msg);return;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(!app.globalData.rewardedVideoAd[postres.unitid]){\n\t\t\t\t\t\t\t\tapp.globalData.rewardedVideoAd[postres.unitid] = wx.createRewardedVideoAd({ adUnitId: postres.unitid});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tvar rewardedVideoAd = app.globalData.rewardedVideoAd[postres.unitid];\n\t\t\t\t\t\t\trewardedVideoAd.load().then(() => {app.showLoading(false);rewardedVideoAd.show();}).catch(err => { app.alert('加载失败');});\n\t\t\t\t\t\t\trewardedVideoAd.onError((err) => {\n\t\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t\tapp.alert(err);\n\t\t\t\t\t\t\t\tconsole.log('onError event emit', err)\n\t\t\t\t\t\t\t\trewardedVideoAd.offLoad()\n\t\t\t\t\t\t\t\trewardedVideoAd.offClose();\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\trewardedVideoAd.onClose(res => {\n\t\t\t\t\t\t\t\tif (res && res.isEnded) {\n\t\t\t\t\t\t\t\t\t//app.alert('播放结束 发放奖励');\n\t\t\t\t\t\t\t\t\tapp.post('ApiRewardVideoAd/givereward',{hid:tourlArr[0]},function(res3){\n\t\t\t\t\t\t\t\t\t\tif(res3.status == 0){\n\t\t\t\t\t\t\t\t\t\t\tapp.alert(res3.msg);\n\t\t\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t\t\tapp.success(res3.msg);\n\t\t\t\t\t\t\t\t\t\t\tif(res3.url){\n\t\t\t\t\t\t\t\t\t\t\t\tapp.goto(res3.url,'redirect');\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tconsole.log('播放中途退出，不下发奖励');\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\trewardedVideoAd.offLoad()\n\t\t\t\t\t\t\t\trewardedVideoAd.offClose();\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tif (tourl.indexOf('sendsms::') === 0) {\n\t\t\t\t\ttourl = tourl.slice(9);\n\t\t\t\t\tvar tourlArr = tourl.split('|');\n\t\t\t\t\twx.chooseContact({\n\t\t\t\t\t\tsuccess:function(res){\n\t\t\t\t\t\t\tconsole.log(res);\n\t\t\t\t\t\t\tif(res.phoneNumber){\n\t\t\t\t\t\t\t\tapp.confirm('确定要给'+res.displayName+'('+res.phoneNumber+')发送短信吗?',function(){\n\t\t\t\t\t\t\t\t\tapp.showLoading();\n\t\t\t\t\t\t\t\t\tapp.post('ApiIndex/sendsmsurl',{tel:res.phoneNumber,tmpl:tourlArr[0],link:tourlArr[1]},function(res2){\n\t\t\t\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t\t\t\tif(res2.status == 0){\n\t\t\t\t\t\t\t\t\t\t\tapp.alert(res2.msg);\n\t\t\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t\t\tapp.success(res2.msg);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t//app.alert('手机号:'+res.phoneNumber)\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tapp.alert('未获取到手机号')\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tif (tourl.indexOf('channelsUserProfile::') === 0) {\n\t\t\t\t\t//console.log('视频号主页');\n\t\t\t\t\t//#ifdef MP-WEIXIN\n\t\t\t\t\tlet finderUserName = tourl.slice(21);\n\t\t\t\t\twx.openChannelsUserProfile({\n\t\t\t\t\t\tfinderUserName: finderUserName,\n\t\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail(res) {\n\t\t\t\t\t\t\tconsole.log(res);\n\t\t\t\t\t\t\t//app.alert(res.errno+res.errMsg)\n\t\t\t\t\t\t},\n\t\t\t\t\t})\n\t\t\t\t\t//#endif\n\t\t\t\t}\n\t\t\t\tif (tourl.indexOf('channelsLive::') === 0) {\n\t\t\t\t\t//console.log('视频号直播');\n\t\t\t\t\t//#ifdef MP-WEIXIN\n\t\t\t\t\tlet finderUserName = tourl.slice(14);\n\t\t\t\t\twx.openChannelsLive({\n\t\t\t\t\t\tfinderUserName: finderUserName,\n\t\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail(res) {\n\t\t\t\t\t\t\tconsole.log(res);\n\t\t\t\t\t\t\t//app.alert(res.errno+res.errMsg)\n\t\t\t\t\t\t},\n\t\t\t\t\t})\n\t\t\t\t\t//#endif\n\t\t\t\t}\n\t\t\t\tif (tourl.indexOf('connectwifi::') === 0) {\n\t\t\t\t\t//连接wifi\n\t\t\t\t\tuni.getSystemInfo({\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tconsole.log(res,'xxxx');\n\t\t\t\t\t\t\tlet system = ''\n\t\t\t\t\t\t\t// console.log(\"当前手机型号===>\",res)\n\t\t\t\t\t\t\tif(res.platform == 'android'){\n\t\t\t\t\t\t\t\tsystem = parseInt(res.platform.substr(8))\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(res.platform =='ios'){\n\t\t\t\t\t\t\t\tsystem = parseInt(res.platform.substr(4))\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(res.platform == 'android' && system < 6){\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle:'手机版本不支持',\n\t\t\t\t\t\t\t\t\ticon:'none'\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(res.platform == 'ios' && system < 11.2){\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle:'手机版本不支持',\n\t\t\t\t\t\t\t\t\ticon:'none'\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t\t}\t\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\n\t\t\t\t\ttourl = tourl.slice(13);\n\t\t\t\t\tvar tourlArr = tourl.split('|');\n\t\t\t\t\tvar wifi_name = tourlArr[0];\n\t\t\t\t\tvar wifi_password = tourlArr[1];\n\t\t\t\t\tapp.showLoading('连接中');\n\t\t\t\t\tif(app.globalData.platform =='wx'){\n\t\t\t\t\t\t//#ifdef MP-WEIXIN\n\t\t\t\t\t\tuni.startWifi({\n\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\tconsole.log(res,'启动wifi')\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: (error) => {\n\t\t\t\t\t\t\t\tapp.error();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.connectWifi({\n\t\t\t\t\t\t\tSSID:wifi_name, //Wi-Fi 设备名称\n\t\t\t\t\t\t\tpassword:wifi_password,//Wi-Fi 密码\n\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\tapp.success('连接成功');\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: (error) => {\n\t\t\t\t\t\t\t\tconsole.log(error)\n\t\t\t\t\t\t\t\tapp.error('连接失败')\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\t//#endif\n\t\t\t\t\t}else if(app.globalData.platform =='alipay'){\n\t\t\t\t\t\t//#ifdef MP-ALIPAY\n\t\t\t\t\t\n\t\t\t\t\t\tmy.startWifi({\n\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\t// 再连接 Wi-Fi\n\t\t\t\t\t\t\t\tmy.connectWifi({\n\t\t\t\t\t\t\t\t\tSSID: wifi_name,\n\t\t\t\t\t\t\t\t\tpassword: wifi_password,\n\t\t\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\t\t\tapp.success('连接成功');\n\t\t\t\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t\t\t},fail:function(res2){\n\t\t\t\t\t\t\t\t\t\tapp.error('连接失败')\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\t//#endif\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t}\n\t\t\t\tif (app.isNull(opentype)) {\n\t\t\t\t\tvar mv = tourl.split(\"?\");\n\t\t\t\t\tvar urlpath = mv[0];\n\t\t\t\t\tif (app.globalData.platform == 'h5' && urlpath == app._fullurl()) {\n\t\t\t\t\t\topentype = 'reLaunch';\n\t\t\t\t\t} else {\n\t\t\t\t\t\topentype = 'navigate';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tapp.globalData.rewardedVideoAd = {};\n\t\t\t\tif (opentype == 'switchTab') {\n\t\t\t\t\tvar args = new Object();\n\t\t\t\t\tvar name, value;\n\t\t\t\t\tvar url = tourl; //跳转链接\n\t\t\t\t\tvar num = url.indexOf(\"?\");\n\t\t\t\t\tif (num >= 0) {\n\t\t\t\t\t\tvar urlbase = url.substr(0, num);\n\t\t\t\t\t\tvar str = url.substr(num + 1); //取得所有参数\n\n\t\t\t\t\t\tvar arr = str.split(\"&\"); //各个参数放到数组里\n\n\t\t\t\t\t\tfor (var i = 0; i < arr.length; i++) {\n\t\t\t\t\t\t\tnum = arr[i].indexOf(\"=\");\n\n\t\t\t\t\t\t\tif (num > 0) {\n\t\t\t\t\t\t\t\tname = arr[i].substring(0, num);\n\t\t\t\t\t\t\t\tvalue = arr[i].substr(num + 1);\n\t\t\t\t\t\t\t\targs[name] = value;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\turl: urlbase,\n\t\t\t\t\t\t\tsuccess: function(e) {\n\t\t\t\t\t\t\t\tvar page = getCurrentPages().pop();\n\t\t\t\t\t\t\t\tif (page == undefined || page == null) return;\n\t\t\t\t\t\t\t\tpage.onLoad(args);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\turl: tourl\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} else if (opentype == 'redirect' || opentype == 'redirectTo') {\n\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\turl: tourl\n\t\t\t\t\t});\n\t\t\t\t} else if (opentype == 'reLaunch') {\n\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\turl: tourl\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tvar pages = getCurrentPages();\n\n\t\t\t\t\tif (pages.length >= 10) {\n\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\turl: tourl\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: tourl\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n      toMiniProgram:function(params= {tourl:'',tourlArr:[]}){\n        var app = this;\n        var tourl    = params && params['tourl'] || '';\n        var tourlArr = params && params['tourlArr'] || [];\n        if(tourlArr && tourlArr[1] && params && params['sendmid'] && params['othermid'] && params['othermid']>0){\n          var pos = tourlArr[1].indexOf('?');\n          if(pos && pos>0){\n            tourlArr[1] += '&othermid='+params['othermid'];\n          }else{\n            tourlArr[1] += '?othermid='+params['othermid'];\n          }\n        }\n        if(app.globalData.platform == 'app'){\n        \t//#ifdef APP-PLUS\n        \tplus.share.getServices(function(res){  \n        \t\tvar sweixin = null;  \n        \t\tfor(var i=0;i<res.length;i++){  \n        \t\t\tvar t = res[i];  \n        \t\t\tif(t.id == 'weixin'){  \n        \t\t\t\tsweixin = t;  \n        \t\t\t}  \n        \t\t}  \n        \t\tif(sweixin){  \n        \t\t\tsweixin.launchMiniProgram({  \n        \t\t\t\tid:tourlArr[2],  \n        \t\t\t\ttype: 0,\n        \t\t\t\tpath:tourlArr[1] ? tourlArr[1] : '',\n        \t\t\t});  \n        \t\t}\n        \t},function(res){  \n        \t\tconsole.log(JSON.stringify(res));  \n        \t});\n        \t//#endif\n        }else{\n\t\t\t\t\t//#ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO\n        \tuni.navigateToMiniProgram({\n        \t\tappId: tourlArr[0],\n        \t\tpath: tourlArr[1] ? tourlArr[1] : '',\n        \t\tcomplete: function() {\n        \t\t},\n        \t\tsuccess:function(){\n        \t\t\tif(tourlArr[3]){\n        \t\t\t\tapp.post('ApiRewardVideoAd/givereward',{hid:tourlArr[3]},function(res3){\n        \t\t\t\t\tif(res3.status == 0){\n        \t\t\t\t\t\t//app.alert(res3.msg);\n        \t\t\t\t\t}else{\n        \t\t\t\t\t\tapp.success(res3.msg);\n        \t\t\t\t\t\tif(res3.url){\n        \t\t\t\t\t\t\tapp.goto(res3.url,'redirect');\n        \t\t\t\t\t\t}\n        \t\t\t\t\t}\n        \t\t\t\t});\n        \t\t\t}\n        \t\t}\n        \t});\n\t\t\t\t\t//#endif\n        }\n      },\n\t\t\tgoback: function(isreload) {\n\t\t\t\tvar app = this;\n\t\t\t\tvar pages = getCurrentPages();\n\t\t\t\tif (isreload && pages.length > 1) {\n\t\t\t\t\tvar prePage = pages[pages.length - 2];\n\t\t\t\t\tprePage.$vm.getdata();\n\t\t\t\t}\n\t\t\t\tif (pages.length == 1) {\n\t\t\t\t\tapp.goto(app.globalData.indexurl, 'reLaunch');\n\t\t\t\t} else {\n\t\t\t\t\tuni.navigateBack({\n\t\t\t\t\t\tfail: function() {\n\t\t\t\t\t\t\tapp.goto(app.globalData.indexurl, 'reLaunch');\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tpost: function(url, param, callback) {\n\t\t\t\tthis.request('POST', url, param, callback);\n\t\t\t},\n\t\t\tget: function(url, param, callback) {\n\t\t\t\tthis.request('GET', url, param, callback);\n\t\t\t},\n\t\t\trequest: function(method, url, param, callback) {\n\t\t\t\tvar oldurl = url;\n\t\t\t\tvar app = this;\n\t\t\t\tif (url.substring(0, 8) != 'https://') {\n\t\t\t\t\turl = app.globalData.baseurl + url;\n\t\t\t\t\turl += (url.indexOf('?') > 0 ? '&' : '?') + 'aid=' + app.globalData.aid + '&platform=' + app\n\t\t\t\t\t\t.globalData.platform + '&session_id=' + app.globalData.session_id + '&pid=' + app.globalData\n\t\t\t\t\t\t.pid;\n\t\t\t\t\tif (app.globalData.isdouyin == 1) {\n\t\t\t\t\t\turl += '&isdouyin=1';\n\t\t\t\t\t}\n\t\t\t\t\tif (!app.globalData.isinit) {\n\t\t\t\t\t\turl += '&needinit=1';\n\t\t\t\t\t\tif (app.globalData.uid) url += '&uid=' + app.globalData.uid;\n\t\t\t\t\t}\n\t\t\t\t\tif (app.globalData.seetype) {\n\t\t\t\t\t\turl += '&seetype=' + app.globalData.seetype;\n\t\t\t\t\t}\n\t\t\t\t\tif (app.globalData.scene) {\n\t\t\t\t\t\turl += '&scene=' + app.globalData.scene;\n\t\t\t\t\t}\n\t\t\t\t\tif (app.globalData.priceRate) {\n\t\t\t\t\t\turl += '&priceRate=' + app.globalData.priceRate;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tuni.request({\n\t\t\t\t\turl: url,\n\t\t\t\t\t//仅为示例，并非真实的接口地址\n\t\t\t\t\tdata: param,\n\t\t\t\t\tmethod: method,\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tapp.setinitdata(res);\n\t\t\t\t \n\t\t\t\t\t\tif (res.data && res.data.status == -1) { //跳转登录页\n\t\t\t\t\t\t\tif(res.data.data){\n\t\t\t\t\t\t\t\tif(res.data.data.pid && res.data.data.pid>0){\n\t\t\t\t\t\t\t\tapp.globalData.pid = res.data.data.pid;\n\t\t\t\t\t\t\t\tuni.setStorageSync('pid',res.data.data.pid);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\tif((app.globalData.initdata.logintype).length == 0 && app.inArray(app.globalData.platform,['wx','mp','baidu','qq','toutiao','alipay'])) return;\n\t\t\t\t\t\t\tvar pages = getCurrentPages();\n\t\t\t\t\t\t\tvar currentPage = pages[pages.length - 1];\n\t\t\t\t\t\t\tcurrentPage.$vm.loading = false;\n\t\t\t\t\t\t\tvar frompage = '';\n\t\t\t\t\t\t\tvar nowurl = app._url();\t\t\t\t\t\t \n\t\t\t\t\t\t\tvar opentype = 'reLaunch';\n\t\t\t\t\t\t\tif (app.globalData.platform == 'baidu') {\n\t\t\t\t\t\t\t\tif (nowurl == '/pages/my/usercenter') {\n\t\t\t\t\t\t\t\t\topentype = 'redirect';\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\topentype = 'navigate';\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (nowurl != '/pages/index/login' && nowurl != '/pages/index/reg' && nowurl !=\n\t\t\t\t\t\t\t\t'/pages/index/getpwd') {\n\t\t\t\t\t\t\t\tvar frompage = encodeURIComponent(app._fullurl());\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (res.data.authlogin == 1 || res.data.authlogin == 2) {\n\t\t\t\t\t\t\t\tapp.authlogin(function(res2) {\n\t\t\t\t\t\t\t\t\tif (res2.status == 1) {\n\t\t\t\t\t\t\t\t\t\tif (res2.msg) app.success(res2.msg);\n\t\t\t\t\t\t\t\t\t\tcurrentPage.$vm.getdata();\n\t\t\t\t\t\t\t\t\t} else if (res2.status == 2) {\n\t\t\t\t\t\t\t\t\t\tapp.goto('/pages/index/login?frompage=' + frompage +\n\t\t\t\t\t\t\t\t\t\t\t'&logintype=4&login_bind=1', opentype);\n\t\t\t\t\t\t\t\t\t}else {\n\t\t\t\t\t\t\t\t\t\tconsole.log(res2);\n\t\t\t\t\t\t\t\t\t\tapp.goto('/pages/index/login?frompage=' + frompage,\n\t\t\t\t\t\t\t\t\t\t\topentype);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t},{authlogin:res.data.authlogin,ali_appid:res.data.ali_appid});\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tapp.goto('/pages/index/login?frompage=' + frompage, opentype);\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (res.data && res.data.status == -10) { //跳转管理员登录\n\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\tvar pages = getCurrentPages();\n\t\t\t\t\t\t\tvar currentPage = pages[pages.length - 1];\n\t\t\t\t\t\t\tcurrentPage.$vm.loading = false\n\t\t\t\t\t\t\t//管理员登录\n\t\t\t\t\t\t\tapp.goto('/admin/index/login', 'redirect');\n\t\t\t\t\t\t} else if (res.data && res.data.status == -2) { //公众号或h5跳转\n\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\tvar pages = getCurrentPages();\n\t\t\t\t\t\t\tvar currentPage = pages[pages.length - 1];\n\t\t\t\t\t\t\tcurrentPage.$vm.loading = false\n\t\t\t\t\t\t\t//跳转\n\t\t\t\t\t\t\tlocation.href = res.data.url\n\t\t\t\t\t\t} else if (res.data && res.data.status == -3) { //跳转到指定页\n\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\tvar pages = getCurrentPages();\n\t\t\t\t\t\t\tvar currentPage = pages[pages.length - 1];\n\t\t\t\t\t\t\tcurrentPage.$vm.loading = false\n\t\t\t\t\t\t\t//跳转\n\t\t\t\t\t\t\tapp.goto(res.data.url, 'redirect');\n\t\t\t\t\t\t} else if (res.data && res.data.status == -4) { //弹窗提示并跳转到指定页\n\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\tvar pages = getCurrentPages();\n\t\t\t\t\t\t\tvar currentPage = pages[pages.length - 1];\n\t\t\t\t\t\t\tcurrentPage.$vm.loading = false\n\t\t\t\t\t\t\t// 判断是否为手机后台商品编辑页面，带有上一页的筛选参数\n\t\t\t\t\t\t\tif(currentPage.route == 'admin/product/edit'){\n\t\t\t\t\t\t\t\tlet cids = currentPage.options.cids;\n\t\t\t\t\t\t\t\t//弹出提示\n\t\t\t\t\t\t\t\tapp.alert(res.data.msg, function() {\n\t\t\t\t\t\t\t\t\tif (res.data.url) {\n\t\t\t\t\t\t\t\t\t\tconst url = res.data.url + '?cids=' + cids;\n\t\t\t\t\t\t\t\t\t\tapp.goto(url, 'redirect');\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t//弹出提示\n\t\t\t\t\t\t\t\tapp.alert(res.data.msg, function() {\n\t\t\t\t\t\t\t\t\tif (res.data.url) {\n\t\t\t\t\t\t\t\t\t\tapp.goto(res.data.url, 'redirect');\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (res.data && res.data.status == -5) { //弹窗提示并跳转上一页\n\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\tvar pages = getCurrentPages();\n\t\t\t\t\t\t\tvar currentPage = pages[pages.length - 1];\n\t\t\t\t\t\t\tcurrentPage.$vm.loading = false\n\t\t\t\t\t\t\t//弹出提示\n\t\t\t\t\t\t\tapp.alert(res.data.msg, function() {\n\t\t\t\t\t\t\t\tuni.navigateBack({ delta: 1 })\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}else if (res.data && res.data.status == -6) { //弹窗提示并跳转上一页\n\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\tvar pages = getCurrentPages();\n\t\t\t\t\t\t\tvar currentPage = pages[pages.length - 1];\n\t\t\t\t\t\t\t    currentPage.$vm.loading = false\t\t\t\t\t\t\t \t\t\t\t\t \t\t\t\t\t\t\t \n\t\t\t\t\t\t\t\tapp.alert(res.data.msg, function(frompage) {\t\t\t\t\t\t\t\t\t \t\t\n\t\t\t\t\t\t\t\tvar frompage = '/pages/my/usercenter';\n \t\t\t\t\t\t    \t  app.goto('/pages/index/login?frompage='+frompage+'&logintype=5&checknickname=1', 'reLaunch')\n\t\t\t\t\t\t\t\t});\t\t \t\t \n\t\t\t\t\t\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\ttypeof callback == \"function\" && callback(res.data);\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tfail: function(res) {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tif (oldurl != 'ApiIndex/linked') {\n\t\t\t\t\t\t\t\tif (res.errMsg != 'request:fail timeout') {\n\t\t\t\t\t\t\t\t\tconsole.log(res);\n\t\t\t\t\t\t\t\t\t//app.alert('请求失败：' + JSON.stringify(res));\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (e) {}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tbaselogin:function(callback){\n\t\t\t\tconsole.log('baselogin');\n\t\t\t\tvar app = this;\n\t\t\t\tif (app.globalData.platform == 'wx') {\n\t\t\t\t\t//#ifdef MP-WEIXIN\n\t\t\t\t\twx.login({\n\t\t\t\t\t\tsuccess(res1) {\n\t\t\t\t\t\t\tvar code = res1.code;\n\t\t\t\t\t\t\tapp.post('ApiIndex/wxbaselogin', {\n\t\t\t\t\t\t\t\tcode: code,\n\t\t\t\t\t\t\t\tpid: app.globalData.pid\n\t\t\t\t\t\t\t}, function(res) {\n\t\t\t\t\t\t\t\ttypeof callback == \"function\" && callback(res);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t//#endif\n\t\t\t\t} else if (app.globalData.platform == 'baidu') {\n\t\t\t\t\t//#ifdef MP-BAIDU\n\t\t\t\t\tuni.getLoginCode({\n\t\t\t\t\t\tsuccess(res1) {\n\t\t\t\t\t\t\tconsole.log('getLoginCode',res1);\n\t\t\t\t\t\t\tvar code = res1.code;\n\t\t\t\t\t\t\tapp.post('ApiIndex/baidulogin', {\n\t\t\t\t\t\t\t\tcode: code,\n\t\t\t\t\t\t\t\tpid: app.globalData.pid\n\t\t\t\t\t\t\t}, function(res) {\n\t\t\t\t\t\t\t\ttypeof callback == \"function\" && callback(res);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail(res2){\n\t\t\t\t\t\t\tconsole.log(res2)\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t//#endif\n\t\t\t\t} else if (app.globalData.platform == 'qq') {\n\t\t\t\t\t//#ifdef MP-QQ\n\t\t\t\t\tqq.login({\n\t\t\t\t\t\tsuccess(res1) {\n\t\t\t\t\t\t\tapp.post('ApiIndex/qqlogin', {\n\t\t\t\t\t\t\t\tcode: res1.code,\n\t\t\t\t\t\t\t\tpid: app.globalData.pid\n\t\t\t\t\t\t\t}, function(res) {\n\t\t\t\t\t\t\t\ttypeof callback == \"function\" && callback(res);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t//#endif\n\t\t\t\t} else if (app.globalData.platform == 'toutiao') {\n\t\t\t\t\t//#ifdef MP-TOUTIAO\n\t\t\t\t\ttt.login({\n\t\t\t\t\t\tforce: true,\n\t\t\t\t\t\tsuccess(res1) {\n\t\t\t\t\t\t\tapp.post('ApiIndex/toutiaologin', {\n\t\t\t\t\t\t\t\tcode: res1.code,\n\t\t\t\t\t\t\t\tpid: app.globalData.pid\n\t\t\t\t\t\t\t}, function(res) {\n\t\t\t\t\t\t\t\ttypeof callback == \"function\" && callback(res);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t//#endif\n\t\t\t\t} else if (app.globalData.platform == 'alipay') {\n\t\t\t\t\t//#ifdef MP-ALIPAY\n\t\t\t\t\tmy.getAuthCode({\n\t\t\t\t\t\tscopes: 'auth_base',\n\t\t\t\t\t\tsuccess(res1) {\n\t\t\t\t\t\t\tapp.post('ApiIndex/alipaylogin', {\n\t\t\t\t\t\t\t\tcode: res1.authCode,\n\t\t\t\t\t\t\t\tpid: app.globalData.pid\n\t\t\t\t\t\t\t}, function(res) {\n\t\t\t\t\t\t\t\ttypeof callback == \"function\" && callback(res);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t//#endif\n\t\t\t\t}else if (app.globalData.platform == 'mp') {\n\t\t\t\t\t//#ifdef H5\n\t\t\t\t\tvar frompage = '';\n\t\t\t\t\tvar nowurl = app._url();\n\t\t\t\t\tif (nowurl != '/pages/index/login' && nowurl != '/pages/index/reg' && nowurl !=\n\t\t\t\t\t\t'/pages/index/getpwd') {\n\t\t\t\t\t\tfrompage = encodeURIComponent(app._fullurl());\n\t\t\t\t\t}\n\t\t\t\t\tlocation.href = app.globalData.pre_url + '/index.php?s=ApiIndex/mpbaselogin&aid=' + app.globalData\n\t\t\t\t\t\t.aid + '&session_id=' + app.globalData.session_id + '&pid=' + app.globalData.pid +\n\t\t\t\t\t\t'&frompage=' + encodeURIComponent(frompage);\n\t\t\t\t\t//#endif\n\t\t\t\t}\n\t\t\t},\n\t\t\tauthlogin: function(callback, params) {\n\t\t\t\tvar app = this;\n\t\t\t\tvar authlogin = 0;\n\t\t\t\tif (params && params.authlogin) {\n\t\t\t\t\tauthlogin = params.authlogin;\n\t\t\t\t}\n\t\t\t\tif (app.globalData.platform == 'wx') {\n\t\t\t\t\t//#ifdef MP-WEIXIN\n\t\t\t\t\tif(authlogin==1 || authlogin==2){\n\t\t\t\t\t\twx.login({\n\t\t\t\t\t\t\tsuccess(res1) {\n\t\t\t\t\t\t\t\tvar code = res1.code;\n\t\t\t\t\t\t\t\tapp.post('ApiIndex/wxlogin', {\n\t\t\t\t\t\t\t\t\tcode: code,\n\t\t\t\t\t\t\t\t\tauthlogin:authlogin,\n\t\t\t\t\t\t\t\t\tpid: app.globalData.pid,\n\t\t\t\t\t\t\t\t\tyqcode:!params?'':params.yqcode,\n\t\t\t\t\t\t\t\t\tregbid:app.globalData.regbid\n\t\t\t\t\t\t\t\t}, function(res) {\n\t\t\t\t\t\t\t\t\ttypeof callback == \"function\" && callback(res);\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}else{\n\t\t\t\t\t\twx.getUserProfile({\n\t\t\t\t\t\t\tlang: 'zh_CN',\n\t\t\t\t\t\t\tdesc: '用于展示头像昵称',\n\t\t\t\t\t\t\tsuccess: function(res2) {\n\t\t\t\t\t\t\t\tconsole.log(res2)\n\t\t\t\t\t\t\t\tvar userinfo = res2.userInfo;\n\t\t\t\t\t\t\t\twx.login({\n\t\t\t\t\t\t\t\t\tsuccess(res1) {\n\t\t\t\t\t\t\t\t\t\tvar code = res1.code;\n\t\t\t\t\t\t\t\t\t\tapp.post('ApiIndex/wxlogin', {\n\t\t\t\t\t\t\t\t\t\t\tcode: code,\n\t\t\t\t\t\t\t\t\t\t\tuserinfo: userinfo,\n\t\t\t\t\t\t\t\t\t\t\tpid: app.globalData.pid,\n\t\t\t\t\t\t\t\t\t\t\tyqcode:!params?'':params.yqcode,\n\t\t\t\t\t\t\t\t\t\t\tregbid:app.globalData.regbid\n\t\t\t\t\t\t\t\t\t\t}, function(res) {\n\t\t\t\t\t\t\t\t\t\t\ttypeof callback == \"function\" && callback(res);\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: function(res2) {\n\t\t\t\t\t\t\t\tconsole.log(res2)\n\t\t\t\t\t\t\t\tif (res2.errMsg == 'getUserProfile:fail auth deny') {\n\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\ttypeof callback == \"function\" && callback({\n\t\t\t\t\t\t\t\t\t\tstatus: 0,\n\t\t\t\t\t\t\t\t\t\tmsg: res2.errMsg\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\t//#endif\n\t\t\t\t} else if (app.globalData.platform == 'mp' || app.globalData.platform == 'h5') {\n\t\t\t\t\t//#ifdef H5\n\t\t\t\t\tvar frompage = '';\n\t\t\t\t\tvar nowurl = app._url();\n\t\t\t\t\tif (nowurl != '/pages/index/login' && nowurl != '/pages/index/reg' && nowurl !=\n\t\t\t\t\t\t'/pages/index/getpwd') {\n\t\t\t\t\t\tfrompage = encodeURIComponent(app._fullurl());\n\t\t\t\t\t}\n\t\t\t\t\tif (params && params.frompage) {\n\t\t\t\t\t\tfrompage = params.frompage;\n\t\t\t\t\t}\n\t\t\t\t\tif (navigator.userAgent.indexOf('AlipayClient') > -1) {\n\t\t\t\t\t\tap.getAuthCode ({\n\t\t\t\t\t\t    appId :  params.ali_appid ,\n\t\t\t\t\t\t    scopes : ['auth_base'],\n\t\t\t\t\t\t},function(res){\n\t\t\t\t\t\t   //var res = JSON.stringify(res);\n\t\t\t\t\t\t    if(!res.error && res.authCode){\n\t\t\t\t\t\t        app.post('ApiIndex/alipaylogin', {\n\t\t\t\t\t\t        \tcode: res.authCode,\n\t\t\t\t\t\t        \tpid: app.globalData.pid,\n\t\t\t\t\t\t          platform:\"h5\",\n                      regbid:app.globalData.regbid,\n\t\t\t\t\t\t\t\t  silent:1\n\t\t\t\t\t\t        }, function(res2) {\n\t\t\t\t\t\t\t\t\ttypeof callback == \"function\" && callback(res2);\n\t\t\t\t\t\t        });\n\t\t\t\t\t\t    }else{\n\t\t\t\t\t\t      app.showLoading(false);\n\t\t\t\t\t\t      return\n\t\t\t\t\t\t    }\n\t\t\t\t\t\t});\n\t\t\t\t\t}else{\n\t\t\t\t\t\tlocation.href = app.globalData.pre_url + '/index.php?s=ApiIndex/shouquan&aid=' + app.globalData\n\t\t\t\t\t\t\t.aid + '&session_id=' + app.globalData.session_id + '&pid=' + app.globalData.pid + '&authlogin=' + authlogin + '&regbid=' + app.globalData.regbid +\n\t\t\t\t\t\t\t'&frompage=' + encodeURIComponent(frompage);\n\t\t\t\t\t}\n\t\t\t\t\t//#endif\n\t\t\t\t} else if (app.globalData.platform == 'app') {\n\t\t\t\t\t//#ifdef APP-PLUS\n\t\t\t\t\tplus.oauth.getServices(function(services) {\n\t\t\t\t\t\tconsole.log(services)\n\t\t\t\t\t\tlet s = services[0]\n\t\t\t\t\t\tfor (var i in services) {\n\t\t\t\t\t\t\tvar service = services[i];\n\t\t\t\t\t\t\tif (service.id == 'weixin') {\n\t\t\t\t\t\t\t\ts = service;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconsole.log(s)\n\t\t\t\t\t\tconsole.log('x----')\n\t\t\t\t\t\ts.authorize(function(e) {\n\t\t\t\t\t\t\tconsole.log(e);\n\t\t\t\t\t\t\tvar code = e.code;\n\t\t\t\t\t\t\tapp.post('ApiIndex/appwxlogin', {\n\t\t\t\t\t\t\t\tcode: code,\n\t\t\t\t\t\t\t\tpid: app.globalData.pid,\n                regbid:app.globalData.regbid\n\t\t\t\t\t\t\t}, function(res) {\n\t\t\t\t\t\t\t\ttypeof callback == \"function\" && callback(res);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t},function(err) {\n\t\t\t\t\t\t\t\ttypeof callback == \"function\" && callback({\n\t\t\t\t\t\t\t\t\tstatus: 0,\n\t\t\t\t\t\t\t\t\tmsg: JSON.stringify(err)\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t},function(err){\n\t\t\t\t\t\ttypeof callback == \"function\" && callback({\n\t\t\t\t\t\t\tstatus: 0,\n\t\t\t\t\t\t\tmsg: JSON.stringify(err)\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t\t//#endif\n\t\t\t\t} else if (app.globalData.platform == 'baidu') {\n\t\t\t\t\t//#ifdef MP-BAIDU\n\t\t\t\t\tuni.getLoginCode({\n\t\t\t\t\t\tsuccess: res => {\n\t\t\t\t\t\t\tconsole.log('getLoginCode success', res);\n\t\t\t\t\t\t\tvar code = res.code;\n\t\t\t\t\t\t\tapp.post('ApiIndex/baidulogin', {\n\t\t\t\t\t\t\t\tcode: code,\n\t\t\t\t\t\t\t\tpid: app.globalData.pid,\n                regbid:app.globalData.regbid\n\t\t\t\t\t\t\t}, function(res) {\n\t\t\t\t\t\t\t\ttypeof callback == \"function\" && callback(res);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: err => {\n\t\t\t\t\t\t\ttypeof callback == \"function\" && callback({\n\t\t\t\t\t\t\t\tstatus: 0,\n\t\t\t\t\t\t\t\tmsg: err.errMsg\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t//#endif\n\t\t\t\t} else if (app.globalData.platform == 'qq') {\n\t\t\t\t\t//#ifdef MP-QQ\n\t\t\t\t\tqq.login({\n\t\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\t\tif (res.code) {\n\t\t\t\t\t\t\t\tapp.post('ApiIndex/qqlogin', {\n\t\t\t\t\t\t\t\t\tcode: res.code,\n\t\t\t\t\t\t\t\t\tpid: app.globalData.pid,\n                  regbid:app.globalData.regbid\n\t\t\t\t\t\t\t\t}, function(res) {\n\t\t\t\t\t\t\t\t\ttypeof callback == \"function\" && callback(res);\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\ttypeof callback == \"function\" && callback({\n\t\t\t\t\t\t\t\t\tstatus: 0,\n\t\t\t\t\t\t\t\t\tmsg: res.errMsg\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail(res) {\n\t\t\t\t\t\t\ttypeof callback == \"function\" && callback({\n\t\t\t\t\t\t\t\tstatus: 0,\n\t\t\t\t\t\t\t\tmsg: res.errMsg\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t//#endif\n\t\t\t\t} else if (app.globalData.platform == 'toutiao') {\n\t\t\t\t\t//#ifdef MP-TOUTIAO\n\t\t\t\t\ttt.login({\n\t\t\t\t\t\tforce: true,\n\t\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\t\tif (res.code) {\n\t\t\t\t\t\t\t\tapp.post('ApiIndex/toutiaologin', {\n\t\t\t\t\t\t\t\t\tcode: res.code,\n\t\t\t\t\t\t\t\t\tpid: app.globalData.pid,\n                  regbid:app.globalData.regbid\n\t\t\t\t\t\t\t\t}, function(res) {\n\t\t\t\t\t\t\t\t\ttypeof callback == \"function\" && callback(res);\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\ttypeof callback == \"function\" && callback({\n\t\t\t\t\t\t\t\t\tstatus: 0,\n\t\t\t\t\t\t\t\t\tmsg: res.errMsg\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail(res) {\n\t\t\t\t\t\t\ttypeof callback == \"function\" && callback({\n\t\t\t\t\t\t\t\tstatus: 0,\n\t\t\t\t\t\t\t\tmsg: res.errMsg\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t//#endif\n\t\t\t\t} else if (app.globalData.platform == 'alipay') {\n\t\t\t\t\t//#ifdef MP-ALIPAY\n\t\t\t\t\tmy.getAuthCode({\n\t\t\t\t\t\tscopes: 'auth_base',\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\t\tif (res.authCode) {\n\t\t\t\t\t\t\t\tapp.post('ApiIndex/alipaylogin', {\n\t\t\t\t\t\t\t\t\tcode: res.authCode,\n\t\t\t\t\t\t\t\t\tpid: app.globalData.pid,\n                  regbid:app.globalData.regbid\n\t\t\t\t\t\t\t\t}, function(res) {\n\t\t\t\t\t\t\t\t\ttypeof callback == \"function\" && callback(res);\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (res) => {\n\t\t\t\t\t\t\ttypeof callback == \"function\" && callback({\n\t\t\t\t\t\t\t\tstatus: 0,\n\t\t\t\t\t\t\t\tmsg: res.errMsg\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t//#endif\n\t\t\t\t}\n\t\t\t},\n\t\t\tsetinitdata: function(res) {\n\t\t\t\tvar app = this;\n\t\t\t\tvar oldmid = app.globalData.mid;\n\t\t\t\tif (res && res.data && (res.data.mid || res.data.mid === 0) && app.globalData.mid != res.data.mid) {\n\t\t\t\t\tapp.globalData.mid = res.data.mid;\n\t\t\t\t\tif (res.data.session_id) {\n\t\t\t\t\t\tuni.setStorageSync('session_id', res.data.session_id);\n\t\t\t\t\t\tapp.globalData.session_id = res.data.session_id;\n\t\t\t\t\t}\n\t\t\t\t\tif (app.globalData.mid) {\n\t\t\t\t\t\tapp.globalData.socket_token = res.data.socket_token\n\t\t\t\t\t\tapp.openSocket();\n\t\t\t\t\t\tuni.removeStorageSync('pid');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (res && res.data && res.data._initdata) {\n\t\t\t\t\tapp.globalData.isinit = true;\n\t\t\t\t\tres.data._initdata.pre_url = app.globalData.pre_url;\n\t\t\t\t\tapp.globalData.initdata = res.data._initdata;\n\t\t\t\t\tapp.globalData.mid = res.data._initdata.mid;\n\t\t\t\t\tapp.globalData.isdouyin = res.data._initdata.isdouyin;\n\t\t\t\t\tuni.setStorageSync('session_id', res.data._initdata.session_id);\n\t\t\t\t\tapp.globalData.session_id = res.data._initdata.session_id;\n\t\t\t\t\tif(res.data._initdata.copyinfo){\n\t\t\t\t\t\tconsole.log(res.data._initdata.copyinfo)\n\t\t\t\t\t\tapp.copy(res.data._initdata.copyinfo);\n\t\t\t\t\t}\n\t\t\t\t\tif (app.globalData.platform == 'mp') {\n\t\t\t\t\t\t//#ifdef H5\n\t\t\t\t\t\tvar share_package = res.data.share_package;\n\t\t\t\t\t\tvar jweixin = require('jweixin-module');\n\t\t\t\t\t\tjweixin.config({\n\t\t\t\t\t\t\tdebug: false,\n\t\t\t\t\t\t\tappId: share_package.appId,\n\t\t\t\t\t\t\ttimestamp: share_package.timestamp,\n\t\t\t\t\t\t\tnonceStr: share_package.nonceStr,\n\t\t\t\t\t\t\tsignature: share_package.signature,\n\t\t\t\t\t\t\tjsApiList: [\n\t\t\t\t\t\t\t\t'checkJsApi',\n\t\t\t\t\t\t\t\t'onMenuShareAppMessage',\n\t\t\t\t\t\t\t\t'onMenuShareTimeline',\n\t\t\t\t\t\t\t\t'updateAppMessageShareData',\n\t\t\t\t\t\t\t\t'updateTimelineShareData',\n\t\t\t\t\t\t\t\t'chooseImage',\n\t\t\t\t\t\t\t\t'previewImage',\n\t\t\t\t\t\t\t\t'uploadImage',\n\t\t\t\t\t\t\t\t'openLocation',\n\t\t\t\t\t\t\t\t'getLocation',\n\t\t\t\t\t\t\t\t'closeWindow',\n\t\t\t\t\t\t\t\t'scanQRCode',\n\t\t\t\t\t\t\t\t'chooseWXPay',\n\t\t\t\t\t\t\t\t'addCard',\n\t\t\t\t\t\t\t\t'chooseCard',\n\t\t\t\t\t\t\t\t'openCard'\n\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t'openTagList': ['wx-open-launch-weapp']\n\t\t\t\t\t\t});\n\t\t\t\t\t\t//#endif\n\t\t\t\t\t}\n\t\t\t\t\tif (app.globalData.mid) {\n\t\t\t\t\t\tapp.globalData.socket_token = res.data.socket_token\n\t\t\t\t\t\tapp.openSocket();\n\t\t\t\t\t\tuni.removeStorageSync('pid');\n\t\t\t\t\t}\n\t\t\t\t\tif(!app.globalData.mid && (app.globalData.initdata.logintype).length == 0){\n\t\t\t\t\t\tapp.baselogin(function(res2){\n\t\t\t\t\t\t\t\tvar pages = getCurrentPages(); //获取加载的页面\n\t\t\t\t\t\t\t\tvar currentPage = pages[pages.length - 1]; //获取当前页面的对象\n\t\t\t\t\t\t\t\tvar url = '/' + (currentPage.route ? currentPage.route : currentPage.__route__); //当前页面url \n\t\t\t\t\t\t\t\tif(url == '/pages/index/login'){\n\t\t\t\t\t\t\t\t\tif(app.globalData.platform == 'baidu'){\n\t\t\t\t\t\t\t\t\t\tvar opts = currentPage.options;\n\t\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t\tvar opts = currentPage.$vm.opt;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tconsole.log(opts)\n\t\t\t\t\t\t\t\t\tif(opts && opts.frompage){\n\t\t\t\t\t\t\t\t\t\tapp.goto(decodeURIComponent(opts.frompage), 'redirect');\n\t\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t\tapp.goto('/pages/my/usercenter', 'redirect');\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\tcurrentPage.$vm.getdata();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}else{\n\t\t\t\t\tapp.globalData.isinit = false;\n\t\t\t\t}\n\t\t\t\tif (app.globalData.mid && app.globalData.mid != oldmid) {\n\t\t\t\t\tif (app.globalData.platform == 'wx') {\n\t\t\t\t\t\t//#ifdef MP-WEIXIN\n\t\t\t\t\t\twx.login({\n\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\tif (res.code) {\n\t\t\t\t\t\t\t\t\tapp.post('ApiIndex/setwxopenid', {\n\t\t\t\t\t\t\t\t\t\tcode: res.code\n\t\t\t\t\t\t\t\t\t}, function() {})\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\t//#endif\n\t\t\t\t\t} else if (app.globalData.platform == 'alipay') {\n\t\t\t\t\t\t//#ifdef MP-ALIPAY\n\t\t\t\t\t\tmy.getAuthCode({\n\t\t\t\t\t\t\tscopes: ['auth_base'],\n\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\tif (res.authCode) {\n\t\t\t\t\t\t\t\t\tapp.post('ApiIndex/setalipayopenid', {\n\t\t\t\t\t\t\t\t\t\tcode: res.authCode\n\t\t\t\t\t\t\t\t\t}, function() {})\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t});\n\t\t\t\t\t\t//#endif\n\t\t\t\t\t} else if (app.globalData.platform == 'baidu') {\n\t\t\t\t\t\t//#ifdef MP-BAIDU\n\t\t\t\t\t\tuni.getLoginCode({\n\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\tif (res.code) {\n\t\t\t\t\t\t\t\t\tapp.post('ApiIndex/setbaiduopenid', {\n\t\t\t\t\t\t\t\t\t\tcode: res.code\n\t\t\t\t\t\t\t\t\t}, function() {})\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: err => {\n\t\t\t\t\t\t\t\tconsole.log('getLoginCode fail', err);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\t//#endif\n\t\t\t\t\t} else if (app.globalData.platform == 'toutiao') {\n\t\t\t\t\t\t//#ifdef MP-TOUTIAO\n\t\t\t\t\t\ttt.login({\n\t\t\t\t\t\t\tforce: true,\n\t\t\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\t\t\tapp.post('ApiIndex/settoutiaoopenid', {\n\t\t\t\t\t\t\t\t\tcode: res.code\n\t\t\t\t\t\t\t\t}, function(res) {});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\t//#endif\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t},\n\t\t\t//启用socket连接\n\t\t\topenSocket: function() {\n\t\t\t\tconsole.log('openSocket');\n\t\t\t\t//#ifndef MP-TOUTIAO\n\t\t\t\tvar app = this;\n\t\t\t\tapp.globalData.socketOpen = false;\n\t\t\t\tuni.closeSocket();\n\t\t\t\tuni.connectSocket({\n\t\t\t\t\turl: (app.globalData.pre_url).replace('https://', \"wss://\") + '/wss'\n\t\t\t\t});\n\t\t\t\tapp.sendSocketMessage({\n\t\t\t\t\ttype: 'khinit',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taid: app.globalData.aid,\n\t\t\t\t\t\tmid: app.globalData.mid,\n\t\t\t\t\t\tplatform: app.globalData.platform\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tclearInterval(app.globalData.socketInterval);\n\t\t\t\tapp.globalData.socketInterval = setInterval(function() {\n\t\t\t\t\tapp.sendSocketMessage({\n\t\t\t\t\t\ttype: 'connect'\n\t\t\t\t\t});\n\t\t\t\t}, 25000);\n\t\t\t\t//#endif\n\t\t\t},\n\t\t\tsendSocketMessage: function(msg) {\n\t\t\t\tvar app = this;\n\t\t\t\tif (!msg.token) msg.token = this.globalData.socket_token;\n\t\t\t\tif (app.globalData.socketOpen) {\n\t\t\t\t\tconsole.log(msg);\n\t\t\t\t\tuni.sendSocketMessage({\n\t\t\t\t\t\tdata: JSON.stringify(msg),\n\t\t\t\t\t\tfail: function(res) {\n\t\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\t\tconsole.log('发送失败');\n\t\t\t\t\t\t\tif (app.globalData.socketConnecttimes < 1) {\n\t\t\t\t\t\t\t\tapp.globalData.socketConnecttimes++;\n\t\t\t\t\t\t\t\tapp.globalData.socketMsgQueue.push(msg);\n\t\t\t\t\t\t\t\tconsole.log('openSocket 重连');\n\t\t\t\t\t\t\t\tapp.openSocket();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tsuccess: function() {\n\t\t\t\t\t\t\tconsole.log('发送成功');\n\t\t\t\t\t\t\tapp.globalData.socketConnecttimes = 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tthis.globalData.socketMsgQueue.push(msg);\n\t\t\t\t}\n\t\t\t},\n\t\t\tchooseImage: async function(callback, count,otherParam=0) {\n\t\t\t\tvar app = this;\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\tuni.showActionSheet({\n\t\t\t\t\titemList: ['拍摄', '从相册选择'],\n\t\t\t\t\tsuccess: async function (res) {\n\t\t\t\t\t\tif(res.tapIndex){ //相册\n\t\t\t\t\t\t\tlet result2 =  await app.$store.dispatch(\"requestPermissions\",'WRITE_EXTERNAL_STORAGE');\n\t\t\t\t\t\t\tif (result2 !== 1) return;\n\t\t\t\t\t\t\tuni.chooseImage({\n\t\t\t\t\t\t\t\tcount: count || 1,\n\t\t\t\t\t\t\t\tsizeType: ['original', 'compressed'],\n\t\t\t\t\t\t\t\tsourceType: ['album'],\n\t\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\t\tvar tempFilePaths = res.tempFilePaths,\n\t\t\t\t\t\t\t\t\t\timageUrls = [];\n\t\t\t\t\t\t\t\t\tvar uploadednum = 0;\n\t\t\t\t\t\t\t\t\tfor (var i = 0; i < tempFilePaths.length; i++) {\n\t\t\t\t\t\t\t\t\t\timageUrls.push('');\n\t\t\t\t\t\t\t\t\t\tapp.showLoading('上传中');\n\t\t\t\t\t\t\t\t\t\tuni.uploadFile({\n\t\t\t\t\t\t\t\t\t\t\turl: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app\n\t\t\t\t\t\t\t\t\t\t\t\t.globalData.aid + '/platform/' + app.globalData.platform +\n\t\t\t\t\t\t\t\t\t\t\t\t'/session_id/' +\n\t\t\t\t\t\t\t\t\t\t\t\tapp.globalData.session_id+'/sortnum/'+i+'/other_param/'+otherParam,\n\t\t\t\t\t\t\t\t\t\t\tfilePath: tempFilePaths[i],\n\t\t\t\t\t\t\t\t\t\t\tname: 'file',\n\t\t\t\t\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t\t\t\t\t\tif(typeof res.data == 'string'){\n\t\t\t\t\t\t\t\t\t\t\t\t\t//兼容微信小程序\n\t\t\t\t\t\t\t\t\t\t\t\t\tvar data = JSON.parse(res.data);\n\t\t\t\t\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t\t\t\t\t//兼容百度小程序\n\t\t\t\t\t\t\t\t\t\t\t\t\tvar data = res.data;\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\tif (data.status == 1) {\n\t\t\t\t\t\t\t\t\t\t\t\t\tuploadednum++;\n\t\t\t\t\t\t\t\t\t\t\t\t\timageUrls[parseInt(data.sortnum)] = data.url;\n\t\t\t\t\t\t\t\t\t\t\t\t\tif (uploadednum == tempFilePaths.length) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttypeof callback == 'function' && callback(imageUrls);\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\t\t\tapp.alert(data.msg);\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\tfail: function(res) {\n\t\t\t\t\t\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t\t\t\t\t\tapp.alert(res.errMsg);\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail: function(res) {}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}else{ //拍摄\n\t\t\t\t\t\t\tlet result = await app.$store.dispatch(\"requestPermissions\",'CAMERA');\n\t\t\t\t\t\t\tif (result !== 1) return;\n\t\t\t\t\t\t\tuni.chooseImage({\n\t\t\t\t\t\t\t\tcount: count || 1,\n\t\t\t\t\t\t\t\tsizeType: ['original', 'compressed'],\n\t\t\t\t\t\t\t\tsourceType: ['camera'],\n\t\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\t\tvar tempFilePaths = res.tempFilePaths,\n\t\t\t\t\t\t\t\t\t\timageUrls = [];\n\t\t\t\t\t\t\t\t\tvar uploadednum = 0;\n\t\t\t\t\t\t\t\t\tfor (var i = 0; i < tempFilePaths.length; i++) {\n\t\t\t\t\t\t\t\t\t\timageUrls.push('');\n\t\t\t\t\t\t\t\t\t\tapp.showLoading('上传中');\n\t\t\t\t\t\t\t\t\t\tuni.uploadFile({\n\t\t\t\t\t\t\t\t\t\t\turl: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app\n\t\t\t\t\t\t\t\t\t\t\t\t.globalData.aid + '/platform/' + app.globalData.platform +\n\t\t\t\t\t\t\t\t\t\t\t\t'/session_id/' +\n\t\t\t\t\t\t\t\t\t\t\t\tapp.globalData.session_id+'/sortnum/'+i+'/other_param/'+otherParam,\n\t\t\t\t\t\t\t\t\t\t\tfilePath: tempFilePaths[i],\n\t\t\t\t\t\t\t\t\t\t\tname: 'file',\n\t\t\t\t\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t\t\t\t\t\tif(typeof res.data == 'string'){\n\t\t\t\t\t\t\t\t\t\t\t\t\t//兼容微信小程序\n\t\t\t\t\t\t\t\t\t\t\t\t\tvar data = JSON.parse(res.data);\n\t\t\t\t\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t\t\t\t\t//兼容百度小程序\n\t\t\t\t\t\t\t\t\t\t\t\t\tvar data = res.data;\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\tif (data.status == 1) {\n\t\t\t\t\t\t\t\t\t\t\t\t\tuploadednum++;\n\t\t\t\t\t\t\t\t\t\t\t\t\timageUrls[parseInt(data.sortnum)] = data.url;\n\t\t\t\t\t\t\t\t\t\t\t\t\tif (uploadednum == tempFilePaths.length) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttypeof callback == 'function' && callback(imageUrls);\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\t\t\tapp.alert(data.msg);\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\tfail: function(res) {\n\t\t\t\t\t\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t\t\t\t\t\tapp.alert(res.errMsg);\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail: function(res) {}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tfail: function (res) {\n\t\t\t\t\t\tconsole.log(res.errMsg);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef APP-PLUS\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: count || 1,\n\t\t\t\t\tsizeType: ['original', 'compressed'],\n\t\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tvar tempFilePaths = res.tempFilePaths,\n\t\t\t\t\t\t\timageUrls = [];\n\t\t\t\t\t\tvar uploadednum = 0;\n\t\t\t\t\t\tfor (var i = 0; i < tempFilePaths.length; i++) {\n\t\t\t\t\t\t\timageUrls.push('');\n\t\t\t\t\t\t\tapp.showLoading('上传中');\n\t\t\t\t\t\t\tuni.uploadFile({\n\t\t\t\t\t\t\t\turl: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app\n\t\t\t\t\t\t\t\t\t.globalData.aid + '/platform/' + app.globalData.platform +\n\t\t\t\t\t\t\t\t\t'/session_id/' +\n\t\t\t\t\t\t\t\t\tapp.globalData.session_id+'/sortnum/'+i+'/other_param/'+otherParam,\n\t\t\t\t\t\t\t\tfilePath: tempFilePaths[i],\n\t\t\t\t\t\t\t\tname: 'file',\n\t\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t\t\tif(typeof res.data == 'string'){\n\t\t\t\t\t\t\t\t\t\t//兼容微信小程序\n\t\t\t\t\t\t\t\t\t\tvar data = JSON.parse(res.data);\n\t\t\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\t\t\t//兼容百度小程序\n\t\t\t\t\t\t\t\t\t\tvar data = res.data;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tif (data.status == 1) {\n\t\t\t\t\t\t\t\t\t\tuploadednum++;\n\t\t\t\t\t\t\t\t\t\timageUrls[parseInt(data.sortnum)] = data.url;\n\n\t\t\t\t\t\t\t\t\t\tif (uploadednum == tempFilePaths.length) {\n\t\t\t\t\t\t\t\t\t\t\t//console.log(imageUrls);\n\t\t\t\t\t\t\t\t\t\t\ttypeof callback == 'function' && callback(imageUrls);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tapp.alert(data.msg);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail: function(res) {\n\t\t\t\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\t\t\t\tapp.alert(res.errMsg);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tfail: function(res) { //alert(res.errMsg);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tchooseFile: async function(callback, count) {\n\t\t\t\tvar app = this;\n\t\t\t\tvar up_url = app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id;\n\t\t\t\t// #ifdef H5\n\t\t\t\tuni.chooseFile({\n\t\t\t\t    count: 1, //默认100\n\t\t\t\t    success: function (res) {\n\t\t\t\t        console.log(res);\n\t\t\t\t        const tempFilePaths = res.tempFiles;\n\t\t\t\t        \n\t\t\t\t        //for (var i = 0; i < tempFilePaths.length; i++) {\n\t\t\t\t        \tapp.showLoading('上传中');\n\t\t\t\t            console.log(tempFilePaths[0]);\n\t\t\t\t        \tuni.uploadFile({\n\t\t\t\t        \t\turl: up_url,\n\t\t\t\t        \t\tfilePath: tempFilePaths[0]['path'],\n\t\t\t\t        \t\tname: 'file',\n\t\t\t\t        \t\tsuccess: function(res) {\n\t\t\t\t        \t\t\tapp.showLoading(false);\n\t\t\t\t        \t\t\tvar data = JSON.parse(res.data);\n\t\t\t\t        \t\t\tif (data.status == 1) {\n\t\t\t\t\t\t\t\t\t\t\t\ttypeof callback == 'function' && callback(data.url);\n\t\t\t\t        \t\t\t} else {\n\t\t\t\t        \t\t\t\tapp.alert(data.msg);\n\t\t\t\t        \t\t\t}\n\t\t\t\t        \t\t},\n\t\t\t\t        \t\tfail: function(res) {\n\t\t\t\t        \t\t\tapp.showLoading(false);\n\t\t\t\t        \t\t\tapp.alert(res.errMsg);\n\t\t\t\t        \t\t}\n\t\t\t\t        \t});\n\t\t\t\t        //}\n\t\t\t\t    }\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\twx.chooseMessageFile({\n\t\t\t\t  count: 1,\n\t\t\t\t  type: 'file',\n\t\t\t\t  success (res) {\n\t\t\t\t    const tempFilePaths = res.tempFiles\n\t\t\t\t    console.log(tempFilePaths);\n\t\t\t\t    \tapp.showLoading('上传中');\n\t\t\t\t    \tuni.uploadFile({\n\t\t\t\t    \t\turl: up_url,\n\t\t\t\t    \t\tfilePath: tempFilePaths[0]['path'],\n\t\t\t\t    \t\tname: 'file',\n\t\t\t\t    \t\tsuccess: function(res) {\n\t\t\t\t    \t\t\tapp.showLoading(false);\n\t\t\t\t    \t\t\tvar data = JSON.parse(res.data);\n\t\t\t\t    \t\t\tif (data.status == 1) {\n\t\t\t\t\t\t\t\t\t\t\ttypeof callback == 'function' && callback(data.url);\n\t\t\t\t    \t\t\t} else {\n\t\t\t\t    \t\t\t\tapp.alert(data.msg);\n\t\t\t\t    \t\t\t}\n\t\t\t\t    \t\t},\n\t\t\t\t    \t\tfail: function(res) {\n\t\t\t\t    \t\t\tapp.showLoading(false);\n\t\t\t\t    \t\t\tapp.alert(res.errMsg);\n\t\t\t\t    \t\t}\n\t\t\t\t    \t});\n\t\t\t\t    //}\n\t\t\t\t  },\n\t\t\t\t  complete(res){\n\t\t\t\t      console.log(res)\n\t\t\t\t  }\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// distance 距离为m\n\t\t\tgetMapZoom:function(distance){\n\t\t\t\tlet zoom = 10;\n\t\t\t\tdistance = Number(distance)\n\t\t\t\tif (0 <= distance && distance <= 25) {\n\t\t\t\t\tzoom = 16\n\t\t\t\t\treturn zoom;\n\t\t\t\t} else if (25 < distance && distance <= 50) {\n\t\t\t\t\tzoom = 16\n\t\t\t\t\treturn zoom;\n\t\t\t\t} else if (50 < distance && distance <= 100) {\n\t\t\t\t\tzoom = 16\n\t\t\t\t\treturn zoom;\n\t\t\t\t} else if (100 < distance && distance <= 200) {\n\t\t\t\t\tzoom = 15\n\t\t\t\t\treturn zoom;\n\t\t\t\t} else if (200 < distance && distance <= 500) {\n\t\t\t\t\tzoom = 15\n\t\t\t\t\treturn zoom;\n\t\t\t\t} else if (500 < distance && distance <= 1000) {\n\t\t\t\t\tzoom = 15\n\t\t\t\t\treturn zoom;\n\t\t\t\t} else if (1000 < distance && distance <= 2000) {\n\t\t\t\t\tzoom = 13\n\t\t\t\t\treturn zoom;\n\t\t\t\t} else if (2000 < distance && distance <= 5000) {\n\t\t\t\t\tzoom = 13\n\t\t\t\t\treturn zoom;\n\t\t\t\t} else if (5000 < distance && distance <= 10000) {\n\t\t\t\t\tzoom = 11\n\t\t\t\t\treturn zoom;\n\t\t\t\t} else if (10000 < distance && distance <= 20000) {\n\t\t\t\t\tzoom = 10\n\t\t\t\t\treturn zoom;\n\t\t\t\t} else if (20000 < distance && distance <= 30000) {\n\t\t\t\t\tzoom = 9\n\t\t\t\t\treturn zoom;\n\t\t\t\t} else if (30000 < distance && distance <= 50000) {\n\t\t\t\t\tzoom = 9\n\t\t\t\t\treturn zoom;\n\t\t\t\t} else if (50000 < distance && distance <= 100000) {\n\t\t\t\t\tzoom = 8\n\t\t\t\t\treturn zoom;\n\t\t\t\t} else if (100000 < distance && distance <= 200000) {\n\t\t\t\t\tzoom = 7\n\t\t\t\t\treturn zoom;\n\t\t\t\t} else if (200000 < distance && distance <= 400000) {\n\t\t\t\t\tzoom = 6\n\t\t\t\t\treturn zoom;\n\t\t\t\t} else if (400000 < distance && distance <= 1000000) {\n\t\t\t\t\tzoom = 5\n\t\t\t\t\treturn zoom;\n\t\t\t\t}\n\t\t\t\treturn 10;\n\t\t\t},\n\t\t\tgetLocation: async function(callback1, callback2) {\n\t\t\t\tvar that = this;\n\t\t\t\tif (this.globalData.platform == 'mp') {\n\t\t\t\t\t//#ifdef H5\n\t\t\t\t\tvar jweixin = require('jweixin-module');\n\t\t\t\t\tjweixin.ready(function() {\n\t\t\t\t\t\tjweixin.getLocation({\n\t\t\t\t\t\t\ttype: 'gcj02',\n\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\tthat.setCache('getLocationCatch', res);\n\t\t\t\t\t\t\t\ttypeof callback1 == 'function' && callback1(res);\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: function(res) {\n\t\t\t\t\t\t\t\tvar locationCatch = that.getCache('getLocationCatch');\n\t\t\t\t\t\t\t\tif (locationCatch) {\n\t\t\t\t\t\t\t\t\ttypeof callback1 == 'function' && callback1(locationCatch);\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\ttypeof callback2 == 'function' && callback2(res);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t\t//#endif\n\t\t\t\t} else if (this.globalData.platform == 'alipay') {\n\t\t\t\t\t//#ifdef MP-ALIPAY\n\t\t\t\t\tuni.getLocation({\n\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\tthat.setCache('getLocationCatch', res);\n\t\t\t\t\t\t\ttypeof callback1 == 'function' && callback1(res);\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: function(res) {\n\t\t\t\t\t\t\tvar locationCatch = that.getCache('getLocationCatch');\n\t\t\t\t\t\t\tif (locationCatch) {\n\t\t\t\t\t\t\t\ttypeof callback1 == 'function' && callback1(locationCatch);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\ttypeof callback2 == 'function' && callback2(res);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(res.extErrorCode == 2001 || res.extErrorCode == 2002 || res.extErrorCode == 2003){}else{\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\t\tcontent: '请在系统设置中打开定位服务',\n\t\t\t\t\t\t\t\t\tsuccess: function (res) {\n\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t//#endif\n\t\t\t\t} else {\n\t\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t\t\tlet result = await this.$store.dispatch(\"requestPermissions\",'ACCESS_FINE_LOCATION')\n\t\t\t\t\t\tif (result !== 1) return;\n\t\t\t\t\t// #endif\n\t\t\t\t\tuni.getLocation({\n\t\t\t\t\t\ttype: 'gcj02',\n\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\tthat.setCache('getLocationCatch', res);\n\t\t\t\t\t\t\ttypeof callback1 == 'function' && callback1(res);\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: function(res) {\n\t\t\t\t\t\t\tvar locationCatch = that.getCache('getLocationCatch');\n\t\t\t\t\t\t\tif (locationCatch) {\n\t\t\t\t\t\t\t\ttypeof callback1 == 'function' && callback1(locationCatch);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\ttypeof callback2 == 'function' && callback2(res);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tlet showModalTitle = ''\n\t\t\t\t\t\t\tif(res.errMsg == 'getLocation:fail auth deny' || res.errMsg =='getLocation:fail:auth denied' || res.errMsg == 'getLocation:fail authorize no response'){\n\t\t\t\t\t\t\t\t// 用户取消定位\n\t\t\t\t\t\t\t}else\tif(res.errMsg == 'getLocation:fail system permission denied' || res.errMsg == 'getLocation:fail:system permission denied' || res.errMsg == 'getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF'){\n\t\t\t\t\t\t\t\tshowModalTitle='请在系统设置中打开定位服务'\n\t\t\t\t\t\t\t}else\tif(res.errMsg == 'getLocation:fail:ERROR_NETWORK'){\n\t\t\t\t\t\t\t\tshowModalTitle = '获取位置信息失败，网络异常'\n\t\t\t\t\t\t\t}else\tif(res.errMsg == 'getLocation:fail:timeout'){\n\t\t\t\t\t\t\t\tshowModalTitle = '获取位置信息失败，定位超时'\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\tshowModalTitle = '获取位置信息失败'+res.errMsg\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(showModalTitle){\n\t\t\t\t\t\t\t\t// #ifndef APP || H5\n\t\t\t\t\t\t\t\tif(showModalTitle == '请在系统设置中打开定位服务'){\n\t\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\t\t\tcontent: showModalTitle,\n\t\t\t\t\t\t\t\t\t\tconfirmText:'去设置',\n\t\t\t\t\t\t\t\t\t\tsuccess: function (res) {\n\t\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\t\t\t\t\tuni.openSetting({\n\t\t\t\t\t\t\t\t\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tconsole.log(res.authSetting)\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t\t\tshowModalTitle ='';\n\t\t\t\t\t\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\t\t\t\t\t\tshowModalTitle ='';\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\t\tcontent: showModalTitle,\n\t\t\t\t\t\t\t\t\tsuccess: function (res) {\n\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\t\t\t\tshowModalTitle ='';\n\t\t\t\t\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\t\t\t\t\tshowModalTitle ='';\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tgetLocationCache:function(key=''){\n\t\t\t\tvar app = this;\n\t\t\t\tvar locationCache = app.getCache('locationCache');\n\t\t\t\tif(locationCache){\n\t\t\t\t\tif(key){\n\t\t\t\t\t\treturn locationCache[key]\n\t\t\t\t\t}else{\n\t\t\t\t\t\treturn locationCache\n\t\t\t\t\t}\n\t\t\t\t}else{\n\t\t\t\t\tif(key){\n\t\t\t\t\t\treturn  '';\n\t\t\t\t\t}else{\n\t\t\t\t\t\treturn {}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tsetLocationCache:function(key='',val='',expireTime=0){\n\t\t\t\tvar app = this;\n\t\t\t\tvar locationCache = app.getCache('locationCache');\n\t\t\t\tif(!locationCache){\n\t\t\t\t\tlocationCache = {}\n\t\t\t\t\tlocationCache[key] = val\n\t\t\t\t}else{\n\t\t\t\t\tlocationCache[key] = val\n\t\t\t\t}\n\t\t\t\tapp.setCache('locationCache',locationCache,expireTime)\n\t\t\t},\n\t\t\tsetLocationCacheData:function(data,expireTime=0){\n\t\t\t\tvar app = this;\n\t\t\t\tapp.setCache('locationCache',data,expireTime)\n\t\t\t},\n\t\t\t//expire_time过期分钟数\n\t\t\tsetCache: function(key, value, expire_time) {\n\t\t\t\t//如果设置了过期时间，则读取的时候 如果过期，就清除expire_time 过期分钟数\n\t\t\t\tif(!expire_time){\n\t\t\t\t\texpire_time = 0;\n\t\t\t\t}\n\t\t\t\tvar expire_time = parseInt(expire_time);\n\t\t\t\tif(expire_time>0){\n\t\t\t\t\tvar curtime = new Date().getTime()\n\t\t\t\t\tuni.setStorageSync(key+'_expiretime_second', expire_time*60*1000);\n\t\t\t\t\tuni.setStorageSync(key+'_expiretime', curtime + (expire_time*60*1000));\n\t\t\t\t}\n\t\t\t\treturn uni.setStorageSync(key, value);\n\t\t\t},\n\t\t\tgetCache: function(key) {\n\t\t\t\tvar app = this;\n\t\t\t\tvar cacheVal = uni.getStorageSync(key);\n\t\t\t\tif(cacheVal){\n\t\t\t\t\t//是不是设置了过期时间：没设置直接返回，设置了判断是否过期\n\t\t\t\t\tvar expirekey = key+'_expiretime';\n\t\t\t\t\tvar expiretime = uni.getStorageSync(expirekey);\n\t\t\t\t\tif(expiretime){\n\t\t\t\t\t\texpiretime = parseInt(expiretime)\n\t\t\t\t\t\t//当前时间戳\n\t\t\t\t\t\tvar curtime = new Date().getTime()\n\t\t\t\t\t\t// console.log('ct:'+curtime)\n\t\t\t\t\t\t// console.log('et:'+expiretime)\n\t\t\t\t\t\t//判断是不是过期\n\t\t\t\t\t\tif(expiretime<curtime){\n\t\t\t\t\t\t\t//过期了 清掉\n\t\t\t\t\t\t\t// console.log('过期')\n\t\t\t\t\t\t\tapp.removeCache(key)\n\t\t\t\t\t\t\tapp.removeCache(expirekey)\n\t\t\t\t\t\t\treturn '';\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t// console.log('未过期')\n\t\t\t\t\t\t\tvar cacheSecond = parseInt(uni.getStorageSync(key+'_expiretime_second'));\n\t\t\t\t\t\t\tapp.setCache(expirekey,curtime+cacheSecond);\n\t\t\t\t\t\t\treturn uni.getStorageSync(key);\n\t\t\t\t\t\t}\n\t\t\t\t\t}else{\n\t\t\t\t\t\treturn cacheVal;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn cacheVal;\n\t\t\t},\n\t\t\tremoveCache: function(key) {\n\t\t\t\tvar $ = this;\n\t\t\t\tif ($.isNull(key)) {\n\t\t\t\t\tuni.clearStorageSync();\n\t\t\t\t} else {\n\t\t\t\t\tuni.removeStorageSync(key);\n\t\t\t\t\tuni.removeStorageSync(key+'_expiretime');\n\t\t\t\t}\n\t\t\t},\n\t\t\t_url: function() {\n\t\t\t\t//获取当前页url\n\t\t\t\tvar pages = getCurrentPages(); //获取加载的页面\n\t\t\t\tvar currentPage = pages[pages.length - 1]; //获取当前页面的对象\n\t\t\t\tvar url = '/' + (currentPage.route ? currentPage.route : currentPage.__route__); //当前页面url \n\t\t\t\treturn url;\n\t\t\t},\n\t\t\t_fullurl: function() {\n\t\t\t\tvar pages = getCurrentPages(); //获取加载的页面\n\t\t\t\tvar currentPage = pages[pages.length - 1]; //获取当前页面的对象\n\t\t\t\tif(currentPage.__page__ && currentPage.__page__.fullPath) return currentPage.__page__.fullPath;\n\t\t\t\tif(currentPage.$page && currentPage.$page.fullPath) return currentPage.$page.fullPath;\n\t\t\t\tvar url = '/' + (currentPage.route ? currentPage.route : currentPage.__route__); //当前页面url \n\t\t\t\tif (this.globalData.platform == 'baidu') {\n\t\t\t\t\tvar opts = currentPage.options;\n\t\t\t\t} else {\n\t\t\t\t\tvar opts = currentPage.$vm.opt;\n\t\t\t\t}\n\t\t\t\tconsole.log(opts)\n\t\t\t\tvar params = [];\n\t\t\t\tfor (var i in opts) {\n\t\t\t\t\tparams.push(i + '=' + opts[i]);\n\t\t\t\t}\n\t\t\t\tif (params.length > 0) {\n\t\t\t\t\turl += '?' + params.join('&');\n\t\t\t\t}\n\t\t\t\tconsole.log(url)\n\t\t\t\treturn url;\n\t\t\t},\n\t\t\tcheckUpdateVersion() {\n\t\t\t\tif (wx.canIUse('getUpdateManager')) {\n\t\t\t\t\t//创建 UpdateManager 实例\n\t\t\t\t\tconst updateManager = wx.getUpdateManager();\n\t\t\t\t\t//console.log('是否进入模拟更新');\n\t\t\t\t\t//检测版本更新\n\t\t\t\t\tupdateManager.onCheckForUpdate(function(res) {\n\t\t\t\t\t\t//console.log('是否获取版本');\n\t\t\t\t\t\t// 请求完新版本信息的回调\n\t\t\t\t\t\tif (res.hasUpdate) {\n\t\t\t\t\t\t\t//监听小程序有版本更新事件\n\t\t\t\t\t\t\tupdateManager.onUpdateReady(function() {\n\t\t\t\t\t\t\t\t//TODO 新的版本已经下载好，调用 applyUpdate 应用新版本并重启 （ 此处进行了自动更新操作）\n\t\t\t\t\t\t\t\tupdateManager.applyUpdate();\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\tupdateManager.onUpdateFailed(function() {\n\t\t\t\t\t\t\t\t// 新版本下载失败\n\t\t\t\t\t\t\t\t//wx.showModal({\n\t\t\t\t\t\t\t\t//\ttitle: '已经有新版本喽~',\n\t\t\t\t\t\t\t\t//\tcontent: '请您删除当前小程序，到微信 “发现-小程序” 页，重新搜索打开哦~',\n\t\t\t\t\t\t\t\t//})\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t} else {\n\t\t\t\t\t//TODO 此时微信版本太低（一般而言版本都是支持的）\n\t\t\t\t\t//wx.showModal({\n\t\t\t\t\t//  title: '溫馨提示',\n\t\t\t\t\t//  content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。'\n\t\t\t\t\t//})\n\t\t\t\t}\n\t\t\t},\n      copy:function(data){\n        if(data){\n          uni.setClipboardData({\n          \tdata: data,\n            showToast:false\n          });\n        }\n      },\n\t\t\t// 防抖\n\t\t\tDebounce:function(fn,t){\n\t\t\t\tconst delay = t || 500\n\t\t\t\tlet timer\n\t\t\t\treturn function() {\n\t\t\t\t\tconst args = arguments\n\t\t\t\t\tif (timer) {\n\t\t\t\t\t\tclearTimeout(timer)\n\t\t\t\t\t}\n\t\t\t\t\ttimer = setTimeout(() => {\n\t\t\t\t\t\ttimer = null\n\t\t\t\t\t\tfn.apply(this, args)\n\t\t\t\t\t}, delay)\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 节流\n\t\t\tThrottle:function(fn, t){\n\t\t\t\tlet last\n\t\t\t\tlet timer\n\t\t\t\tconst interval = t || 500\n\t\t\t\treturn function() {\n\t\t\t\t\tconst args = arguments\n\t\t\t\t\tconst now = +new Date()\n\t\t\t\t\tif (last && now - last < interval) {\n\t\t\t\t\t\tclearTimeout(timer)\n\t\t\t\t\t\ttimer = setTimeout(() => {\n\t\t\t\t\t\t\tlast = now\n\t\t\t\t\t\t\tfn.apply(this, args)\n\t\t\t\t\t\t}, interval)\n\t\t\t\t\t} else {\n\t\t\t\t\t\tlast = now\n\t\t\t\t\t\tfn.apply(this, args)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t};\n</script>\n<style>\n\t@import \"./common.css\";\n\t@import \"./iconfont.css\";\n\t@import '@/components/parse/parse.css';\n</style>\n", "import mod from \"-!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213002271\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}