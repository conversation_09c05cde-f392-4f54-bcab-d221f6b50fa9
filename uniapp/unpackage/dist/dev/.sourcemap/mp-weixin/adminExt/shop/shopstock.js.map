{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/shop/shopstock.vue?178f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/shop/shopstock.vue?0c62", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/shop/shopstock.vue?e2b1", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/shop/shopstock.vue?ca76", "uni-app:///adminExt/shop/shopstock.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/shop/shopstock.vue?d3b6", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/shop/shopstock.vue?9bc0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "loading", "nodata", "mid", "pre_url", "prodata", "payTypeArr", "payTypeIndex", "paytype", "dialogShow", "onSharelink", "navigationMenu", "platform", "items", "orderNotes", "buydata", "onLoad", "onShow", "methods", "goBack", "app", "wxNavigationBarMenu", "clearShopCartFn", "that", "uni", "title", "content", "success", "cartid", "shareBut", "showdialog", "inputNum", "inputPrice", "inputTotalPrice", "computetotalprice", "getdatacart", "thisprodata", "addshop", "tosubmit", "proid", "ggid", "buynum", "buytotal", "prodataIdArr"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAAu0B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyE31B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAEA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EAEAC;IACAC;MACAC;IACA;IACAC;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACAC;MACAC;QACAC;QACAC;QACAC;UACA;YAEAP;cAAAQ;YAAA;cACAL;cACAA;YACA;UACA,wBACA;QACA;MACA;IACA;IACAM;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA5B;MACA;IACA;IACA6B;MACA;MACA;MACA;MACA;MACA;MACA7B;MACA;IACA;IACA8B;MACA;MACAZ;MACAH;QACAG;QACAA;QAEA;UACA;UACAa;QACA;QACA;UACAb;QACA;MACA;IACA;IACAc;MACAjB;IACA;IACA;IACAkB;MACA;MACA;MACA;MACA;MACA;QACA;QACA;UACAlB;UACA;QACA;QACA;UACAA;UACA;QACA;QACA;UACAmB;UACAC;UACAC;UACAC;QACA;QACAC;MACA;MACAvB;MACAA;QACAf;MACA;QACAe;QACA;UACA;UACAA;UACA;QACA;UACAA;UACAG;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrOA;AAAA;AAAA;AAAA;AAAorC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACAxsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "adminExt/shop/shopstock.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './adminExt/shop/shopstock.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./shopstock.vue?vue&type=template&id=d56c1f74&\"\nvar renderjs\nimport script from \"./shopstock.vue?vue&type=script&lang=js&\"\nexport * from \"./shopstock.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shopstock.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"adminExt/shop/shopstock.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopstock.vue?vue&type=template&id=d56c1f74&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var m2 = _vm.isload ? _vm.t(\"color1\") : null\n  var m3 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopstock.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopstock.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view >\r\n\t\t<!-- #ifndef H5 -->\r\n\t\t<view class=\"navigation\">\r\n\t\t\t<view class='navcontent' :style=\"{marginTop:navigationMenu.top+'px',width:(navigationMenu.right)+'px'}\">\r\n\t\t\t\t<view class=\"header-location-top\" :style=\"{height:navigationMenu.height+'px'}\">\r\n\t\t\t\t\t<view class=\"header-back-but\" @click=\"goBack\">\r\n\t\t\t\t\t\t<image  :src=\"pre_url+'/static/img/admin/goback.png'\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"header-page-title\">录入库存</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t\t<view class=\"content\" v-if=\"isload\">\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<view class=\"title-view flex-y-center\">\r\n\t\t\t\t\t<view>商品列表</view>\r\n\t\t\t\t\t<view class=\"but-class\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @click=\"addshop\">添加商品</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"product\">\r\n\t\t\t\t\t<view v-for=\"(item, index2) in prodata\" :key=\"index2\">\r\n\t\t\t\t\t\t<view class=\"item flex\">\r\n\t\t\t\t\t\t\t<!-- @tap=\"goto\" :data-url=\"'/pages/shop/product?id=' + item.product.id\" -->\r\n\t\t\t\t\t\t\t<view class=\"img\">\r\n\t\t\t\t\t\t\t\t<image v-if=\"item.guige.pic\" :src=\"item.guige.pic\"></image>\r\n\t\t\t\t\t\t\t\t<image v-else :src=\"item.product.pic\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"info flex1\">\r\n\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.product.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"f2\">规格：{{item.guige.name}}</view>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<view class=\"f3\">\r\n\t\t\t\t\t\t\t\t\t<!-- <block><text style=\"font-weight:bold;\">￥{{item.guige.sell_price}}</text></block> -->\r\n\t\t\t\t\t\t\t\t\t<!-- <text style=\"padding-left:20rpx\"> × {{item.num}}</text> -->\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"del-view flex-y-center\" @tap.stop=\"clearShopCartFn(item.id)\" style=\"color:#999999;font-size:24rpx\">\r\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/del.png'\" style=\"width:24rpx;height:24rpx;margin-right:6rpx\"/>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view> \r\n\t\t\t\t\t\t<view class=\"flex flex-y-center sb\">\r\n\t\t\t\t\t\t\t<view class=\"modify-price flex-y-center\">\r\n\t\t\t\t\t\t\t\t<input type=\"digit\" placeholder=\"录入数量\" :value=\"item.num\" class=\"inputPrice\" @input=\"inputNum($event,index2)\">\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t * \r\n\t\t\t\t\t\t\t<view class=\"modify-price flex-y-center\">\r\n\t\t\t\t\t\t\t\t<input type=\"digit\" placeholder=\"进货单价\" :value=\"item.guige.sell_price\"  class=\"inputPrice\" @input=\"inputPrice($event,index2)\">\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t=\r\n\t\t\t\t\t\t\t<view class=\"modify-price flex-y-center\">\r\n\t\t\t\t\t\t\t\t<input type=\"digit\" placeholder=\"总价\" :value=\"item.buytotal\"  class=\"inputPrice\" @input=\"inputTotalPrice($event,index2)\">\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<nodata v-if=\"nodata\" :text=\"'请添加需要操作的商品'\"></nodata>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"width: 100%; height:182rpx;\"></view>\r\n\t\t\t<view class=\"footer flex notabbarbot\">\r\n\r\n\t\t\t\t<button class=\"op\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @click=\"tosubmit\">\r\n\t\t\t\t\t确定</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tconst app = getApp();\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn{\r\n\t\t\t\tisload:false,\r\n\t\t\t\tloading:false,\r\n\t\t\t\tnodata:false,\r\n\t\t\t\tmid:'',\r\n\t\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\tprodata:[],\r\n\t\t\t\tpayTypeArr: [],\r\n\t\t\t\tpayTypeIndex:0,\r\n\t\t\t\tpaytype:'',\r\n\t\t\t\tdialogShow:false,\r\n\t\t\t\tonSharelink:'',\r\n\t\t\t\tnavigationMenu:{},\r\n\t\t\t\tplatform: app.globalData.platform,\r\n\t\t\t\titems:[],\r\n\t\t\t\torderNotes:'',\r\n\t\t\t\tbuydata:{},\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(opt) {\r\n\t\t\tlet that = this;\r\n\t\t\tthis.getdatacart();\r\n\t\t\tvar sysinfo = uni.getSystemInfoSync();\r\n\t\t\tthis.statusBarHeight = sysinfo.statusBarHeight;\r\n\t\t\tthis.wxNavigationBarMenu();\r\n\t\t\tthis.isload = true;\r\n\t\t},\r\n\t\tonShow(){\r\n\t\t\tlet that = this;\r\n\t\t\tthis.getdatacart();\r\n\t\t},\r\n\t\t\r\n\t\tmethods:{\r\n\t\t\tgoBack(){\r\n\t\t\t\tapp.goto('/adminCore/index/index','reLaunch')\r\n\t\t\t},\r\n\t\t\twxNavigationBarMenu:function(){\r\n\t\t\t\tif(this.platform=='wx'){\r\n\t\t\t\t\t//胶囊菜单信息\r\n\t\t\t\t\tthis.navigationMenu = wx.getMenuButtonBoundingClientRect()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tclearShopCartFn: function (id) {\r\n\t\t\t  var that = this;\r\n\t\t\t  that.loading = true;\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '确认删除该商品吗？',\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tapp.post(\"ApiAdminOrderlr/cartdelete\", {cartid:id}, function (res) {\r\n\t\t\t\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\t\t  that.getdatacart(that.mid)\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshareBut(){\r\n\t\t\t\tthis.dialogShow = false;\r\n\t\t\t},\r\n\t\t\tshowdialog(){\r\n\t\t\t\tthis.dialogShow = !this.dialogShow;\r\n\t\t\t},\r\n\t\t\tinputNum(event,index){\r\n\t\t\t\tthis.prodata[index].num = event.detail.value;\r\n\t\t\t\tthis.computetotalprice(index);\r\n\t\t\t},\r\n\t\t\tinputPrice(event,index){\r\n\t\t\t\tthis.prodata[index].guige.sell_price = event.detail.value;\r\n\t\t\t\tthis.computetotalprice(index);\r\n\t\t\t},\r\n\t\t\tinputTotalPrice(event,index){\r\n\t\t\t\tthis.prodata[index].buytotal = event.detail.value;\t\r\n\t\t\t\tlet prodata = this.prodata\r\n\t\t\t\tthis.prodata = [];\r\n\t\t\t\tvar totalprice = prodata[index].buytotal;\r\n\t\t\t\tvar buynum = prodata[index].num;\r\n\t\t\t\tvar sell_price = parseFloat( totalprice / buynum).toFixed(2);\r\n\t\t\t\tprodata[index].guige.sell_price =sell_price;\r\n\t\t\t\tthis.prodata = prodata;\r\n\t\t\t},\r\n\t\t\tcomputetotalprice(index){\r\n\t\t\t\tvar buynum = this.prodata[index].num;\r\n\t\t\t\tvar sell_price = this.prodata[index].guige.sell_price;\r\n\t\t\t\tvar buyprice = parseFloat( buynum * sell_price).toFixed(2);\r\n\t\t\t\tlet prodata = this.prodata;\r\n\t\t\t\t this.prodata = [];\r\n\t\t\t\tprodata[index].buytotal =buyprice;\r\n\t\t\t\tthis.prodata = prodata;\r\n\t\t\t},\r\n\t\t\tgetdatacart(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post('ApiAdminOrderlr/cart', {}, function (res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.prodata = res.cartlist;\r\n\r\n\t\t\t\t\tfor (var i = 0; i < that.prodata.length; i++) {\r\n\t\t\t\t\t\tvar thisprodata = that.prodata[i];\r\n\t\t\t\t\t\tthisprodata.buytotal = parseFloat( thisprodata.num * thisprodata.guige.sell_price).toFixed(2);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.prodata.length <=0){\r\n\t\t\t\t\t\tthat.nodata = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\taddshop(){\r\n\t\t\t\tapp.goto('shopstockgoods'  )\r\n\t\t\t},\r\n\t\t\t//提交代付款订单 \r\n\t\t\ttosubmit: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar prodata = that.prodata;\t\r\n\t\t\t\tif(!that.prodata.length) return app.error('请添加商品');\r\n\t\t\t\tvar prodataIdArr = [];\r\n\t\t\t\tfor (var i = 0; i < prodata.length; i++) {\r\n\t\t\t\t\tvar thisprodata = prodata[i];\r\n\t\t\t\t\tif(thisprodata.num <=0 || thisprodata.num ==undefined){\r\n\t\t\t\t\t\tapp.error('检查录入数量');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(thisprodata.guige.sell_price <=0 || thisprodata.guige.sell_price ==undefined){\r\n\t\t\t\t\t\tapp.error('检查货品单价');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar buydata = {\r\n\t\t\t\t\t\tproid:thisprodata.product.id,\r\n\t\t\t\t\t\tggid:thisprodata.guige.id,\r\n\t\t\t\t\t\tbuynum:thisprodata.num,\r\n\t\t\t\t\t\tbuytotal:thisprodata.buytotal,\r\n\t\t\t\t\t}\r\n\t\t\t\t  prodataIdArr.push(buydata);\r\n\t\t\t\t}\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiAdminOrderlr/shopsotckSave', {\r\n\t\t\t\t\tprodata:prodataIdArr,\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\t//that.showsuccess(res.data.msg);\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\t\tthat.getdatacart();\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* #ifdef H5 */\r\n\t/deep/ .input-value{padding: 0px 0px !important;color:#626262 !important;font-size:28rpx;}\r\n\t/deep/ .placeholder{color:#626262 !important;font-size:28rpx;}\r\n\t/* #endif */\r\n\t.headimg-mendian image{ width: 100rpx; height:100rpx; border-radius:10rpx;margin-right: 20rpx;}\r\n\t.data-v-31ccf324{padding: 0px 0px !important;color:#626262 !important;font-size:28rpx;}\r\n\t.content{width: 95%;margin: 0 auto;}\r\n\t.item{width: 100%;border-radius: 12rpx;background: #fff;margin-top: 20rpx;padding: 15rpx;justify-content: space-between;}\r\n\t.itemfirst{width: 100%;height:120rpx;margin-top: 20rpx;justify-content: space-between;}\r\n\t.itemfirst-options{width: 47%;height: 100%;border-radius: 12rpx;background: #fff;justify-content: space-between;padding: 0rpx 15rpx;}\r\n\t.avat-img-view {width: 80rpx;height:80rpx;border-radius: 50%;overflow: hidden;}\r\n\t.avat-img-view image{width: 100%;height: 100%;}\r\n\t.title-view{justify-content: space-between;padding: 10rpx 0rpx 20rpx 0;border-bottom: 2rpx solid #eeeeee}\r\n\t.title-view .but-class{width: 150rpx;height: 50rpx;line-height: 50rpx;color: #fff;text-align: center;font-size: 24rpx;border-radius:35rpx}\r\n\t\r\n\t.user-info{margin-left: 20rpx;}\r\n\t.item .user-info .un-text{font-size: 28rpx;color: rgba(34, 34, 34, 0.7);}\r\n\t.item .user-info .tel-text{font-size: 26rpx;color: rgba(34, 34, 34, 0.7);margin-top: 5rpx;}\r\n\t.jiantou-img{width: 24rpx;height: 24rpx;}\r\n\t.jiantou-img image{width: 100%;height: 100%;}\r\n\t.input-view{padding: 10rpx 0rpx;margin-bottom: 10rpx;}\r\n\t.input-view .picker-paytype{display: flex;align-items: center;justify-content: space-between;}\r\n\t.picker-class{width: 500rpx;}\r\n\t.input-view .input-title{width: 150rpx;white-space: nowrap;}\r\n\t.input-view .but-class{width: 100rpx;height: 50rpx;line-height: 50rpx;color: #fff;text-align: center;font-size: 24rpx;border-radius:35rpx}\r\n\t.address-view{display: flex;align-items: flex-start;}\r\n\t.address-chose{justify-content: space-between;width: 540rpx;display: flex;align-items: flex-start;}\r\n\t.address-plac{color:#626262;font-size:28rpx;}\r\n\t.product {width: 100%;}\r\n\t.product .item {position: relative;width: 100%;padding: 20rpx 0 0rpx 0;background: #fff;}\r\n\t.product .del-view{position: absolute;right: 10rpx;top: 50%;margin-top: -7px;padding: 10rpx;}\r\n\t.product .info {padding-left: 20rpx;}\r\n\t.product .info .f1 {color: #222222;font-weight: bold;font-size: 26rpx;line-height: 36rpx;margin-bottom: 10rpx;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;width: 90%;}\r\n\t.product .info .f2 {color: #999999;font-size: 24rpx}\r\n\t.product .info .f3 {color: #FF4C4C;font-size: 28rpx;display: flex;align-items: center;margin-top: 10rpx}\r\n\t\r\n\t.product  .modify-price{padding: 10rpx 0rpx;margin-right:10rpx}\r\n\t\r\n\t.product image {width: 140rpx;height: 140rpx}\r\n\t\r\n\t.inputPrice {border: 1px solid #ddd; width: 180rpx; height: 60rpx; border-radius: 10rpx; padding: 0 10rpx;}\r\n\t.footer {width: 96%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;padding:20rpx 2% 0 2%;display: flex;align-items: center;z-index: 8;box-sizing:content-box}\r\n\t.footer .text1 {height: 110rpx;line-height: 110rpx;color: #2a2a2a;font-size: 30rpx;}\r\n\t.footer .text1 text {color: #e94745;font-size: 32rpx;}\r\n\t.footer .op {width: 200rpx;height: 80rpx;line-height: 80rpx;color: #fff;text-align: center;font-size: 30rpx;border-radius: 44rpx}\r\n\t.footer .op[disabled] { background: #aaa !important; color: #666;}\r\n\t\r\n\t.navigation {width: 100%;padding-bottom:10px;overflow: hidden;}\r\n\t.navcontent {display: flex;align-items: center;padding-left: 10px;}\r\n\t.header-location-top{position: relative;display: flex;justify-content: center;align-items: center;flex:1;}\r\n\t.header-back-but{position: absolute;left:12rpx;display: flex;align-items: center;width: 35rpx;height: 35rpx;overflow: hidden;}\r\n\t.header-back-but image{width: 17rpx;height: 31rpx;} \r\n\t.header-page-title{display: flex;flex: 1;align-items: center;justify-content: center;font-size: 34rpx;letter-spacing: 2rpx;}\r\n\t.del-view{color:#999999;font-size:24rpx}\r\n\t.del-view image{width:24rpx;height:24rpx;margin-right:6rpx}\r\n\t.buyprice{height: 65rpx;line-height: 65rpx;color: #FF4C4C;font-weight: bold;    width: 45%;\r\n    text-align: left;}\r\n\t.sb{justify-content: space-between;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopstock.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopstock.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213038293\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}