{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/hotel/orderdetail.vue?4b9b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/hotel/orderdetail.vue?9e3c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/hotel/orderdetail.vue?351b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/hotel/orderdetail.vue?aae0", "uni-app:///adminExt/hotel/orderdetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/hotel/orderdetail.vue?5c83", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/hotel/orderdetail.vue?9855"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "format", "opt", "loading", "isload", "menuindex", "pre_url", "djs", "detail", "hotel", "lefttime", "codtxt", "date", "text", "computed", "startDate", "endDate", "onLoad", "onPullDownRefresh", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "id", "interval", "getdjs", "setremark", "setremarkconfirm", "type", "orderid", "content", "setTimeout", "qrdaodian", "qrlidian", "dialogLeaveClose", "confirmleave", "real_leavedate", "ispay", "confirmorder", "refund", "refund<PERSON><PERSON><PERSON>", "delOrder", "closeOrder", "refundnopass", "refundpass", "bindDateChange", "getDate", "year", "month", "day"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChGA;AAAA;AAAA;AAAA;AAAy0B,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoS71B;AACA;AAAA,eAEA;EACAC;IACA;MACAC;IACA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAG;YACAH;YACAA;UACA;QACA;QACAA;MACA;IACA;IACAI;MACA;MACA;MAEA;QACAJ;MACA;QACA;QACA;QACA;QACA;QACAA;MACA;IACA;IACAK;MACA;IACA;IACAC;MACA;MACA;MACAL;QAAAM;QAAAC;QAAAC;MAAA;QACAR;QACAS;UACAV;QACA;MACA;IACA;IACAW;MACA;MACA;MACAV;QACAA;QACAA;UAAAO;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAY;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAb;QAAAO;QAAAO;MAAA;QACAd;QACAS;UACAV;QACA;MACA;IACA;IAEAgB;MACA;MACA;MACAf;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAiB;MACA;MACA;MACAhB;QACAA;QACAA;UAAAO;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAkB;MACA;MACA;MACAjB;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAmB;MACA;MACA;MACAlB;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAoB;MACA;MACA;MACAnB;MACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAT;UACA;QACA;MACA;IACA;IACAoB;MACA;MACA;MACApB;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAsB;MACA;MACA;MACArB;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAuB;MACA;MACA;MACAtB;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAwB;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MAEA;QACAC;MACA;QACAA;MACA;MACAC;MACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC9hBA;AAAA;AAAA;AAAA;AAAsrC,CAAgB,smCAAG,EAAC,C;;;;;;;;;;;ACA1sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "adminExt/hotel/orderdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './adminExt/hotel/orderdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderdetail.vue?vue&type=template&id=a7658d56&\"\nvar renderjs\nimport script from \"./orderdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./orderdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"adminExt/hotel/orderdetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=template&id=a7658d56&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"会员\") : null\n  var m1 =\n    _vm.isload &&\n    _vm.detail.isbefore == 1 &&\n    _vm.detail.real_usemoney > 0 &&\n    _vm.detail.real_roomprice > 0\n      ? _vm.t(\"余额单位\")\n      : null\n  var m2 =\n    _vm.isload &&\n    _vm.detail.isbefore == 1 &&\n    _vm.detail.real_usemoney > 0 &&\n    _vm.detail.real_roomprice == 0\n      ? _vm.t(\"余额单位\")\n      : null\n  var m3 =\n    _vm.isload && _vm.detail.use_money > 0 && _vm.detail.leftmoney > 0\n      ? _vm.t(\"余额单位\")\n      : null\n  var m4 =\n    _vm.isload &&\n    !(_vm.detail.use_money > 0 && _vm.detail.leftmoney > 0) &&\n    _vm.detail.use_money > 0 &&\n    _vm.detail.leftmoney == 0\n      ? _vm.t(\"余额单位\")\n      : null\n  var m5 = _vm.isload && _vm.detail.leveldk_money > 0 ? _vm.t(\"会员\") : null\n  var m6 = _vm.isload && _vm.detail.couponmoney > 0 ? _vm.t(\"优惠券\") : null\n  var m7 = _vm.isload && _vm.detail.scoredk_money > 0 ? _vm.t(\"积分\") : null\n  var m8 = _vm.isload && _vm.detail.use_money > 0 ? _vm.t(\"余额\") : null\n  var m9 = _vm.isload && _vm.detail.use_money > 0 ? _vm.t(\"余额单位\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"ordertop\" :style=\"'background:url(' + pre_url + '/static/img/ordertop.png);background-size:100%'\">\n\t\t\t<view class=\"f1\" v-if=\"detail.status==0\">\n\t\t\t\t<view class=\"t1\">等待买家付款</view>\n\t\t\t\t<view class=\"t2\" v-if=\"djs\">剩余时间：{{djs}}</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==1\">\n\t\t\t\t<view class=\"t1\">'已成功付款'</view>\n\t\t\t\t<view class=\"t2\">请尽快确认</view>\n\t\t\t</view>\r\n\t\t\t<view class=\"f1\" v-if=\"detail.status==2\">\n\t\t\t\t<view class=\"t1\">已确认</view>\r\n\t\t\t\t<view class=\"t2\" >待入住</view>\r\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==3\">\n\t\t\t\t<view class=\"t1\">已到店</view>\n\t\t\t\t<view class=\"t2\" >入住信息：{{detail.linkname}} {{detail.tel}}</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==4\">\n\t\t\t\t<view class=\"t1\">已离店</view>\n\t\t\t</view>\r\n\t\t\t<view class=\"f1\" v-if=\"detail.status==5\">\r\n\t\t\t\t<view class=\"t1\">已完成</view>\r\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==-1\">\n\t\t\t\t<view class=\"t1\">订单已取消</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"product\">\n\t\t\t<view class=\"content\">\n\t\t\t\t<view>\n\t\t\t\t\t<image :src=\"detail.pic\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t<text class=\"t1\">{{hotel.name}}</text>\n\t\t\t\t\t<text class=\"t2\">{{detail.title}}</text>\n\t\t\t\t\t<view class=\"t3\">\r\n\t\t\t\t\t\t<text class=\"x1 flex1\" >￥{{detail.sell_price}}</text>\r\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单人</text>\n\t\t\t\t<text class=\"flex1\"></text>\n\t\t\t\t<image :src=\"detail.headimg\" style=\"width:80rpx;height:80rpx;margin-right:8rpx\"/>\n\t\t\t\t<text  style=\"height:80rpx;line-height:80rpx\">{{detail.nickname}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}ID</text>\n\t\t\t\t<text class=\"t2\">{{detail.mid}}</text>\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">入住姓名</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.linkman}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">联系电话</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.tel}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">入住日期</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.in_date}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">离店日期</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.leave_date}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" >\r\n\t\t\t\t<text class=\"t1\">入住人数</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.totalnum}}</text>\r\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\" v-if=\"detail.remark\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">备注</text>\n\t\t\t\t<text class=\"t2\">{{detail.remark}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单编号</text>\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{detail.ordernum}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytypeid!='4' && detail.paytime\">\n\t\t\t\t<text class=\"t1\">支付时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytime\">\n\t\t\t\t<text class=\"t1\">支付方式</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytype}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>1 && detail.daodian_time\">\n\t\t\t\t<text class=\"t1\">到店时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.daodian_time}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status==4 && detail.collect_time\">\n\t\t\t\t<text class=\"t1\">实际离店日期</text>\n\t\t\t\t<text class=\"t2\">{{detail.real_leavedate}}</text>\n\t\t\t</view>\n\t\t</view>\t\t\r\n\t\t<view class=\"orderinfo\" v-if=\"detail.isbefore==1\">\r\n\t\t\t<view class=\"item\" v-if=\"detail.fuwu_refund_money>0\">\r\n\t\t\t\t<text class=\"t1\">{{text['服务费']}}退款</text>\r\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.fuwu_refund_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" >\r\n\t\t\t\t<text class=\"t1\">实际支付房费</text>\r\n\t\t\t\t<block v-if=\"detail.real_usemoney>0 && detail.real_roomprice>0\">\r\n\t\t\t\t\t<text class=\"t2 red\" >¥{{detail.real_roomprice}} + {{detail.real_usemoney}}{{t('余额单位')}}</text>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"detail.real_usemoney>0 && detail.real_roomprice==0\">\r\n\t\t\t\t\t<text class=\"t2 red\"> {{detail.real_usemoney}}{{t('余额单位')}}</text>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"detail.real_usemoney==0 && d.order.real_roomprice>0\">\r\n\t\t\t\t\t<text class=\"t2 red\">¥{{detail.real_roomprice}}</text>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"orderinfo\" v-if=\"detail.yajin_money>0 && detail.status>2\">\r\n\t\r\n\t\t\t<view class=\"item flex-bt\">\r\n\t\t\t\t<text class=\"t1\">押金状态</text>\r\n\t\t\t\t<view class=\"ordernum-info flex-bt\">\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.yajin_refund_status==0\">待申请</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.yajin_refund_status==1\">审核中</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.yajin_refund_status==2\">已退款</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.yajin_refund_status==3\">已驳回</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item flex-bt\" v-if=\"detail.yajin_refund_status==3\">\r\n\t\t\t\t<text class=\"t1\">驳回原因</text>\r\n\t\t\t\t<view class=\"ordernum-info flex-bt\">\r\n\t\t\t\t\t<text class=\"t2\" >{{detail.yajin_refund_reason?detail.yajin_refund_reason:'无'}}</text>\r\n\t\t\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">房费金额</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.use_money>0 && detail.leftmoney>0\">{{detail.use_money}}{{t('余额单位')}} + ￥{{detail.leftmoney}}</text>\r\n\t\t\t\t<text class=\"t2 red\" v-else-if=\"detail.use_money>0 && detail.leftmoney==0\">{{detail.use_money}}{{t('余额单位')}}</text>\r\n\t\t\t\t<text class=\"t2 red\" v-else>￥{{detail.sell_price}}</text>\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.fuwu_money>0\">\r\n\t\t\t\t<text class=\"t1\">{{text['服务费']}}</text>\r\n\t\t\t\t<text class=\"t2 red\">+¥{{detail.fuwu_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"item\" v-if=\"detail.yajin_money>0\">\r\n\t\t\t\t<text class=\"t1\">押金 </text>\r\n\t\t\t\t<text class=\"t2 red\">+¥{{detail.yajin_money}}\r\n\r\n\t\t\t\t</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-else>\r\n\t\t\t\t<text class=\"t1\">押金 (免押)</text>\r\n\t\t\t\t<text class=\"t2 red\">+¥{{detail.yajin_money}}\r\n\t\t\t\r\n\t\t\t\t</text>\r\n\t\t\t</view>\r\n\t\t\t\n\t\t\t<view class=\"item\" v-if=\"detail.leveldk_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}折扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.leveldk_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.jianmoney > 0\">\n\t\t\t\t<text class=\"t1\">满减活动</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.manjian_money}}</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"item\" v-if=\"detail.couponmoney > 0\">\n\t\t\t\t<text class=\"t1\">{{t('优惠券')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.coupon_money}}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"item\" v-if=\"detail.scoredk_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('积分')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.scoredk_money}}</text>\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.use_money > 0\">\r\n\t\t\t\t<text class=\"t1\">{{t('余额')}}抵扣</text>\r\n\t\t\t\t<text class=\"t2 red\">-{{detail.use_money}}{{t('余额单位')}}</text>\r\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">支付金额</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.totalprice}}</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0\">未付款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==1\">待确认</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==2\">待入住</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==3\">已到店</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==4\">已离店</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==5\">已完成</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==-1\">已关闭</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\n\t\t\t\t<text class=\"t1\">退款状态</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==1\">审核中,¥{{detail.refund_money}}</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==2\">已退款,¥{{detail.refund_money}}</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==3\">已驳回,¥{{detail.refund_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\n\t\t\t\t<text class=\"t1\">退款原因</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_reason}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_checkremark\">\n\t\t\t\t<text class=\"t1\">审核备注</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_checkremark}}</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单备注</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.message ? detail.message : '无'}}</text>\n\t\t\t</view>\n\t\t\n\t\t\t<view class=\"item flex-col\" v-if=\"(detail.status==1 || detail.status==2)\">\n\t\t\t\t<text class=\"t1\">核销码</text>\n\t\t\t\t<view class=\"flex-x-center\">\n\t\t\t\t\t<image :src=\"detail.hexiao_qr\" style=\"width:400rpx;height:400rpx\" @tap=\"previewImage\" :data-url=\"detail.hexiao_qr\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view style=\"width:100%;height:calc(160rpx + env(safe-area-inset-bottom));\"></view>\n\n\t\t<view class=\"bottom notabbarbot\">\n\t\t\t<view v-if=\"detail.refund_status==1\" class=\"btn2\" @tap=\"refundnopass\" :data-id=\"detail.id\">退款驳回</view>\n\t\t\t<view v-if=\"detail.refund_status==1\" class=\"btn2\" @tap=\"refundpass\" :data-id=\"detail.id\">退款通过</view>\n\t\t\t<view v-if=\"detail.status==0\" class=\"btn2\" @tap=\"closeOrder\" :data-id=\"detail.id\">关闭订单</view>\n\t\t\r\n\t\t\t<view v-if=\"detail.status==1\" class=\"btn2\" @tap=\"confirmorder\" :data-id=\"detail.id\">确认订单</view>\n\t\t\t<view v-if=\"detail.status==2\" class=\"btn2\" @tap=\"qrdaodian\" :data-id=\"detail.id\">确认到店</view>\r\n\t\t\t<view v-if=\"detail.status==3\" class=\"btn2\" @tap=\"qrlidian\" :data-id=\"detail.id\">确认离店</view>\n\t\t\t<!--<view v-if=\"detail.status==4\" class=\"btn2\" @tap=\"delOrder\" :data-id=\"detail.id\">删除</view>-->\r\n\t\t\t<view v-if=\"detail.status==4 && !detail.yajin_refund_status && detail.yajin_money>0\" class=\"btn2\" @tap=\"refundYajin\" :data-id=\"detail.id\">退押金</view>\n\t\t\t<view class=\"btn2\" @tap=\"setremark\" :data-id=\"detail.id\">设置备注</view>\n\t\t</view>\n\t\t<uni-popup id=\"dialogSetremark\" ref=\"dialogSetremark\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"设置备注\" :value=\"detail.remark\" placeholder=\"请输入备注\" @confirm=\"setremarkconfirm\"></uni-popup-dialog>\n\t\t</uni-popup>\n\n\t\t\n\t\t<uni-popup id=\"dialogLeave\" ref=\"dialogLeave\" type=\"dialog\">\n\t\t\t<view class=\"uni-popup-dialog\">\n\t\t\t\t<view class=\"uni-dialog-title\">\n\t\t\t\t\t<text class=\"uni-dialog-title-text\">确认离店</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-content\">\n\t\t\t\t\t<view class=\"uni-list-cell-db\">\r\n\t\t\t\t\t\t<label>离店日期</label>\r\n\t\t\t\t\t\t<picker mode=\"date\" :value=\"date\" :start=\"startDate\" :end=\"endDate\" @change=\"bindDateChange\">\r\n\t\t\t\t\t\t\t<view class=\"uni-input\">{{date}}</view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-button-group\">\n\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogLeaveClose\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"confirmleave\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n        \r\n    \n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nvar interval = null;\n\nexport default {\n  data() {\r\n\t\tconst currentDate = this.getDate({\r\n\t\t\tformat: true\r\n\t\t})\n    return {\n        opt:{},\n        loading:false,\n        isload: false,\n        menuindex:-1,\n        pre_url:app.globalData.pre_url,\n        djs: '',\n        detail: \"\",\n        hotel: \"\",\n        lefttime: \"\",\n        codtxt: \"\",\r\n\t\t\t  date: currentDate,\r\n\t\t\t\ttext:[]\n    };\n  },\n  computed: {\n\t\t\tstartDate() {\n\t\t\t\t\treturn this.getDate('start');\n\t\t\t},\n\t\t\tendDate() {\n\t\t\t\t\treturn this.getDate('end');\n\t\t\t}\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onUnload: function () {\n    clearInterval(interval);\n  },\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiAdminHotelOrder/detail', {id: that.opt.id}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.detail = res.detail;\n\t\t\t\tthat.hotel = res.hotel;\n\t\t\t\tthat.lefttime = res.lefttime;\n\t\t\t\tthat.codtxt = res.codtxt;\r\n\t\t\t\tthat.text = res.text\n\t\t\t\tif (res.lefttime > 0) {\n\t\t\t\t\tinterval = setInterval(function () {\n\t\t\t\t\t\tthat.lefttime = that.lefttime - 1;\n\t\t\t\t\t\tthat.getdjs();\n\t\t\t\t\t}, 1000);\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    getdjs: function () {\n      var that = this;\n      var totalsec = that.lefttime;\n\n      if (totalsec <= 0) {\n        that.djs = '00时00分00秒';\n      } else {\n        var houer = Math.floor(totalsec / 3600);\n        var min = Math.floor((totalsec - houer * 3600) / 60);\n        var sec = totalsec - houer * 3600 - min * 60;\n        var djs = (houer < 10 ? '0' : '') + houer + '时' + (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';\n        that.djs = djs;\n      }\n    },\n\t\tsetremark:function(){\n\t\t\tthis.$refs.dialogSetremark.open();\n\t\t},\n\t\tsetremarkconfirm: function (done, remark) {\n\t\t\tthis.$refs.dialogSetremark.close();\n\t\t\tvar that = this\n\t\t\tapp.post('ApiAdminHotelOrder/setremark', { type:'hotel',orderid: that.detail.id,content:remark }, function (res) {\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n    },\r\n\t\tqrdaodian:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar orderid = e.currentTarget.dataset.id\r\n\t\t\tapp.confirm('确定用户已经到店吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiAdminHotelOrder/qrdaodian', { orderid: orderid }, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t}, 1000)\r\n\t\t\t\t})\r\n\t\t\t});\r\n\t\t},\n\t\tqrlidian:function(){\n\t\t\tthis.$refs.dialogLeave.open();\n\t\t},\n\t\tdialogLeaveClose:function(){\n\t\t\tthis.$refs.dialogLeave.close();\n\t\t},\n\t\tconfirmleave:function(){\n\t\t\tthis.$refs.dialogLeave.close();\n\t\t\tvar that = this\n\t\t\tapp.post('ApiAdminHotelOrder/confirmleave', { orderid: that.detail.id,real_leavedate:that.date}, function (res) {\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n\t\t},\r\n\t\t\n\t\tispay:function(e){\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要改为已支付吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/ispay', {type:'hotel', orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\r\n\t\tconfirmorder:function(e){\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定确认该订单吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminHotelOrder/confirmorder', {orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\r\n\t\trefund: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar orderid = e.currentTarget.dataset.id\r\n\t\t\tapp.confirm('确定要拒单并退款吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiAdminHotelOrder/judan', { type:'hotel',orderid: orderid }, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t}, 1000)\r\n\t\t\t\t})\r\n\t\t\t});\r\n\t\t},\r\n\t\trefundYajin: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar orderid = e.currentTarget.dataset.id\r\n\t\t\tapp.confirm('确定要退还押金吗?', function () {\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiAdminHotelOrder/refundYajin', { type:'hotel',orderid: orderid }, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tapp.success(data.msg);\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t}, 1000)\r\n\t\t\t\t})\r\n\t\t\t});\r\n\t\t},\n\t\tdelOrder: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.showLoading('删除中');\n\t\t\tapp.confirm('确定要删除该订单吗?', function () {\n\t\t\t\tapp.post('ApiAdminHotelOrder/delOrder', { type:'hotel',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tapp.goto('takeawayorder');\n\t\t\t\t\t}, 1000)\n\t\t\t\t});\n\t\t\t})\n\t\t},\n\t\tcloseOrder: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要关闭该订单吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminHotelOrder/closeOrder', { type:'hotel',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function (){\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t});\n\t\t\t})\n\t\t},\n\t\trefundnopass: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要驳回退款申请吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/refundnopass', { type:'hotel',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\trefundpass: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要审核通过并退款吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminHotelOrder/refundpass', { type:'hotel',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},  \r\n\t\tbindDateChange: function(e) {\n\t\t\t\tthis.date = e.detail.value\n\t\t},\n\t\tgetDate(type) {\n\t\t\t\tconst date = new Date();\n\t\t\t\tlet year = date.getFullYear();\n\t\t\t\tlet month = date.getMonth() + 1;\n\t\t\t\tlet day = date.getDate();\n\n\t\t\t\tif (type === 'start') {\n\t\t\t\t\t\tyear = year - 60;\n\t\t\t\t} else if (type === 'end') {\n\t\t\t\t\t\tyear = year + 2;\n\t\t\t\t}\n\t\t\t\tmonth = month > 9 ? month : '0' + month;\n\t\t\t\tday = day > 9 ? day : '0' + day;\n\t\t\t\treturn `${year}-${month}-${day}`;\n\t\t}\n  }\n};\n</script>\n<style>\n.ordertop{width:100%;height:220rpx;padding:50rpx 0 0 70rpx}\n.ordertop .f1{color:#fff}\n.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}\n.ordertop .f1 .t2{font-size:24rpx}\n\n.address{ display:flex;width: 100%; padding: 20rpx 3%; background: #FFF;margin-bottom:20rpx;}\n.address .img{width:40rpx}\n.address image{width:40rpx; height:40rpx;}\n.address .info{flex:1;display:flex;flex-direction:column;}\n.address .info .t1{font-size:28rpx;font-weight:bold;color:#333}\n.address .info .t2{font-size:24rpx;color:#999}\n\n.product{width:100%; padding: 14rpx 3%;background: #FFF;}\n.product .content{display:flex;position:relative;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;}\n.product .content:last-child{ border-bottom: 0; }\n.product .content image{ width: 140rpx; height: 140rpx;}\n.product .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\n.product .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.product .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\n.product .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\n.product .content .detail .x1{ flex:1}\n.product .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\n.product .content .comment{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc702 solid; border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.product .content .comment2{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc7c2 solid; border-radius:10rpx;background:#fff; color: #ffc7c2;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.orderinfo{ width:100%;margin-top:10rpx;padding: 14rpx 3%;background: #FFF;}\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}\n.orderinfo .item:last-child{ border-bottom: 0;}\n.orderinfo .item .t1{width:200rpx;}\n.orderinfo .item .t2{flex:1;text-align:right}\n.orderinfo .item .red{color:red}\n\n.bottom{ width: 100%;height:calc(92rpx + env(safe-area-inset-bottom));padding: 0 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}\n\n.btn1{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}\n.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n.btn3{position:absolute;top:60rpx;right:10rpx;font-size:24rpx;width:120rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n\n.btitle{ width:100%;height:100rpx;background:#fff;padding:0 20rpx;border-bottom:1px solid #f5f5f5}\n.btitle .comment{border: 1px #ffc702 solid;border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.btitle .comment2{border: 1px #ffc7c0 solid;border-radius:10rpx;background:#fff; color: #ffc7c0;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.picker{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n\n.uni-popup-dialog {width: 300px;border-radius: 5px;background-color: #fff;}\n.uni-dialog-title {display: flex;flex-direction: row;justify-content: center;padding-top: 15px;padding-bottom: 5px;}\n.uni-dialog-title-text {font-size: 16px;font-weight: 500;}\n.uni-dialog-content {display: flex;flex-direction: row;align-items: center;padding: 5px 15px 15px 15px; margin-top: 20rpx;}\n.uni-dialog-content-text {font-size: 14px;color: #6e6e6e;}\n.uni-dialog-button-group {display: flex;flex-direction: row;border-top-color: #f5f5f5;border-top-style: solid;border-top-width: 1px;}\n.uni-dialog-button {display: flex;flex: 1;flex-direction: row;justify-content: center;align-items: center;height: 45px;}\n.uni-border-left {border-left-color: #f0f0f0;border-left-style: solid;border-left-width: 1px;}\n.uni-dialog-button-text {font-size: 14px;}\n.uni-button-color {color: #007aff;}\r\n\r\n\r\n/*确认离店*/\r\n.uni-list-cell-db{ display: flex;}\r\n.uni-list-cell-db label{ padding-right:20rpx}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213038639\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}