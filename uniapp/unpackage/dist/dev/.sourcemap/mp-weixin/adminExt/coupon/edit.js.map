{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/coupon/edit.vue?a700", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/coupon/edit.vue?2831", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/coupon/edit.vue?0d60", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/coupon/edit.vue?6671", "uni-app:///adminExt/coupon/edit.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/coupon/edit.vue?d1f6", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/adminExt/coupon/edit.vue?6d8b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "loading", "pre_url", "info", "memberlevel", "pic", "start_time1", "start_time2", "end_time1", "end_time2", "endtime", "endtime2", "starttime", "starttime2", "product_showset", "showtjArr", "zhif", "giftProductsList", "giftServiceList", "buyproGiveNum", "giftProductsLists", "pageType", "bid", "auth", "restaurant", "editType", "productdata", "addshopType", "yuyue_product", "addfuwushopType", "categorydata", "onLoad", "type", "gettj", "yxqtype", "fwtype", "tolist", "isgive", "paygive", "buyprogive", "buyyuyueprogive", "stock", "perlimit", "showtj", "uni", "title", "onShow", "setTimeout", "icon", "that", "onUnload", "methods", "inputNumChange", "tColor", "clearInterval", "displayConditions", "paymentGiftChange", "collectionConditions", "initTime", "date", "restaurantShop", "url", "addshopClass", "restaurantClass", "clearRestaurant", "content", "success", "clearShopClass", "scopeApplicationRes", "scopeApplication", "clearShopCartFn", "clearShopCartFn2", "addshopProgive", "addshop", "getdata", "app", "id", "item", "subform", "formdata", "delta", "bindBuyyuyueprogiveChange", "bindBuyprogiveChange", "bindYxqtypeChange", "couponCenterChange", "bindStatusChange", "bindStartTimeChange", "bindStartTime1Change", "bindStartTimeChange2", "bindEndTimeChange", "bindEndTimeChange2", "bindStartTime2Change", "bindEndTime1Change", "bindEndTime2Change", "bindPaygiveChange", "bindTypeChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1RA;AAAA;AAAA;AAAA;AAAk0B,CAAgB,kyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0dt1B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;;EAEAC;IACA;IACA;IACA;MAAA;MACA;MACA;MACA;IACA;MAAA;MACA;MACA;IACA;MAAA;MACA;MACA;MACA;MACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;IACA;MAAA;MACA;MACA;MACA;QACAX;QACAC;QACAE;QACAD;QACAE;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;IACA;IACA;MACAC;QACAC;MACA;IACA;MACAD;QACAC;MACA;IACA;EACA;EACAC;IACAF;IACA;IACAA;MACA;QACAG;UACA;YAAA;UAAA;UACA;YACAA;cACAH;gBAAAI;gBAAAH;cAAA;YACA;YACA;UACA;UACAI;UACA;YAAA;UAAA;UACAA;UACAA;YAAA;UAAA;QACA;MACA;QACA;QACAF;UACA;YAAA;UAAA;UACA;YACAA;cACAH;gBAAAI;gBAAAH;cAAA;YACA;YACA;UACA;UACAI;UACA;YAAA;UAAA;UACAA;QACA;MACA;IACA;IACAL;MACA;QACA;UAAA;QAAA;QACA;UACAG;YACAH;cAAAI;cAAAH;YAAA;UACA;UACA;QACA;QACAI;QACA;UAAA;QAAA;QACAA;QACAA;UAAA;QAAA;MACA;QACA;UAAA;QAAA;QACA;UACAF;YACAH;cAAAI;cAAAH;YAAA;UACA;UACA;QACA;QACAI;MACA;IACA;IACA;IACAL;MACA;QAAA;MAAA;MACA;QACAG;UACAH;YAAAI;YAAAH;UAAA;QACA;QACA;MACA;MACAI;MACA;QAAA;MAAA;MACAA;IACA;EACA;EACAC,+BAEA;EACAC;IACA;IACAC;MACA;MACA;QAAA;QACAH;UAAA;QAAA;MACA;QAAA;QACAA;UAAA;QAAA;MACA;IACA;IACAI;MACA;MACA;QACA;UACA;YACAJ;UACA;UACAK;QACA;UACA;QACA;MACA;QACA;MACA;QACA;UACAP;YACAE;UACA;QACA;UACA;UACA;QACA;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAM;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACAhB;QACAiB;MACA;IACA;IACA;IACAC;MACA;QACAlB;UACAiB;QACA;MACA;QACAjB;UACAiB;QACA;MACA;IAEA;IACA;IACAE;MACAnB;QACAiB;MACA;IACA;IACA;IACAG;MACA;MACApB;QACAC;QACAoB;QACAC;UACA;YACA;cAAA;YAAA;YACAjB;YACA;cAAA;YAAA;YACAA;UACA,wBACA;QACA;MACA;IACA;IACA;IACAkB;MACA;MACAvB;QACAC;QACAoB;QACAC;UACA;YACA;cAAA;YAAA;YACAjB;YACA;cAAA;YAAA;YACAA;UACA,wBACA;QACA;MACA;IACA;IACA;IACAmB;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA1B;QACAC;QACAoB;QACAC;UACA;YACA;cACA;gBAAA;cAAA;cACAjB;YACA;cACA;gBAAA;cAAA;cACAA;YACA;UACA,wBACA;QACA;MACA;IACA;IACAsB;MACA;MACA3B;QACAC;QACAoB;QACAC;UACA;YACA;cACA;gBAAA;cAAA;cACAjB;YACA;cACA;gBAAA;cAAA;cACAA;YACA;UACA,wBACA;QACA;MACA;IACA;IACA;IACAuB;MACA;MACA5B;QACAiB;MACA;IACA;IACA;IACAY;MACA;MACA7B;QACAiB;MACA;IACA;IACAa;MACA;MACA;MACAzB;MACA0B;QAAAC;MAAA;QACA3B;QACA;QAAA,CACA;UACAA;UACA;UACA;YACA;YACA;YACAA;YACAA;YACA;YACAA;YACAA;UACA;UACA;UACA;YACAA;YACAA;UACA;UACA;YACAA;YACAA;UACA;UACA;UACA;YACAA;YACAA;YACAA;cACA4B;YACA;UACA;UACA;UACA;YACA5B;YACA;YACAA;cACA4B;YACA;UACA;QACA;QACA;QACA5B;QACAA;QACAA;QACA;QACAA;QACAA;QACA;QACA;UACAA;QACA;QACA;QACA;UACAA;QACA;QACA;QACA;UACAA;QACA;QACAA;MACA;IACA;IACA6B;MACA;MACA;MACA;MACA;QAAA;QACA;UACA;UACA;MAAA;MAEA;MACAC;MACA;QACAA;MACA;MACA9B;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACA0B;QAAAxE;MAAA;QACA;UACAwE;UACA5B;YACAH;cACAoC;YACA;UACA;QACA;UACAL;QACA;MACA;IACA;IACAM;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;UAAA;UACA;UACA;UACA;MAAA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3+BA;AAAA;AAAA;AAAA;AAA+qC,CAAgB,+lCAAG,EAAC,C;;;;;;;;;;;ACAnsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "adminExt/coupon/edit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './adminExt/coupon/edit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit.vue?vue&type=template&id=126fd9a4&\"\nvar renderjs\nimport script from \"./edit.vue?vue&type=script&lang=js&\"\nexport * from \"./edit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"adminExt/coupon/edit.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=template&id=126fd9a4&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.pageType ? _vm.t(\"优惠券\") : null\n  var m1 = _vm.isload && _vm.pageType ? _vm.t(\"优惠券\") : null\n  var m2 =\n    _vm.isload &&\n    _vm.pageType &&\n    (_vm.info.type == 1 ||\n      _vm.info.type == 10 ||\n      _vm.info.type == 20 ||\n      _vm.info.type == 11) &&\n    (_vm.info.fwtype == 1 || _vm.info.fwtype == 6)\n      ? _vm.t(\"color1\")\n      : null\n  var m3 =\n    _vm.isload &&\n    _vm.pageType &&\n    (_vm.info.type == 1 ||\n      _vm.info.type == 10 ||\n      _vm.info.type == 20 ||\n      _vm.info.type == 11) &&\n    (_vm.info.fwtype == 1 || _vm.info.fwtype == 6)\n      ? _vm.t(\"color1rgb\")\n      : null\n  var g0 =\n    _vm.isload &&\n    _vm.pageType &&\n    (_vm.info.type == 1 ||\n      _vm.info.type == 10 ||\n      _vm.info.type == 20 ||\n      _vm.info.type == 11) &&\n    (_vm.info.fwtype == 1 || _vm.info.fwtype == 6)\n      ? _vm.categorydata.length\n      : null\n  var m4 =\n    _vm.isload &&\n    _vm.pageType &&\n    (_vm.info.type == 1 ||\n      _vm.info.type == 10 ||\n      _vm.info.type == 20 ||\n      _vm.info.type == 11) &&\n    _vm.info.fwtype == 2\n      ? _vm.t(\"color1\")\n      : null\n  var m5 =\n    _vm.isload &&\n    _vm.pageType &&\n    (_vm.info.type == 1 ||\n      _vm.info.type == 10 ||\n      _vm.info.type == 20 ||\n      _vm.info.type == 11) &&\n    _vm.info.fwtype == 2\n      ? _vm.t(\"color1rgb\")\n      : null\n  var g1 =\n    _vm.isload &&\n    _vm.pageType &&\n    (_vm.info.type == 1 ||\n      _vm.info.type == 10 ||\n      _vm.info.type == 20 ||\n      _vm.info.type == 11) &&\n    _vm.info.fwtype == 2\n      ? _vm.productdata.length\n      : null\n  var m6 =\n    _vm.isload &&\n    _vm.pageType &&\n    (_vm.info.type == 1 ||\n      _vm.info.type == 10 ||\n      _vm.info.type == 20 ||\n      _vm.info.type == 11) &&\n    _vm.info.fwtype == 4\n      ? _vm.t(\"color1\")\n      : null\n  var m7 =\n    _vm.isload &&\n    _vm.pageType &&\n    (_vm.info.type == 1 ||\n      _vm.info.type == 10 ||\n      _vm.info.type == 20 ||\n      _vm.info.type == 11) &&\n    _vm.info.fwtype == 4\n      ? _vm.t(\"color1rgb\")\n      : null\n  var g2 =\n    _vm.isload &&\n    _vm.pageType &&\n    (_vm.info.type == 1 ||\n      _vm.info.type == 10 ||\n      _vm.info.type == 20 ||\n      _vm.info.type == 11) &&\n    _vm.info.fwtype == 4\n      ? _vm.yuyue_product.length\n      : null\n  var m8 =\n    _vm.isload && !_vm.pageType && _vm.info.fwtype == 1 ? _vm.t(\"color1\") : null\n  var m9 =\n    _vm.isload && !_vm.pageType && _vm.info.fwtype == 1\n      ? _vm.t(\"color1rgb\")\n      : null\n  var g3 =\n    _vm.isload && !_vm.pageType && _vm.info.fwtype == 1\n      ? _vm.categorydata.length\n      : null\n  var m10 =\n    _vm.isload && !_vm.pageType && _vm.info.fwtype == 2 ? _vm.t(\"color1\") : null\n  var m11 =\n    _vm.isload && !_vm.pageType && _vm.info.fwtype == 2\n      ? _vm.t(\"color1rgb\")\n      : null\n  var g4 =\n    _vm.isload && !_vm.pageType && _vm.info.fwtype == 2\n      ? _vm.productdata.length\n      : null\n  var m12 = _vm.isload ? _vm.inArray(\"-1\", _vm.info.gettj) : null\n  var m13 = _vm.isload ? _vm.inArray(\"0\", _vm.info.gettj) : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.memberlevel, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m14 = _vm.inArray(item.id, _vm.info.gettj)\n        return {\n          $orig: $orig,\n          m14: m14,\n        }\n      })\n    : null\n  var m15 =\n    _vm.isload && _vm.info.tolist == 1 && _vm.pageType\n      ? _vm.inArray(\"-1\", _vm.info.showtj)\n      : null\n  var m16 =\n    _vm.isload && _vm.info.tolist == 1 && _vm.pageType\n      ? _vm.inArray(\"0\", _vm.info.showtj)\n      : null\n  var l1 =\n    _vm.isload && _vm.info.tolist == 1 && _vm.pageType\n      ? _vm.__map(_vm.memberlevel, function (item, __i0__) {\n          var $orig = _vm.__get_orig(item)\n          var m17 = _vm.inArray(item.id, _vm.info.showtj)\n          return {\n            $orig: $orig,\n            m17: m17,\n          }\n        })\n      : null\n  var m18 =\n    _vm.isload && _vm.info.paygive == 1 && _vm.pageType\n      ? _vm.inArray(\"shop\", _vm.info.paygive_scene)\n      : null\n  var m19 =\n    _vm.isload && _vm.info.paygive == 1 && _vm.pageType\n      ? _vm.inArray(\"scoreshop\", _vm.info.paygive_scene)\n      : null\n  var m20 =\n    _vm.isload && _vm.info.paygive == 1 && _vm.pageType\n      ? _vm.inArray(\"collage\", _vm.info.paygive_scene)\n      : null\n  var m21 =\n    _vm.isload && _vm.info.paygive == 1 && _vm.pageType\n      ? _vm.inArray(\"kanjia\", _vm.info.paygive_scene)\n      : null\n  var m22 =\n    _vm.isload && _vm.info.paygive == 1 && _vm.pageType\n      ? _vm.inArray(\"seckill\", _vm.info.paygive_scene)\n      : null\n  var m23 =\n    _vm.isload && _vm.info.paygive == 1 && _vm.pageType\n      ? _vm.inArray(\"tuangou\", _vm.info.paygive_scene)\n      : null\n  var m24 =\n    _vm.isload && _vm.info.paygive == 1 && _vm.pageType\n      ? _vm.inArray(\"lucky_collage\", _vm.info.paygive_scene)\n      : null\n  var m25 =\n    _vm.isload && _vm.info.paygive == 1 && _vm.pageType\n      ? _vm.inArray(\"recharge\", _vm.info.paygive_scene)\n      : null\n  var m26 =\n    _vm.isload && _vm.info.paygive == 1 && _vm.pageType\n      ? _vm.inArray(\"maidan\", _vm.info.paygive_scene)\n      : null\n  var m27 =\n    _vm.isload && _vm.info.paygive == 1 && !_vm.pageType\n      ? _vm.inArray(\"restaurant\", _vm.info.paygive_scene)\n      : null\n  var m28 =\n    _vm.isload && _vm.info.paygive == 1 && !_vm.pageType\n      ? _vm.inArray(\"recharge\", _vm.info.paygive_scene)\n      : null\n  var m29 = _vm.isload && _vm.info.buyprogive == 1 ? _vm.t(\"color1\") : null\n  var m30 = _vm.isload && _vm.info.buyprogive == 1 ? _vm.t(\"color1rgb\") : null\n  var g5 =\n    _vm.isload && _vm.info.buyprogive == 1 ? _vm.giftProductsList.length : null\n  var m31 = _vm.isload && _vm.info.buyyuyueprogive == 1 ? _vm.t(\"color1\") : null\n  var m32 =\n    _vm.isload && _vm.info.buyyuyueprogive == 1 ? _vm.t(\"color1rgb\") : null\n  var g6 =\n    _vm.isload && _vm.info.buyyuyueprogive == 1\n      ? _vm.giftProductsLists.length\n      : null\n  var m33 = _vm.isload ? _vm.tColor(\"color1\") : null\n  var m34 = _vm.isload ? _vm.tColor(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        g0: g0,\n        m4: m4,\n        m5: m5,\n        g1: g1,\n        m6: m6,\n        m7: m7,\n        g2: g2,\n        m8: m8,\n        m9: m9,\n        g3: g3,\n        m10: m10,\n        m11: m11,\n        g4: g4,\n        m12: m12,\n        m13: m13,\n        l0: l0,\n        m15: m15,\n        m16: m16,\n        l1: l1,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        m23: m23,\n        m24: m24,\n        m25: m25,\n        m26: m26,\n        m27: m27,\n        m28: m28,\n        m29: m29,\n        m30: m30,\n        g5: g5,\n        m31: m31,\n        m32: m32,\n        g6: g6,\n        m33: m33,\n        m34: m34,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<form @submit=\"subform\">\r\n      <view class=\"form-box\">\r\n        <view class=\"form-item flex-col\" v-if=\"pageType\">\r\n          <view class=\"f1\">{{t('优惠券')}}类型</view>\r\n          <view class=\"f2\">\r\n            <radio-group name=\"type\" @change=\"bindTypeChange\">\r\n\t\t\t\t\t\t\t<view class=\"radio-group-view\">\r\n\t\t\t\t\t\t\t\t<label><radio value=\"1\" :checked=\"info.type==1?true:false\"></radio> 代金券</label>\r\n\t\t\t\t\t\t\t\t<label><radio value=\"10\" :checked=\"!info || info.type==10?true:false\"></radio> 折扣券</label>\r\n\t\t\t\t\t\t\t\t<label><radio value=\"2\" :checked=\"info.type==2?true:false\"></radio> 礼品券</label>\r\n\t\t\t\t\t\t\t\t<label><radio value=\"3\" :checked=\"info.type==3?true:false\"></radio> 计次券</label>\r\n\t\t\t\t\t\t\t\t<label><radio value=\"4\" :checked=\"info.type==4?true:false\"></radio> 运费抵扣券</label>\r\n\t\t\t\t\t\t\t</view>\r\n            </radio-group>\r\n          </view>\r\n        </view>\r\n        <view class=\"form-item\">\r\n          <view class=\"f1\" v-if=\"pageType\">{{t('优惠券')}}名称<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"f1\" v-else>优惠券名称<text style=\"color:red\"> *</text></view>\r\n          <view class=\"f2\"><input type=\"text\" name=\"name\" :value=\"info.name\" placeholder=\"请填写名称\" placeholder-style=\"color:#888\"></input></view>\r\n        </view>\r\n\t\t\t\t<block v-if=\"pageType\">\r\n\t\t\t\t\t<block v-if=\"info.type == 1 || info.type == 10 || info.type == 20 || info.type == 11\">\r\n\t\t\t\t\t  <view class=\"form-item\" v-if=\"info.type!=10 && info.type!=11\">\r\n\t\t\t\t\t    <view class=\"f1\">优惠金额(元)<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t    <view class=\"f2\"><input type=\"digit\" name=\"money\" :value=\"info.money\" placeholder=\"请填写优惠金额(元)\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t\t  </view>\r\n\t\t\t\t\t  <view class=\"form-item\" v-if=\"info.type==10 \">\r\n\t\t\t\t\t    <view class=\"f1\">折扣比例<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t    <view class=\"f2\"><input type=\"digit\" name=\"discount\" :value=\"info.discount\" placeholder=\"例如9折则填写90\" placeholder-style=\"color:#888\" style=\"margin-right: 10rpx;\"></input>%</view>\r\n\t\t\t\t\t  </view>\r\n\t\t\t\t\t  <view class=\"form-item\" v-if=\"info.type!=11 \">\r\n\t\t\t\t\t    <view class=\"f1\" style=\"width: 228rpx\">最低消费金额(元)<text style=\"color:red\"> </text></view>\r\n\t\t\t\t\t    <view class=\"f2\"><input type=\"text\" name=\"minprice\" :value=\"info.minprice\" placeholder=\"请填写最低消费金额(元)\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t\t  </view>\r\n\t\t\t\t\t  <view class=\"form-item flex-col\">\r\n\t\t\t\t\t    <view class=\"f1\">适用范围</view>\r\n\t\t\t\t\t    <view class=\"f2\">\r\n\t\t\t\t\t      <radio-group class=\"radio-group\" name=\"fwtype\" @change=\"scopeApplication\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"radio-group-view\">\r\n\t\t\t\t\t\t\t\t\t\t<label><radio value=\"0\" :checked=\"info.fwtype==0?true:false\"></radio> 所有商品</label>\r\n\t\t\t\t\t\t\t\t\t\t<label><radio value=\"1\" :checked=\"info.fwtype==1?true:false\"></radio> 指定类目</label>\r\n\t\t\t\t\t\t\t\t\t\t<label v-if=\"bid\"><radio value=\"6\" :checked=\"info.fwtype==6?true:false\"></radio> 指定商家类目</label>\r\n\t\t\t\t\t\t\t\t\t\t<label><radio value=\"2\" :checked=\"info.fwtype==2?true:false\"></radio> 指定商品</label>\r\n\t\t\t\t\t\t\t\t\t\t<label v-if=\"restaurant\"><radio value=\"3\" :checked=\"info.fwtype==3?true:false\"></radio> 指定菜品</label>\r\n\t\t\t\t\t\t\t\t\t\t<label v-if=\"auth.yuyue\"><radio value=\"4\" :checked=\"info.fwtype==4?true:false\"></radio> 指定服务商品</label>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t      </radio-group>\r\n\t\t\t\t\t    </view>\r\n\t\t\t\t\t  </view>\r\n\t\t\t\t\t\t<!-- 指定类目 -->\r\n\t\t\t\t\t\t<view class=\"form-item\" v-if=\"info.fwtype==1 || info.fwtype==6\">\r\n\t\t\t\t\t\t\t<view class=\"flex flex-col addshow-list-view\">\r\n\t\t\t\t\t\t\t\t<view class=\"flex title-view\">\r\n\t\t\t\t\t\t\t\t\t<view>指定类目</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"but-class\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @click=\"addshopClass(info.fwtype)\">添加类目</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"product\" v-if=\"categorydata.length\">\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(item, index2) in categorydata\" :key=\"index2\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"item flex\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"img-view\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image v-if=\"item.pic\" :src=\"item.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view v-else class=\"img-view-empty\"></view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view></view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"del-view flex-y-center\" @tap.stop=\"clearShopClass(item.id)\"><image :src=\"pre_url+'/static/img/del.png'\" style=\"width:24rpx;height:24rpx;margin-right:6rpx\"/></view>\r\n\t\t\t\t\t\t\t\t\t\t</view> \r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- 指定商家类目 -->\r\n\t\t\t\t\t\t<!-- 指定商品 -->\r\n\t\t\t\t\t\t<view class=\"form-item\" v-if=\"info.fwtype==2\">\r\n\t\t\t\t\t\t\t<view class=\"flex flex-col addshow-list-view\">\r\n\t\t\t\t\t\t\t\t<view class=\"flex title-view\">\r\n\t\t\t\t\t\t\t\t\t<view>指定商品列表</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"but-class\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @click=\"addshop(0)\">添加商品</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"product\" v-if=\"productdata.length\">\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(item, index2) in productdata\" :key=\"index2\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"item flex\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"img-view\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image v-if=\"item.pic\" :src=\"item.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view v-else class=\"img-view-empty\"></view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view></view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"del-view flex-y-center\" @tap.stop=\"clearShopCartFn(item.id,0)\" style=\"color:#999999;font-size:24rpx\"><image :src=\"pre_url+'/static/img/del.png'\" style=\"width:24rpx;height:24rpx;margin-right:6rpx\"/></view>\r\n\t\t\t\t\t\t\t\t\t\t</view> \r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- 指定菜品 -->\r\n\t\t\t\t\t\t<!-- 指定服务商品 -->\r\n\t\t\t\t\t\t<view class=\"form-item\" v-if=\"info.fwtype==4\">\r\n\t\t\t\t\t\t\t<view class=\"flex flex-col addshow-list-view\">\r\n\t\t\t\t\t\t\t\t<view class=\"flex title-view\">\r\n\t\t\t\t\t\t\t\t\t<view>指定服务商品列表</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"but-class\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @click=\"addshopProgive(0)\">添加商品</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"product\" v-if=\"yuyue_product.length\">\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(item, index2) in yuyue_product\" :key=\"index2\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"item flex\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"img-view\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image v-if=\"item.pic\" :src=\"item.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view v-else class=\"img-view-empty\"></view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"del-view flex-y-center\" @tap.stop=\"clearShopCartFn2(item.id,0)\" style=\"color:#999999;font-size:24rpx\"><image :src=\"pre_url+'/static/img/del.png'\" style=\"width:24rpx;height:24rpx;margin-right:6rpx\"/></view>\r\n\t\t\t\t\t\t\t\t\t\t</view> \r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-else>\r\n\t\t\t\t  <view class=\"form-item\" v-if=\"info.type != 51\">\r\n\t\t\t\t    <view class=\"f1\">优惠金额(元)<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t    <view class=\"f2\"><input type=\"digit\" name=\"money\" :value=\"info.money\" placeholder=\"请填写优惠金额(元)\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t  </view>\r\n\t\t\t\t  <view class=\"form-item\" v-if=\"info.type == 10\">\r\n\t\t\t\t    <view class=\"f1\">折扣比例<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t    <view class=\"f2\"><input type=\"digit\" name=\"discount\" :value=\"info.discount\" placeholder=\"例如9折则填写90\" placeholder-style=\"color:#888\"></input>%</view>\r\n\t\t\t\t  </view>\r\n\t\t\t\t  <view class=\"form-item\" v-if=\"info.type != 51\">\r\n\t\t\t\t    <view class=\"f1\" style=\"width: 228rpx\">最低消费金额(元)<text style=\"color:red\"> </text></view>\r\n\t\t\t\t    <view class=\"f2\"><input type=\"text\" name=\"minprice\" :value=\"info.minprice\" placeholder=\"请填写最低消费金额(元)\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t  </view>\r\n\t\t\t\t  <view class=\"form-item flex-col\">\r\n\t\t\t\t    <view class=\"f1\">适用范围<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t    <view class=\"f2\">\r\n\t\t\t\t      <radio-group class=\"radio-group\" name=\"fwtype\" @change=\"scopeApplicationRes\">\r\n\t\t\t\t        <label><radio value=\"0\" :checked=\"info.fwtype==0?true:false\"></radio>全场通用</label>\r\n\t\t\t\t        <label style=\"margin-left: 20rpx;\"><radio value=\"1\" :checked=\"info.fwtype==1?true:false\"></radio> 指定类目</label>\r\n\t\t\t\t        <label style=\"margin-left: 20rpx;\"><radio value=\"2\" :checked=\"info.fwtype==2?true:false\"></radio> 指定菜品</label>  \r\n\t\t\t\t      </radio-group>\r\n\t\t\t\t    </view>\r\n\t\t\t\t  </view>\r\n\t\t\t\t\t<!-- 指定类目-餐饮 -->\r\n\t\t\t\t\t<view class=\"form-item\" v-if=\"info.fwtype==1\">\r\n\t\t\t\t\t\t<view class=\"flex flex-col addshow-list-view\">\r\n\t\t\t\t\t\t\t<view class=\"flex title-view\">\r\n\t\t\t\t\t\t\t\t<view>指定类目</view>\r\n\t\t\t\t\t\t\t\t<view class=\"but-class\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @click=\"restaurantClass()\">添加类目</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"product\" v-if=\"categorydata.length\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(item, index2) in categorydata\" :key=\"index2\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"item flex\"  style=\"align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"info\"  style=\"height: 40rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"del-view-class flex-y-center\" @tap.stop=\"clearShopClass(item.id)\"><image :src=\"pre_url+'/static/img/del.png'\" style=\"width:24rpx;height:24rpx;margin-right:6rpx\"/></view>\r\n\t\t\t\t\t\t\t\t\t</view> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 指定菜品-餐饮 -->\r\n\t\t\t\t\t<view class=\"form-item\" v-if=\"info.fwtype==2\">\r\n\t\t\t\t\t\t<view class=\"flex flex-col addshow-list-view\">\r\n\t\t\t\t\t\t\t<view class=\"flex title-view\">\r\n\t\t\t\t\t\t\t\t<view>指定菜品</view>\r\n\t\t\t\t\t\t\t\t<view class=\"but-class\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @click=\"restaurantShop()\">添加菜品</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"product\" v-if=\"productdata.length\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(item, index2) in productdata\" :key=\"index2\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"item flex\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"img-view\">\r\n\t\t\t\t\t\t\t\t\t\t\t<image v-if=\"item.pic\" :src=\"item.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t\t\t<view v-else class=\"img-view-empty\"></view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view></view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"del-view flex-y-center\" @tap.stop=\"clearRestaurant(item.id)\" style=\"color:#999999;font-size:24rpx\"><image :src=\"pre_url+'/static/img/del.png'\" style=\"width:24rpx;height:24rpx;margin-right:6rpx\"/></view>\r\n\t\t\t\t\t\t\t\t\t</view> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n        <block v-if=\" info.type == 3\">\r\n          <view class=\"form-item\">\r\n            <view class=\"f1\">可用次数<text style=\"color:red\"></text></view>\r\n            <view class=\"f2\"><input type=\"text\" name=\"limit_count\" :value=\"info.limit_count\" placeholder=\"每张券可以使用多少次\" placeholder-style=\"color:#888\"></input></view>\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <view class=\"f1\">每天可用<text style=\"color:red\"></text></view>\r\n            <view class=\"f2\"><input type=\"text\" name=\"limit_perday\" :value=\"info.limit_perday\" placeholder=\"每张券每天可以用多少次\" placeholder-style=\"color:#888\"></input></view>\r\n          </view>\r\n        </block>\r\n        <view class=\"form-item flex-col\">\r\n          <view class=\"f1\">使用说明<text style=\"color:red\"> </text></view>\r\n         <textarea :value=\"info.usetips\" name=\"usetips\" placeholder=\"请输入使用说明\"></textarea>\r\n        </view>\r\n      </view>\r\n      <view class=\"form-box\">\r\n        <view class=\"form-item flex-col\">\r\n          <view class=\"f1\">有效期<text style=\"color:red\"> </text></view>\r\n          <view class=\"f2\">\r\n            <radio-group class=\"radio-group\" name=\"yxqtype\" @change=\"bindYxqtypeChange\">\r\n\t\t\t\t\t\t\t<view class=\"radio-group-view\">\r\n\t\t\t\t\t\t\t\t<label><radio value=\"1\" :checked=\"info.yxqtype==1?true:false\"></radio> 固定时间范围</label>\r\n\t\t\t\t\t\t\t\t<label><radio value=\"2\" :checked=\"info.yxqtype==2?true:false\"></radio> 领取后时长</label>\r\n\t\t\t\t\t\t\t\t<label><radio value=\"3\" :checked=\"info.yxqtype==3?true:false\"></radio> 领取后时长（次日起）</label>\r\n\t\t\t\t\t\t\t</view>\r\n            </radio-group>\r\n          </view>\r\n        </view>\r\n        <view class=\"form-item flex-col\" v-if=\"info.yxqtype == 1\">\r\n          <view class=\"f1\">有效期时间</view>\r\n          <view class=\"f2\" style=\"line-height:30px\">\r\n            <picker mode=\"date\" :value=\"start_time1\" @change=\"bindStartTime1Change\">\r\n              <view class=\"picker\">{{start_time1}}</view>\r\n            </picker>\r\n            <picker mode=\"time\" :value=\"start_time2\" @change=\"bindStartTime2Change\">\r\n              <view class=\"picker\" style=\"padding-left:10rpx\">{{start_time2}}</view>\r\n            </picker>\r\n            <view style=\"padding:0 10rpx;color:#222;font-weight:bold\">到</view>\r\n            <picker mode=\"date\" :value=\"end_time1\" @change=\"bindEndTime1Change\">\r\n              <view class=\"picker\">{{end_time1}}</view>\r\n            </picker>\r\n            <picker mode=\"time\" :value=\"end_time2\" @change=\"bindEndTime2Change\">\r\n              <view class=\"picker\" style=\"padding-left:10rpx\">{{end_time2}}</view>\r\n            </picker>\r\n          </view>\r\n        </view>\r\n        <view class=\"form-item\" v-if=\"info.yxqtype==2\">\r\n          <view class=\"f1\" style=\"width: 228rpx\">领取后几天有效<text style=\"color:red\"> *</text></view>\r\n          <view class=\"f2\"><input type=\"text\" name=\"yxqdate2\" :value=\"info.yxqdate\" placeholder=\"领取后几天有效(天)\" placeholder-style=\"color:#888\"></input></view>\r\n        </view>\r\n        <view class=\"form-item\" v-if=\"info.yxqtype==3\">\r\n          <view class=\"f1\" style=\"width: 228rpx\">领取后几天有效<text style=\"color:red\"> *</text></view>\r\n          <view class=\"f2\"><input type=\"text\" name=\"yxqdate3\" :value=\"info.yxqdate\" placeholder=\"次日0点开始计算有效期\" placeholder-style=\"color:#888\"></input></view>\r\n        </view>\r\n        <view class=\"form-item flex-col\">\r\n          <view class=\"f1\" v-if=\"pageType\">领取条件</view>\r\n\t\t\t\t\t<view class=\"f1\" v-else>参与条件</view>\r\n          <view class=\"f2\" style=\"line-height:30px\">\r\n            <checkbox-group class=\"radio-group\" name=\"gettj\"  @change=\"collectionConditions\">\r\n              <label><checkbox value=\"-1\" :checked=\"inArray('-1',info.gettj)?true:false\"></checkbox> 所有人</label>\r\n              <label><checkbox value=\"0\" :checked=\"inArray('0',info.gettj)?true:false\"></checkbox> 关注用户</label>\r\n              <label v-for=\"(item,index) in memberlevel\" :key=\"item.id\">\r\n\t\t\t\t\t\t\t\t<checkbox :value=\"item.id\" :checked=\"inArray(item.id,info.gettj)?true:false\"></checkbox>\r\n\t\t\t\t\t\t\t\t{{item.name}}\r\n\t\t\t\t\t\t\t</label>\r\n            </checkbox-group>\r\n          </view>\r\n        </view>\r\n        <view class=\"form-item\">\r\n          <view class=\"f1\" style=\"width: 228rpx\">所需金额(元)<text style=\"color:red\"> </text></view>\r\n          <view class=\"f2\"><input type=\"text\" name=\"price\" :value=\"info.price\" placeholder=\"需要消耗多少钱购买(元)\" placeholder-style=\"color:#888\"></input></view>\r\n        </view>\r\n        <view class=\"form-item\">\r\n          <view class=\"f1\" style=\"width: 228rpx\">所需积分<text style=\"color:red\"> </text></view>\r\n          <view class=\"f2\"><input type=\"text\" name=\"score\" :value=\"info.score\" placeholder=\"需要消耗多少积分兑换\" placeholder-style=\"color:#888\"></input></view>\r\n        </view>\r\n        <view class=\"form-item\">\r\n          <view class=\"f1\" style=\"width: 228rpx\">库存<text style=\"color:red\"> </text></view>\r\n          <view class=\"f2\"><input type=\"text\" name=\"stock\" :value=\"info.stock\" placeholder=\"\" placeholder-style=\"color:#888\"></input></view>\r\n        </view>\r\n        <view class=\"form-item\">\r\n          <view class=\"f1\" style=\"width: 228rpx\">每人可领取数<text style=\"color:red\"> </text></view>\r\n          <view class=\"f2\"><input type=\"text\" name=\"perlimit\" :value=\"info.perlimit\" placeholder=\"每人最多可领取多少张\" placeholder-style=\"color:#888\"></input></view>\r\n        </view>\r\n        <view class=\"form-item\">\r\n          <view class=\"f1\" style=\"width: 228rpx\">开始时间<text style=\"color:red\"> </text></view>\r\n          <view class=\"f2\"><picker mode=\"date\" :value=\"starttime\" @change=\"bindStartTimeChange\">\r\n            <view class=\"picker\">{{starttime}}</view>\r\n          </picker>\r\n            <picker mode=\"time\" :value=\"starttime2\" @change=\"bindStartTimeChange2\">\r\n              <view class=\"picker\" style=\"padding-left:10rpx\">{{starttime2}}</view>\r\n            </picker></view>\r\n        </view>\r\n        <view class=\"form-item\">\r\n          <view class=\"f1\" style=\"width: 228rpx\">结束时间<text style=\"color:red\"> </text></view>\r\n          <view class=\"f2\"><picker mode=\"date\" :value=\"endtime\" @change=\"bindEndTimeChange\">\r\n            <view class=\"picker\">{{endtime}}</view>\r\n          </picker>\r\n            <picker mode=\"time\" :value=\"endtime2\" @change=\"bindEndTimeChange2\">\r\n              <view class=\"picker\" style=\"padding-left:10rpx\">{{endtime2}}</view>\r\n            </picker></view>\r\n        </view>\r\n        <view class=\"form-item\">\r\n          <view class=\"f1\" style=\"width: 228rpx\">序号<text style=\"color:red\"> </text></view>\r\n          <view class=\"f2\"><input type=\"text\" name=\"sort\" :value=\"info.sort\" placeholder=\"用于排序,越大越靠前\" placeholder-style=\"color:#888\"></input></view>\r\n        </view>\r\n      </view>\r\n      <view class=\"form-box\">\r\n        <view class=\"form-item\" v-if=\"pageType\">\r\n          <view class=\"f1\">领券中心显示<text style=\"color:red\"> </text></view>\r\n          <view class=\"f2\">\r\n            <radio-group class=\"radio-group\" name=\"tolist\" @change=\"couponCenterChange\">\r\n              <label><radio value=\"1\" :checked=\"info.tolist==1?true:false\"></radio> 是</label>\r\n              <label style=\"margin-left: 20rpx;\"><radio value=\"0\" :checked=\"info.tolist==0?true:false\"></radio> 否</label>\r\n            </radio-group>\r\n          </view>\r\n        </view>\r\n        <view class=\"form-item flex-col\" v-if=\"info.tolist==1 && pageType\">\r\n          <view class=\"f1\">显示条件</view>\r\n          <view class=\"f2\" style=\"line-height:30px\">\r\n            <checkbox-group class=\"radio-group\" name=\"showtj\" @change=\"displayConditions\">\r\n              <label><checkbox value=\"-1\" :checked=\"inArray('-1',info.showtj)?true:false\"></checkbox> 所有人</label>\r\n              <label><checkbox value=\"0\" :checked=\"inArray('0',info.showtj)?true:false\"></checkbox> 关注用户</label>\r\n              <label v-for=\"item in memberlevel\" :key=\"item.id\"><checkbox :value=\"''+item.id\" :checked=\"inArray(item.id,info.showtj)?true:false\"></checkbox> {{item.name}}</label>\r\n            </checkbox-group>\r\n          </view>\r\n        </view>\r\n        <view class=\"form-item\" v-if=\"!pageType\">\r\n          <view class=\"f1\">可直接领取<text style=\"color:red\"> </text></view>\r\n          <view class=\"f2\">\r\n            <radio-group class=\"radio-group\" name=\"tolist\" @change=\"couponCenterChange\">\r\n              <label><radio value=\"1\" :checked=\"info.tolist==1?true:false\"></radio> 是</label>\r\n              <label style=\"margin-left: 20rpx;\"><radio value=\"0\" :checked=\"info.tolist==0?true:false\"></radio> 否</label>\r\n            </radio-group>\r\n          </view>\r\n        </view>\r\n        <view class=\"form-item flex-col\">\r\n          <view class=\"f1\">使用范围<text style=\"color:red\"> </text></view>\r\n          <view class=\"f2\">\r\n            <radio-group class=\"radio-group\" name=\"isgive\" @change=\"bindStatusChange\">\r\n              <label><radio value=\"0\" :checked=\"info.isgive==0?true:false\"></radio> 仅自用</label>\r\n              <label style=\"margin-left: 20rpx;\"><radio value=\"1\" :checked=\"info.isgive==1?true:false\"></radio> 自用+转赠</label>\r\n              <label style=\"margin-left: 20rpx;\"><radio value=\"2\" :checked=\"info.isgive==2?true:false\"></radio> 仅转赠</label>\r\n            </radio-group>\r\n          </view>\r\n        </view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t  <view class=\"f1\">支付后赠送<text style=\"color:red\"> </text></view>\r\n\t\t\t\t  <view class=\"f2\">\r\n\t\t\t\t    <radio-group class=\"radio-group\" name=\"paygive\" @change=\"bindPaygiveChange\">\r\n\t\t\t\t      <label><radio value=\"0\" :checked=\"info.paygive==0?true:false\"></radio> 关闭</label>\r\n\t\t\t\t      <label style=\"margin-left: 20rpx;\"><radio value=\"1\" :checked=\"info.paygive==1?true:false\"></radio> 开启</label>\r\n\t\t\t\t    </radio-group>\r\n\t\t\t\t  </view>\r\n\t\t\t\t</view>\r\n        <view class=\"form-item flex-col\" v-if=\"info.paygive == 1\">\r\n          <view class=\"f1\">支付赠送场景</view>\r\n          <view class=\"f2\" style=\"line-height:30px\" v-if='pageType'>\r\n            <checkbox-group class=\"radio-group\" name=\"paygive_scene\" @change=\"paymentGiftChange\">\r\n              <label><checkbox value=\"shop\" :checked=\"inArray('shop',info.paygive_scene)?true:false\"></checkbox> 商城</label>\r\n              <label><checkbox value=\"scoreshop\" :checked=\"inArray('scoreshop',info.paygive_scene)?true:false\"></checkbox> 兑换</label>\r\n              <label><checkbox value=\"collage\" :checked=\"inArray('collage',info.paygive_scene)?true:false\"></checkbox> 拼团</label>\r\n              <label><checkbox value=\"kanjia\" :checked=\"inArray('kanjia',info.paygive_scene)?true:false\"></checkbox> 砍价</label>\r\n              <label><checkbox value=\"seckill\" :checked=\"inArray('seckill',info.paygive_scene)?true:false\"></checkbox> 秒杀</label>\r\n              <label><checkbox value=\"tuangou\" :checked=\"inArray('tuangou',info.paygive_scene)?true:false\"></checkbox> 团购</label>\r\n              <label><checkbox value=\"lucky_collage\" :checked=\"inArray('lucky_collage',info.paygive_scene)?true:false\"></checkbox> 幸运拼团</label>\r\n              <label><checkbox value=\"recharge\" :checked=\"inArray('recharge',info.paygive_scene)?true:false\"></checkbox> 充值</label>\r\n              <label><checkbox value=\"maidan\" :checked=\"inArray('maidan',info.paygive_scene)?true:false\"></checkbox> 买单收款</label>\r\n            </checkbox-group>\r\n          </view>\r\n\t\t\t\t\t<view class=\"f2\" style=\"line-height:30px\" v-else>\r\n\t\t\t\t\t  <checkbox-group class=\"radio-group\" name=\"paygive_scene\" @change=\"paymentGiftChange\">\r\n\t\t\t\t\t\t\t<label><checkbox value=\"restaurant\" :checked=\"inArray('restaurant',info.paygive_scene)?true:false\"></checkbox> 下单</label>\r\n\t\t\t\t\t\t\t<label><checkbox value=\"recharge\" :checked=\"inArray('recharge',info.paygive_scene)?true:false\"></checkbox> 充值</label>\r\n\t\t\t\t\t  </checkbox-group>\r\n\t\t\t\t\t</view>\r\n        </view>\r\n        <view class=\"form-item\" v-if=\"info.paygive == 1\">\r\n          <view class=\"f1\">支付金额范围</view>\r\n          <view class=\"amount-range-view flex\">\r\n\t\t\t\t\t\t<input type=\"text\" name=\"paygive_minprice\" :value=\"info.paygive_minprice\" placeholder=\"请输入金额\" placeholder-style=\"color:#888\" style=\"text-align: right;\" />\r\n\t\t\t\t\t\t<view style=\"padding: 0px 20rpx;\">-</view>\r\n\t\t\t\t\t\t<input type=\"text\" name=\"paygive_maxprice\" :value=\"info.paygive_maxprice\" placeholder=\"请输入金额\" placeholder-style=\"color:#888\" />元\r\n\t\t\t\t\t</view>\r\n        </view>\r\n        <view class=\"form-item\" v-if=\"pageType\">\r\n          <view class=\"f1\">购买商品赠送<text style=\"color:red\"> </text></view>\r\n          <view class=\"f2\">\r\n            <radio-group class=\"radio-group\" name=\"buyprogive\" @change=\"bindBuyprogiveChange\">\r\n              <label><radio value=\"0\" :checked=\"info.buyprogive==0?true:false\"></radio> 关闭</label>\r\n              <label style=\"margin-left: 20rpx;\"><radio value=\"1\" :checked=\"info.buyprogive==1?true:false\"></radio> 开启</label>\r\n            </radio-group>\r\n          </view>\r\n        </view>\r\n\t\t\t\t<view class=\"form-item\" v-if=\"info.buyprogive == 1\">\r\n\t\t\t\t\t<view class=\"flex flex-col addshow-list-view\">\r\n\t\t\t\t\t\t<view class=\"flex title-view\">\r\n\t\t\t\t\t\t\t<view>商品列表</view>\r\n\t\t\t\t\t\t\t<view class=\"but-class\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @click=\"addshop(1)\">添加商品</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"product\" v-if=\"giftProductsList.length\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item, index2) in giftProductsList\" :key=\"index2\">\r\n\t\t\t\t\t\t\t\t<view class=\"item flex\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"img-view\">\r\n\t\t\t\t\t\t\t\t\t\t<image v-if=\"item.pic\" :src=\"item.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<view v-else class=\"img-view-empty\"></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"modify-price flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\">赠送数量：</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<input type=\"digit\" v-model=\"item.give_num\" class=\"inputPrice\" @input=\"inputNumChange($event,0)\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"del-view flex-y-center\" @tap.stop=\"clearShopCartFn(item.id,1)\" style=\"color:#999999;font-size:24rpx\"><image :src=\"pre_url+'/static/img/del.png'\" style=\"width:24rpx;height:24rpx;margin-right:6rpx\"/></view>\r\n\t\t\t\t\t\t\t\t</view> \r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n        <view class=\"form-item\" v-if=\"pageType\">\r\n          <view class=\"f1\" style=\"width: 250rpx\">购买服务商品赠送<text style=\"color:red\"> </text></view>\r\n          <view class=\"f2\">\r\n            <radio-group class=\"radio-group\" name=\"buyyuyueprogive\" @change=\"bindBuyyuyueprogiveChange\">\r\n              <label><radio value=\"0\" :checked=\"info.buyyuyueprogive==0?true:false\"></radio> 关闭</label>\r\n              <label style=\"margin-left: 20rpx;\"><radio value=\"1\" :checked=\"info.buyyuyueprogive==1?true:false\"></radio> 开启</label>\r\n            </radio-group>\r\n          </view>\r\n        </view>\r\n\t\t\t\t<view class=\"form-item\" v-if=\"info.buyyuyueprogive == 1\">\r\n\t\t\t\t\t<view class=\"flex flex-col addshow-list-view\">\r\n\t\t\t\t\t\t<view class=\"flex title-view\">\r\n\t\t\t\t\t\t\t<view>服务商品列表</view>\r\n\t\t\t\t\t\t\t<view class=\"but-class\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @click=\"addshopProgive(1)\">添加商品</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"product\" v-if=\"giftProductsLists.length\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item, index2) in giftProductsLists\" :key=\"index2\">\r\n\t\t\t\t\t\t\t\t<view class=\"item flex\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"img-view\">\r\n\t\t\t\t\t\t\t\t\t\t<image v-if=\"item.pic\" :src=\"item.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<view v-else class=\"img-view-empty\"></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"modify-price flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f2\">赠送数量：</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<input type=\"digit\" v-model=\"item.give_num\" class=\"inputPrice\" @input=\"inputNumChange($event,1)\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"del-view flex-y-center\" @tap.stop=\"clearShopCartFn2(item.id,1)\" style=\"color:#999999;font-size:24rpx\"><image :src=\"pre_url+'/static/img/del.png'\" style=\"width:24rpx;height:24rpx;margin-right:6rpx\"/></view>\r\n\t\t\t\t\t\t\t\t</view> \r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n      </view>\r\n      <view class=\"form-box\" v-if=\"pageType\">\r\n        <view class=\"form-item\">\r\n          <view class=\"f1\" style=\"width: 228rpx\">字体颜色<text style=\"color:red\"> </text></view>\r\n          <view class=\"f2\"><input type=\"text\" name=\"font_color\" :value=\"info.font_color\" placeholder=\"如：#2B2B2B\" placeholder-style=\"color:#888\"></input></view>\r\n        </view>\r\n        <view class=\"form-item\">\r\n          <view class=\"f1\" style=\"width: 228rpx\">标题颜色<text style=\"color:red\"> </text></view>\r\n          <view class=\"f2\"><input type=\"text\" name=\"title_color\" :value=\"info.title_color\" placeholder=\"如：#2B2B2B\" placeholder-style=\"color:#888\"></input></view>\r\n        </view>\r\n        <view class=\"form-item\">\r\n          <view class=\"f1\" style=\"width: 228rpx\">背景颜色<text style=\"color:red\"> </text></view>\r\n          <view class=\"f2\"><input type=\"text\" name=\"bg_color\" :value=\"info.bg_color\" placeholder=\"如：#FFFFFF\" placeholder-style=\"color:#888\"></input></view>\r\n        </view>\r\n      </view>\r\n\t\t\t<!-- 编辑 & 添加 -->\r\n\t\t\t<button class=\"savebtn\" :style=\"'background:linear-gradient(90deg,'+tColor('color1')+' 0%,rgba('+tColor('color1rgb')+',0.8) 100%)'\" form-type=\"submit\">提交</button>\r\n\t\t\t<view style=\"height:50rpx\"></view>\r\n\t\t</form>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n</view>\r\n</template>\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\tisload:true,\r\n\t\t\tloading:false,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n      info:{},\r\n\t\t\tmemberlevel:[],\r\n\t\t\tpic:[],\r\n\t\t\tstart_time1:'-选择日期-',\r\n\t\t\tstart_time2:'-选择时间-',\r\n\t\t\tend_time1:'-选择日期-',\r\n\t\t\tend_time2:'-选择时间-',\r\n\t\t\tendtime:'-选择日期-',\r\n\t\t\tendtime2:'-选择时间-',\r\n\t\t\tstarttime:'-选择日期-',\r\n\t\t\tstarttime2:'-选择时间-',\r\n\t\t\tproduct_showset:0,\r\n\t\t\tshowtjArr:['-1'],\r\n      zhif:0,\r\n\t\t\tgiftProductsList:[],\r\n\t\t\tgiftServiceList:[],\r\n\t\t\tbuyproGiveNum:[],\r\n\t\t\tgiftProductsLists:[],\r\n\t\t\tpageType:false,\r\n\t\t\tbid:0,\r\n\t\t\tauth:[],\r\n\t\t\trestaurant:false,\r\n\t\t\teditType:'',\r\n\t\t\tproductdata:[],//适用范围-指定商品\r\n\t\t\taddshopType:0,//添加商品类型\r\n\t\t\tyuyue_product:[],//适用范围-指定服务商品\r\n\t\t\taddfuwushopType:0,\r\n\t\t\tcategorydata:[],//适用范围-指定类目\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.editType = opt.type;\r\n\t\tif(opt.type == 1){ //优惠券详情\r\n\t\t\tthis.pageType = true;\r\n\t\t\tthis.getdata('ApiAdminCoupon/edit',opt.type);\r\n\t\t\tthis.subformUrl = 'ApiAdminCoupon/save';\r\n\t\t}else if(opt.type == 0){  //餐饮优惠券详情\r\n\t\t\tthis.getdata('ApiAdminRestaurantCoupon/edit',opt.type);\r\n\t\t\tthis.subformUrl = 'ApiAdminRestaurantCoupon/save';\r\n\t\t}else if(opt.type == 2){ //添加优惠券详情\r\n\t\t\tthis.pageType = true;\r\n\t\t\tthis.getdata('ApiAdminCoupon/edit',opt.type);\r\n\t\t\tthis.subformUrl = 'ApiAdminCoupon/save';\r\n\t\t\tlet params = {\r\n\t\t\t\ttype:'1',\r\n\t\t\t\tgettj:['-1'],\r\n\t\t\t\tyxqtype:'1',\r\n\t\t\t\tfwtype:0,\r\n\t\t\t\ttolist:1,\r\n\t\t\t\tisgive:0,\r\n\t\t\t\tpaygive:0,\r\n\t\t\t\tbuyprogive:0,\r\n\t\t\t\tbuyyuyueprogive:0,\r\n\t\t\t\tstock:100,\r\n\t\t\t\tperlimit:1,\r\n\t\t\t\tshowtj:[-1]\r\n\t\t\t}\r\n\t\t\tthis.info = params;\r\n\t\t\tthis.initTime();\r\n\t\t}else if(opt.type == 3){ //添加餐饮优惠券详情\r\n\t\t\tthis.getdata('ApiAdminRestaurantCoupon/edit',opt.type);\r\n\t\t\tthis.subformUrl = 'ApiAdminRestaurantCoupon/save';\r\n\t\t\tlet params = {\r\n\t\t\t\ttype:5,\r\n\t\t\t\tgettj:['-1'],\r\n\t\t\t\tfwtype:0,\r\n\t\t\t\tyxqtype:'1',\r\n\t\t\t\ttolist:'1',\r\n\t\t\t\tisgive:0,\r\n\t\t\t\tpaygive:0,\r\n\t\t\t\tbuyprogive:0,\r\n\t\t\t\tbuyyuyueprogive:0,\r\n\t\t\t\tstock:'100',\r\n\t\t\t\tperlimit:'1',\r\n\t\t\t\tshowtj:[-1]\r\n\t\t\t}\r\n\t\t\tthis.info = params;\r\n\t\t\tthis.initTime();\r\n\t\t}\r\n\t\tif(opt.type == 1 || opt.type == 2){\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: this.t('优惠券')+'设置'\r\n\t\t\t});\r\n\t\t}else{\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: '餐饮优惠券设置'\r\n\t\t\t});\r\n\t\t}\r\n  },\r\n\tonShow() {\r\n\t\tuni.$off();\r\n\t\tlet that = this;\r\n\t\tuni.$once('shopDataEmit',function(e){\r\n\t\t\tif(that.addshopType){\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tlet nowArr = that.giftProductsList.map(item => item.id);\r\n\t\t\t\t\tif(nowArr.includes(e.id)) {\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.showToast({icon:'none',title:'该商品已添加过了'}) ;\r\n\t\t\t\t\t\t},600);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.giftProductsList.push(e);\r\n\t\t\t\t\tlet idArr = that.giftProductsList.map(item => item.id);\r\n\t\t\t\t\tthat.info.buyproids = idArr.join(',');\r\n\t\t\t\t\tthat.info.buypro_give_num = that.giftProductsList.map(item => item.give_num);\r\n\t\t\t\t})\r\n\t\t\t}else{\r\n\t\t\t\t//指定商品列表\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tlet nowArr = that.productdata.map(item => item.id);\r\n\t\t\t\t\tif(nowArr.includes(e.id)) {\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.showToast({icon:'none',title:'该商品已添加过了'}) ;\r\n\t\t\t\t\t\t},600);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.productdata.push(e);\r\n\t\t\t\t\tlet idArr = that.productdata.map(item => item.id);\r\n\t\t\t\t\tthat.info.productids = idArr.join(',');\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t})\r\n\t\tuni.$once('shopDataEmitS',function(e){\r\n\t\t\tif(that.addfuwushopType){\r\n\t\t\t\tlet nowArr = that.giftProductsLists.map(item => item.id);\r\n\t\t\t\tif(nowArr.includes(e.id)) {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.showToast({icon:'none',title:'该商品已添加过了'}) ;\r\n\t\t\t\t\t},600)\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthat.giftProductsLists.push(e);\r\n\t\t\t\tlet idArr = that.giftProductsLists.map(item => item.id);\r\n\t\t\t\tthat.info.buyyuyueproids = idArr.join(',');\r\n\t\t\t\tthat.info.buyyuyuepro_give_num = that.giftProductsLists.map(item => item.give_num);\r\n\t\t\t}else{\r\n\t\t\t\tlet nowArr = that.yuyue_product.map(item => item.id);\r\n\t\t\t\tif(nowArr.includes(e.id)) {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.showToast({icon:'none',title:'该服务商品已添加过了'}) ;\r\n\t\t\t\t\t},600)\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthat.yuyue_product.push(e);\r\n\t\t\t}\r\n\t\t})\r\n\t\t// 普通优惠券--添加分类\r\n\t\tuni.$once('shopDataClass',function(e){\r\n\t\t\tlet nowArr = that.categorydata.map(item => item.id);\r\n\t\t\tif(nowArr.includes(e.id)) {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.showToast({icon:'none',title:'该分类已添加过了'}) ;\r\n\t\t\t\t},600)\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tthat.categorydata.push(e);\r\n\t\t\tlet idArr = that.categorydata.map(item => item.id);\r\n\t\t\tthat.info.categoryids = idArr.join(',');\r\n\t\t})\r\n\t},\t\r\n\tonUnload(){\r\n\t\t\r\n\t},\r\n  methods: {\r\n\t\t// 修改商品数量事件\r\n\t\tinputNumChange(e,type){\r\n\t\t\tlet that = this;\r\n\t\t\tif(type == 0){ //购买赠送商品列表\r\n\t\t\t\tthat.info.buypro_give_num = that.giftProductsList.map(item => item.give_num);\r\n\t\t\t}else if(type == 1){ //购买赠送服务商品列表\r\n\t\t\t\tthat.info.buyyuyuepro_give_num = that.giftProductsLists.map(item => item.give_num);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\ttColor(text){\r\n\t\t\t\t\tlet that = this;\r\n\t\t\t\t\tif(text=='color1'){\r\n\t\t\t\t\t\tif(app.globalData.initdata.color1 == undefined){\r\n\t\t\t\t\t\t\tlet timer = setInterval(() => {\r\n\t\t\t\t\t\t\t\tthat.tColor('color1')\r\n\t\t\t\t\t\t\t},1000)\r\n\t\t\t\t\t\t\tclearInterval(timer)\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\treturn app.globalData.initdata.color1;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else if(text=='color2'){\r\n\t\t\t\t\t\treturn app.globalData.initdata.color2;\r\n\t\t\t\t\t}else if(text=='color1rgb'){\r\n\t\t\t\t\t\tif(app.globalData.initdata.color1rgb == undefined){\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tthat.tColor('color1rgb')\r\n\t\t\t\t\t\t\t},1000)\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tvar color1rgb = app.globalData.initdata.color1rgb;\r\n\t\t\t\t\t\t\treturn color1rgb['red']+','+color1rgb['green']+','+color1rgb['blue'];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else if(text=='color2rgb'){\r\n\t\t\t\t\t\tvar color2rgb = app.globalData.initdata.color2rgb;\r\n\t\t\t\t\t\treturn color2rgb['red']+','+color2rgb['green']+','+color2rgb['blue'];\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\treturn app.globalData.initdata.textset[text] || text;\r\n\t\t\t\t\t}\r\n\t\t\t},\r\n\t\t// 显示条件\r\n\t\tdisplayConditions(e){\r\n\t\t\tthis.info.showtj = e.detail.value;\r\n\t\t},\r\n\t\t// 支付赠送场景\r\n\t\tpaymentGiftChange(e){\r\n\t\t\tthis.info.paygive_scene = e.detail.value;\r\n\t\t},\r\n\t\t// 领取条件\r\n\t\tcollectionConditions:function(e){\r\n\t\t\tthis.info.gettj = e.detail.value;\r\n\t\t},\r\n\t\t// 初始化时间\r\n\t\tinitTime(){\r\n\t\t\tlet dateww = new Date();\r\n\t\t\tlet startTime = app.dateFormat(dateww.getTime()/1000);\r\n\t\t\tthis.start_time1 = startTime.split(' ')[0];\r\n\t\t\tthis.start_time2 = this.end_time2 = this.starttime2 = this.endtime2 = '00:00:00';\r\n\t\t\tlet date = new Date(dateww);\r\n\t\t\tdate.setDate(date.getDate() + 7); \r\n\t\t\tlet endTime = app.dateFormat(date.getTime()/1000);\r\n\t\t\tthis.end_time1 = endTime.split(' ')[0];\r\n\t\t\tthis.starttime = startTime.split(' ')[0];\r\n\t\t\tthis.endtime = endTime.split(' ')[0];\r\n\t\t\t// 有效时间期\r\n\t\t\tthis.info.yxqtime = this.start_time1 + ' ' + this.start_time2 + ' ~ ' + this.end_time1 + ' ' + this.end_time2;\r\n\t\t\tthis.info.starttime = this.starttime + ' ' + this.starttime2;\r\n\t\t\tthis.info.endtime = this.endtime + ' ' + this.endtime2;\r\n\t\t},\r\n\t\t// 添加菜品\r\n\t\trestaurantShop(){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:'/admin/restaurant/product/index?coupon=1'+'&bid='+this.bid\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 添加类目-普通优惠券\r\n\t\taddshopClass(type){\r\n\t\t\tif(type == 1){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:'category'\r\n\t\t\t\t})\r\n\t\t\t}else{\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:'category?bid=' + this.bid\r\n\t\t\t\t})\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\t// 添加菜品分类\r\n\t\trestaurantClass(){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:'/admin/restaurant/category/index?coupont=1'+'&bid=' + this.bid\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 删除菜品\r\n\t\tclearRestaurant(id){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: '确认删除菜品吗？',\r\n\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tlet ArrarList  = that.productdata.filter(item => item.id != id);\r\n\t\t\t\t\t\tthat.productdata = ArrarList;\r\n\t\t\t\t\t\tlet idArr = that.productdata.map(item => item.id);\r\n\t\t\t\t\t\tthat.info.productids = idArr.join(',');\r\n\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 删除指定类目-餐饮-普通\r\n\t\tclearShopClass(id){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: '确认删除分类吗？',\r\n\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tlet ArrarList  = that.categorydata.filter(item => item.id != id);\r\n\t\t\t\t\t\tthat.categorydata = ArrarList;\r\n\t\t\t\t\t\tlet idArr = that.categorydata.map(item => item.id);\r\n\t\t\t\t\t\tthat.info.categoryids = idArr.join(',');\r\n\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 适用范围-餐饮\r\n\t\tscopeApplicationRes(e){\r\n\t\t\tthis.info.fwtype = e.detail.value;\r\n\t\t},\r\n\t\t// 适用范围\r\n\t\tscopeApplication(e){\r\n\t\t\tthis.info.fwtype = e.detail.value;\r\n\t\t},\r\n\t\tclearShopCartFn: function (id,type) {\r\n\t\t  var that = this;\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: '确认删除商品吗？',\r\n\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tif(type){\r\n\t\t\t\t\t\t\tlet ArrarList  = that.giftProductsList.filter(item => item.id != id);\r\n\t\t\t\t\t\t\tthat.giftProductsList = ArrarList;\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tlet ArrarList  = that.productdata.filter(item => item.id != id);\r\n\t\t\t\t\t\t\tthat.productdata = ArrarList;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tclearShopCartFn2: function (id,type) {\r\n\t\t  var that = this;\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: '确认删除商品吗？',\r\n\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tif(type){\r\n\t\t\t\t\t\t\tlet ArrarList  = that.giftProductsLists.filter(item => item.id != id);\r\n\t\t\t\t\t\t\tthat.giftProductsLists = ArrarList;\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tlet ArrarList  = that.yuyue_product.filter(item => item.id != id);\r\n\t\t\t\t\t\t\tthat.yuyue_product = ArrarList;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 添加服务商品\r\n\t\taddshopProgive(type){\r\n\t\t\tthis.addfuwushopType = type;\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:'prolist?bid=' + this.bid\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 添加普通商品\r\n\t\taddshop(type){\r\n\t\t\tthis.addshopType = type;\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:'/adminBusiness/order/dkfastbuy?coupon=1'+'&bid='+this.bid\r\n\t\t\t})\r\n\t\t},\r\n\t\tgetdata:function(URL,type){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = that.opt.id ? that.opt.id : '';\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get(URL,{id:id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif(type == 2 || type == 3){ //添加优惠券\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.info = res.info;\r\n\t\t\t\t\t// 固定时间范围\r\n\t\t\t\t\tif(that.info.yxqtime){\r\n\t\t\t\t\t\tlet yxqtime_time = that.info.yxqtime.split('~');\r\n\t\t\t\t\t\tlet start_times = yxqtime_time[0];\r\n\t\t\t\t\t\tthat.start_time1 = start_times.split(' ')[0];\r\n\t\t\t\t\t\tthat.start_time2 = start_times.split(' ')[1];\r\n\t\t\t\t\t\tlet end_times = yxqtime_time[1];\r\n\t\t\t\t\t\tthat.end_time1 = end_times.split(' ')[1];\r\n\t\t\t\t\t\tthat.end_time2 = end_times.split(' ')[2];\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 开始时间 & 结束时间\r\n\t\t\t\t\tif(that.info.starttime){\r\n\t\t\t\t\t\tthat.starttime = that.info.starttime.split(' ')[0];\r\n\t\t\t\t\t\tthat.starttime2 = that.info.starttime.split(' ')[1];\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.info.endtime){\r\n\t\t\t\t\t\tthat.endtime = that.info.endtime.split(' ')[0];\r\n\t\t\t\t\t\tthat.endtime2 = that.info.endtime.split(' ')[1];\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 商品赠送列表\r\n\t\t\t\t\tif(res.productdata2){\r\n\t\t\t\t\t\tthat.giftProductsList = res.productdata2;\r\n\t\t\t\t\t\tthat.buyproGiveNum = res.info.buypro_give_num;\r\n\t\t\t\t\t\tthat.giftProductsList.forEach((item,index) => {\r\n\t\t\t\t\t\t\titem.give_num = that.buyproGiveNum[index]\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 服务商品列表\r\n\t\t\t\t\tif(res.yuyue_products2){\r\n\t\t\t\t\t\tthat.giftProductsLists = res.yuyue_products2;\r\n\t\t\t\t\t\t// that.buyproGiveNum = res.info.buyyuyuepro_give_num;\r\n\t\t\t\t\t\tthat.giftProductsLists.forEach((item,index) => {\r\n\t\t\t\t\t\t\titem.give_num = res.info.buyyuyuepro_give_num[index]\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 其他判断条件\r\n\t\t\t\tthat.bid = res.bid ? res.bid:0;\r\n\t\t\t\tthat.auth = res.auth;\r\n\t\t\t\tthat.restaurant = res.restaurant;\r\n\t\t\t\t// 领取条件\r\n\t\t\t\tthat.memberlevel = res.memberlevel;\r\n\t\t\t\tthat.showtjArr = that.info.showtj\r\n\t\t\t\t// 适用范围-指定商品\r\n\t\t\t\tif(res.productdata){\r\n\t\t\t\t\tthat.productdata = res.productdata\r\n\t\t\t\t}\r\n\t\t\t\t// 适用范围-指定服务商品\r\n\t\t\t\tif(res.yuyue_product){\r\n\t\t\t\t\tthat.yuyue_product = res.yuyue_product\r\n\t\t\t\t}\r\n\t\t\t\t// 适用范围-指定类目\r\n\t\t\t\tif(res.categorydata){\r\n\t\t\t\t\tthat.categorydata = res.categorydata\r\n\t\t\t\t}\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n    subform: function (e) {\r\n      var that = this;\r\n      var formdata = e.detail.value;\r\n\t\t\tif(!formdata.name) return app.alert('请填写名称');\r\n\t\t\tswitch(that.info.type){ //必填条件判断优惠券类型\r\n\t\t\t\tcase '1':\r\n\t\t\t\tif(!formdata.money) return app.alert('请填写金额');\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n      var id = that.opt.id ? that.opt.id : '';\r\n\t\t\tformdata.id = id;\r\n\t\t\tif(this.editType == 3){\r\n\t\t\t\tformdata.type = this.info.type;\r\n\t\t\t}\r\n\t\t\tthat.info.name = formdata.name;\r\n\t\t\tthat.info.money = formdata.money;\r\n\t\t\tthat.info.yxqtime = that.start_time1 + ' ' + that.start_time2 + ' ~ ' + that.end_time1 + ' ' + that.end_time2;\r\n\t\t\tthat.info.starttime = that.starttime + ' ' + that.starttime2;\r\n\t\t\tthat.info.endtime = that.endtime + ' ' + that.endtime2;\r\n\t\t\tthat.info.usetips = formdata.usetips;\r\n\t\t\tthat.info.price =  formdata.price || 0;\r\n\t\t\tthat.info.score = formdata.score || 0;\r\n\t\t\tthat.info.sort = formdata.sort;\r\n\t\t\tthat.info.perlimit = formdata.perlimit;\r\n\t\t\tthat.info.minprice = formdata.minprice || 0;\r\n\t\t\tthat.info.stock = formdata.stock;\r\n\t\t\tthat.info.yxqdate2 = formdata.yxqdate2;\r\n\t\t\tthat.info.yxqdate3 = formdata.yxqdate3;\r\n\t\t\tthat.info.font_color = formdata.font_color;\r\n\t\t\tthat.info.title_color = formdata.title_color;\r\n\t\t\tthat.info.bg_color = formdata.bg_color;\r\n\t\t\tthat.info.paygive_minprice = formdata.paygive_minprice || 0;\r\n\t\t\tthat.info.paygive_maxprice = formdata.paygive_maxprice || 9999;\r\n\t\t\tthat.info.limit_count = formdata.limit_count;\r\n\t\t\tthat.info.limit_perday = formdata.limit_perday || 0;\r\n\t\t\tthat.info.discount = formdata.discount;\r\n      app.post(that.subformUrl, {info:that.info}, function (res) {\r\n        if (res.status == 1) {\r\n\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t uni.navigateBack({\r\n\t\t\t\t\t\t delta:1\r\n\t\t\t\t\t })\r\n\t\t\t\t\t}, 1000);\r\n        } else {\r\n\t\t\t\t\tapp.error(res.msg);\r\n        }\r\n      });\r\n    },\r\n\t\tbindBuyyuyueprogiveChange(e){\r\n\t\t\tthis.info.buyyuyueprogive = e.detail.value;\r\n\t\t},\r\n\t\tbindBuyprogiveChange(e){\r\n\t\t\tthis.info.buyprogive = e.detail.value;\r\n\t\t},\r\n    bindYxqtypeChange:function(e){\r\n      this.$set(this.info,'yxqtype',e.detail.value);\r\n    },\r\n\t\tcouponCenterChange:function(e){\r\n\t\t\tthis.info.tolist = e.detail.value\r\n\t\t},\r\n\t\tbindStatusChange:function(e){ //使用范围\r\n      this.info.isgive = e.detail.value;\r\n\t\t},\r\n\t\tbindStartTimeChange:function(e){\r\n\t\t\tthis.starttime = e.target.value\r\n\t\t},\r\n\t\tbindStartTime1Change:function(e){\r\n\t\t\tthis.start_time1 = e.target.value\r\n\t\t},\r\n\t\tbindStartTimeChange2:function(e){\r\n\t\t\tthis.starttime2 = e.target.value\r\n\t\t},\r\n\t\tbindEndTimeChange:function(e){\r\n\t\t\tthis.endtime = e.target.value\r\n\t\t},\r\n\t\tbindEndTimeChange2:function(e){\r\n\t\t\tthis.endtime2 = e.target.value\r\n\t\t},\r\n\t\tbindStartTime2Change:function(e){\r\n\t\t\tthis.start_time2 = e.target.value\r\n\t\t},\r\n\t\tbindEndTime1Change:function(e){\r\n\t\t\tthis.end_time1 = e.target.value\r\n\t\t},\r\n\t\tbindEndTime2Change:function(e){\r\n\t\t\tthis.end_time2 = e.target.value\r\n\t\t},\r\n    bindPaygiveChange:function(e){\r\n      this.$set(this.info,'paygive',e.detail.value);\r\n    },\r\n\t\t// 选择优惠券类型\r\n    bindTypeChange:function(e){\r\n\t\t\tswitch(e.detail.value){\r\n\t\t\t\tcase '3': //计次券默认参数\r\n\t\t\t\tthis.$set(this.info,'limit_count',1);\r\n\t\t\t\tthis.$set(this.info,'limit_perday',0);\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n      this.$set(this.info,'type',e.detail.value);\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style>\r\nradio{transform: scale(0.6);}\r\ncheckbox{transform: scale(0.6);}\r\n.form-box{ padding:2rpx 20rpx 0 20rpx; background: #fff;margin: 20rpx;border-radius: 10rpx}\r\n.form-item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }\r\n.form-item .f1{color:#222;width:200rpx;flex-shrink:0}\r\n.form-item .f2{display:flex;align-items:center}\r\n.form-box .form-item:last-child{ border:none}\r\n.form-box .flex-col{padding-bottom:20rpx}\r\n.form-item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}\r\n.form-item textarea{ width:100%;min-height:100rpx;border: none;}\r\n.form-item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }\r\n.form-item .upload_pic image{ width: 32rpx;height: 32rpx; }\r\n.form-item .but-class{width: 150rpx;height: 50rpx;line-height: 50rpx;color: #fff;text-align: center;font-size: 24rpx;border-radius:35rpx;background: #999;}\r\n.savebtn{ width: 90%; height:96rpx; line-height: 96rpx; text-align:center;border-radius:48rpx; color: #fff;font-weight:bold;margin: 0 5%; margin-top:60rpx; border: none;background: red;}\r\n.addshow-list-view{width: 100%;}\r\n.addshow-list-view .title-view{flex: 1;justify-content: space-between;align-items: center;}\r\n.addshow-list-view .product {width: 100%;}\r\n.addshow-list-view .product .item {position: relative;width: 100%;padding: 0rpx 0 20rpx;align-items: center;}\r\n.addshow-list-view .product .img-view{width: 140rpx;height: 140rpx;border-radius: 10rpx;overflow: hidden;}\r\n.addshow-list-view .product .img-view .img-view-empty{width: 100%;height: 100%;background: #eee;}\r\n.addshow-list-view .product .img-view image {width: 100%;height: 100%;}\r\n.addshow-list-view .product .info .modify-price{padding: 0rpx 0rpx;}\r\n.product .info .modify-price .inputPrice {border: 1px solid #ddd; width: 200rpx; height: 40rpx; border-radius: 10rpx; padding: 0 4rpx;text-align: left;}\r\n.addshow-list-view .product .del-view{position: absolute;right: 10rpx;top: 50%;margin-top: -7px;padding: 10rpx;}\r\n.addshow-list-view .product .del-view-class{padding: 10rpx;color:#999999;font-size:24rpx}\r\n.addshow-list-view .product .info {padding-left: 20rpx;flex: 1;height:140rpx;}\r\n.addshow-list-view .product .info .f1 {color: #222222;font-weight: bold;font-size: 24rpx;line-height: 32rpx;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;\r\nwidth: 96%;}\r\n.addshow-list-view .product .info .f2 {color: #999999;font-size: 24rpx;white-space: nowrap;}\r\n.amount-range-view{align-items: center;justify-content: flex-end;}\r\n.amount-range-view input{text-align: left;}\r\n.radio-group-view{display: flex;align-items: center;flex-wrap: wrap;justify-content: flex-start;}\r\n.radio-group-view label{white-space: nowrap;margin-right: 20rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213038097\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}