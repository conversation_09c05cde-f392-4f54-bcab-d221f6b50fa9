{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/hexiao/hexiao.vue?4672", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/hexiao/hexiao.vue?928f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/hexiao/hexiao.vue?c1cd", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/hexiao/hexiao.vue?c5e4", "uni-app:///admin/hexiao/hexiao.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/hexiao/hexiao.vue?2e3f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/hexiao/hexiao.vue?5fcc"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "pre_url", "hxnum", "co", "type", "order", "nodata", "nomore", "hexiao_status", "hexiao_type", "mendian_no_select", "mendians", "mdid", "onLoad", "console", "onPullDownRefresh", "methods", "getdata", "that", "app", "<PERSON><PERSON><PERSON>", "tip", "op", "<PERSON><PERSON><PERSON><PERSON>", "uni", "success", "hxplus", "hxminus", "hxinput", "radioChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpRA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACouBx1B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAC;IACA;IACA;IACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACA;MACA;MACAC;QAAAf;QAAAD;QAAAD;MAAA;QACAgB;QACA;UACAC;YACAA;UACA;UACA;QACA;QACA;UACAA;YACAA;UACA;UACA;QACA;QACAD;QACA;UACAA;QACA;QACAA;QACAA;QACAA;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;MACAF;QACAA;QACAA;UAAAG;UAAAlB;UAAAD;UAAAD;UAAAU;QAAA;UACAO;UACA;YACAA;YAAA;UACA;UACA;YACAA;YACAD;UACA;YACAC;cACAA;YACA;UACA;QAEA;MACA;IACA;IACAI;MACA;MACA;QACAJ;QAAA;MACA,6CA+EA;QAEAK;UACAC;YACAX;YACA;YACA;YAEA;cACA;cAEA;gBACA;kBACA;kBACA;oBACA;sBACA;oBACA;oBACA;sBACA;oBACA;oBACA;sBACA;oBACA;kBACA;gBACA;gBAEA;kBACA;kBACA;oBACA;sBACA;oBACA;oBACA;sBACA;oBACA;oBACA;sBACA;oBACA;kBACA;gBACA;gBAEA;kBACA;kBACA;oBACA;sBACA;oBACA;oBACA;sBACA;oBACA;oBACA;sBACA;oBACA;kBACA;gBACA;gBAEA;kBACAI;kBACAA;kBACA;oBACAA;kBACA;kBACAA;kBACAA;gBACA;kBACAC;gBACA;cACA;gBACAA;cACA;YAEA;cACAA;YACA;UACA;QACA;MAEA;IACA;IACA;IACAO;MACA;MACA;MACA;QACAP;QACA;MACA;MACA;IACA;IACA;IACAQ;MACA;MACA;QACAzB;MACA;MACA;IACA;IACA0B;MACA;MACAd;MACA;MACA;MACA;QACAK;QACA;MACA;MACA;IACA;IACAU;MACA;MACA;MACAX;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtgCA;AAAA;AAAA;AAAA;AAAirC,CAAgB,imCAAG,EAAC,C;;;;;;;;;;;ACArsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/hexiao/hexiao.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/hexiao/hexiao.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./hexiao.vue?vue&type=template&id=67c79511&\"\nvar renderjs\nimport script from \"./hexiao.vue?vue&type=script&lang=js&\"\nexport * from \"./hexiao.vue?vue&type=script&lang=js&\"\nimport style0 from \"./hexiao.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/hexiao/hexiao.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hexiao.vue?vue&type=template&id=67c79511&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    _vm.isload &&\n    !_vm.hexiao_status &&\n    (_vm.type == \"shop\" ||\n      _vm.type == \"collage\" ||\n      _vm.type == \"lucky_collage\" ||\n      _vm.type == \"kanjia\" ||\n      _vm.type == \"scoreshop\" ||\n      _vm.type == \"cycle\" ||\n      _vm.type == \"restaurant_shop\" ||\n      _vm.type == \"restaurant_takeaway\" ||\n      _vm.type == \"tuangou\" ||\n      _vm.type == \"seckill\")\n      ? _vm.__map(_vm.order.prolist, function (item, idx) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.type == \"scoreshop\" ? _vm.t(\"积分\") : null\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var m1 =\n    _vm.isload &&\n    !_vm.hexiao_status &&\n    (_vm.type == \"shop\" ||\n      _vm.type == \"collage\" ||\n      _vm.type == \"lucky_collage\" ||\n      _vm.type == \"kanjia\" ||\n      _vm.type == \"scoreshop\" ||\n      _vm.type == \"cycle\" ||\n      _vm.type == \"restaurant_shop\" ||\n      _vm.type == \"restaurant_takeaway\" ||\n      _vm.type == \"tuangou\" ||\n      _vm.type == \"seckill\")\n      ? _vm.t(\"会员\")\n      : null\n  var m2 =\n    _vm.isload &&\n    !_vm.hexiao_status &&\n    (_vm.type == \"shop\" ||\n      _vm.type == \"collage\" ||\n      _vm.type == \"lucky_collage\" ||\n      _vm.type == \"kanjia\" ||\n      _vm.type == \"scoreshop\" ||\n      _vm.type == \"cycle\" ||\n      _vm.type == \"restaurant_shop\" ||\n      _vm.type == \"restaurant_takeaway\" ||\n      _vm.type == \"tuangou\" ||\n      _vm.type == \"seckill\") &&\n    _vm.order.status > 1 &&\n    _vm.order.send_time\n      ? _vm.dateFormat(_vm.order.send_time)\n      : null\n  var m3 =\n    _vm.isload &&\n    !_vm.hexiao_status &&\n    (_vm.type == \"shop\" ||\n      _vm.type == \"collage\" ||\n      _vm.type == \"lucky_collage\" ||\n      _vm.type == \"kanjia\" ||\n      _vm.type == \"scoreshop\" ||\n      _vm.type == \"cycle\" ||\n      _vm.type == \"restaurant_shop\" ||\n      _vm.type == \"restaurant_takeaway\" ||\n      _vm.type == \"tuangou\" ||\n      _vm.type == \"seckill\") &&\n    _vm.type == \"scoreshop\" &&\n    _vm.order.totalmoney\n      ? _vm.t(\"积分\")\n      : null\n  var m4 =\n    _vm.isload &&\n    !_vm.hexiao_status &&\n    (_vm.type == \"shop\" ||\n      _vm.type == \"collage\" ||\n      _vm.type == \"lucky_collage\" ||\n      _vm.type == \"kanjia\" ||\n      _vm.type == \"scoreshop\" ||\n      _vm.type == \"cycle\" ||\n      _vm.type == \"restaurant_shop\" ||\n      _vm.type == \"restaurant_takeaway\" ||\n      _vm.type == \"tuangou\" ||\n      _vm.type == \"seckill\") &&\n    _vm.type == \"scoreshop\" &&\n    !_vm.order.totalmoney\n      ? _vm.t(\"积分\")\n      : null\n  var m5 =\n    _vm.isload &&\n    !_vm.hexiao_status &&\n    (_vm.type == \"shop\" ||\n      _vm.type == \"collage\" ||\n      _vm.type == \"lucky_collage\" ||\n      _vm.type == \"kanjia\" ||\n      _vm.type == \"scoreshop\" ||\n      _vm.type == \"cycle\" ||\n      _vm.type == \"restaurant_shop\" ||\n      _vm.type == \"restaurant_takeaway\" ||\n      _vm.type == \"tuangou\" ||\n      _vm.type == \"seckill\") &&\n    _vm.order.disprice > 0\n      ? _vm.t(\"会员\")\n      : null\n  var m6 =\n    _vm.isload &&\n    !_vm.hexiao_status &&\n    (_vm.type == \"shop\" ||\n      _vm.type == \"collage\" ||\n      _vm.type == \"lucky_collage\" ||\n      _vm.type == \"kanjia\" ||\n      _vm.type == \"scoreshop\" ||\n      _vm.type == \"cycle\" ||\n      _vm.type == \"restaurant_shop\" ||\n      _vm.type == \"restaurant_takeaway\" ||\n      _vm.type == \"tuangou\" ||\n      _vm.type == \"seckill\") &&\n    _vm.order.couponmoney > 0\n      ? _vm.t(\"优惠券\")\n      : null\n  var m7 =\n    _vm.isload &&\n    !_vm.hexiao_status &&\n    (_vm.type == \"shop\" ||\n      _vm.type == \"collage\" ||\n      _vm.type == \"lucky_collage\" ||\n      _vm.type == \"kanjia\" ||\n      _vm.type == \"scoreshop\" ||\n      _vm.type == \"cycle\" ||\n      _vm.type == \"restaurant_shop\" ||\n      _vm.type == \"restaurant_takeaway\" ||\n      _vm.type == \"tuangou\" ||\n      _vm.type == \"seckill\") &&\n    _vm.order.scoredk > 0\n      ? _vm.t(\"积分\")\n      : null\n  var m8 =\n    _vm.isload &&\n    !_vm.hexiao_status &&\n    (_vm.type == \"shop\" ||\n      _vm.type == \"collage\" ||\n      _vm.type == \"lucky_collage\" ||\n      _vm.type == \"kanjia\" ||\n      _vm.type == \"scoreshop\" ||\n      _vm.type == \"cycle\" ||\n      _vm.type == \"restaurant_shop\" ||\n      _vm.type == \"restaurant_takeaway\" ||\n      _vm.type == \"tuangou\" ||\n      _vm.type == \"seckill\") &&\n    _vm.type == \"scoreshop\"\n      ? _vm.t(\"积分\")\n      : null\n  var m9 =\n    _vm.isload && !_vm.hexiao_status && _vm.type == \"coupon\"\n      ? _vm.t(\"color1\")\n      : null\n  var m10 =\n    _vm.isload && !_vm.hexiao_status && _vm.type == \"coupon\"\n      ? _vm.t(\"color1rgb\")\n      : null\n  var m11 =\n    _vm.isload &&\n    !_vm.hexiao_status &&\n    _vm.type == \"coupon\" &&\n    _vm.order.type == 1\n      ? _vm.t(\"color1\")\n      : null\n  var m12 =\n    _vm.isload &&\n    !_vm.hexiao_status &&\n    _vm.type == \"coupon\" &&\n    !(_vm.order.type == 1) &&\n    _vm.order.type == 2\n      ? _vm.t(\"color1\")\n      : null\n  var m13 =\n    _vm.isload &&\n    !_vm.hexiao_status &&\n    _vm.type == \"coupon\" &&\n    !(_vm.order.type == 1) &&\n    !(_vm.order.type == 2) &&\n    _vm.order.type == 3\n      ? _vm.t(\"color1\")\n      : null\n  var m14 =\n    _vm.isload &&\n    !_vm.hexiao_status &&\n    _vm.type == \"coupon\" &&\n    !(_vm.order.type == 1) &&\n    !(_vm.order.type == 2) &&\n    !(_vm.order.type == 3) &&\n    _vm.order.type == 4\n      ? _vm.t(\"color1\")\n      : null\n  var m15 =\n    _vm.isload && !_vm.hexiao_status && _vm.type == \"choujiang\"\n      ? _vm.dateFormat(_vm.order.createtime, \"Y-m-d H:i\")\n      : null\n  var m16 =\n    _vm.isload && !_vm.hexiao_status && _vm.type == \"huodong_baoming\"\n      ? _vm.dateFormat(_vm.order.createtime, \"Y-m-d H:i\")\n      : null\n  var m17 =\n    _vm.isload && !_vm.hexiao_status && _vm.type == \"business_miandan\"\n      ? _vm.dateFormat(_vm.order.createtime, \"Y-m-d H:i\")\n      : null\n  var m18 =\n    _vm.isload && !_vm.hexiao_status && _vm.type == \"verifyauth\"\n      ? _vm.t(\"color1\")\n      : null\n  var m19 =\n    _vm.isload &&\n    !_vm.hexiao_status &&\n    _vm.type == \"hotel\" &&\n    _vm.order.status > 1 &&\n    _vm.order.send_time\n      ? _vm.dateFormat(_vm.order.confirm_time)\n      : null\n  var m20 =\n    _vm.isload &&\n    !_vm.hexiao_status &&\n    _vm.type == \"hotel\" &&\n    _vm.order.couponmoney > 0\n      ? _vm.t(\"优惠券\")\n      : null\n  var m21 =\n    _vm.isload &&\n    !_vm.hexiao_status &&\n    _vm.type == \"hotel\" &&\n    _vm.order.scoredk > 0\n      ? _vm.t(\"积分\")\n      : null\n  var m22 =\n    _vm.isload && !_vm.hexiao_status && _vm.type != \"verifyauth\"\n      ? _vm.t(\"color1\")\n      : null\n  var m23 = _vm.isload && !!_vm.hexiao_status ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        m23: m23,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hexiao.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hexiao.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n    <block v-if=\"isload\">\r\n        <block v-if=\"!hexiao_status\">\r\n            <block v-if=\"type=='shop' || type=='collage' || type=='lucky_collage'  || type=='kanjia' || type=='scoreshop' || type=='cycle' || type=='restaurant_shop' || type=='restaurant_takeaway' || type=='tuangou' || type=='seckill'\">\r\n                <view class=\"address\">\r\n                    <view class=\"img\">\r\n                        <image :src=\"pre_url+'/static/img/address3.png'\"></image>\r\n                    </view>\r\n                    <view class=\"info\">\r\n                        <text class=\"t1\">{{order.linkman}} {{order.tel}}</text>\r\n                        <text class=\"t2\" v-if=\"order.freight_type!=1 && order.freight_type!=3\">地址：{{order.area}}{{order.address}}</text>\r\n                        <text class=\"t2\" v-if=\"order.freight_type==1\" @tap=\"openLocation\" :data-address=\"order.storeinfo.address\" :data-latitude=\"order.storeinfo.latitude\" :data-longitude=\"order.storeinfo.longitude\">取货地点：{{order.storeinfo.name}} - {{order.storeinfo.address}}</text>\r\n                    </view>\r\n                </view>\r\n                <view class=\"product\">\r\n                    <view v-for=\"(item, idx) in order.prolist\" :key=\"idx\" class=\"content\">\r\n                        <view>\r\n                            <image :src=\"item.pic\"></image>\r\n                        </view>\r\n                        <view class=\"detail\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t1\">{{item.name}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.ggname\">{{item.ggname}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"t3\" v-if=\"type == 'scoreshop'\"><text class=\"x1 flex1\"><text v-if=\"item.money_price>0\">￥{{item.money_price}}+</text>{{item.score_price}}{{t('积分')}}</text><text class=\"x2\">×{{item.num}}</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"t3\" v-else><text class=\"x1 flex1\">￥{{item.sell_price}}</text><text class=\"x2\" v-if=\"type !='cycle'\">×{{item.num}}</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"mendian_no_select==1 && item.is_hx\" class=\"t3\">已核销 </view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n                    </view>\r\n                </view>\r\n                \r\n                <view class=\"orderinfo\" v-if=\"(order.status==3 || order.status==2) && (order.freight_type==3 || order.freight_type==4)\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"item flex-col\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t1\" style=\"color:#111\">发货信息</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t2\" style=\"text-align:left;margin-top:10rpx;padding:0 10rpx\" user-select=\"true\" selectable=\"true\">{{order.freight_content}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n                </view>\r\n                \r\n                <view class=\"orderinfo\">\r\n                    <view class=\"item\">\r\n                        <text class=\"t1\">下单人</text>\r\n                        <text class=\"flex1\"></text>\r\n                        <image :src=\"order.headimg\" style=\"width:80rpx;height:80rpx;margin-right:8rpx\"/>\r\n                        <text  style=\"height:80rpx;line-height:80rpx\">{{order.nickname}}</text>\r\n                    </view>\r\n                    <view class=\"item\">\r\n                        <text class=\"t1\">{{t('会员')}}ID</text>\r\n                        <text class=\"t2\">{{order.mid}}</text>\r\n                    </view>\r\n                </view>\r\n                <view class=\"orderinfo\" v-if=\"order.remark\">\r\n                    <view class=\"item\">\r\n                        <text class=\"t1\">备注</text>\r\n                        <text class=\"t2\">{{order.remark}}</text>\r\n                    </view>\r\n                </view>\r\n                <view class=\"orderinfo\">\r\n                    <view class=\"item\">\r\n                        <text class=\"t1\">订单编号</text>\r\n                        <text class=\"t2\" user-select=\"true\" selectable=\"true\">{{order.ordernum}}</text>\r\n                    </view>\r\n                    <view class=\"item\">\r\n                        <text class=\"t1\">下单时间</text>\r\n                        <text class=\"t2\">{{order.createtime}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.status>0 && order.paytypeid!='4' && order.paytime\">\r\n                        <text class=\"t1\">支付时间</text>\r\n                        <text class=\"t2\">{{order.paytime}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.status>0 && order.paytime\">\r\n                        <text class=\"t1\">支付方式</text>\r\n                        <text class=\"t2\">{{order.paytype}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.status>1 && order.send_time\">\r\n                        <text class=\"t1\">发货时间</text>\r\n                        <text class=\"t2\">{{dateFormat(order.send_time)}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.status==3 && order.collect_time\">\r\n                        <text class=\"t1\">收货时间</text>\r\n                        <text class=\"t2\">{{order.collect_time}}</text>\r\n                    </view>\r\n                </view>\r\n                <view class=\"orderinfo\">\r\n                    <view class=\"item\" v-if=\"type=='cycle'\">\r\n                        <text class=\"t1\">总配送期数</text>\r\n                        <text class=\"t2 red\">共{{order.qsnum}}期</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"type=='cycle'\">\r\n                        <text class=\"t1\">当前配送期数</text>\r\n                        <text class=\"t2 red\">第{{order.stage.cycle_number}}期</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"type=='cycle'\">\r\n                        <text class=\"t1\">每期数量</text>\r\n                        <text class=\"t2 red\">共{{order.stage.num}}件</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"type =='scoreshop'\">\r\n                        <text class=\"t1\">商品金额</text>\r\n                        <text class=\"t2 red\" v-if=\"order.totalmoney\">¥{{order.totalmoney}} + {{order.totalscore}}{{t('积分')}}</text>\r\n                        <text class=\"t2 red\" v-else>{{order.totalscore}}{{t('积分')}}</text>\r\n                    </view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"item\" v-else>\r\n\t\t\t\t\t\t\t\t\t\t    <text class=\"t1\">商品金额</text>\r\n\t\t\t\t\t\t\t\t\t\t    <text class=\"t2 red\">¥{{order.product_price}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n                    <view class=\"item\" v-if=\"order.disprice > 0\">\r\n                        <text class=\"t1\">{{t('会员')}}折扣</text>\r\n                        <text class=\"t2 red\">-¥{{order.leveldk_money}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.jianmoney > 0\">\r\n                        <text class=\"t1\">满减活动</text>\r\n                        <text class=\"t2 red\">-¥{{order.manjian_money}}</text>\r\n                    </view>\r\n                    <view class=\"item\">\r\n                        <text class=\"t1\">配送方式</text>\r\n                        <text class=\"t2\">{{order.freight_text}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.freight_type==1 && order.freightprice > 0\">\r\n                        <text class=\"t1\">服务费</text>\r\n                        <text class=\"t2 red\">+¥{{order.freight_price}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.freight_time\">\r\n                        <text class=\"t1\">{{order.freight_type!=1?'配送':'提货'}}时间</text>\r\n                        <text class=\"t2\">{{order.freight_time}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.couponmoney > 0\">\r\n                        <text class=\"t1\">{{t('优惠券')}}抵扣</text>\r\n                        <text class=\"t2 red\">-¥{{order.coupon_money}}</text>\r\n                    </view>\r\n                    \r\n                    <view class=\"item\" v-if=\"order.scoredk > 0\">\r\n                        <text class=\"t1\">{{t('积分')}}抵扣</text>\r\n                        <text class=\"t2 red\">-¥{{order.scoredk_money}}</text>\r\n                    </view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"item\" v-if=\"type =='scoreshop'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"t1\">实付款</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"t2 red\">¥{{order.totalprice}} + {{order.totalscore}}{{t('积分')}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n                    <view class=\"item\" v-else>\r\n                        <text class=\"t1\">实付款</text>\r\n                        <text class=\"t2 red\">¥{{order.totalprice}}</text>\r\n                    </view>\r\n\r\n                    <view class=\"item\">\r\n                        <text class=\"t1\">订单状态</text>\r\n                        <text class=\"t2\" v-if=\"order.status==0\">未付款</text>\r\n                        <text class=\"t2\" v-if=\"order.status==1\">已付款</text>\r\n                        <text class=\"t2\" v-if=\"order.status==2\">已发货</text>\r\n                        <text class=\"t2\" v-if=\"order.status==3\">已收货</text>\r\n                        <text class=\"t2\" v-if=\"order.status==4\">已关闭</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.refund_status>0\">\r\n                        <text class=\"t1\">退款状态</text>\r\n                        <text class=\"t2 red\" v-if=\"order.refund_status==1\">审核中,¥{{order.refund_money}}</text>\r\n                        <text class=\"t2 red\" v-if=\"order.refund_status==2\">已退款,¥{{order.refund_money}}</text>\r\n                        <text class=\"t2 red\" v-if=\"order.refund_status==3\">已驳回,¥{{order.refund_money}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.refund_status>0\">\r\n                        <text class=\"t1\">退款原因</text>\r\n                        <text class=\"t2 red\">{{order.refund_reason}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.refund_checkremark\">\r\n                        <text class=\"t1\">审核备注</text>\r\n                        <text class=\"t2 red\">{{order.refund_checkremark}}</text>\r\n                    </view>\r\n\r\n                    <view class=\"item\">\r\n                        <text class=\"t1\">备注</text>\r\n                        <text class=\"t2 red\">{{order.message ? order.message : '无'}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.field1\">\r\n                        <text class=\"t1\">{{order.field1data[0]}}</text>\r\n                        <text class=\"t2 red\">{{order.field1data[1]}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.field2\">\r\n                        <text class=\"t1\">{{order.field2data[0]}}</text>\r\n                        <text class=\"t2 red\">{{order.field2data[1]}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.field3\">\r\n                        <text class=\"t1\">{{order.field3data[0]}}</text>\r\n                        <text class=\"t2 red\">{{order.field3data[1]}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.field4\">\r\n                        <text class=\"t1\">{{order.field4data[0]}}</text>\r\n                        <text class=\"t2 red\">{{order.field4data[1]}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.field5\">\r\n                        <text class=\"t1\">{{order.field5data[0]}}</text>\r\n                        <text class=\"t2 red\">{{order.field5data[1]}}</text>\r\n                    </view>\r\n                </view>\r\n            </block>\r\n\t\t\t\t\t\t\r\n            <block v-if=\"type=='coupon'\">\r\n                <view class=\"couponbg\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"></view>\r\n                <view class=\"orderinfo\">\r\n                    <view class=\"topitem\">\r\n                        <view class=\"f1\" :style=\"{color:t('color1')}\" v-if=\"order.type==1\"><text style=\"font-size:32rpx\">￥</text><text class=\"t1\">{{order.money}}</text></view>\r\n                        <view class=\"f1\" :style=\"{color:t('color1')}\" v-else-if=\"order.type==2\">礼品券</view>\r\n                        <view class=\"f1\" :style=\"{color:t('color1')}\" v-else-if=\"order.type==3\"><text class=\"t1\">{{order.limit_count}}</text><text class=\"t2\">次</text></view>\r\n                        <view class=\"f1\" :style=\"{color:t('color1')}\" v-else-if=\"order.type==4\">抵运费</view>\r\n                        <view class=\"f2\">\r\n                            <view class=\"t1\">{{order.couponname}}</view>\r\n                            <view class=\"t2\" v-if=\"order.type==1 || order.type==4\">\r\n                                <text v-if=\"order.minprice>0\">满{{order.minprice}}元可用</text>\r\n                                <text v-else>无门槛</text>\r\n                            </view>\r\n                            <view class=\"t2\" v-if=\"order.type==2\">礼品券</view>\r\n                            <view class=\"t2\" v-if=\"order.type==3\">计次券</view>\r\n                        </view>\r\n                    </view>\r\n                    <view class=\"item\">\r\n                        <text class=\"t1\">类型</text>\r\n                        <text class=\"t2\" v-if=\"order.type==1\">代金券</text>\r\n                        <text class=\"t2\" v-if=\"order.type==2\">礼品券</text>\r\n                        <text class=\"t2\" v-if=\"order.type==3\">计次券</text>\r\n                        <text class=\"t2\" v-if=\"order.type==4\">运费抵扣券</text>\r\n                    </view>\r\n                    <block v-if=\"order.type==3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t1\">共计次数</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t2\">{{order.limit_count}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t1\">已使用次数</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t2\">{{order.used_count}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"order.limit_perday>0\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t1\">每天限制次数</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t2\">{{order.limit_perday}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</block>\r\n                    </block>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"item\" v-if=\"order.show_addnum ==1\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t1\" style=\"flex: 1\">核销次数</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"addnum\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"minus\" @tap=\"hxminus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-minus.png'\" /></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<input class=\"input\" type=\"number\" :value=\"hxnum\" @input=\"hxinput\"></input>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"plus\" @tap=\"hxplus\"><image class=\"img\" :src=\"pre_url+'/static/img/cart-plus.png'\"/></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n                    <view class=\"item\">\r\n                        <text class=\"t1\">领取时间</text>\r\n                        <text class=\"t2\">{{order.createtime}}</text>\r\n                    </view>\r\n                    <block v-if=\"order.status==1\">\r\n                    <view class=\"item\">\r\n                        <text class=\"t1\">使用时间</text>\r\n                        <text class=\"t2\">{{order.usetime}}</text>\r\n                    </view>\r\n                    </block>\r\n                    \r\n                    <view class=\"item flex-col\">\r\n                        <text class=\"t1\">有效期</text>\r\n                        <text class=\"t2\">{{order.starttime}} 至 {{order.endtime}}</text>\r\n                    </view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"item flex-col\" v-if=\"order.is_show_mendians == 1\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t1\">选择门店</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t2\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<radio-group class=\"radio-group\" @change=\"radioChange\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label v-for=\"(item1,idx) in mendians\" :key=\"idx\" class=\"flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<radio class=\"radio\" :value=\"idx\" />{{item1}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n                    <view class=\"item flex-col\">\r\n                        <text class=\"t1\">使用说明</text>\r\n                        <view class=\"t2 textarea\">{{order.usetips}}</view>\r\n                    </view>\r\n                </view>\r\n            </block>\r\n\t\t\t\t\t\t\r\n            <block v-if=\"type=='choujiang'\">\r\n                <view style=\"padding:15px 0 15px 0;\">\r\n                    <view style=\"text-align: center;font-size:20px;color: #3cc51f;font-weight: 400;margin: 0 15%;\">核销信息</view>\r\n                </view>\r\n                <view class=\"orderinfo\">\r\n                    <view class=\"item\">\r\n                        <view class=\"t1\">核销类型</view>\r\n                        <view class=\"t2\" style=\"font-size:32rpx\">抽奖奖品</view>\r\n                    </view>\r\n                    <view class=\"item\">\r\n                        <view class=\"t1\">活动名称</view>\r\n                        <view class=\"t2\">{{order.name}}</view>\r\n                    </view>\r\n                    <view class=\"item\">\r\n                        <view class=\"t1\">奖品名称</view>\r\n                        <view class=\"t2\" style=\"font-size:16px;color:#000\">{{order.jxmc}}</view>\r\n                    </view>\r\n                    <view class=\"item\">\r\n                        <view class=\"t1\">中奖时间</view>\r\n                        <view class=\"t2\">{{dateFormat(order.createtime,'Y-m-d H:i')}}</view>\r\n                    </view>\r\n                    <block v-if=\"order.formdata\">\r\n                        <view class=\"item\" v-for=\"(item, index) in order.formdata\" :key=\"index\">\r\n                            <text class=\"t1\">{{index}}</text>\r\n                            <text class=\"t2\" :user-select=\"true\" :selectable=\"true\">{{item}}</text>\r\n                        </view>\r\n                    </block>\r\n                </view>\r\n            </block>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<block v-if=\"type=='huodong_baoming'\">\r\n                <view style=\"padding:15px 0 15px 0;\">\r\n                    <view style=\"text-align: center;font-size:20px;color: #3cc51f;font-weight: 400;margin: 0 15%;\">核销信息</view>\r\n                </view>\r\n                <view class=\"orderinfo\">\r\n                    <view class=\"item\">\r\n                        <view class=\"t1\">核销类型</view>\r\n                        <view class=\"t2\" style=\"font-size:32rpx\">活动报名</view>\r\n                    </view>\r\n                    <view class=\"item\">\r\n                        <view class=\"t1\">活动名称</view>\r\n                        <view class=\"t2\">{{order.title}}</view>\r\n                    </view>\r\n                    <view class=\"item\">\r\n                        <view class=\"t1\">核销数量</view>\r\n                        <view class=\"t2\" style=\"font-size:16px;color:#000\">{{order.num}}</view>\r\n                    </view>\r\n                    <view class=\"item\">\r\n                        <view class=\"t1\">报名时间</view>\r\n                        <view class=\"t2\">{{dateFormat(order.createtime,'Y-m-d H:i')}}</view>\r\n                    </view>\r\n                </view>\r\n            </block>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<block v-if=\"type=='business_miandan'\">\r\n                <view style=\"padding:15px 0 15px 0;\">\r\n                    <view style=\"text-align: center;font-size:20px;color: #3cc51f;font-weight: 400;margin: 0 15%;\">核销信息</view>\r\n                </view>\r\n                <view class=\"orderinfo\">\r\n                    <view class=\"item\">\r\n                        <view class=\"t1\">核销类型</view>\r\n                        <view class=\"t2\" style=\"font-size:32rpx\">商户免单</view>\r\n                    </view>\r\n                    <view class=\"item\">\r\n                        <view class=\"t1\">活动名称</view>\r\n                        <view class=\"t2\">{{order.title}}</view>\r\n                    </view>\r\n                    <view class=\"item\">\r\n                        <view class=\"t1\">核销数量</view>\r\n                        <view class=\"t2\" style=\"font-size:16px;color:#000\">{{order.num}}</view>\r\n                    </view>\r\n                    <view class=\"item\">\r\n                        <view class=\"t1\">报名时间</view>\r\n                        <view class=\"t2\">{{dateFormat(order.createtime,'Y-m-d H:i')}}</view>\r\n                    </view>\r\n                </view>\r\n            </block>\r\n\t\t\t\t\t\t\r\n            <block v-if=\"type=='hbtk'\">\r\n                <view class=\"product\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t\t\t\t\t\t<view >\r\n\t\t\t\t\t\t\t\t\t\t\t<image :src=\"order.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"t1\">{{order.name}}</text>\t   \r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"t3\"><text class=\"x1 flex1\">￥{{order.price}}</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"t2\"><text>已邀请{{order.yqnum}}人</text></view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n                </view>\r\n                <view class=\"orderinfo\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"t1 flex-y-center\">下单人</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"flex1\"></text>\r\n\t\t\t\t\t\t\t\t\t\t\t<image :src=\"order.headimg\" style=\"width:80rpx;height:80rpx;margin-right:8rpx\"/>\r\n\t\t\t\t\t\t\t\t\t\t\t<text  style=\"height:80rpx;line-height:80rpx\">{{order.nickname}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n                </view>\r\n\t\t\t\t\t\t\t\t<view class=\"orderinfo\" v-if=\"order.yqlist\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"item flex-y-center\" style=\"padding: 10rpx 0;\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t1\">邀请人员</text>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"t2\" user-select=\"true\" selectable=\"true\" style=\"overflow: hidden;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-for=\"(item,index) in order.yqlist\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image class=\"yq_image\" :src=\"item.headimg\"/>\r\n\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n                <view class=\"orderinfo\">\r\n                    <view class=\"item\">\r\n                        <text class=\"t1\">订单编号</text>\r\n                        <text class=\"t2\" user-select=\"true\" selectable=\"true\">{{order.ordernum}}</text>\r\n                    </view>\r\n                    <view class=\"item\">\r\n                        <text class=\"t1\">下单时间</text>\r\n                        <text class=\"t2\">{{order.createtime}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.status > 0 && order.price > 0 && order.paytime\">\r\n                        <text class=\"t1\">支付时间</text>\r\n                        <text class=\"t2\">{{order.paytime}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.status >=1 && order.price > 0\">\r\n                        <text class=\"t1\">支付方式</text>\r\n                        <text class=\"t2\">{{order.paytype}}</text>\r\n                    </view>\r\n                </view>\r\n                <view class=\"orderinfo\">\r\n                    <view class=\"item\" v-if=\" order.price > 0\">\r\n                        <text class=\"t1\">支付金额</text>\r\n                        <text class=\"t2 red\">¥{{order.price}}</text>\r\n                    </view>\r\n                    <view class=\"item\">\r\n                        <text class=\"t1\">订单状态</text>\r\n                        <text class=\"t2\" v-if=\"order.status==1\">待核销</text>\r\n                        <text class=\"t2\" v-if=\"order.status==2\">已核销</text>\r\n                    </view>\r\n                </view>\r\n            </block>\r\n\t\t\t\t\t\t\r\n            <block v-if=\"type=='shopproduct' || type=='takeaway_order_product'\">\r\n              <view class=\"product\">\r\n                  <view class=\"content\">\r\n                      <view>\r\n                          <image :src=\"order.ogdata.pic\"></image>\r\n                      </view>\r\n                      <view class=\"detail\">\r\n                          <text class=\"t1\">{{order.ogdata.name}}</text>\r\n                          <text class=\"t2\">{{order.ogdata.ggname}}</text>\r\n                          <view class=\"t3\"><text class=\"x1 flex1\">￥{{order.ogdata.sell_price}}</text><text class=\"x2\">×{{order.ogdata.num}}</text></view>\r\n                          <view class=\"t3\" v-if=\"order.ogdata.refund_num && order.ogdata.refund_num>0\"><text class=\"x1 flex1\"></text><text >已退：{{order.ogdata.refund_num}}件</text></view>\r\n                      </view>\r\n                  </view>\r\n              </view>\r\n              <view class=\"orderinfo\">\r\n                  <view class=\"item\">\r\n                      <text class=\"t1\">订单编号</text>\r\n                      <text class=\"t2\" user-select=\"true\" selectable=\"true\">{{order.ordernum}}</text>\r\n                  </view>\r\n                  <view class=\"item\">\r\n                      <text class=\"t1\">下单时间</text>\r\n                      <text class=\"t2\">{{order.createtime}}</text>\r\n                  </view>\r\n                  <view class=\"item\" v-if=\"order.status>0 && order.paytypeid!='4' && order.paytime\">\r\n                      <text class=\"t1\">支付时间</text>\r\n                      <text class=\"t2\">{{order.paytime}}</text>\r\n                  </view>\r\n                  <view class=\"item\" v-if=\"order.status>0 && order.paytime\">\r\n                      <text class=\"t1\">支付方式</text>\r\n                      <text class=\"t2\">{{order.paytype}}</text>\r\n                  </view>\r\n                  <view class=\"item\" v-if=\"type=='shopproduct'\">\r\n                      <text class=\"t1\">已核销数</text>\r\n                      <text class=\"t2\">{{order.ogdata.hexiao_num}}</text>\r\n                  </view>\r\n              </view>\r\n              <view class=\"orderinfo\" v-if=\"type=='shopproduct'\">\r\n                  <view class=\"item\">\r\n                      <text class=\"t1\">本次核销数</text>\r\n                      <text class=\"t2\" style=\"color:#223;font-weight:bold;font-size:32rpx\">{{order.hxnum}}</text>\r\n                  </view>\r\n              </view>\r\n              <view class=\"orderinfo\" v-if=\"type=='takeaway_order_product'\">\r\n                  <view class=\"item\">\r\n                      <text class=\"t1\">本次核销数</text>\r\n                      <text class=\"t2\" style=\"color:#223;font-weight:bold;font-size:32rpx\">{{order.now_hxnum}}</text>\r\n                  </view>\r\n              </view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\r\n            <block v-if=\"type=='gift_bag' || type=='gift_bag_goods'\">\r\n                <view v-if=\"type=='gift_bag'\" class=\"product\" >\r\n                    <view v-for=\"(item, idx) in order.prolist\" :key=\"idx\" class=\"content\">\r\n                        <view>\r\n                            <image :src=\"item.pic\"></image>\r\n                        </view>\r\n                        <view class=\"detail\">\r\n                            <text class=\"t1\">{{item.name}}</text>\r\n                            <view class=\"t3\"><text class=\"x1 flex1\">￥{{item.sell_price}}</text><text class=\"x2\" v-if=\"type !='cycle'\">×{{item.num}}</text></view>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n                <view v-if=\"type=='gift_bag_goods'\" class=\"product\">\r\n                    <view class=\"content\">\r\n                        <view>\r\n                            <image :src=\"order.ogdata.pic\"></image>\r\n                        </view>\r\n                        <view class=\"detail\">\r\n                            <text class=\"t1\">{{order.ogdata.name}}</text>\r\n                            <view class=\"t3\"><text class=\"x1 flex1\">￥{{order.ogdata.sell_price}}</text><text class=\"x2\">×{{order.ogdata.num}}</text></view>\r\n                            <view class=\"t3\" v-if=\"order.ogdata.refund_num && order.ogdata.refund_num>0\"><text class=\"x1 flex1\"></text><text >已退：{{order.ogdata.refund_num}}件</text></view>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n                <view class=\"orderinfo\">\r\n                    <view class=\"item\">\r\n                        <text class=\"t1\">订单编号</text>\r\n                        <text class=\"t2\" user-select=\"true\" selectable=\"true\">{{order.ordernum}}</text>\r\n                    </view>\r\n                    <view class=\"item\">\r\n                        <text class=\"t1\">下单时间</text>\r\n                        <text class=\"t2\">{{order.createtime}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.status>0 && order.paytypeid!='4' && order.paytime\">\r\n                        <text class=\"t1\">支付时间</text>\r\n                        <text class=\"t2\">{{order.paytime}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"order.status>0 && order.paytime\">\r\n                        <text class=\"t1\">支付方式</text>\r\n                        <text class=\"t2\">{{order.paytype}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"type=='gift_bag_goods'\">\r\n                        <text class=\"t1\">已核销数</text>\r\n                        <text class=\"t2\">{{order.ogdata.hexiao_num}}</text>\r\n                    </view>\r\n                    <view class=\"item\" v-if=\"type=='gift_bag'\">\r\n                        <text class=\"t1\">实付款</text>\r\n                        <text class=\"t2 red\">¥{{order.totalprice}}</text>\r\n                    </view>\r\n                    <block  v-if=\"type=='gift_bag'\">\r\n                      <view class=\"item\" v-if=\"order.refund_status>0\">\r\n                          <text class=\"t1\">退款状态</text>\r\n                          <text class=\"t2 red\" v-if=\"order.refund_status==1\">审核中,¥{{order.refund_money}}</text>\r\n                          <text class=\"t2 red\" v-if=\"order.refund_status==2\">已退款,¥{{order.refund_money}}</text>\r\n                          <text class=\"t2 red\" v-if=\"order.refund_status==3\">已驳回,¥{{order.refund_money}}</text>\r\n                      </view>\r\n                      <view class=\"item\" v-if=\"order.refund_status>0\">\r\n                          <text class=\"t1\">退款原因</text>\r\n                          <text class=\"t2 red\">{{order.refund_reason}}</text>\r\n                      </view>\r\n                      <view class=\"item\" v-if=\"order.refund_checkremark\">\r\n                          <text class=\"t1\">审核备注</text>\r\n                          <text class=\"t2 red\">{{order.refund_checkremark}}</text>\r\n                      </view>\r\n                    </block>\r\n                </view>\r\n                \r\n                <view class=\"orderinfo\" v-if=\"type=='gift_bag_goods'\">\r\n                    <view class=\"item\">\r\n                        <text class=\"t1\">本次核销数</text>\r\n                        <text class=\"t2\" style=\"color:#223;font-weight:bold;font-size:32rpx\">{{order.hxnum}}</text>\r\n                    </view>\r\n                </view>\r\n            </block>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<block v-if=\"type=='verifyauth'\">\r\n\t\t\t\t\t\t\t<view class=\"flex\" style=\"flex-direction: column;align-items: center;margin-top: 40%\">\r\n\t\t\t\t\t\t\t\t<img :src=\"pre_url+'/static/img/shouquan.png'\" style=\"width:300rpx;height: 300rpx;\" alt=\"\">\r\n\t\t\t\t\t\t\t\t<view style=\"font-size: 32rpx\">{{order.tip}}</view>\r\n\t\t\t\t\t\t\t\t<view style=\"text-align: center;color: #999;padding: 20rpx;\">\r\n\t\t\t\t\t\t\t\t\t{{order.title}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"btn-add\" :style=\"{background:t('color1')}\" style=\"bottom: 15%;\" @tap=\"hexiao\"><text v-if=\"type =='verifyauth'\" >确认授权</text><text v-else>立即核销</text></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<block v-if=\"type=='hotel'\">\r\n\t\t\t\t\t\t\t<view class=\"product\">\r\n\t\t\t\t\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t\t\t<image :src=\"order.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t1\">{{order.title}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"order.title\">{{order.title}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"t3\">￥{{order.sell_price}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"orderinfo\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t\t\t\t\t<label class=\"t1\">入住姓名</label>\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"t2\">{{order.linkman}} </text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t\t\t\t\t<label class=\"t1\">联系手机</label>\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"t2\">{{order.tel}} </text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t1\">入住日期</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t2 \">{{order.in_date}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t\t\t    <text class=\"t1\">离店日期</text>\r\n\t\t\t\t\t\t\t\t\t    <text class=\"t2 \">{{order.leave_date}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"orderinfo\">\r\n\t\t\t\t\t\t\t\t    <view class=\"item\">\r\n\t\t\t\t\t\t\t\t        <text class=\"t1\">订单编号</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2\" user-select=\"true\" selectable=\"true\">{{order.ordernum}}</text>\r\n\t\t\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t\t\t    <view class=\"item\">\r\n\t\t\t\t\t\t\t\t        <text class=\"t1\">下单时间</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2\">{{order.createtime}}</text>\r\n\t\t\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t\t\t    <view class=\"item\" v-if=\"order.status>0 && order.paytime\">\r\n\t\t\t\t\t\t\t\t        <text class=\"t1\">支付时间</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2\">{{order.paytime}}</text>\r\n\t\t\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t\t\t    <view class=\"item\" v-if=\"order.status>0 && order.paytime\">\r\n\t\t\t\t\t\t\t\t        <text class=\"t1\">支付方式</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2\">{{order.paytype}}</text>\r\n\t\t\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t\t\t    <view class=\"item\" v-if=\"order.status>1 && order.send_time\">\r\n\t\t\t\t\t\t\t\t        <text class=\"t1\">确认时间</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2\">{{dateFormat(order.confirm_time)}}</text>\r\n\t\t\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t\t\t    <view class=\"item\" v-if=\"order.status==3 && order.daodian_time\">\r\n\t\t\t\t\t\t\t\t        <text class=\"t1\">到店时间</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2\">{{order.daodian_time}}</text>\r\n\t\t\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"item\" v-if=\"order.status==3 && order.collect_time\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t1\">离店时间</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"t2\">{{order.collect_time}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"orderinfo\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"item\" >\r\n\t\t\t\t\t\t\t\t\t\t    <text class=\"t1\">押金</text>\r\n\t\t\t\t\t\t\t\t\t\t    <text class=\"t2 red\">¥{{order.yajin_money}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t    <view class=\"item\" v-if=\"order.fuwu_money > 0\">\r\n\t\t\t\t\t\t\t\t        <text class=\"t1\">服务费</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2 red\">¥{{order.fuwu_money}}</text>\r\n\t\t\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t\t\t    <view class=\"item\" v-if=\"order.dikou_money > 0\">\r\n\t\t\t\t\t\t\t\t        <text class=\"t1\">余额抵扣</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2 red\">-¥{{order.dikou_money}}</text>\r\n\t\t\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t\t\t    <view class=\"item\" v-if=\"order.couponmoney > 0\">\r\n\t\t\t\t\t\t\t\t        <text class=\"t1\">{{t('优惠券')}}抵扣</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2 red\">-¥{{order.coupon_money}}</text>\r\n\t\t\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t\t\t    \r\n\t\t\t\t\t\t\t\t    <view class=\"item\" v-if=\"order.scoredk > 0\">\r\n\t\t\t\t\t\t\t\t        <text class=\"t1\">{{t('积分')}}抵扣</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2 red\">-¥{{order.scoredk_money}}</text>\r\n\t\t\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t\t\t    <view class=\"item\" v-else>\r\n\t\t\t\t\t\t\t\t        <text class=\"t1\">实付款</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2 red\">¥{{order.totalprice}}</text>\r\n\t\t\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t\t\t    <view class=\"item\">\r\n\t\t\t\t\t\t\t\t        <text class=\"t1\">订单状态</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2\" v-if=\"order.status==0\">未付款</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2\" v-if=\"order.status==1\">待确认</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2\" v-if=\"order.status==2\">待使用</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2\" v-if=\"order.status==3\">已到店</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2\" v-if=\"order.status==4\">已完成</text>\r\n\t\t\t\t\t\t\t\t\t\t\t  <text class=\"t2\" v-if=\"order.status==4\">已关闭</text>\r\n\t\t\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t\t\t    <view class=\"item\" v-if=\"order.refund_status>0\">\r\n\t\t\t\t\t\t\t\t        <text class=\"t1\">退款状态</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2 red\" v-if=\"order.refund_status==1\">审核中,¥{{order.refund_money}}</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2 red\" v-if=\"order.refund_status==2\">已退款,¥{{order.refund_money}}</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2 red\" v-if=\"order.refund_status==3\">已驳回,¥{{order.refund_money}}</text>\r\n\t\t\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t\t\t    <view class=\"item\" v-if=\"order.refund_status>0\">\r\n\t\t\t\t\t\t\t\t        <text class=\"t1\">退款原因</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2 red\">{{order.refund_reason}}</text>\r\n\t\t\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t\t\t    <view class=\"item\" v-if=\"order.refund_checkremark\">\r\n\t\t\t\t\t\t\t\t        <text class=\"t1\">审核备注</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2 red\">{{order.refund_checkremark}}</text>\r\n\t\t\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t    <view class=\"item\">\r\n\t\t\t\t\t\t\t\t        <text class=\"t1\">备注</text>\r\n\t\t\t\t\t\t\t\t        <text class=\"t2 red\">{{order.message ? order.message : '无'}}</text>\r\n\t\t\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<block v-if=\"type=='form'\">\r\n\t\t\t\t\t\t    <view style=\"padding:15px 0 15px 0;\">\r\n\t\t\t\t\t\t        <view style=\"text-align: center;font-size:20px;color: #3cc51f;font-weight: 400;margin: 0 15%;\">核销信息</view>\r\n\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t    <view class=\"orderinfo\">\r\n\t\t\t\t\t\t        <view class=\"item\">\r\n\t\t\t\t\t\t            <view class=\"t1\">核销类型</view>\r\n\t\t\t\t\t\t            <view class=\"t2\" style=\"font-size:32rpx\">表单信息</view>\r\n\t\t\t\t\t\t        </view>\r\n\t\t\t\t\t\t        <view class=\"item\">\r\n\t\t\t\t\t\t            <view class=\"t1\">表单名称</view>\r\n\t\t\t\t\t\t            <view class=\"t2\">{{order.title}}</view>\r\n\t\t\t\t\t\t        </view>\r\n\t\t\t\t\t\t        <view class=\"item\">\r\n\t\t\t\t\t\t            <view class=\"t1\">填写时间</view>\r\n\t\t\t\t\t\t            <view class=\"t2\">{{order.createtime}}</view>\r\n\t\t\t\t\t\t        </view>\r\n\t\t\t\t\t\t        <view class=\"item\" v-if=\"order.paytime\">\r\n\t\t\t\t\t\t            <view class=\"t1\">支付时间</view>\r\n\t\t\t\t\t\t            <view class=\"t2\">{{order.paytime}}</view>\r\n\t\t\t\t\t\t        </view>\r\n\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<view class=\"orderinfo\">\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(item, index) in order.formdata\" :key=\"index\" >\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"item\" v-if=\"!item.hidden && item.val12\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"t1\" :class=\"item.key=='separate'?'title':''\">{{item.val1}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.key!='upload' && item.key!='upload_file' && item.key!='upload_video' && item.key!='upload_pics'\">\r\n\t\t\t\t\t\t\t\t\t\t\t{{order['form'+index]}}\r\n\t\t\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"item.key=='upload'\"><image :src=\"order['form'+index]\" style=\"width:50px\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"order['form'+index]\"></image></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- #ifdef !H5 && !MP-WEIXIN -->\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"item.key=='upload_file'\" style=\"overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{{order['form'+index]}}\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- #ifdef H5 || MP-WEIXIN -->\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"item.key=='upload_file'\"  @tap=\"download\" :data-file=\"order['form'+index]\" style=\"overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t点击下载查看\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"item.key=='upload_video'\"><video :src=\"order['form'+index]\"  style=\"width:80%;height:300rpx;margin-top:20rpx\"></video></view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"item\" v-if=\"item.key=='map' && detail.show_distance\" @tap=\"openLocation\" :data-latitude=\"detail.adr_lat\" :data-longitude=\"detail.adr_lon\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"t1\">\r\n\t\t\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t距离您{{detail.distance}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/b_addr.png'\" style=\"width:26rpx;height:26rpx;margin-right:10rpx\"/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t点击导航\r\n\t\t\t\t\t\t\t\t\t\t\t</view>  \r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"item.key=='upload_pics'\" >\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-for=\"(item2,index2) in order['form'+index]\" :key=\"index2\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"item2\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"item2\" style=\"width:50px;margin-right: 10rpx;\"></image>\r\n\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t    </view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view style=\"height:140rpx\"></view>\r\n\t\t\t\t\t\t<view class=\"btn-add\" :style=\"{background:t('color1')}\" @tap=\"hexiao\" v-if=\"type !='verifyauth'\" >立即核销</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<view style=\"height:140rpx\"></view>\r\n\t\t\t\t\t\t\t<view class=\"btn-add\" :style=\"{background:t('color1')}\" @tap=\"saoyisao\">继续核销</view>\r\n\t\t\t\t\t</block>\r\n    </block>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n        opt:{},\r\n        loading:false,\r\n        isload: false,\r\n        pre_url:app.globalData.pre_url,\r\n        hxnum:'',\r\n        co:'',\r\n        type:'',\r\n        order:{},\r\n        nodata: false,\r\n        nomore: false,\r\n        hexiao_status:false,\r\n        hexiao_type:0,\r\n\t\t\t\tmendian_no_select:0,\r\n\t\t\t\tmendians:{},\r\n\t\t\t\tmdid:0\r\n    };\r\n  },\r\n  \r\n\tonLoad: function (opt) {\r\n\t\t\tthis.opt   = app.getopts(opt);\r\n\t\t\tconsole.log(this.opt);\r\n\t\t\tthis.hxnum = this.opt.hxnum?this.opt.hxnum:'';\r\n\t\t\tthis.co    = this.opt.co? this.opt.co:'';\r\n\t\t\tthis.type  = this.opt.type;\r\n\t\t\tif(this.type =='coupon'){\r\n\t\t\t\tthis.hxnum = 1;\r\n\t\t\t}\r\n\t\t\tthis.getdata();\r\n\t},\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tmethods: {\r\n\t\t\tgetdata: function () {\r\n\t\t\t\t\tvar that = this;\r\n\t\t\t\t\tthat.loading = true;\r\n\t\t\t\t\tvar hxnum    = that.hxnum;\r\n\t\t\t\t\tvar co       = that.co;\r\n\t\t\t\t\tapp.post('ApiAdminHexiao/hexiao',{type:that.type,co:co,hxnum:hxnum}, function (res) {\r\n\t\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\tif(res.status == 0){\r\n\t\t\t\t\t\t\t\tapp.alert(res.msg,function(){\r\n\t\t\t\t\t\t\t\t\t\tapp.goto('/admin/index/index','reLaunch');\t\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(res.status == 2){\r\n\t\t\t\t\t\t\t\tapp.confirm(res.msg,function(){\r\n\t\t\t\t\t\t\t\t\t\tapp.goto('/admin/index/login','reLaunch');\t\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.order = res.order\r\n\t\t\t\t\t\tif(res.hexiao_type){\r\n\t\t\t\t\t\t\t\tthat.hexiao_type = res.hexiao_type;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.mendian_no_select = res.mendian_no_select;\r\n\t\t\t\t\t\tthat.mendians = res.order.mendians;\r\n\t\t\t\t\t\tthat.loaded(); \r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\thexiao:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar hxnum    = that.hxnum;\r\n\t\t\tvar co       = that.co;\r\n\t\t\tvar tip = '核销'\r\n\t\t\tif(that.type =='verifyauth'){\r\n\t\t\t\ttip ='授权';\r\n\t\t\t}\r\n\t\t\tapp.confirm('确定要'+tip+'吗?',function(){\r\n\t\t\t\tapp.showLoading(tip+'中');\r\n\t\t\t\tapp.post('ApiAdminHexiao/hexiao',{op:'confirm',type:that.type,co:co,hxnum:hxnum,mdid:that.mdid}, function (res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif(res.status == 0){\r\n\t\t\t\t\t\tapp.alert(res.msg);return;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.hexiao_type == 1){\r\n\t\t\t\t\t\t\tapp.success(tip+'成功');\r\n\t\t\t\t\t\t\tthat.hexiao_status = true;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tapp.alert(res.msg,function(){\r\n\t\t\t\t\t\t\t\tapp.goto('/admin/index/index','reLaunch');\t\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t},\r\n\t\tsaoyisao: function (d) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif(app.globalData.platform == 'h5'){\r\n\t\t\t\t\t\tapp.alert('请使用微信扫一扫功能扫码核销');return;\r\n\t\t\t\t}else if(app.globalData.platform == 'mp'){\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tvar jweixin = require('jweixin-module');\r\n\t\t\t\t\tjweixin.ready(function () {   //需在用户可能点击分享按钮前就先调用\r\n\t\t\t\t\t\t\tjweixin.scanQRCode({\r\n\t\t\t\t\t\t\t\t\tneedResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，\r\n\t\t\t\t\t\t\t\t\tscanType: [\"qrCode\",\"barCode\"], // 可以指定扫二维码还是一维码，默认二者都有\r\n\t\t\t\t\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\t\t\t\t\t\tvar content = res.resultStr; // 当needResult 为 1 时，扫码返回的结果\r\n\t\t\t\t\t\t\t\t\t\t\tvar params = content.split('?')[1];\r\n\t\t\t\t\t\t\t\t\t\t\tif(params){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvar params1 = params.split('&');\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tif(params1){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params1[0]){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar params_type = params1[0].split('=')[0];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type == 'type'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar type = params1[0].split('=')[1];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type == 'co'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar co   = params1[0].split('=')[1];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type == 'hxnum'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar hxnum   = params1[0].split('=')[1];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params1[1]){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar params_type = params1[1].split('=')[0];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type == 'type'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar type = params1[1].split('=')[1];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type == 'co'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar co   = params1[1].split('=')[1];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type == 'hxnum'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar hxnum   = params1[1].split('=')[1];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params1[2]){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar params_type = params1[2].split('=')[0];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type == 'type'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar type = params1[2].split('=')[1];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type == 'co'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar co   = params1[2].split('=')[1];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type == 'hxnum'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar hxnum   = params1[2].split('=')[1];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(type&&co){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.type = type;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.co   = co;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(hxnum){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.hxnum = hxnum;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.hexiao_status = false;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tapp.alert('识别错误');\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tapp.alert('识别错误');\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tapp.alert('识别错误');\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}else{\r\n\t\t\t\t\t// #ifndef H5\r\n\t\t\t\t\tuni.scanCode({\r\n\t\t\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\t\t\t\tvar content = res.result;\r\n\t\t\t\t\t\t\t\t\tvar params = content.split('?')[1];\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\tif(params){\r\n\t\t\t\t\t\t\t\t\t\t\tvar params1 = params.split('&');\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\tif(params1){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tif(params1[0]){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar params_type = params1[0].split('=')[0];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type == 'type'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar type = params1[0].split('=')[1];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type == 'co'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar co   = params1[0].split('=')[1];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type == 'hxnum'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar hxnum   = params1[0].split('=')[1];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tif(params1[1]){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar params_type = params1[1].split('=')[0];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type == 'type'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar type = params1[1].split('=')[1];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type == 'co'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar co   = params1[1].split('=')[1];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type == 'hxnum'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar hxnum   = params1[1].split('=')[1];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tif(params1[2]){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar params_type = params1[2].split('=')[0];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type == 'type'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar type = params1[2].split('=')[1];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type == 'co'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar co   = params1[2].split('=')[1];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(params_type == 'hxnum'){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvar hxnum   = params1[2].split('=')[1];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tif(type&&co){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.type = type;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.co   = co;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif(hxnum){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.hxnum = hxnum;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.hexiao_status = false;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tapp.alert('识别错误');\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tapp.alert('识别错误');\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\t\tapp.alert('识别错误');\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t},\r\n\t\t//加\r\n\t\thxplus: function (e) {\r\n\t\t\tvar hxnum = this.hxnum + 1;\r\n\t\t\tvar synum = this.order.limit_count - this.order.used_count ;\r\n\t\t\tif (hxnum > synum) {\r\n\t\t\t\tapp.error('剩余核销次数不足');\r\n\t\t\t\treturn 1;\r\n\t\t\t}\r\n\t\t\tthis.hxnum = hxnum;\r\n\t\t},\r\n\t\t//减\r\n\t\thxminus: function (e) {\r\n\t\t\tvar hxnum = this.hxnum - 1;\r\n\t\t\tif(hxnum < 1){\r\n\t\t\t\thxnum = 1;\r\n\t\t\t}\r\n\t\t\tthis.hxnum = hxnum;\r\n\t\t},\r\n\t\thxinput: function (e) {\r\n\t\t\tvar hxnum = parseInt(e.detail.value);\r\n\t\t\tconsole.log(hxnum,'---');\r\n\t\t\tif(hxnum < 1)return 1;\r\n\t\t\tvar synum = this.order.limit_count - this.order.used_count ;\r\n\t\t\tif (hxnum > synum) {\r\n\t\t\t\tapp.error('剩余核销次数不足');\r\n\t\t\t\treturn 1;\r\n\t\t\t}\r\n\t\t\tthis.hxnum = hxnum;\r\n\t\t},\r\n\t\tradioChange: function(evt) {\r\n\t\t\t var that=this\r\n\t\t\t var mdid = evt.detail.value\r\n\t\t\t that.mdid = mdid\r\n\t\t}\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.address{ display:flex;width:94%;margin:0 3%;border-radius:12rpx;padding: 20rpx 3%; background: #FFF;margin-top:20rpx;}\r\n.address .img{width:40rpx}\r\n.address image{width:40rpx; height:40rpx;}\r\n.address .info{flex:1;display:flex;flex-direction:column;}\r\n.address .info .t1{font-size:28rpx;font-weight:bold;color:#333}\r\n.address .info .t2{font-size:24rpx;color:#999}\r\n\r\n.product{width:94%;margin:0 3%;border-radius:12rpx;padding: 14rpx 3%;background: #FFF;margin-top:20rpx;}\r\n.product .content{display:flex;position:relative;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;}\r\n.product .content:last-child{ border-bottom: 0; }\r\n.product .content image{ width: 140rpx; height: 140rpx;}\r\n.product .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\r\n.product .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\n.product .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\r\n.product .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\r\n.product .content .detail .x1{ flex:1}\r\n.product .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\r\n.product .content .comment{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc702 solid; border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n.product .content .comment2{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc7c2 solid; border-radius:10rpx;background:#fff; color: #ffc7c2;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\r\n\r\n.orderinfo{ width:94%;margin:0 3%;border-radius:12rpx;margin-top:10rpx;padding: 14rpx 3%;background: #FFF;}\r\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}\r\n.orderinfo .item:last-child{ border-bottom: 0;}\r\n.orderinfo .item .t1{width:200rpx;}\r\n.orderinfo .item .t2{flex:1;text-align:right;}\r\n.orderinfo .item .textarea { text-align: left;white-space:pre-wrap;padding: 20rpx;}\r\n.orderinfo .item .red{color:red}\r\n.orderinfo .topitem{display:flex;padding:60rpx 40rpx;align-items:center;border-bottom:2px dashed #E5E5E5;position:relative}\r\n.orderinfo .topitem .f1{font-size:50rpx;font-weight:bold;}\r\n.orderinfo .topitem .f1 .t1{font-size:60rpx;}\r\n.orderinfo .topitem .f1 .t2{font-size:40rpx;}\r\n.orderinfo .topitem .f2{margin-left:40rpx}\r\n.orderinfo .topitem .f2 .t1{font-size:36rpx;color:#2B2B2B;font-weight:bold;height:50rpx;line-height:50rpx}\r\n.orderinfo .topitem .f2 .t2{font-size:24rpx;color:#999999;height:50rpx;line-height:50rpx;}\r\n\r\n.btn-add{width:90%;max-width:700px;margin:0 auto;height:96rpx;line-height:96rpx;text-align:center;color:#fff;font-size:30rpx;font-weight:bold;border-radius:40rpx;position: fixed;left:0px;right:0;bottom:20rpx;}\r\n.yq_image{height: 60rpx;width: 60rpx;border-radius: 100rpx;margin-right: 10rpx;}\r\n\r\n/*核销次数*/\r\n.addnum{width: 200rpx;display: flex;}\r\n.addnum .plus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n.addnum .minus {width:65rpx;height:48rpx;background:#F6F8F7;display:flex;align-items:center;justify-content:center}\r\n.addnum .input{flex:1;width:50rpx;border:0;text-align:center;color:#2B2B2B;font-size:28rpx;margin: 0 15rpx;}\r\n.addnum .img{width:24rpx;height:24rpx}\r\n\r\n.orderinfo .item .radio-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\r\n.orderinfo .item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center;margin-left: 30rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hexiao.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hexiao.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213051217\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}