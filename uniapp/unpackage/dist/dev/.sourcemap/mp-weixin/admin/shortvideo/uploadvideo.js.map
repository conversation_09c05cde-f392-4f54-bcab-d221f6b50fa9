{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/shortvideo/uploadvideo.vue?fdcc", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/shortvideo/uploadvideo.vue?2ea3", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/shortvideo/uploadvideo.vue?57cc", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/shortvideo/uploadvideo.vue?68f6", "uni-app:///admin/shortvideo/uploadvideo.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/shortvideo/uploadvideo.vue?3d11", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/shortvideo/uploadvideo.vue?0b1c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "datalist", "content_pic", "pagenum", "cateArr", "cindex", "pics", "video", "sysset", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "cateChange", "formsubmit", "console", "title", "cid", "content", "setTimeout", "uploadimg", "uploadvideo", "uni", "sourceType", "maxDuration", "success", "url", "filePath", "name", "fail", "removeimg"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxDA;AAAA;AAAA;AAAA;AAAy0B,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqD71B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QACAD;QACA;UACAC;UACA;QACA;QACAD;QACAA;QACA;QACA;UACA;UACA;YACA;cACAA;YACA;YACAT;UACA;QACA;UACAA;QACA;QACAS;QACAA;MACA;IACA;IACAE;MACA;IACA;IACAC;MACA;MACAC;MACA;MACA;QACA;UACAH;UACA;QACA;QACA;MACA;QACA;MACA;MACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MACAA;QAAAI;QAAAC;QAAAb;QAAAc;QAAAb;MAAA;QACAO;QACA;UACAA;UACAO;YACAP;UACA;QACA;UACAA;QACA;MACA;IACA;IACAQ;MACA;MACA;MACA;MACA;MACAR;QACA;UACAR;QACA;QACA;QACA;QACA;MACA;IACA;IACAiB;MACA;MACA;MACA;MACA;MACA;MACAC;QACAC;QACAC;QACAC;UACA;UACA;UACA;YACAb;YAAA;UACA;UACA;UACAA;UACAU;YACAI;YACAC;YACAC;YACAH;cACAb;cACA;cAEA;gBACAD;gBAEA;kBACA;kBACAP;kBACAO;gBACA;cAEA;gBACAC;cACA;YACA;YACAiB;cACAjB;cACAA;YACA;UACA;QACA;QACAiB;UACAd;QACA;MACA;IACA;;IACAe;MACA;MACA;MACA;MACA;MACA1B;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClOA;AAAA;AAAA;AAAA;AAAsrC,CAAgB,smCAAG,EAAC,C;;;;;;;;;;;ACA1sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/shortvideo/uploadvideo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/shortvideo/uploadvideo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./uploadvideo.vue?vue&type=template&id=d4c68a50&\"\nvar renderjs\nimport script from \"./uploadvideo.vue?vue&type=script&lang=js&\"\nexport * from \"./uploadvideo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uploadvideo.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/shortvideo/uploadvideo.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uploadvideo.vue?vue&type=template&id=d4c68a50&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.pics.length : null\n  var g1 = _vm.isload ? _vm.pics.join(\",\") : null\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uploadvideo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uploadvideo.vue?vue&type=script&lang=js&\"", "<template>\n<view>\n\t<block v-if=\"isload\">\n\t\t<form @submit=\"formsubmit\">\n\t\t<view class=\"st_box\">\n\t\t\t<view class=\"st_form\">\n\t\t\t\t<view v-if=\"cateArr\">\n\t\t\t\t\t<picker @change=\"cateChange\" :value=\"cindex\" :range=\"cateArr\" style=\"height:80rpx;line-height:80rpx;border-bottom:1px solid #EEEEEE;font-size:18px\">\n\t\t\t\t\t\t<view class=\"picker\">{{cindex==-1? '请选择类型' : cateArr[cindex]}}</view>\n\t\t\t\t\t</picker>\n\t\t\t\t</view>\n\t\t\t\t<!-- <view v-if=\"cateArr\">\n\t\t\t\t\t<picker @change=\"cateChange\" :value=\"cindex\" :range=\"cateArr\" style=\"height:80rpx;line-height:80rpx;border-bottom:1px solid #EEEEEE;font-size:18px\">\n\t\t\t\t\t\t<view class=\"picker\">{{cindex==-1? '请选择曝光标签' : cateArr[cindex]}}</view>\n\t\t\t\t\t</picker>\n\t\t\t\t</view> -->\n\t\t\t\t<view><input placeholder=\"输入标题\" name=\"title\" maxlength=\"-1\"/></view>\n\t\t\t\t<view><textarea placeholder=\"输入简介\" name=\"content\" maxlength=\"-1\" style=\"height:100rpx;\"></textarea></view>\n\t\t\t\t<view class=\"uploadbtn_ziti1\">上传封面</view>\n\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx;\">\n\t\t\t\t\t<view v-for=\"(item, index) in pics\" :key=\"index\" class=\"layui-imgbox\">\n\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pics\"><image :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\n\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\n\t\t\t\t\t\t<!-- <view class=\"layui-imgbox-repeat\" @tap=\"xuanzhuan\" :data-index=\"index\" data-field=\"pics\"><text class=\"fa fa-repeat\"></text></view> -->\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pics\" v-if=\"pics.length<1\">\n\t\t\t\t\t\t\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"pics\" :value=\"pics.join(',')\" maxlength=\"-1\"></input>\n\t\t\t\n\t\t\t<view class=\"uploadbtn_ziti2\">上传短视频</view>\n\t\t\t\t<view class=\"flex-y-center\" style=\"width:100%;padding:20rpx 0;margin-top:20rpx;\">\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/uploadvideo.png'\" style=\"width:200rpx;height:200rpx;background:#eee;\" @tap=\"uploadvideo\"></image><text v-if=\"video\" style=\"padding-left:20rpx;color:#333\">已上传短视频</text></view>\n\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"video\" :value=\"video\" maxlength=\"-1\"></input>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"st_button flex-y-center\">\n\t\t\t<button form-type=\"submit\" :style=\"{background:t('color1')}\">发表</button>\n\t\t</view>\n\t\t<view style=\"width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center\" @tap=\"goto\" data-url=\"myupload\">我的发表记录<image :src=\"pre_url+'/static/img/arrowright.png'\" style=\"width:30rpx;height:30rpx\"/></view>\n\t\t<view style=\"width:100%;height:60rpx\"></view>\n\t\t</form>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tpre_url:app.globalData.pre_url,\n      datalist: [],\n      content_pic: [],\n      pagenum: 1,\n      cateArr: [],\n      cindex: -1,\n\t\t\tpics:[],\n      video: '',\n\t\t\tsysset:{},\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiAdminShortvideo/uploadvideo', {}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 0) {\n\t\t\t\t\tapp.alert(res.msg);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthat.clist = res.clist;\n\t\t\t\tthat.sysset = res.sysset;\n\t\t\t\tvar clist = res.clist;\n\t\t\t\tif (clist.length > 0) {\n\t\t\t\t\tvar cateArr = [];\n\t\t\t\t\tfor (var i in clist) {\n\t\t\t\t\t\tif (that.opt && that.opt.cid == clist[i].id) {\n\t\t\t\t\t\t\tthat.cindex = i;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcateArr.push(clist[i].name);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tcateArr = false;\n\t\t\t\t}\n\t\t\t\tthat.cateArr = cateArr\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    cateChange: function (e) {\n      this.cindex = e.detail.value;\n    },\n    formsubmit: function (e) {\n      var that = this;\n      console.log(e);\n      var clist = that.clist;\n      if (clist.length > 0) {\n        if (that.cindex == -1) {\n          app.error('请选择分类');\n          return false;\n        }\n        var cid = clist[that.cindex].id;\n      } else {\n        var cid = 0;\n      }\n      var formdata = e.detail.value;\n      var title = formdata.title;\n\t\t\tif (title == '') {\n        app.error('请输入标题');\n        return false;\n      }\n      var content = formdata.content;\n      var pics = formdata.pics;\n      var video = formdata.video;\n      if (pics == '') {\n        app.error('请上传封面');\n        return false;\n      }\n      if (video == '') {\n        app.error('请上传短视频');\n        return false;\n      }\n      app.post('ApiAdminShortvideo/uploadvideo', {title:title,cid: cid,pics: pics,content: content,video: video}, function (res) {\n        app.showLoading(false);\n        if (res.status == 1) {\n          app.success(res.msg);\n          setTimeout(function () {\n            app.goback(true);\n          }, 1000);\n        } else {\n          app.error(res.msg);\n        }\n      });\n    },\n\t\tuploadimg:function(e){\n\t\t\tvar that = this;\n\t\t\tvar field= e.currentTarget.dataset.field\n\t\t\tvar pics = that[field]\n\t\t\tif(!pics) pics = [];\n\t\t\tapp.chooseImage(function(urls){\n\t\t\t\tfor(var i=0;i<urls.length;i++){\n\t\t\t\t\tpics.push(urls[i]);\n\t\t\t\t}\n\t\t\t\tif(field == 'pic') that.pic = pics;\n\t\t\t\tif(field == 'pics') that.pics = pics;\n\t\t\t\tif(field == 'zhengming') that.zhengming = pics;\n\t\t\t},1)\n\t\t},\n    uploadvideo: function () {\n      var that = this;\n      var maxDuration = that.sysset.upload_maxduration;\n\t\t\tif(!maxDuration) maxDuration = 999999;\n      var maxsize = that.sysset.upload_maxsize;\n\t\t\tif(!maxsize) maxsize = 999999999999999;\n      uni.chooseVideo({\n        sourceType: ['album', 'camera'],\n        maxDuration: maxDuration,\n        success: function (res) {\n          var tempFilePath = res.tempFilePath;\n\t\t\t\t\tvar size = res.size;\n\t\t\t\t\tif(size > maxsize * 1024){\n\t\t\t\t\t\tapp.alert('短视频文件过大');return;\n\t\t\t\t\t}\n\t\t\t\t\t//console.log(size);return;\n          app.showLoading('上传中');\n          uni.uploadFile({\n            url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id+ '/type/1',\n            filePath: tempFilePath,\n            name: 'file',\n            success: function (res) {\n              app.showLoading(false);\n              var data = JSON.parse(res.data);\n\n              if (data.status == 1) {\n                that.video = data.url;\n                \n                if(data.ffmpeg_img){\n                    var pics = []\n                    pics.push(data.ffmpeg_img);\n                    that.pics = pics;\n                }\n                \n              } else {\n                app.alert(data.msg);\n              }\n            },\n            fail: function (res) {\n              app.showLoading(false);\n              app.alert(res.errMsg);\n            }\n          });\n        },\n        fail: function (res) {\n          console.log(res); //alert(res.errMsg);\n        }\n      });\n    },\n\t\tremoveimg:function(e){\n\t\t\tvar that = this;\n\t\t\tvar index= e.currentTarget.dataset.index\n\t\t\tvar field= e.currentTarget.dataset.field\n\t\t\tvar pics = that[field]\n\t\t\tpics.splice(index,1)\n\t\t},\n  }\n};\n</script>\n<style>\npage{background:#f7f7f7}\n.st_box{ padding:0rpx 0 }\n.st_button{ display: flex; justify-content: space-between;padding:24rpx 24rpx 10rpx 24rpx;}\n.st_button button{background: #1658c6;border-radius:6rpx;border: none;padding:0 20rpx;color: #fff;font-size:36rpx;text-align: center;width:100%;display: flex;height:100rpx;justify-content: center;align-items: center;}\n\n.st_form{ padding: 24rpx;background: #ffffff;margin: 10px;border-radius: 15px;}\n.st_form input{ width: 100%;height: 120rpx; border: none;border-bottom:1px solid #EEEEEE;}\n.st_form input::-webkit-input-placeholder { /* WebKit browsers */ color:    #BBBBBB; font-size: 24rpx}\n.st_form textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;border-bottom:1px solid #EEEEEE;}\n\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\n.layui-imgbox-img>image{max-width:100%;}\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\n.uploadbtn_ziti1{height:30rpx; line-height: 30rpx;font-size:30rpx; margin-top: 20rpx;}\n.uploadbtn_ziti2{height:30rpx; line-height: 30rpx;font-size:30rpx; padding-top: 20rpx; margin-top: 20rpx;border-top:1px solid #EEEEEE;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uploadvideo.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uploadvideo.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213040798\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}