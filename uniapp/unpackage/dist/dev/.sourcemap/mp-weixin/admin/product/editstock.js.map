{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/product/editstock.vue?d9da", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/product/editstock.vue?1db6", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/product/editstock.vue?4480", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/product/editstock.vue?eda6", "uni-app:///admin/product/editstock.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/product/editstock.vue?c8e6", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/product/editstock.vue?bf80"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "loading", "pre_url", "info", "pagecontent", "aglevellist", "levellist", "clist", "clist2", "cateArr", "cateArr2", "glist", "groupArr", "freighttypeArr", "freightindex", "freightList", "freightdata", "freightIds", "gui<PERSON><PERSON>", "pic", "pics", "cids", "cids2", "gids", "cnames", "cnames2", "gnames", "clistshow", "clist2show", "glistshow", "ggname", "ggindex", "ggindex2", "oldgglist", "gglist", "catche_detailtxt", "start_time1", "start_time2", "end_time1", "end_time2", "start_hours", "end_hours", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "product_showset", "commission_canset", "bid", "paramList", "paramdata", "resparamdata", "editorFormdata", "business_selfscore", "test", "showtjArr", "onLoad", "methods", "getdata", "that", "app", "id", "thisval", "console", "editorBind<PERSON>icker<PERSON><PERSON><PERSON>", "setfield", "subform", "formdata", "<PERSON><PERSON><PERSON>", "paramformdata", "setTimeout", "detailAddtxt", "dialogDetailtxtClose", "catcheDetailtxt", "dialogDetailtxtConfirm", "detailAddpic", "detailAddvideo", "uni", "sourceType", "success", "url", "filePath", "name", "fail", "detailMoveup", "detailMovedown", "detailMovedel", "changeFrieght", "newfreightIds", "ischecked", "freighttypeChange", "bindStatusChange", "bindStartTime1Change", "bindStartTime2Change", "bindEndTime1Change", "bindEndTime2Change", "bindStartHoursChange", "bindEndHoursChange", "gglistInput", "getgglist", "newlen", "h", "k", "title", "tarr", "sarr", "ks", "titles", "market_price", "cost_price", "sell_price", "weight", "stock", "givescore", "lvprice_data", "addgggroupname", "delgggroupname", "itemList", "newguigedata", "setgggroupname", "items", "addggname", "delggname", "newitems", "index2", "setggname", "cidsChange", "newcids", "<PERSON><PERSON><PERSON>", "cid", "getcnames", "cids2Change", "getcnames2", "gidsChange", "newgids", "<PERSON><PERSON><PERSON>", "changeClistDialog", "changeClist2Dialog", "changeGlistDialog", "uploadimg", "removeimg", "uploadimg2", "removeimg2", "scanprocode", "j<PERSON><PERSON>", "needResult", "scanType", "content"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAAu0B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuE31B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACA;UACA;UACAA;UACAA;QACA;QACA;UACA;UACAA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;QACA;UACAA;UACA;YACAA;UACA;QACA;QACA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEAA;QACAA;QACA;QACA;QACA;QACA;UACA;UACA;YACA;cACAG;YACA;cACAA;YACA;UACA;UAEA;YACA;cACA;gBACAA;cACA;YACA;UACA;UACAV;UACAF;QACA;QACAa;QACAA;QACAJ;QACAA;QACA,cACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAK;MACA;MACA;MACA;MACA;MACA;MACAZ;MACAW;MACA;MACA;MAEA;MACA;IACA;IACAE;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACAC;MACA;QACAA;MACA;MACAA;MACA;MACA;QACAP;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;MACA;MACA;QACAO;MACA;QACAA;QACAA;MACA;QACAA;MACA;QACAA;MACA;MACA;QACA;QACAA;QACAA;QACA;QACA;QACA;MACA;MACA;QACA;QACAA;QACAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;MACA;MACAJ;MACA;MACA;QACA;QACA;UACAH;UAAA;QACA;QACA;UACAQ;QACA;QACA;UACAA;QACA;QACAC;MACA;MAEA;MACAT;QAAAC;QAAAxB;MAAA;QACA;UACAuB;QACA;UACAA;UACAU;YACAV;UACA;QACA;MACA;IACA;IACAW;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAV;MACA;IACA;IACAW;MACA;MACAX;MACA;MACA;MACAxD;QAAA;QAAA;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;UAAA;UAAA;YAAA;UAAA;QAAA;QAAA;QAAA;QAAA;MAAA;MACA;MACA;IACA;IACAoE;MACA;MACAf;QACA;QACA;QACA;UACA;UACArC;YAAA;YAAA;YAAA;YAAA;UAAA;QACA;QACA;QACAhB;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;cAAA;YAAA;YAAA;cAAA;YAAA;UAAA;UAAA;UAAA;UAAA;QAAA;QACAoD;MACA;IACA;IACAiB;MACA;MACAC;QACAC;QACAC;UACA;UACAnB;UACAiB;YACAG;YACAC;YACAC;YACAH;cACAnB;cACA;cACA;gBACAD;gBACA;gBACA;gBACApD;kBAAA;kBAAA;kBAAA;oBAAA;oBAAA;oBAAA;oBAAA;oBAAA;oBAAA;oBAAA;sBAAA;oBAAA;oBAAA;sBAAA;oBAAA;kBAAA;kBAAA;kBAAA;kBAAA;gBAAA;gBACAoD;cACA;gBACAC;cACA;YACA;YACAuB;cACAvB;cACAA;YACA;UACA;QACA;QACAuB;UACApB;QACA;MACA;IACA;;IACAqB;MACA;MACA;MACA,eACA7E;IACA;IACA8E;MACA;MACA;MACA,oCACA9E;IACA;IACA+E;MACA;MACA;MACA/E;IACA;IACAgF;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;QACA;UACAC;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA7D;MACA;MACA0B;IACA;IACAoC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;UAAA;QAAA;QAAA;QACAC;QACAC;QACA;UACAA;YAAAC;YAAAC;UAAA;QACA;MACA;;MAEA;MACA;MACA;MACA;QACA;QACA;UACA;YACAC;UACA;QAAA;QACAC;MACA;MACA1C;MACAA;MAEA;QACA;QACA;QACA;UACA2C;UACAC;QACA;QACAD;QACAC;QACA;QACA;QACA;UACA;QACA;UACA;YAAAD;YAAAxB;YAAA0B;YAAAC;YAAAC;YAAAC;YAAAC;YAAA1F;YAAA2F;YAAAC;UAAA;QACA;QACA7E;MACA;MACA;MACA0B;IACA;IACAoD;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAvC;QACAwC;QACAtC;UACA;YACA;cAAA;cACApB;cACAA;cACAA;cAAA;YACA;cAAA;cACA;cACA;cACA;gBACA;kBACA2D;gBACA;cACA;cACA3D;cACAI;cACAJ;YACA;UACA;QACA;MACA;IACA;IACA4D;MACA;MACA;MACA;QAAA;QACArF;QACAb;UAAAiF;UAAAC;UAAAiB;QAAA;QACA;MACA;QAAA;QACAnG;QACA;MACA;MACA;MACA;IACA;IACAoG;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA7C;QACAwC;QACAtC;UACA;YACA;cAAA;cACApB;cACAA;cACAA;cACAA;cAAA;YACA;cAAA;cACA;cACA;cACA;gBACA;kBACA;kBACA;kBACA;oBACA;sBACAgE;wBAAArB;wBAAAC;sBAAA;sBACAqB;oBACA;kBACA;kBACAvG;gBACA;gBACAiG;cACA;cACA3D;cACAI;cACAJ;YACA;UACA;QACA;MACA;IACA;IACAkE;MACA;MACA;MACA;MACA;QAAA;QACA;QACA1F;QACAqF;UAAAlB;UAAAC;QAAA;QACAlF;QACA;MACA;QAAA;QACAA;QACA;MACA;MACA;MACA;IACA;IACAyG;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;QACA;UACAtC;QACA;MACA;MACA;QACA;UACA7B;UAAA;QACA;QACAmE;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACApE;QAAAqE;MAAA;QACAtE;QACA;QACA;QACA;QACA;QACAI;QACA;UACA;UACAA;UACA;YACA;cACAD;YACA;cACAA;YACA;UACA;UACA;YACA;cACA;gBACAA;cACA;YACA;UACA;UACAV;UACAF;QACA;QACAa;QACAA;QACAJ;QACAA;QACAA;MACA;IACA;IACAuE;MACA;MACA;MACA;MACA;QACAvG;MACA;MACA;IACA;IACAwG;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACAJ;QACA;UACAtC;QACA;MACA;MACA;QACA;UACA7B;UAAA;QACA;QACAmE;MACA;MACA;MACA;IACA;IACAK;MACA;MACA;MACA;MACA;QACAzG;MACA;MACA;IACA;IACA0G;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;QACA;UACA7C;QACA;MACA;MACA;QACA6C;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA1G;MACA;MACA;IACA;IACA2G;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA/E;QACA;UACArC;QACA;QACA;QACA;MACA;IACA;IACAqH;MACA;MACA;MACA;MACA;QACA;QACArH;QACAoC;MACA;QACA;QACApC;QACAoC;MACA;IACA;IACAkF;MACA;MACA;MACAjF;QACAD;MACA;IACA;IACAmF;MACA;MACA;MACAnF;IACA;IACAoF;MACA;MACA;QACA;QACAC;UAAA;UACAA;YACAC;YAAA;YACAC;YAAA;YACAnE;cACA;cACA;gBACAoE;cACA;cACAxF;cACAA;YACA;YACAwB;cACAvB;YACA;UACA;QACA;MACA;QACAiB;UACAE;YACAhB;YACA;YACAJ;YACAA;UACA;UACAwB;YACAvB;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACx0BA;AAAA;AAAA;AAAA;AAAorC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACAxsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/product/editstock.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/product/editstock.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./editstock.vue?vue&type=template&id=62462b64&\"\nvar renderjs\nimport script from \"./editstock.vue?vue&type=script&lang=js&\"\nexport * from \"./editstock.vue?vue&type=script&lang=js&\"\nimport style0 from \"./editstock.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/product/editstock.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editstock.vue?vue&type=template&id=62462b64&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.pic.length : null\n  var g1 = _vm.isload ? _vm.pic.join(\",\") : null\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editstock.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editstock.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<form @submit=\"subform\">\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">商品名称<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"f2\">{{info.name}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item flex-col\" style=\"border-bottom:0\">\r\n\t\t\t\t\t<view class=\"f1\">商品主图<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in pic\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pic\"><image style=\"display:block\" :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pic\" data-pernum=\"1\" v-if=\"pic.length==0\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"pic\" :value=\"pic.join(',')\" maxlength=\"-1\"/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\r\n\t\t\t<!-- 规格列表 -->\r\n\t\t\t<view class=\"form-box\" v-for=\"(item,index) in gglist\" :key=\"index\">\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">规格</view>\r\n\t\t\t\t\t<view class=\"f2\" style=\"font-weight:bold;line-height: 40rpx;\">{{item.name}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">市场价（元）</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" @input=\"gglistInput\" :data-index=\"index\" data-field=\"market_price\" :name=\"'market_price['+index+']'\" :value=\"item.market_price\" placeholder=\"请填写市场价\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">成本价（元）</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" @input=\"gglistInput\" :data-index=\"index\" data-field=\"cost_price\" :name=\"'cost_price['+index+']'\" :value=\"item.cost_price\" placeholder=\"请填写成本价\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">销售价（元）</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" @input=\"gglistInput\" :data-index=\"index\" data-field=\"sell_price\" :name=\"'sell_price['+index+']'\" :value=\"item.sell_price\" placeholder=\"请填写销售价\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">重量（克）</view>\r\n\t\t\t\t\t<view class=\"f2\">{{item.weight}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\" style=\"position:relative\">库存 \r\n\t\t\t\t\t\t<block v-if=\"item.isstock_warning==1\">\r\n\t\t\t\t\t\t\t<view class=\"stockwarning\"><image :src=\"pre_url+'/static/img/workorder/ts.png'\" style=\"width:30rpx;height:30rpx\" >库存不足</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t<input type=\"text\" @input=\"gglistInput\" :data-index=\"index\" data-field=\"stock\" :name=\"'stock['+index+']'\" :value=\"item.stock\" placeholder=\"请填写库存\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t</view>\r\n\r\n\t\t\t<button class=\"savebtn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\" form-type=\"submit\">提交</button>\r\n\t\t\t<view style=\"height:50rpx\"></view>\r\n\t\t</form>\r\n\t</block>\r\n\t<view style=\"display:none\">{{test}}</view>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t<wxxieyi></wxxieyi>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\tisload:false,\r\n\t\t\tloading:false,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n      info:{},\r\n\t\t\tpagecontent:[],\r\n\t\t\taglevellist:[],\r\n\t\t\tlevellist:[],\r\n\t\t\tclist:[],\r\n\t\t\tclist2:[],\r\n\t\t\tcateArr:[],\r\n\t\t\tcateArr2:[],\r\n\t\t\tglist:[],\r\n\t\t\tgroupArr:[],\r\n\t\t\tfreighttypeArr:['全部模板','指定模板','自动发货','在线卡密'],\r\n\t\t\tfreightindex:0,\r\n\t\t\tfreightList:[],\r\n\t\t\tfreightdata:[],\r\n\t\t\tfreightIds:[],\r\n\t\t\tguigedata:[],\r\n\t\t\tpic:[],\r\n\t\t\tpics:[],\r\n\t\t\tcids:[],\r\n\t\t\tcids2:[],\r\n\t\t\tgids:[],\r\n\t\t\tcnames:'',\r\n\t\t\tcnames2:'',\r\n\t\t\tgnames:'',\r\n\t\t\tclistshow:false,\r\n\t\t\tclist2show:false,\r\n\t\t\tglistshow:false,\r\n\t\t\tggname:'',\r\n\t\t\tggindex:0,\r\n\t\t\tggindex2:0,\r\n\t\t\toldgglist:[],\r\n\t\t\tgglist:[],\r\n\t\t\tcatche_detailtxt:'',\r\n\t\t\tstart_time1:'',\r\n\t\t\tstart_time2:'',\r\n\t\t\tend_time1:'',\r\n\t\t\tend_time2:'',\r\n\t\t\tstart_hours:'',\r\n\t\t\tend_hours:'',\r\n\t\t\tgettjArr:['-1'],\r\n\t\t\tproduct_showset:1,\r\n\t\t\tcommission_canset:1,\r\n\t\t\tbid:0,\r\n\t\t\tparamList:[],\r\n\t\t\tparamdata:[],\r\n\t\t\tresparamdata:{},\r\n\t\t\teditorFormdata:[],\r\n\t\t\tbusiness_selfscore:0,\r\n\t\t\ttest:'',\r\n\t\t\tshowtjArr:['-1'],\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n  methods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = that.opt.id ? that.opt.id : '';\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiAdminProduct/edit',{id:id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.info = res.info;\r\n\t\t\t\tif(that.info.start_time){\r\n\t\t\t\t\tvar start_times = (that.info.start_time).split(' ');\r\n\t\t\t\t\tthat.start_time1 = start_times[0];\r\n\t\t\t\t\tthat.start_time2 = start_times[1];\r\n\t\t\t\t}\r\n\t\t\t\tif(that.info.end_time){\r\n\t\t\t\t\tvar end_times = (that.info.end_time).split(' ');\r\n\t\t\t\t\tthat.end_time1 = end_times[0];\r\n\t\t\t\t\tthat.end_time2 = end_times[1];\r\n\t\t\t\t}\r\n\t\t\t\tif(that.info.start_hours){\r\n\t\t\t\t\tthat.start_hours = that.info.start_hours;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.info.end_hours){\r\n\t\t\t\t\tthat.end_hours = that.info.end_hours;\r\n\t\t\t\t}\r\n\t\t\t\tthat.product_showset = res.product_showset\r\n\t\t\t\tthat.commission_canset = res.commission_canset\r\n\t\t\t\tthat.gettjArr = that.info.gettj\r\n\t\t\t\tthat.showtjArr = that.info.showtj\r\n\t\t\t\tthat.pagecontent = res.pagecontent;\r\n\t\t\t\tthat.aglevellist = res.aglevellist;\r\n\t\t\t\tthat.levellist = res.levellist;\r\n\t\t\t\tthat.oldgglist = res.newgglist;\r\n\t\t\t\tthat.clist = res.clist;\r\n\t\t\t\tthat.cateArr = res.cateArr;\r\n\t\t\t\tthat.clist2 = res.clist2;\r\n\t\t\t\tthat.cateArr2 = res.cateArr2;\r\n\t\t\t\tthat.glist = res.glist;\r\n\t\t\t\tthat.groupArr = res.groupArr;\r\n\t\t\t\tthat.freightList = res.freightList;\r\n\t\t\t\tthat.freightdata = res.freightdata;\r\n\t\t\t\tif(res.info.freighttype == 1) that.freightindex = 0;\r\n\t\t\t\tif(res.info.freighttype == 0){\r\n\t\t\t\t\tthat.freightindex = 1;\r\n\t\t\t\t\tif(res.info.freightdata){\r\n\t\t\t\t\t\tthat.freightIds = res.info.freightdata.split(',');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif(res.info.freighttype == 3) that.freightindex = 2;\r\n\t\t\t\tif(res.info.freighttype == 4) that.freightindex = 3;\r\n\t\t\t\tthat.pic = res.pic;\r\n\t\t\t\tthat.pics = res.pics;\r\n\t\t\t\tthat.cids = res.cids;\r\n\t\t\t\tthat.cids2 = res.cids2;\r\n\t\t\t\tthat.gids = res.gids;\r\n\t\t\t\tthat.guigedata = res.guigedata;\r\n\t\t\t\t\r\n\t\t\t\tthat.paramList = res.paramList;\r\n\t\t\t\tthat.resparamdata = res.paramdata;\r\n\t\t\t\tvar paramList = res.paramList;\r\n\t\t\t\tvar editorFormdata = [];\r\n\t\t\t\tvar paramdata = {};\r\n\t\t\t\tfor(var i in paramList){\r\n\t\t\t\t\tvar thisval = res.paramdata[paramList[i].name];\r\n\t\t\t\t\tif(!thisval){\r\n\t\t\t\t\t\tif(paramList[i].type ==2){\r\n\t\t\t\t\t\t\tthisval = [];\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthisval = '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (paramList[i].type == '1') {\r\n\t\t\t\t\t\tfor(var j in paramList[i].params){\r\n\t\t\t\t\t\t\tif(paramList[i].params[j] == thisval){\r\n\t\t\t\t\t\t\t\tthisval = j;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\teditorFormdata.push(thisval);\r\n\t\t\t\t\tparamdata['form'+i] = thisval;\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(editorFormdata)\r\n\t\t\t\tconsole.log(paramdata)\r\n\t\t\t\tthat.editorFormdata = editorFormdata;\r\n\t\t\t\tthat.paramdata = paramdata;\r\n\t\t\t\tif(res.bset)\r\n\t\t\t\tthat.bset = res.bset;\r\n\t\t\t\tthat.bid = res.bid;\r\n\t\t\t\tthat.business_selfscore = res.business_selfscore || 0;\r\n\t\t\t\tthat.getcnames();\r\n\t\t\t\tthat.getcnames2();\r\n\t\t\t\tthat.getgnames();\r\n\t\t\t\tthat.getgglist();\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n\t\teditorBindPickerChange:function(e){\r\n\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\tvar tplindex = e.currentTarget.dataset.tplindex;\r\n\t\t\tvar val = e.detail.value;\r\n\t\t\tvar editorFormdata = this.editorFormdata;\r\n\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\teditorFormdata[idx] = val;\r\n\t\t\tconsole.log(editorFormdata)\r\n\t\t\tthis.editorFormdata = editorFormdata;\r\n\t\t\tthis.test = Math.random();\r\n\r\n\t\t\tvar field = e.currentTarget.dataset.formidx;\r\n\t\t\tthis.paramdata[field] = val;\r\n\t\t},\r\n\t\tsetfield:function(e){\r\n\t\t\tvar field = e.currentTarget.dataset.formidx;\r\n\t\t\tvar value = e.detail.value;\r\n\t\t\tthis.paramdata[field] = value;\r\n\t\t},\r\n    subform: function (e) {\r\n      var that = this;\r\n      var formdata = e.detail.value;\r\n      formdata.cid = that.cids.join(',');\r\n\t\t\tif(that.bid > 0){\r\n\t\t\t\tformdata.cid2 = that.cids2.join(',');\r\n\t\t\t}\r\n      formdata.gid = that.gids;\r\n\t\t\tvar guigedata = that.guigedata;\r\n\t\t\tif(guigedata.length == 0){\r\n\t\t\t\tapp.alert('至少需要添加一个规格');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tfor(var i in guigedata){\r\n\t\t\t\tif(guigedata[i].items.length==0){\r\n\t\t\t\t\tapp.alert('规格分组['+guigedata[i].title+']至少需要添加一个规格');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tvar freightindex = this.freightindex;\r\n\t\t\tif(freightindex == 0){\r\n\t\t\t\tformdata.freighttype = 1;\r\n\t\t\t}else if(freightindex == 1){\r\n\t\t\t\tformdata.freighttype = 0;\r\n\t\t\t\tformdata.freightdata = this.freightIds.join(',');\r\n\t\t\t}else if(freightindex == 2){\r\n\t\t\t\tformdata.freighttype = 3;\r\n\t\t\t}else if(freightindex == 3){\r\n\t\t\t\tformdata.freighttype = 4;\r\n\t\t\t}\r\n\t\t\tif(formdata.status==2){\r\n\t\t\t\tif(!that.start_time1 || !that.start_time2 || !that.end_time1 || !that.end_time2) return app.error('请选择完整时间区间');\r\n\t\t\t\tformdata.start_time = that.start_time1 + ' '+that.start_time2;\r\n\t\t\t\tformdata.end_time = that.end_time1 + ' '+that.end_time2;\r\n\t\t\t\tlet obj1 = new Date(formdata.start_time)\r\n\t\t\t\tlet obj = new Date(formdata.end_time)\r\n\t\t\t\tif (obj1.getTime() > obj.getTime()) return app.error('开始时间不能大于结束时间');\r\n\t\t\t}\r\n\t\t\tif(formdata.status==3){\r\n\t\t\t\tif(!that.start_hours || !that.end_hours) return app.error('请选择完整时间区间');\r\n\t\t\t\tformdata.start_hours = that.start_hours;\r\n\t\t\t\tformdata.end_hours = that.end_hours;\r\n\t\t\t\tlet NewDate = new Date();\r\n\t\t\t\tlet year = NewDate.getFullYear();\r\n\t\t\t\tlet month = NewDate.getMonth() + 1 < 10 ? \"0\" + (NewDate.getMonth() + 1) : NewDate.getMonth() + 1;\r\n\t\t\t\tlet day = NewDate.getDate() < 10 ? \"0\" + NewDate.getDate() : NewDate.getDate();\r\n\t\t\t\tlet obj1 = new Date(year + '-' + month + '-'+ day + ' ' + formdata.start_hours);\r\n\t\t\t\tlet obj = new Date(year + '-' + month + '-'+ day + ' ' + formdata.end_hours);\r\n\t\t\t\tif (obj1.getTime() > obj.getTime()) return app.error('开始时间不能大于结束时间');\r\n\t\t\t}\r\n\t\t\tvar paramdata = this.paramdata;\r\n\t\t\tvar paramformdata = {};\r\n\t\t\tconsole.log(paramdata)\r\n\t\t\tvar paramList = that.paramList;\r\n\t\t\tfor (var i = 0; i < paramList.length;i++){\r\n\t\t\t\tvar paramval = paramdata['form' + i]  === undefined ? '' : paramdata['form' + i];\r\n\t\t\t\tif (paramList[i].is_required == 1 && (paramdata['form' + i] === '' || paramdata['form' + i] === null || paramdata['form' + i] === undefined || paramdata['form' + i].length==0)){\r\n\t\t\t\t\t\tapp.alert(paramList[i].name+' 必填');return;\r\n\t\t\t\t}\r\n\t\t\t\tif (paramList[i].type == '1'){\r\n\t\t\t\t\t\tparamval = paramList[i].params[paramdata['form' + i]];\r\n\t\t\t\t}\r\n\t\t\t\tif (paramList[i].type == '2' && paramval === ''){\r\n\t\t\t\t\tparamval = [];\r\n\t\t\t\t}\r\n\t\t\t\tparamformdata[paramList[i].name] = paramval;\r\n\t\t\t}\r\n\r\n      var id = that.opt.id ? that.opt.id : '';\r\n      app.post('ApiAdminProduct/savestock', {id:id,gglist:that.gglist}, function (res) {\r\n        if (res.status == 0) {\r\n          app.error(res.msg);\r\n        } else {\r\n          app.success(res.msg);\r\n          setTimeout(function () {\r\n            app.goto('index', 'redirect');\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n\t\tdetailAddtxt:function(){\r\n\t\t\tthis.$refs.dialogDetailtxt.open();\r\n\t\t},\r\n\t\tdialogDetailtxtClose:function(){\r\n\t\t\tthis.$refs.dialogDetailtxt.close();\r\n\t\t},\r\n\t\tcatcheDetailtxt:function(e){\r\n\t\t\tconsole.log(e)\r\n\t\t\tthis.catche_detailtxt = e.detail.value;\r\n\t\t},\r\n\t\tdialogDetailtxtConfirm:function(e){\r\n\t\t\tvar detailtxt = this.catche_detailtxt;\r\n\t\t\tconsole.log(detailtxt)\r\n\t\t\tvar Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tpagecontent.push({\"id\":Mid,\"temp\":\"text\",\"params\":{\"content\":detailtxt,\"showcontent\":detailtxt,\"bgcolor\":\"#ffffff\",\"fontsize\":\"14\",\"lineheight\":\"20\",\"letter_spacing\":\"0\",\"bgpic\":\"\",\"align\":\"left\",\"color\":\"#000\",\"margin_x\":\"0\",\"margin_y\":\"0\",\"padding_x\":\"5\",\"padding_y\":\"5\",\"quanxian\":{\"all\":true},\"platform\":{\"all\":true}},\"data\":\"\",\"other\":\"\",\"content\":\"\"});\r\n\t\t\tthis.pagecontent = pagecontent;\r\n\t\t\tthis.$refs.dialogDetailtxt.close();\r\n\t\t},\r\n\t\tdetailAddpic:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tvar Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\t\tvar pics = [];\r\n\t\t\t\tfor(var i in urls){\r\n\t\t\t\t\tvar picid = 'p' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\t\t\tpics.push({\"id\":picid,\"imgurl\":urls[i],\"hrefurl\":\"\",\"option\":\"0\"})\r\n\t\t\t\t}\r\n\t\t\t\tvar pagecontent = that.pagecontent;\r\n\t\t\t\tpagecontent.push({\"id\":Mid,\"temp\":\"picture\",\"params\":{\"bgcolor\":\"#FFFFFF\",\"margin_x\":\"0\",\"margin_y\":\"0\",\"padding_x\":\"0\",\"padding_y\":\"0\",\"quanxian\":{\"all\":true},\"platform\":{\"all\":true}},\"data\":pics,\"other\":\"\",\"content\":\"\"});\r\n\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t},9);\r\n\t\t},\r\n\t\tdetailAddvideo:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.chooseVideo({\r\n        sourceType: ['album', 'camera'],\r\n        success: function (res) {\r\n          var tempFilePath = res.tempFilePath;\r\n          app.showLoading('上传中');\r\n          uni.uploadFile({\r\n            url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id,\r\n            filePath: tempFilePath,\r\n            name: 'file',\r\n            success: function (res) {\r\n              app.showLoading(false);\r\n              var data = JSON.parse(res.data);\r\n              if (data.status == 1) {\r\n                that.video = data.url;\r\n\t\t\t\t\t\t\t\tvar pagecontent = that.pagecontent;\r\n\t\t\t\t\t\t\t\tvar Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\t\t\t\t\t\tpagecontent.push({\"id\":Mid,\"temp\":\"video\",\"params\":{\"bgcolor\":\"#FFFFFF\",\"margin_x\":\"0\",\"margin_y\":\"0\",\"padding_x\":\"0\",\"padding_y\":\"0\",\"src\":data.url,\"quanxian\":{\"all\":true},\"platform\":{\"all\":true}},\"data\":\"\",\"other\":\"\",\"content\":\"\"});\r\n\t\t\t\t\t\t\t\tthat.pagecontent = pagecontent;\r\n              } else {\r\n                app.alert(data.msg);\r\n              }\r\n            },\r\n            fail: function (res) {\r\n              app.showLoading(false);\r\n              app.alert(res.errMsg);\r\n            }\r\n          });\r\n        },\r\n        fail: function (res) {\r\n          console.log(res); //alert(res.errMsg);\r\n        }\r\n      });\r\n\t\t},\r\n\t\tdetailMoveup:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tif(index > 0)\r\n\t\t\t\tpagecontent[index] = pagecontent.splice(index-1, 1, pagecontent[index])[0];\r\n\t\t},\r\n\t\tdetailMovedown:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tif(index < pagecontent.length-1)\r\n\t\t\t\tpagecontent[index] = pagecontent.splice(index+1, 1, pagecontent[index])[0];\r\n\t\t},\r\n\t\tdetailMovedel:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tpagecontent.splice(index,1);\r\n\t\t},\r\n\t\tchangeFrieght:function(e){\r\n\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar freightIds = this.freightIds;\r\n\t\t\tvar newfreightIds = [];\r\n\t\t\tvar ischecked = false;\r\n\t\t\tfor(var i in freightIds){\r\n\t\t\t\tif(freightIds[i] != id){\r\n\t\t\t\t\tnewfreightIds.push(freightIds[i]);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tischecked = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(!ischecked) newfreightIds.push(id);\r\n\t\t\tthis.freightIds = newfreightIds;\r\n\t\t},\r\n\t\tfreighttypeChange:function(e){\r\n\t\t\tthis.freightindex = e.detail.value;\r\n\t\t},\r\n\t\tbindStatusChange:function(e){\r\n\t\t\tthis.info.status = e.detail.value;\r\n\t\t},\r\n\t\tbindStartTime1Change:function(e){\r\n\t\t\tthis.start_time1 = e.target.value\r\n\t\t},\r\n\t\tbindStartTime2Change:function(e){\r\n\t\t\tthis.start_time2 = e.target.value\r\n\t\t},\r\n\t\tbindEndTime1Change:function(e){\r\n\t\t\tthis.end_time1 = e.target.value\r\n\t\t},\r\n\t\tbindEndTime2Change:function(e){\r\n\t\t\tthis.end_time2 = e.target.value\r\n\t\t},\r\n\t\tbindStartHoursChange:function(e){\r\n\t\t\tthis.start_hours = e.target.value\r\n\t\t},\r\n\t\tbindEndHoursChange:function(e){\r\n\t\t\tthis.end_hours = e.target.value\r\n\t\t},\r\n\t\tgglistInput:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar field = e.currentTarget.dataset.field;\r\n\t\t\tvar gglist = this.gglist;\r\n\t\t\tgglist[index][field] = e.detail.value;\r\n\t\t\tthis.gglist = gglist;\r\n\t\t\tconsole.log(gglist)\r\n\t\t},\r\n\t\tgetgglist:function(){\r\n\t\t\tvar oldgglist = this.oldgglist;\r\n\t\t\tvar guigedata = this.guigedata;\r\n\t\t\tvar gglist = [];\r\n\t\t\tvar len = guigedata.length;\r\n\t\t\tvar newlen = 1; \r\n\t\t\tvar h = new Array(len);\r\n\t\t\tfor(var i=0;i<len;i++){\r\n\t\t\t\tvar itemlen = guigedata[i].items.length;\r\n\t\t\t\tif(itemlen<=0) { return; };\r\n\t\t\t\tnewlen*=itemlen;\r\n\t\t\t\th[i] = new Array(itemlen);\r\n\t\t\t\tfor(var j=0;j<itemlen;j++){\r\n\t\t\t\t\th[i][j] = { k:guigedata[i].items[j].k,title:guigedata[i].items[j].title};\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t \r\n\t\t\t//排列组合算法\r\n\t\t\tvar arr = h;  //原二维数组\r\n\t\t\tvar sarr = [[]];  //排列组合后的数组\r\n\t\t\tfor (var i = 0; i < arr.length; i++) {\r\n\t\t\t\tvar tarr = [];\r\n\t\t\t\tfor (var j = 0; j < sarr.length; j++)\r\n\t\t\t\t\tfor (var k = 0; k < arr[i].length; k++){\r\n\t\t\t\t\t\ttarr.push(sarr[j].concat(arr[i][k]));\r\n\t\t\t\t\t}\r\n\t\t\t\t\tsarr = tarr;\r\n\t\t\t}\r\n\t\t\tconsole.log(sarr);\r\n\t\t\tconsole.log(' ------ ');\r\n\t\t\r\n\t\t\tfor(var i=0;i<sarr.length;i++){\r\n\t\t\t\tvar ks = [];\r\n\t\t\t\tvar titles = [];\r\n\t\t\t\tfor(var j=0;j<sarr[i].length;j++){\r\n\t\t\t\t\tks.push( sarr[i][j].k);\r\n\t\t\t\t\ttitles.push( sarr[i][j].title);\r\n\t\t\t\t}\r\n\t\t\t\tks =ks.join(',');\r\n\t\t\t\ttitles =titles.join(',');\r\n\t\t\t\t//console.log(ks);\r\n\t\t\t\t//console.log(titles);\r\n\t\t\t\tif(typeof(oldgglist[ks])!='undefined'){\r\n\t\t\t\t\tvar val = oldgglist[ks];\r\n\t\t\t\t}else{\r\n\t\t\t\t\tvar val = { ks:ks,name:titles,market_price:'',cost_price:'',sell_price:'',weight:'100',stock:'1000',pic:'',givescore:'0',lvprice_data:null};\r\n\t\t\t\t}\r\n\t\t\t\tgglist.push(val);\r\n\t\t\t}\r\n\t\t\tthis.gglist = gglist;\r\n\t\t\tconsole.log(gglist);\r\n\t\t},\r\n\t\taddgggroupname:function(e){\r\n\t\t\tthis.ggname = '';\r\n\t\t\tthis.ggindex = -1;\r\n\t\t\tthis.$refs.dialogInput2.open();\r\n\t\t},\r\n\t\tdelgggroupname:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar ggindex = e.currentTarget.dataset.index;\r\n\t\t\tvar title = e.currentTarget.dataset.title;\r\n\t\t\tuni.showActionSheet({\r\n        itemList: [ '修改','删除'],\r\n        success: function (res) {\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tif (res.tapIndex == 0) { //修改规格项\r\n\t\t\t\t\t\t\tthat.ggname = title;\r\n\t\t\t\t\t\t\tthat.ggindex = ggindex;\r\n\t\t\t\t\t\t\tthat.$refs.dialogInput2.open();return;\r\n\t\t\t\t\t\t}else if (res.tapIndex == 1) { //删除规格项\r\n\t\t\t\t\t\t\tvar guigedata = that.guigedata;\r\n\t\t\t\t\t\t\tvar newguigedata = [];\r\n\t\t\t\t\t\t\tfor(var i in guigedata){\r\n\t\t\t\t\t\t\t\tif(i != ggindex){\r\n\t\t\t\t\t\t\t\t\tnewguigedata.push(guigedata[i]);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthat.guigedata = newguigedata;\r\n\t\t\t\t\t\t\tconsole.log(newguigedata);\r\n\t\t\t\t\t\t\tthat.getgglist();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tsetgggroupname:function(done,val){\r\n\t\t\tvar guigedata = this.guigedata;\r\n\t\t\tvar ggindex = this.ggindex;\r\n\t\t\tif(ggindex == -1){ //新增规格分组\r\n\t\t\t\tggindex = guigedata.length;\r\n\t\t\t\tguigedata.push({k:ggindex,title:val,items:[]});\r\n\t\t\t\tthis.guigedata = guigedata;\r\n\t\t\t}else{ //修改规格分组名称\r\n\t\t\t\tguigedata[ggindex].title = val;\r\n\t\t\t\tthis.guigedata = guigedata;\r\n\t\t\t}\r\n\t\t\tthis.$refs.dialogInput2.close();\r\n\t\t\tthis.getgglist();\r\n\t\t},\r\n\t\taddggname:function(e){\r\n\t\t\tvar ggindex = e.currentTarget.dataset.index;\r\n\t\t\tthis.ggname = '';\r\n\t\t\tthis.ggindex = ggindex;\r\n\t\t\tthis.ggindex2 = -1;\r\n\t\t\tthis.$refs.dialogInput.open();\r\n\t\t},\r\n\t\tdelggname:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar ggindex = e.currentTarget.dataset.index;\r\n\t\t\tvar ggindex2 = e.currentTarget.dataset.index2;\r\n\t\t\tvar k = e.currentTarget.dataset.k;\r\n\t\t\tvar title = e.currentTarget.dataset.title;\r\n\t\t\tuni.showActionSheet({\r\n        itemList: [ '修改','删除'],\r\n        success: function (res) {\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tif (res.tapIndex == 0) { //修改规格项\r\n\t\t\t\t\t\t\tthat.ggname = title;\r\n\t\t\t\t\t\t\tthat.ggindex = ggindex;\r\n\t\t\t\t\t\t\tthat.ggindex2 = ggindex2;\r\n\t\t\t\t\t\t\tthat.$refs.dialogInput.open();return;\r\n\t\t\t\t\t\t}else if (res.tapIndex == 1) { //删除规格项\r\n\t\t\t\t\t\t\tvar guigedata = that.guigedata;\r\n\t\t\t\t\t\t\tvar newguigedata = [];\r\n\t\t\t\t\t\t\tfor(var i in guigedata){\r\n\t\t\t\t\t\t\t\tif(i == ggindex){\r\n\t\t\t\t\t\t\t\t\tvar newitems = [];\r\n\t\t\t\t\t\t\t\t\tvar index2 = 0;\r\n\t\t\t\t\t\t\t\t\tfor(var j in guigedata[i].items){\r\n\t\t\t\t\t\t\t\t\t\tif(j!=ggindex2){\r\n\t\t\t\t\t\t\t\t\t\t\tnewitems.push({k:index2,title:guigedata[i].items[j].title});\r\n\t\t\t\t\t\t\t\t\t\t\tindex2++;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tguigedata[i].items = newitems;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tnewguigedata.push(guigedata[i]);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthat.guigedata = newguigedata;\r\n\t\t\t\t\t\t\tconsole.log(newguigedata)\r\n\t\t\t\t\t\t\tthat.getgglist();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tsetggname:function(done,val){\r\n\t\t\tvar guigedata = this.guigedata;\r\n\t\t\tvar ggindex = this.ggindex;\r\n\t\t\tvar ggindex2 = this.ggindex2;\r\n\t\t\tif(ggindex2 == -1){ //新增规格名称\r\n\t\t\t\tvar items = guigedata[ggindex].items;\r\n\t\t\t\tggindex2 = items.length;\r\n\t\t\t\titems.push({k:ggindex2,title:val});\r\n\t\t\t\tguigedata[ggindex].items = items;\r\n\t\t\t\tthis.guigedata = guigedata;\r\n\t\t\t}else{ //修改规格名称\r\n\t\t\t\tguigedata[ggindex].items[ggindex2].title = val;\r\n\t\t\t\tthis.guigedata = guigedata;\r\n\t\t\t}\r\n\t\t\tthis.$refs.dialogInput.close();\r\n\t\t\tthis.getgglist();\r\n\t\t},\r\n\t\tcidsChange:function(e){\r\n\t\t\tvar clist = this.clist;\r\n\t\t\tvar cids = this.cids;\r\n\t\t\tvar cid = e.currentTarget.dataset.id;\r\n\t\t\tvar newcids = [];\r\n\t\t\tvar ischecked = false;\r\n\t\t\tfor(var i in cids){\r\n\t\t\t\tif(cids[i] != cid){\r\n\t\t\t\t\tnewcids.push(cids[i]);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tischecked = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(ischecked==false){\r\n\t\t\t\tif(newcids.length >= 5){\r\n\t\t\t\t\tapp.error('最多只能选择五个分类');return;\r\n\t\t\t\t}\r\n\t\t\t\tnewcids.push(cid);\r\n\t\t\t}\r\n\t\t\tthis.cids = newcids;\r\n\t\t\tthis.getcnames();\r\n\t\t\tthis.getparams();\r\n\t\t},\r\n\t\tgetparams:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tapp.post('ApiAdminProduct/getParam', {cid:(this.cids).join(',')}, function (res) {\r\n\t\t\t\tthat.paramList = res.paramList;\r\n\t\t\t\t//that.paramdata = res.paramdata;\r\n\t\t\t\tvar paramList = res.paramList;\r\n\t\t\t\tvar editorFormdata = [];\r\n\t\t\t\tvar paramdata = {};\r\n\t\t\t\t\tconsole.log(that.paramdata);\r\n\t\t\t\tfor(var i in paramList){\r\n\t\t\t\t\tvar thisval = that.resparamdata[paramList[i].name];\r\n\t\t\t\t\tconsole.log(thisval);\r\n\t\t\t\t\tif(!thisval){\r\n\t\t\t\t\t\tif(paramList[i].type ==2){\r\n\t\t\t\t\t\t\tthisval = [];\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthisval = '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (paramList[i].type == '1') {\r\n\t\t\t\t\t\tfor(var j in paramList[i].params){\r\n\t\t\t\t\t\t\tif(paramList[i].params[j] == thisval){\r\n\t\t\t\t\t\t\t\tthisval = j;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\teditorFormdata.push(thisval);\r\n\t\t\t\t\tparamdata['form'+i] = thisval;\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(editorFormdata)\r\n\t\t\t\tconsole.log(paramdata)\r\n\t\t\t\tthat.editorFormdata = editorFormdata;\r\n\t\t\t\tthat.paramdata = paramdata;\r\n\t\t\t\tthat.test = Math.random();\r\n      });\r\n\t\t},\r\n\t\tgetcnames:function(){\r\n\t\t\tvar cateArr = this.cateArr;\r\n\t\t\tvar cids = this.cids;\r\n\t\t\tvar cnames = [];\r\n\t\t\tfor(var i in cids){\r\n\t\t\t\tcnames.push(cateArr[cids[i]]);\r\n\t\t\t}\r\n\t\t\tthis.cnames = cnames.join(',');\r\n\t\t},\r\n\t\tcids2Change:function(e){\r\n\t\t\tvar clist = this.clist2;\r\n\t\t\tvar cids = this.cids2;\r\n\t\t\tvar cid = e.currentTarget.dataset.id;\r\n\t\t\tvar newcids = [];\r\n\t\t\tvar ischecked = false;\r\n\t\t\tfor(var i in cids){\r\n\t\t\t\tif(cids[i] != cid){\r\n\t\t\t\t\tnewcids.push(cids[i]);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tischecked = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(ischecked==false){\r\n\t\t\t\tif(newcids.length >= 5){\r\n\t\t\t\t\tapp.error('最多只能选择五个分类');return;\r\n\t\t\t\t}\r\n\t\t\t\tnewcids.push(cid);\r\n\t\t\t}\r\n\t\t\tthis.cids2 = newcids;\r\n\t\t\tthis.getcnames2();\r\n\t\t},\r\n\t\tgetcnames2:function(){\r\n\t\t\tvar cateArr = this.cateArr2;\r\n\t\t\tvar cids = this.cids2;\r\n\t\t\tvar cnames = [];\r\n\t\t\tfor(var i in cids){\r\n\t\t\t\tcnames.push(cateArr[cids[i]]);\r\n\t\t\t}\r\n\t\t\tthis.cnames2 = cnames.join(',');\r\n\t\t},\r\n\t\tgidsChange:function(e){\r\n\t\t\tvar glist = this.glist;\r\n\t\t\tvar gids = this.gids;\r\n\t\t\tvar gid = e.currentTarget.dataset.id;\r\n\t\t\tvar newgids = [];\r\n\t\t\tvar ischecked = false;\r\n\t\t\tfor(var i in gids){\r\n\t\t\t\tif(gids[i] != gid){\r\n\t\t\t\t\tnewgids.push(gids[i]);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tischecked = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(ischecked==false){\r\n\t\t\t\tnewgids.push(gid);\r\n\t\t\t}\r\n\t\t\tthis.gids = newgids;\r\n\t\t\tthis.getgnames();\r\n\t\t},\r\n\t\tgetgnames:function(){\r\n\t\t\tvar groupArr = this.groupArr;\r\n\t\t\tvar gids = this.gids;\r\n\t\t\tvar gnames = [];\r\n\t\t\tfor(var i in gids){\r\n\t\t\t\tgnames.push(groupArr[gids[i]]);\r\n\t\t\t}\r\n\t\t\tthis.gnames = gnames.join(',');\r\n\t\t},\r\n\t\tchangeClistDialog:function(){\r\n\t\t\tthis.clistshow = !this.clistshow\r\n\t\t},\r\n\t\tchangeClist2Dialog:function(){\r\n\t\t\tthis.clist2show = !this.clist2show\r\n\t\t},\r\n\t\tchangeGlistDialog:function(){\r\n\t\t\tthis.glistshow = !this.glistshow\r\n\t\t},\r\n\t\tuploadimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar pernum = parseInt(e.currentTarget.dataset.pernum);\r\n\t\t\tif(!pernum) pernum = 1;\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tvar pics = that[field]\r\n\t\t\tif(!pics) pics = [];\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tfor(var i=0;i<urls.length;i++){\r\n\t\t\t\t\tpics.push(urls[i]);\r\n\t\t\t\t}\r\n\t\t\t\tif(field == 'pic') that.pic = pics;\r\n\t\t\t\tif(field == 'pics') that.pics = pics;\r\n\t\t\t},pernum);\r\n\t\t},\r\n\t\tremoveimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tif(field == 'pic'){\r\n\t\t\t\tvar pics = that.pic\r\n\t\t\t\tpics.splice(index,1);\r\n\t\t\t\tthat.pic = pics;\r\n\t\t\t}else if(field == 'pics'){\r\n\t\t\t\tvar pics = that.pics\r\n\t\t\t\tpics.splice(index,1);\r\n\t\t\t\tthat.pics = pics;\r\n\t\t\t}\r\n\t\t},\r\n\t\tuploadimg2:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tthat.gglist[index].pic = urls[0];\r\n\t\t\t},1);\r\n\t\t},\r\n\t\tremoveimg2:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\tthat.gglist[index].pic = '';\r\n\t\t},\r\n\t\tscanprocode: function (d) {\r\n\t\t\tvar that = this;\r\n\t\t\tif(app.globalData.platform == 'mp'){\r\n\t\t\t\tvar jweixin = require('jweixin-module');\r\n\t\t\t\tjweixin.ready(function () {   //需在用户可能点击分享按钮前就先调用\r\n\t\t\t\t\tjweixin.scanQRCode({\r\n\t\t\t\t\t\tneedResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，\r\n\t\t\t\t\t\tscanType: [\"qrCode\",\"barCode\"], // 可以指定扫二维码还是一维码，默认二者都有\r\n\t\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\t\tvar content = res.resultStr; // 当needResult 为 1 时，扫码返回的结果\r\n\t\t\t\t\t\t\tif(content.indexOf(',') > 0){\r\n\t\t\t\t\t\t\t\tcontent = content.split(',')[1];\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthat.info.procode = content\r\n\t\t\t\t\t\t\tthat.test = Math.random();\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail:function(err){\r\n\t\t\t\t\t\t\tapp.error(err.errMsg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t}else{\r\n\t\t\t\tuni.scanCode({\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\tvar content = res.result;\r\n\t\t\t\t\t\tthat.info.procode = content\r\n\t\t\t\t\t\tthat.test = Math.random();\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail:function(err){\r\n\t\t\t\t\t\tapp.error(err.errMsg);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n  }\r\n};\r\n</script>\r\n<style>\r\nradio{transform: scale(0.6);}\r\ncheckbox{transform: scale(0.6);}\r\n.form-box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}\r\n.form-item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }\r\n.form-item .f1{color:#222;width:200rpx;flex-shrink:0}\r\n.form-item .f2{display:flex;align-items:center}\r\n.form-box .form-item:last-child{ border:none}\r\n.form-box .flex-col{padding-bottom:20rpx}\r\n.form-item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}\r\n.form-item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}\r\n.form-item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }\r\n.form-item .upload_pic image{ width: 32rpx;height: 32rpx; }\r\n.savebtn{ width: 90%; height:96rpx; line-height: 96rpx; text-align:center;border-radius:48rpx; color: #fff;font-weight:bold;margin: 0 5%; margin-top:60rpx; border: none; }\r\n\r\n.ggtitle{height:60rpx;line-height:60rpx;color:#111;font-weight:bold;font-size:26rpx;display:flex;border-bottom:1px solid #f4f4f4}\r\n.ggtitle .t1{width:200rpx;}\r\n.ggcontent{line-height:60rpx;margin-top:10rpx;color:#111;font-size:26rpx;display:flex}\r\n.ggcontent .t1{width:200rpx;display:flex;align-items:center;flex-shrink:0}\r\n.ggcontent .t1 .edit{width:40rpx;height:40rpx}\r\n.ggcontent .t2{display:flex;flex-wrap:wrap;align-items:center}\r\n.ggcontent .ggname{background:#f55;color:#fff;height:40rpx;line-height:40rpx;padding:0 20rpx;border-radius:8rpx;margin-right:20rpx;margin-bottom:10rpx;font-size:24rpx;position:relative}\r\n.ggcontent .ggname .close{position:absolute;top:-14rpx;right:-14rpx;background:#fff;height:28rpx;width:28rpx;border-radius:14rpx}\r\n.ggcontent .ggnameadd{background:#ccc;font-size:36rpx;color:#fff;height:40rpx;line-height:40rpx;padding:0 20rpx;border-radius:8rpx;margin-right:20rpx;margin-left:10rpx;position:relative}\r\n.ggcontent .ggadd{font-size:26rpx;color:#558}\r\n\r\n.ggbox{line-height:50rpx;}\r\n\r\n\r\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.layui-imgbox-img>image{max-width:100%;}\r\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n\r\n\r\n.clist-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\r\n.radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\r\n.radio .radio-img{width:100%;height:100%;display:block}\r\n\r\n.freightitem{width:100%;height:60rpx;display:flex;align-items:center;margin-left:40rpx}\r\n.freightitem .f1{color:#666;flex:1}\r\n\r\n.detailop{display:flex;line-height:60rpx}\r\n.detailop .btn{border:1px solid #ccc;margin-right:10rpx;padding:0 16rpx;color:#222;border-radius:10rpx}\r\n.detaildp{position:relative;line-height:50rpx}\r\n.detaildp .op{width:100%;display:flex;justify-content:flex-end;font-size:24rpx;height:60rpx;line-height:60rpx;margin-top:10rpx}\r\n.detaildp .op .btn{background:rgba(0,0,0,0.4);margin-right:10rpx;padding:0 10rpx;color:#fff}\r\n.detaildp .detailbox{border:2px dashed #00a0e9}\r\n\r\n.uni-popup-dialog {width: 300px;border-radius: 5px;background-color: #fff;}\r\n.uni-dialog-title {display: flex;flex-direction: row;justify-content: center;padding-top: 15px;padding-bottom: 5px;}\r\n.uni-dialog-title-text {font-size: 16px;font-weight: 500;}\r\n.uni-dialog-content {display: flex;flex-direction: row;justify-content: center;align-items: center;padding: 5px 15px 15px 15px;}\r\n.uni-dialog-content-text {font-size: 14px;color: #6e6e6e;}\r\n.uni-dialog-button-group {display: flex;flex-direction: row;border-top-color: #f5f5f5;border-top-style: solid;border-top-width: 1px;}\r\n.uni-dialog-button {display: flex;flex: 1;flex-direction: row;justify-content: center;align-items: center;height: 45px;}\r\n.uni-border-left {border-left-color: #f0f0f0;border-left-style: solid;border-left-width: 1px;}\r\n.uni-dialog-button-text {font-size: 14px;}\r\n.uni-button-color {color: #007aff;}\r\n\r\n.stockwarning{ position: absolute; right:0rpx; bottom:0;display:flex; align-items:center;font-size:24rpx;color:red;   }\r\n.stockwarning image{  margin-right:10rpx}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editstock.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editstock.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213700549\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}