{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/product/index.vue?b852", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/product/index.vue?a17c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/product/index.vue?ad72", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/product/index.vue?1a39", "uni-app:///admin/product/index.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/product/index.vue?2438", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/product/index.vue?8375"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "st", "datalist", "pagenum", "nodata", "nomore", "loading", "count0", "count1", "countall", "sclist", "keyword", "pre_url", "manage_set", "clistshow", "clist", "cids", "bid", "onLoad", "onPullDownRefresh", "onReachBottom", "onNavigationBarSearchInputConfirmed", "detail", "value", "onNavigationBarButtonTap", "methods", "pageMarker", "cidsChange", "newcids", "ischecked", "getclist", "app", "id", "that", "changeClistDialog", "changeClistDialogsearch", "changeClistDialogReset", "changetab", "getdata", "todel", "setst", "searchConfirm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClJA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmGv1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;MAAAC;QAAAC;MAAA;IAAA;EACA;EACAC;IACA;MACA;IACA;EACA;EACAC;IACAC,mCAQA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;QACA;UACAC;QACA;MACA;MACA;QACAD;MACA;MACA;MACA;IACA;IACA;IACAE;MACA;MACA;MACAC;QAAAC;MAAA;QACA;UACAC;QACA;UACAA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACAJ;MACAA;IACA;IACAK;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACAL;MACAA;MACAA;MACAF;QAAApB;QAAAR;QAAAF;QAAAe;MAAA;QACAiB;QACA;QACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAM;MACA;MACA;MACAR;QACAA;UAAAC;QAAA;UACA;YACAD;YACAE;UACA;YACAF;UACA;QACA;MACA;IACA;IACAS;MACA;MACA;MACA;MACAT;QACAA;UAAA9B;UAAA+B;QAAA;UACA;YACAD;YACAE;UACA;YACAF;UACA;QACA;MACA;IACA;IACAU;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACpRA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/product/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/product/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=13bee5d4&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/product/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=13bee5d4&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.cids.length\n  var m0 = g0 > 0 ? _vm.t(\"color1\") : null\n  var g1 = _vm.cids.length\n  var m1 = g1 > 0 ? _vm.t(\"color1\") : null\n  var l0 = _vm.__map(_vm.datalist, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m2 =\n      item.plate_id != 0 &&\n      _vm.manage_set &&\n      _vm.manage_set.status_product == 1 &&\n      (!item.status || item.status == 0)\n        ? _vm.t(\"color1\")\n        : null\n    var m3 =\n      item.plate_id != 0 &&\n      _vm.manage_set &&\n      _vm.manage_set.status_product == 1 &&\n      !(!item.status || item.status == 0)\n        ? _vm.t(\"color2\")\n        : null\n    var m4 =\n      !(item.plate_id != 0) && (!item.status || item.status == 0)\n        ? _vm.t(\"color1\")\n        : null\n    var m5 =\n      !(item.plate_id != 0) && !(!item.status || item.status == 0)\n        ? _vm.t(\"color2\")\n        : null\n    return {\n      $orig: $orig,\n      m2: m2,\n      m3: m3,\n      m4: m4,\n      m5: m5,\n    }\n  })\n  var l3 = _vm.clistshow\n    ? _vm.__map(_vm.clist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m6 = _vm.inArray(item.id, _vm.cids)\n        var m7 = m6 ? _vm.t(\"color1\") : null\n        var g2 = item.child.length\n        var l2 = _vm.__map(item.child, function (item2, index2) {\n          var $orig = _vm.__get_orig(item2)\n          var m8 = _vm.inArray(item2.id, _vm.cids)\n          var m9 = m8 ? _vm.t(\"color1\") : null\n          var g3 = item2.child.length\n          var l1 = _vm.__map(item2.child, function (item3, index3) {\n            var $orig = _vm.__get_orig(item3)\n            var m10 = _vm.inArray(item3.id, _vm.cids)\n            var m11 = m10 ? _vm.t(\"color1\") : null\n            return {\n              $orig: $orig,\n              m10: m10,\n              m11: m11,\n            }\n          })\n          return {\n            $orig: $orig,\n            m8: m8,\n            m9: m9,\n            g3: g3,\n            l1: l1,\n          }\n        })\n        return {\n          $orig: $orig,\n          m6: m6,\n          m7: m7,\n          g2: g2,\n          l2: l2,\n        }\n      })\n    : null\n  var m12 = _vm.clistshow ? _vm.t(\"color1\") : null\n  var m13 = _vm.clistshow ? _vm.t(\"color1\") : null\n  var m14 = _vm.clistshow ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        g1: g1,\n        m1: m1,\n        l0: l0,\n        l3: l3,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<dd-tab :itemdata=\"['全部('+countall+')','已上架('+count1+')','未上架('+count0+')']\" :itemst=\"['all','1','0']\" :st=\"st\" :isfixed=\"true\" @changetab=\"changetab\"></dd-tab>\n\t<view style=\"width:100%;height:100rpx\"></view>\n\t<!-- #ifndef H5 || APP-PLUS -->\n\t<view class=\"topsearch flex-y-center\">\n\t\t<view class=\"f1 flex-y-center\">\n\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\" ></image>\n\t\t\t<input :value=\"keyword\" placeholder=\"输入关键字搜索\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\"></input>\n\t\t</view>\n\t\t<view class=\"screen-view flex flex-y-center\" @click=\"changeClistDialog\">\n\t\t\t<view class=\"screen-view-text\" :style=\"{color: cids.length > 0 ?  t('color1'):''}\">分类筛选</view>\n\t\t\t<text class=\"iconfont iconshaixuan\" :style=\"{color: cids.length > 0 ?  t('color1'):''}\"></text>\n\t\t</view>\n\t</view>\n\t<!--  #endif -->\n\t<view class=\"order-content\">\n\t<block v-for=\"(item, index) in datalist\" :key=\"index\">\n\t\t<view class=\"order-box\">\n\t\t\t<view class=\"content\" style=\"border-bottom:none\">\n\t\t\t\t<view @tap=\"goto\" :data-url=\"'/pages/shop/product?id=' + item.id\">\n\t\t\t\t\t<image :src=\"item.pic\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t<view class=\"t1\">{{item.name}}\n\t\t\t\t\t\t<view  class=\"stockwarning\"  v-if=\"item.isstock_warning==1\">\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/workorder/ts.png'\" style=\"width:30rpx;height:30rpx\" ></image> 库存不足\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"t2\">剩余：{{item.stock}} <text style=\"color:#a88;padding-left:20rpx\">已售：{{item.sales}}</text></view>\n\t\t\t\t\t<view class=\"t3\"><text class=\"x1\">￥{{item.sell_price}}</text><text class=\"x2\" v-if=\"item.market_price != null\">￥{{item.market_price}}</text></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"op\">\n\t\t\t\t<text style=\"color:red\" class=\"flex1\" v-if=\"!item.status || item.status==0\">未上架</text>\n\t\t\t\t<text style=\"color:green\" class=\"flex1\" v-else>已上架</text>\n\t\t\t\t<text style=\"color:orange\" class=\"flex1\" v-if=\"!item.ischecked\">待审核</text>\n\t\t\t\t<block v-if=\"item.plate_id!=0\">\n\t\t\t\t\t<view v-if=\"manage_set && manage_set.status_product==1\">\n\t\t\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color1')}\" @tap=\"setst\" data-st=\"1\" :data-id=\"item.id\" v-if=\"!item.status || item.status==0\">上架</view>\n\t\t\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color2')}\" @tap=\"setst\" data-st=\"0\" :data-id=\"item.id\" v-else>下架</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"manage_set && manage_set.stock_product==1\">\n\t\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'editstock?id='+item.id\" class=\"btn2\">修改价格</view>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t\t<block v-else>\n\t\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color1')}\" @tap=\"setst\" data-st=\"1\" :data-id=\"item.id\" v-if=\"!item.status || item.status==0\">上架</view>\n\t\t\t\t\t<view class=\"btn1\" :style=\"{background:t('color2')}\" @tap=\"setst\" data-st=\"0\" :data-id=\"item.id\" v-else>下架</view>\n\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'edit?id='+item.id + '&cids=' + cids\" class=\"btn2\">编辑</view>\n\t\t\t\t\t<view class=\"btn2\" @tap=\"todel\" :data-id=\"item.id\">删除</view>\n\t\t\t\t</block>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t</view>\n\t<view class=\"popup__container\" v-if=\"clistshow\">\n\t\t<view class=\"popup__overlay\" @tap.stop=\"changeClistDialog\"></view>\n\t\t<view class=\"popup__modal\" style=\"max-height: 1400rpx;\">\n\t\t\t<view class=\"popup__title\">\n\t\t\t\t<text class=\"popup__title-text\">请选择分类</text>\n\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"changeClistDialog\"/>\n\t\t\t</view>\n\t\t\t<view class=\"popup__content\">\n\t\t\t\t<block v-for=\"(item, index) in clist\" :key=\"item.id\">\n\t\t\t\t\t<view class=\"clist-item\" @tap=\"cidsChange\" :data-id=\"item.id\">\n\t\t\t\t\t\t<view class=\"flex1\">{{item.name}}</view>\n\t\t\t\t\t\t<view class=\"radio\" :style=\"inArray(item.id,cids) ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<block v-for=\"(item2, index2) in item.child\" :key=\"item2.id\">\n\t\t\t\t\t\t<view class=\"clist-item\" style=\"padding-left:80rpx\" @tap=\"cidsChange\" :data-id=\"item2.id\">\n\t\t\t\t\t\t\t<view class=\"flex1\" v-if=\"item.child.length-1==index2\">└ {{item2.name}}</view>\n\t\t\t\t\t\t\t<view class=\"flex1\" v-else>├ {{item2.name}}</view>\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"inArray(item2.id,cids) ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<block v-for=\"(item3, index3) in item2.child\" :key=\"item3.id\">\n\t\t\t\t\t\t<view class=\"clist-item\" style=\"padding-left:160rpx\" @tap=\"cidsChange\" :data-id=\"item3.id\">\n\t\t\t\t\t\t\t<view class=\"flex1\" v-if=\"item2.child.length-1==index3\">└ {{item3.name}}</view>\n\t\t\t\t\t\t\t<view class=\"flex1\" v-else>├ {{item3.name}}</view>\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"inArray(item3.id,cids) ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</block>\n\t\t\t\t</block>\n\t\t\t</view>\n\t\t\t<view class=\"popup_but_view flex\">\n\t\t\t\t<view class=\"popup_but\" :style=\"{borderColor:t('color1'),color:t('color1')}\" @tap.stop=\"changeClistDialogReset\">重置</view>\n\t\t\t\t<view class=\"popup_but\" :style=\"{background:t('color1')}\" @tap.stop=\"changeClistDialogsearch\">确定</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n\t<loading v-if=\"loading\"></loading>\n\t<nomore v-if=\"nomore\"></nomore>\n\t<nodata v-if=\"nodata\"></nodata>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n      st: 'all',\n      datalist: [],\n      pagenum: 1,\n      nodata: false,\n      nomore: false,\n\t\t\tloading:false,\n      count0: 0,\n      count1: 0,\n      countall: 0,\n      sclist: \"\",\n\t\t\tkeyword: '',\n\t\t\tpre_url:app.globalData.pre_url,\n\t\t\tmanage_set:[],\n\t\t\tclistshow:false,\n\t\t\tclist:[],\n\t\t\tcids:[],\n\t\t\tbid:0,\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tif(opt.cids) this.cids = opt.cids.split(\",\");\n\t\tthis.getdata();\n\t\tthis.getclist();\n\t\t// 判断H5|App 右上角角标展示\n\t\tthis.pageMarker();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata(true);\n    }\n  },\n\tonNavigationBarSearchInputConfirmed:function(e){\n\t\tthis.searchConfirm({detail:{value:e.text}});\n\t},\n\tonNavigationBarButtonTap(e) {\n\t\tif(e.index == 1){\n\t\t\tthis.changeClistDialog()\n\t\t}\n\t},\n  methods: {\n\t\tpageMarker(){\n\t\t\t// #ifdef H5\n\t\t\tif(this.cids.length > 0){\n\t\t\t\tdocument.querySelectorAll('.uni-page-head .uni-page-head-ft .uni-page-head-btn')[1].querySelector('.uni-btn-icon').style.color = this.t('color1');\n\t\t\t}else{\n\t\t\t\tdocument.querySelectorAll('.uni-page-head .uni-page-head-ft .uni-page-head-btn')[1].querySelector('.uni-btn-icon').style.color = 'rgb(0,0,0)';\n\t\t\t}\n\t\t\t// #endif\n\t\t},\n\t\tcidsChange:function(e){\n\t\t\tvar clist = this.clist;\n\t\t\tvar cids = this.cids;\n\t\t\tvar cid = e.currentTarget.dataset.id;\n\t\t\tvar newcids = [];\n\t\t\tvar ischecked = false;\n\t\t\tfor(var i in cids){\n\t\t\t\tif(cids[i] != cid){\n\t\t\t\t\tnewcids.push(cids[i]);\n\t\t\t\t}else{\n\t\t\t\t\tischecked = true;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(ischecked==false){\n\t\t\t\tnewcids.push(cid);\n\t\t\t}\n\t\t\tthis.cids = newcids;\n\t\t\tthis.pageMarker();\n\t\t},\n\t\t// 获取分类列表\n\t\tgetclist:function(){\n\t\t\tvar that = this;\n\t\t\tvar id = that.opt.id ? that.opt.id : '';\n\t\t\tapp.get('ApiAdminProduct/edit',{id:id}, function (res) {\n\t\t\t\tif(res.bid > 0){\n\t\t\t\t\tthat.clist = res.clist2;\n\t\t\t\t}else{\n\t\t\t\t\tthat.clist = res.clist;\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tchangeClistDialog:function(){\n\t\t\tthis.clistshow = !this.clistshow;\n\t\t},\n\t\tchangeClistDialogsearch(){\n\t\t\tthis.clistshow = !this.clistshow;\n\t\t\tthis.getdata();\n\t\t},\n\t\tchangeClistDialogReset(){\n\t\t\tthis.clistshow = !this.clistshow;\n\t\t\tthis.cids = [];\n\t\t\tthis.pageMarker();\n\t\t\tthis.getdata();\n\t\t},\n    changetab: function (st) {\n      var that = this;\n      that.st = st;\n      that.getdata();\n    },\n    getdata: function (loadmore) {\n     if(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var pagenum = that.pagenum;\n      var st = that.st;\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n\t\t\tthat.loading = true;\n      app.post('ApiAdminProduct/index', {keyword:that.keyword,pagenum: pagenum,st: that.st,cids:that.cids}, function (res) {\n        that.loading = false;\n        var data = res.datalist;\n        if (pagenum == 1){\n\t\t\t\t\tthat.countall = res.countall;\n\t\t\t\t\tthat.count0 = res.count0;\n\t\t\t\t\tthat.count1 = res.count1;\n\t\t\t\t\tthat.datalist = data;\n\t\t\t\t\tthat.manage_set = res.manage_set;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n\t\t\t\t\tthat.loaded();\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n    todel: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      app.confirm('确定要删除该商品吗?', function () {\n        app.post('ApiAdminProduct/del', {id: id}, function (res) {\n          if (res.status == 1) {\n            app.success(res.msg);\n            that.getdata();\n          } else {\n            app.error(res.msg);\n          }\n        });\n      });\n    },\n    setst: function (e) {\n      var that = this;\n      var id = e.currentTarget.dataset.id;\n      var st = e.currentTarget.dataset.st;\n      app.confirm('确定要' + (st == 0 ? '下架' : '上架') + '吗?', function () {\n        app.post('ApiAdminProduct/setst', {st: st,id: id}, function (res) {\n          if (res.status == 1) {\n            app.success(res.msg);\n            that.getdata();\n          } else {\n            app.error(res.msg);\n          }\n        });\n      });\n    },\n\t\tsearchConfirm:function(e){\n\t\t\tthis.keyword = e.detail.value;\n      this.getdata(false);\n\t\t}\n  }\n};\n</script>\n<style>\n.container{ width:100%;}\n.topsearch{width:94%;margin:10rpx 3%;}\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\n.topsearch .screen-view{font-size: 24rpx;color: #999;margin-left: 10rpx;}\n.topsearch .screen-view-text{width: 120rpx;text-align: right;}\n.topsearch .screen-view text{font-size: 23rpx;font-weight: bold;margin-left: 8rpx;margin-top: 3rpx;}\n.topsearch .screen-view image{width: 22rpx;height: 22rpx;margin-left: 10rpx;transform: rotate(180deg);}\n.order-content{display:flex;flex-direction:column}\n.order-box{ width: 94%;margin:10rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}\n\n.order-box .content{display:flex;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;position:relative}\n.order-box .content:last-child{ border-bottom: 0; }\n.order-box .content image{ width: 140rpx; height: 140rpx;}\n.order-box .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\n.order-box .content .detail .t1{font-size:26rpx;min-height:50rpx;line-height:36rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.order-box .content .detail .t2{height:36rpx;line-height:36rpx;color: #999;overflow: hidden;font-size: 24rpx;}\n.order-box .content .detail .t3{display:flex;height: 36rpx;line-height: 36rpx;color: #ff4246;}\n.order-box .content .detail .x1{ font-size:30rpx;margin-right:5px}\n.order-box .content .detail .x2{ font-size:24rpx;text-decoration:line-through;color:#999}\n\n.order-box .bottom{ width:100%; padding:10rpx 0px; border-top: 1px #e5e5e5 solid; color: #555;}\n.order-box .op{ display:flex;align-items:center;width:100%; padding:10rpx 0px; border-top: 1px #e5e5e5 solid; color: #555;}\n.btn1{margin-left:20rpx;width:120rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}\n.btn2{margin-left:20rpx;width:120rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n\n.stockwarning{ font-size:24rpx;color:red;  display:flex;  align-items:center}\n.content .stockwarning image{ width:30rpx; height:30rpx; margin-right:10rpx}\n\n.clist-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\n.radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\n.radio .radio-img{width:100%;height:100%;display:block}\n.popup_but_view{width: 92%;margin: 0 auto;align-items: center;justify-content: space-between;}\n.popup_but{width: 48%;padding: 16rpx 0rpx;border-radius: 60rpx;text-align: center;font-size: 28rpx;color: #FFFFFF;border: 1px solid;box-sizing: border-box;}\n/*  */\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213700550\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}