{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/cycleplandetail.vue?95ae", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/cycleplandetail.vue?90ab", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/cycleplandetail.vue?a1b4", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/cycleplandetail.vue?113f", "uni-app:///admin/order/cycleplandetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/cycleplandetail.vue?80b7", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/cycleplandetail.vue?c031", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/cycleplandetail.vue?6808", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/cycleplandetail.vue?53a5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "detail", "order", "storeinfo", "pre_url", "advance_days", "optionList", "title", "text", "value", "listIndex", "alertStatus", "onLoad", "methods", "showhxqr", "closeHxqr", "orderCollect", "app", "id", "setTimeout", "that", "<PERSON><PERSON><PERSON><PERSON>", "logistics", "getdata", "alertClick", "itemClick", "advanceDays", "days"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACa;AACwB;;;AAGnG;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA60B,CAAgB,6yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoJj2B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,aACA;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MAEAC;QACAA;QACAA;UAAAC;QAAA;UACAD;UACAA;UACAE;YACAC;UACA;QACA;MACA;IACA;IACAC;MACA;MAEAJ;QACAA;QACAA;UAAAC;QAAA;UACAD;UACAA;UACAE;YACAC;UACA;QACA;MACA;IACA;IACAE;MACA;MACA;MACAL;IACA;IACAM;MACA;MACAN;MACAA;QAAAC;MAAA;QACAE;QACAA;QACAA;QACAH;QACAG;MACA;IACA;IACAI;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACAT;MACAA;QAAAC;QAAAS;MAAA;QACAV;QACAG;QACAA;QACAH;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACjQA;AAAA;AAAA;AAAA;AAA0rC,CAAgB,0mCAAG,EAAC,C;;;;;;;;;;;ACA9sC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAktC,CAAgB,koCAAG,EAAC,C;;;;;;;;;;;ACAtuC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/order/cycleplandetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/order/cycleplandetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./cycleplandetail.vue?vue&type=template&id=11dbe9fe&scoped=true&\"\nvar renderjs\nimport script from \"./cycleplandetail.vue?vue&type=script&lang=js&\"\nexport * from \"./cycleplandetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cycleplandetail.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./cycleplandetail.vue?vue&type=style&index=1&id=11dbe9fe&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"11dbe9fe\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/order/cycleplandetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cycleplandetail.vue?vue&type=template&id=11dbe9fe&scoped=true&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cycleplandetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cycleplandetail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\" v-if=\"isload\">\r\n\t\t<view class=\"module\">\r\n\t\t\t<view class=\"module_title\">\r\n\t\t\t\t<view>第{{detail.cycle_number}}期 <text class=\"module_num\">{{detail.cycle_number}}/{{detail.order.qsnum}}</text></view>\r\n\t\t\t\t\r\n\t\t\t\t<view v-if=\"(order.freight_type == 0 ||order.freight_type == 2 ) && detail.status == 2\" class=\"btn2\" @tap=\"orderCollect\"  :data-id=\"detail.id\">确认收货</view>\r\n\t\t\t\t<view  v-if=\"order.freight_type == 1  && detail.status == 1\" class=\"btn_c\" @tap=\"orderhexiao\"  :data-id=\"detail.id\">核销</view>\r\n\t\t\t\t\r\n\t\t\t\t<text class=\"module_state\" v-if=\" detail.status == 1 && order.freight_type == 2\">待配送</text>\r\n\t\t\t\t<text class=\"module_state\" v-if=\" detail.status == 1 && order.freight_type == 0\">待发货</text>\r\n\t\t\t\t\r\n\t\t\t\t<text class=\"module_state\" v-if=\"order.freight_type == 2 && detail.status == 2\">配送中</text>\r\n\t\t\t\t<text class=\"module_state\" v-if=\"order.freight_type == 1 && detail.status == 1\">待取货</text>\r\n\t\t\t\t<text class=\"module_state\" v-if=\"order.freight_type == 0 && detail.status == 2\">已发货</text>\r\n\t\t\t\t<text class=\"module_state\" v-if=\" detail.status == 3\">已完成</text>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t<view v-if=\"order.freight_type == 2 || order.freight_type == 0\">\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"module_list\">\r\n\t\t\t\t\t<view class=\"module_lable\">收货人</view>\r\n\t\t\t\t\t<view class=\"module_text\">\r\n\t\t\t\t\t\t{{order.linkman}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"module_list\">\r\n\t\t\t\t\t<view class=\"module_lable\">\r\n\t\t\t\t\t\t<text v-if=\"order.freight_type == 1\">取货</text>\r\n\t\t\t\t\t\t<text v-else>收货</text>  \r\n\t\t\t\t\t\t日期\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"module_text\">\r\n\t\t\t\t\t\t{{detail.cycle_date}} / {{detail.week}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view  class=\"module_update\">\r\n\t\t\t\t\t\t<img  :src=\"pre_url+'/static/img/week/week_write.png'\" alt=\"\"/>修改\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"module_list\">\r\n\t\t\t\t\t<view class=\"module_lable\">收货地址</view>\r\n\t\t\t\t\t<view class=\"module_text\">\r\n\t\t\t\t\t\t{{order.area}}{{order.address}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"module_list\" v-if=\"order.freight_type == 0 && detail.status > 1\">\r\n\t\t\t\t\t<view class=\"module_lable\">物流信息</view>\r\n\t\t\t\t\t<view class=\"module_text\">\r\n\t\t\t\t\t\t<view class=\"wl_btn\" @tap.stop=\"logistics\" :data-express_com=\"detail.express_com\" :data-express_no=\"detail.express_no\" >\r\n\t\t\t\t\t\t\t查看物流\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"module_list\" v-if=\"order.freight_type == 2 && (detail.status == 2 || detail.status == 3)\">\r\n\t\t\t\t\t<view class=\"module_lable\">配送信息</view>\r\n\t\t\t\t\t<view class=\"module_text\">\r\n\t\t\t\t\t\t<view class=\"wl_btn\" @tap.stop=\"logistics\" :data-express_com=\"detail.express_com\" :data-express_no=\"detail.express_no\" >\r\n\t\t\t\t\t\t\t查看配送信息\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-else-if=\"order.freight_type == 1\">\r\n\t\t\t\t<view class=\"module_list\">\r\n\t\t\t\t\t<view class=\"module_lable\">取货人</view>\r\n\t\t\t\t\t<view class=\"module_text\">\r\n\t\t\t\t\t\t{{order.linkman}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"module_list\">\r\n\t\t\t\t\t<view class=\"module_lable\">\r\n\t\t\t\t\t\t<text v-if=\"order.freight_type == 1\">取货</text>\r\n\t\t\t\t\t\t<text v-else>收货</text>  \r\n\t\t\t\t\t\t日期\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"module_text\">\r\n\t\t\t\t\t\t{{detail.cycle_date}} / {{detail.week}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view  class=\"module_update\">\r\n\t\t\t\t\t\t<img  :src=\"pre_url+'/static/img/week/week_write.png'\" alt=\"\"/>修改\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"module_list\">\r\n\t\t\t\t\t<view class=\"module_lable\">取货地址</view>\r\n\t\t\t\t\t<view class=\"module_text\">\r\n\t\t\t\t\t\t{{storeinfo.name}} - {{storeinfo.address}}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"module_list\" v-if=\"order.freight_type == 1 \">\r\n\t\t\t\t\t<view class=\"module_lable\">核销码</view>\r\n\t\t\t\t\t<view class=\"module_text\">\r\n\t\t\t\t\t\t<view class=\"wl_btn\" @click=\"showhxqr\" >\r\n\t\t\t\t\t\t\t查看核销码\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<view class=\"alert\" v-if=\"alertStatus\">\r\n\t\t\t<view @click=\"alertClick()\" class=\"alert_none\"></view>\r\n\t\t\t<view class=\"alert_module\">\r\n\t\t\t\t<view class=\"alert_title\">\r\n\t\t\t\t\t修改顺延日期\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"alert_item\" v-for=\"(item,index) in optionList\" :key=\"index\" :class=\"listIndex==index?'alert_active':''\" @click=\"itemClick(index)\">\r\n\t\t\t\t\t<view class=\"alert_tag\"></view>\r\n\t\t\t\t\t<view class=\"alert_data\">\r\n\t\t\t\t\t\t<view class=\"alert_name\">\r\n\t\t\t\t\t\t\t{{item.title}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"alert_text\">\r\n\t\t\t\t\t\t\t{{item.text}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<img v-if=\"listIndex==index\"  :src=\"pre_url+'/static/img/week/week_true.png'\" class=\"alert_icon\" alt=\"\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"alert_opt\">\r\n\t\t\t\t\t<view @click=\"alertClick()\" class=\"alert_btn\">\r\n\t\t\t\t\t\t取消\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"alert_btn\" @click=\"advanceDays\">\r\n\t\t\t\t\t\t确定\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<uni-popup id=\"dialogHxqr\" ref=\"dialogHxqr\" type=\"dialog\">\r\n\t\t\t<view class=\"hxqrbox\">\r\n\t\t\t\t<image :src=\"detail.hexiao_qr\" @tap=\"previewImage\" :data-url=\"detail.hexiao_qr\" class=\"img\"/>\r\n\t\t\t\t<!-- <view class=\"txt\">请出示核销码给核销员进行核销</view> -->\r\n\t\t\t\t<view class=\"close\" @tap=\"closeHxqr\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close2.png'\" style=\"width:100%;height:100%\"/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisload:false,\r\n\t\t\t\tdetail:{},\r\n\t\t\t\torder:{},\r\n\t\t\t\tstoreinfo:{},\r\n\t\t\t\tpre_url: app.globalData.pre_url,\r\n\t\t\t\tadvance_days:'',\r\n\t\t\t\toptionList:[\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle:\"顺延一日\",\r\n\t\t\t\t\t\ttext:\"默认收获日顺延一日\",\r\n\t\t\t\t\t\tvalue:1\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle:\"顺延二日\",\r\n\t\t\t\t\t\ttext:\"默认收获日顺延二日\",\r\n\t\t\t\t\t\tvalue:2\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle:\"顺延三日\",\r\n\t\t\t\t\t\ttext:\"默认收获日顺延三日\",\r\n\t\t\t\t\t\tvalue:3\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tlistIndex:null,\r\n\t\t\t\talertStatus:false\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//查看核销码\r\n\t\t\tshowhxqr:function(){\r\n\t\t\t\tthis.$refs.dialogHxqr.open();\r\n\t\t\t},\r\n\t\t\tcloseHxqr:function(){\r\n\t\t\t\tthis.$refs.dialogHxqr.close();\r\n\t\t\t},\r\n\t\t\torderCollect:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\r\n\t\t\t\tapp.confirm('确定已收到货吗?', function () {\r\n\t\t\t\t\t\t\t\tapp.showLoading();\r\n\t\t\t\t  app.post('ApiAdminOrder/cycleorderStageCollect', {id: that.opt.id}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t    app.success(data.msg);\r\n\t\t\t\t    setTimeout(function () {\r\n\t\t\t\t      that.getdata();\r\n\t\t\t\t    }, 1000);\r\n\t\t\t\t  });\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\torderhexiao:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\r\n\t\t\t\tapp.confirm('确定客户已取货吗?', function () {\r\n\t\t\t\t\t\t\t\tapp.showLoading();\r\n\t\t\t\t  app.post('ApiAdminOrder/cycleorderHexiao', {id: that.opt.id}, function (data) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t    app.success(data.msg);\r\n\t\t\t\t    setTimeout(function () {\r\n\t\t\t\t      that.getdata();\r\n\t\t\t\t    }, 1000);\r\n\t\t\t\t  });\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tlogistics:function(e){\r\n\t\t\t\tvar express_com = e.currentTarget.dataset.express_com\r\n\t\t\t\tvar express_no = e.currentTarget.dataset.express_no\r\n\t\t\t\tapp.goto('/pagesExt/cycle/logistics?express_com=' + express_com + '&express_no=' + express_no);\r\n\t\t\t},\r\n\t\t\tgetdata: function () {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tapp.showLoading();\r\n\t\t\t\tapp.get('ApiCycle/getCycleDetail', {id: that.opt.id}, function (res) {\r\n\t\t\t\t\tthat.detail = res.data;\r\n\t\t\t\t\tthat.order = res.data.order;\r\n\t\t\t\t\tthat.storeinfo = res.data.storeinfo;\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tthat.isload = true;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\talertClick(){\r\n\t\t\t\tif(this.alertStatus){\r\n\t\t\t\t\tthis.alertStatus = false\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.alertStatus = true\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\titemClick(e){\r\n\t\t\t\tthis.listIndex=e;\r\n\t\t\t\tthis.advance_days = this.optionList[this.listIndex].value;\r\n\t\t\t},\r\n\t\t\tadvanceDays(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tapp.showLoading();\r\n\t\t\t\tapp.post('ApiCycle/advanceDays', {id: that.opt.id,days:this.advance_days}, function (res) {\r\n\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\tthat.alertStatus = false\r\n\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\tpage {\r\n\t\tbackground: #F6F6F6;\r\n\t}\r\n</style>\r\n<style scoped>\r\n\t.page {\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t.module {\r\n\t\tbackground: #FFFFFF;\r\n\t\tpadding: 0 30rpx 50rpx 30rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tmargin: 0 auto;\r\n\t\twidth: 690rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.module_title {\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1px solid #f6f6f6;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.module_num {\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin-left: 30rpx;\r\n\t}\r\n\r\n\t.module_state {\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #6FD16B;\r\n\t}\r\n\r\n\t.module_list {\r\n\t\tmargin-top: 30rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.module_lable {\r\n\t\twidth: 120rpx;\r\n\t\ttext-align: right;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.module_text {\r\n\t\tmargin-left: 30rpx;\r\n\t\tcolor: #333;\r\n\t\tflex: 1;\r\n\t}\r\n\t\r\n\t.module_update{\r\n\t\tcolor: #FC4343;\r\n\t}\r\n\t.module_update img{\r\n\t\theight: 24rpx;\r\n\t\twidth: 24rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.opt {\r\n\t\tpadding: 20rpx 30rpx;\r\n\t}\r\n\t.wl_btn {\r\n\t\twidth: 240rpx;\r\n\t\theight: 70rpx;\r\n\t\tbackground: #FD4A46;\r\n\t\tborder-radius: 10rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #FFFFFF;\r\n\t\tbox-sizing: border-box;\r\n\t\t\r\n\t}\r\n\r\n\r\n\t.opt_btn {\r\n\t\theight: 88rpx;\r\n\t\tbackground: #FD4A46;\r\n\t\tborder-radius: 10rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #FFFFFF;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\t\r\n\t.alert{\r\n\t\tposition: fixed;\r\n\t\theight: 100%;\r\n\t\twidth: 100%;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbackground: rgba(0, 0, 0, 0.6);\r\n\t}\r\n\t.alert_none{\r\n\t\tposition: absolute;\r\n\t\theight: 100%;\r\n\t\twidth: 100%;\r\n\t}\r\n\t.alert_module{\r\n\t\tposition: relative;\r\n\t\twidth: 560rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder-radius: 24rpx;\r\n\t}\r\n\t.alert_title{\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #323232;\r\n\t\ttext-align: center;\r\n\t\tpadding: 0 0 20rpx 0;\r\n\t}\r\n\t.alert_close{\r\n\t\tposition: absolute;\r\n\t\tright: 35rpx;\r\n\t\ttop: 35rpx;\r\n\t\theight: 28rpx;\r\n\t\twidth: 28rpx;\r\n\t}\r\n\t.alert_item{\r\n\t\theight: 120rpx;\r\n\t\tbackground: #F9F6F6;\r\n\t\tborder-radius: 8rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t\tpadding: 0 30rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\t.alert_tag{\r\n\t\twidth: 8rpx;\r\n\t\theight: 8rpx;\r\n\t\tbackground: #323232;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\t.alert_data{\r\n\t\tflex: 1;\r\n\t\tmargin-left: 25rpx;\r\n\t}\r\n\t.alert_name{\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #323232;\r\n\t}\r\n\t.alert_text{\r\n\t\tfont-size: 22rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #999999;\r\n\t\tmargin-top: 15rpx;\r\n\t}\r\n\t.alert_icon{\r\n\t\theight: 36rpx;\r\n\t\twidth: 36rpx;\r\n\t}\r\n\t.alert_active{\r\n\t\tbackground: #ffe8e8;\r\n\t}\r\n\t.alert_active .alert_tag{\r\n\t\tbackground: #FC4343;\r\n\t}\r\n\t.alert_active .alert_name{\r\n\t\tcolor: #FC4343;\r\n\t}\r\n\t.alert_active .alert_text{\r\n\t\tcolor: #FC4343;\r\n\t\topacity: 0.7;\r\n\t}\r\n\t.alert_opt{\r\n\t\tmargin-top: 60rpx;\r\n\t\tdisplay: flex;\r\n\t}\r\n\t.alert_btn{\r\n\t\tflex: 1;\r\n\t\theight: 88rpx;\r\n\t\tbackground: #ffe8e8;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #FC4343;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 26rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\t.alert_btn:last-child{\r\n\t\tflex: 2;\r\n\t\tmargin-left: 20rpx;\r\n\t\theight: 88rpx;\r\n\t\tbackground: #FC4343;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 26rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\t.btn2{ border-radius: 10rpx;margin-left:20rpx;margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;background:#FC4343;border-radius:3px;text-align:center}\r\n\t.btn_c{font-size: 14px; border-radius: 10rpx;margin-left:20rpx;margin-top: 10rpx;width:120rpx;height:40rpx;line-height:40rpx;color:#fff;background:#FC4343;border-radius:3px;text-align:center}\r\n\r\n\t.hxqrbox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}\r\n\t.hxqrbox .img{width:400rpx;height:400rpx}\r\n\t.hxqrbox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}\r\n\t.hxqrbox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}\r\n\t\r\n</style>\r\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cycleplandetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cycleplandetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213045680\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cycleplandetail.vue?vue&type=style&index=1&id=11dbe9fe&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cycleplandetail.vue?vue&type=style&index=1&id=11dbe9fe&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213045198\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}