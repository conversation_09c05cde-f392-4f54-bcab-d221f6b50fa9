{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/dkorder.vue?fade", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/dkorder.vue?b565", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/dkorder.vue?337c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/dkorder.vue?1c29", "uni-app:///admin/order/dkorder.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/dkorder.vue?da70", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/dkorder.vue?67f5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "mid", "pre_url", "merberInfo", "realname", "tel", "headimg", "id", "linkman", "prodata", "freightList", "freightkey", "address", "freight", "pstype", "goodsprice", "totalprice", "totalpricefocus", "payTypeArr", "payTypeIndex", "paytype", "dialogShow", "onSharelink", "navigationMenu", "platform", "statusBarHeight", "userAddress", "name", "regiondata", "items", "orderNotes", "buydata", "storeshowall", "freight_id", "storeid", "isshowglass", "glassrecordlist", "grid", "hasglassproduct", "curindex", "onLoad", "app", "url", "uni", "method", "header", "success", "that", "onShow", "computed", "priceCount", "onShareAppMessage", "title", "pic", "link", "methods", "doStoreShowAll", "choosestore", "changeFreight", "focusInput", "telInput", "linkmanInput", "addressInput", "regionchange", "goBack", "wxNavigationBarMenu", "getMemberInfo", "pagenum", "memberdata", "clearShopCartFn", "content", "cartid", "shareBut", "showdialog", "bindPickerChange", "getpaytype", "totalpricenblur", "inputPrice", "getdatacart", "selectAddres", "changeAddress", "addshop", "topay", "prodataIdStr", "prodataIdArr", "province", "city", "district", "freightprice", "paycheck", "remark", "showglass", "getglassrecord", "listrow", "hideglass", "chooseglass"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5HA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkSz1B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAH;MACAI;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAKAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACArB;QACAsB;QACAf;QACAgB;MACA;MACAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;MACA;QACAC;MACA;MACAC;QACAD;QACA1C;QACA4C;QACAC;UAAA;QAAA;QACAC;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA;IACAL;MACAI;MACAA;IACA;IACA;MACA;IACA;IACA;EACA;EACAE;IACAC;MACA;QAAA;MAAA;MACA;MACA;QAAA;MAAA;MACA;IACA;EACA;EACAC;IACA;MAAAC;MAAAC;MAAAC;IAAA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACAX;MACAA;MACAA;MACAA;MACA;QACAA;MACA;IACA;IACAY;MACA;MACAZ;QACAA;MACA;IACA;IACAa;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACAvB;IACA;IACAwB;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACAnB;MACAN;QAAAlC;QAAA4D;MAAA;QACApB;QACA;QACA;UAAAqB;QAAA;QAAA;QACArB;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAsB;MACA;MACA1B;QACAS;QACAkB;QACAxB;UACA;YACAL;cAAAxC;cAAAsE;YAAA;cACAxB;YACA;UACA,wBACA;QACA;MACA;IACA;IACAyB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACAlC;QACA;UACAM;UACAA;QACA;MACA;IACA;IACA6B;MAEA;IAEA;IACAC;MACA;IACA;IACAC;MACA;MACA/B;MACAN;QAAAxC;MAAA;QACA8C;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAgC;MACA;MACAtC;IACA;IACAuC;MACA;MACA;QAAA;QACA;UACAjC;QACA;UACAA;QACA;MACA;QAAA;QACAA;MACA;MACAA;QACAA;MACA;MACAA;MACAA;MACAA;IACA;IACAkC;MACA;MACAxC;IACA;IACA;IACAyC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;UACAC;QACA;QACAC;MACA;MACA3C;MACAA;QACAxC;QACAO;QACAH;QACAO;QACAyE;QACAC;QACAC;QACA;QACAC;QACAC;QACAzE;QACAD;QACAN;QACAW;QACAsE;QACAxD;QACAD;MACA;QACAQ;QACA;UACA;UACAA;UACA;QACA;UAEAA;UACAM;UACAA;UACAA;QAkCA;MACA;IACA;IACA4C;MACA;MACA;MACA;MAEA;QACA;QACA5C;MACA;QACAA;MACA;MAEAA;MACAA;IACA;IACA6C;MACA;MACA;QACA7C;QACAN;UAAA0B;UAAA0B;QAAA;UACA9C;UACA;UACAA;UACAA;QACA;MACA;IACA;IACA+C;MACA;MACA/C;IACA;IACAgD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAhD;MACAA;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtrBA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/order/dkorder.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/order/dkorder.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./dkorder.vue?vue&type=template&id=789cad88&\"\nvar renderjs\nimport script from \"./dkorder.vue?vue&type=script&lang=js&\"\nexport * from \"./dkorder.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dkorder.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/order/dkorder.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dkorder.vue?vue&type=template&id=789cad88&\"", "var components\ntry {\n  components = {\n    uniDataPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-data-picker/uni-data-picker\" */ \"@/components/uni-data-picker/uni-data-picker.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.t(\"color1\")\n  var m1 = _vm.t(\"color1rgb\")\n  var m2 = _vm.t(\"color1\")\n  var m3 = _vm.t(\"color1rgb\")\n  var l0 = _vm.__map(_vm.prodata, function (item, index2) {\n    var $orig = _vm.__get_orig(item)\n    var m4 = item.product.product_type == 1 ? _vm.t(\"color1rgb\") : null\n    return {\n      $orig: $orig,\n      m4: m4,\n    }\n  })\n  var g0 = _vm.freightList.length\n  var l1 = g0\n    ? _vm.__map(_vm.freightList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m5 = _vm.freightkey == index ? _vm.t(\"color1\") : null\n        var m6 = _vm.freightkey == index ? _vm.t(\"color1rgb\") : null\n        return {\n          $orig: $orig,\n          m5: m5,\n          m6: m6,\n        }\n      })\n    : null\n  var g1 = g0\n    ? _vm.buydata.storedata &&\n      _vm.buydata.storedata.length &&\n      _vm.buydata.pstype == 1\n    : null\n  var g2 = g0 && _vm.buydata.pstype == 5 ? _vm.buydata.storedata.length : null\n  var l2 =\n    g0 && _vm.buydata.pstype == 5\n      ? _vm.__map(_vm.buydata.storedata, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m7 =\n            (_vm.storeshowall ? index < 5 : true) &&\n            _vm.buydata.storekey == index\n              ? _vm.t(\"color1\")\n              : null\n          return {\n            $orig: $orig,\n            m7: m7,\n          }\n        })\n      : null\n  var g3 =\n    g0 && _vm.buydata.pstype == 5\n      ? _vm.buydata.storedata.length > 5 && _vm.storeshowall\n      : null\n  var m8 = _vm.t(\"color1\")\n  var m9 = _vm.t(\"color1rgb\")\n  var m10 = _vm.t(\"color1\")\n  var m11 = _vm.t(\"color1rgb\")\n  var m12 = _vm.dialogShow ? _vm.t(\"color1\") : null\n  var l3 = _vm.isshowglass\n    ? _vm.__map(_vm.glassrecordlist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m13 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          m13: m13,\n        }\n      })\n    : null\n  var m14 = _vm.isshowglass ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        l0: l0,\n        g0: g0,\n        l1: l1,\n        g1: g1,\n        g2: g2,\n        l2: l2,\n        g3: g3,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        l3: l3,\n        m14: m14,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dkorder.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dkorder.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<!-- #ifndef H5 -->\r\n\t\t<view class=\"navigation\">\r\n\t\t\t<view class='navcontent' :style=\"{marginTop:navigationMenu.top+'px',width:(navigationMenu.right)+'px'}\">\r\n\t\t\t\t<view class=\"header-location-top\" :style=\"{height:navigationMenu.height+'px'}\">\r\n\t\t\t\t\t<view class=\"header-back-but\" @click=\"goBack\">\r\n\t\t\t\t\t\t<image  :src=\"pre_url+'/static/img/admin/goback.png'\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"header-page-title\">代客下单</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t<view class=\"content\">\r\n\t\t<view class=\"itemfirst flex-y-center\">\r\n\t\t\t<view class=\"itemfirst-options flex-y-center\" @tap=\"goto\" data-url=\"../member/index?type=''\">\r\n\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t<view class=\"avat-img-view\"><image :src=\"merberInfo.headimg ? merberInfo.headimg : `${pre_url}/static/img/touxiang.png`\"></image></view>\r\n\t\t\t\t\t<view class=\"user-info\" v-if=\"merberInfo.id\">\r\n\t\t\t\t\t\t<view class=\"un-text\">{{merberInfo.realname ? merberInfo.realname : merberInfo.nickname}}</view>\r\n\t\t\t\t\t\t<view class=\"tel-text\">\r\n\t\t\t\t\t\t\t<text>id : </text>{{merberInfo.id}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"user-info\" v-else>\r\n\t\t\t\t\t\t <text class=\"un-text\">请选择会员</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"jiantou-img flex flex-y-center\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"itemfirst-options flex-y-center\" @tap=\"goto\" data-url=\"/admin/order/addmember?type=0\">\r\n\t\t\t\t<view>添加会员</view>\r\n\t\t\t\t<view class=\"jiantou-img flex flex-y-center\">\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"item flex-col\">\r\n\t\t\t<view class=\"flex-y-center input-view\"> \r\n\t\t\t\t<text class=\"input-title\">联 系 人：</text>\r\n\t\t\t\t<input placeholder=\"请输入联系人的姓名\" v-model=\"linkman\" @input=\"linkmanInput\"\tplaceholder-style=\"color:#626262;font-size:28rpx\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-y-center input-view\">\r\n\t\t\t\t<text class=\"input-title\">联系电话：</text>\r\n\t\t\t\t<input type=\"number\" placeholder=\"请输入联系人的手机号\" v-model=\"tel\" @input=\"telInput\"\tplaceholder-style=\"color:#626262;font-size:28rpx\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-y-center input-view\">\r\n\t\t\t\t<text class=\"input-title\">所在地区：</text>\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<uni-data-picker ref=\"unidatapicker\" :localdata=\"items\" :border=\"false\" :placeholder=\"regiondata || '请选择省市区'\" @change=\"regionchange\"></uni-data-picker>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-y-center input-view address-view\">\r\n\t\t\t\t<text class=\"input-title\">详细地址：</text>\r\n\t\t\t\t<view class=\"address-chose flex-y-center\">\r\n\t\t\t\t\t<textarea placeholder=\"请输入联系人的地址\" v-model=\"address\" @input=\"addressInput\"\tplaceholder-style=\"color:#626262;font-size:28rpx\" style=\"width: 400rpx;height: 100rpx;\" />\r\n\t\t\t\t\t<view class=\"but-class\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @tap=\"selectAddres()\">\r\n\t\t\t\t\t\t选择\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"item\">\r\n\t\t\t<view class=\"title-view flex-y-center\">\r\n\t\t\t\t<view>商品列表</view>\r\n\t\t\t\t<view class=\"but-class\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" @click=\"addshop\">添加商品</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"product\">\r\n\t\t\t\t<view v-for=\"(item, index2) in prodata\" :key=\"index2\">\r\n\t\t\t\t\t<view class=\"item flex\">\r\n\t\t\t\t\t\t<!-- @tap=\"goto\" :data-url=\"'/pages/shop/product?id=' + item.product.id\" -->\r\n\t\t\t\t\t\t<view class=\"img\">\r\n\t\t\t\t\t\t\t<image v-if=\"item.guige.pic\" :src=\"item.guige.pic\"></image>\r\n\t\t\t\t\t\t\t<image v-else :src=\"item.product.pic\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"info flex1\">\r\n\t\t\t\t\t\t\t<view class=\"f1\">{{item.product.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"f2\">规格：{{item.guige.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"modify-price flex-y-center\">\r\n\t\t\t\t\t\t\t\t<view class=\"f2\">修改单价：</view>\r\n\t\t\t\t\t\t\t\t<input type=\"digit\" :value=\"item.guige.sell_price\" class=\"inputPrice\" @input=\"inputPrice($event,index2)\">\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"f3\">\r\n\t\t\t\t\t\t\t\t<block><text style=\"font-weight:bold;\">￥{{item.guige.sell_price}}</text></block>\r\n\t\t\t\t\t\t\t\t<text style=\"padding-left:20rpx\"> × {{item.num}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"del-view flex-y-center\" @tap.stop=\"clearShopCartFn(item.id)\" style=\"color:#999999;font-size:24rpx\"><image :src=\"pre_url+'/static/img/del.png'\" style=\"width:24rpx;height:24rpx;margin-right:6rpx\"/></view>\r\n\t\t\t\t\t</view>\r\n          <view class=\"glassinfo\" v-if=\"item.product.product_type==1\" @tap=\"showglass\" :data-index=\"index2\" :data-grid=\"item.product.has_glassrecord==1?item.product.glassrecord.id:0\" :style=\"'background:rgba('+t('color1rgb')+',0.8);color:#FFF'\">\r\n          \t<view class=\"f1\">\r\n          \t\t视力档案\r\n          \t</view>\r\n          \t<view class=\"f2\">\r\n          \t\t<text>{{item.product.has_glassrecord==1?item.product.glassrecord.name:''}}</text>\r\n          \t\t<image :src=\"pre_url+'/static/img/arrowright.png'\" >\r\n          \t</view>\r\n          </view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"input-view\" v-if=\"freightList.length\"> \r\n\t\t\t\t<view class=\"title-view flex-y-center\">\r\n\t\t\t\t\t<text class=\"input-title\">配送方式：</text> <!-- <input placeholder=\"请输入配送方式\" v-model=\"freight\" @input=\"\"\tplaceholder-style=\"color:#626262;font-size:28rpx\"/> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"freight\">\r\n\t\t\t\t\t<view class=\"freight-ul\">\r\n\t\t\t\t\t\t<view style=\"width:100%;overflow-y:hidden;overflow-x:scroll;white-space: nowrap;\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item, index) in freightList\" :key=\"idx2\">\r\n\t\t\t\t\t\t\t\t<view class=\"freight-li\"\r\n\t\t\t\t\t\t\t\t\t:style=\"freightkey==index ? 'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''\"\r\n\t\t\t\t\t\t\t\t\t@tap=\"changeFreight(item,index)\">{{item.name}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n<!-- \t\t\t\t\t<view class=\"freighttips\"\r\n\t\t\t\t\t\tv-if=\"freightList[freightkey].minpriceset==1 && freightList[freightkey].minprice > 0 && freightList[freightkey].minprice*1 > product_price*1\">\r\n\t\t\t\t\t\t满{{freightList[freightkey].minprice}}元起送，还差{{(freightList[freightkey].minprice - product_price).toFixed(2)}}元\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"freighttips\" v-if=\"freightList[freightkey].isoutjuli==1\">超出配送范围</view>\r\n\t\t\t\t\t<view class=\"freighttips\" v-if=\"freightList[freightkey].desc\">{{freightList[freightkey].desc}}</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"freighttips\" v-if=\"buydata.desc && buydata.pstype == 0\">{{buydata.desc}}</view>\r\n\t\t\t\t<view class=\"freighttips\" v-if=\"buydata.isoutjuli==1\">超出配送范围</view>\r\n\t\t\t\t<view class=\"storeitem\" v-if=\"buydata.storedata && buydata.storedata.length && buydata.pstype == 1\">\r\n\t\t\t\t\t<view class=\"panel\">\r\n\t\t\t\t\t\t<view class=\"f1\">可使用门店</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<block>\t\t\r\n\t\t\t\t\t\t<block v-for=\"(item, idx) in buydata.storedata\" :key=\"idx\">\r\n\t\t\t\t\t\t\t<view class=\"radio-item\" :data-bid=\"buydata.bid\" :data-index=\"idx\" v-if=\"idx<5\">\r\n\t\t\t\t\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t\t\t\t\t<view>{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"item.address\" class=\"flex-y-center\" style=\"text-align:left;font-size:24rpx;color:#aaaaae;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:1;overflow: hidden;\">{{item.address}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<text style=\"color:#f50;\">{{item.juli}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"storeitem\" v-if=\"buydata.pstype==5\">\r\n\t\t\t\t  <view class=\"panel\">\r\n\t\t\t\t    <view class=\"f1\">配送门店</view>\r\n\t\t\t\t    <view class=\"f2\" v-if=\"buydata.storedata.length > 0\">\r\n\t\t\t\t\t\t\t<text class=\"iconfont icondingwei\"></text>{{buydata.storedata[buydata.storekey].name}}\r\n\t\t\t\t    </view>\r\n\t\t\t\t    <view class=\"f2\" v-else>暂无</view>\r\n\t\t\t\t  </view>\r\n\t\t\t\t  <block v-for=\"(item, index) in buydata.storedata\" :key=\"index\">\r\n\t\t\t\t    <view class=\"radio-item\" @tap.stop=\"choosestore(index)\" v-if=\"storeshowall ? index < 5 :true\">\r\n\t\t\t\t      <view class=\"f1\">\r\n\t\t\t\t        <view>{{item.name}}</view>\r\n\t\t\t\t        <view v-if=\"item.address\" style=\"text-align:left;font-size:24rpx;color:#aaaaae;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:1;overflow: hidden;\">{{item.address}}</view>\r\n\t\t\t\t      </view>\r\n\t\t\t\t      <text style=\"color:#f50;\">{{item.juli}}</text>\r\n\t\t\t\t      <view class=\"radio\" :style=\"buydata.storekey == index ? 'background:'+t('color1')+';border:0' : ''\">\r\n\t\t\t\t        <image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\" />\r\n\t\t\t\t      </view>\r\n\t\t\t\t    </view>\r\n\t\t\t\t  </block>\r\n\t\t\t\t  <view class=\"storeviewmore\" @tap=\"doStoreShowAll\" v-if=\"buydata.storedata.length >5 && storeshowall\">- 查看更多 - </view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-y-center input-view\">\r\n\t\t\t\t<text class=\"input-title\">支付方式：</text>\r\n\t\t\t\t<view class='picker-paytype flex1'>\r\n\t\t\t\t\t<picker @change=\"bindPickerChange\" :value=\"payTypeIndex\" :range=\"payTypeArr\" class=\"picker-class\">\r\n\t\t\t\t\t\t<view class=\"uni-input\">{{payTypeArr[payTypeIndex]}}</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t\t<view class=\"jiantou-img flex flex-y-center\">\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/arrowright.png'\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n<!-- \t\t\t<view class=\"flex-y-center input-view\">\r\n\t\t\t\t<text class=\"input-title\">配送费：</text>\r\n\t\t\t\t<input placeholder=\"请输入您的姓名\" v-model=\"freightprice\" :disabled=\"true\" @input=\"\"\tplaceholder-style=\"color:#626262;font-size:28rpx\"/>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"flex-y-center input-view\">\r\n\t\t\t\t<text class=\"input-title\">商品金额：</text>\r\n\t\t\t\t<text class=\"f2\">¥{{priceCount}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-y-center input-view\" style=\"justify-content: space-between;\">\r\n\t\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t\t<text class=\"input-title\">订单总价：</text>\r\n\t\t\t\t\t<input type=\"digit\" placeholder=\"请输入订单总价\" v-model=\"totalprice\" :disabled=\"totalpricefocus\" :focus=\"!totalpricefocus\" @blur='totalpricenblur'\tplaceholder-style=\"color:#626262;font-size:28rpx\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t<view class=\"but-class\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @touchend.prevent=\"focusInput\">\r\n\t\t\t\t\t修改\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-y-center input-view\">\r\n\t\t\t\t<text class=\"input-title\">订单备注：</text>\r\n\t\t\t\t<input placeholder=\"请输入订单备注\" v-model=\"orderNotes\" @input=\"\"\tplaceholder-style=\"color:#626262;font-size:28rpx;\" style=\"flex:1\" />\r\n\t\t\t</view>\r\n<!-- \t\t\t<view class=\"freight\">\r\n\t\t\t\t<view class=\"f1\">配送方式</view>\r\n\t\t\t\t<view class=\"freight-ul\">\r\n\t\t\t\t\t<view style=\"width:100%;overflow-y:hidden;overflow-x:scroll;white-space: nowrap;\">\r\n\t\t\t\t\t\t<block v-for=\"(item, idx2) in freightList\" :key=\"idx2\">\r\n\t\t\t\t\t\t\t<view class=\"freight-li\"\r\n\t\t\t\t\t\t\t\t:style=\"freightkey==idx2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.2)':''\"\r\n\t\t\t\t\t\t\t\t@tap=\"changeFreight\" :data-bid=\"bid\" :data-index=\"idx2\">{{item.name}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t\t<!-- <view>付款时间</view> -->\r\n\t\t\t<!-- <view>总金额</view> -->\r\n\t\t</view>\r\n\t<view style=\"width: 100%; height:182rpx;\"></view>\r\n\t<view class=\"footer flex notabbarbot\">\r\n\t\t<view class=\"text1 flex1\">总计：\r\n\t\t\t<block>\r\n\t\t\t\t\t<text style=\"font-weight:bold;font-size:36rpx\">￥{{totalprice}}</text>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t\t<button class=\"op\" :style=\"{background:'linear-gradient(-90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"  @click=\"topay\">\r\n\t\t\t提交订单</button>\r\n\t</view>\r\n\t<view v-if=\"dialogShow\" class=\"popup__container\">\r\n\t\t<view class=\"popup__overlay\" @tap.stop=\"showdialog\"></view>\r\n\t\t<view class=\"popup__modal\" style=\"min-height: 450rpx;\">\r\n\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t<text class=\"popup__title-text\">提示</text>\r\n\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\"\r\n\t\t\t\t\**********=\"showdialog\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"popup__content invoiceBox\">\r\n\t\t\t\t<form @submit=\"sendCoupon\" @reset=\"formReset\" report-submit=\"true\">\r\n\t\t\t\t\t<view class=\"orderinfo\">\r\n\t\t\t\t\t\t下单成功！快去分享吧\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class=\"ff_btn\" open-type=\"share\" :style=\"{background:t('color1')}\" @click=\"shareBut\">去分享</button>\r\n\t\t\t\t</form>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n  <!-- 眼镜档案 -->\r\n  <view v-if=\"isshowglass\" class=\"popup__container glass_popup\">\r\n  \t<view class=\"popup__overlay\" @tap.stop=\"hideglass\"></view>\r\n  \t<view class=\"popup__modal\" style=\"height: 1100rpx;\">\r\n  \t\t<view class=\"popup__title\">\r\n  \t\t\t<text class=\"popup__title-text\">视力档案</text>\r\n  \t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx;\"\r\n  \t\t\t\**********=\"hideglass\" />\r\n  \t\t</view>\r\n  \t\t<view class=\"popup__content\">\r\n  \t\t\t<radio-group @change=\"chooseglass\">\r\n  \t\t\t<block v-for=\"(item,index) in glassrecordlist\" :key=\"index\">\r\n  \t\t\t<label>\r\n  \t\t\t\t<view class=\"glassitem\" :class=\"grid==item.id?'on':''\">\r\n  \t\t\t\t\t\t<view class=\"fc\">\r\n  \t\t\t\t\t\t\t<view class=\"radio\"><radio :color=\"t('color1')\" :checked=\"grid==item.id?true:false\" :value=\"''+index\" style=\"transform: scale(0.8);\"></radio></view>\r\n  \t\t\t\t\t\t\t<view class=\"gcontent\">\r\n  \t\t\t\t\t\t\t\t<view class=\"grow gtitle\">{{item.name}} {{item.nickname?item.nickname:''}} {{item.check_time?item.check_time:''}} {{item.typetxt}}\r\n  \t\t\t\t\t\t\t\t\t<text v-if=\"item.double_ipd==0\"> {{item.ipd?' PD'+item.ipd:''}}</text>\r\n  \t\t\t\t\t\t\t\t\t<text v-else> PD R{{item.ipd_right}} L{{item.ipd_left}}</text>\r\n  \t\t\t\t\t\t\t\t</view>\r\n  \t\t\t\t\t\t\t\t<view class=\"grow\">\r\n  \t\t\t\t\t\t\t\tR {{item.degress_right}}/{{item.ats_right}}*{{item.ats_zright}}  <text v-if=\"item.type==3\" class=\"pdl10\"> ADD+{{item.add_right?item.add_right:0}}</text>\r\n  \t\t\t\t\t\t\t\t</view>\r\n  \t\t\t\t\t\t\t\t<view class=\"grow\">\r\n  \t\t\t\t\t\t\t\t\t<text>L {{item.degress_left}}/{{item.ats_left}}*{{item.ats_zleft}} </text>  <text v-if=\"item.type==3\" class=\"pdl10\"> ADD+{{item.add_left?item.add_left:0}}</text>\r\n  \t\t\t\t\t\t\t\t</view>\r\n  \t\t\t\t\t\t\t</view>\r\n  \t\t\t\t\t\t\t<view class=\"opt\" @tap=\"goto\" :data-url=\"'/pagesExt/glass/add?id='+item.id\">编辑</view>\r\n  \t\t\t\t\t\t</view>\r\n  \t\t\t\t\t\t<view class=\"gremark\" v-if=\"item.remark\">备注：{{item.remark}}</view>\r\n  \t\t\t\t</view>\r\n  \t\t\t</label>\r\n  \t\t\t</block>\r\n  \t\t\t</radio-group>\r\n  \t\t\t<view class=\"gr-add\"><button class=\"gr-btn\" :style=\"{background:t('color1')}\" @tap=\"goto\" data-url=\"/pagesExt/glass/add\">新增档案</button></view>\r\n  \t\t</view>\r\n  \t</view>\r\n  </view>\r\n  <!-- 眼镜档案 -->\r\n\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tconst app = getApp();\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn{\r\n\t\t\t\tmid:'',\r\n\t\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\tmerberInfo:{\r\n\t\t\t\t\trealname:'',\r\n\t\t\t\t\ttel:'',\r\n\t\t\t\t\theadimg:'',\r\n\t\t\t\t\tid:''\r\n\t\t\t\t},\r\n\t\t\t\tlinkman:'',\r\n\t\t\t\ttel:'',\r\n\t\t\t\tprodata:[],\r\n\t\t\t\tfreightList:[],\r\n\t\t\t\tfreightkey:0,\r\n\t\t\t\taddress:'',\r\n\t\t\t\tfreight:'商家配送',\r\n\t\t\t\tpstype:1,\r\n\t\t\t\tgoodsprice:'',\r\n\t\t\t\ttotalprice:'',\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\ttotalpricefocus:false,\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef H5\r\n\t\t\t\ttotalpricefocus:true,\r\n\t\t\t\t// #endif\r\n\t\t\t\tpayTypeArr: [],\r\n\t\t\t\tpayTypeIndex:0,\r\n\t\t\t\tpaytype:'',\r\n\t\t\t\tdialogShow:false,\r\n\t\t\t\tonSharelink:'',\r\n\t\t\t\tnavigationMenu:{},\r\n\t\t\t\tplatform: app.globalData.platform,\r\n\t\t\t\tstatusBarHeight: 20,\r\n\t\t\t\tuserAddress:{\r\n\t\t\t\t\ttel:'',\r\n\t\t\t\t\tname:'',\r\n\t\t\t\t\taddress:'',\r\n\t\t\t\t\tregiondata:''\r\n\t\t\t\t},\r\n\t\t\t\tregiondata:'',\r\n\t\t\t\titems:[],\r\n\t\t\t\torderNotes:'',\r\n\t\t\t\tbuydata:{},\r\n\t\t\t\tstoreshowall:true,\r\n\t\t\t\tfreight_id:'',\r\n\t\t\t\tstoreid:'',\r\n        isshowglass:false,\r\n        glassrecordlist:[],\r\n\t\tgrid:0,\r\n        hasglassproduct:0,\r\n        curindex:0,\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(opt) {\r\n\t\t\tlet that = this;\r\n\t\t\tthis.mid = opt.mid ? opt.mid  : '';\r\n\t\t\tthis.userAddress = opt.addressData ? JSON.parse(opt.addressData):this.userAddress;\r\n\t\t\tif(opt.addressData){\r\n\t\t\t\tthis.changeAddress(this.userAddress);\r\n\t\t\t}\r\n\t\t\tif(this.mid) {\r\n\t\t\t\tthis.getMemberInfo(this.mid);\r\n\t\t\t}\r\n\t\t\tthis.getpaytype(); //代客下单支付方式\r\n\t\t\tvar sysinfo = uni.getSystemInfoSync();\r\n\t\t\tthis.statusBarHeight = sysinfo.statusBarHeight;\r\n\t\t\tthis.wxNavigationBarMenu();\r\n\t\t\tapp.get('ApiIndex/getCustom',{}, function (customs) {\r\n\t\t\t\tvar url = app.globalData.pre_url+'/static/area.json';\r\n\t\t\t\tif(customs.data.includes('plug_zhiming')) {\r\n\t\t\t\t\turl = app.globalData.pre_url+'/static/area_gaoxin.json';\r\n\t\t\t\t}\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: url,\r\n\t\t\t\t\tdata: {},\r\n\t\t\t\t\tmethod: 'GET',\r\n\t\t\t\t\theader: { 'content-type': 'application/json' },\r\n\t\t\t\t\tsuccess: function(res2) {\r\n\t\t\t\t\t\tthat.items = res2.data\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\t\tonShow(){\r\n\t\t\tlet that = this;\r\n\t\t\tuni.$once('dkPageOn',function(res){\r\n\t\t\t\tthat.userAddress = res;\r\n\t\t\t\tthat.changeAddress(res);\r\n\t\t\t})\r\n      if(this.hasglassproduct==1){\r\n      \tthis.getglassrecord()\r\n      }\r\n\t\t\tif(this.mid) this.getdatacart(this.mid);\r\n\t\t},\r\n\t\tcomputed:{\r\n\t\t\tpriceCount(){\r\n\t\t\t\tthis.goodsprice = this.prodata.reduce((total,current) => total + (current.num * current.guige.sell_price),0);\r\n\t\t\t\tthis.totalprice = (Number(this.goodsprice) + Number(0)).toFixed(2);\r\n\t\t\t\tlet num = this.prodata.reduce((total,current) => total + (current.num * current.guige.sell_price),0);\r\n\t\t\t\treturn num.toFixed(2)\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShareAppMessage:function(){\r\n\t\t\treturn this._sharewx({title:this.prodata[0].name,pic:this.prodata[0].product.pic,link:this.onSharelink});\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tdoStoreShowAll:function(){\r\n\t\t\t\tthis.storeshowall = false;\r\n\t\t\t},\r\n\t\t\tchoosestore: function(e) {\r\n\t\t\t\tthis.buydata.storekey = e;\r\n\t\t\t\tthis.storeid = this.buydata.storedata[this.buydata.storekey].id;\r\n\t\t\t},\r\n\t\t\tchangeFreight(item,index) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet bid = that.mid;\r\n\t\t\t\tthat.freightkey = index;\r\n\t\t\t\tthat.buydata = that.freightList[index];\r\n\t\t\t\tthat.freight_id = that.freightList[index].id;\r\n\t\t\t\tthat.pstype = that.freightList[index].pstype;\r\n\t\t\t\tif(that.buydata.pstype == 5){\r\n\t\t\t\t\tthat.storeid = that.buydata.storedata[0].id;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tfocusInput(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.$nextTick(() => {\r\n\t\t\t\t\tthat.totalpricefocus = false;\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttelInput(event){\r\n\t\t\t\tthis.userAddress['tel'] = event.detail.value;\r\n\t\t\t},\r\n\t\t\tlinkmanInput(event){\r\n\t\t\t\tthis.userAddress['name'] = event.detail.value;\r\n\t\t\t},\r\n\t\t\taddressInput(event){\r\n\t\t\t\tthis.userAddress['address'] = event.detail.value;\r\n\t\t\t},\r\n\t\t\tregionchange(e) {\r\n\t\t\t\tconst value = e.detail.value\r\n\t\t\t\tthis.regiondata = value[0].text + '/' + value[1].text + '/' + value[2].text;\r\n\t\t\t\tthis.userAddress['regiondata'] = this.regiondata;\r\n\t\t\t},\r\n\t\t\tgoBack(){\r\n\t\t\t\tapp.goto('/admin/index/index','reLaunch')\r\n\t\t\t},\r\n\t\t\twxNavigationBarMenu:function(){\r\n\t\t\t\tif(this.platform=='wx'){\r\n\t\t\t\t\t//胶囊菜单信息\r\n\t\t\t\t\tthis.navigationMenu = wx.getMenuButtonBoundingClientRect()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetMemberInfo: function (mid) {\r\n\t\t\t  var that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t  app.post('ApiAdminMember/index', {id: mid,pagenum: '1'}, function (res) {\r\n\t\t\t    that.loading = false;\r\n\t\t\t\t\tlet memberdata = {};\r\n\t\t\t\t\tif(res.datalist){ memberdata = res.datalist[0] };\r\n\t\t\t\t\tthat.merberInfo = memberdata;\r\n\t\t\t\t\tif(!that.linkman){\r\n\t\t\t\t\t\tthat.linkman = that.merberInfo.realname ? that.merberInfo.realname:'';\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(!that.tel){\r\n\t\t\t\t\t\tthat.tel = that.merberInfo.tel ? that.merberInfo.tel:'';\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.mid = that.merberInfo.id;\r\n\t\t\t\t\tthat.userAddress.tel = that.merberInfo.tel ? that.merberInfo.tel:that.tel;\r\n\t\t\t\t\tthat.userAddress.name = that.merberInfo.realname ? that.merberInfo.realname:that.linkman;\r\n\t\t\t\t\tthat.getdatacart(that.mid);\r\n\t\t\t  });\r\n\t\t\t},\r\n\t\t\tclearShopCartFn: function (id) {\r\n\t\t\t  var that = this;\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '确认删除选购的商品吗？',\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tapp.post(\"ApiAdminOrderlr/cartdelete\", {mid:that.mid,cartid:id}, function (res) {\r\n\t\t\t\t\t\t\t  that.getdatacart(that.mid)\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshareBut(){\r\n\t\t\t\tthis.dialogShow = false;\r\n\t\t\t},\r\n\t\t\tshowdialog(){\r\n\t\t\t\tthis.dialogShow = !this.dialogShow;\r\n\t\t\t},\r\n\t\t\tbindPickerChange: function(e) {\r\n\t\t\t  this.payTypeIndex = e.detail.value;\r\n\t\t\t\tthis.paytype = this.payTypeArr[this.payTypeIndex];\r\n\t\t\t},\r\n\t\t\tgetpaytype(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tapp.post('ApiAdminOrderlr/getpaytype',{},function(res){\r\n\t\t\t\t\tif(res.status == 1){\r\n\t\t\t\t\t\tthat.payTypeArr = Object.values(res.datalist);\r\n\t\t\t\t\t\tthat.paytype = that.payTypeArr[0];\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttotalpricenblur(){\r\n\t\t\t\t// #ifndef H5\r\n\t\t\t\tthis.totalpricefocus = true;\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tinputPrice(event,index){\r\n\t\t\t\tthis.prodata[index].guige.sell_price = event.detail.value;\r\n\t\t\t},\r\n\t\t\tgetdatacart(id){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post('ApiAdminOrderlr/cart', {mid:id}, function (res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.prodata = res.cartlist;\r\n\t\t\t\t\tthat.freightList = res.freightList;\r\n\t\t\t\t\tthat.buydata = that.freightList[0];\r\n\t\t\t\t\tthat.freight_id = that.freightList[0].id;\r\n\t\t\t\t\tthat.hasglassproduct = res.hasglassproduct\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tselectAddres(){\r\n\t\t\t\tif(!this.merberInfo.id) return  app.error('请先选择会员');\r\n\t\t\t\tapp.goto('dkaddress?mid=' + this.merberInfo.id)\r\n\t\t\t},\r\n\t\t\tchangeAddress(res){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif(res.type == 1){ //选择地址\r\n\t\t\t\t\tif(res.area){\r\n\t\t\t\t\t\tthat.address = res.area + res.address;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.address = res.address;\r\n\t\t\t\t\t}\r\n\t\t\t\t}else{ //添加地址\r\n\t\t\t\t\tthat.address = res.address;\r\n\t\t\t\t}\r\n\t\t\t\tthat.$nextTick(() => {\r\n\t\t\t\t\tthat.$refs.unidatapicker.inputSelected = [];\r\n\t\t\t\t})\r\n\t\t\t\tthat.regiondata = res.regiondata;\r\n\t\t\t\tthat.linkman = res.name;\r\n\t\t\t\tthat.tel = res.tel;\r\n\t\t\t},\r\n\t\t\taddshop(){\r\n\t\t\t\tif(!this.merberInfo.id) return  app.error('请先选择会员');\r\n\t\t\t\tapp.goto('dkfastbuy?mid=' + this.merberInfo.id + '&addressData=' + JSON.stringify(this.userAddress))\r\n\t\t\t},\r\n\t\t\t//提交代付款订单 \r\n\t\t\ttopay: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar mid = that.merberInfo.id\r\n\t\t\t\tvar linkman = that.linkman;\r\n\t\t\t\tvar tel = that.tel;\r\n\t\t\t\tvar address = that.address;\r\n\t\t\t\tvar freight = that.freight;\r\n\t\t\t\tvar freightprice = 0;\r\n\t\t\t\tvar paycheck = that.paycheck;\r\n\t\t\t\tvar totalprice = that.totalprice;\r\n\t\t\t  var goodsprice = that.goodsprice;\r\n\t\t\t\tvar prodata = that.prodata;\r\n\t\t\t\tvar paytype = that.paytype;\r\n\t\t\t\tvar remark = that.orderNotes;\r\n\t\t\t\tvar storeid = that.storeid;\r\n\t\t\t\tvar freight_id = that.freight_id;\r\n\t\t\t\t\r\n\t\t\t  if (!mid) return app.error('请先选择会员');\r\n\t\t\t  if (!linkman) return app.error('请输入联系人');\r\n\t\t\t\tif (!tel) return app.error('请输入联系电话');\r\n\t\t\t\tif(that.pstype !=1){\r\n\t\t\t\t\tif (!that.regiondata) return app.error('请先选择地区');\r\n\t\t\t\t\tif (!address) return app.error('请输入地址');\r\n\t\t\t\t}\t\t\t\t\r\n\t\t\t\tif(!that.prodata.length) return app.error('请添加商品');\r\n\t\t\t\tvar province = that.regiondata.split('/')[0] || '';\r\n\t\t\t\tvar city = that.regiondata.split('/')[1] || '';\r\n\t\t\t\tvar district = that.regiondata.split('/')[2] || '';\r\n\t\t\t\tvar prodataIdArr = [];\r\n\t\t\t\tfor (var i = 0; i < prodata.length; i++) {\r\n          let prodataIdStr = prodata[i].product.id + ',' + prodata[i].guige.id + ',' + prodata[i].num + ',' + prodata[i].guige.sell_price;\r\n          if(prodata[i].product.glass_record_id && prodata[i].product.glass_record_id > 0){\r\n            prodataIdStr += ',' + prodata[i].product.glass_record_id;\r\n          }\r\n\t\t\t\t  prodataIdArr.push(prodataIdStr);\r\n\t\t\t\t}\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiAdminOrderlr/createOrder', {\r\n\t\t\t\t\tmid: mid,\r\n\t\t\t\t\tlinkman: linkman,\r\n\t\t\t\t\ttel: tel,\r\n\t\t\t\t\taddress: address,\r\n\t\t\t\t\tprovince:province,\r\n\t\t\t\t\tcity:city,\r\n\t\t\t\t\tdistrict:district,\r\n\t\t\t\t\t// freight:freight,\r\n\t\t\t\t\tfreightprice: freightprice,\r\n\t\t\t\t\tpaycheck:'1',\r\n\t\t\t\t\ttotalprice:totalprice,\r\n\t\t\t    goodsprice:that.goodsprice,\r\n\t\t\t    prodata:prodataIdArr.join('-'),\r\n\t\t\t\t\tpaytype:paytype,\r\n\t\t\t\t\tremark:remark,\r\n\t\t\t\t\tstoreid:storeid,\r\n\t\t\t\t\tfreight_id:freight_id,\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tif (res.status == 0) {\r\n\t\t\t\t\t\t//that.showsuccess(res.data.msg);\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t// #ifndef H5\r\n\t\t\t\t\tapp.success('下单成功！');\r\n\t\t\t\t\tthat.onSharelink = res.url;\r\n\t\t\t\t\tthat.dialogShow = true;\r\n\t\t\t\t\tthat.getMemberInfo(that.mid);\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\tlet shareLink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#' + res.url;\r\n\t\t\t\t\t\tthat.getMemberInfo(that.mid);\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\tcontent: '复制链接分享好友',\r\n\t\t\t\t\t\t\tconfirmText:'复制分享',\r\n\t\t\t\t\t\t\tsuccess: function (ress) {\r\n\t\t\t\t\t\t\t\tif (ress.confirm) {\r\n\t\t\t\t\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\t\t\t\t\tdata: shareLink,\r\n\t\t\t\t\t\t\t\t\tsuccess: function() {\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '复制成功,快去分享吧！',\r\n\t\t\t\t\t\t\t\t\t\t\tduration: 3000,\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '复制失败',\r\n\t\t\t\t\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n      showglass:function(e){\r\n      \tvar that = this\r\n      \tvar grid = e.currentTarget.dataset.grid;\r\n      \tvar index = e.currentTarget.dataset.index;\r\n        \r\n      \tif(that.glassrecordlist.length<1){\r\n      \t\t//没有数据 就重新请求\r\n      \t\tthat.getglassrecord();\r\n      \t}else{\r\n      \t\tthat.isshowglass = true\r\n      \t}\r\n      \t\r\n      \tthat.curindex = index\r\n      \tthat.grid = grid\r\n      },\r\n      getglassrecord:function(e){\r\n      \tvar that = this\r\n      \tif(that.hasglassproduct==1){\r\n      \t\tthat.loading  = true;\r\n      \t\tapp.post('ApiGlass/myrecord', {pagenum:1,listrow:100}, function (res) {\r\n      \t\t\tthat.loading = false;\r\n      \t\t  var datalist = res.data;\r\n      \t\t\tthat.glassrecordlist = datalist;\r\n            that.isshowglass = true\r\n      \t\t});\r\n      \t}\r\n      },\r\n      hideglass:function(e){\r\n      \tvar that = this\r\n      \tthat.isshowglass = false;\r\n      },\r\n      chooseglass:function(e){\r\n      \tvar that = this;\r\n      \tvar gindex = e.detail.value;\r\n      \tvar prodata = that.prodata;\r\n      \tvar index = that.curindex;\r\n      \tvar glassrecordlist = that.glassrecordlist;\r\n      \tvar prodataArr = [];\r\n      \tvar sid = glassrecordlist[gindex].id\r\n        that.prodata[index].product.glass_record_id = sid;\r\n        that.prodata[index].product.glassrecord = glassrecordlist[gindex];\r\n        that.isshowglass = false;\r\n      },\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* #ifdef H5 */\r\n\t/deep/ .input-value{padding: 0px 0px !important;color:#626262 !important;font-size:28rpx;}\r\n\t/deep/ .placeholder{color:#626262 !important;font-size:28rpx;}\r\n\t/* #endif */\r\n\t.headimg-mendian image{ width: 100rpx; height:100rpx; border-radius:10rpx;margin-right: 20rpx;}\r\n\t.data-v-31ccf324{padding: 0px 0px !important;color:#626262 !important;font-size:28rpx;}\r\n\t.content{width: 95%;margin: 0 auto;}\r\n\t.item{width: 100%;border-radius: 12rpx;background: #fff;margin-top: 20rpx;padding: 15rpx;justify-content: space-between;}\r\n\t.itemfirst{width: 100%;height:120rpx;margin-top: 20rpx;justify-content: space-between;}\r\n\t.itemfirst-options{width: 47%;height: 100%;border-radius: 12rpx;background: #fff;justify-content: space-between;padding: 0rpx 15rpx;}\r\n\t.avat-img-view {width: 80rpx;height:80rpx;border-radius: 50%;overflow: hidden;}\r\n\t.avat-img-view image{width: 100%;height: 100%;}\r\n\t.title-view{justify-content: space-between;padding: 15rpx 0rpx;}\r\n\t.title-view .but-class{width: 150rpx;height: 50rpx;line-height: 50rpx;color: #fff;text-align: center;font-size: 24rpx;border-radius:35rpx}\r\n\t.user-info{margin-left: 20rpx;}\r\n\t.user-info .un-text{font-size: 28rpx;color: rgba(34, 34, 34, 0.7);white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 186rpx;} \r\n\t.user-info .tel-text{font-size: 26rpx;color: rgba(34, 34, 34, 0.7);margin-top: 5rpx;}\r\n\t.jiantou-img{width: 24rpx;height: 24rpx;}\r\n\t.jiantou-img image{width: 100%;height: 100%;}\r\n\t.input-view{padding: 10rpx 0rpx;margin-bottom: 10rpx;}\r\n\t.input-view .picker-paytype{display: flex;align-items: center;justify-content: space-between;}\r\n\t.picker-class{width: 500rpx;}\r\n\t.input-view .input-title{width: 150rpx;white-space: nowrap;}\r\n\t.input-view .but-class{width: 100rpx;height: 50rpx;line-height: 50rpx;color: #fff;text-align: center;font-size: 24rpx;border-radius:35rpx}\r\n\t.address-view{display: flex;align-items: flex-start;}\r\n\t.address-chose{justify-content: space-between;width: 540rpx;display: flex;align-items: flex-start;}\r\n\t.address-plac{color:#626262;font-size:28rpx;}\r\n\t.product {width: 100%;border-bottom: 1px solid #f4f4f4;}\r\n\t.product .item {position: relative;width: 100%;padding: 20rpx 0;background: #fff;}\r\n\t.product .del-view{position: absolute;right: 10rpx;top: 50%;margin-top: -7px;padding: 10rpx;}\r\n\t.product .info {padding-left: 20rpx;}\r\n\t.product .info .f1 {color: #222222;font-weight: bold;font-size: 26rpx;line-height: 36rpx;margin-bottom: 10rpx;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}\r\n\t.product .info .f2 {color: #999999;font-size: 24rpx}\r\n\t.product .info .f3 {color: #FF4C4C;font-size: 28rpx;display: flex;align-items: center;margin-top: 10rpx}\r\n\t.product .info .modify-price{padding: 10rpx 0rpx;}\r\n\t.product image {width: 140rpx;height: 140rpx}\r\n\t.freight {width: 100%;padding: 10rpx 0;background: #fff;display: flex;flex-direction: column;}\r\n\t.freight .f1 {color: #333;margin-bottom: 10rpx}\r\n\t.freight .f2 {color: #111111;text-align: right;flex: 1}\r\n\t.freight .f3 {width: 24rpx;height: 28rpx;}\r\n\t.freighttips {color: red;font-size: 24rpx;}\r\n\t.freight-ul {width: 100%;}\r\n\t.freight-li {background: #F5F6F8;border-radius: 24rpx;color: #6C737F;font-size: 24rpx;line-height: 48rpx;padding: 0 28rpx;margin: 12rpx 10rpx 12rpx 0;display: inline-block;white-space: break-spaces;max-width: 610rpx;vertical-align: middle;}\r\n\t.inputPrice {border: 1px solid #ddd; width: 200rpx; height: 40rpx; border-radius: 10rpx; padding: 0 4rpx;}\r\n\t.footer {width: 96%;background: #fff;margin-top: 5px;position: fixed;left: 0px;bottom: 0px;padding: 0 2%;display: flex;align-items: center;z-index: 8;box-sizing:content-box}\r\n\t.footer .text1 {height: 110rpx;line-height: 110rpx;color: #2a2a2a;font-size: 30rpx;}\r\n\t.footer .text1 text {color: #e94745;font-size: 32rpx;}\r\n\t.footer .op {width: 200rpx;height: 80rpx;line-height: 80rpx;color: #fff;text-align: center;font-size: 30rpx;border-radius: 44rpx}\r\n\t.footer .op[disabled] { background: #aaa !important; color: #666;}\r\n\t.check-area{display: flex;align-items: center;position: fixed;left: 0px;bottom: 182rpx;width: 100%;padding: 10rpx 15rpx;}\r\n\t.orderinfo{width:94%;margin:0 3%;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\r\n\t.ff_btn{ height:80rpx;line-height: 80rpx;width:90%;margin:0 auto;border-radius:40rpx;margin-top:40rpx;color: #fff;font-size: 28rpx;font-weight:bold}\r\n\t\r\n\t.navigation {width: 100%;padding-bottom:10px;overflow: hidden;}\r\n\t.navcontent {display: flex;align-items: center;padding-left: 10px;}\r\n\t.header-location-top{position: relative;display: flex;justify-content: center;align-items: center;flex:1;}\r\n\t.header-back-but{position: absolute;left:12rpx;display: flex;align-items: center;width: 35rpx;height: 35rpx;overflow: hidden;}\r\n\t.header-back-but image{width: 17rpx;height: 31rpx;} \r\n\t.header-page-title{display: flex;flex: 1;align-items: center;justify-content: center;font-size: 34rpx;letter-spacing: 2rpx;}\r\n\t.storeitem {width: 100%;padding: 20rpx 0;display: flex;flex-direction: column;color: #333}\r\n\t.storeitem .panel {width: 100%;height: 60rpx;line-height: 60rpx;font-size: 28rpx;color: #333;margin-bottom: 10rpx;display: flex}\r\n\t.storeitem .panel .f1 {color: #333}\r\n\t.storeitem .panel .f2 {color: #111;font-weight: bold;text-align: right;flex: 1}\r\n\t.storeitem .radio-item {display: flex;width: 100%;color: #000;align-items: center;background: #fff;padding:20rpx 20rpx;border-bottom:1px dotted #f1f1f1}\r\n\t.storeitem .radio-item:last-child {border: 0}\r\n\t.storeitem .radio-item .f1 {color: #333;font-size:30rpx;flex: 1}\r\n\t.storeitem .headimg image{ width: 100rpx; height:100rpx; border-radius:10rpx;margin-right: 20rpx;}\r\n\t\r\n\t\r\n\t.storeitem .radio {flex-shrink: 0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-left: 30rpx}\r\n\t.storeitem .radio .radio-img {width: 100%;height: 100%}\r\n\t.storeviewmore{width:100%;text-align:center;color:#889;height:40rpx;line-height:40rpx;margin-top:10rpx}\r\n  \r\n  .glassinfo{color: #333; padding:10rpx; border-radius: 10rpx;display: flex;justify-content: space-between;align-items: center;background: #f4f4f4;margin-top: 10rpx;font-size: 30rpx;}\r\n  .glassinfo .f2{display: flex;justify-content: flex-end;}\r\n  .glassinfo .f2 image{width: 32rpx;height: 36rpx;padding-top: 4rpx;}\r\n  .glassinfo .f1{font-weight: bold;}\r\n  .glass_popup .popup__content{max-height: 920rpx;}\r\n  .glass_popup .gr-add{margin-top: 30rpx;}\r\n  .glass_popup .gr-add .gr-btn{width: 240rpx;color: #FFF;border-radius: 10rpx;}\r\n  .glass_popup .popup__title{padding: 30rpx 0 0 0;}\r\n  .glassitem{background:#f7f7f7;border-radius: 10rpx;width: 94%;margin: 20rpx 3%;padding: 20rpx 0;}\r\n  .glassitem .fc{display: flex;align-items: center;}\r\n  .glassitem .gremark{padding: 0 20rpx;padding-left: 100rpx;font-size: 24rpx;color: #707070;}\r\n  .glassitem.on{background: #ffe6c8;}\r\n  .glassitem .radio{width: 80rpx;flex-shrink: 0;text-align: center;}\r\n  .glassitem .gcontent{flex:1;padding: 0 20rpx;}\r\n  .glassitem .grow{line-height: 46rpx;color: #545454;font-size: 24rpx;}\r\n  .glassitem .gtitle{font-size: 24rpx;color: #222222;}\r\n  .glassitem .bt{border-top:1px solid #e3e3e3}\r\n  .glassitem .opt{width: 80rpx;font-size: 26rpx;border: 1rpx solid #c5c5c5;border-radius: 6rpx;height: 50rpx;line-height: 50rpx;text-align: center;margin-right: 16rpx;}\r\n  .pdl10{padding-left: 10rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dkorder.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dkorder.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213040980\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}