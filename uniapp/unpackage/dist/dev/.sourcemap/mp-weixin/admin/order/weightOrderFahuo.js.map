{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/weightOrderFahuo.vue?a443", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/weightOrderFahuo.vue?b590", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/weightOrderFahuo.vue?6c16", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/weightOrderFahuo.vue?b506", "uni-app:///admin/order/weightOrderFahuo.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/weightOrderFahuo.vue?cd1b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/weightOrderFahuo.vue?25e7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "detail", "prolist", "totalprice", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "id", "setTimeout", "calprice", "inputChange", "fahuo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,yBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACoE;AACL;AACa;;;AAG5E;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAA80B,CAAgB,8yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4Gl2B;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAA;UACAA;UACAA;UACAA;QACA;UACAC;UACAE;YACAF;UACA;QACA;MACA;IACA;IACAG;MACA;MACA;MACA;MACA;QACA;QACA;QACAT;MACA;MACAK;IACA;IACAK;MACA;MACA;MACA;MACA;MACA;MACAX;MACAM;MACAA;IACA;IACAM;MACA;MACAL;QAAAC;QAAAR;MAAA;QACA;UACAO;UACAE;YACAF;UACA;QACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC1LA;AAAA;AAAA;AAAA;AAA2rC,CAAgB,2mCAAG,EAAC,C;;;;;;;;;;;ACA/sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/order/weightOrderFahuo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/order/weightOrderFahuo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./weightOrderFahuo.vue?vue&type=template&id=30a0f140&\"\nvar renderjs\nimport script from \"./weightOrderFahuo.vue?vue&type=script&lang=js&\"\nexport * from \"./weightOrderFahuo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./weightOrderFahuo.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/order/weightOrderFahuo.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./weightOrderFahuo.vue?vue&type=template&id=30a0f140&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload && _vm.detail.leveldk_money > 0 ? _vm.t(\"会员\") : null\n  var m1 = _vm.isload && _vm.detail.coupon_money > 0 ? _vm.t(\"优惠券\") : null\n  var m2 = _vm.isload && _vm.detail.scoredk_money > 0 ? _vm.t(\"积分\") : null\n  var m3 = _vm.isload && _vm.detail.dec_money > 0 ? _vm.t(\"余额\") : null\n  var m4 = _vm.isload ? _vm.t(\"color1\") : null\n  var m5 = _vm.isload ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./weightOrderFahuo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./weightOrderFahuo.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"address\">\r\n\t\t\t<view class=\"img\">\r\n\t\t\t\t<image :src=\"pre_url+'/static/img/address3.png'\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info\">\r\n\t\t\t\t<view class=\"t1\" user-select=\"true\" selectable=\"true\">{{detail.linkman}} <text v-if=\"detail.tel\" @tap=\"goto\" :data-url=\"'tel:'+detail.tel\" style=\"margin-left: 20rpx;\">{{detail.tel}}</text></view>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type!=1 && detail.freight_type!=3\" user-select=\"true\" selectable=\"true\">地址：{{detail.area}}{{detail.address}}</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type==1\" @tap=\"openLocation\" :data-address=\"storeinfo.address\" :data-latitude=\"storeinfo.latitude\" :data-longitude=\"storeinfo.longitude\" user-select=\"true\" selectable=\"true\">取货地点：{{storeinfo.name}} - {{storeinfo.address}}</text>\r\n      </view>\r\n\t\t</view>\r\n\t\t<view class=\"orderinfo\">\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">订单金额</text>\r\n\t\t\t\t<text class=\"t2 red\">¥{{detail.product_price}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.leveldk_money > 0\">\r\n\t\t\t\t<text class=\"t1\">{{t('会员')}}折扣</text>\r\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.leveldk_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.manjian_money > 0\">\r\n\t\t\t\t<text class=\"t1\">满减活动</text>\r\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.manjian_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.coupon_money > 0\">\r\n\t\t\t\t<text class=\"t1\">{{t('优惠券')}}抵扣</text>\r\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.coupon_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"item\" v-if=\"detail.scoredk_money > 0\">\r\n\t\t\t\t<text class=\"t1\">{{t('积分')}}抵扣</text>\r\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.scoredk_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.dec_money > 0\">\r\n\t\t\t\t<text class=\"t1\">{{t('余额')}}抵扣</text>\r\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.dec_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">实付金额</text>\r\n\t\t\t\t<text class=\"t2 red\">¥{{detail.totalprice}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">下单时间</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">订单状态</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0\">未付款</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==1\">待发货</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==2\">已发货</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==3\">已收货</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==4\">已关闭</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\r\n\t\t\t\t<text class=\"t1\">退款状态</text>\r\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==1\">审核中,¥{{detail.refund_money}}</text>\r\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==2\">已退款,¥{{detail.refund_money}}</text>\r\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==3\">已驳回,¥{{detail.refund_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\r\n\t\t\t\t<text class=\"t1\">退款原因</text>\r\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_reason||'暂无'}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.refund_checkremark\">\r\n\t\t\t\t<text class=\"t1\">审核备注</text>\r\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_checkremark}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"product\">\r\n\t\t\t<view class=\"colitem row-header\">\r\n\t\t\t\t<view class=\"col col-1\">商品</view>\r\n\t\t\t\t<view class=\"col col-2\">单价(元/斤)</view>\r\n\t\t\t\t<view class=\"col col-3\">应拣(斤)</view>\r\n\t\t\t\t<view class=\"col col-4\">实拣(斤)</view>\r\n\t\t\t\t<view class=\"col col-5\">总价</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-for=\"(item, idx) in prolist\" :key=\"idx\" class=\"colitem\">\r\n\t\t\t\t<view class=\"col col-1\">\r\n\t\t\t\t\t<view>{{item.name}}</view>\r\n\t\t\t\t\t<view>{{item.ggname}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"col col-2\"><input type=\"text\" :value=\"item.real_sell_price\" @input=\"inputChange\"  :data-index=\"idx\" data-field=\"real_sell_price\" /></view>\r\n\t\t\t\t<view class=\"col col-3\">{{item.total_weight}}</view>\r\n\t\t\t\t<view class=\"col col-4\"><input type=\"text\" :value=\"item.real_total_weight\" @input=\"inputChange\" :data-index=\"idx\" data-field=\"real_total_weight\"/></view>\r\n\t\t\t\t<view class=\"col col-5\">{{item.real_totalprice}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"heji\"><text>合计：</text><text :style=\"{color:t('color1')}\">￥{{totalprice}}</text></view>\r\n\t\t</view>\r\n\t\t<view class=\"tips\">\r\n\t\t\t<view>* 发货重量为实际结算重量;</view>\r\n\t\t\t<view>* 实拣重量小于购买重量，订单差额会原路退还用户;</view>\r\n\t\t\t<view>* 实拣重量大于购买重量，用户无法追加订单金额，请谨慎操作！</view>\r\n\t\t</view>\r\n\t\t<view style=\"width:100%;height:160rpx\"></view>\r\n\t\t\r\n\t\t<view class=\"bottom\">\r\n\t\t\t<view class=\"btn2\" :style=\"{background:t('color1'),color:'#FFF'}\" @tap=\"fahuo\">确定发货</view>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar interval = null;\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n        opt:{},\r\n        loading:false,\r\n        isload: false,\r\n        menuindex:-1,\r\n\r\n        pre_url:app.globalData.pre_url,\r\n        detail: \"\",\r\n        prolist: \"\",\r\n\t\t\t\ttotalprice:0\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiAdminOrder/weightOrderFahuo', {id: that.opt.id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif(res.status==1){\r\n\t\t\t\t\tthat.detail = res.detail;\r\n\t\t\t\t\tthat.prolist = res.prolist;\r\n\t\t\t\t\tthat.totalprice = res.detail.totalprice\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\tapp.goback(true);\r\n\t\t\t\t\t},1000)\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tcalprice:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar prolist = that.prolist\r\n\t\t\tvar totalprice = 0;\r\n\t\t\tfor(var i in prolist){\r\n\t\t\t\tvar gprice = prolist[i].real_sell_price;\r\n\t\t\t\tvar gweight = prolist[i].real_total_weight;\r\n\t\t\t\ttotalprice = totalprice + gprice * gweight\r\n\t\t\t}\r\n\t\t\tthat.totalprice = totalprice.toFixed(2)\r\n\t\t},\r\n\t\tinputChange:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar prolist = that.prolist\r\n\t\t\tvar field = e.currentTarget.dataset.field\r\n\t\t\tvar index = e.currentTarget.dataset.index\r\n\t\t\tvar val = e.detail.value;\r\n\t\t\tprolist[index][field] = val\r\n\t\t\tthat.prolist = prolist\r\n\t\t\tthat.calprice()\r\n\t\t},\r\n\t\tfahuo:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tapp.post('ApiAdminOrder/weightOrderFahuo', {id: that.opt.id,prolist:that.prolist}, function (res) {\r\n\t\t\t\tif(res.status==1){\r\n\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\tapp.goto('/admin/order/shoporder');\r\n\t\t\t\t\t},1000)\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.address{ display:flex;width: 100%; padding: 20rpx 3%; background: #FFF;}\r\n.address .img{width:40rpx}\r\n.address image{width:40rpx; height:40rpx;}\r\n.address .info{flex:1;display:flex;flex-direction:column;}\r\n.address .info .t1{font-size:28rpx;font-weight:bold;color:#333}\r\n.address .info .t2{font-size:24rpx;color:#999}\r\n\r\n.product{background: #ffffff;padding: 20rpx;margin-top: 10rpx;}\r\n.colitem{display: flex;justify-content: space-around;max-width: 100%;overflow-y: scroll;flex-wrap: nowrap;padding: 20rpx 0;font-size: 24rpx;align-items: center;}\r\n.colitem view{text-align: center;}\r\n.col-1 {width: 150rpx;}\r\n.col input{border-bottom: 1rpx solid #999;height: 60rpx;width: 140rpx;font-size: 24rpx;}\r\n.row-header{border-bottom: 1rpx solid #f1f1f1;padding-bottom: 14rpx;font-size: 28rpx;}\r\n.heji{display: flex;justify-content: space-between;align-items: center;font-weight: bold;font-size: 30rpx;padding: 20rpx 20rpx 0 50rpx;border-top: 1rpx solid #f1f1f1;}\r\n.orderinfo{width:100%;border-radius:8rpx;margin-top:16rpx;margin-top:10rpx;padding: 14rpx 3%;background: #FFF;}\r\n.orderinfo .item{display:flex;width:100%;padding:10rpx 0;}\r\n.orderinfo .item:last-child{ border-bottom: 0;}\r\n.orderinfo .item .t1{width:200rpx;color: #666;}\r\n.orderinfo .item .t2{flex:1;text-align:right}\r\n.orderinfo .item .red{color:red}\r\n\r\n.bottom{ width: 100%;padding: 14rpx 0;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:center;align-items:center;}\r\n.btn2{border-radius:3px;text-align:center;width: 92%;margin: 0 4%;padding: 16rpx}\r\n.tips{background: #fff; width: 100%;font-size: 24rpx;line-height: 40rpx;color: #fca92d;padding: 20rpx;margin-top: 10rpx;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./weightOrderFahuo.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./weightOrderFahuo.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213041013\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}