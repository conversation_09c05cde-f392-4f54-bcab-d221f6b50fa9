{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/seckillorderdetail.vue?8dd3", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/seckillorderdetail.vue?fce7", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/seckillorderdetail.vue?d20e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/seckillorderdetail.vue?2792", "uni-app:///admin/order/seckillorderdetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/seckillorderdetail.vue?826b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/seckillorderdetail.vue?ed17"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "expressdata", "express_index", "express_no", "prodata", "detail", "team", "prolist", "shopset", "storeinfo", "lefttime", "p<PERSON><PERSON><PERSON><PERSON>", "peisonguser2", "index2", "express_pic", "express_fhname", "express_fhaddress", "express_shname", "express_shaddress", "express_remark", "myt_weight", "myt_remark", "mytindex", "myt_shop_id", "onLoad", "onPullDownRefresh", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "id", "setremark", "setremarkconfirm", "type", "orderid", "content", "setTimeout", "fahuo", "dialogExpressClose", "dialogExpress10Close", "expresschange", "setexpressno", "<PERSON><PERSON><PERSON><PERSON>", "express_com", "setexpress_pic", "setexpress_fhname", "setexpress_fhaddress", "setexpress_shname", "setexpress_shaddress", "setexpress_remark", "confirmfahuo10", "pic", "fhname", "fhaddress", "shname", "shaddress", "remark", "ispay", "<PERSON><PERSON><PERSON>", "delOrder", "closeOrder", "refundnopass", "refundpass", "peisong", "psid", "dialogPeisongClose", "peisong<PERSON>hange", "confirmPeisong", "uploadimg", "pics", "removeimg", "peisongMyt", "goMyt", "confirmfahuo11", "dialogExpress11Close", "mytWeight", "mytRemark", "mytshopChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,2BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AACsE;AACL;AACa;;;AAG9E;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,wFAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3EA;AAAA;AAAA;AAAA;AAAg1B,CAAgB,gzBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6Vp2B;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAG;MACA;IACA;IACAC;MACA;MACA;MACAH;QAAAI;QAAAC;QAAAC;MAAA;QACAN;QACAO;UACAR;QACA;MACA;IACA;IACAS;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACAb;QAAAI;QAAAC;QAAAjC;QAAA0C;MAAA;QACAd;QACAO;UACAR;QACA;MACA;IACA;IACAgB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACArB;QAAAI;QAAAC;QAAAiB;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;QACA3B;QACAO;UACAR;QACA;MACA;IACA;IACA6B;MACA;MACA;MACA5B;QACAA;QACAA;UAAAI;UAAAC;QAAA;UACAL;UACAA;UACAO;YACAR;UACA;QACA;MACA;IACA;IACA8B;MACA;MACA;MACA7B;QACAA;QACAA;UAAAI;UAAAC;QAAA;UACAL;UACAA;UACAO;YACAR;UACA;QACA;MACA;IACA;IACA+B;MACA;MACA;MACA9B;MACAA;QACAA;UAAAI;UAAAC;QAAA;UACAL;UACAA;UACAO;YACAP;UACA;QACA;MACA;IACA;IACA+B;MACA;MACA;MACA/B;QACAA;QACAA;UAAAI;UAAAC;QAAA;UACAL;UACAA;UACAO;YACAR;UACA;QACA;MACA;IACA;IACAiC;MACA;MACA;MACAhC;QACAA;QACAA;UAAAI;UAAAC;QAAA;UACAL;UACAA;UACAO;YACAR;UACA;QACA;MACA;IACA;IACAkC;MACA;MACA;MACAjC;QACAA;QACAA;UAAAI;UAAAC;QAAA;UACAL;UACAA;UACAO;YACAR;UACA;QACA;MACA;IACA;IACAmC;MACA;MACAnC;MACAC;QAAAI;QAAAC;MAAA;QACAN;QACA;QACA;QACA;QACA;QAEA;QACA;UACAlB;QACA;QACAkB;QACAA;QACA;UACAA;QACA;UACA;YACA;UACA;YACA;UACA;UACA;YACA;UACA;YACA;UACA;UACAC;YACAA;cAAAI;cAAAC;cAAA8B;YAAA;cACAnC;cACAO;gBACAR;cACA;YACA;UACA;QACA;MACA;IACA;IACAqC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAtC;QAAAI;QAAAC;QAAA8B;MAAA;QACAnC;QACAD;QACAQ;UACAR;QACA;MACA;IACA;IACAwC;MACA;MACA;MACA;MACA;MACA;MACA;MACAvC;QACA;UACAwC;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA1C;MACA;IACA;IACA2C;MACA;MACA;MACA;QACA;MACA;QACA3C;MACA;IACA;IACA4C;MACA;MACA;MACA;MACA3C;QACAD;QACAA;QACA;UACAK;UACAC;UACAhB;UACAC;UACAE;QACA;QACAQ;UACAD;UACA;YACAC;YACAO;cACAR;YACA;UACA;YACAC;UACA;QAEA;MACA;IACA;IACA4C;MACA;MACA7C;IACA;IACA8C;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACAjD;MACA;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACjsBA;AAAA;AAAA;AAAA;AAA6rC,CAAgB,6mCAAG,EAAC,C;;;;;;;;;;;ACAjtC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/order/seckillorderdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/order/seckillorderdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./seckillorderdetail.vue?vue&type=template&id=5eb1f382&\"\nvar renderjs\nimport script from \"./seckillorderdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./seckillorderdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./seckillorderdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/order/seckillorderdetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./seckillorderdetail.vue?vue&type=template&id=5eb1f382&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"会员\") : null\n  var m1 = _vm.isload && _vm.detail.disprice > 0 ? _vm.t(\"会员\") : null\n  var m2 = _vm.isload && _vm.detail.couponmoney > 0 ? _vm.t(\"优惠券\") : null\n  var m3 = _vm.isload && _vm.detail.scoredk > 0 ? _vm.t(\"积分\") : null\n  var g0 =\n    _vm.isload && _vm.detail.isfuwu && _vm.detail.fuwuendtime > 0\n      ? _vm._.dateFormat(_vm.detail.fuwuendtime, \"Y-m-d H:i\")\n      : null\n  var g1 = _vm.isload ? _vm.detail.formdata.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./seckillorderdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./seckillorderdetail.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"ordertop\" :style=\"'background:url(' + pre_url + '/static/img/ordertop.png);background-size:100%'\">\n\t\t\t<view class=\"f1\" v-if=\"detail.status==0\">\n\t\t\t\t<view class=\"t1\">等待买家付款</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==1\">\n\t\t\t\t<view class=\"t1\">已成功付款</view>\n\t\t\t\t<view class=\"t2\" v-if=\"detail.freight_type!=1\">请尽快发货</view>\n\t\t\t\t<view class=\"t2\" v-if=\"detail.freight_type==1\">待提货</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==2\">\n\t\t\t\t<view class=\"t1\">订单已发货</view>\n\t\t\t\t<view class=\"t2\" v-if=\"detail.freight_type!=3\">发货信息：{{detail.express_com}} {{detail.express_no}}</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==3\">\n\t\t\t\t<view class=\"t1\">订单已完成</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==4\">\n\t\t\t\t<view class=\"t1\">订单已取消</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"address\">\n\t\t\t<view class=\"img\">\n\t\t\t\t<image :src=\"pre_url+'/static/img/address3.png'\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"info\">\n\t\t\t\t<text class=\"t1\" user-select=\"true\" selectable=\"true\">{{detail.linkman}} {{detail.tel}}</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type!=1 && detail.freight_type!=3\" user-select=\"true\" selectable=\"true\">地址：{{detail.area}}{{detail.address}}</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type==1\" @tap=\"openLocation\" :data-address=\"storeinfo.address\" :data-latitude=\"storeinfo.latitude\" :data-longitude=\"storeinfo.longitude\" user-select=\"true\" selectable=\"true\">取货地点：{{storeinfo.name}} - {{storeinfo.address}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"product\">\n\t\t\t<view class=\"content\">\n\t\t\t\t<view @tap=\"goto\" :data-url=\"'/activity/seckill/product?id=' + detail.proid\">\n\t\t\t\t\t<image :src=\"detail.propic\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t<text class=\"t1\">{{detail.proname}}</text>\n\t\t\t\t\t<text class=\"t2\">{{detail.ggname}}</text>\n\t\t\t\t\t<view class=\"t3\"><text class=\"x1 flex1\">￥{{detail.sell_price}}</text><text class=\"x2\">×{{detail.num}}</text></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo\" v-if=\"(detail.status==3 || detail.status==2) && (detail.freight_type==3 || detail.freight_type==4)\">\n\t\t\t<view class=\"item flex-col\">\n\t\t\t\t<text class=\"t1\" style=\"color:#111\">发货信息</text>\n\t\t\t\t<text class=\"t2\" style=\"text-align:left;margin-top:10rpx;padding:0 10rpx\" user-select=\"true\" selectable=\"true\">{{detail.freight_content}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单人</text>\n\t\t\t\t<text class=\"flex1\"></text>\n\t\t\t\t<image :src=\"detail.headimg\" style=\"width:80rpx;height:80rpx;margin-right:8rpx\"/>\n\t\t\t\t<text  style=\"height:80rpx;line-height:80rpx\">{{detail.nickname}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}ID</text>\n\t\t\t\t<text class=\"t2\">{{detail.mid}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\" v-if=\"detail.remark\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">备注</text>\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{detail.remark}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单编号</text>\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{detail.ordernum}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytime\">\n\t\t\t\t<text class=\"t1\">支付时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytime\">\n\t\t\t\t<text class=\"t1\">支付方式</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytype}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>1 && detail.send_time\">\n\t\t\t\t<text class=\"t1\">发货时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.send_time}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status==3 && detail.collect_time\">\n\t\t\t\t<text class=\"t1\">收货时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.collect_time}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">商品金额</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.product_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.disprice > 0\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}折扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.leveldk_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.jianmoney > 0\">\n\t\t\t\t<text class=\"t1\">满减活动</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.manjian_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">配送方式</text>\n\t\t\t\t<text class=\"t2\">{{detail.freight_text}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.freight_type==1 && detail.freightprice > 0\">\n\t\t\t\t<text class=\"t1\">服务费</text>\n\t\t\t\t<text class=\"t2 red\">+¥{{detail.freight_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.freight_time\">\n\t\t\t\t<text class=\"t1\">{{detail.freight_type!=1?'配送':'提货'}}时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.freight_time}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.couponmoney > 0\">\n\t\t\t\t<text class=\"t1\">{{t('优惠券')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.coupon_money}}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"item\" v-if=\"detail.scoredk > 0\">\n\t\t\t\t<text class=\"t1\">{{t('积分')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.scoredk_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">实付款</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.totalprice}}</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0\">未付款</text>\n\t\t\t\t<block v-if=\"detail.status==1\">\n\t\t\t\t\t<text v-if=\"detail.freight_type!=1\" class=\"t2\">待发货</text>\n\t\t\t\t\t<text v-if=\"detail.freight_type==1\" class=\"t2\">待提货</text>\n\t\t\t\t</block>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==2\">已发货</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==3\">已收货</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==4\">已关闭</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\n\t\t\t\t<text class=\"t1\">退款状态</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==1\">审核中,¥{{detail.refund_money}}</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==2\">已退款,¥{{detail.refund_money}}</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==3\">已驳回,¥{{detail.refund_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\n\t\t\t\t<text class=\"t1\">退款原因</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_reason}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_checkremark\">\n\t\t\t\t<text class=\"t1\">审核备注</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_checkremark}}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"item\" v-if=\"detail.isfuwu && detail.fuwuendtime > 0\">\n\t\t\t\t<text class=\"t1\">到期时间</text>\n\t\t\t\t<text class=\"t2 red\">{{_.dateFormat(detail.fuwuendtime,'Y-m-d H:i')}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item flex-col\" v-if=\"(detail.status==1 || detail.status==2) && detail.freight_type==1\">\n\t\t\t\t<text class=\"t1\">核销码</text>\n\t\t\t\t<view class=\"flex-x-center\">\n\t\t\t\t\t<image :src=\"detail.hexiao_qr\" style=\"width:400rpx;height:400rpx\" @tap=\"previewImage\" :data-url=\"detail.hexiao_qr\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo\" v-if=\"(detail.formdata).length > 0\">\n\t\t\t<view class=\"item\" v-for=\"item in detail.formdata\" :key=\"index\">\n\t\t\t\t<text class=\"t1\">{{item[0]}}</text>\n\t\t\t\t<view class=\"t2\" v-if=\"item[2]=='upload'\"><image :src=\"item[1]\" style=\"width:400rpx;height:auto\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"item[1]\"/></view>\n\t\t\t\t<text class=\"t2\" v-else user-select=\"true\" selectable=\"true\">{{item[1]}}</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view style=\"width:100%;height:calc(160rpx + env(safe-area-inset-bottom));\"></view>\n\n\t\t<view class=\"bottom notabbarbot\">\n\t\t\t<view v-if=\"detail.refund_status==1\" class=\"btn2\" @tap=\"refundnopass\" :data-id=\"detail.id\">退款驳回</view>\n\t\t\t<view v-if=\"detail.refund_status==1\" class=\"btn2\" @tap=\"refundpass\" :data-id=\"detail.id\">退款通过</view>\n\t\t\t<view v-if=\"detail.status==0\" class=\"btn2\" @tap=\"closeOrder\" :data-id=\"detail.id\">关闭订单</view>\n\t\t\t<view v-if=\"detail.status==0 && detail.bid==0\" class=\"btn2\" @tap=\"ispay\" :data-id=\"detail.id\">改为已支付</view>\n\t\t\t<view v-if=\"detail.status==1\" class=\"btn2\" @tap=\"fahuo\" :data-id=\"detail.id\">发货</view>\n\t\t\t<view v-if=\"(detail.status==1 || detail.status==2) && detail.freight_type==1\" class=\"btn2\" @tap=\"hexiao\" :data-id=\"detail.id\">核销</view>\n\t\t\t<view v-if=\"detail.status==1 && detail.canpeisong\" class=\"btn2\">\n                <view v-if=\"detail.myt_status\" @tap=\"peisongMyt\" :data-id=\"detail.id\">麦芽田配送</view>\n                <view v-else @tap=\"peisong\" :data-id=\"detail.id\">配送</view>\n            </view>\n\t\t\t<view v-if=\"(detail.status==2 || detail.status==3) && detail.express_com\" class=\"btn2\" @tap=\"goto\" :data-url=\"'/pagesExt/order/logistics?express_com='+detail.express_com+'&express_no='+detail.express_no\">查物流</view>\n\t\t\t<view v-if=\"detail.status==2 && detail.freight_type==10\" class=\"btn2\" @tap=\"fahuo\" :data-id=\"detail.id\">修改物流</view>\n\t\t\t<view v-if=\"detail.status==4\" class=\"btn2\" @tap=\"delOrder\" :data-id=\"detail.id\">删除</view>\n\t\t\t<view class=\"btn2\" @tap=\"setremark\" :data-id=\"detail.id\">设置备注</view>\n\t\t</view>\n\t\t<uni-popup id=\"dialogSetremark\" ref=\"dialogSetremark\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"设置备注\" :value=\"detail.remark\" placeholder=\"请输入备注\" @confirm=\"setremarkconfirm\"></uni-popup-dialog>\n\t\t</uni-popup>\n\n\t\t<uni-popup id=\"dialogExpress\" ref=\"dialogExpress\" type=\"dialog\">\n\t\t\t<view class=\"uni-popup-dialog\">\n\t\t\t\t<view class=\"uni-dialog-title\">\n\t\t\t\t\t<text class=\"uni-dialog-title-text\">发货</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-content\">\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx\">\n\t\t\t\t\t\t\t<text style=\"font-size:28rpx;color:#000\">快递公司：</text>\n\t\t\t\t\t\t\t<picker @change=\"expresschange\" :value=\"express_index\" :range=\"expressdata\" style=\"font-size:28rpx;border: 1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1\">\n\t\t\t\t\t\t\t\t<view class=\"picker\">{{expressdata[express_index]}}</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t</view> \n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n\t\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">快递单号：</view>\n\t\t\t\t\t\t\t<input type=\"text\" placeholder=\"请输入快递单号\" @input=\"setexpressno\" style=\"border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-button-group\">\n\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogExpressClose\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"confirmfahuo\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t<uni-popup id=\"dialogPeisong\" ref=\"dialogPeisong\" type=\"dialog\">\n\t\t\t<view class=\"uni-popup-dialog\">\n\t\t\t\t<view class=\"uni-dialog-title\">\n\t\t\t\t\t<text class=\"uni-dialog-title-text\">请选择配送员</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-content\">\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<picker @change=\"peisongChange\" :value=\"index2\" :range=\"peisonguser2\" style=\"font-size:28rpx;border: 1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1\">\n\t\t\t\t\t\t\t<view class=\"picker\">{{peisonguser2[index2]}}</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-button-group\">\n\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogPeisongClose\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"confirmPeisong\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\n\t\t\n\t\t<uni-popup id=\"dialogExpress10\" ref=\"dialogExpress10\" type=\"dialog\">\n\t\t\t<view class=\"uni-popup-dialog\">\n\t\t\t\t<view class=\"uni-dialog-title\">\n\t\t\t\t\t<text class=\"uni-dialog-title-text\">发货信息</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-content\">\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<view class=\"form-item flex\" style=\"border-bottom:0;\">\n\t\t\t\t\t\t\t<view class=\"f1\" style=\"margin-right:20rpx\">物流单照片</view>\n\t\t\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t\t\t<view class=\"layui-imgbox\" v-if=\"express_pic\">\n\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"0\" data-field=\"express_pic\"><image style=\"display:block\" :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\n\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"express_pic\" @tap=\"previewImage\" :data-url=\"express_pic\" mode=\"widthFix\"></image></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"express_pic\" data-pernum=\"1\" v-else></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"express_pic\" :value=\"express_pic\" maxlength=\"-1\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n\t\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">发货人：</view>\n\t\t\t\t\t\t\t<input type=\"text\" placeholder=\"请输入发货人信息\" @input=\"setexpress_fhname\" style=\"border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n\t\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">发货地址：</view>\n\t\t\t\t\t\t\t<input type=\"text\" placeholder=\"请输入发货地址\" @input=\"setexpress_fhaddress\" style=\"border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n\t\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">收货人：</view>\n\t\t\t\t\t\t\t<input type=\"text\" placeholder=\"请输入发货人信息\" @input=\"setexpress_shname\" style=\"border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n\t\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">收货地址：</view>\n\t\t\t\t\t\t\t<input type=\"text\" placeholder=\"请输入发货地址\" @input=\"setexpress_shaddress\" style=\"border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n\t\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">备注：</view>\n\t\t\t\t\t\t\t<input type=\"text\" placeholder=\"请输入备注\" @input=\"setexpress_remark\" style=\"border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-button-group\">\n\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogExpress10Close\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"confirmfahuo10\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n        \n        <uni-popup id=\"dialogExpress11\" ref=\"dialogExpress11\" type=\"dialog\">\n        \t<view class=\"uni-popup-dialog\">\n        \t\t<view class=\"uni-dialog-title\">\n        \t\t\t<text class=\"uni-dialog-title-text\">配送设置</text>\n        \t\t</view>\n        \t\t<view class=\"uni-dialog-content\">\n        \t\t\t<view>\n                        <view v-if=\"detail.myt_shop\" class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n                        \t<view style=\"font-size:28rpx;color:#555\">门店：</view>\n                            <picker @change=\"mytshopChange\" :value=\"mytindex\" :range=\"detail.myt_shoplist\"  range-key='name' style=\"font-size:28rpx;border: 1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;line-height: 52rpx;\">\n                            \t<view class=\"picker\">{{detail.myt_shoplist[mytindex]['name']}}</view>\n                            </picker>\n                        </view>\n        \t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n        \t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">重量：</view>\n        \t\t\t\t\t<input type=\"text\" placeholder=\"请输入重量(选填)\" @input=\"mytWeight\" style=\"border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n        \t\t\t\t</view>\n        \t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n        \t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">备注：</view>\n        \t\t\t\t\t<input type=\"text\" placeholder=\"请输入备注(选填)\" @input=\"mytRemark\" style=\"border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n        \t\t\t\t</view>\n        \t\t\t</view>\n        \t\t</view>\n        \t\t<view class=\"uni-dialog-button-group\">\n        \t\t\t<view class=\"uni-dialog-button\" @click=\"dialogExpress11Close\">\n        \t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\n        \t\t\t</view>\n        \t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"confirmfahuo11\">\n        \t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\n        \t\t\t</view>\n        \t\t</view>\n        \t</view>\n        </uni-popup>\n\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nvar interval = null;\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tpre_url:app.globalData.pre_url,\n\t\t\texpressdata:[],\n\t\t\texpress_index:0,\n\t\t\texpress_no:'',\n      prodata: '',\n      detail: \"\",\n\t\t\tteam:{},\n      prolist: \"\",\n      shopset: \"\",\n      storeinfo: \"\",\n      lefttime: \"\",\n\t\t\tpeisonguser:[],\n\t\t\tpeisonguser2:[],\n\t\t\tindex2:0,\n\t\t\texpress_pic:'',\n\t\t\texpress_fhname:'',\n\t\t\texpress_fhaddress:'',\n\t\t\texpress_shname:'',\n\t\t\texpress_shaddress:'',\n\t\t\texpress_remark:'',\n            \n            myt_weight:'',\n            myt_remark:'',\n            mytindex:0,\n            myt_shop_id:0\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onUnload: function () {\n    clearInterval(interval);\n  },\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiAdminOrder/seckillorderdetail', {id: that.opt.id}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.expressdata = res.expressdata;\n\t\t\t\tthat.detail = res.detail;\n\t\t\t\tthat.team = res.team;\n\t\t\t\tthat.storeinfo = res.storeinfo;\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n\t\tsetremark:function(){\n\t\t\tthis.$refs.dialogSetremark.open();\n\t\t},\n\t\tsetremarkconfirm: function (done, remark) {\n\t\t\tthis.$refs.dialogSetremark.close();\n\t\t\tvar that = this\n\t\t\tapp.post('ApiAdminOrder/setremark', { type:'seckill',orderid: that.detail.id,content:remark }, function (res) {\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n    },\n\t\tfahuo:function(){\n\t\t\tif(this.detail.freight_type==10){\n\t\t\t\tthis.$refs.dialogExpress10.open();\n\t\t\t}else{\n\t\t\t\tthis.$refs.dialogExpress.open();\n\t\t\t}\n\t\t},\n\t\tdialogExpressClose:function(){\n\t\t\tthis.$refs.dialogExpress.close();\n\t\t},\n\t\tdialogExpress10Close:function(){\n\t\t\tthis.$refs.dialogExpress10.close();\n\t\t},\n\t\texpresschange:function(e){\n\t\t\tthis.express_index = e.detail.value;\n\t\t},\n\t\tsetexpressno:function(e){\n\t\t\tthis.express_no = e.detail.value;\n\t\t},\n\t\tconfirmfahuo:function(){\n\t\t\tthis.$refs.dialogExpress.close();\n\t\t\tvar that = this\n\t\t\tvar express_com = this.expressdata[this.express_index]\n\t\t\tapp.post('ApiAdminOrder/sendExpress', { type:'seckill',orderid: that.detail.id,express_no:that.express_no,express_com:express_com}, function (res) {\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n\t\t},\n\t\tsetexpress_pic:function(e){\n\t\t\tthis.express_pic = e.detail.value;\n\t\t},\n\t\tsetexpress_fhname:function(e){\n\t\t\tthis.express_fhname = e.detail.value;\n\t\t},\n\t\tsetexpress_fhaddress:function(e){\n\t\t\tthis.express_fhaddress = e.detail.value;\n\t\t},\n\t\tsetexpress_shname:function(e){\n\t\t\tthis.express_shname = e.detail.value;\n\t\t},\n\t\tsetexpress_shaddress:function(e){\n\t\t\tthis.express_shaddress = e.detail.value;\n\t\t},\n\t\tsetexpress_remark:function(e){\n\t\t\tthis.express_remark = e.detail.value;\n\t\t},\n\t\tconfirmfahuo10:function(){\n\t\t\tthis.$refs.dialogExpress10.close();\n\t\t\tvar that = this\n\t\t\tvar express_com = this.expressdata[this.express_index]\n\t\t\tapp.post('ApiAdminOrder/sendExpress', { type:'seckill',orderid: that.detail.id,pic:that.express_pic,fhname:that.express_fhname,fhaddress:that.express_fhaddress,shname:that.express_shname,shaddress:that.express_shaddress,remark:that.express_remark}, function (res) {\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n\t\t},\n\t\tispay:function(e){\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要改为已支付吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/ispay', { type:'seckill',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\thexiao:function(e){\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要核销并改为已完成状态吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/hexiao', { type:'seckill',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\tdelOrder: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.showLoading('删除中');\n\t\t\tapp.confirm('确定要删除该订单吗?', function () {\n\t\t\t\tapp.post('ApiAdminOrder/delOrder', { type:'seckill',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tapp.goto('shoporder');\n\t\t\t\t\t}, 1000)\n\t\t\t\t});\n\t\t\t})\n\t\t},\n\t\tcloseOrder: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要关闭该订单吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/closeOrder', { type:'seckill',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function (){\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t});\n\t\t\t})\n\t\t},\n\t\trefundnopass: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要驳回退款申请吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/refundnopass', { type:'seckill',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\trefundpass: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要审核通过并退款吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/refundpass', { type:'seckill',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\tpeisong:function(){\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.post('ApiAdminOrder/getpeisonguser',{type:'seckill_order',orderid:that.detail.id},function(res){\n\t\t\t\tthat.loading = false;\n\t\t\t\tvar peisonguser = res.peisonguser\n\t\t\t\tvar paidantype = res.paidantype\n\t\t\t\tvar psfee = res.psfee\n\t\t\t\tvar ticheng = res.ticheng\n\n\t\t\t\tvar peisonguser2 = [];\n\t\t\t\tfor(var i in peisonguser){\n\t\t\t\t\tpeisonguser2.push(peisonguser[i].title);\n\t\t\t\t}\n\t\t\t\tthat.peisonguser = res.peisonguser;\n\t\t\t\tthat.peisonguser2 = peisonguser2;\n\t\t\t\tif(paidantype==1){\n\t\t\t\t\tthat.$refs.dialogPeisong.open();\n\t\t\t\t}else{\n\t\t\t\t\tif(that.detail.bid == 0){\n\t\t\t\t\t\tvar tips='选择配送员配送，订单将发布到抢单大厅由配送员抢单，配送员提成￥'+ticheng+'，确定要配送员配送吗？';\n\t\t\t\t\t}else{\n\t\t\t\t\t\tvar tips='选择配送员配送，订单将发布到抢单大厅由配送员抢单，需扣除配送费￥'+psfee+'，确定要配送员配送吗？';\n\t\t\t\t\t}\n\t\t\t\t\tif(paidantype == 2){\n\t\t\t\t\t\tvar psid = '-1';\n\t\t\t\t\t}else{\n\t\t\t\t\t\tvar psid = '0';\n\t\t\t\t\t}\n\t\t\t\t\tapp.confirm(tips,function(){\n\t\t\t\t\t\tapp.post('ApiAdminOrder/peisong', { type:'seckill_order',orderid: that.detail.id,psid:psid}, function (res) {\n\t\t\t\t\t\t\tapp.success(res.msg);\n\t\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t\t\t}, 1000)\n\t\t\t\t\t\t})\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\tdialogPeisongClose:function(){\n\t\t\tthis.$refs.dialogPeisong.close();\n\t\t},\n\t\tpeisongChange:function(e){\n\t\t\tthis.index2 = e.detail.value;\n\t\t},\n\t\tconfirmPeisong:function(){\n\t\t\tvar that = this\n\t\t\tvar psid = this.peisonguser[this.index2].id\n\t\t\tapp.post('ApiAdminOrder/peisong', { type:'seckill_order',orderid: that.detail.id,psid:psid}, function (res) {\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tthat.$refs.dialogPeisong.close();\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n\t\t},\n\t\tuploadimg:function(e){\n\t\t\tvar that = this;\n\t\t\tvar pernum = parseInt(e.currentTarget.dataset.pernum);\n\t\t\tif(!pernum) pernum = 1;\n\t\t\tvar field= e.currentTarget.dataset.field\n\t\t\tvar pics = that[field]\n\t\t\tif(!pics) pics = [];\n\t\t\tapp.chooseImage(function(urls){\n\t\t\t\tfor(var i=0;i<urls.length;i++){\n\t\t\t\t\tpics.push(urls[i]);\n\t\t\t\t}\n\t\t\t\tif(field == 'express_pic') that.express_pic = pics[0];\n\t\t\t},pernum);\n\t\t},\n\t\tremoveimg:function(e){\n\t\t\tvar that = this;\n\t\t\tvar index= e.currentTarget.dataset.index\n\t\t\tvar field= e.currentTarget.dataset.field\n\t\t\tif(field == 'express_pic'){\n\t\t\t\tthat.express_pic = '';\n\t\t\t}\n\t\t},\n        peisongMyt:function(e){\n            var that = this;\n            var detail = that.detail;\n            if(detail.myt_set){\n                this.$refs.dialogExpress11.open();\n            }else{\n                that.goMyt();\n            }\n        },\n        goMyt:function(){\n            var that = this;\n            var detail = that.detail;\n            var tips='选择麦芽田配送，订单将派单到第三方配送平台，并扣除相应费用，确定要派单吗？';\n            app.confirm(tips,function(){\n                that.$refs.dialogExpress11.close();\n                that.loading = true;\n                var data = {\n                    type:'seckill_order',\n                    orderid: detail.id,\n                    myt_weight:that.myt_weight,\n                    myt_remark:that.myt_remark,\n                    myt_shop_id:that.myt_shop_id\n                }\n                app.post('ApiAdminOrder/peisong', data, function (res) {\n                    that.loading = false;\n                    if(res.status == 1){\n                        app.success(res.msg);\n                        setTimeout(function () {\n                            that.getdata();\n                        }, 1000)\n                    }else{\n                        app.alert(res.msg)\n                    }\n                    \n                })\n            })\n        },\n        confirmfahuo11:function(){\n        \tvar that = this\n        \tthat.goMyt();\n        },\n        dialogExpress11Close:function(){\n        \tthis.$refs.dialogExpress11.close();\n        },\n        mytWeight:function(e){\n        \tthis.myt_weight = e.detail.value;\n        },\n        mytRemark:function(e){\n        \tthis.myt_remark = e.detail.value;\n        },\n        mytshopChange:function(e){\n            var that = this;\n            var detail   = that.detail;\n            var mytindex = e.detail.value;\n            that.mytindex = mytindex;\n            //that.myt_name  = detail.myt_shoplist[mytindex]['name'];\n            that.myt_shop_id    = detail.myt_shoplist[mytindex]['id'];\n        },\n  }\n};\n</script>\n<style>\n.ordertop{width:100%;height:220rpx;padding:50rpx 0 0 70rpx}\n.ordertop .f1{color:#fff}\n.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}\n.ordertop .f1 .t2{font-size:24rpx}\n\n.address{ display:flex;width: 100%; padding: 20rpx 3%; background: #FFF;}\n.address .img{width:40rpx}\n.address image{width:40rpx; height:40rpx;}\n.address .info{flex:1;display:flex;flex-direction:column;}\n.address .info .t1{font-size:28rpx;font-weight:bold;color:#333}\n.address .info .t2{font-size:24rpx;color:#999}\n\n.product{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\n.product .content{display:flex;position:relative;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;}\n.product .content:last-child{ border-bottom: 0; }\n.product .content image{ width: 140rpx; height: 140rpx;}\n.product .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\n.product .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.product .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\n.product .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\n.product .content .detail .x1{ flex:1}\n.product .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\n.product .content .comment{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc702 solid; border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.product .content .comment2{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc7c2 solid; border-radius:10rpx;background:#fff; color: #ffc7c2;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.orderinfo{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}\n.orderinfo .item:last-child{ border-bottom: 0;}\n.orderinfo .item .t1{width:200rpx;}\n.orderinfo .item .t2{flex:1;text-align:right}\n.orderinfo .item .red{color:red}\n\n.bottom{ width: 100%;height:calc(92rpx + env(safe-area-inset-bottom));padding: 0 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}\n\n.btn1{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}\n.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n.btn3{position:absolute;top:60rpx;right:10rpx;font-size:24rpx;width:120rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n\n.btitle{ width:100%;height:100rpx;background:#fff;padding:0 20rpx;border-bottom:1px solid #f5f5f5}\n.btitle .comment{border: 1px #ffc702 solid;border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.btitle .comment2{border: 1px #ffc7c0 solid;border-radius:10rpx;background:#fff; color: #ffc7c0;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.picker{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n\n.uni-popup-dialog {width: 300px;border-radius: 5px;background-color: #fff;}\n.uni-dialog-title {display: flex;flex-direction: row;justify-content: center;padding-top: 15px;padding-bottom: 5px;}\n.uni-dialog-title-text {font-size: 16px;font-weight: 500;}\n.uni-dialog-content {display: flex;flex-direction: row;justify-content: center;align-items: center;padding: 5px 15px 15px 15px;}\n.uni-dialog-content-text {font-size: 14px;color: #6e6e6e;}\n.uni-dialog-button-group {display: flex;flex-direction: row;border-top-color: #f5f5f5;border-top-style: solid;border-top-width: 1px;}\n.uni-dialog-button {display: flex;flex: 1;flex-direction: row;justify-content: center;align-items: center;height: 45px;}\n.uni-border-left {border-left-color: #f0f0f0;border-left-style: solid;border-left-width: 1px;}\n.uni-dialog-button-text {font-size: 14px;}\n.uni-button-color {color: #007aff;}\n\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\n.layui-imgbox-img>image{max-width:100%;}\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./seckillorderdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./seckillorderdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213041424\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}