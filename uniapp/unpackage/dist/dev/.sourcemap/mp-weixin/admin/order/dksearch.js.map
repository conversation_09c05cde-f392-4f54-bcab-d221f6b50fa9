{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/dksearch.vue?ab03", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/dksearch.vue?2ea7", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/dksearch.vue?5bef", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/dksearch.vue?335a", "uni-app:///admin/order/dksearch.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/dksearch.vue?880d", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/dksearch.vue?bff3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "nomore", "nodata", "keyword", "pagenum", "datalist", "history_list", "history_show", "order", "field", "oldcid", "<PERSON>ecid", "catchegid", "cid", "gid", "cid2", "oldcid2", "catchecid2", "clist", "clist2", "glist", "productlisttype", "showfilter", "cpid", "bid", "set", "mendianid", "latitude", "longitude", "area", "showstyle", "showcommission", "showprice", "params", "showLinkStatus", "onLoad", "uni", "title", "onPullDownRefresh", "onReachBottom", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentTarget", "dataset", "buydialogChange", "addcart", "that", "app", "proid", "ggid", "num", "sell_price", "mid", "setTimeout", "getdata", "isCoupon", "mendian_id", "is_coupon", "getprolist", "order_add_mobile", "showDrawer", "closeDrawer", "change", "searchChange", "searchFocus", "searchConfirm", "searchproduct", "sortClick", "groupClick", "cateClick", "cate2Click", "filterConfirm", "filterReset", "filterClick", "addHistory", "historylist", "newhistorylist", "historyClick", "deleteSearchHistory", "couponAddChange", "id", "name", "pic", "give_num", "delta"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1SA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwN11B;AAAA,eACA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;IAAA,oDACA,oDACA,qDACA,oDACA,yDACA,qDACA,+CACA,sDACA,wDACA;EAEA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACA;IACA;IACA;MACAC;QACAC;MACA;IACA;IACA;IACA;IACA;MACA;QACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QAAAC;UAAAC;QAAA;MAAA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAC;MACAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;QACAN;QACA;UACAA;UACAC;UACAM;YACAN;UACA;QACA;UACAA;QACA;MACA;IACA;IACAO;MACA;MACAR;MACAA;MACA;MACA;MACA;MACA;MACAA;MACA;MACA;QACAS;MACA;MACAR;QAAAlC;QAAAC;QAAAU;QAAAT;QAAAyC;QAAA7B;QAAAC;QAAAC;QAAA4B;MAAA;QACAX;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAY;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAZ;MACAA;MACAA;MACAA;MACA;MACA;MACA;QACAS;MACA;MACAR;QAAA3C;QAAAD;QAAAM;QAAAD;QAAAM;QAAAD;QAAAE;QAAAQ;QAAAC;QAAAgC;QAAA7B;QAAAC;QAAAC;QAAA8B;QAAAF;MAAA;QACAX;QACA;QACA;UACAA;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACA;IACAc;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;IACA;IAEAC;MACA;MACA;MACAnB;MACAA;IACA;IACAoB;MACA;MACApB;MACAA;MACAA;MACAA;IACA;IACAqB;MACA;MACA;MACArB;MACAA;MACAA;IACA;IACAsB;MACA;MACA;MACA;MACAtB;IACA;IACAuB;MACA;MACA;MACA;MACAvB;IACA;IACAwB;MACA;MACA;MACA;MACAxB;IACA;IACAyB;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAC;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;MACA7B;MACAD;IACA;IACA+B;MACA;MACA;MACA;MACA/B;MACAA;IACA;IACAgC;MACA;MACAhC;MACAC;IACA;IACAgC;MACA3C;QAAA4C;QAAAC;QAAAC;QAAAC;MAAA;MACA/C;QACAgD;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5gBA;AAAA;AAAA;AAAA;AAAmrC,CAAgB,mmCAAG,EAAC,C;;;;;;;;;;;ACAvsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/order/dksearch.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/order/dksearch.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./dksearch.vue?vue&type=template&id=462a8dde&\"\nvar renderjs\nimport script from \"./dksearch.vue?vue&type=script&lang=js&\"\nexport * from \"./dksearch.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dksearch.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/order/dksearch.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dksearch.vue?vue&type=template&id=462a8dde&\"", "var components\ntry {\n  components = {\n    uniDrawer: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-drawer/uni-drawer\" */ \"@/components/uni-drawer/uni-drawer.vue\"\n      )\n    },\n    buydialog: function () {\n      return import(\n        /* webpackChunkName: \"components/buydialog/buydialog\" */ \"@/components/buydialog/buydialog.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? !_vm.history_list || _vm.history_list.length == 0 : null\n  var m0 =\n    _vm.isload && (!_vm.field || _vm.field == \"sort\") ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload && _vm.field == \"sales\" ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload && _vm.field == \"sell_price\" ? _vm.t(\"color1\") : null\n  var m3 =\n    _vm.isload && _vm.field == \"sell_price\" && _vm.order == \"asc\"\n      ? _vm.t(\"color1\")\n      : null\n  var m4 =\n    _vm.isload && _vm.field == \"sell_price\" && _vm.order == \"desc\"\n      ? _vm.t(\"color1\")\n      : null\n  var m5 = _vm.isload && _vm.catchegid == \"\" ? _vm.t(\"color1\") : null\n  var m6 = _vm.isload && _vm.catchegid == \"\" ? _vm.t(\"color1rgb\") : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.glist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m7 = _vm.catchegid == item.id ? _vm.t(\"color1\") : null\n        var m8 = _vm.catchegid == item.id ? _vm.t(\"color1rgb\") : null\n        return {\n          $orig: $orig,\n          m7: m7,\n          m8: m8,\n        }\n      })\n    : null\n  var m9 =\n    _vm.isload && (!_vm.bid || _vm.bid <= 0) && _vm.catchecid == _vm.oldcid\n      ? _vm.t(\"color1\")\n      : null\n  var m10 =\n    _vm.isload && (!_vm.bid || _vm.bid <= 0) && _vm.catchecid == _vm.oldcid\n      ? _vm.t(\"color1rgb\")\n      : null\n  var l1 =\n    _vm.isload && (!_vm.bid || _vm.bid <= 0)\n      ? _vm.__map(_vm.clist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m11 = _vm.catchecid == item.id ? _vm.t(\"color1\") : null\n          var m12 = _vm.catchecid == item.id ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m11: m11,\n            m12: m12,\n          }\n        })\n      : null\n  var m13 =\n    _vm.isload && !(!_vm.bid || _vm.bid <= 0) && _vm.catchecid2 == _vm.oldcid2\n      ? _vm.t(\"color1\")\n      : null\n  var m14 =\n    _vm.isload && !(!_vm.bid || _vm.bid <= 0) && _vm.catchecid2 == _vm.oldcid2\n      ? _vm.t(\"color1rgb\")\n      : null\n  var l2 =\n    _vm.isload && !(!_vm.bid || _vm.bid <= 0)\n      ? _vm.__map(_vm.clist2, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m15 = _vm.catchecid2 == item.id ? _vm.t(\"color1\") : null\n          var m16 = _vm.catchecid2 == item.id ? _vm.t(\"color1rgb\") : null\n          return {\n            $orig: $orig,\n            m15: m15,\n            m16: m16,\n          }\n        })\n      : null\n  var m17 = _vm.isload ? _vm.t(\"color1\") : null\n  var g1 = _vm.isload ? _vm.datalist && _vm.datalist.length > 0 : null\n  var l3 =\n    _vm.isload && g1\n      ? _vm.__map(_vm.datalist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m18 =\n            _vm.showcommission == 1 && item.commission_price > 0\n              ? _vm.t(\"color2rgb\")\n              : null\n          var m19 =\n            _vm.showcommission == 1 && item.commission_price > 0\n              ? _vm.t(\"color2\")\n              : null\n          var m20 =\n            _vm.showcommission == 1 && item.commission_price > 0\n              ? _vm.t(\"佣金\")\n              : null\n          var m21 =\n            _vm.showprice != \"0\" &&\n            (item.price_type != 1 || item.sell_price > 0) &&\n            (!item.show_sellprice ||\n              (item.show_sellprice && item.show_sellprice == true) ||\n              item.usd_sellprice) &&\n            !item.price_color\n              ? _vm.t(\"color1\")\n              : null\n          var m22 =\n            item.xunjia_text &&\n            item.price_type == 1 &&\n            item.sell_price <= 0 &&\n            _vm.showstyle != 1\n              ? _vm.t(\"color1\")\n              : null\n          var m23 =\n            item.xunjia_text &&\n            item.price_type == 1 &&\n            item.sell_price <= 0 &&\n            _vm.showstyle == 1\n              ? _vm.t(\"color1\")\n              : null\n          var m24 =\n            item.xunjia_text &&\n            item.price_type == 1 &&\n            item.sell_price <= 0 &&\n            item.xunjia_text &&\n            item.price_type == 1\n              ? _vm.t(\"color1\")\n              : null\n          var m25 =\n            _vm.params.style == \"2\" && _vm.params.nowbuy == 1\n              ? _vm.t(\"color1rgb\")\n              : null\n          var m26 =\n            _vm.params.style == \"2\" && _vm.params.nowbuy == 1\n              ? _vm.t(\"color1\")\n              : null\n          var m27 =\n            _vm.showcart == 1 &&\n            !item.price_type &&\n            item.hide_cart != true &&\n            !_vm.setCoupon &&\n            _vm.params.style == \"2\" &&\n            _vm.params.nowbuy == 1\n              ? _vm.t(\"color1rgb\")\n              : null\n          var m28 =\n            _vm.showcart == 1 &&\n            !item.price_type &&\n            item.hide_cart != true &&\n            !_vm.setCoupon &&\n            _vm.params.style == \"2\" &&\n            _vm.params.nowbuy == 1\n              ? _vm.t(\"color1\")\n              : null\n          var m29 =\n            _vm.showcart == 1 &&\n            !item.price_type &&\n            item.hide_cart != true &&\n            !_vm.setCoupon &&\n            !(_vm.params.style == \"2\" && _vm.params.nowbuy == 1)\n              ? _vm.t(\"color1rgb\")\n              : null\n          var m30 =\n            _vm.showcart == 1 &&\n            !item.price_type &&\n            item.hide_cart != true &&\n            !_vm.setCoupon &&\n            !(_vm.params.style == \"2\" && _vm.params.nowbuy == 1)\n              ? _vm.t(\"color1\")\n              : null\n          var m31 = !(\n            _vm.showcart == 1 &&\n            !item.price_type &&\n            item.hide_cart != true &&\n            !_vm.setCoupon\n          )\n            ? _vm.t(\"color1\")\n            : null\n          var m32 = !(\n            _vm.showcart == 1 &&\n            !item.price_type &&\n            item.hide_cart != true &&\n            !_vm.setCoupon\n          )\n            ? _vm.t(\"color1rgb\")\n            : null\n          var m33 = item.hongbaoEdu > 0 ? _vm.t(\"color2\") : null\n          var m34 = item.hongbaoEdu > 0 ? _vm.t(\"color2rgb\") : null\n          return {\n            $orig: $orig,\n            m18: m18,\n            m19: m19,\n            m20: m20,\n            m21: m21,\n            m22: m22,\n            m23: m23,\n            m24: m24,\n            m25: m25,\n            m26: m26,\n            m27: m27,\n            m28: m28,\n            m29: m29,\n            m30: m30,\n            m31: m31,\n            m32: m32,\n            m33: m33,\n            m34: m34,\n          }\n        })\n      : null\n  var m35 =\n    _vm.isload && g1 && _vm.showLinkStatus && _vm.lx_tel\n      ? _vm.t(\"color1\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        l0: l0,\n        m9: m9,\n        m10: m10,\n        l1: l1,\n        m13: m13,\n        m14: m14,\n        l2: l2,\n        m17: m17,\n        g1: g1,\n        l3: l3,\n        m35: m35,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dksearch.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dksearch.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"search-container\" :style=\"history_show?'height:100%;':''\">\n\t\t\t<view class=\"topsearch flex-y-center\">\n\t\t\t\t<view class=\"f1 flex-y-center\">\n\t\t\t\t\t<image class=\"img\" :src=\"pre_url+'/static/img/search_ico.png'\"></image>\n\t\t\t\t\t<input :value=\"keyword\" placeholder=\"搜索感兴趣的商品\" placeholder-style=\"font-size:24rpx;color:#C2C2C2\" @confirm=\"searchConfirm\" @input=\"searchChange\" @focus=\"searchFocus\"></input>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"search-history\" v-show=\"history_show\">\n\t\t\t\t<view>\n\t\t\t\t\t<text class=\"search-history-title\">最近搜索</text>\n\t\t\t\t\t<view class=\"delete-search-history\" @tap=\"deleteSearchHistory\">\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/del.png'\" style=\"width:36rpx;height:36rpx\"/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"search-history-list\">\n\t\t\t\t\t<view v-for=\"(item, index) in history_list\" :key=\"index\" class=\"search-history-item\" :data-value=\"item\" @tap=\"historyClick\">{{item}}\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"!history_list || history_list.length==0\" class=\"flex-y-center\"><image :src=\"pre_url+'/static/img/tanhao.png'\" style=\"width:36rpx;height:36rpx;margin-right:10rpx\"/>暂无记录\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"search-navbar\" v-show=\"!history_show\">\n\t\t\t\t<view @tap.stop=\"sortClick\" class=\"search-navbar-item\" :style=\"(!field||field=='sort')?'color:'+t('color1'):''\" data-field=\"sort\" data-order=\"desc\">综合</view>\n\t\t\t\t<view @tap.stop=\"sortClick\" class=\"search-navbar-item\" :style=\"field=='sales'?'color:'+t('color1'):''\" data-field=\"sales\" data-order=\"desc\">销量</view>\n\t\t\t\t<view @tap.stop=\"sortClick\" class=\"search-navbar-item\" data-field=\"sell_price\" :data-order=\"order=='asc'?'desc':'asc'\">\n\t\t\t\t\t<text :style=\"field=='sell_price'?'color:'+t('color1'):''\">价格</text>\n\t\t\t\t\t<text class=\"iconfont iconshangla\" :style=\"field=='sell_price'&&order=='asc'?'color:'+t('color1'):''\"></text>\n\t\t\t\t\t<text class=\"iconfont icondaoxu\" :style=\"field=='sell_price'&&order=='desc'?'color:'+t('color1'):''\"></text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"search-navbar-item flex-x-center flex-y-center\" @click.stop=\"showDrawer('showRight')\">筛选 <text :class=\"'iconfont iconshaixuan ' + (showfilter?'active':'')\"></text></view>\n\t\t\t</view>\n\t\t\t<uni-drawer ref=\"showRight\" mode=\"right\" @change=\"change($event,'showRight')\" :width=\"280\">\n\t\t\t\t<scroll-view scroll-y=\"true\" class=\"filter-scroll-view filter-page\">\n\t\t\t\t\t<view class=\"filter-scroll-view-box\">\n\t\t\t\t\t\t<view class=\"search-filter\">\n\t\t\t\t\t\t\t<view class=\"filter-title\">筛选</view>\n\t\t\t\t\t\t\t<view class=\"filter-content-title\">商品分组</view>\n\t\t\t\t\t\t\t<view class=\"search-filter-content\">\n\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catchegid==''?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"groupClick\" data-gid=\"\">全部</view>\n\t\t\t\t\t\t\t\t<block v-for=\"(item, index) in glist\" :key=\"index\">\n\t\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catchegid==item.id?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"groupClick\" :data-gid=\"item.id\">{{item.name}}</view>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<block v-if=\"!bid || bid <=0\">\n\t\t\t\t\t\t\t<view class=\"filter-content-title\">商品分类</view>\n\t\t\t\t\t\t\t<view class=\"search-filter-content\">\n\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catchecid==oldcid?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"cateClick\" :data-cid=\"oldcid\">全部</view>\n\t\t\t\t\t\t\t\t<block v-for=\"(item, index) in clist\" :key=\"index\">\n\t\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catchecid==item.id?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"cateClick\" :data-cid=\"item.id\">{{item.name}}</view>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t<view class=\"filter-content-title\">商品分类</view>\n\t\t\t\t\t\t\t<view class=\"search-filter-content\">\n\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catchecid2==oldcid2?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"cate2Click\" :data-cid2=\"oldcid2\">全部</view>\n\t\t\t\t\t\t\t\t<block v-for=\"(item, index) in clist2\" :key=\"index\">\n\t\t\t\t\t\t\t\t\t<view class=\"filter-item\" :style=\"catchecid2==item.id?'color:'+t('color1')+';background:rgba('+t('color1rgb')+',0.1)':''\" @tap.stop=\"cate2Click\" :data-cid2=\"item.id\">{{item.name}}</view>\n\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<view class=\"search-filter-btn\">\n\t\t\t\t\t\t\t\t<view class=\"btn\" @tap=\"filterReset\">重置</view>\n\t\t\t\t\t\t\t\t<view class=\"btn2\" :style=\"{background:t('color1')}\" @tap=\"filterConfirm\">确定</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</uni-drawer>\n\n\t\t\t\n\t\t</view>\n\t\t<view class=\"product-container\">\n\t\t\t<block v-if=\"datalist && datalist.length>0\">\n\t\t\t<view style=\"width:100%\">\n\t\t\t\t<view class=\"dp-product-normal-item\">\n\t\t\t\t\t<!-- @click=\"goto\" :data-url=\"'/pages/shop/product?id='+item[idfield]\" -->\n\t\t\t\t\t<view class=\"item\" v-for=\"(item,index) in datalist\" :style=\"'background:'+probgcolor+';'+(showstyle==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (showstyle==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%'))\" :key=\"item.id\">\n\t\t\t\t\t\t<view class=\"product-pic\" >\n\t\t\t\t\t\t\t<image class=\"image\" :src=\"item.pic\" mode=\"widthFix\"/>\n\t\t\t\t\t\t\t<image class=\"saleimg\" :src=\"saleimg\" v-if=\"saleimg!=''\" mode=\"widthFix\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t\t\t<view class=\"p1\" v-if=\"item.name\">{{item.name}}</view>\n\t\t\t\t\t\t\t<!-- 是否显示商家 距离 佣金 S-->\n\t\t\t\t\t\t\t<view class=\"binfo flex-bt\" v-if=\"(showbname=='1' || showbdistance=='1') && item.binfo\">\n\t\t\t\t\t\t\t\t\t<view class=\"flex-y-center b1\">\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"showbname=='1'\">\n\t\t\t\t\t\t\t\t\t\t\t<image :src=\"item.binfo.logo\" class=\"t1\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"t2\">{{item.binfo.name}}</text>\n\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"b2 t2\" v-if=\"showbdistance=='1' && item.binfo.distance\">{{item.binfo.distance}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"couponitem\" v-if=\"showcommission == 1 && item.commission_price>0\">\n\t\t\t\t\t\t\t\t<view class=\"f1\">\n\t\t\t\t\t\t\t\t\t<view class=\"t\" :style=\"{background:'rgba('+t('color2rgb')+',0.1)',color:t('color2')}\">\n\t\t\t\t\t\t\t\t\t\t<text>{{t('佣金')}}{{item.commission_price}}{{item.commission_desc}}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- 是否显示商家 距离 佣金 E-->\n\t\t\t                \n\t\t\t        <view v-if=\"showstyle==2\">\n\t\t\t            <view class=\"field_buy\" v-if=\"params.brand == 1 && item.brand\">\n\t\t\t                <span style=\"width: 80rpx\">品牌：</span>\n\t\t\t                <span>{{item.brand}}</span>\n\t\t\t            </view>\n\t\t\t            <view  class=\"field_buy\" v-if=\"params.barcode == 1 && item.barcode\">\n\t\t\t                <span style=\"width: 80rpx\">货号：</span>\n\t\t\t                <span>{{item.barcode}}</span>\n\t\t\t            </view>\n\t\t\t            <view  class=\"field_buy\" v-if=\"params.guige == 1 && item.ggname\">\n\t\t\t                <span style=\"width: 80rpx\"> 规格：</span>\n\t\t\t                <span>{{item.ggname}}</span>\n\t\t\t            </view>\n\t\t\t            <view  class=\"field_buy\" v-if=\"params.unit == 1 && item.unit\">\n\t\t\t                <span style=\"width: 80rpx\"> 单位：</span>\n\t\t\t                <span>{{item.unit}}</span>\n\t\t\t            </view>\n\t\t\t            <view  class=\"field_buy\" v-if=\"params.ggstock == 1\">\n\t\t\t                <span style=\"width: 80rpx\"> 库存：</span>\n\t\t\t                <span>{{item.ggstock}}</span>\n\t\t\t            </view>\n\t\t\t            <view  class=\"field_buy\" v-if=\"params.valid_time == 1 && item.valid_time\">\n\t\t\t                <span style=\"width: 80rpx\"> 有效期：</span>\n\t\t\t                <span>{{item.valid_time}}</span>\n\t\t\t            </view>\n\t\t\t            <view  class=\"field_buy\" v-if=\"params.remark == 1 && item.remark\">\n\t\t\t                <span style=\"width: 80rpx\"> 备注：</span>\n\t\t\t                <span>{{item.remark}}</span>\n\t\t\t            </view>\n\t\t\t        </view>\n\t\t\t        <view v-if=\"(showstyle=='2' || showstyle=='3') && item.price_type != 1 && item.show_cost == '1'\" :style=\"{color:item.cost_color?item.cost_color:'#999',fontSize:'36rpx'}\"><text style=\"font-size: 24rpx;\">{{item.cost_tag}}</text>{{item.cost_price}}</view>      \n\t\t\t\t\t\t\t<view class=\"p2\">\n\t\t\t\t\t\t\t\t<view class=\"p2-1\" :class=\"params.style=='1'?'flex-bt flex-y-center':''\" v-if=\"showprice != '0' && ( item.price_type != 1 || item.sell_price > 0)\">\n\t\t\t\t\t\t\t\t\t<view v-if=\"showstyle=='1' && item.price_type != 1 && item.show_cost=='1'\" :style=\"{color:item.cost_color?item.cost_color:'#999',fontSize:'36rpx'}\"><text style=\"font-size: 24rpx;\">{{item.cost_tag}}</text>{{item.cost_price}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"flex-y-center\" v-if=\"(!item.show_sellprice || (item.show_sellprice && item.show_sellprice==true)) || item.usd_sellprice\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"t1\" :style=\"{color:item.price_color?item.price_color:t('color1')}\">\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.usd_sellprice\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">$</text>{{item.usd_sellprice}}\n\t\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 28rpx;\"><text style=\"font-size:24rpx\">￥</text>{{item.sell_price}}</text><text style=\"font-size:24rpx\" v-if=\"item.product_unit\">/{{item.product_unit}}</text>\n\t\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size:24rpx\">{{item.price_tag?item.price_tag:'￥'}}</text>{{item.sell_price}}<text style=\"font-size:24rpx\" v-if=\"item.product_unit\">/{{item.product_unit}}</text>\n\t\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"(!item.show_sellprice || (item.show_sellprice && item.show_sellprice==true)) && item.market_price*1 > item.sell_price*1 && showprice == '1'\">￥{{item.market_price}}</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"t3\" v-if=\"item.juli\" style=\"color:#888;\">{{item.juli}}</text> \n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"p2-1\" v-if=\"item.xunjia_text && item.price_type == 1 && item.sell_price <= 0\" style=\"height: 50rpx;line-height: 44rpx;\">\n\t\t\t\t\t\t\t\t\t<text v-if=\"showstyle!=1\" class=\"t1\" :style=\"{color:t('color1'),fontSize:'30rpx'}\">询价</text>\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"showstyle==1\" class=\"t1\" :style=\"{color:t('color1')}\">询价</text>\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.xunjia_text && item.price_type == 1\">\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"lianxi\" :style=\"{background:t('color1')}\" @tap.stop=\"showLinkChange\" :data-lx_name=\"item.lx_name\" :data-lx_bid=\"item.lx_bid\" :data-lx_bname=\"item.lx_bname\" :data-lx_tel=\"item.lx_tel\" data-btntype=\"2\">{{item.xunjia_text?item.xunjia_text:'联系TA'}}</view>\n\t\t\t\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"p1\" v-if=\"item.merchant_name\" style=\"color: #666;font-size: 24rpx;white-space: nowrap;text-overflow: ellipsis;margin-top: 6rpx;height: 30rpx;line-height: 30rpx;font-weight: normal;\"><text>{{item.merchant_name}}</text></view>\n\t\t\t\t\t\t\t<view class=\"p1\" v-if=\"item.main_business\" style=\"color: #666;font-size: 24rpx;margin-top: 4rpx;font-weight: normal;\"><text>{{item.main_business}}</text></view>\n\t\t\t\t\t\t\t<text class=\"p3\" v-if=\"item.product_type == 3\">手工费: ￥{{item.hand_fee?item.hand_fee:0}}</text>\n\t\t\t\t\t\t\t<view class=\"p3\" v-if=\"showsales=='1' && item.sales>0\">已售{{item.sales}}件</view>\n\t\t\t\t\t\t\t<view v-if=\"(showsales !='1' ||  item.sales<=0) && item.main_business\" style=\"height: 44rpx;\"></view>\n\t\t\t        <view v-if=\"params.style=='2' && params.nowbuy == 1\" @click.stop=\"buydialogChange\" data-btntype=\"2\" :data-proid=\"item[idfield]\" class=\"nowbuy\" :style=\"{background:'rgba('+t('color1rgb')+',0.1)',color:t('color1')}\" >\n\t\t\t            立即购买\n\t\t\t        </view>\n\t\t\t\t\t\t\t<view class=\"p4\" :style=\"params.style=='2' && params.nowbuy == 1?'bottom:24rpx;background:rgba('+t('color1rgb')+',0.1);color:'+t('color1'):'background:rgba('+t('color1rgb')+',0.1);color:'+t('color1')\" v-if=\"showcart==1 && !item.price_type && item.hide_cart!=true && !setCoupon\" @click.stop=\"buydialogChange\" data-btntype=\"1\" :data-proid=\"item.id\"><text class=\"iconfont icon_gouwuche\"></text></view>\n\t\t\t\t\t\t\t<view class=\"addbut\" :style=\"{background:'linear-gradient(270deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" v-else @click=\"couponAddChange(item)\">添加</view>\n\t\t\t\t\t\t\t<!-- <view class=\"p4\" :style=\"params.style=='2' && params.nowbuy == 1?'bottom:24rpx;background:rgba('+t('color1rgb')+',0.1);color:'+t('color1'):'background:rgba('+t('color1rgb')+',0.1);color:'+t('color1')\" v-if=\"showcart==2 && !item.price_type && item.hide_cart!=true\" @click.stop=\"buydialogChange\" data-btntype=\"1\" :data-proid=\"item[idfield]\"><image :src=\"cartimg\" class=\"img\"/></text></view> -->\n\t\t\t      </view>\n\t\t\t\t\t\t<view class=\"bg-desc\" v-if=\"item.hongbaoEdu > 0\" :style=\"{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}\">可获额度 +{{item.hongbaoEdu}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<buydialog v-if=\"buydialogShow\" :proid=\"proid\" @buydialogChange=\"buydialogChange\" @addcart=\"afteraddcart\" :menuindex=\"menuindex\" btntype=\"1\" :needaddcart=\"false\"></buydialog>\n\t\t\t    <view class=\"posterDialog linkDialog\" v-if=\"showLinkStatus\">\n\t\t\t    \t<view class=\"main\">\n\t\t\t    \t\t<view class=\"close\" @tap=\"showLinkChange\"><image class=\"img\" :src=\"pre_url+'/static/img/close.png'\"/></view>\n\t\t\t    \t\t<view class=\"content\">\n\t\t\t    \t\t\t<view class=\"title\">{{lx_name}}</view>\n\t\t\t    \t\t\t<view class=\"row\" v-if=\"lx_bid > 0\">\n\t\t\t    \t\t\t\t<view class=\"f1\" style=\"width: 150rpx;\">店铺名称</view>\n\t\t\t    \t\t\t\t<view class=\"f2\" style=\"width: 100%;max-width: 470rpx;display: flex;\" @tap=\"goto\" :data-url=\"'/pagesExt/business/index?id='+lx_bid\">\n\t\t\t    \t\t\t\t  <view style=\"width: 100%;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;\">{{lx_bname}}</view>\n\t\t\t    \t\t\t\t  <view style=\"flex: 1;\"></view>\n\t\t\t    \t\t\t\t  <image :src=\"pre_url+'/static/img/arrowright.png'\" class=\"image\"/>\n\t\t\t    \t\t\t\t</view>\n\t\t\t    \t\t\t</view>\n\t\t\t    \t\t\t<view class=\"row\" v-if=\"lx_tel\">\n\t\t\t    \t\t\t\t<view class=\"f1\" style=\"width: 150rpx;\">联系电话</view>\n\t\t\t    \t\t\t\t<view class=\"f2\" style=\"width: 100%;max-width: 470rpx;\" @tap=\"goto\" :data-url=\"'tel::'+lx_tel\" :style=\"{color:t('color1')}\">{{lx_tel}}<image :src=\"pre_url+'/static/img/copy.png'\" class=\"copyicon\" @tap.stop=\"copy\" :data-text=\"lx_tel\"></image></view>\n\t\t\t    \t\t\t</view>\n\t\t\t    \t\t</view>\n\t\t\t    \t</view>\n\t\t\t    </view>\n\t\t\t</view>\n\t\t\t\n\t\t\t\n\t\t\t\t<!-- <dp-product-item v-if=\"productlisttype=='item2'\" :data=\"datalist\" :menuindex=\"menuindex\"></dp-product-item> -->\n\t\t\t\t<!-- <dp-product-itemlist v-if=\"productlisttype=='itemlist'\" :data=\"datalist\" :menuindex=\"menuindex\"></dp-product-itemlist> -->\n\t\t\t</block>\n\t\t\t<nomore text=\"没有更多商品了\" v-if=\"nomore\"></nomore>\n\t\t\t<nodata text=\"没有查找到相关商品\" v-if=\"nodata\"></nodata>\n\t\t\t<loading v-if=\"loading\"></loading>\n\t\t</view>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<wxxieyi></wxxieyi>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\tpre_url:app.globalData.pre_url,\n\n\t\t\tnomore:false,\n\t\t\tnodata:false,\n      keyword: '',\n      pagenum: 1,\n      datalist: [],\n      history_list: [],\n      history_show: true,\n      order: '',\n\t\t\tfield:'',\n      oldcid: \"\",\n      catchecid: \"\",\n      catchegid: \"\",\n      cid: \"\",\n      gid: '',\n\t\t\tcid2:'',\n      oldcid2: \"\",\n      catchecid2: \"\",\n      clist: [],\n      clist2: [],\n      glist: [],\n      productlisttype: 'item2',\n      showfilter: \"\",\n\t\t\tcpid:0,\n\t\t\tbid:0,\n\t\t\tset:{},\n\t\t\tmendianid:0,\n\t\t\tlatitude:'',\n\t\t\tlongitude:'',\n\t\t\tarea:'',\n\t\t\t\n\t\t\tshowstyle:2,\n\t\t\tshowcommission: 0,\n\t\t\tshowprice:1,\n\t\t\tparams:{},\n\t\t\tshowLinkStatus:false,\n\t\t\tshowprice:1,\n\t\t\tshowcost:0,\n\t\t\tshowsales:1,\n\t\t\tshowcart:1,\n\t\t\tbuydialogShow:false,\n\t\t\tproid:0,\n\t\t\tmid:'',\n\t\t\tsetCoupon:false,\n\t\t\tshowname:1\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tif(opt.coupon == 'false') this.setCoupon = true;\n\t\tthis.oldcid = this.opt.cid || '';\n\t\tthis.catchecid = this.opt.cid;\n\t\tthis.cid = this.opt.cid;\n\t\tthis.cid2 = this.opt.cid2 || '';\n\t\tthis.oldcid2 = this.opt.cid2 || '';\n\t\tthis.catchecid2 = this.opt.cid2;\n\t\tthis.gid = this.opt.gid;\n\t\tthis.cpid = this.opt.cpid || 0;\n\t\tthis.bid = this.opt.bid ? this.opt.bid : 0;\n\t\tthis.mid = this.opt.mid ? this.opt.mid  : '';\n\t\tif(this.opt.keyword) {\n\t\t\tthis.keyword = this.opt.keyword;\n\t\t}\n\t\tif(this.cpid > 0){\n\t\t\tuni.setNavigationBarTitle({\n\t\t\t\ttitle: '可用商品列表'\n\t\t\t});\n\t\t}\n\t\t//读全局缓存的地区信息\n\t\tvar locationCache =  app.getLocationCache();\n\t\tif(locationCache){\n\t\t\tif(locationCache.latitude){\n\t\t\t\tthis.latitude = locationCache.latitude\n\t\t\t\tthis.longitude = locationCache.longitude\n\t\t\t}\n\t\t\tif(locationCache.area){\n\t\t\t\tthis.area = locationCache.area\n\t\t\t}\n\t\t\tif(locationCache.mendian_id){\n\t\t\t\tthis.mendianid = locationCache.mendian_id\n\t\t\t}\n\t\t}\n    var productlisttype = app.getCache('productlisttype');\n    if (productlisttype) this.productlisttype = productlisttype;\n\t\tthis.history_list = app.getCache('search_history_list');\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getprolist();\n    }\n  },\n  methods: {\n\t\tafteraddcart: function (e) {\n\t\t\tthis.addcart({currentTarget:{dataset:e}});\n\t\t},\n\t\tbuydialogChange: function (e) {\n\t\t\tif(!this.buydialogShow){\n\t\t\t\tthis.proid = e.currentTarget.dataset.proid\n\t\t\t}\n\t\t\tthis.buydialogShow = !this.buydialogShow;\n\t\t},\n\t\taddcart:function(e){\n\t\t\tvar that = this;\n\t\t\tvar sell_price = '';\n\t\t\tvar num = e.currentTarget.dataset.num;\n\t\t\tvar proid = e.currentTarget.dataset.proid;\n\t\t\tvar ggid = e.currentTarget.dataset.ggid;\n\t\t\tthat.loading = true;\n\t\t\tapp.post('ApiAdminOrderlr/addcart', {proid: proid,ggid: ggid,num: num,sell_price:sell_price,mid:that.mid}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t\tapp.success(res.msg)\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tapp.goto('dkfastbuy?mid='+that.mid,'reLaunch')\n\t\t\t\t\t},500)\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tgetdata:function(){\n\t\t\tvar that = this;\n\t\t\tthat.pagenum = 1;\n\t\t\tthat.datalist = [];\n\t\t\tvar cid = that.opt.cid;\n\t\t\tvar gid = that.opt.gid;\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\n\t\t\tvar cid2 = that.cid2;\n\t\t\tthat.loading = true;\n\t\t\tlet isCoupon  = '';\n\t\t\tif(that.setCoupon){\n\t\t\t\tisCoupon = 1\n\t\t\t}\n\t\t\tapp.get('ApiShop/prolist', {cid: cid,gid: gid,bid:bid,cid2:cid2,mendian_id:that.mendianid,latitude:that.latitude,longitude:that.longitude,area:that.area,is_coupon:isCoupon}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t  that.clist = res.clist;\n\t\t\t  that.clist2 = res.clist2;\n\t\t\t  that.glist = res.glist;\n\t\t\t\tthat.set = res.set;\n\t\t\t\tthat.loaded();\n\t\t\t\tthat.getprolist();\n\t\t\t});\n\t\t},\n    getprolist: function () {\n      var that = this;\n      var pagenum = that.pagenum;\n      var keyword = that.keyword;\n      var order = that.order;\n      var field = that.field;\n      var gid = that.gid;\n      var cid = that.cid;\n\t\t\tvar cid2 = that.cid2;\n      var cpid = that.cpid;\n      that.history_show = false;\n\t\t\tthat.loading = true;\n\t\t\tthat.nodata = false;\n      that.nomore = false;\n\t\t\tvar bid = that.opt.bid ? that.opt.bid : '';\n\t\t\tlet isCoupon  = '';\n\t\t\tif(that.setCoupon){\n\t\t\t\tisCoupon = 1\n\t\t\t}\n      app.post('ApiShop/getprolist',{pagenum: pagenum,keyword: keyword,field: field,order: order,gid: gid,cid: cid,cid2:cid2,cpid:cpid,bid:bid,mendian_id:that.mendianid,latitude:that.latitude,longitude:that.longitude,area:that.area,order_add_mobile:1,is_coupon:isCoupon}, function (res) {\n\t\t\t\tthat.loading = false;\n        var data = res.data;\n        if (pagenum == 1) {\n          that.datalist = data;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n\t\t// 打开窗口\n\t\tshowDrawer(e) {\n\t\t\tthis.$refs[e].open()\n\t\t},\n\t\t// 关闭窗口\n\t\tcloseDrawer(e) {\n\t\t\tthis.$refs[e].close()\n\t\t},\n\t\t// 抽屉状态发生变化触发\n\t\tchange(e, type) {\n\t\t\tthis[type] = e\n\t\t},\n    searchChange: function (e) {\n      this.keyword = e.detail.value;\n      if (e.detail.value == '') {\n        this.history_show = true;\n        this.datalist = [];\n      }\n    },\n\t\tsearchFocus: function (e) {\n      this.history_show = true;\n    },\n\n    searchConfirm: function (e) {\n      var that = this;\n      var keyword = e.detail.value;\n      that.keyword = keyword\n      that.searchproduct();\n    },\n    searchproduct: function () {\n      var that = this;\n      that.pagenum = 1;\n      that.datalist = [];\n      that.addHistory();\n      that.getprolist();\n    },\n    sortClick: function (e) {\n      var that = this;\n      var t = e.currentTarget.dataset;\n      that.field = t.field;\n      that.order = t.order;\n      that.searchproduct();\n    },\n    groupClick: function (e) {\n      var that = this;\n      var gid = e.currentTarget.dataset.gid;\n\t\t\tif(gid === true) gid = '';\n      that.catchegid = gid\n    },\n    cateClick: function (e) {\n      var that = this;\n      var cid = e.currentTarget.dataset.cid;\n\t\t\tif(cid === true) cid = '';\n      that.catchecid = cid\n    },\n    cate2Click: function (e) {\n      var that = this;\n      var cid2 = e.currentTarget.dataset.cid2;\n\t\t\tif(cid2 === true) cid2 = '';\n      that.catchecid2 = cid2\n    },\n\t\tfilterConfirm(){\n\t\t\tthis.cid = this.catchecid;\n\t\t\tthis.cid2 = this.catchecid2;\n\t\t\tthis.gid = this.catchegid;\n\t\t\tthis.searchproduct();\n\t\t\tthis.$refs['showRight'].close()\n\t\t},\n\t\tfilterReset(){\n\t\t\tthis.catchecid = this.oldcid;\n\t\t\tthis.catchecid2 = this.oldcid2;\n\t\t\tthis.catchegid = '';\n\t\t},\n    filterClick: function () {\n      this.showfilter = !this.showfilter\n    },\n    addHistory: function () {\n      var that = this;\n      var keyword = that.keyword;\n      if (app.isNull(keyword)) return;\n      var historylist = app.getCache('search_history_list');\n      if (app.isNull(historylist)) historylist = [];\n      historylist.unshift(keyword);\n      var newhistorylist = [];\n      for (var i in historylist) {\n        if (historylist[i] != keyword || i == 0) {\n          newhistorylist.push(historylist[i]);\n        }\n      }\n      if (newhistorylist.length > 5) newhistorylist.splice(5, 1);\n      app.setCache('search_history_list', newhistorylist);\n      that.history_list = newhistorylist\n    },\n    historyClick: function (e){\n      var that = this;\n      var keyword = e.currentTarget.dataset.value;\n      if (keyword.length == 0) return;\n      that.keyword = keyword;\n      that.searchproduct();\n    },\n    deleteSearchHistory: function () {\n      var that = this;\n      that.history_list = null;\n      app.removeCache(\"search_history_list\");\n    },\n\t\tcouponAddChange(item){\n\t\t\tuni.$emit('shopDataEmit',{id:item.id,name:item.name,pic:item.pic,give_num:1});\n\t\t\tuni.navigateBack({\n\t\t\t\tdelta: 2\n\t\t\t});\n\t\t}\n  }\n};\n</script>\n<style>\n.addbut{width:88rpx;height:60rpx;border-radius:30rpx;text-align:center;font-size: 24rpx;line-height:60rpx;color: #fff;position:absolute;bottom:6rpx;right:4rpx;}\n.search-container {position: fixed;width: 100%;background: #fff;z-index:9;top:var(--window-top)}\n.topsearch{width:100%;padding:16rpx 20rpx;}\n.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}\n.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}\n.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}\n.topsearch .f1 .camera {height:72rpx;width:72rpx;color: #666;border: 0px;padding: 0px;margin: 0px;background-position: center;background-repeat: no-repeat; background-size:40rpx;}\n\n.search-navbar {display: flex;text-align: center;align-items:center;padding:5rpx 0}\n.search-navbar-item {flex: 1;height: 70rpx;line-height: 70rpx;position: relative;font-size:28rpx;font-weight:bold;color:#323232}\n\n.search-navbar-item .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\n.search-navbar-item .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}\n.search-navbar-item .iconshaixuan{margin-left:10rpx;font-size:22rpx;color:#7d7d7d}\n.search-history {padding: 24rpx 34rpx;}\n.search-history .search-history-title {color: #666;}\n.search-history .delete-search-history {float: right;padding: 15rpx 20rpx;margin-top: -15rpx;}\n.search-history-list {padding: 24rpx 0 0 0;}\n.search-history-list .search-history-item {display: inline-block;height: 50rpx;line-height: 50rpx;padding: 0 20rpx;margin: 0 10rpx 10rpx 0;background: #ddd;border-radius: 10rpx;font-size: 26rpx;}\n\n.filter-page{height: 100%;}\n.filter-scroll-view{margin-top:var(--window-top)}\n.search-filter{display: flex;flex-direction: column;text-align: left;width:100%;flex-wrap:wrap;padding:0;}\n.filter-content-title{color:#999;font-size:28rpx;height:30rpx;line-height:30rpx;padding:0 30rpx;margin-top:30rpx;margin-bottom:10rpx}\n.filter-title{color:#BBBBBB;font-size:32rpx;background:#F8F8F8;padding:60rpx 0 30rpx 20rpx;}\n.search-filter-content{display: flex;flex-wrap:wrap;padding:10rpx 20rpx;}\n.search-filter-content .filter-item{background:#F4F4F4;border-radius:28rpx;color:#2B2B2B;font-weight:bold;margin:10rpx 10rpx;min-width:140rpx;height:56rpx;line-height:56rpx;text-align:center;font-size: 24rpx;padding:0 30rpx}\n.search-filter-content .close{text-align: right;font-size:24rpx;color:#ff4544;width:100%;padding-right:20rpx}\n.search-filter button .icon{margin-top:6rpx;height:54rpx;}\n.search-filter-btn{display:flex;padding:30rpx 30rpx 50rpx 30rpx;justify-content: space-between}\n.search-filter-btn .btn{width:240rpx;height:66rpx;line-height:66rpx;background:#fff;border:1px solid #e5e5e5;border-radius:33rpx;color:#2B2B2B;font-weight:bold;font-size:24rpx;text-align:center}\n.search-filter-btn .btn2{width:240rpx;height:66rpx;line-height:66rpx;border-radius:33rpx;color:#fff;font-weight:bold;font-size:24rpx;text-align:center}\n\n.product-container {width: 100%;margin-top: 190rpx;font-size:26rpx;padding:0 24rpx}\n.dp-product-normal-item{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}\n.dp-product-normal-item .item{display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:10rpx;overflow:hidden;}\n.dp-product-normal-item .product-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 100%;position: relative;}\n.dp-product-normal-item .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}\n.dp-product-normal-item .product-pic .saleimg{ position: absolute;width: 60px;height: auto; top: -3px; left:-3px;}\n.dp-product-normal-item .product-info {padding:20rpx 20rpx;position: relative;}\n.dp-product-normal-item .product-info .p1 {color:#323232;font-weight:bold;font-size:28rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}\n.dp-product-normal-item .product-info .p2{display:flex;align-items:center;overflow:hidden;padding:2px 0}\n.dp-product-normal-item .product-info .p2-1{flex-grow:1;flex-shrink:1;height:40rpx;line-height:40rpx;overflow:hidden;white-space: nowrap}\n.dp-product-normal-item .product-info .p2-1 .t1{font-size:36rpx;}\n.dp-product-normal-item .product-info .p2-1 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}\n.dp-product-normal-item .product-info .p2-1 .t3 {margin-left:10rpx;font-size:22rpx;color: #999;}\n.dp-product-normal-item .product-info .p2-2{font-size:20rpx;height:40rpx;line-height:40rpx;text-align:right;padding-left:20rpx;color:#999}\n.dp-product-normal-item .product-info .p3{color:#999999;font-size:20rpx;margin-top:10rpx}\n.dp-product-normal-item .product-info .p4{width:52rpx;height:52rpx;border-radius:50%;position:absolute;display:relative;bottom:16rpx;right:20rpx;text-align:center;}\n.dp-product-normal-item .product-info .p4 .icon_gouwuche{font-size:30rpx;height:52rpx;line-height:52rpx}\n.dp-product-normal-item .product-info .p4 .img{width:100%;height:100%}\n.bg-desc {color: #fff; padding: 10rpx 20rpx;}\n\n.dp-product-normal-item .product-info .binfo {padding-bottom:6rpx;display: flex;align-items: center;min-width: 0;}\n.dp-product-normal-item .product-info .binfo .t1 {width: 30rpx;\theight: 30rpx;border-radius: 50%;margin-right: 10rpx;flex-shrink: 0;}\n.dp-product-normal-item .product-info .binfo .t2 {color: #666;font-size: 24rpx;font-weight: normal;\toverflow: hidden;text-overflow: ellipsis;\twhite-space: nowrap;}\n.dp-product-normal-item .product-info .binfo .b2{flex-shrink: 0;}\n.dp-product-normal-item .product-info .binfo .b1{max-width: 75%;}\n.dp-product-normal-item .couponitem {width: 100%;\t/* padding: 0 20rpx 20rpx 20rpx; */font-size: 24rpx;color: #333;display: flex;align-items: center;}\n.dp-product-normal-item .couponitem .f1 {flex: 1;\tdisplay: flex;flex-wrap: nowrap;overflow: hidden}\n.dp-product-normal-item .couponitem .f1 .t {margin-right: 10rpx;border-radius: 3px;font-size: 22rpx;height: 40rpx;line-height: 40rpx;padding-right: 10rpx;flex-shrink: 0;overflow: hidden}\n.lianxi{color: #fff;border-radius: 50rpx 50rpx;line-height: 50rpx;text-align: center;font-size: 22rpx;padding: 0 14rpx;display: inline-block;float: right;}\n.field_buy{line-height: 40rpx;border-bottom: 0;padding: 4rpx 0;word-break: break-all;}\n.nowbuy{width:160rpx;line-height:60rpx;text-align: center;border-radius: 4rpx;margin-top: 10rpx;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dksearch.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dksearch.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213040877\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}