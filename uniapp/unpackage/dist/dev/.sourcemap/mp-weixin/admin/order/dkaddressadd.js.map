{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/dkaddressadd.vue?50d7", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/dkaddressadd.vue?23ce", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/dkaddressadd.vue?b431", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/dkaddressadd.vue?6151", "uni-app:///admin/order/dkaddressadd.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/dkaddressadd.vue?9e69", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/order/dkaddressadd.vue?6ae9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "name", "tel", "area", "address", "longitude", "latitude", "regiondata", "type", "addressxx", "company", "items", "showCompany", "onLoad", "app", "url", "uni", "method", "header", "success", "that", "onPullDownRefresh", "methods", "getdata", "id", "regionchange", "console", "<PERSON><PERSON><PERSON><PERSON>", "fail", "formSubmit", "<PERSON><PERSON><PERSON><PERSON>", "addressid", "setTimeout", "bindPickerChange", "setaddressxx", "shibie", "isrs", "getweixinaddress"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yRAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpEA;AAAA;AAAA;AAAA;AAA00B,CAAgB,0yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoD91B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;IAEAC;MACA;MACA;QACAC;MACA;MACAC;QACAD;QACAnB;QACAqB;QACAC;UAAA;QAAA;QACAC;UACAC;QACA;MACA;IACA;IAEA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAT;QACA;UACAM;QACA;MACA;MACA;MACAA;MACAN;QAAAU;QAAAhB;MAAA;QACAY;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACA;QACA;UACA;QACA;QACAA;QACAA;MACA;MACA;MACA;MACA;IACA;IACAK;MACA;MACAC;MACA;IACA;IACAC;MACAD;MACA;MACAV;QACAG;UACAO;UACAN;UACAA;UACAA;UACAA;QACA;QACAQ;UACAF;UACA;YACA;YACAZ;cACAE;YACA;UACA;QACA;MACA;IACA;IACAa;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;UACAf;UACA;QACA;MACA;QACA;QACA;UACAA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;MACA;QACAV;QACAH;QACAC;QACAK;QACAC;MACA;MACAQ;MACAF;IACA;IACAgB;MACA;MACA;MACAhB;QACAA;QACAA;UAAAiB;QAAA;UACAjB;UACAA;UACAkB;YACAlB;UACA;QACA;MACA;IACA;IACAmB;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACArB;QAAAL;MAAA;QACA;QACA;UACA2B;UACAhB;QACA;QACA;UACAgB;UACAhB;QACA;QACA;UACAgB;UACAhB;QACA;QACA;UACAgB;UACAhB;QACA;QACA;UACAN;QACA;UACAA;QACA;MACA;IACA;IACAuB;MACA;MACA9C;QACA4B;UACAL;UACAA;YAAAN;YAAAuB;YAAA9B;YAAAC;YAAAC;YAAAC;UAAA;YACAU;YACA;cACAA;cACA;YACA;YACAA;YACAkB;cACAlB;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9QA;AAAA;AAAA;AAAA;AAAurC,CAAgB,umCAAG,EAAC,C;;;;;;;;;;;ACA3sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/order/dkaddressadd.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/order/dkaddressadd.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./dkaddressadd.vue?vue&type=template&id=0aa646e3&\"\nvar renderjs\nimport script from \"./dkaddressadd.vue?vue&type=script&lang=js&\"\nexport * from \"./dkaddressadd.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dkaddressadd.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/order/dkaddressadd.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dkaddressadd.vue?vue&type=template&id=0aa646e3&\"", "var components\ntry {\n  components = {\n    uniDataPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-data-picker/uni-data-picker\" */ \"@/components/uni-data-picker/uni-data-picker.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"详细地址\") : null\n  var m1 = _vm.isload ? _vm.t(\"详细地址\") : null\n  var m2 = _vm.isload ? _vm.t(\"color1\") : null\n  var m3 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dkaddressadd.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dkaddressadd.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<form @submit=\"formSubmit\">\n\t\t\t<view class=\"form\">\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"label\">姓 名</text>\n\t\t\t\t\t<input class=\"input\" type=\"text\" placeholder=\"请输入姓名\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"name\" :value=\"name\"></input>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-item\" v-if=\"showCompany\">\n\t\t\t\t\t<text class=\"label\">公 司</text>\n\t\t\t\t\t<input class=\"input\" type=\"text\" placeholder=\"请输入公司或单位名称\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"company\" :value=\"company\"></input>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"label\">手机号</text>\n\t\t\t\t\t<input class=\"input\" type=\"number\" placeholder=\"请输入手机号\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"tel\" :value=\"tel\"></input>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-item\" v-if=\"type==1\">\n\t\t\t\t\t<text class=\"label flex0\">选择位置</text>\n\t\t\t\t\t<text class=\"flex1\" style=\"text-align:right\" :style=\"area ? '' : 'color:#BBBBBB'\" @tap=\"selectzuobiao\" >{{area ? area : '请选择您的位置'}}</text>\n\t\t\t\t\t<!-- <input class=\"input\" type=\"text\" placeholder=\"请选择您的位置\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"area\" :value=\"area\" @tap=\"selectzuobiao\"></input> -->\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-item\" v-else>\n\t\t\t\t\t<text class=\"label flex1\">所在地区</text>\n\t\t\t\t\t<uni-data-picker :localdata=\"items\" :border=\"false\" :placeholder=\"regiondata || '请选择省市区'\" @change=\"regionchange\"></uni-data-picker>\n\t\t\t\t\t<!-- <picker mode=\"region\" name=\"regiondata\" :value=\"regiondata\" class=\"input\" @change=\"bindPickerChange\">\n\t\t\t\t\t\t<view class=\"picker\" v-if=\"regiondata\">{{regiondata}}</view>\n\t\t\t\t\t\t<view v-else>请选择地区</view>\n\t\t\t\t\t</picker> -->\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"label\">{{t('详细地址')}}</text>\n\t\t\t\t\t<input class=\"input\" type=\"text\" :placeholder=\"'请输入'+t('详细地址')\" placeholder-style=\"font-size:28rpx;color:#BBBBBB\" name=\"address\" :value=\"address\"></input>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"item flex-y-center\" v-if=\"type!=1\">\n\t\t\t\t\t<view class=\"f2 flex-y-center flex1\">\n\t\t\t\t\t\t<input id=\"addressxx\" placeholder=\"粘贴地址信息，可自动识别并填写，如：张三，188********，广东省 东莞市 xx区 xx街道 xxxx\" placeholder-style=\"font-size:24rpx;color:#BBBBBB\" style=\"width:85%;font-size:24rpx;margin:20rpx 0;height:100rpx;padding:4rpx 10rpx\" @input=\"setaddressxx\"></input>\n\t\t\t\t\t\t<view style=\"width:15%;text-align:center;color:#999\" @tap=\"shibie\">识别</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<button class=\"savebtn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\" form-type=\"submit\">保 存</button>\n\t\t</form>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<wxxieyi></wxxieyi>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t  opt:{},\n\t  loading:false,\n\t  isload: false,\n\t  menuindex:-1,\n      name: '',\n      tel: '',\n      area: '',\n      address: '',\n      longitude: '',\n      latitude: '',\n      regiondata: '',\n      type: 0,\n      addressxx: '',\n      company: '',\n\t\t\titems:[],\n\t\t\tshowCompany:false,\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n    this.type = this.opt.type || 0;\n\t\tvar that = this;\n\t\t\n\t\tapp.get('ApiIndex/getCustom',{}, function (customs) {\n\t\t\tvar url = app.globalData.pre_url+'/static/area.json';\n\t\t\tif(customs.data.includes('plug_zhiming')) {\n\t\t\t\turl = app.globalData.pre_url+'/static/area_gaoxin.json';\n\t\t\t}\n\t\t\tuni.request({\n\t\t\t\turl: url,\n\t\t\t\tdata: {},\n\t\t\t\tmethod: 'GET',\n\t\t\t\theader: { 'content-type': 'application/json' },\n\t\t\t\tsuccess: function(res2) {\n\t\t\t\t\tthat.items = res2.data\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t\t\n    this.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata:function(){\n\t\t\tvar that = this;\n\t\t\tvar addressId = that.opt.id || '';\n\t\t\tapp.get('ApiIndex/getCustom',{}, function (customs) {\n\t\t\t\tif(customs.data.includes('plug_xiongmao')) {\n\t\t\t\t\tthat.showCompany = true;\n\t\t\t\t}\n\t\t\t});\n\t\t\t// if(addressId) {\n\t\t\t\tthat.loading = true;\n\t\t\t\tapp.get('ApiAddress/addressadd', {id: addressId,type: that.type}, function (res) {\n\t\t\t\t\tthat.loading = false;\n\t\t\t\t\tthat.name = res.data.name;\n\t\t\t\t\tthat.tel = res.data.tel;\n\t\t\t\t\tthat.area = res.data.area;\n\t\t\t\t\tthat.address = res.data.address;\n\t\t\t\t\tthat.longitude = res.data.longitude;\n\t\t\t\t\tthat.latitude = res.data.latitude;\n\t\t\t\t\tthat.company = res.data.company;\n\t\t\t\t\tif (res.data.province){\n\t\t\t\t\t\tvar regiondata = res.data.province+ ',' + res.data.city+ ',' + res.data.district;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tvar regiondata = '';\n\t\t\t\t\t}\n\t\t\t\t\tthat.regiondata = regiondata\n\t\t\t\t\tthat.loaded();\n\t\t\t\t});\n\t\t\t// }else{\n\t\t\t// \tthat.loaded();\n\t\t\t// }\n\t\t},\n\t\tregionchange(e) {\n\t\t\tconst value = e.detail.value\n\t\t\tconsole.log(value[0].text + ',' + value[1].text + ',' + value[2].text);\n\t\t\tthis.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text\n\t\t},\n    selectzuobiao: function () {\n\t\t\tconsole.log('selectzuobiao')\n      var that = this;\n      uni.chooseLocation({\n        success: function (res) {\n          console.log(res);\n          that.area = res.address;\n          that.address = res.name;\n          that.latitude = res.latitude;\n          that.longitude = res.longitude;\n        },\n        fail: function (res) {\n\t\t\t\t\tconsole.log(res)\n          if (res.errMsg == 'chooseLocation:fail auth deny') {\n            //$.error('获取位置失败，请在设置中开启位置信息');\n            app.confirm('获取位置失败，请在设置中开启位置信息', function () {\n              uni.openSetting({});\n            });\n          }\n        }\n      });\n    },\n    formSubmit: function (e) {\n      var that = this;\n      var formdata = e.detail.value;\n      var addressId = that.opt.id || '';\n      var name = formdata.name;\n      var tel = formdata.tel;\n      var regiondata = that.regiondata;\n      if (that.type == 1) {\n        var area = that.area;\n\t\t\t\tif(area == '') {\n\t\t\t\t\tapp.error('请选择位置');\n\t\t\t\t\treturn;\n\t\t\t\t}\n      } else {\n        var area = regiondata;\n\t\t\t\tif(area == '') {\n\t\t\t\t\tapp.error('请选择省市区');\n\t\t\t\t\treturn;\n\t\t\t\t}\n      }\n      var address = formdata.address;\n      var longitude = that.longitude;\n      var latitude = that.latitude;\n      var company = formdata.company;\n      if (name == '' || tel == '' || address == '') {\n        app.error('请填写完整信息');\n        return;\n      }\r\n\t\t\tlet reg1 = new RegExp(',','g')\r\n\t\t\tlet areaText = regiondata.replace(reg1,'/');\r\n\t\t\tlet resData = {\r\n\t\t\t\taddress:address,\r\n\t\t\t\tname:name,\r\n\t\t\t\ttel:tel,\r\n\t\t\t\tregiondata:areaText,\r\n\t\t\t\ttype:2\r\n\t\t\t}\r\n\t\t\tuni.$emit('dkPageOn',resData);\r\n\t\t\tapp.goback(true);\n    },\n    delAddress: function () {\n      var that = this;\n      var addressId = that.opt.id;\n      app.confirm('确定要删除该收获地址吗?', function () {\n\t\t\t\tapp.showLoading('删除中');\n        app.post('ApiAddress/del', {addressid: addressId}, function () {\n\t\t\t\t\tapp.showLoading(false);\n          app.success('删除成功');\n          setTimeout(function () {\n            app.goback(true);\n          }, 1000);\n        });\n      });\n    },\n    bindPickerChange: function (e) {\n      var val = e.detail.value;\n      this.regiondata = val;\n    },\n    setaddressxx: function (e) {\n      this.addressxx = e.detail.value;\n    },\n    shibie: function () {\n      var that = this;\n      var addressxx = that.addressxx;\n      app.post('ApiAddress/shibie', {addressxx: addressxx}, function (res) {\n        var isrs = 0;\n        if (res.province) {\n          isrs = 1;\n          that.regiondata = res.province + ',' +res.city + ',' +res.county\n        }\n        if (res.detail) {\n          isrs = 1;\n          that.address = res.detail\n        }\n        if (res.person) {\n          isrs = 1;\n          that.name = res.person\n        }\n        if (res.phonenum) {\n          isrs = 1;\n          that.tel = res.phonenum\n        }\n        if (isrs == 0) {\n          app.error('识别失败');\n        } else {\n          app.success('识别完成');\n        }\n      });\n    },\n\t\tgetweixinaddress:function(){\n      var that = this;\n\t\t\twx.chooseAddress({\n\t\t\t\tsuccess (res) {\n\t\t\t\t\tapp.showLoading('提交中');\n\t\t\t\t\tapp.post('ApiAddress/addressadd', {type: that.type,addressid: '',name: res.userName,tel: res.telNumber,area: res.provinceName+','+res.cityName+','+res.countyName,address: res.detailInfo}, function (res) {\n\t\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\t\tif (res.status == 0) {\n\t\t\t\t\t\t\tapp.alert(res.msg);\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tapp.success('添加成功');\n\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\tapp.goback(true);\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t})\n\t\t}\n  }\n};\n</script>\n<style>\n.container{display:flex;flex-direction:column}\n\n.addfromwx{width:94%;margin:20rpx 3% 0 3%;border-radius:5px;padding:20rpx 3%;background: #FFF;display:flex;align-items:center;color:#666;font-size:28rpx;}\n.addfromwx .img{width:40rpx;height:40rpx;margin-right:20rpx;}\n.form{ width:94%;margin:20rpx 3%;border-radius:5px;padding: 0 3%;background: #FFF;}\n.form-item{display:flex;align-items:center;width:100%;border-bottom: 1px #ededed solid;height:98rpx;}\n.form-item:last-child{border:0}\n.form-item .label{ color:#8B8B8B;font-weight:bold;height: 60rpx; line-height: 60rpx; text-align:left;width:160rpx;padding-right:20rpx}\n.form-item .input{ flex:1;height: 60rpx; line-height: 60rpx;text-align:right}\n\n.savebtn{ width: 90%; height:96rpx; line-height: 96rpx; text-align:center;border-radius:48rpx; color: #fff;font-weight:bold;margin: 0 5%; margin-top:60rpx; border: none; }\n\n\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dkaddressadd.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dkaddressadd.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213040936\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}