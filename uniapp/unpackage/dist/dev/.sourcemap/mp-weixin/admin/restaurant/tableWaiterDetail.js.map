{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/tableWaiterDetail.vue?2985", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/tableWaiterDetail.vue?5c73", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/tableWaiterDetail.vue?ec51", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/tableWaiterDetail.vue?2501", "uni-app:///admin/restaurant/tableWaiterDetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/tableWaiterDetail.vue?ab76", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/tableWaiterDetail.vue?7163"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "pre_url", "detail", "order", "orderGoods", "business", "nindex", "orderGoodsSum", "numArr", "onLoad", "onShow", "onPullDownRefresh", "methods", "getdata", "that", "app", "id", "subform", "info", "clean", "tableId", "cleanOver", "close", "numChange", "startpause", "tableid", "type"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,0BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACqE;AACL;AACa;;;AAG7E;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/EA;AAAA;AAAA;AAAA;AAA+0B,CAAgB,+yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6Kn2B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAC;YACAA;UACA;UAAA;QACA;QACAD;QACAA;QACAA;QACAA;QAEAA;QACA;MACA;IACA;;IACAG;MACA;MACA;MACAC;MACAA;MACA;MACA;QACAH;QACA;MACA;MACA;QACAA;QACA;MACA;MACAD;MACAC;QAAAG;MAAA;QACAJ;QACA;UACAC;UAAA;QACA;QACAA;UACAD;QACA;MACA;IACA;IACAK;MACA;MACA;MACAL;MACAC;QAAAK;MAAA;QACAN;QACA;UACAC;UAAA;QACA;QACAA;UACAD;QACA;MACA;IACA;IACAO;MACA;MACA;MACAP;MACAC;QAAAK;MAAA;QACAN;QACA;UACAC;UAAA;QACA;QACAA;UACAD;QACA;MACA;IACA;IACAQ;MACA;MACA;MACAP;QACAD;QACAC;UAAAK;QAAA;UACAN;UACA;YACAC;YAAA;UACA;UACA;YACAA;YACAD;UACA;QACA;MACA;IACA;IACAS;MACA;IACA;IACAC;MACA;MACA;MACA;MACAV;MACAC;QAAAU;QAAAC;MAAA;QACAZ;QACA;UACAC;UAAA;QACA;QACA;UACAA;UACAD;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACvTA;AAAA;AAAA;AAAA;AAA4rC,CAAgB,4mCAAG,EAAC,C;;;;;;;;;;;ACAhtC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/restaurant/tableWaiterDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/restaurant/tableWaiterDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./tableWaiterDetail.vue?vue&type=template&id=32798c86&\"\nvar renderjs\nimport script from \"./tableWaiterDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./tableWaiterDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tableWaiterDetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/restaurant/tableWaiterDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tableWaiterDetail.vue?vue&type=template&id=32798c86&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isload && _vm.detail.status == 2\n      ? _vm.dateFormat(_vm.order.createtime)\n      : null\n  var l0 =\n    _vm.isload &&\n    _vm.detail.status == 2 &&\n    _vm.detail.timing_fee_type &&\n    _vm.detail.timing_fee_type > 0\n      ? _vm.__map(_vm.detail.timing_log, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g0 = _vm.detail.timing_log.length\n          return {\n            $orig: $orig,\n            g0: g0,\n          }\n        })\n      : null\n  var g1 = _vm.isload && _vm.detail.status == 2 ? _vm.orderGoods.length : null\n  var l1 =\n    _vm.isload && _vm.detail.status == 2 && g1 > 0\n      ? _vm.__map(_vm.orderGoods, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g2 = item.ggtext && item.ggtext.length\n          return {\n            $orig: $orig,\n            g2: g2,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        l0: l0,\n        g1: g1,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tableWaiterDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tableWaiterDetail.vue?vue&type=script&lang=js&\"", "<template><!-- todo 展示关联的预约信息 -->\r\n\t<view class=\"container\">\r\n\t\t<block v-if=\"isload\">\r\n\t\t\t<view class=\"head-bg\">\r\n\t\t\t\t<h1 class=\"text-center\">{{detail.name}}</h1>\r\n\t\t\t\t<h2 class=\"title flex\" v-if=\"detail.status == 2 && order.status != 3\"><image :src=\"pre_url+'/static/img/admin/fork.png'\" class=\"image\"></image><view>用餐中</view></h2>\r\n\t\t\t\t<h2 class=\"title flex\" v-else-if=\"detail.status == 0\"><image :src=\"pre_url+'/static/img/admin/fork.png'\" class=\"image\"></image><view>空闲中</view></h2>\r\n\t\t\t\t<h2 class=\"title flex\" v-else-if=\"order.status == 3\"><image :src=\"pre_url+'/static/img/admin/fork.png'\" class=\"image\"></image><view>已结算，待清台</view></h2>\r\n\t\t\t\t<h2 class=\"title flex\" v-else-if=\"detail.status == 3\"><image :src=\"pre_url+'/static/img/admin/fork.png'\" class=\"image\"></image><view>清台中</view></h2>\r\n\t\t\t\t<!-- <view class=\"text-center mt20\">请在预定时间20分钟内到店。</view> -->\r\n\t\t\t</view>\r\n\t\t\t<form @submit=\"subform\">\r\n\t\t\t<view class=\"card-view\" v-if=\"detail.status == 0\">\r\n\t\t\t\t<view class=\"card-wrap\">\r\n\t\t\t\t\t<view class=\"card-title\">餐桌信息</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<view class=\"t1\">桌台</view>\r\n\t\t\t\t\t\t<view class=\"t2\">{{detail.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<view class=\"t1\">座位数</view>\r\n\t\t\t\t\t\t<view class=\"t2\">{{detail.seat}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-wrap\">\r\n\t\t\t\t\t<view class=\"card-title \">用餐信息</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<view class=\"t1\">人数</view>\r\n\t\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t\t<picker @change=\"numChange\" :value=\"nindex\" :range=\"numArr\" name=\"renshu\">\r\n\t\t\t\t\t\t\t\t<view class=\"picker\">{{numArr[nindex]}}</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<view class=\"t1\">顾客姓名</view>\r\n\t\t\t\t\t\t<view class=\"t2\"><input type=\"text\" data-name=\"linkman\" name=\"linkman\" placeholder=\"选填\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<view class=\"t1\">手机号</view>\r\n\t\t\t\t\t\t<view class=\"t2\"><input type=\"number\" data-name=\"tel\" name=\"tel\" placeholder=\"选填\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item info-textarea\">\r\n\t\t\t\t\t\t<view class=\" remark\" >\r\n\t\t\t\t\t\t\t<view class=\"t1\">备注</view>\r\n\t\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t<textarea data-name=\"message\" name=\"message\" placeholder=\"如您有其他需求请填写\" placeholder-style=\"color:#ABABABFF\"></textarea>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"btn-view button-sp-area\">\r\n\t\t\t\t<!-- <button type=\"default\" class=\"btn-default\">取消</button> -->\r\n\t\t\t\t<button type=\"primary\" form-type=\"submit\" v-if=\"detail.status == 0\">开始用餐</button>\r\n\t\t\t</view>\r\n\t\t\t</form>\r\n\t\t\t\r\n\t\t\t<view class=\"card-view\" v-if=\"detail.status == 2\">\r\n\t\t\t\t<view class=\"card-wrap\">\r\n\t\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t\t <view class=\"item\" v-if=\"orderGoodsSum == 0 && order.status != 3\" @tap=\"goto\" :data-url=\"'/restaurant/shop/index?type=admin&tableId='+detail.id + '&bid=' + detail.bid\">\r\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/admin/dish.png'\" class=\"image\"/>\r\n\t\t\t\t\t\t\t\t<text class=\"t3\">点餐</text>\r\n\t\t\t\t\t\t </view>\r\n\t\t\t\t\t\t <view class=\"item\" v-if=\"orderGoodsSum > 0 && order.status != 3\" @tap=\"goto\" :data-url=\"'/restaurant/shop/index?type=admin&tableId='+detail.id + '&bid=' + detail.bid\">\r\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/admin/dish.png'\" class=\"image\"/>\r\n\t\t\t\t\t\t\t\t<text class=\"t3\">加菜</text>\r\n\t\t\t\t\t\t </view>\r\n\t\t\t\t\t\t <view class=\"item\" v-if=\"orderGoodsSum > 0 && order.status != 3\" @tap=\"goto\" :data-url=\"'tableWaiterPay?id='+detail.id\">\r\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/admin/money.png'\" class=\"image\"/>\r\n\t\t\t\t\t\t\t\t<text class=\"t3\">结算</text>\r\n\t\t\t\t\t\t </view>\r\n\t\t\t\t\t\t <view class=\"item\" v-if=\"order.status != 3\" @tap=\"goto\" :data-url=\"'tableWaiter?operate=change&origin='+detail.id\">\r\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/admin/change.png'\" class=\"image\"/>\r\n\t\t\t\t\t\t\t\t<text class=\"t3\">换桌</text>\r\n\t\t\t\t\t\t </view>\r\n\t\t\t\t\t\t <view class=\"item\" @tap=\"clean\">\r\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/admin/clean.png'\" class=\"image\"/>\r\n\t\t\t\t\t\t\t\t<text class=\"t3\">清台</text>\r\n\t\t\t\t\t\t </view>\r\n\t\t\t\t\t\t <view class=\"item\" v-if=\"order.status != 3\" @tap=\"close\">\r\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/admin/close.png'\" class=\"image\"/>\r\n\t\t\t\t\t\t\t\t<text class=\"t3\">关闭</text>\r\n\t\t\t\t\t\t </view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-wrap\">\r\n\t\t\t\t\t<view class=\"card-title\">餐桌信息</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<view class=\"t1\">桌台</view>\r\n\t\t\t\t\t\t<view class=\"t2\">{{detail.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<view class=\"t1\">人数/座位数</view>\r\n\t\t\t\t\t\t<view class=\"t2\">{{order.renshu}}/{{detail.seat}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<view class=\"t1\">客户信息</view>\r\n\t\t\t\t\t\t<view class=\"t2\">{{order.linkman}} {{order.tel}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<view class=\"t1\">时间</view>\r\n\t\t\t\t\t\t<view class=\"t2\">{{dateFormat(order.createtime)}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item info-textarea\">\r\n\t\t\t\t\t\t<view class=\"t1\">备注信息</view>\r\n\t\t\t\t\t\t<view class=\"t2\">{{order.message}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-wrap\" v-if=\"detail.timing_fee_type && detail.timing_fee_type > 0\">\r\n\t\t\t\t\t<view class=\"card-title\">计时收费</view>\r\n\t\t\t\t\t<image v-if=\"detail.is_start==0\" class=\"img\" :src=\"pre_url+'/static/img/admin/start.png'\" @tap=\"startpause\" :data-status='1'></image>\r\n\t\t\t\t\t<image v-else class=\"img\" :src=\"pre_url+'/static/img/admin/pause.png'\" @tap=\"startpause\" :data-status='0'></image>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"info-item\" style=\"justify-content: center;\" v-if=\"detail.timing_log.length > 0\" v-for=\"(item,index) in detail.timing_log \">\r\n\t\t\t\t\t\t<view class=\"t1\">{{item.start_time}} ~ {{item.end_time}}</view>\r\n\t\t\t\t\t\t<view class=\"t2\" style=\"flex: 0.3\">{{item.num}} 分钟</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item info-textarea\">\r\n\t\t\t\t\t\t<view class=\"t1\">{{detail.timing_fee_text}}</view>\r\n\t\t\t\t\t\t<view class=\"t2\">￥{{detail.timing_money}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"card-wrap card-goods\" v-if=\"orderGoods.length > 0\">\r\n\t\t\t\t\t<view class=\"flex\">\r\n\t\t\t\t\t\t<view class=\"card-title\">已点菜品({{orderGoodsSum}})</view>\r\n\t\t\t\t\t\t<view class=\"flex1\"></view>\r\n\t\t\t\t\t\t<view class=\"btn-text\" @tap=\"goto\" :data-url=\"'shoporderEdit?id='+order.id\">修改菜品</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\" v-for=\"(item,index) in orderGoods\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"t1\">\r\n\t\t\t\t\t\t\t<view style=\"line-height: 60rpx;\">{{item.name}}<text v-if=\"item.ggname\">[{{item.ggname}}{{item.jltitle?item.jltitle:''}}]</text></view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view v-if=\"(item.ggtext && item.ggtext.length)\" class=\"flex-col\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(item2,index) in item.ggtext\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"ggtext\" >{{item2}}</text>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"t2\">x \r\n\t\t\t\t\t\t\t<text >{{item.num}}<text style=\"font-size: 20rpx;\"v-if=\"item.product_type && item.product_type ==1\">斤</text></text> \r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"t3\">￥{{item.real_totalprice}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<view class=\"t1\">合计</view>\r\n\t\t\t\t\t\t<view class=\"t2\" >x{{orderGoodsSum}}</view>\r\n\t\t\t\t\t\t<view class=\"t3\">￥{{order.totalprice}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-wrap\" v-else>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<view class=\"t1\">还未点菜</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"btn-view button-sp-area mb\">\r\n\t\t\t\t<button type=\"primary\" @tap=\"cleanOver\" v-if=\"detail.status == 3\">清理完成</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"btn-view button-sp-area mb\">\r\n\t\t\t\t<button type=\"default\" class=\"btn-default\" @tap=\"goto\" data-url=\"tableWaiter\">返回餐桌列表</button>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t</block>\r\n\t\t<loading v-if=\"loading\"></loading>\r\n\t\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt:{},\r\n\t\t\t\tloading:false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\t\r\n\t\t\t\tdetail:{},\r\n\t\t\t\torder:{},\r\n\t\t\t\torderGoods:[],\r\n\t\t\t\tbusiness:{},\r\n\t\t\t\tnindex:0,\r\n\t\t\t\torderGoodsSum:0,\r\n\t\t\t\tnumArr:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20],\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad: function (opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonShow:function(){\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tonPullDownRefresh: function () {\r\n\t\t\tthis.getdata();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetdata: function () {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.get('ApiAdminRestaurantTable/detail', {id:that.opt.id}, function (res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif(res.status == 0){\r\n\t\t\t\t\t\tapp.alert(res.msg,function(){\r\n\t\t\t\t\t\t\tapp.goback();\r\n\t\t\t\t\t\t});return;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.detail = res.info;\r\n\t\t\t\t\tthat.order = res.order;\r\n\t\t\t\t\tthat.orderGoods = res.order_goods;\r\n\t\t\t\t\tthat.orderGoodsSum = res.order_goods_sum;\r\n\t\t\t\t\t\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t\t//\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsubform: function (e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar info = e.detail.value;\r\n\t\t\t\tinfo.tableId = that.opt.id;\r\n\t\t\t\tinfo.renshu = that.numArr[that.nindex]\r\n\t\t\t\t// console.log(info);return;\r\n\t\t\t\tif (info.renshu <= 0) {\r\n\t\t\t\t\tapp.error('请选择人数');\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tif (info.tableId == 0) {\r\n\t\t\t\t\tapp.error('请选择餐桌');\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post(\"ApiAdminRestaurantShopOrder/add\", {info: info}, function (res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif(res.status == 0) {\r\n\t\t\t\t\t\tapp.alert(res.msg);return;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tapp.alert(res.msg,function(){\r\n\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t});\r\n\t\t\t  });\r\n\t\t\t},\r\n\t\t\tclean: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar tableId = that.opt.id;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post(\"ApiAdminRestaurantTable/clean\", {tableId: tableId}, function (res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif(res.status == 0) {\r\n\t\t\t\t\t\tapp.alert(res.msg);return;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tapp.alert(res.msg,function(){\r\n\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcleanOver: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar tableId = that.opt.id;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post(\"ApiAdminRestaurantTable/cleanOver\", {tableId: tableId}, function (res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif(res.status == 0) {\r\n\t\t\t\t\t\tapp.alert(res.msg);return;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tapp.alert(res.msg,function(){\r\n\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tclose:function(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar tableId = that.opt.id;\r\n\t\t\t\tapp.confirm('确定要关闭订单吗？关闭后会自动切换餐桌状态为空闲，请及时通知厨房取消相关菜品', function () {\r\n\t\t\t\t\tthat.loading = true;\r\n\t\t\t\t  app.post('ApiAdminRestaurantTable/closeOrder', {tableId: tableId}, function (res) {\r\n\t\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\tif(res.status == 0) {\r\n\t\t\t\t\t\t\tapp.alert(res.msg);return;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t    if (res.status == 1) {\r\n\t\t\t\t      app.success(res.msg);\r\n\t\t\t\t      that.getdata();\r\n\t\t\t\t    }\r\n\t\t\t\t  });\r\n\t\t\t\t});\r\n\t\t\t},\t\r\n\t\t\tnumChange: function (e) {\r\n\t\t\t  this.nindex = e.detail.value;\r\n\t\t\t},\r\n\t\t\tstartpause:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar status = e.currentTarget.dataset.status;\r\n\t\t\t\tvar tableId = that.opt.id;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post('ApiAdminRestaurantTable/timingPause', {tableid: tableId,type:status}, function (res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tif(res.status == 0) {\r\n\t\t\t\t\t\tapp.alert(res.msg);return;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.btn-text { color: #007AFF;}\r\n\t.mb {margin-bottom: 10rpx;}\r\n\t.text-center {text-align: center;}\r\n\t.container {padding-bottom: 20rpx;}\r\n\t.head-bg {width: 100%;height: 320rpx; background: linear-gradient(-90deg, #FFCF34 0%, #FFD75F 100%); color: #333;}\r\n\t.head-bg h1 { line-height: 100rpx; font-size: 42rpx;}\r\n\t.head-bg .title { align-items: center; width: 94%; margin: 0 auto;}\r\n\t.head-bg .image{ width:80rpx;height:80rpx; margin: 0 10rpx;}\r\n\t\r\n\t.card-wrap { background-color: #FFFFFF; border-radius: 10rpx;padding: 30rpx; margin: 30rpx auto 0; width: 94%;}\r\n\t.card-view{ margin-top: -140rpx; }\r\n\t.card-wrap .card-title {font-size: 34rpx; color: #333; font-weight: bold;}\r\n\r\n\t\r\n.info-item{ display:flex;align-items:center;width: 100%; background: #fff; /* border-bottom: 1px #f3f3f3 solid; */line-height:70rpx}\r\n.info-item:last-child{border:none}\r\n.info-item .t1{ width: 200rpx;color: #8B8B8B;line-height:30rpx; height: auto;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\r\nform .info-item .t1 {color: #333; font-size: 30rpx;}\r\n.info-item .t2{ color:#444444;text-align:right;flex:1;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden; padding-right: 10rpx;}\r\n.info-item .t3{ }\r\n.info-item .ggtext{width: 90%;padding-left: 10%;text-align: left;}\r\n.card-goods .t1 {width: 60%;}\r\n.card-goods .t2 {width: 8%; padding-right: 2%;}\r\n.card-goods .t3 {width: 20%;}\r\n.info-textarea { height: auto; line-height: 40rpx;}\r\n.info-textarea textarea {height: 80rpx;}\r\n.info-textarea .t2{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp: unset;overflow: scroll;}\r\n\r\n.btn-view { display: flex;justify-content: space-between; margin: 30rpx 0;}\r\n.btn-view button{ width: 90%; border-radius: 10rpx;background: linear-gradient(-90deg, #F7D156 0%, #F9D873 100%); color: #333; font-weight: bold;}\r\n.btn-view .btn-default {background: #FFFFFF;}\r\n\r\n.content{ display:flex;width:100%;padding:0 0 10rpx 0;align-items:center;font-size:24rpx}\r\n.content .item{padding:10rpx 0;flex:1;display:flex;flex-direction:column;align-items:center;position:relative}\r\n.content .item .image{ width:80rpx;height:80rpx}\r\n.content .item .iconfont{font-size:60rpx}\r\n.content .item .t3{ padding-top:3px}\r\n.content .item .t2{display:flex;align-items:center;justify-content:center;background: red;color: #fff;border-radius:50%;padding: 0 10rpx;position: absolute;top: 0px;right:20rpx;width:35rpx;height:35rpx;text-align:center;}\r\n\r\n.card-wrap .img{width:80rpx;height:80rpx;margin: 0 auto;display: block;}\r\n</style>\r\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tableWaiterDetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tableWaiterDetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213990650\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}