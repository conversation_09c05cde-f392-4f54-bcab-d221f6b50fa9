{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/shoporderdetail.vue?b54b", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/shoporderdetail.vue?55c5", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/shoporderdetail.vue?ee5c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/shoporderdetail.vue?47d1", "uni-app:///admin/restaurant/shoporderdetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/shoporderdetail.vue?e00f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/shoporderdetail.vue?4636"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "expressdata", "express_index", "express_no", "prodata", "djs", "detail", "prolist", "shopset", "storeinfo", "lefttime", "codtxt", "p<PERSON><PERSON><PERSON><PERSON>", "peisonguser2", "index2", "returnProlist", "refundNum", "refundTotalprice", "refundReason", "onLoad", "onPullDownRefresh", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "id", "interval", "getdjs", "setremark", "setremarkconfirm", "type", "orderid", "content", "setTimeout", "fahuo", "dialogExpressClose", "expresschange", "setexpressno", "<PERSON><PERSON><PERSON><PERSON>", "express_com", "ispay", "delOrder", "closeOrder", "refundnopass", "refundpass", "print", "refundinit", "retundInput", "max", "ogid", "valnum", "console", "total", "refundMoneyReason", "refundMoney", "dialogRefundClose", "gotoRefundMoney", "reason", "money"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACmE;AACL;AACa;;;AAG3E;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChFA;AAAA;AAAA;AAAA;AAA60B,CAAgB,6yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsRj2B;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAG;YACAH;YACAA;UACA;QACA;QACAA;MACA;IACA;IACAI;MACA;MACA;MAEA;QACAJ;MACA;QACA;QACA;QACA;QACA;QACAA;MACA;IACA;IACAK;MACA;IACA;IACAC;MACA;MACA;MACAL;QAAAM;QAAAC;QAAAC;MAAA;QACAR;QACAS;UACAV;QACA;MACA;IACA;IACAW;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACAd;QAAAM;QAAAC;QAAA9B;QAAAsC;MAAA;QACAf;QACAS;UACAV;QACA;MACA;IACA;IACAiB;MACA;MACA;MACAhB;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAkB;MACA;MACA;MACAjB;MACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAT;UACA;QACA;MACA;IACA;IACAkB;MACA;MACA;MACAlB;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAoB;MACA;MACA;MACAnB;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAqB;MACA;MACA;MACApB;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAsB;MACA;MACA;MACArB;MACAA;QAAAM;QAAAC;MAAA;QACAP;QACAA;MACA;IACA;IACAsB;MACA;MACAvB;MACAC;QAAAO;MAAA;QACAR;QACA;QACAA;QACAA;QACA;UACAA;YACA;YACA;UACA;QACA;QACAA;MACA;IACA;IACAwB;MACA;MACA;MACA;QAAAC;QAAAC;MACA;MACA;MACA;MACAC;MACA;QACA;MACA;MACA;QACA;UACApC;QACA;QACAqC;QACA;QACA;UACAC;QACA;UACAA;QACA;MACA;MACAA;MACAA;MACA7B;IACA;IACA8B;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACAL;MACA3B;QACAD;QACAC;QACAA;UACAO;UACAjB;UACA2C;UACAC;QACA;UACA;YACAlC;YACA;UACA;UACAA;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACxiBA;AAAA;AAAA;AAAA;AAA0rC,CAAgB,0mCAAG,EAAC,C;;;;;;;;;;;ACA9sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/restaurant/shoporderdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/restaurant/shoporderdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./shoporderdetail.vue?vue&type=template&id=e080cf36&\"\nvar renderjs\nimport script from \"./shoporderdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./shoporderdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shoporderdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/restaurant/shoporderdetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporderdetail.vue?vue&type=template&id=e080cf36&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.prolist, function (item, idx) {\n        var $orig = _vm.__get_orig(item)\n        var g0 = item.ggtext && item.ggtext.length\n        var g1 = item.jlprice\n          ? parseFloat(\n              parseFloat(item.sell_price) + parseFloat(item.jlprice)\n            ).toFixed(2)\n          : null\n        return {\n          $orig: $orig,\n          g0: g0,\n          g1: g1,\n        }\n      })\n    : null\n  var m0 = _vm.isload ? _vm.t(\"会员\") : null\n  var m1 = _vm.isload && _vm.detail.leveldk_money > 0 ? _vm.t(\"会员\") : null\n  var m2 = _vm.isload && _vm.detail.coupon_money > 0 ? _vm.t(\"优惠券\") : null\n  var m3 = _vm.isload && _vm.detail.scoredk > 0 ? _vm.t(\"积分\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporderdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporderdetail.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"ordertop\" :style=\"'background:url(' + pre_url + '/static/img/ordertop.png);background-size:100%'\">\n\t\t\t<view class=\"f1\" v-if=\"detail.status==0\">\n\t\t\t\t<view class=\"t1\">等待买家付款</view>\n\t\t\t\t<!-- <view class=\"t2\" v-if=\"djs\">剩余时间：{{djs}}</view> -->\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==1 && detail.paytypeid != 4\">\r\n\t\t\t\t<view class=\"t1\">已支付</view>\r\n\t\t\t\t<view><text style=\"font-weight: bold;margin-left: 10rpx;font-size: 45rpx;\" v-if=\"detail.is_bar_table_order ==1 || detail.pickup_number\">取餐号:{{detail.pickup_number}} </text></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f1\" v-if=\"detail.status==1 && detail.paytypeid == 4\">\r\n\t\t\t\t<view class=\"t1\">线下支付，请收款</view>\r\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==2\">\n\t\t\t\t<view class=\"t1\">订单已发货</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==3\">\n\t\t\t\t<view class=\"t1\">订单已完成</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==4\">\n\t\t\t\t<view class=\"t1\">订单已取消</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"address\" >\n\t\t\t<view class=\"info\" >\n\t\t\t\t<text class=\"t1\" v-if=\"detail.linkman\">{{detail.linkman}} {{detail.tel}}</text>\n\t\t\t\t<text class=\"t2\" >{{detail.tabletext}}：{{detail.tableName}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"product\">\n\t\t\t<view v-for=\"(item, idx) in prolist\" :key=\"idx\" class=\"content\">\n\t\t\t\t<view @tap=\"goto\" :data-url=\"'/restaurant/shop/product?id=' + item.proid\">\n\t\t\t\t\t<image :src=\"item.pic\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t<text class=\"t1\">{{item.name}}</text>\n\t\t\t\t\t<text class=\"t2\" v-if=\"item.ggname\">{{item.ggname}}{{item.jltitle?item.jltitle:''}}</text>\r\n\t\t\t\t\t<view v-if=\"(item.ggtext && item.ggtext.length)\" class=\"flex-col\">\r\n\t\t\t\t\t\t<block v-for=\"(item2,index) in item.ggtext\">\r\n\t\t\t\t\t\t\t<text class=\"ggtext\" >{{item2}}</text>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"t3\">\r\n\t\t\t\t\t\t<!-- <text class=\"x1 flex1\">￥{{item.sell_price}}</text> -->\r\n\t\t\t\t\t\t<text class=\"x1 flex1\" v-if=\"item.jlprice\">￥{{  parseFloat(parseFloat(item.sell_price)+parseFloat(item.jlprice)).toFixed(2) }}</text>\r\n\t\t\t\t\t\t<text class=\"x1 flex1\" v-else>￥{{item.sell_price}}</text>\r\n\t\t\t\t\t\t<text class=\"x2\">×{{item.num}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"item.remark\">备注：{{item.remark}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo\" v-if=\"(detail.status==3 || detail.status==2) && (detail.freight_type==3 || detail.freight_type==4)\">\n\t\t\t<view class=\"item flex-col\">\n\t\t\t\t<text class=\"t1\" style=\"color:#111\">发货信息</text>\n\t\t\t\t<text class=\"t2\" style=\"text-align:left;margin-top:10rpx;padding:0 10rpx\" user-select=\"true\" selectable=\"true\">{{detail.freight_content}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单人</text>\n\t\t\t\t<text class=\"flex1\"></text>\n\t\t\t\t<image :src=\"detail.headimg\" style=\"width:80rpx;height:80rpx;margin-right:8rpx\"/>\n\t\t\t\t<text  style=\"height:80rpx;line-height:80rpx\">{{detail.nickname}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}ID</text>\n\t\t\t\t<text class=\"t2\">{{detail.mid}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\" v-if=\"detail.remark\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">备注</text>\n\t\t\t\t<text class=\"t2\">{{detail.remark}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单编号</text>\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{detail.ordernum}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytypeid!='4' && detail.paytime\">\n\t\t\t\t<text class=\"t1\">支付时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytime\">\n\t\t\t\t<text class=\"t1\">支付方式</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytype}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>1 && detail.send_time\">\n\t\t\t\t<text class=\"t1\">发货时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.send_time}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status==3 && detail.collect_time\">\n\t\t\t\t<text class=\"t1\">收货时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.collect_time}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">菜品金额</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.product_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.leveldk_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}折扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.leveldk_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.jianmoney > 0\">\n\t\t\t\t<text class=\"t1\">满减活动</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.manjian_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.tea_fee > 0\">\r\n\t\t\t\t<text class=\"t1\">{{shopset.tea_fee_text}}</text>\r\n\t\t\t\t<text class=\"t2 red\">+¥{{detail.tea_fee}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.coupon_money > 0\">\r\n\t\t\t\t<text class=\"t1\">{{t('优惠券')}}抵扣</text>\r\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.coupon_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.scoredk > 0\">\r\n\t\t\t\t<text class=\"t1\">{{t('积分')}}抵扣</text>\r\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.scoredk_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.discount_money > 0\">\r\n\t\t\t\t<text class=\"t1\">优惠</text>\r\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.discount_money}}</text>\r\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.timing_money > 0\">\r\n\t\t\t\t<text class=\"t1\">{{shopset.timing_fee_text}}</text>\r\n\t\t\t\t<text class=\"t2 red\">+¥{{detail.timing_money}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.service_money > 0\">\r\n\t\t\t\t<text class=\"t1\">服务费</text>\r\n\t\t\t\t<text class=\"t2 red\">¥{{detail.service_money}}</text>\r\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">实付款</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.totalprice}}</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0\">未付款</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==1 && detail.paytypeid != 4\">已付款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==1 && detail.paytypeid == 4\">线下支付</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==2\">已发货</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==3\">已完成</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==4\">已关闭</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\n\t\t\t\t<text class=\"t1\">退款状态</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==1\">审核中,¥{{detail.refund_money}}</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==2\">已退款,¥{{detail.refund_money}}</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==3\">已驳回,¥{{detail.refund_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\n\t\t\t\t<text class=\"t1\">退款原因</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_reason}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_checkremark\">\n\t\t\t\t<text class=\"t1\">审核备注</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_checkremark}}</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">备注</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.message ? detail.message : '无'}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.field1\">\n\t\t\t\t<text class=\"t1\">{{detail.field1data[0]}}</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.field1data[1]}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.field2\">\n\t\t\t\t<text class=\"t1\">{{detail.field2data[0]}}</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.field2data[1]}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.field3\">\n\t\t\t\t<text class=\"t1\">{{detail.field3data[0]}}</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.field3data[1]}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.field4\">\n\t\t\t\t<text class=\"t1\">{{detail.field4data[0]}}</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.field4data[1]}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.field5\">\n\t\t\t\t<text class=\"t1\">{{detail.field5data[0]}}</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.field5data[1]}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item flex-col\" v-if=\"(detail.status==1 || detail.status==2) && detail.freight_type==1\">\n\t\t\t\t<text class=\"t1\">核销码</text>\n\t\t\t\t<view class=\"flex-x-center\">\n\t\t\t\t\t<image :src=\"detail.hexiao_qr\" style=\"width:400rpx;height:400rpx\" @tap=\"previewImage\" :data-url=\"detail.hexiao_qr\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view style=\"width:100%;height:120rpx\"></view>\n\n\t\t<view class=\"bottom\">\n\t\t\t<view v-if=\"detail.refund_status==1\" class=\"btn2\" @tap=\"refundnopass\" :data-id=\"detail.id\">退款驳回</view>\n\t\t\t<view v-if=\"detail.refund_status==1\" class=\"btn2\" @tap=\"refundpass\" :data-id=\"detail.id\">退款通过</view>\n\t\t\t<view v-if=\"detail.status==0\" class=\"btn2\" @tap=\"closeOrder\" :data-id=\"detail.id\">关闭订单</view>\n\t\t\t<view v-if=\"detail.status==0 && detail.bid==0\" class=\"btn2\" @tap=\"ispay\" :data-id=\"detail.id\">改为已支付</view>\n\t\t\t<!-- <view v-if=\"detail.status==1\" class=\"btn2\" @tap=\"fahuo\" :data-id=\"detail.id\">发货</view>\n\t\t\t<view v-if=\"detail.status==1 && detail.canpeisong\" class=\"btn2\" @tap=\"peisong\" :data-id=\"detail.id\">配送</view>\n\t\t\t<view v-if=\"detail.status==2 || detail.status==3\" class=\"btn2\" @tap=\"goto\" :data-url=\"'/pagesExt/order/logistics?express_com='+detail.express_com+'&express_no='+detail.express_no\">查物流</view> -->\n\t\t\t<view v-if=\"detail.status==4\" class=\"btn2\" @tap=\"delOrder\" :data-id=\"detail.id\">删除</view>\r\n\t\t\t<view class=\"btn2\" @tap=\"setremark\" :data-id=\"detail.id\">设置备注</view>\r\n\t\t\t<view class=\"btn2\" v-if=\"shopset.is_refund &&( detail.status==1 || detail.status==2 || detail.status==3)\" @tap=\"refundinit\" :data-id=\"detail.id\">退款</view>\n\t\t</view>\n\t\t<uni-popup id=\"dialogSetremark\" ref=\"dialogSetremark\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"设置备注\" :value=\"detail.remark\" placeholder=\"请输入备注\" @confirm=\"setremarkconfirm\"></uni-popup-dialog>\n\t\t</uni-popup>\n\t\t<uni-popup id=\"dialogRefund\" ref=\"dialogRefund\" type=\"dialog\" :mask-click=\"false\">\r\n\t\t\t<view class=\"uni-popup-dialog\">\r\n\t\t\t\t<view class=\"uni-dialog-title\">\r\n\t\t\t\t\t<text class=\"uni-dialog-title-text\">退款</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-dialog-content\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view class=\"product\" style=\"width: 100%;margin:0;padding:0\">\r\n\t\t\t\t\t\t\t<scroll-view class=\"popup-content\" scroll-y=\"true\" style=\"max-height: 600rpx;overflow: hidden;\">\r\n\t\t\t\t\t\t\t<view v-for=\"(item, idx) in returnProlist\" :key=\"idx\" class=\"box\">\r\n\t\t\t\t\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'/pages/shop/product?id=' + item.proid\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"item.pic\" style=\"width: 110rpx;height: 110rpx\"></image>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t1\">{{item.name}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t2\">{{item.ggname}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"t3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"x1 flex1\">￥{{item.sell_price}}×{{item.num}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<view style=\"color: #888;font-size: 24rpx;display: flex;\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text>退货数量</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"number\" :value=\"item.can_num\" :data-max=\"item.can_num\" :data-ogid=\"item.id\" @input=\"retundInput\" class=\"retundNum\" style=\"border: 1px #eee solid;width: 80rpx;margin-left: 10rpx;text-align: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;height: 80rpx;\">\r\n\t\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">退款原因：</view>\r\n\t\t\t\t\t\t\t<input type=\"text\" placeholder=\"请输入退款原因\" @input=\"refundMoneyReason\" adjust-position=\"false\" style=\"border: 1px #eee solid;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;padding:0 10rpx\"/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;height: 80rpx\">\r\n\t\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">退款金额：</view>\r\n\t\t\t\t\t\t\t<input type=\"text\" placeholder=\"请输入退款金额\" @input=\"refundMoney\" adjust-position=\"false\" :value=\"refundTotalprice\" style=\"border: 1px #eee solid;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;padding:0 10rpx\"/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-dialog-button-group\">\r\n\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogRefundClose\">\r\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"gotoRefundMoney()\">\r\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nvar interval = null;\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\t\t\t\n\t\t\tpre_url:app.globalData.pre_url,\n\t\t\texpressdata:[],\n\t\t\texpress_index:0,\n\t\t\texpress_no:'',\n      prodata: '',\n      djs: '',\n      detail: \"\",\n      prolist: \"\",\n      shopset: \"\",\n      storeinfo: \"\",\n      lefttime: \"\",\n      codtxt: \"\",\n\t\t\tpeisonguser:[],\n\t\t\tpeisonguser2:[],\n\t\t\tindex2:0,\r\n\t\t\treturnProlist:[], //退款商品\r\n\t\t\trefundNum:[],//退款数据\r\n\t\t\trefundTotalprice:0,//退款总金额\r\n\t\t\trefundReason:''//退款理由\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onUnload: function () {\n    clearInterval(interval);\n  },\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiAdminRestaurantShopOrder/detail', {id: that.opt.id}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.expressdata = res.expressdata;\n\t\t\t\tthat.detail = res.detail;\n\t\t\t\tthat.prolist = res.prolist;\n\t\t\t\tthat.shopset = res.shopset;\n\t\t\t\tthat.storeinfo = res.storeinfo;\n\t\t\t\tthat.lefttime = res.lefttime;\n\t\t\t\tthat.codtxt = res.codtxt;\n\t\t\t\tif (res.lefttime > 0) {\n\t\t\t\t\tinterval = setInterval(function () {\n\t\t\t\t\t\tthat.lefttime = that.lefttime - 1;\n\t\t\t\t\t\tthat.getdjs();\n\t\t\t\t\t}, 1000);\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    getdjs: function () {\n      var that = this;\n      var totalsec = that.lefttime;\n\n      if (totalsec <= 0) {\n        that.djs = '00时00分00秒';\n      } else {\n        var houer = Math.floor(totalsec / 3600);\n        var min = Math.floor((totalsec - houer * 3600) / 60);\n        var sec = totalsec - houer * 3600 - min * 60;\n        var djs = (houer < 10 ? '0' : '') + houer + '时' + (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';\n        that.djs = djs;\n      }\n    },\n\t\tsetremark:function(){\n\t\t\tthis.$refs.dialogSetremark.open();\n\t\t},\n\t\tsetremarkconfirm: function (done, remark) {\n\t\t\tthis.$refs.dialogSetremark.close();\n\t\t\tvar that = this\n\t\t\tapp.post('ApiAdminOrder/setremark', { type:'restaurant_shop',orderid: that.detail.id,content:remark }, function (res) {\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n    },\n\t\tfahuo:function(){\n\t\t\tthis.$refs.dialogExpress.open();\n\t\t},\n\t\tdialogExpressClose:function(){\n\t\t\tthis.$refs.dialogExpress.close();\n\t\t},\n\t\texpresschange:function(e){\n\t\t\tthis.express_index = e.detail.value;\n\t\t},\n\t\tsetexpressno:function(e){\n\t\t\tthis.express_no = e.detail.value;\n\t\t},\n\t\tconfirmfahuo:function(){\n\t\t\tthis.$refs.dialogExpress.close();\n\t\t\tvar that = this\n\t\t\tvar express_com = this.expressdata[this.express_index]\n\t\t\tapp.post('ApiAdminOrder/sendExpress', { type:'restaurant_shop',orderid: that.detail.id,express_no:that.express_no,express_com:express_com}, function (res) {\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n\t\t},\n\t\tispay:function(e){\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要改为已支付吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/ispay', { type:'restaurant_shop',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\tdelOrder: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.showLoading('删除中');\n\t\t\tapp.confirm('确定要删除该订单吗?', function () {\n\t\t\t\tapp.post('ApiAdminOrder/delOrder', { type:'restaurant_shop',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tapp.goto('shoporder');\n\t\t\t\t\t}, 1000)\n\t\t\t\t});\n\t\t\t})\n\t\t},\n\t\tcloseOrder: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要关闭该订单吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/closeOrder', { type:'restaurant_shop',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function (){\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t});\n\t\t\t})\n\t\t},\n\t\trefundnopass: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要驳回退款申请吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/refundnopass', { type:'restaurant_shop',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\trefundpass: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要审核通过并退款吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/refundpass', { type:'restaurant_shop',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\r\n\t\tprint: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t\tvar orderid = e.currentTarget.dataset.id\r\n\t\t\tapp.showLoading('打印中');\r\n\t\t\tapp.post('ApiAdminOrder/print', { type:'restaurant_takeaway',orderid: orderid }, function (data) {\r\n\t\t\t\tapp.showLoading(false);\r\n\t\t\t\tapp.success(data.msg);\r\n\t\t\t})\r\n\t\t},\r\n\t\trefundinit:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.post('ApiAdminRestaurantShopOrder/refundProlist',{orderid:that.detail.id},function(data){\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tlet prolist = data.prolist;\r\n\t\t\t\tthat.returnProlist = data.prolist;\r\n\t\t\t\tthat.refundTotalprice = data.detail.totalprice;\r\n\t\t\t\tfor(var i in prolist){\r\n\t\t\t\t\tthat.refundNum.push({\r\n\t\t\t\t\t\t'ogid':prolist[i].id,\r\n\t\t\t\t\t\t'num':prolist[i].can_num\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tthat.$refs.dialogRefund.open();\r\n\t\t\t})\r\n\t\t},\r\n\t\tretundInput:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar valnum = e.detail.value;\r\n\t\t\tvar {max,ogid} = e.currentTarget.dataset;\r\n\t\t\tvar prolist = that.returnProlist;\r\n\t\t\tvar refundNum = that.refundNum;\r\n\t\t\tvar total = 0;\r\n\t\t\tvalnum = !valnum?0:valnum;\r\n\t\t\tif(valnum > max){\r\n\t\t\t\treturn app.error('请输入正确的数量');\r\n\t\t\t}\r\n\t\t\tfor(var i in refundNum){\r\n\t\t\t\tif(refundNum[i].ogid == ogid){\r\n\t\t\t\t\trefundNum[i].num = valnum;\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(prolist[i]);\r\n\t\t\t\tvar pro = prolist[i];\r\n\t\t\t\tif(refundNum[i].num == pro.num){\r\n\t\t\t\t\ttotal += parseFloat(prolist[i].real_totalprice)\r\n\t\t\t\t}else{\r\n\t\t\t\t\ttotal += refundNum[i].num * parseFloat(prolist[i].real_totalprice) / prolist[i].num \r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\ttotal = parseFloat(total);\r\n\t\t\ttotal = total.toFixed(2);\r\n\t\t\tthat.refundTotalprice = total; \r\n\t\t},\r\n\t\trefundMoneyReason:function(e){\r\n\t\t\tthis.refundReason = e.detail.value;\r\n\t\t},\r\n\t\trefundMoney:function(e){\r\n\t\t\tthis.refundTotalprice = e.detail.value;\r\n\t\t},\r\n\t\tdialogRefundClose:function(){\r\n\t\t\tthis.returnProlist = [];\r\n\t\t\tthis.refundReason = '';\r\n\t\t\tthis.$refs.dialogRefund.close();\r\n\t\t},\r\n\t\tgotoRefundMoney:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tconsole.log(that.refundNum,11111);\r\n\t\t\tapp.confirm('确定要退款吗?', function () {\r\n\t\t\t\tthat.$refs.dialogRefund.close();\r\n\t\t\t\tapp.showLoading('提交中');\r\n\t\t\t\tapp.post('ApiAdminRestaurantShopOrder/refund',{\r\n\t\t\t\t\torderid:that.detail.id,\r\n\t\t\t\t\trefundNum:that.refundNum,\r\n\t\t\t\t\treason:that.refundReason,\r\n\t\t\t\t\tmoney:that.refundTotalprice\r\n\t\t\t\t},function(res){\r\n\t\t\t\t\tif(res.status == 0){\r\n\t\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tapp.showLoading(false);\r\n\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tthat.getdata();\r\n\t\t\t\t\t}, 1000)\r\n\t\t\t\t})\r\n\t\t\t});\r\n\t\t},\n  }\n};\n</script>\n<style>\n.ordertop{width:100%;height:220rpx;padding:50rpx 0 0 70rpx}\n.ordertop .f1{color:#fff}\n.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}\n.ordertop .f1 .t2{font-size:24rpx}\n\n.address{ display:flex;width: 100%; padding: 20rpx 3%; background: #FFF;margin-bottom:20rpx;}\n.address .img{width:40rpx}\n.address image{width:40rpx; height:40rpx;}\n.address .info{flex:1;display:flex;flex-direction:column;}\n.address .info .t1{font-size:28rpx;font-weight:bold;color:#333}\n.address .info .t2{font-size:24rpx;color:#999}\n\n.product{width:100%; padding: 14rpx 3%;background: #FFF;}\n.product .content{display:flex;position:relative;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;}\n.product .content:last-child{ border-bottom: 0; }\n.product .content image{ width: 140rpx; height: 140rpx;}\n.product .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\n.product .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.product .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\n.product .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\n.product .content .detail .x1{ flex:1}\n.product .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\n.product .content .comment{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc702 solid; border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.product .content .comment2{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc7c2 solid; border-radius:10rpx;background:#fff; color: #ffc7c2;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.orderinfo{ width:100%;margin-top:10rpx;padding: 14rpx 3%;background: #FFF;}\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}\n.orderinfo .item:last-child{ border-bottom: 0;}\n.orderinfo .item .t1{width:200rpx;}\n.orderinfo .item .t2{flex:1;text-align:right}\n.orderinfo .item .red{color:red}\n\n.bottom{ width: 100%; padding: 16rpx 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}\n\n.btn1{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}\n.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n.btn3{position:absolute;top:60rpx;right:10rpx;font-size:24rpx;width:120rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n\n.btitle{ width:100%;height:100rpx;background:#fff;padding:0 20rpx;border-bottom:1px solid #f5f5f5}\n.btitle .comment{border: 1px #ffc702 solid;border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.btitle .comment2{border: 1px #ffc7c0 solid;border-radius:10rpx;background:#fff; color: #ffc7c0;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.picker{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n\n.uni-popup-dialog {width: 300px;border-radius: 5px;background-color: #fff;}\n.uni-dialog-title {display: flex;flex-direction: row;justify-content: center;padding-top: 15px;padding-bottom: 5px;}\n.uni-dialog-title-text {font-size: 16px;font-weight: 500;}\n.uni-dialog-content {display: flex;flex-direction: row;justify-content: center;align-items: center;padding: 5px 15px 15px 15px;}\n.uni-dialog-content-text {font-size: 14px;color: #6e6e6e;}\n.uni-dialog-button-group {display: flex;flex-direction: row;border-top-color: #f5f5f5;border-top-style: solid;border-top-width: 1px;}\n.uni-dialog-button {display: flex;flex: 1;flex-direction: row;justify-content: center;align-items: center;height: 45px;}\n.uni-border-left {border-left-color: #f0f0f0;border-left-style: solid;border-left-width: 1px;}\n.uni-dialog-button-text {font-size: 14px;}\n.uni-button-color {color: #007aff;}\r\n.ggtext{color: #999;font-size: 26rpx}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporderdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporderdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213990649\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}