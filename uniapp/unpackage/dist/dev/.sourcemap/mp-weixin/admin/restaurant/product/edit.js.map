{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/product/edit.vue?f4b5", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/product/edit.vue?1b95", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/product/edit.vue?0efd", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/product/edit.vue?bf55", "uni-app:///admin/restaurant/product/edit.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/product/edit.vue?c8f6", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/product/edit.vue?40b7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "loading", "pre_url", "info", "pagecontent", "aglevellist", "levellist", "clist", "cateArr", "groupArr", "freighttypeArr", "freightindex", "freightdata", "freightIds", "gui<PERSON><PERSON>", "pic", "pics", "cids", "gids", "cnames", "gnames", "clistshow", "glistshow", "ggname", "ggindex", "ggindex2", "oldgglist", "gglist", "catche_detailtxt", "start_time1", "start_time2", "end_time1", "end_time2", "start_hours", "end_hours", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "product_showset", "showtjArr", "onLoad", "methods", "getdata", "that", "app", "id", "subform", "formdata", "setTimeout", "detailAddtxt", "dialogDetailtxtClose", "catcheDetailtxt", "console", "dialogDetailtxtConfirm", "detailAddpic", "detailMoveup", "detailMovedown", "detailMovedel", "bindStatusChange", "bindStartTime1Change", "bindStartTime2Change", "bindEndTime1Change", "bindEndTime2Change", "bindStartHoursChange", "bindEndHoursChange", "gglistInput", "getgglist", "itemlen", "newlen", "h", "rowspans", "kid", "n", "k", "title", "ks", "titles", "name", "market_price", "cost_price", "sell_price", "stock", "givescore", "lvprice_data", "addgggroupname", "delgggroupname", "uni", "itemList", "success", "newguigedata", "setgggroupname", "items", "addggname", "delggname", "newitems", "index2", "setggname", "cidsChange", "newcids", "ischecked", "getcnames", "gidsChange", "newgids", "<PERSON><PERSON><PERSON>", "changeClistDialog", "changeGlistDialog", "uploadimg", "removeimg", "uploadimg2", "removeimg2"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACmN;AACnN,gBAAgB,iNAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,iNAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,iQAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/PA;AAAA;AAAA;AAAA;AAAi1B,CAAgB,kyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiVr2B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACA;UACA;UACAA;UACAA;QACA;QACA;UACA;UACAA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAG;MACA;MACA;MACAC;MACAA;MACA;MACA;QACAH;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;MAEA;QACAG;QACAA;MACA;MACA;QACAA;QACAA;MACA;MAEA;MACAH;QAAAC;QAAAxC;QAAAW;QAAAa;QAAAvB;MAAA;QACA;UACAsC;QACA;UACAA;UACAI;YACAJ;UACA;QACA;MACA;IACA;IACAK;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAC;MACA;IACA;IACAC;MACA;MACAD;MACA;MACA;MACA9C;QAAA;QAAA;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;UAAA;UAAA;YAAA;UAAA;QAAA;QAAA;QAAA;QAAA;MAAA;MACA;MACA;IACA;IACAgD;MACA;MACAV;QACA;QACA;QACA;UACA;UACA1B;YAAA;YAAA;YAAA;YAAA;UAAA;QACA;QACA;QACAZ;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;cAAA;YAAA;YAAA;cAAA;YAAA;UAAA;UAAA;UAAA;UAAA;QAAA;QACAqC;MACA;IACA;IACAY;MACA;MACA;MACAjD;IACA;IACAkD;MACA;MACA;MACAlD;IACA;IACAmD;MACA;MACA;MACAnD;IACA;IACAoD;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACApC;MACA;MACAuB;IACA;IACAc;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;UAAAC;QAAA;QAAA;QACAC;QACAC;QACA;UACAA;QACA;QACA;QACAC;QACA;UACAA;QACA;MACA;MACA;QACA;UAAAC;UAAAC;QACA;UACA;UAEAH;YAAAI;YAAAC;YAAA7B;UAAA;UAEA2B;UACA;YACAD;YAAA;cAAAA;YAAA;YACAC;UACA;QACA;MACA;MACA;QACA;QACA;QACA;UACAG;UACAC;QACA;QACA;QACAD;QACAC;QACA;UACAhD;UACA;QACA;UACA;YAAA+C;YAAAE;YAAAC;YAAAC;YAAAC;YAAAC;YAAAhE;YAAAiE;YAAAC;UAAA;QACA;QACAtD;MACA;MACA;MACAuB;IACA;IACAgC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAC;QACAC;QACAC;UACA;YACA;cAAA;cACA7C;cACAA;cACAA;cAAA;YACA;cAAA;cACA;cACA;cACA;gBACA;kBACA8C;gBACA;cACA;cACA9C;cACAS;cACAT;YACA;UACA;QACA;MACA;IACA;IACA+C;MACA;MACA;MACA;QAAA;QACAhE;QACAV;UAAAyD;UAAAC;UAAAiB;QAAA;QACA;MACA;QAAA;QACA3E;QACA;MACA;MACA;MACA;IACA;IACA4E;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAP;QACAC;QACAC;UACA;YACA;cAAA;cACA7C;cACAA;cACAA;cACAA;cAAA;YACA;cAAA;cACA;cACA;cACA;gBACA;kBACA;kBACA;kBACA;oBACA;sBACAmD;wBAAArB;wBAAAC;sBAAA;sBACAqB;oBACA;kBACA;kBACA/E;gBACA;gBACAyE;cACA;cACA9C;cACAS;cACAT;YACA;UACA;QACA;MACA;IACA;IACAqD;MACA;MACA;MACA;MACA;QAAA;QACA;QACArE;QACAgE;UAAAlB;UAAAC;QAAA;QACA1D;QACA;MACA;QAAA;QACAA;QACA;MACA;MACA;MACA;IACA;IACAiF;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;QACA;UACAC;QACA;MACA;MACA;QACA;UACAvD;UAAA;QACA;QACAsD;MACA;MACA;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;QACA/E;MACA;MACA;IACA;IACAgF;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;QACA;UACAH;QACA;MACA;MACA;QACAG;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAjF;MACA;MACA;IACA;IACAkF;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA9D;QACA;UACA1B;QACA;QACA;QACA;MACA;IACA;IACAyF;MACA;MACA;MACA;MACA;QACA;QACAzF;QACAyB;MACA;QACA;QACAzB;QACAyB;MACA;IACA;IACAiE;MACA;MACA;MACAhE;QACAD;MACA;IACA;IACAkE;MACA;MACA;MACAlE;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpzBA;AAAA;AAAA;AAAA;AAAosC,CAAgB,+lCAAG,EAAC,C;;;;;;;;;;;ACAxtC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/restaurant/product/edit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/restaurant/product/edit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit.vue?vue&type=template&id=0f35215e&\"\nvar renderjs\nimport script from \"./edit.vue?vue&type=script&lang=js&\"\nexport * from \"./edit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/restaurant/product/edit.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=template&id=0f35215e&\"", "var components\ntry {\n  components = {\n    dpNotice: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-notice/dp-notice\" */ \"@/components/dp-notice/dp-notice.vue\"\n      )\n    },\n    dpBanner: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-banner/dp-banner\" */ \"@/components/dp-banner/dp-banner.vue\"\n      )\n    },\n    dpSearch: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-search/dp-search\" */ \"@/components/dp-search/dp-search.vue\"\n      )\n    },\n    dpText: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-text/dp-text\" */ \"@/components/dp-text/dp-text.vue\"\n      )\n    },\n    dpTitle: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-title/dp-title\" */ \"@/components/dp-title/dp-title.vue\"\n      )\n    },\n    dpDhlist: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-dhlist/dp-dhlist\" */ \"@/components/dp-dhlist/dp-dhlist.vue\"\n      )\n    },\n    dpLine: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-line/dp-line\" */ \"@/components/dp-line/dp-line.vue\"\n      )\n    },\n    dpBlank: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-blank/dp-blank\" */ \"@/components/dp-blank/dp-blank.vue\"\n      )\n    },\n    dpMenu: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-menu/dp-menu\" */ \"@/components/dp-menu/dp-menu.vue\"\n      )\n    },\n    dpMap: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-map/dp-map\" */ \"@/components/dp-map/dp-map.vue\"\n      )\n    },\n    dpCube: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-cube/dp-cube\" */ \"@/components/dp-cube/dp-cube.vue\"\n      )\n    },\n    dpPicture: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-picture/dp-picture\" */ \"@/components/dp-picture/dp-picture.vue\"\n      )\n    },\n    dpPictures: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-pictures/dp-pictures\" */ \"@/components/dp-pictures/dp-pictures.vue\"\n      )\n    },\n    dpVideo: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-video/dp-video\" */ \"@/components/dp-video/dp-video.vue\"\n      )\n    },\n    dpShop: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-shop/dp-shop\" */ \"@/components/dp-shop/dp-shop.vue\"\n      )\n    },\n    dpProduct: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-product/dp-product\" */ \"@/components/dp-product/dp-product.vue\"\n      )\n    },\n    dpCollage: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-collage/dp-collage\" */ \"@/components/dp-collage/dp-collage.vue\"\n      )\n    },\n    dpKanjia: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-kanjia/dp-kanjia\" */ \"@/components/dp-kanjia/dp-kanjia.vue\"\n      )\n    },\n    dpSeckill: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-seckill/dp-seckill\" */ \"@/components/dp-seckill/dp-seckill.vue\"\n      )\n    },\n    dpScoreshop: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-scoreshop/dp-scoreshop\" */ \"@/components/dp-scoreshop/dp-scoreshop.vue\"\n      )\n    },\n    dpCoupon: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-coupon/dp-coupon\" */ \"@/components/dp-coupon/dp-coupon.vue\"\n      )\n    },\n    dpArticle: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-article/dp-article\" */ \"@/components/dp-article/dp-article.vue\"\n      )\n    },\n    dpBusiness: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-business/dp-business\" */ \"@/components/dp-business/dp-business.vue\"\n      )\n    },\n    dpLiveroom: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-liveroom/dp-liveroom\" */ \"@/components/dp-liveroom/dp-liveroom.vue\"\n      )\n    },\n    dpButton: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-button/dp-button\" */ \"@/components/dp-button/dp-button.vue\"\n      )\n    },\n    dpHotspot: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-hotspot/dp-hotspot\" */ \"@/components/dp-hotspot/dp-hotspot.vue\"\n      )\n    },\n    dpCover: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-cover/dp-cover\" */ \"@/components/dp-cover/dp-cover.vue\"\n      )\n    },\n    dpRichtext: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-richtext/dp-richtext\" */ \"@/components/dp-richtext/dp-richtext.vue\"\n      )\n    },\n    dpForm: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-form/dp-form\" */ \"@/components/dp-form/dp-form.vue\"\n      )\n    },\n    dpUserinfo: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-userinfo/dp-userinfo\" */ \"@/components/dp-userinfo/dp-userinfo.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.cids.length : null\n  var g1 = _vm.isload ? _vm.pic.length : null\n  var g2 = _vm.isload ? _vm.pic.join(\",\") : null\n  var g3 = _vm.isload ? _vm.pics.length : null\n  var g4 = _vm.isload ? _vm.pics.join(\",\") : null\n  var m0 =\n    _vm.isload && _vm.product_showset == 1\n      ? _vm.inArray(\"-1\", _vm.showtjArr)\n      : null\n  var m1 =\n    _vm.isload && _vm.product_showset == 1\n      ? _vm.inArray(\"-2\", _vm.showtjArr)\n      : null\n  var m2 =\n    _vm.isload && _vm.product_showset == 1\n      ? _vm.inArray(\"0\", _vm.showtjArr)\n      : null\n  var l0 =\n    _vm.isload && _vm.product_showset == 1\n      ? _vm.__map(_vm.levellist, function (item, __i0__) {\n          var $orig = _vm.__get_orig(item)\n          var m3 = _vm.inArray(item.id, _vm.showtjArr)\n          return {\n            $orig: $orig,\n            m3: m3,\n          }\n        })\n      : null\n  var m4 = _vm.isload ? _vm.t(\"color1\") : null\n  var m5 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  var l1 =\n    _vm.isload && _vm.clistshow\n      ? _vm.__map(_vm.clist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m6 = _vm.inArray(item.id, _vm.cids)\n          var m7 = m6 ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m6: m6,\n            m7: m7,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        l0: l0,\n        m4: m4,\n        m5: m5,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<form @submit=\"subform\">\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">菜品名称<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" name=\"name\" :value=\"info.name\" placeholder=\"请填写菜品名称\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">菜品分类<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"f2\" @tap=\"changeClistDialog\"><text v-if=\"cids.length>0\">{{cnames}}</text><text v-else style=\"color:#888\">请选择</text><image :src=\"pre_url+'/static/img/arrowright.png'\" style=\"width:30rpx;height:30rpx\"/></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item flex-col\" style=\"border-bottom:0\">\r\n\t\t\t\t\t<view class=\"f1\">菜品主图<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in pic\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pic\"><image style=\"display:block\" :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pic\" data-pernum=\"1\" v-if=\"pic.length==0\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"pic\" :value=\"pic.join(',')\" maxlength=\"-1\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item flex-col\">\r\n\t\t\t\t\t<view class=\"f1\">菜品图片</view>\r\n\t\t\t\t\t<view class=\"f2\" style=\"flex-wrap:wrap\">\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in pics\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"pics\"><image style=\"display:block\" :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"pics\" data-pernum=\"9\" v-if=\"pics.length<5\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"pics\" :value=\"pics.join(',')\" maxlength=\"-1\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item flex-col\">\r\n\t\t\t\t\t<view class=\"f1\">设置规格</view>\r\n\t\t\t\t\t<view class=\"flex-col\">\r\n\t\t\t\t\t\t<view class=\"ggtitle\">\r\n\t\t\t\t\t\t\t<view class=\"t1\">规格分组</view>\r\n\t\t\t\t\t\t\t<view class=\"t2\">规格名称</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"ggcontent\" v-for=\"(gg,index) in guigedata\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"t1\" @tap=\"delgggroupname\" :data-index=\"index\" :data-title=\"gg.title\">{{gg.title}}<image class=\"edit\" :src=\"pre_url+'/static/img/edit2.png'\"/></view>\r\n\t\t\t\t\t\t\t<view class=\"t2\">\r\n\t\t\t\t\t\t\t\t<view class=\"ggname\" v-for=\"(ggitem,index2) in gg.items\" :key=\"index2\" @tap=\"delggname\" :data-index=\"index\" :data-index2=\"index2\" :data-title=\"ggitem.title\" :data-k=\"ggitem.k\">{{ggitem.title}}<image class=\"close\" :src=\"pre_url+'/static/img/ico-del.png'\"/></view>\r\n\t\t\t\t\t\t\t\t<view class=\"ggnameadd\" @tap=\"addggname\" :data-index=\"index\">+</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"ggcontent\">\r\n\t\t\t\t\t\t\t<view class=\"ggadd\" @tap=\"addgggroupname\">添加分组</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\r\n\t\t\t<!-- 规格列表 -->\r\n\t\t\t<view class=\"form-box\" v-for=\"(item,index) in gglist\" :key=\"index\">\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">规格</view>\r\n\t\t\t\t\t<view class=\"f2\" style=\"font-weight:bold\">{{item.name}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">市场价（元）</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" @input=\"gglistInput\" :data-index=\"index\" data-field=\"market_price\" :name=\"'market_price['+index+']'\" :value=\"item.market_price\" placeholder=\"请填写市场价\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">成本价（元）</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" @input=\"gglistInput\" :data-index=\"index\" data-field=\"cost_price\" :name=\"'cost_price['+index+']'\" :value=\"item.cost_price\" placeholder=\"请填写成本价\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">销售价（元）</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" @input=\"gglistInput\" :data-index=\"index\" data-field=\"sell_price\" :name=\"'sell_price['+index+']'\" :value=\"item.sell_price\" placeholder=\"请填写销售价\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">每日库存</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" @input=\"gglistInput\" :data-index=\"index\" data-field=\"stock_daily\" :name=\"'stock_daily['+index+']'\" :value=\"item.stock_daily\" placeholder=\"请填写每日库存\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">总库存</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" @input=\"gglistInput\" :data-index=\"index\" data-field=\"stock\" :name=\"'stock['+index+']'\" :value=\"item.stock\" placeholder=\"请填写库存\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t<!-- \t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">赠{{t('积分')}}(个)</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" @input=\"gglistInput\" :data-index=\"index\" data-field=\"givescore\" :name=\"'givescore['+index+']'\" :value=\"item.givescore\" placeholder=\"请填写赠送数量\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">规格图片</view>\r\n\t\t\t\t\t<view class=\"f2\" style=\"flex-wrap:wrap;margin-top:20rpx;margin-bottom:20rpx\">\r\n\t\t\t\t\t\t<view class=\"layui-imgbox\" v-if=\"item.pic!=''\">\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg2\" :data-index=\"index\"><image style=\"display:block\" :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item.pic\" @tap=\"previewImage\" :data-url=\"item.pic\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"uploadbtn\" v-else :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg2\" :data-index=\"index\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">销量</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" name=\"sales\" :value=\"info.sales\" placeholder=\"请填写销量\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t\t<input type=\"text\" hidden=\"true\" name=\"oldsales\" :value=\"info.id?info.sales:'0'\">\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">每人限购</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" name=\"limit_per\" :value=\"info.limit_per\" placeholder=\"0表示不限购\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">序号</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" name=\"sort\" :value=\"info.sort\" placeholder=\"用于排序,越大越靠前\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-box\" v-if=\"product_showset==1\">\r\n\t\t\t\t<view class=\"form-item flex-col\">\r\n\t\t\t\t\t<view class=\"f1\">显示条件</view>\r\n\t\t\t\t\t<view class=\"f2\" style=\"line-height:30px\">\r\n\t\t\t\t\t\t<checkbox-group class=\"radio-group\" name=\"showtj\" >\r\n\t\t\t\t\t\t\t<label><checkbox value=\"-1\" :checked=\"inArray('-1',showtjArr)?true:false\"></checkbox> 所有人</label> \r\n\t\t\t\t\t\t\t<label><checkbox value=\"-2\" :checked=\"inArray('-2',showtjArr)?true:false\"></checkbox> 未登录用户</label> \r\n\t\t\t\t\t\t\t<label><checkbox value=\"0\" :checked=\"inArray('0',showtjArr)?true:false\"></checkbox> 关注用户</label>\r\n\t\t\t\t\t\t\t<label v-for=\"item in levellist\" :key=\"item.id\"><checkbox :value=\"''+item.id\" :checked=\"inArray(item.id,showtjArr)?true:false\"></checkbox> {{item.name}}</label>\r\n\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view>状态<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<radio-group class=\"radio-group\" name=\"status\" @change=\"bindStatusChange\">\r\n\t\t\t\t\t\t\t<label><radio value=\"1\" :checked=\"info.status==1?true:false\"></radio> 上架</label> \r\n\t\t\t\t\t\t\t<label><radio value=\"0\" :checked=\"!info || info.status==0?true:false\"></radio> 下架</label>\r\n\t\t\t\t\t\t\t<label><radio value=\"2\" :checked=\"info.status==2?true:false\"></radio> 上架时间</label>\r\n\t\t\t\t\t\t\t<label><radio value=\"3\" :checked=\"info.status==3?true:false\"></radio> 上架周期</label>\r\n\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item flex-col\" v-if=\"info.status==2\">\r\n\t\t\t\t\t<view class=\"f1\">上架时间</view>\r\n\t\t\t\t\t<view class=\"f2\" style=\"line-height:30px\">\r\n\t\t\t\t\t\t <picker mode=\"date\" :value=\"start_time1\" @change=\"bindStartTime1Change\">\r\n               <view class=\"picker\">{{start_time1}}</view>\r\n             </picker>\r\n\t\t\t\t\t\t <picker mode=\"time\" :value=\"start_time2\" @change=\"bindStartTime2Change\">\r\n               <view class=\"picker\" style=\"padding-left:10rpx\">{{start_time2}}</view>\r\n             </picker>\r\n\t\t\t\t\t\t <view style=\"padding:0 10rpx;color:#222;font-weight:bold\">到</view>\r\n\t\t\t\t\t\t <picker mode=\"date\" :value=\"end_time1\" @change=\"bindEndTime1Change\">\r\n               <view class=\"picker\">{{end_time1}}</view>\r\n             </picker>\r\n\t\t\t\t\t\t <picker mode=\"time\" :value=\"end_time2\" @change=\"bindEndTime2Change\">\r\n               <view class=\"picker\" style=\"padding-left:10rpx\">{{end_time2}}</view>\r\n             </picker>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item flex-col\" v-if=\"info.status==3\">\r\n\t\t\t\t\t<view class=\"f1\">上架周期</view>\r\n\t\t\t\t\t<view class=\"f2\" style=\"line-height:30px\">\r\n\t\t\t\t\t\t <picker mode=\"time\" :value=\"start_hours\" @change=\"bindStartHoursChange\">\r\n               <view class=\"picker\">{{start_hours}}</view>\r\n             </picker>\r\n\t\t\t\t\t\t <view style=\"padding:0 10rpx;color:#222;font-weight:bold\">到</view>\r\n\t\t\t\t\t\t <picker mode=\"time\" :value=\"end_hours\" @change=\"bindEndHoursChange\">\r\n               <view class=\"picker\">{{end_hours}}</view>\r\n             </picker>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item flex-col\">\r\n\t\t\t\t\t<text>菜品详情</text>\r\n\t\t\t\t\t<view class=\"detailop\"><view class=\"btn\" @tap=\"detailAddtxt\">+文本</view><view class=\"btn\" @tap=\"detailAddpic\">+图片</view></view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<block v-for=\"(setData, index) in pagecontent\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"detaildp\">\r\n\t\t\t\t\t\t\t<view class=\"op\"><view class=\"flex1\"></view><view class=\"btn\" @tap=\"detailMoveup\" :data-index=\"index\">上移</view><view class=\"btn\" @tap=\"detailMovedown\" :data-index=\"index\">下移</view><view class=\"btn\" @tap=\"detailMovedel\" :data-index=\"index\">删除</view></view>\r\n\t\t\t\t\t\t\t<view class=\"detailbox\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='notice'\">\r\n\t\t\t\t\t\t\t\t\t<dp-notice :params=\"setData.params\" :data=\"setData.data\"></dp-notice>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='banner'\">\r\n\t\t\t\t\t\t\t\t\t<dp-banner :params=\"setData.params\" :data=\"setData.data\"></dp-banner> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='search'\">\r\n\t\t\t\t\t\t\t\t\t<dp-search :params=\"setData.params\" :data=\"setData.data\"></dp-search>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='text'\">\r\n\t\t\t\t\t\t\t\t\t<dp-text :params=\"setData.params\" :data=\"setData.data\"></dp-text>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='title'\">\r\n\t\t\t\t\t\t\t\t\t<dp-title :params=\"setData.params\" :data=\"setData.data\"></dp-title>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='dhlist'\">\r\n\t\t\t\t\t\t\t\t\t<dp-dhlist :params=\"setData.params\" :data=\"setData.data\"></dp-dhlist>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='line'\">\r\n\t\t\t\t\t\t\t\t\t<dp-line :params=\"setData.params\" :data=\"setData.data\"></dp-line>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='blank'\">\r\n\t\t\t\t\t\t\t\t\t<dp-blank :params=\"setData.params\" :data=\"setData.data\"></dp-blank>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='menu'\">\r\n\t\t\t\t\t\t\t\t\t<dp-menu :params=\"setData.params\" :data=\"setData.data\"></dp-menu> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='map'\">\r\n\t\t\t\t\t\t\t\t\t<dp-map :params=\"setData.params\" :data=\"setData.data\"></dp-map> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='cube'\">\r\n\t\t\t\t\t\t\t\t\t<dp-cube :params=\"setData.params\" :data=\"setData.data\"></dp-cube> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='picture'\">\r\n\t\t\t\t\t\t\t\t\t<dp-picture :params=\"setData.params\" :data=\"setData.data\"></dp-picture> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='pictures'\"> \r\n\t\t\t\t\t\t\t\t\t<dp-pictures :params=\"setData.params\" :data=\"setData.data\"></dp-pictures> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='video'\">\r\n\t\t\t\t\t\t\t\t\t<dp-video :params=\"setData.params\" :data=\"setData.data\"></dp-video> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='shop'\">\r\n\t\t\t\t\t\t\t\t\t<dp-shop :params=\"setData.params\" :data=\"setData.data\" :shopinfo=\"setData.shopinfo\"></dp-shop> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='product'\">\r\n\t\t\t\t\t\t\t\t\t<dp-product :params=\"setData.params\" :data=\"setData.data\" :menuindex=\"menuindex\"></dp-product> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='collage'\">\r\n\t\t\t\t\t\t\t\t\t<dp-collage :params=\"setData.params\" :data=\"setData.data\"></dp-collage> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='kanjia'\">\r\n\t\t\t\t\t\t\t\t\t<dp-kanjia :params=\"setData.params\" :data=\"setData.data\"></dp-kanjia> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='seckill'\">\r\n\t\t\t\t\t\t\t\t\t<dp-seckill :params=\"setData.params\" :data=\"setData.data\"></dp-seckill> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='scoreshop'\">\r\n\t\t\t\t\t\t\t\t\t<dp-scoreshop :params=\"setData.params\" :data=\"setData.data\"></dp-scoreshop> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='coupon'\">\r\n\t\t\t\t\t\t\t\t\t<dp-coupon :params=\"setData.params\" :data=\"setData.data\"></dp-coupon> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='article'\">\r\n\t\t\t\t\t\t\t\t\t<dp-article :params=\"setData.params\" :data=\"setData.data\"></dp-article> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='business'\">\r\n\t\t\t\t\t\t\t\t\t<dp-business :params=\"setData.params\" :data=\"setData.data\"></dp-business> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='liveroom'\">\r\n\t\t\t\t\t\t\t\t\t<dp-liveroom :params=\"setData.params\" :data=\"setData.data\"></dp-liveroom> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='button'\">\r\n\t\t\t\t\t\t\t\t\t<dp-button :params=\"setData.params\" :data=\"setData.data\"></dp-button> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='hotspot'\">\r\n\t\t\t\t\t\t\t\t\t<dp-hotspot :params=\"setData.params\" :data=\"setData.data\"></dp-hotspot> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='cover'\">\r\n\t\t\t\t\t\t\t\t\t<dp-cover :params=\"setData.params\" :data=\"setData.data\"></dp-cover> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='richtext'\">\r\n\t\t\t\t\t\t\t\t\t<dp-richtext :params=\"setData.params\" :data=\"setData.data\" :content=\"setData.content\"></dp-richtext> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='form'\">\r\n\t\t\t\t\t\t\t\t\t<dp-form :params=\"setData.params\" :data=\"setData.data\" :content=\"setData.content\"></dp-form> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='userinfo'\">\r\n\t\t\t\t\t\t\t\t\t<dp-userinfo :params=\"setData.params\" :data=\"setData.data\" :content=\"setData.content\"></dp-userinfo> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\r\n\t\t\t<button class=\"savebtn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\" form-type=\"submit\">提交</button>\r\n\t\t\t<view style=\"height:50rpx\"></view>\r\n\t\t</form>\r\n\r\n\t\t\r\n\t\t<view class=\"popup__container\" v-if=\"clistshow\">\r\n\t\t\t<view class=\"popup__overlay\" @tap.stop=\"changeClistDialog\"></view>\r\n\t\t\t<view class=\"popup__modal\">\r\n\t\t\t\t<view class=\"popup__title\">\r\n\t\t\t\t\t<text class=\"popup__title-text\">请选择菜品分类</text>\r\n\t\t\t\t\t<image :src=\"pre_url+'/static/img/close.png'\" class=\"popup__close\" style=\"width:36rpx;height:36rpx\" @tap.stop=\"changeClistDialog\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"popup__content\">\r\n\t\t\t\t\t<block v-for=\"(item, index) in clist\" :key=\"item.id\">\r\n\t\t\t\t\t\t<view class=\"clist-item\" @tap=\"cidsChange\" :data-id=\"item.id\">\r\n\t\t\t\t\t\t\t<view class=\"flex1\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"radio\" :style=\"inArray(item.id,cids) ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<uni-popup id=\"dialogInput\" ref=\"dialogInput\" type=\"dialog\">\r\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"输入规格名称\" :value=\"ggname\" placeholder=\"请输入规格名称\" @confirm=\"setggname\"></uni-popup-dialog>\r\n\t\t</uni-popup>\r\n\t\t<uni-popup id=\"dialogInput2\" ref=\"dialogInput2\" type=\"dialog\">\r\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"输入规格分组\" :value=\"ggname\" placeholder=\"请输入规格分组\" @confirm=\"setgggroupname\"></uni-popup-dialog>\r\n\t\t</uni-popup>\r\n\t\t<uni-popup id=\"dialogDetailtxt\" ref=\"dialogDetailtxt\" type=\"dialog\">\r\n\t\t\t<view class=\"uni-popup-dialog\">\r\n\t\t\t\t<view class=\"uni-dialog-title\">\r\n\t\t\t\t\t<text class=\"uni-dialog-title-text\">请输入文本内容</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-dialog-content\">\r\n\t\t\t\t\t<textarea value=\"\" placeholder=\"请输入文本内容\" @input=\"catcheDetailtxt\"></textarea>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-dialog-button-group\">\r\n\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogDetailtxtClose\">\r\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"dialogDetailtxtConfirm\">\r\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-popup-dialog__close\" @click=\"dialogDetailtxtClose\">\r\n\t\t\t\t\t<span class=\"uni-popup-dialog__close-icon \"></span>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\tisload:false,\r\n\t\t\tloading:false,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n      info:{},\r\n\t\t\tpagecontent:[],\r\n\t\t\taglevellist:[],\r\n\t\t\tlevellist:[],\r\n\t\t\tclist:[],\r\n\t\t\tcateArr:[],\r\n\t\t\tgroupArr:[],\r\n\t\t\tfreighttypeArr:['全部模板','指定模板','自动发货','在线卡密'],\r\n\t\t\tfreightindex:0,\r\n\t\t\tfreightdata:[],\r\n\t\t\tfreightIds:[],\r\n\t\t\tguigedata:[],\r\n\t\t\tpic:[],\r\n\t\t\tpics:[],\r\n\t\t\tcids:[],\r\n\t\t\tgids:[],\r\n\t\t\tcnames:'',\r\n\t\t\tgnames:'',\r\n\t\t\tclistshow:false,\r\n\t\t\tglistshow:false,\r\n\t\t\tggname:'',\r\n\t\t\tggindex:0,\r\n\t\t\tggindex2:0,\r\n\t\t\toldgglist:[],\r\n\t\t\tgglist:[],\r\n\t\t\tcatche_detailtxt:'',\r\n\t\t\tstart_time1:'-选择日期-',\r\n\t\t\tstart_time2:'-选择时间-',\r\n\t\t\tend_time1:'-选择日期-',\r\n\t\t\tend_time2:'-选择时间-',\r\n\t\t\tstart_hours:'-开始时间-',\r\n\t\t\tend_hours:'-结束时间-',\r\n\t\t\tgettjArr:['-1'],\r\n\t\t\tproduct_showset:0,\r\n\t\t\tshowtjArr:['-1']\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n  methods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = that.opt.id ? that.opt.id : '';\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiAdminRestaurantProduct/edit',{id:id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.info = res.info;\r\n\t\t\t\tif(that.info.start_time){\r\n\t\t\t\t\tvar start_times = (that.info.start_time).split(' ');\r\n\t\t\t\t\tthat.start_time1 = start_times[0];\r\n\t\t\t\t\tthat.start_time2 = start_times[1];\r\n\t\t\t\t}\r\n\t\t\t\tif(that.info.end_time){\r\n\t\t\t\t\tvar end_times = (that.info.end_time).split(' ');\r\n\t\t\t\t\tthat.end_time1 = end_times[0];\r\n\t\t\t\t\tthat.end_time2 = end_times[1];\r\n\t\t\t\t}\r\n\t\t\t\tif(that.info.start_hours){\r\n\t\t\t\t\tthat.start_hours = that.info.start_hours;\r\n\t\t\t\t}\r\n\t\t\t\tif(that.info.end_hours){\r\n\t\t\t\t\tthat.end_hours = that.info.end_hours;\r\n\t\t\t\t}\r\n\t\t\t\tthat.pagecontent = res.pagecontent;\r\n\t\t\t\tthat.aglevellist = res.aglevellist;\r\n\t\t\t\tthat.levellist = res.levellist;\r\n\t\t\t\tthat.oldgglist = res.newgglist;\r\n\t\t\t\tthat.clist = res.clist;\r\n\t\t\t\tthat.cateArr = res.cateArr;\r\n\t\t\t\tthat.groupArr = res.groupArr;\r\n\t\t\t\tthat.pic = res.pic;\r\n\t\t\t\tthat.pics = res.pics;\r\n\t\t\t\tthat.cids = res.cids;\r\n\t\t\t\tthat.gids = res.gids;\r\n\t\t\t\tthat.guigedata = res.guigedata;\r\n\t\t\t\tthat.product_showset = res.product_showset;\r\n\t\t\t\tthat.showtjArr = that.info.showtj\r\n\t\t\t\tthat.getcnames();\r\n\t\t\t\tthat.getgnames();\r\n\t\t\t\tthat.getgglist();\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n    subform: function (e) {\r\n      var that = this;\r\n      var formdata = e.detail.value;\r\n      formdata.cid = that.cids.join(',');\r\n      formdata.gid = that.gids;\r\n\t\t\tvar guigedata = that.guigedata;\r\n\t\t\tif(guigedata.length == 0){\r\n\t\t\t\tapp.alert('至少需要添加一个规格');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tfor(var i in guigedata){\r\n\t\t\t\tif(guigedata[i].items.length==0){\r\n\t\t\t\t\tapp.alert('规格分组['+guigedata[i].title+']至少需要添加一个规格');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif(formdata.status==2){\r\n\t\t\t\tformdata.start_time = that.start_time1 + ' '+that.start_time2;\r\n\t\t\t\tformdata.end_time = that.end_time1 + ' '+that.end_time2;\r\n\t\t\t}\r\n\t\t\tif(formdata.status==3){\r\n\t\t\t\tformdata.start_hours = that.start_hours;\r\n\t\t\t\tformdata.end_hours = that.end_hours;\r\n\t\t\t}\r\n\r\n      var id = that.opt.id ? that.opt.id : '';\r\n      app.post('ApiAdminRestaurantProduct/save', {id:id,info:formdata,guigedata:guigedata,gglist:that.gglist,pagecontent:that.pagecontent}, function (res) {\r\n        if (res.status == 0) {\r\n          app.error(res.msg);\r\n        } else {\r\n          app.success(res.msg);\r\n          setTimeout(function () {\r\n            app.goto('index', 'redirect');\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n\t\tdetailAddtxt:function(){\r\n\t\t\tthis.$refs.dialogDetailtxt.open();\r\n\t\t},\r\n\t\tdialogDetailtxtClose:function(){\r\n\t\t\tthis.$refs.dialogDetailtxt.close();\r\n\t\t},\r\n\t\tcatcheDetailtxt:function(e){\r\n\t\t\tconsole.log(e)\r\n\t\t\tthis.catche_detailtxt = e.detail.value;\r\n\t\t},\r\n\t\tdialogDetailtxtConfirm:function(e){\r\n\t\t\tvar detailtxt = this.catche_detailtxt;\r\n\t\t\tconsole.log(detailtxt)\r\n\t\t\tvar Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tpagecontent.push({\"id\":Mid,\"temp\":\"text\",\"params\":{\"content\":detailtxt,\"showcontent\":detailtxt,\"bgcolor\":\"#ffffff\",\"fontsize\":\"14\",\"lineheight\":\"20\",\"letter_spacing\":\"0\",\"bgpic\":\"\",\"align\":\"left\",\"color\":\"#000\",\"margin_x\":\"0\",\"margin_y\":\"0\",\"padding_x\":\"5\",\"padding_y\":\"5\",\"quanxian\":{\"all\":true},\"platform\":{\"all\":true}},\"data\":\"\",\"other\":\"\",\"content\":\"\"});\r\n\t\t\tthis.pagecontent = pagecontent;\r\n\t\t\tthis.$refs.dialogDetailtxt.close();\r\n\t\t},\r\n\t\tdetailAddpic:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tvar Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\t\tvar pics = [];\r\n\t\t\t\tfor(var i in urls){\r\n\t\t\t\t\tvar picid = 'p' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\t\t\tpics.push({\"id\":picid,\"imgurl\":urls[i],\"hrefurl\":\"\",\"option\":\"0\"})\r\n\t\t\t\t}\r\n\t\t\t\tvar pagecontent = that.pagecontent;\r\n\t\t\t\tpagecontent.push({\"id\":Mid,\"temp\":\"picture\",\"params\":{\"bgcolor\":\"#FFFFFF\",\"margin_x\":\"0\",\"margin_y\":\"0\",\"padding_x\":\"0\",\"padding_y\":\"0\",\"quanxian\":{\"all\":true},\"platform\":{\"all\":true}},\"data\":pics,\"other\":\"\",\"content\":\"\"});\r\n\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t},9);\r\n\t\t},\r\n\t\tdetailMoveup:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tpagecontent[index] = pagecontent.splice(index-1, 1, pagecontent[index])[0];\r\n\t\t},\r\n\t\tdetailMovedown:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tpagecontent[index] = pagecontent.splice(index+1, 1, pagecontent[index])[0];\r\n\t\t},\r\n\t\tdetailMovedel:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tpagecontent.splice(index,1);\r\n\t\t},\r\n\t\tbindStatusChange:function(e){\r\n\t\t\tthis.info.status = e.detail.value;\r\n\t\t},\r\n\t\tbindStartTime1Change:function(e){\r\n\t\t\tthis.start_time1 = e.target.value\r\n\t\t},\r\n\t\tbindStartTime2Change:function(e){\r\n\t\t\tthis.start_time2 = e.target.value\r\n\t\t},\r\n\t\tbindEndTime1Change:function(e){\r\n\t\t\tthis.end_time1 = e.target.value\r\n\t\t},\r\n\t\tbindEndTime2Change:function(e){\r\n\t\t\tthis.end_time2 = e.target.value\r\n\t\t},\r\n\t\tbindStartHoursChange:function(e){\r\n\t\t\tthis.start_hours = e.target.value\r\n\t\t},\r\n\t\tbindEndHoursChange:function(e){\r\n\t\t\tthis.end_hours = e.target.value\r\n\t\t},\r\n\t\tgglistInput:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar field = e.currentTarget.dataset.field;\r\n\t\t\tvar gglist = this.gglist;\r\n\t\t\tgglist[index][field] = e.detail.value;\r\n\t\t\tthis.gglist = gglist;\r\n\t\t\tconsole.log(gglist)\r\n\t\t},\r\n\t\tgetgglist:function(){\r\n\t\t\tvar oldgglist = this.oldgglist;\r\n\t\t\tvar guigedata = this.guigedata;\r\n\t\t\tvar gglist = [];\r\n\t\t\tvar len = guigedata.length;\r\n\t\t\tvar newlen = 1; \r\n\t\t\tvar h = new Array(len);\r\n\t\t\tvar rowspans = new Array(len);\r\n\t\t\tfor(var i=0;i<len;i++){\r\n\t\t\t\tvar itemlen = guigedata[i].items.length;\r\n\t\t\t\tif(itemlen<=0) { itemlen = 1 };\r\n\t\t\t\tnewlen*=itemlen;\r\n\t\t\t\th[i] = new Array(newlen);\r\n\t\t\t\tfor(var j=0;j<newlen;j++){\r\n\t\t\t\t\th[i][j] = new Array();\r\n\t\t\t\t}\r\n        var l = guigedata[i].items.length;\r\n        rowspans[i] = 1;\r\n        for(j=i+1;j<len;j++){\r\n          rowspans[i]*= guigedata[j].items.length;\r\n        }\r\n\t\t\t}\r\n\t\t\tfor(var m=0;m<len;m++){\r\n\t\t\t\tvar k = 0,kid = 0,n=0;\r\n\t\t\t\tfor(var j=0;j<newlen;j++){\r\n          var rowspan = rowspans[m];\r\n          \r\n\t\t\t\t\th[m][j]={ k:guigedata[m].items[kid].k,title:guigedata[m].items[kid].title,id: guigedata[m].items[kid].id};\r\n\t\t\t\t\t\r\n          n++;\r\n          if(n==rowspan){\r\n            kid++; if(kid>guigedata[m].items.length-1) { kid=0; }\r\n            n=0;\r\n          }\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tfor(var i=0;i<newlen;i++){\r\n\t\t\t\tvar ks = [];\r\n\t\t\t\tvar titles = [];\r\n\t\t\t\tfor(var j=0;j<len;j++){\r\n\t\t\t\t\tks.push( h[j][i].k);\r\n\t\t\t\t\ttitles.push( h[j][i].title);\r\n\t\t\t\t}\r\n\t\t\t\tvar ks2 =ks.join('_');\r\n\t\t\t\tks =ks.join(',');\r\n\t\t\t\ttitles =titles.join(',');\r\n\t\t\t\tif(typeof(oldgglist[ks])!='undefined'){\r\n          oldgglist[ks].name = titles;\r\n\t\t\t\t\tvar val = oldgglist[ks];\r\n\t\t\t\t}else{\r\n\t\t\t\t\tvar val = { ks:ks,name:titles,market_price:'',cost_price:'',sell_price:'',stock:'1000',pic:'',givescore:'0',lvprice_data:null};\r\n\t\t\t\t}\r\n\t\t\t\tgglist.push(val);\r\n\t\t\t}\r\n\t\t\tthis.gglist = gglist;\r\n\t\t\tconsole.log(gglist);\r\n\t\t},\r\n\t\taddgggroupname:function(e){\r\n\t\t\tthis.ggname = '';\r\n\t\t\tthis.ggindex = -1;\r\n\t\t\tthis.$refs.dialogInput2.open();\r\n\t\t},\r\n\t\tdelgggroupname:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar ggindex = e.currentTarget.dataset.index;\r\n\t\t\tvar title = e.currentTarget.dataset.title;\r\n\t\t\tuni.showActionSheet({\r\n        itemList: [ '修改','删除'],\r\n        success: function (res) {\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tif (res.tapIndex == 0) { //修改规格项\r\n\t\t\t\t\t\t\tthat.ggname = title;\r\n\t\t\t\t\t\t\tthat.ggindex = ggindex;\r\n\t\t\t\t\t\t\tthat.$refs.dialogInput2.open();return;\r\n\t\t\t\t\t\t}else if (res.tapIndex == 1) { //删除规格项\r\n\t\t\t\t\t\t\tvar guigedata = that.guigedata;\r\n\t\t\t\t\t\t\tvar newguigedata = [];\r\n\t\t\t\t\t\t\tfor(var i in guigedata){\r\n\t\t\t\t\t\t\t\tif(i != ggindex){\r\n\t\t\t\t\t\t\t\t\tnewguigedata.push(guigedata[i]);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthat.guigedata = newguigedata;\r\n\t\t\t\t\t\t\tconsole.log(newguigedata);\r\n\t\t\t\t\t\t\tthat.getgglist();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tsetgggroupname:function(done,val){\r\n\t\t\tvar guigedata = this.guigedata;\r\n\t\t\tvar ggindex = this.ggindex;\r\n\t\t\tif(ggindex == -1){ //新增规格分组\r\n\t\t\t\tggindex = guigedata.length;\r\n\t\t\t\tguigedata.push({k:ggindex,title:val,items:[]});\r\n\t\t\t\tthis.guigedata = guigedata;\r\n\t\t\t}else{ //修改规格分组名称\r\n\t\t\t\tguigedata[ggindex].title = val;\r\n\t\t\t\tthis.guigedata = guigedata;\r\n\t\t\t}\r\n\t\t\tthis.$refs.dialogInput2.close();\r\n\t\t\tthis.getgglist();\r\n\t\t},\r\n\t\taddggname:function(e){\r\n\t\t\tvar ggindex = e.currentTarget.dataset.index;\r\n\t\t\tthis.ggname = '';\r\n\t\t\tthis.ggindex = ggindex;\r\n\t\t\tthis.ggindex2 = -1;\r\n\t\t\tthis.$refs.dialogInput.open();\r\n\t\t},\r\n\t\tdelggname:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar ggindex = e.currentTarget.dataset.index;\r\n\t\t\tvar ggindex2 = e.currentTarget.dataset.index2;\r\n\t\t\tvar k = e.currentTarget.dataset.k;\r\n\t\t\tvar title = e.currentTarget.dataset.title;\r\n\t\t\tuni.showActionSheet({\r\n        itemList: [ '修改','删除'],\r\n        success: function (res) {\r\n\t\t\t\t\tif(res.tapIndex >= 0){\r\n\t\t\t\t\t\tif (res.tapIndex == 0) { //修改规格项\r\n\t\t\t\t\t\t\tthat.ggname = title;\r\n\t\t\t\t\t\t\tthat.ggindex = ggindex;\r\n\t\t\t\t\t\t\tthat.ggindex2 = ggindex2;\r\n\t\t\t\t\t\t\tthat.$refs.dialogInput.open();return;\r\n\t\t\t\t\t\t}else if (res.tapIndex == 1) { //删除规格项\r\n\t\t\t\t\t\t\tvar guigedata = that.guigedata;\r\n\t\t\t\t\t\t\tvar newguigedata = [];\r\n\t\t\t\t\t\t\tfor(var i in guigedata){\r\n\t\t\t\t\t\t\t\tif(i == ggindex){\r\n\t\t\t\t\t\t\t\t\tvar newitems = [];\r\n\t\t\t\t\t\t\t\t\tvar index2 = 0;\r\n\t\t\t\t\t\t\t\t\tfor(var j in guigedata[i].items){\r\n\t\t\t\t\t\t\t\t\t\tif(j!=ggindex2){\r\n\t\t\t\t\t\t\t\t\t\t\tnewitems.push({k:index2,title:guigedata[i].items[j].title});\r\n\t\t\t\t\t\t\t\t\t\t\tindex2++;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tguigedata[i].items = newitems;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tnewguigedata.push(guigedata[i]);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthat.guigedata = newguigedata;\r\n\t\t\t\t\t\t\tconsole.log(newguigedata)\r\n\t\t\t\t\t\t\tthat.getgglist();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tsetggname:function(done,val){\r\n\t\t\tvar guigedata = this.guigedata;\r\n\t\t\tvar ggindex = this.ggindex;\r\n\t\t\tvar ggindex2 = this.ggindex2;\r\n\t\t\tif(ggindex2 == -1){ //新增规格名称\r\n\t\t\t\tvar items = guigedata[ggindex].items;\r\n\t\t\t\tggindex2 = items.length;\r\n\t\t\t\titems.push({k:ggindex2,title:val});\r\n\t\t\t\tguigedata[ggindex].items = items;\r\n\t\t\t\tthis.guigedata = guigedata;\r\n\t\t\t}else{ //修改规格名称\r\n\t\t\t\tguigedata[ggindex].items[ggindex2].title = val;\r\n\t\t\t\tthis.guigedata = guigedata;\r\n\t\t\t}\r\n\t\t\tthis.$refs.dialogInput.close();\r\n\t\t\tthis.getgglist();\r\n\t\t},\r\n\t\tcidsChange:function(e){\r\n\t\t\tvar clist = this.clist;\r\n\t\t\tvar cids = this.cids;\r\n\t\t\tvar cid = e.currentTarget.dataset.id;\r\n\t\t\tvar newcids = [];\r\n\t\t\tvar ischecked = false;\r\n\t\t\tfor(var i in cids){\r\n\t\t\t\tif(cids[i] != cid){\r\n\t\t\t\t\tnewcids.push(cids[i]);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tischecked = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(ischecked==false){\r\n\t\t\t\tif(newcids.length >= 5){\r\n\t\t\t\t\tapp.error('最多只能选择五个分类');return;\r\n\t\t\t\t}\r\n\t\t\t\tnewcids.push(cid);\r\n\t\t\t}\r\n\t\t\tthis.cids = newcids;\r\n\t\t\tthis.getcnames();\r\n\t\t},\r\n\t\tgetcnames:function(){\r\n\t\t\tvar cateArr = this.cateArr;\r\n\t\t\tvar cids = this.cids;\r\n\t\t\tvar cnames = [];\r\n\t\t\tfor(var i in cids){\r\n\t\t\t\tcnames.push(cateArr[cids[i]]);\r\n\t\t\t}\r\n\t\t\tthis.cnames = cnames.join(',');\r\n\t\t},\r\n\t\tgidsChange:function(e){\r\n\t\t\tvar gids = this.gids;\r\n\t\t\tvar gid = e.currentTarget.dataset.id;\r\n\t\t\tvar newgids = [];\r\n\t\t\tvar ischecked = false;\r\n\t\t\tfor(var i in gids){\r\n\t\t\t\tif(gids[i] != gid){\r\n\t\t\t\t\tnewgids.push(gids[i]);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tischecked = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(ischecked==false){\r\n\t\t\t\tnewgids.push(gid);\r\n\t\t\t}\r\n\t\t\tthis.gids = newgids;\r\n\t\t\tthis.getgnames();\r\n\t\t},\r\n\t\tgetgnames:function(){\r\n\t\t\tvar groupArr = this.groupArr;\r\n\t\t\tvar gids = this.gids;\r\n\t\t\tvar gnames = [];\r\n\t\t\tfor(var i in gids){\r\n\t\t\t\tgnames.push(groupArr[gids[i]]);\r\n\t\t\t}\r\n\t\t\tthis.gnames = gnames.join(',');\r\n\t\t},\r\n\t\tchangeClistDialog:function(){\r\n\t\t\tthis.clistshow = !this.clistshow\r\n\t\t},\r\n\t\tchangeGlistDialog:function(){\r\n\t\t\tthis.glistshow = !this.glistshow\r\n\t\t},\r\n\t\tuploadimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar pernum = parseInt(e.currentTarget.dataset.pernum);\r\n\t\t\tif(!pernum) pernum = 1;\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tvar pics = that[field]\r\n\t\t\tif(!pics) pics = [];\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tfor(var i=0;i<urls.length;i++){\r\n\t\t\t\t\tpics.push(urls[i]);\r\n\t\t\t\t}\r\n\t\t\t\tif(field == 'pic') that.pic = pics;\r\n\t\t\t\tif(field == 'pics') that.pics = pics;\r\n\t\t\t},pernum);\r\n\t\t},\r\n\t\tremoveimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tif(field == 'pic'){\r\n\t\t\t\tvar pics = that.pic\r\n\t\t\t\tpics.splice(index,1);\r\n\t\t\t\tthat.pic = pics;\r\n\t\t\t}else if(field == 'pics'){\r\n\t\t\t\tvar pics = that.pics\r\n\t\t\t\tpics.splice(index,1);\r\n\t\t\t\tthat.pics = pics;\r\n\t\t\t}\r\n\t\t},\r\n\t\tuploadimg2:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tthat.gglist[index].pic = urls[0];\r\n\t\t\t},1);\r\n\t\t},\r\n\t\tremoveimg2:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\tthat.gglist[index].pic = '';\r\n\t\t},\r\n  }\r\n};\r\n</script>\r\n<style>\r\nradio{transform: scale(0.6);}\r\ncheckbox{transform: scale(0.6);}\r\n.form-box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}\r\n.form-item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }\r\n.form-item .f1{color:#222;width:200rpx;flex-shrink:0}\r\n.form-item .f2{display:flex;align-items:center}\r\n.form-box .form-item:last-child{ border:none}\r\n.form-box .flex-col{padding-bottom:20rpx}\r\n.form-item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}\r\n.form-item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}\r\n.form-item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }\r\n.form-item .upload_pic image{ width: 32rpx;height: 32rpx; }\r\n.savebtn{ width: 90%; height:96rpx; line-height: 96rpx; text-align:center;border-radius:48rpx; color: #fff;font-weight:bold;margin: 0 5%; margin-top:60rpx; border: none; }\r\n\r\n.ggtitle{height:60rpx;line-height:60rpx;color:#111;font-weight:bold;font-size:26rpx;display:flex;border-bottom:1px solid #f4f4f4}\r\n.ggtitle .t1{width:200rpx;}\r\n.ggcontent{line-height:60rpx;margin-top:10rpx;color:#111;font-size:26rpx;display:flex}\r\n.ggcontent .t1{width:200rpx;display:flex;align-items:center;flex-shrink:0}\r\n.ggcontent .t1 .edit{width:40rpx;height:40rpx}\r\n.ggcontent .t2{display:flex;flex-wrap:wrap;align-items:center}\r\n.ggcontent .ggname{background:#f55;color:#fff;height:40rpx;line-height:40rpx;padding:0 20rpx;border-radius:8rpx;margin-right:20rpx;margin-bottom:10rpx;font-size:24rpx;position:relative}\r\n.ggcontent .ggname .close{position:absolute;top:-14rpx;right:-14rpx;background:#fff;height:28rpx;width:28rpx;border-radius:14rpx}\r\n.ggcontent .ggnameadd{background:#ccc;font-size:36rpx;color:#fff;height:40rpx;line-height:40rpx;padding:0 20rpx;border-radius:8rpx;margin-right:20rpx;margin-left:10rpx;position:relative}\r\n.ggcontent .ggadd{font-size:26rpx;color:#558}\r\n\r\n.ggbox{line-height:50rpx;}\r\n\r\n\r\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.layui-imgbox-img>image{max-width:100%;}\r\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n\r\n\r\n.clist-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\r\n.radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\r\n.radio .radio-img{width:100%;height:100%;display:block}\r\n\r\n.freightitem{width:100%;height:60rpx;display:flex;align-items:center;margin-left:40rpx}\r\n.freightitem .f1{color:#666;flex:1}\r\n\r\n.detailop{display:flex;line-height:60rpx}\r\n.detailop .btn{border:1px solid #ccc;margin-right:10rpx;padding:0 16rpx;color:#222;border-radius:10rpx}\r\n.detaildp{position:relative;line-height:50rpx}\r\n.detaildp .op{width:100%;display:flex;justify-content:flex-end;font-size:24rpx;height:60rpx;line-height:60rpx;margin-top:10rpx}\r\n.detaildp .op .btn{background:rgba(0,0,0,0.4);margin-right:10rpx;padding:0 10rpx;color:#fff}\r\n.detaildp .detailbox{border:2px dashed #00a0e9}\r\n\r\n.uni-popup-dialog {width: 300px;border-radius: 5px;background-color: #fff;}\r\n.uni-dialog-title {display: flex;flex-direction: row;justify-content: center;padding-top: 15px;padding-bottom: 5px;}\r\n.uni-dialog-title-text {font-size: 16px;font-weight: 500;}\r\n.uni-dialog-content {display: flex;flex-direction: row;justify-content: center;align-items: center;padding: 5px 15px 15px 15px;}\r\n.uni-dialog-content-text {font-size: 14px;color: #6e6e6e;}\r\n.uni-dialog-button-group {display: flex;flex-direction: row;border-top-color: #f5f5f5;border-top-style: solid;border-top-width: 1px;}\r\n.uni-dialog-button {display: flex;flex: 1;flex-direction: row;justify-content: center;align-items: center;height: 45px;}\r\n.uni-border-left {border-left-color: #f0f0f0;border-left-style: solid;border-left-width: 1px;}\r\n.uni-dialog-button-text {font-size: 14px;}\r\n.uni-button-color {color: #007aff;}\r\n</style>", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213990651\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}