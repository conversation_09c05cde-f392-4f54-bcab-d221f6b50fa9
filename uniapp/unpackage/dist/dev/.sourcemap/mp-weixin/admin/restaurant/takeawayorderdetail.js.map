{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/takeawayorderdetail.vue?e837", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/takeawayorderdetail.vue?ade6", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/takeawayorderdetail.vue?44f7", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/takeawayorderdetail.vue?a237", "uni-app:///admin/restaurant/takeawayorderdetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/takeawayorderdetail.vue?a4c4", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/takeawayorderdetail.vue?1961"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "expressdata", "express_index", "express_no", "prodata", "djs", "detail", "prolist", "shopset", "storeinfo", "lefttime", "codtxt", "p<PERSON><PERSON><PERSON><PERSON>", "peisonguser2", "index2", "goods_hexiao_status", "myt_weight", "myt_remark", "mytindex", "myt_shop_id", "mytdata", "returnProlist", "refundNum", "refundTotalprice", "refundReason", "onLoad", "onPullDownRefresh", "onUnload", "clearInterval", "methods", "getdata", "that", "app", "id", "interval", "getdjs", "setremark", "setremarkconfirm", "type", "orderid", "content", "setTimeout", "fahuo", "dialogExpressClose", "expresschange", "setexpressno", "<PERSON><PERSON><PERSON><PERSON>", "express_com", "ispay", "jiedan", "refund", "delOrder", "closeOrder", "refundnopass", "refundpass", "print", "peisong", "psid", "dialogPeisongClose", "peisong<PERSON>hange", "confirmPeisong", "peisongWx", "peisongMyt", "goMyt", "confirmfahuo11", "dialogExpress11Close", "mytWeight", "mytRemark", "mytshopChange", "refundinit", "retundInput", "max", "ogid", "valnum", "console", "total", "refundMoneyReason", "refundMoney", "dialogRefundClose", "gotoRefundMoney", "reason", "money"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,4BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AACuE;AACL;AACa;;;AAG/E;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,yFAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAAi1B,CAAgB,izBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsZr2B;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EAEAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACA;UACAA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACAG;YACAH;YACAA;UACA;QACA;QACAA;MACA;IACA;IACAI;MACA;MACA;MAEA;QACAJ;MACA;QACA;QACA;QACA;QACA;QACAA;MACA;IACA;IACAK;MACA;IACA;IACAC;MACA;MACA;MACAL;QAAAM;QAAAC;QAAAC;MAAA;QACAR;QACAS;UACAV;QACA;MACA;IACA;IACAW;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACAd;QAAAM;QAAAC;QAAApC;QAAA4C;MAAA;QACAf;QACAS;UACAV;QACA;MACA;IACA;IACAiB;MACA;MACA;MACAhB;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAkB;MACA;MACA;MACAjB;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAmB;MACA;MACA;MACAlB;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAoB;MACA;MACA;MACAnB;MACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAT;UACA;QACA;MACA;IACA;IACAoB;MACA;MACA;MACApB;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAsB;MACA;MACA;MACArB;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAuB;MACA;MACA;MACAtB;QACAA;QACAA;UAAAM;UAAAC;QAAA;UACAP;UACAA;UACAS;YACAV;UACA;QACA;MACA;IACA;IACAwB;MACA;MACA;MACAvB;MACAA;QAAAM;QAAAC;MAAA;QACAP;QACAA;MACA;IACA;IACAwB;MACA;MACAzB;MACAC;QAAAM;QAAAC;MAAA;QACAR;QACA;QACA;QACA;QACA;QAEA;QACA;UACAlB;QACA;QACAkB;QACAA;QACA;UACAA;QACA;UACA;YACA;UACA;YACA;UACA;UACA;YACA;UACA;YACA;UACA;UACAC;YACAA;cAAAM;cAAAC;cAAAkB;YAAA;cACAzB;cACAS;gBACAV;cACA;YACA;UACA;QACA;MACA;IACA;IACA2B;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA5B;QAAAM;QAAAC;QAAAkB;MAAA;QACAzB;QACAD;QACAU;UACAV;QACA;MACA;IACA;IACA8B;MACA;MACA;MACA;QACA;MACA;QACA;MACA;MACA7B;QACAD;QACAC;UAAAM;UAAAC;QAAA;UACAR;UACAC;UACAS;YACAV;UACA;QACA;MACA;IACA;IACA+B;MACA;MACA;MACA;QACA;UACAxB;UACAC;QACA;QACAP;UACAD;UACA;YACAA;YACA;cACAA;YACA;YACAA;UACA;YACAC;UACA;QACA;MACA;IACA;IACA+B;MACA;MACA;MACA;MACA/B;QACAD;QACAA;QACA;UACAO;UACAC;UACAvB;UACAC;UACAE;QACA;QACAa;UACAD;UACA;YACAC;YACAS;cACAV;YACA;UACA;YACAC;UACA;QAEA;MACA;IACA;IACAgC;MACA;MACAjC;IACA;IACAkC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACArC;MACA;MACAA;IACA;IACAsC;MACA;MACAtC;MACAC;QAAAO;MAAA;QAEA;QACAR;QACAA;QAEA;UACAA;YACA;YACA;UACA;QACA;QACAA;QACAA;MAEA;IACA;IACAuC;MACA;MACA;MACA;QAAAC;QAAAC;MACA;MACA;MACA;MACAC;MACA;QACA;MACA;MACA;QACA;UACAnD;QACA;QACAoD;QACA;QACA;UACAC;QACA;UACAA;QACA;MACA;MACAA;MACAA;MACA5C;IACA;IACA6C;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACAL;MACA1C;QACAD;QACAC;QACAA;UACAO;UACAjB;UACA0D;UACAC;QACA;UACA;YACAjD;YACA;UACA;UACAA;UACAA;UACAS;YACAV;YACAA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACr2BA;AAAA;AAAA;AAAA;AAA8rC,CAAgB,8mCAAG,EAAC,C;;;;;;;;;;;ACAltC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/restaurant/takeawayorderdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/restaurant/takeawayorderdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./takeawayorderdetail.vue?vue&type=template&id=a3d130f4&\"\nvar renderjs\nimport script from \"./takeawayorderdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./takeawayorderdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./takeawayorderdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/restaurant/takeawayorderdetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./takeawayorderdetail.vue?vue&type=template&id=a3d130f4&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.prolist, function (item, idx) {\n        var $orig = _vm.__get_orig(item)\n        var g0 = item.jlprice\n          ? parseFloat(\n              parseFloat(item.sell_price) + parseFloat(item.jlprice)\n            ).toFixed(2)\n          : null\n        return {\n          $orig: $orig,\n          g0: g0,\n        }\n      })\n    : null\n  var m0 = _vm.isload ? _vm.t(\"会员\") : null\n  var m1 = _vm.isload && _vm.detail.leveldk_money > 0 ? _vm.t(\"会员\") : null\n  var m2 = _vm.isload && _vm.detail.couponmoney > 0 ? _vm.t(\"优惠券\") : null\n  var m3 = _vm.isload && _vm.detail.scoredk > 0 ? _vm.t(\"积分\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./takeawayorderdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./takeawayorderdetail.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<view class=\"ordertop\" :style=\"'background:url(' + pre_url + '/static/img/ordertop.png);background-size:100%'\">\n\t\t\t<view class=\"f1\" v-if=\"detail.status==0\">\n\t\t\t\t<view class=\"t1\">等待买家付款</view>\n\t\t\t\t<view class=\"t2\" v-if=\"djs\">剩余时间：{{djs}}</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==1\">\n\t\t\t\t<view class=\"t1\">{{detail.paytypeid==4 ? '已选择'+detail.paytype : '已成功付款'}}</view>\n\t\t\t\t<view class=\"t2\">请尽快接单</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==12\">\n\t\t\t\t<view class=\"t1\">{{detail.paytypeid==4 ? '已选择'+detail.paytype : '已成功付款'}}</view>\n\t\t\t\t<view class=\"t2\" v-if=\"detail.freight_type!=1\">请尽快发货</view>\n\t\t\t\t<view class=\"t2\" v-if=\"detail.freight_type==1\">待提货</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==2\">\n\t\t\t\t<view class=\"t1\">订单已发货</view>\n\t\t\t\t<view class=\"t2\" v-if=\"detail.freight_type!=3 && detail.freight_type!=2\">发货信息：{{detail.express}} {{detail.express_no}}</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==3\">\n\t\t\t\t<view class=\"t1\">订单已完成</view>\n\t\t\t</view>\n\t\t\t<view class=\"f1\" v-if=\"detail.status==4\">\n\t\t\t\t<view class=\"t1\">订单已取消</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"address\">\n\t\t\t<view class=\"img\">\n\t\t\t\t<image :src=\"pre_url+'/static/img/address3.png'\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"info\">\n\t\t\t\t<text class=\"t1\">{{detail.linkman}} {{detail.tel}}</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type!=1 && detail.freight_type!=3\">地址：{{detail.area}}{{detail.address}}</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.freight_type==1\" @tap=\"openLocation\" :data-address=\"storeinfo.address\" :data-latitude=\"storeinfo.latitude\" :data-longitude=\"storeinfo.longitude\">取货地点：{{storeinfo.name}} - {{storeinfo.address}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"product\">\n\t\t\t<view v-for=\"(item, idx) in prolist\" :key=\"idx\" class=\"content\">\n\t\t\t\t<view>\n\t\t\t\t\t<image :src=\"item.pic\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t<text class=\"t1\">{{item.name}}</text>\n\t\t\t\t\t<text class=\"t2\">{{item.ggname}}{{item.jltitle?item.jltitle:''}}</text>\n\t\t\t\t\t<view class=\"t3\">\n\t\t\t\t\t\t\n\t\t\t\t\t\t<text class=\"x1 flex1\" v-if=\"item.jlprice\">￥{{  parseFloat(parseFloat(item.sell_price)+parseFloat(item.jlprice)).toFixed(2) }}</text>\n\t\t\t\t\t\t<text class=\"x1 flex1\" v-else>￥{{item.sell_price}}</text>\n\t\t\t\t\t\t<block v-if=\"goods_hexiao_status && (detail.status==1 || detail.status==2 || detail.status==12) && detail.freight_type==1 && item.hexiao_code && item.num>0\">\n\t\t\t\t\t\t    <view>\n                                <view v-if=\"item.status == 3\" style=\"color: #999;\">已核销</view>\n\t\t\t\t\t\t        <view v-else style=\"color: #f60;\">未核销</view>\n\t\t\t\t\t\t        \n\t\t\t\t\t\t    </view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<text class=\"x2\">×{{item.num}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo\" v-if=\"(detail.status==3 || detail.status==2) && (detail.freight_type==3 || detail.freight_type==4)\">\n\t\t\t<view class=\"item flex-col\">\n\t\t\t\t<text class=\"t1\" style=\"color:#111\">发货信息</text>\n\t\t\t\t<text class=\"t2\" style=\"text-align:left;margin-top:10rpx;padding:0 10rpx\" user-select=\"true\" selectable=\"true\">{{detail.freight_content}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单人</text>\n\t\t\t\t<text class=\"flex1\"></text>\n\t\t\t\t<image :src=\"detail.headimg\" style=\"width:80rpx;height:80rpx;margin-right:8rpx\"/>\n\t\t\t\t<text  style=\"height:80rpx;line-height:80rpx\">{{detail.nickname}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}ID</text>\n\t\t\t\t<text class=\"t2\">{{detail.mid}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\" v-if=\"detail.remark\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">备注</text>\n\t\t\t\t<text class=\"t2\">{{detail.remark}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单编号</text>\n\t\t\t\t<text class=\"t2\" user-select=\"true\" selectable=\"true\">{{detail.ordernum}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">下单时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytypeid!='4' && detail.paytime\">\n\t\t\t\t<text class=\"t1\">支付时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>0 && detail.paytime\">\n\t\t\t\t<text class=\"t1\">支付方式</text>\n\t\t\t\t<text class=\"t2\">{{detail.paytype}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status>1 && detail.send_time\">\n\t\t\t\t<text class=\"t1\">发货时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.send_time}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.status==3 && detail.collect_time\">\n\t\t\t\t<text class=\"t1\">收货时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.collect_time}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">商品金额</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.product_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.leveldk_money > 0\">\n\t\t\t\t<text class=\"t1\">{{t('会员')}}折扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.leveldk_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.jianmoney > 0\">\n\t\t\t\t<text class=\"t1\">满减活动</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.manjian_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">配送方式</text>\n\t\t\t\t<text class=\"t2\">{{detail.freight_text}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.freight_type==1 && detail.freightprice > 0\">\n\t\t\t\t<text class=\"t1\">服务费</text>\n\t\t\t\t<text class=\"t2 red\">+¥{{detail.freight_price}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.freight_time\">\n\t\t\t\t<text class=\"t1\">{{detail.freight_type!=1?'配送':'提货'}}时间</text>\n\t\t\t\t<text class=\"t2\">{{detail.freight_time}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.couponmoney > 0\">\n\t\t\t\t<text class=\"t1\">{{t('优惠券')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.coupon_money}}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"item\" v-if=\"detail.scoredk > 0\">\n\t\t\t\t<text class=\"t1\">{{t('积分')}}抵扣</text>\n\t\t\t\t<text class=\"t2 red\">-¥{{detail.scoredk_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">{{detail.status ==0?'应付':'实付'}}款</text>\n\t\t\t\t<text class=\"t2 red\">¥{{detail.totalprice}}</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">订单状态</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0\">未付款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==1\">已付款</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==2\">已发货</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==3\">已收货</text>\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==4\">已关闭</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\n\t\t\t\t<text class=\"t1\">退款状态</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==1\">审核中,¥{{detail.refund_money}}</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==2\">已退款,¥{{detail.refund_money}}</text>\n\t\t\t\t<text class=\"t2 red\" v-if=\"detail.refund_status==3\">已驳回,¥{{detail.refund_money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_status>0\">\n\t\t\t\t<text class=\"t1\">退款原因</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_reason}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.refund_checkremark\">\n\t\t\t\t<text class=\"t1\">审核备注</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.refund_checkremark}}</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">备注</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.message ? detail.message : '无'}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.field1\">\n\t\t\t\t<text class=\"t1\">{{detail.field1data[0]}}</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.field1data[1]}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.field2\">\n\t\t\t\t<text class=\"t1\">{{detail.field2data[0]}}</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.field2data[1]}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.field3\">\n\t\t\t\t<text class=\"t1\">{{detail.field3data[0]}}</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.field3data[1]}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.field4\">\n\t\t\t\t<text class=\"t1\">{{detail.field4data[0]}}</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.field4data[1]}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"detail.field5\">\n\t\t\t\t<text class=\"t1\">{{detail.field5data[0]}}</text>\n\t\t\t\t<text class=\"t2 red\">{{detail.field5data[1]}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item flex-col\" v-if=\"(detail.status==1 || detail.status==12 || detail.status==2) && detail.freight_type==1\">\n\t\t\t\t<text class=\"t1\">核销码</text>\n\t\t\t\t<view class=\"flex-x-center\">\n\t\t\t\t\t<image :src=\"detail.hexiao_qr\" style=\"width:400rpx;height:400rpx\" @tap=\"previewImage\" :data-url=\"detail.hexiao_qr\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view style=\"width:100%;height:120rpx\"></view>\n\n\t\t<view class=\"bottom\">\n\t\t\t<view v-if=\"detail.refund_status==1\" class=\"btn2\" @tap=\"refundnopass\" :data-id=\"detail.id\">退款驳回</view>\n\t\t\t<view v-if=\"detail.refund_status==1\" class=\"btn2\" @tap=\"refundpass\" :data-id=\"detail.id\">退款通过</view>\n\t\t\t<view v-if=\"detail.status==0\" class=\"btn2\" @tap=\"closeOrder\" :data-id=\"detail.id\">关闭订单</view>\n\t\t\t<view v-if=\"detail.status==0 && detail.bid==0\" class=\"btn2\" @tap=\"ispay\" :data-id=\"detail.id\">改为已支付</view>\n\t\t\t<view v-if=\"detail.status==1\" class=\"btn2\" @tap=\"refund\" :data-id=\"detail.id\">拒单退款</view>\n\t\t\t<view v-if=\"detail.status==1\" class=\"btn2\" @tap=\"jiedan\" :data-id=\"detail.id\">接单</view>\n\t\t\t<view v-if=\"detail.status==12\" class=\"btn2\" @tap=\"fahuo\" :data-id=\"detail.id\">发货</view>\n\t\t\t<block v-if=\"detail.status==12 && detail.canpeisong\">\n\t\t\t\t<view class=\"btn2\" v-if=\"detail.express_wx_status\" @tap=\"peisongWx\" :data-id=\"detail.id\">即时配送</view>\n        <view class=\"btn2\" v-else-if=\"detail.myt_status\"   @tap=\"peisongMyt\" :data-id=\"detail.id\">麦芽田配送</view>\n\t\t\t\t<view class=\"btn2\" v-else @tap=\"peisong\" :data-id=\"detail.id\">配送</view>\n\t\t\t</block>\n\t\t\t<view v-if=\"detail.status==2 || detail.status==3\" class=\"btn2\" @tap=\"goto\" :data-url=\"'/pagesExt/order/logistics?express_com='+detail.express_com+'&express_no='+detail.express_no+'&type='+detail.express_type\">查物流</view>\n\t\t\t<view v-if=\"detail.status==4\" class=\"btn2\" @tap=\"delOrder\" :data-id=\"detail.id\">删除</view>\n\t\t\t<view class=\"btn2\" @tap=\"setremark\" :data-id=\"detail.id\">设置备注</view>\n\t\t\t<view class=\"btn2\" @tap=\"print\" :data-id=\"detail.id\">打印小票</view>\n\t\t\t<view class=\"btn2\" v-if=\"shopset.is_refund &&( detail.status==1 || detail.status==2 || detail.status==3 || detail.status==12)\" @tap=\"refundinit\" :data-id=\"detail.id\">退款</view>\n\t\t</view>\n\t\t<uni-popup id=\"dialogSetremark\" ref=\"dialogSetremark\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"设置备注\" :value=\"detail.remark\" placeholder=\"请输入备注\" @confirm=\"setremarkconfirm\"></uni-popup-dialog>\n\t\t</uni-popup>\n\n\t\t<uni-popup id=\"dialogExpress\" ref=\"dialogExpress\" type=\"dialog\">\n\t\t\t<view class=\"uni-popup-dialog\">\n\t\t\t\t<view class=\"uni-dialog-title\">\n\t\t\t\t\t<text class=\"uni-dialog-title-text\">发货</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-content\">\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx\">\n\t\t\t\t\t\t\t<text style=\"font-size:28rpx;color:#000\">快递公司：</text>\n\t\t\t\t\t\t\t<picker @change=\"expresschange\" :value=\"express_index\" :range=\"expressdata\" style=\"font-size:28rpx;border: 1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1\">\n\t\t\t\t\t\t\t\t<view class=\"picker\">{{expressdata[express_index]}}</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t</view> \n\t\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n\t\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">快递单号：</view>\n\t\t\t\t\t\t\t<input type=\"text\" placeholder=\"请输入快递单号\" @input=\"setexpressno\" style=\"border: 1px #eee solid;padding: 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-button-group\">\n\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogExpressClose\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"confirmfahuo\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t<uni-popup id=\"dialogPeisong\" ref=\"dialogPeisong\" type=\"dialog\">\n\t\t\t<view class=\"uni-popup-dialog\">\n\t\t\t\t<view class=\"uni-dialog-title\">\n\t\t\t\t\t<text class=\"uni-dialog-title-text\">请选择配送员</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-content\">\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<picker @change=\"peisongChange\" :value=\"index2\" :range=\"peisonguser2\" style=\"font-size:28rpx;border: 1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1\">\n\t\t\t\t\t\t\t<view class=\"picker\">{{peisonguser2[index2]}}</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-button-group\">\n\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogPeisongClose\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"confirmPeisong\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n        \n    <uni-popup id=\"dialogExpress11\" ref=\"dialogExpress11\" type=\"dialog\">\n      <view class=\"uni-popup-dialog\">\n        <view class=\"uni-dialog-title\">\n          <text class=\"uni-dialog-title-text\">配送设置</text>\n        </view>\n        <view class=\"uni-dialog-content\" style=\"display: block;\">\n          <block v-if=\"mytdata\">\n            <block v-if=\"!mytdata.msg\">\n                <scroll-view scroll-Y=\"true\" style=\"width: 100%;max-height: 500rpx;\">\n                  <block  v-for=\"item in mytdata.detail\" :key=\"index\">\n                    <view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n                      <view style=\"font-size:28rpx;color:#555;width: 130rpx;\">{{item.name}}：</view>\n                      <view style=\"padding: 10rpx;flex:1;font-size:28rpx;line-height: 40rpx;\">\n                        <block v-if=\"!item.error_message\">\n                          <view>配送费：{{item.amount}}元</view> \n                          <view>距离：{{item.distance}}米</view>\n                        </block>\n                        <block v-else>\n                          <view>计价失败原因：{{item.error_message}}</view>\n                        </block>\n                      </view>\n                    </view>\n                  </block>\n                </scroll-view>\n                <view v-if=\"detail.myt_shop\" class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n                  <view style=\"font-size:28rpx;color:#555;width: 130rpx;\">门店：</view>\n                    <picker @change=\"mytshopChange\" :value=\"mytindex\" :range=\"detail.myt_shoplist\"  range-key='name' style=\"font-size:28rpx;border: 1px #eee solid;padding:0 10rpx;height:70rpx;border-radius:4px;flex:1;line-height: 52rpx;\">\n                      <view class=\"picker\">{{detail.myt_shoplist[mytindex]['name']}}</view>\n                    </picker>\n                </view>\n                <view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n                  <view style=\"font-size:28rpx;color:#555;width: 130rpx;\">重量：</view>\n                  <input type=\"text\" placeholder=\"请输入重量(选填)\" @input=\"mytWeight\" :value=\"myt_weight\" style=\"border: 1px #eee solid;padding:0 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n                  kg\n                </view>\n                <view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n                  <view style=\"font-size:28rpx;color:#555;width: 130rpx;\">备注：</view>\n                  <input type=\"text\" placeholder=\"请输入备注(选填)\" @input=\"mytRemark\" style=\"border: 1px #eee solid;padding:0 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;\"/>\n                </view>\n            </block>\n            <block  v-else>\n              <view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n                <view style=\"font-size:28rpx;color:#555\">错误信息：</view>\n                <view style=\"padding: 10rpx;flex:1;font-size:28rpx;line-height: 40rpx;\">{{mytdata.msg}}</view>\n              </view>\n            </block>\n          </block>\n          <block v-else>\n            <view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;\">\n              <view style=\"font-size:28rpx;color:#555\">错误：</view>\n              <view style=\"padding: 10rpx;height:70rpx;flex:1;font-size:28rpx;\">无数据返回</view>\n            </view>\n          </block>\n        </view>\n        <view class=\"uni-dialog-button-group\">\n          <view class=\"uni-dialog-button\" @click=\"dialogExpress11Close\">\n            <text class=\"uni-dialog-button-text\">取消</text>\n          </view>\n          <view class=\"uni-dialog-button uni-border-left\" @click=\"confirmfahuo11\">\n            <text class=\"uni-dialog-button-text uni-button-color\">确定</text>\n          </view>\n        </view>\n      </view>\n    </uni-popup>\n\t<uni-popup id=\"dialogRefund\" ref=\"dialogRefund\" type=\"dialog\" :mask-click=\"false\">\n\t\t<view class=\"uni-popup-dialog\">\n\t\t\t<view class=\"uni-dialog-title\">\n\t\t\t\t<text class=\"uni-dialog-title-text\">退款</text>\n\t\t\t</view>\n\t\t\t<view class=\"uni-dialog-content\">\n\t\t\t\t<view>\n\t\t\t\t\t<view class=\"product\" style=\"width: 100%;margin:0;padding:0\">\n\t\t\t\t\t\t<scroll-view class=\"popup-content\" scroll-y=\"true\" style=\"max-height: 600rpx;overflow: hidden;\">\n\t\t\t\t\t\t<view v-for=\"(item, idx) in returnProlist\" :key=\"idx\" class=\"box\">\n\t\t\t\t\t\t\t<view class=\"content\">\n\t\t\t\t\t\t\t\t<view @tap=\"goto\" :data-url=\"'/pages/shop/product?id=' + item.proid\">\n\t\t\t\t\t\t\t\t\t<image :src=\"item.pic\" style=\"width: 110rpx;height: 110rpx\"></image>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"detail\">\n\t\t\t\t\t\t\t\t\t<text class=\"t1\">{{item.name}}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"t2\">{{item.ggname}}</text>\n\t\t\t\t\t\t\t\t\t<view class=\"t3\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"x1 flex1\">￥{{item.sell_price}}×{{item.num}}</text>\n\t\t\t\t\t\t\t\t\t\t<view style=\"color: #888;font-size: 24rpx;display: flex;\">\n\t\t\t\t\t\t\t\t\t\t\t<text>退货数量</text>\n\t\t\t\t\t\t\t\t\t\t\t<input type=\"number\" :value=\"item.can_num\" :data-max=\"item.can_num\" :data-ogid=\"item.id\" @input=\"retundInput\" class=\"retundNum\" style=\"border: 1px #eee solid;width: 80rpx;margin-left: 10rpx;text-align: center;\">\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;height: 80rpx;\">\n\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">退款原因：</view>\n\t\t\t\t\t\t<input type=\"text\" placeholder=\"请输入退款原因\" @input=\"refundMoneyReason\" adjust-position=\"false\" style=\"border: 1px #eee solid;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;padding:0 10rpx\"/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex-y-center flex-x-center\" style=\"margin:20rpx 20rpx;height: 80rpx\">\n\t\t\t\t\t\t<view style=\"font-size:28rpx;color:#555\">退款金额：</view>\n\t\t\t\t\t\t<input type=\"text\" placeholder=\"请输入退款金额\" @input=\"refundMoney\" adjust-position=\"false\" :value=\"refundTotalprice\" style=\"border: 1px #eee solid;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;padding:0 10rpx\"/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"uni-dialog-button-group\">\n\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogRefundClose\">\n\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"gotoRefundMoney()\">\n\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</uni-popup>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n</view>\n</template>\n\n<script>\nvar app = getApp();\nvar interval = null;\n\nexport default {\n  data() {\n    return {\n        opt:{},\n        loading:false,\n        isload: false,\n        menuindex:-1,\n\n        pre_url:app.globalData.pre_url,\n        expressdata:[],\n        express_index:0,\n        express_no:'',\n        prodata: '',\n        djs: '',\n        detail: \"\",\n        prolist: \"\",\n        shopset: \"\",\n        storeinfo: \"\",\n        lefttime: \"\",\n        codtxt: \"\",\n        peisonguser:[],\n        peisonguser2:[],\n        index2:0,\n        goods_hexiao_status:false,\n        \n        myt_weight:'',\n        myt_remark:'',\n        mytindex:0,\n        myt_shop_id:0,\n        mytdata:'',\n\t\treturnProlist:[], //退款商品\n\t\trefundNum:[],//退款数据\n\t\trefundTotalprice:0,//退款总金额\n\t\trefundReason:''//退款理由\n    };\n  },\n\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onUnload: function () {\n    clearInterval(interval);\n  },\n  methods: {\n\t\tgetdata: function () {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.get('ApiAdminRestaurantTakeawayOrder/detail', {id: that.opt.id}, function (res) {\n\t\t\t\tthat.loading = false;\n                if(res.goods_hexiao_status){\n                    that.goods_hexiao_status = true\n                }\n\t\t\t\tthat.expressdata = res.expressdata;\n\t\t\t\tthat.detail = res.detail;\n\t\t\t\tthat.prolist = res.prolist;\n\t\t\t\tthat.shopset = res.shopset;\n\t\t\t\tthat.storeinfo = res.storeinfo;\n\t\t\t\tthat.lefttime = res.lefttime;\n\t\t\t\tthat.codtxt = res.codtxt;\n\t\t\t\tif (res.lefttime > 0) {\n\t\t\t\t\tinterval = setInterval(function () {\n\t\t\t\t\t\tthat.lefttime = that.lefttime - 1;\n\t\t\t\t\t\tthat.getdjs();\n\t\t\t\t\t}, 1000);\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    getdjs: function () {\n      var that = this;\n      var totalsec = that.lefttime;\n\n      if (totalsec <= 0) {\n        that.djs = '00时00分00秒';\n      } else {\n        var houer = Math.floor(totalsec / 3600);\n        var min = Math.floor((totalsec - houer * 3600) / 60);\n        var sec = totalsec - houer * 3600 - min * 60;\n        var djs = (houer < 10 ? '0' : '') + houer + '时' + (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';\n        that.djs = djs;\n      }\n    },\n\t\tsetremark:function(){\n\t\t\tthis.$refs.dialogSetremark.open();\n\t\t},\n\t\tsetremarkconfirm: function (done, remark) {\n\t\t\tthis.$refs.dialogSetremark.close();\n\t\t\tvar that = this\n\t\t\tapp.post('ApiAdminOrder/setremark', { type:'restaurant_takeaway',orderid: that.detail.id,content:remark }, function (res) {\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n    },\n\t\tfahuo:function(){\n\t\t\tthis.$refs.dialogExpress.open();\n\t\t},\n\t\tdialogExpressClose:function(){\n\t\t\tthis.$refs.dialogExpress.close();\n\t\t},\n\t\texpresschange:function(e){\n\t\t\tthis.express_index = e.detail.value;\n\t\t},\n\t\tsetexpressno:function(e){\n\t\t\tthis.express_no = e.detail.value;\n\t\t},\n\t\tconfirmfahuo:function(){\n\t\t\tthis.$refs.dialogExpress.close();\n\t\t\tvar that = this\n\t\t\tvar express_com = this.expressdata[this.express_index]\n\t\t\tapp.post('ApiAdminOrder/sendExpress', { type:'restaurant_takeaway',orderid: that.detail.id,express_no:that.express_no,express_com:express_com}, function (res) {\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n\t\t},\n\t\tispay:function(e){\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要改为已支付吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/ispay', { type:'restaurant_takeaway',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\tjiedan:function(e){\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要接单吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/jiedan', { type:'restaurant_takeaway',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\trefund: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要拒单并退款吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/judan', { type:'restaurant_takeaway',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\tdelOrder: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.showLoading('删除中');\n\t\t\tapp.confirm('确定要删除该订单吗?', function () {\n\t\t\t\tapp.post('ApiAdminOrder/delOrder', { type:'restaurant_takeaway',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tapp.goto('takeawayorder');\n\t\t\t\t\t}, 1000)\n\t\t\t\t});\n\t\t\t})\n\t\t},\n\t\tcloseOrder: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要关闭该订单吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/closeOrder', { type:'restaurant_takeaway',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function (){\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t});\n\t\t\t})\n\t\t},\n\t\trefundnopass: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要驳回退款申请吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/refundnopass', { type:'restaurant_takeaway',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\trefundpass: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.confirm('确定要审核通过并退款吗?', function () {\n\t\t\t\tapp.showLoading('提交中');\n\t\t\t\tapp.post('ApiAdminOrder/refundpass', { type:'restaurant_takeaway',orderid: orderid }, function (data) {\n\t\t\t\t\tapp.showLoading(false);\n\t\t\t\t\tapp.success(data.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t});\n\t\t},\n\t\tprint: function (e) {\n\t\t\tvar that = this;\n\t\t\tvar orderid = e.currentTarget.dataset.id\n\t\t\tapp.showLoading('打印中');\n\t\t\tapp.post('ApiAdminOrder/print', { type:'restaurant_takeaway',orderid: orderid }, function (data) {\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tapp.success(data.msg);\n\t\t\t})\n\t\t},\n\t\tpeisong:function(){\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.post('ApiAdminOrder/getpeisonguser',{type:'restaurant_takeaway_order',orderid:that.detail.id},function(res){\n\t\t\t\tthat.loading = false;\n\t\t\t\tvar peisonguser = res.peisonguser\n\t\t\t\tvar paidantype = res.paidantype\n\t\t\t\tvar psfee = res.psfee\n\t\t\t\tvar ticheng = res.ticheng\n\n\t\t\t\tvar peisonguser2 = [];\n\t\t\t\tfor(var i in peisonguser){\n\t\t\t\t\tpeisonguser2.push(peisonguser[i].title);\n\t\t\t\t}\n\t\t\t\tthat.peisonguser = res.peisonguser;\n\t\t\t\tthat.peisonguser2 = peisonguser2;\n\t\t\t\tif(paidantype==1){\n\t\t\t\t\tthat.$refs.dialogPeisong.open();\n\t\t\t\t}else{\n\t\t\t\t\tif(that.detail.bid == 0){\n\t\t\t\t\t\tvar tips='选择配送员配送，订单将发布到抢单大厅由配送员抢单，配送员提成￥'+ticheng+'，确定要配送员配送吗？';\n\t\t\t\t\t}else{\n\t\t\t\t\t\tvar tips='选择配送员配送，订单将发布到抢单大厅由配送员抢单，需扣除配送费￥'+psfee+'，确定要配送员配送吗？';\n\t\t\t\t\t}\n\t\t\t\t\tif(paidantype == 2){\n\t\t\t\t\t\tvar psid = '-1';\n\t\t\t\t\t}else{\n\t\t\t\t\t\tvar psid = '0';\n\t\t\t\t\t}\n\t\t\t\t\tapp.confirm(tips,function(){\n\t\t\t\t\t\tapp.post('ApiAdminOrder/peisong', { type:'restaurant_takeaway_order',orderid: that.detail.id,psid:psid}, function (res) {\n\t\t\t\t\t\t\tapp.success(res.msg);\n\t\t\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t\t\t}, 1000)\n\t\t\t\t\t\t})\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\tdialogPeisongClose:function(){\n\t\t\tthis.$refs.dialogPeisong.close();\n\t\t},\n\t\tpeisongChange:function(e){\n\t\t\tthis.index2 = e.detail.value;\n\t\t},\n\t\tconfirmPeisong:function(){\n\t\t\tvar that = this\n\t\t\tvar psid = this.peisonguser[this.index2].id\n\t\t\tapp.post('ApiAdminOrder/peisong', { type:'restaurant_takeaway_order',orderid: that.detail.id,psid:psid}, function (res) {\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tthat.$refs.dialogPeisong.close();\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n\t\t},\n\t\tpeisongWx:function(){\n\t\t\tvar that = this;\n\t\t\tvar psfee = that.detail.freight_price;\n\t\t\tif(that.detail.bid == 0){\n\t\t\t\tvar tips='选择即时配送，订单将派单到第三方配送平台，并扣除相应费用，确定要派单吗？';\n\t\t\t}else{\n\t\t\t\tvar tips='选择即时配送，订单将派单到第三方配送平台，需扣除配送费￥'+psfee+'，确定要派单吗？';\n\t\t\t}\n\t\t\tapp.confirm(tips,function(){\n\t\t\t\tthat.loading = true;\n\t\t\t\tapp.post('ApiAdminOrder/peisongWx', { type:'restaurant_takeaway_order',orderid: that.detail.id}, function (res) {\n\t\t\t\t\tthat.loading = false;\n\t\t\t\t\tapp.success(res.msg);\n\t\t\t\t\tsetTimeout(function () {\n\t\t\t\t\t\tthat.getdata();\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t})\n\t\t},\n    peisongMyt:function(e){\n        var that = this;\n        var detail = that.detail;\n        if(detail.myt_set){\n          var data = {\n              type:'restaurant_takeaway_order',\n              orderid: detail.id,\n          }\n          app.post('ApiAdminOrder/mytprice', data, function (res) {\n              that.loading = false;\n              if(res.status == 1){\n                  that.mytdata = res.data;\n                  if(res.data.weight){\n                    that.myt_weight = res.data.weight\n                  }\n                  that.$refs.dialogExpress11.open();\n              }else{\n                  app.alert(res.msg)\n              }\n          })\n        }\n    },\n    goMyt:function(){\n        var that = this;\n        var detail = that.detail;\n        var tips='选择麦芽田配送，订单将派单到第三方配送平台，并扣除相应费用，确定要派单吗？';\n        app.confirm(tips,function(){\n            that.$refs.dialogExpress11.close();\n            that.loading = true;\n            var data = {\n                type:'restaurant_takeaway_order',\n                orderid: detail.id,\n                myt_weight:that.myt_weight,\n                myt_remark:that.myt_remark,\n                myt_shop_id:that.myt_shop_id\n            }\n            app.post('ApiAdminOrder/peisong', data, function (res) {\n                that.loading = false;\n                if(res.status == 1){\n                    app.success(res.msg);\n                    setTimeout(function () {\n                        that.getdata();\n                    }, 1000)\n                }else{\n                    app.alert(res.msg)\n                }\n                \n            })\n        })\n    },\n    confirmfahuo11:function(){\n      var that = this\n      that.goMyt();\n    },\n    dialogExpress11Close:function(){\n      this.$refs.dialogExpress11.close();\n    },\n    mytWeight:function(e){\n      this.myt_weight = e.detail.value;\n    },\n    mytRemark:function(e){\n      this.myt_remark = e.detail.value;\n    },\n    mytshopChange:function(e){\n        var that = this;\n        var detail   = that.detail;\n        var mytindex = e.detail.value;\n        that.mytindex = mytindex;\n        //that.myt_name  = detail.myt_shoplist[mytindex]['name'];\n        that.myt_shop_id    = detail.myt_shoplist[mytindex]['id'];\n    },\n\trefundinit:function(e){\n\t\tvar that = this;\n\t\tthat.loading = true;\n\t\tapp.post('ApiAdminRestaurantTakeawayOrder/refundProlist',{orderid:that.detail.id},function(data){\n\t\t\t\n\t\t\tlet prolist = data.prolist;\n\t\t\tthat.returnProlist = data.prolist;\n\t\t\tthat.refundTotalprice = data.detail.totalprice;\n\t\t\t\n\t\t\tfor(var i in prolist){\n\t\t\t\tthat.refundNum.push({\n\t\t\t\t\t'ogid':prolist[i].id,\n\t\t\t\t\t'num':prolist[i].can_num\n\t\t\t\t})\n\t\t\t}\n\t\t\tthat.loading = false;\n\t\t\tthat.$refs.dialogRefund.open();\n\t\t\t\n\t\t})\n\t},\n\tretundInput:function(e){\n\t\tvar that = this;\n\t\tvar valnum = e.detail.value;\n\t\tvar {max,ogid} = e.currentTarget.dataset;\n\t\tvar prolist = that.returnProlist;\n\t\tvar refundNum = that.refundNum;\n\t\tvar total = 0;\n\t\tvalnum = !valnum?0:valnum;\n\t\tif(valnum > max){\n\t\t\treturn app.error('请输入正确的数量');\n\t\t}\n\t\tfor(var i in refundNum){\n\t\t\tif(refundNum[i].ogid == ogid){\n\t\t\t\trefundNum[i].num = valnum;\n\t\t\t}\n\t\t\tconsole.log(prolist[i]);\n\t\t\tvar pro = prolist[i];\n\t\t\tif(refundNum[i].num == pro.num){\n\t\t\t\ttotal += parseFloat(prolist[i].real_totalprice)\n\t\t\t}else{\n\t\t\t\ttotal += refundNum[i].num * parseFloat(prolist[i].real_totalprice) / prolist[i].num \n\t\t\t}\n\t\t}\n\t\ttotal = parseFloat(total);\n\t\ttotal = total.toFixed(2);\n\t\tthat.refundTotalprice = total; \n\t},\n\trefundMoneyReason:function(e){\n\t\tthis.refundReason = e.detail.value;\n\t},\n\trefundMoney:function(e){\n\t\tthis.refundTotalprice = e.detail.value;\n\t},\n\tdialogRefundClose:function(){\n\t\tthis.returnProlist = [];\n\t\tthis.refundReason = '';\n\t\tthis.$refs.dialogRefund.close();\n\t},\n\tgotoRefundMoney:function(){\n\t\tvar that = this;\n\t\tconsole.log(that.refundNum,11111);\n\t\tapp.confirm('确定要退款吗?', function () {\n\t\t\tthat.$refs.dialogRefund.close();\n\t\t\tapp.showLoading('提交中');\n\t\t\tapp.post('ApiAdminRestaurantTakeawayOrder/refund',{\n\t\t\t\torderid:that.detail.id,\n\t\t\t\trefundNum:that.refundNum,\n\t\t\t\treason:that.refundReason,\n\t\t\t\tmoney:that.refundTotalprice\n\t\t\t},function(res){\n\t\t\t\tif(res.status == 0){\n\t\t\t\t\tapp.error(res.msg);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tapp.showLoading(false);\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t\tthat.refundinit();\n\t\t\t\t}, 1000)\n\t\t\t})\n\t\t});\n\t},\n  }\n};\n</script>\n<style>\n.ordertop{width:100%;height:220rpx;padding:50rpx 0 0 70rpx}\n.ordertop .f1{color:#fff}\n.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}\n.ordertop .f1 .t2{font-size:24rpx}\n\n.address{ display:flex;width: 100%; padding: 20rpx 3%; background: #FFF;margin-bottom:20rpx;}\n.address .img{width:40rpx}\n.address image{width:40rpx; height:40rpx;}\n.address .info{flex:1;display:flex;flex-direction:column;}\n.address .info .t1{font-size:28rpx;font-weight:bold;color:#333}\n.address .info .t2{font-size:24rpx;color:#999}\n\n.product{width:100%; padding: 14rpx 3%;background: #FFF;}\n.product .content{display:flex;position:relative;width: 100%; padding:16rpx 0px;border-bottom: 1px #e5e5e5 dashed;}\n.product .content:last-child{ border-bottom: 0; }\n.product .content image{ width: 140rpx; height: 140rpx;}\n.product .content .detail{display:flex;flex-direction:column;margin-left:14rpx;flex:1}\n.product .content .detail .t1{font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}\n.product .content .detail .t2{height: 46rpx;line-height: 46rpx;color: #999;overflow: hidden;font-size: 26rpx;}\n.product .content .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}\n.product .content .detail .x1{ flex:1}\n.product .content .detail .x2{ width:100rpx;font-size:32rpx;text-align:right;margin-right:8rpx}\n.product .content .comment{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc702 solid; border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.product .content .comment2{position:absolute;top:64rpx;right:10rpx;border: 1px #ffc7c2 solid; border-radius:10rpx;background:#fff; color: #ffc7c2;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.orderinfo{ width:100%;margin-top:10rpx;padding: 14rpx 3%;background: #FFF;}\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}\n.orderinfo .item:last-child{ border-bottom: 0;}\n.orderinfo .item .t1{width:200rpx;}\n.orderinfo .item .t2{flex:1;text-align:right}\n.orderinfo .item .red{color:red}\n\n.bottom{ width: 100%; padding: 16rpx 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}\n\n.btn1{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}\n.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n.btn3{position:absolute;top:60rpx;right:10rpx;font-size:24rpx;width:120rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\n\n.btitle{ width:100%;height:100rpx;background:#fff;padding:0 20rpx;border-bottom:1px solid #f5f5f5}\n.btitle .comment{border: 1px #ffc702 solid;border-radius:10rpx;background:#fff; color: #ffc702;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n.btitle .comment2{border: 1px #ffc7c0 solid;border-radius:10rpx;background:#fff; color: #ffc7c0;  padding: 0 10rpx; height: 46rpx; line-height: 46rpx;}\n\n.picker{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\n\n.uni-popup-dialog {width: 300px;border-radius: 5px;background-color: #fff;}\n.uni-dialog-title {display: flex;flex-direction: row;justify-content: center;padding-top: 15px;padding-bottom: 5px;}\n.uni-dialog-title-text {font-size: 16px;font-weight: 500;}\n.uni-dialog-content {display: flex;flex-direction: row;justify-content: center;align-items: center;padding: 5px 15px 15px 15px;}\n.uni-dialog-content-text {font-size: 14px;color: #6e6e6e;}\n.uni-dialog-button-group {display: flex;flex-direction: row;border-top-color: #f5f5f5;border-top-style: solid;border-top-width: 1px;}\n.uni-dialog-button {display: flex;flex: 1;flex-direction: row;justify-content: center;align-items: center;height: 45px;}\n.uni-border-left {border-left-color: #f0f0f0;border-left-style: solid;border-left-width: 1px;}\n.uni-dialog-button-text {font-size: 14px;}\n.uni-button-color {color: #007aff;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./takeawayorderdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./takeawayorderdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213990649\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}