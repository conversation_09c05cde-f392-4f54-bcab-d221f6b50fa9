{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/shoporderEdit.vue?5160", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/shoporderEdit.vue?eadf", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/shoporderEdit.vue?af8c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/shoporderEdit.vue?71cf", "uni-app:///admin/restaurant/shoporderEdit.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/shoporderEdit.vue?2b88", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/restaurant/shoporderEdit.vue?ffdd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "loading", "pre_url", "info", "ordergoods", "onLoad", "methods", "getdata", "that", "app", "id", "ginput", "total", "totalprice", "input", "subform", "goods", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvDA;AAAA;AAAA;AAAA;AAA20B,CAAgB,2yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgG/1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QACAA;MACA;IACA;IACAG;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAF;QAAA;MACA;MACAL;MACA;QACA;UACAQ;QACA;UACAA;QACA;MACA;MAEAJ;MACA;MACAK;MACAA;MACA;QACAJ;QAAA;MACA;MAEAD;IACA;IACAM;MACA;MACA;MACA;MACA;QACAL;QAAA;MACA;MACAD;MAEA;MACAK;MACAA;MACA;QACAJ;QAAA;MACA;MACAD;IACA;IACAO;MACA;MACA;MACA;QACAN;QAAA;MACA;MACA;MACAA;QAAAC;QAAAP;QAAAa;MAAA;QACA;UACAP;QACA;UACAA;UACAQ;YACAR;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC9LA;AAAA;AAAA;AAAA;AAAwrC,CAAgB,wmCAAG,EAAC,C;;;;;;;;;;;ACA5sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/restaurant/shoporderEdit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/restaurant/shoporderEdit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./shoporderEdit.vue?vue&type=template&id=fd59c684&\"\nvar renderjs\nimport script from \"./shoporderEdit.vue?vue&type=script&lang=js&\"\nexport * from \"./shoporderEdit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shoporderEdit.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/restaurant/shoporderEdit.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporderEdit.vue?vue&type=template&id=fd59c684&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.ordergoods, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 = item.ggtext && item.ggtext.length\n        return {\n          $orig: $orig,\n          g0: g0,\n        }\n      })\n    : null\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporderEdit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporderEdit.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<form @submit=\"subform\">\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">桌台<text style=\"color:red\"> </text></view>\r\n\t\t\t\t\t<view class=\"f2\">{{info.tableName}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">订单号<text style=\"color:red\"> </text></view>\r\n\t\t\t\t\t<view class=\"f2\" >{{info.ordernum}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 列表 -->\r\n\t\t\t<view class=\"form-box\" v-for=\"(item,index) in ordergoods\" :key=\"index\">\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">菜品名称</view>\r\n\t\t\t\t\t<view class=\"f2 product-name\" style=\"font-weight:bold\">{{item.name}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\" v-if=\"item.ggname\">\r\n\t\t\t\t\t<view class=\"f1\">规格</view>\r\n\t\t\t\t\t<view class=\"f2\">{{item.ggname}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"line-height:80rpx\" v-if=\"item.ggtext && item.ggtext.length\">\r\n\t\t\t\t\t<view class=\"f1\">套餐</view>\r\n\t\t\t\t\t<view class=\" flex-col\">\r\n\t\t\t\t\t\t<block v-for=\"(item2,index) in item.ggtext\" >\r\n\t\t\t\t\t\t\t<text style=\"line-height: 40rpx;text-align: left;\">{{item2}}</text>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\">价格（元）</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" @input=\"ginput\" :data-index=\"index\" data-field=\"sell_price\" :name=\"'sell_price['+index+']'\" :value=\"item.sell_price\" placeholder=\"请填写销售价\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\" style=\"height:80rpx;line-height:80rpx\">\r\n\t\t\t\t\t<view class=\"f1\" v-if=\"item.product_type && item.product_type ==1\">重量</view>\r\n\t\t\t\t\t<view class=\"f1\" v-else>数量</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"f2\"  v-if=\"item.product_type && item.product_type ==1\">\r\n\t\t\t\t\t\t<input type=\"text\" @input=\"ginput\" :data-index=\"index\" data-field=\"num\" :name=\"'num['+index+']'\" :value=\"item.num\" placeholder=\"请填写重量\" placeholder-style=\"color:#888\"></input>斤\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"f2\"  v-else>\r\n\t\t\t\t\t\t<input type=\"text\" @input=\"ginput\" :data-index=\"index\" data-field=\"num\" :name=\"'num['+index+']'\" :value=\"item.num\" placeholder=\"请填写数量\" placeholder-style=\"color:#888\"></input>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">规格图片</view>\r\n\t\t\t\t\t<view class=\"f2\" style=\"flex-wrap:wrap;margin-top:20rpx;margin-bottom:20rpx\">\r\n\t\t\t\t\t\t<view class=\"layui-imgbox\" v-if=\"item.pic!=''\">\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg2\" :data-index=\"index\"><image style=\"display:block\" :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item.pic\" @tap=\"previewImage\" :data-url=\"item.pic\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"uploadbtn\" v-else :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg2\" :data-index=\"index\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view> -->\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">菜品总价</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"number\" name=\"product_price\" @input=\"input\" disabled=\"true\" data-field=\"product_price\" :value=\"info.product_price\" placeholder=\"\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">会员折扣</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"number\" name=\"leveldk_money\" @input=\"input\" data-field=\"leveldk_money\" :value=\"info.leveldk_money\" placeholder=\"\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">优惠券抵扣</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"number\" name=\"coupon_money\" @input=\"input\" data-field=\"coupon_money\" :value=\"info.coupon_money\" placeholder=\"\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">优惠券抵扣</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"number\" name=\"scoredk_money\" @input=\"input\" data-field=\"scoredk_money\" :value=\"info.scoredk_money\" placeholder=\"\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">实付款</view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"number\" name=\"totalprice\" @input=\"input\" data-field=\"totalprice\" :value=\"info.totalprice\" placeholder=\"\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\r\n\t\t\t<button class=\"savebtn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\" form-type=\"submit\">提交</button>\r\n\t\t\t<view style=\"height:50rpx\"></view>\r\n\t\t</form>\r\n\t\t\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\tisload:false,\r\n\t\t\tloading:false,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n      info:{},\r\n\t\t\tordergoods:[]\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n  methods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = that.opt.id ? that.opt.id : '';\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiAdminRestaurantShopOrder/edit',{id:id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.info = res.info;\r\n\t\t\t\tthat.ordergoods = res.order_goods;\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n\t\tginput: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t  var value = e.detail.value;\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar field = e.currentTarget.dataset.field;\r\n\t\t\tvar ordergoods = that.ordergoods;\r\n\t\t\tvar total = 0;\r\n\t\t\tif(value < 0 || value == '') {\r\n\t\t\t\tapp.error('请输入正确的数值');return;\r\n\t\t\t}\r\n\t\t\tordergoods[index][field] = value;\r\n\t\t\tfor (var i in ordergoods) {\r\n\t\t\t\tif(ordergoods[i].type && ordergoods[i].type==1){\r\n\t\t\t\t\ttotal += ordergoods[i].weigh * ordergoods[i].sell_price;\r\n\t\t\t\t}else{\r\n\t\t\t\t\ttotal += ordergoods[i].num * ordergoods[i].sell_price;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthat.info.product_price = total;\r\n\t\t\tvar totalprice = total - that.info.leveldk_money - that.info.coupon_money - that.info.scoredk_money;\r\n\t\t\ttotalprice = parseFloat(totalprice);\r\n\t\t\ttotalprice = totalprice.toFixed(2)\r\n\t\t\tif(totalprice < 0) {\r\n\t\t\t\tapp.error('总金额不能小于0');return;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthat.info.totalprice = totalprice;\r\n\t\t},\r\n\t\tinput: function (e) {\r\n\t\t\tvar that = this;\r\n\t\t  var value = e.detail.value;\r\n\t\t\tvar field = e.currentTarget.dataset.field;\r\n\t\t\tif(value < 0 || value =='') {\r\n\t\t\t\tapp.error('请输入正确的金额');return;\r\n\t\t\t}\r\n\t\t\tthat.info[field] = value;\r\n\t\t\t\r\n\t\t\tvar totalprice = that.info.product_price - that.info.leveldk_money - that.info.coupon_money - that.info.scoredk_money;\r\n\t\t\ttotalprice = parseFloat(totalprice);\r\n\t\t\ttotalprice = totalprice.toFixed(2)\r\n\t\t\tif(totalprice < 0) {\r\n\t\t\t\tapp.error('总金额不能小于0');return;\r\n\t\t\t}\r\n\t\t\tthat.info.totalprice = totalprice;\r\n\t\t},\r\n    subform: function (e) {\r\n      var that = this;\r\n      var formdata = e.detail.value;\r\n\t\t\tif(that.info.totalprice < 0) {\r\n\t\t\t\tapp.error('总金额不能小于0');return;\r\n\t\t\t}\r\n      var id = that.opt.id ? that.opt.id : '';\r\n      app.post('ApiAdminRestaurantShopOrder/edit', {id:id,info:formdata,goods:that.ordergoods}, function (res) {\r\n        if (res.status == 0) {\r\n          app.error(res.msg);\r\n        } else {\r\n          app.success(res.msg);\r\n          setTimeout(function () {\r\n            app.goto('tableWaiterDetail?id=' + that.info.tableid, 'redirect');\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style>\r\nradio{transform: scale(0.6);}\r\ncheckbox{transform: scale(0.6);}\r\n.form-box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}\r\n.form-item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }\r\n.form-item .f1{color:#222;width:200rpx;flex-shrink:0}\r\n.form-item .f2{display:flex;align-items:center;}\r\n.form-item .product-name {display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}\r\n.form-box .form-item:last-child{ border:none}\r\n.form-box .flex-col{padding-bottom:20rpx}\r\n.form-item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}\r\n.form-item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}\r\n.form-item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }\r\n.form-item .upload_pic image{ width: 32rpx;height: 32rpx; }\r\n.savebtn{ width: 90%; height:96rpx; line-height: 96rpx; text-align:center;border-radius:48rpx; color: #fff;font-weight:bold;margin: 0 5%; margin-top:60rpx; border: none; }\r\n\r\n.ggtitle{height:60rpx;line-height:60rpx;color:#111;font-weight:bold;font-size:26rpx;display:flex;border-bottom:1px solid #f4f4f4}\r\n.ggtitle .t1{width:200rpx;}\r\n.ggcontent{line-height:60rpx;margin-top:10rpx;color:#111;font-size:26rpx;display:flex}\r\n.ggcontent .t1{width:200rpx;display:flex;align-items:center;flex-shrink:0}\r\n.ggcontent .t1 .edit{width:40rpx;height:40rpx}\r\n.ggcontent .t2{display:flex;flex-wrap:wrap;align-items:center}\r\n.ggcontent .ggname{background:#f55;color:#fff;height:40rpx;line-height:40rpx;padding:0 20rpx;border-radius:8rpx;margin-right:20rpx;margin-bottom:10rpx;font-size:24rpx;position:relative}\r\n.ggcontent .ggname .close{position:absolute;top:-14rpx;right:-14rpx;background:#fff;height:28rpx;width:28rpx;border-radius:14rpx}\r\n.ggcontent .ggnameadd{background:#ccc;font-size:36rpx;color:#fff;height:40rpx;line-height:40rpx;padding:0 20rpx;border-radius:8rpx;margin-right:20rpx;margin-left:10rpx;position:relative}\r\n.ggcontent .ggadd{font-size:26rpx;color:#558}\r\n\r\n.ggbox{line-height:50rpx;}\r\n\r\n\r\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.layui-imgbox-img>image{max-width:100%;}\r\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n\r\n\r\n.clist-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\r\n.radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\r\n.radio .radio-img{width:100%;height:100%;display:block}\r\n\r\n.uni-button-color {color: #007aff;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporderEdit.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shoporderEdit.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213990643\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}