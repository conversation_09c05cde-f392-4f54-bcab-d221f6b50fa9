{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/formlog.vue?6f51", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/formlog.vue?dcf0", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/formlog.vue?0202", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/formlog.vue?4ed6", "uni-app:///admin/workorder/formlog.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/formlog.vue?8ec5", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/formlog.vue?4f65"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "st", "datalist", "pagenum", "nomore", "ishowjindu", "jdlist", "nodata", "pre_url", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "changetab", "uni", "scrollTop", "duration", "getdata", "app", "formid", "that", "jindu", "id", "close"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnEA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuDz1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;QACAC;QACAC;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;QAAAC;QAAAlB;QAAAE;MAAA;QACAiB;QACA;QACA;UACAA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAC;MACA;MACAD;MACA;MACA;MACAF;QAAAI;MAAA;QACA;UACA;UACAF;QACA;MAEA;IACA;IACAG;MACA;MACAH;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpJA;AAAA;AAAA;AAAA;AAAkrC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/workorder/formlog.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/workorder/formlog.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./formlog.vue?vue&type=template&id=c2ab4c20&\"\nvar renderjs\nimport script from \"./formlog.vue?vue&type=script&lang=js&\"\nexport * from \"./formlog.vue?vue&type=script&lang=js&\"\nimport style0 from \"./formlog.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/workorder/formlog.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./formlog.vue?vue&type=template&id=c2ab4c20&\"", "var components\ntry {\n  components = {\n    ddTab: function () {\n      return import(\n        /* webpackChunkName: \"components/dd-tab/dd-tab\" */ \"@/components/dd-tab/dd-tab.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.ishowjindu ? _vm.jdlist.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./formlog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./formlog.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<dd-tab :itemdata=\"['全部','待处理','处理中','已处理','待支付']\" :itemst=\"['all','0','1','2','10']\" :st=\"st\" :isfixed=\"true\" @changetab=\"changetab\"></dd-tab>\n\t\t<view style=\"width:100%;height:90rpx\"></view>\n\t\t<view class=\"content\" id=\"datalist\">\n\t\t\t<view class=\"item\"v-for=\"(item, index) in datalist\" :key=\"index\">\n\t\t\t\t<view class=\"f1\">\n\n\t\t\t\t\t\t<view class=\"flex\" style=\"justify-content: space-between;\">\n\t\t\t\t\t\t\t<text class=\"t1\"  @tap=\"goto\" :data-url=\"'myformdetail?id=' + item.id\" >工单类型：{{item.title}}</text>\t\n\t\t\t\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t\t\t\t<text class=\"t1\" v-if=\"item.status==0 && (!item.payorderid||item.paystatus==1)\" style=\"color:#88e\">待处理</text>\n\t\t\t\t\t\t\t\t\t<text class=\"t1\" v-if=\"item.status==0 && item.payorderid && item.paystatus==0\" style=\"color:red\">待支付</text>\n\t\t\t\t\t\t\t\t\t<text class=\"t1\" v-if=\"item.status==1\" style=\"color:green\">处理中</text>\n\t\t\t\t\t\t\t\t\t<text class=\"t1\" v-if=\"item.status==2\" style=\"color:red\">已处理</text>\n\t\t\t\t\t\t\t\t\t<text class=\"t1\" v-if=\"item.status==-1\" style=\"color:red\">已驳回</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"flex\" style=\"justify-content: space-between;margin-top: 15rpx;\">\n\t\t\t\t\t\t\t<text class=\"t2\"  @tap=\"goto\" :data-url=\"'myformdetail?id=' + item.id\" >提交时间：{{item.createtime}}</text>\n\t\t\t\t\t\t\t<view class=\"jindu\" @tap=\"jindu\" :data-id=\"item.id\"  v-if=\"!item.payorderid || (item.payorderid && item.paystatus==1)\">查看进度</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t\n\t<view class=\"modal\" v-if=\"ishowjindu\">\n\t\t<view class=\"modal_jindu\">\n\t\t\t\t\t<view class=\"close\" @tap=\"close\"><image :src=\"pre_url+'/static/img/close.png'\" /></view>\n\t\t\t\t\t<block v-if=\"jdlist.length>0\">\n\t\t\t\t\t\t<view class=\"item \" v-for=\"(item,index) in jdlist\" :key=\"index\">\n\t\t\t\t\t\t\t<view class=\"f1\"><image :src=\"'/static/img/jindu' + (index==0?'2':'1') + '.png'\"></image></view>\n\t\t\t\t\t\t\t<view class=\"f2\">\n\t\t\t\t\t\t\t\t<text class=\"t2\"> 时间：{{item.time}}</text>\n\t\t\t\t\t\t\t\t<text class=\"t1\">{{item.desc}}({{item.remark}}) </text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t<view style=\"font-size:14px;color:#f05555;padding:10px;\">等待处理</view>\n\t\t\t\t\t</block>\n\t\t</view>\n\t</view>\n\t<nodata v-if=\"nodata\"></nodata>\n\t<nomore v-if=\"nomore\"></nomore>\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n\t\t\topt:{},\n\t\t\tloading:false,\n      isload: false,\n\t\t\tmenuindex:-1,\n\n      st: 'all',\n      datalist: [],\n      pagenum: 1,\n      nomore: false,\n\t\t\tishowjindu:false,\n\t\t\tjdlist:[],\n\t\t\tnodata: false,\n\t\t\tpre_url: app.globalData.pre_url,\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.st = this.opt.st || 'all';\n\t\tthis.formid = this.opt.formid || '';\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  onReachBottom: function () {\n    if (!this.nodata && !this.nomore) {\n      this.pagenum = this.pagenum + 1;\n      this.getdata(true);\n    }\n  },\n  methods: {\n    changetab: function (st) {\n      this.st = st;\n      uni.pageScrollTo({\n        scrollTop: 0,\n        duration: 0\n      });\n      this.getdata();\n    },\n    getdata: function (loadmore) {\n\t\t\tif(!loadmore){\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.datalist = [];\n\t\t\t}\n      var that = this;\n      var pagenum = that.pagenum;\n      var st = that.st;\n\t\t\tthis.nodata = false;\n\t\t\tthis.nomore = false;\n\t\t\tthis.loading = true;\n      app.post('ApiAdminWorkorder/myformlog', {formid:that.formid,st: st,pagenum: pagenum}, function (res) {\n\t\t\t\tthat.loading = false;\n        var data = res.data;\n        if (pagenum == 1) {\n\t\t\t\t\tthat.datalist = data;\n          if (data.length == 0) {\n            that.nodata = true;\n          }\n\t\t\t\t\tthat.loaded();\n        }else{\n          if (data.length == 0) {\n            that.nomore = true;\n          } else {\n            var datalist = that.datalist;\n            var newdata = datalist.concat(data);\n            that.datalist = newdata;\n          }\n        }\n      });\n    },\n\t\tjindu:function(e){\n\t\t\tvar that=this\n\t\t\tthat.ishowjindu=true\n\t\t\tvar id = e.currentTarget.dataset.id\n\t\t\t//读取进度表\n\t\t\tapp.post('ApiWorkorder/selectjindu', { id: id }, function (res) {\n\t\t\t\t\tif(res.status==1){\n\t\t\t\t\t\tvar data = res.data\n\t\t\t\t\t\tthat.jdlist =data\n\t\t\t\t\t}\n\t\t\t\t\n\t\t\t})\n\t\t},\n\t\tclose:function(e){\n\t\t\tvar that=this\n\t\t\tthat.ishowjindu=false\n\t\t}\n  }\n}\n</script>\n<style>\n\n\t.content{ width:100%;margin:0;}\n\t.content .item{ width:94%;margin:20rpx 3%;;background:#fff;border-radius:16rpx;padding:30rpx 30rpx;display:flex;align-items:center;}\n\t.content .item:last-child{border:0}\n\t.content .item .f1{width:100%;display:flex;flex-direction:column}\n\t.content .item .f1 .t1{color:#000000;font-size:30rpx;word-break:break-all;overflow:hidden;text-overflow:ellipsis;}\n\t.content .item .f1 .t2{color:#666666;margin-top:10rpx}\n\t.content .item .f1 .t3{color:#666666}\n\t.content .item .f2{width:20%;font-size:24rpx;text-align:right}\n\t.content .item .f2 .t1{color:#03bc01;font-size:24rpx;}\n\t.content .item .f2 .t2{color:#000000}\n\t.content .item .f3{ flex:1;font-size:30rpx;text-align:right}\n\t.content .item .f3 .t1{color:#03bc01}\n\t.content .item .f3 .t2{color:#000000}\n\t.jindu{ border: 1rpx solid #ccc; font-size: 24rpx; padding: 5rpx 10rpx; border-radius: 10rpx; color: #555;}\n\t\n\t\n\t.modal{ position: fixed; background:rgba(0,0,0,0.3); width: 100%; height: 100%; top:0; z-index: 1000;}\n\t.modal .modal_jindu{ background: #fff; position: absolute; top: 20%; align-items: center; margin: auto; width: 90%; left: 30rpx; border-radius: 10rpx; padding: 40rpx;}\n\t.modal_jindu .close image { width: 20rpx; height: 20rpx; position: absolute; top:10rpx; right: 20rpx;}\n\t.modal_jindu .on{color: #23aa5e;}\n\t.modal_jindu .item{display:flex;width: 96%;  margin: 0 2%;/*border-left: 1px #dadada solid;*/padding:0 0}\n\t.modal_jindu .item .f1{ width:60rpx;position:relative}\n\t/*.logistics img{width: 15px; height: 15px; position: absolute; left: -8px; top:11px;}*/\n\t.modal_jindu .item .f1 image{width: 30rpx; height: 100%; position: absolute; left: -16rpx; top: 0rpx;}\n\t.modal_jindu .item .f2{display:flex;flex-direction:column;flex:auto;padding:10rpx 0}\n\t.modal_jindu .item .f2 .t1{font-size: 30rpx;}\n\t.modal_jindu .item .f2 .t1{font-size: 26rpx;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./formlog.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./formlog.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213040755\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}