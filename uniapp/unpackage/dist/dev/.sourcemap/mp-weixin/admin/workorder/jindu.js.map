{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/jindu.vue?db59", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/jindu.vue?5831", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/jindu.vue?9f04", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/jindu.vue?6901", "uni-app:///admin/workorder/jindu.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/jindu.vue?4550", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/jindu.vue?f407"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "pre_url", "nodata", "detail", "content_pic", "lclist", "jdlist", "form", "showmore", "curindex", "cid", "editorFormdata", "formdata", "formvaldata", "onLoad", "methods", "getdata", "that", "app", "id", "getliucheng", "formsubmit", "newformdata", "console", "logid", "lcid", "setTimeout", "zhankai", "zhankai1", "editorChooseImage", "pics", "removeimg", "download", "uni", "url", "success", "filePath", "showMenu", "chooseFile", "count", "maxsize", "name", "fail", "complete", "removeimg2", "upVideo", "sourceType"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgQv1B;AAAA,eACA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAN;MACAO;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,mDACA,sDACA,sDACA,8CACA;EAEA;EACAC;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;MACAC;QAAAC;MAAA;QACAF;QACA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IAEAG;MACA;MACAF;QAAAR;MAAA;QACA;QACAO;MAEA;IACA;IACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAI;MACA;MACA;MACA;MACA;MACA;QACAH;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;UACAA;UAAA;QACA;QACA;UACA;YAAA;YACA;cACAA;cAAA;YACA;UACA;UACA;YAAA;YACA;cACAA;cAAA;YACA;UACA;UACA;YAAA;YACA;cACAA;cAAA;YACA;UACA;QACA;QACAI;MACA;MAEAC;MAEAL;MACAA;QAAAM;QAAAC;QAAAb;MAAA;QACA;QACAM;QACA;UACAA;UACAQ;YACA;YACAR;UACA;QACA;UACAA;QACA;MAEA;IACA;IACAS;MACA;MACA;MACA;MACA;QACA;QACAV;MACA;MACA;QACA;QACAA;QACAA;MACA;IACA;IACAW;MACA;MACA;MACA;MACA;QACA;QACAX;MACA;MACA;QACA;QACAA;QACAA;MACA;IACA;IACAY;MACA;MACA;MACA;MACA;MACAN;MACA;MACAL;QACA;UACAY;QACA;QACAb;QACAA;QACAA;QACAM;QACAN;MACA;IACA;IACAc;MACA;MACA;MACA;MACA;MACA;MACAd;MACAA;MACA;MACAA;IACA;IACAe;MACA;MACA;MAMAC;QACAC;QACAC;UACA;UACA;YACAF;cACAG;cACAC;cACAF;gBACAZ;cACA;YACA;UACA;QACA;MACA;IAEA;IAEAe;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MA6CA/C;QACAgD;QACA;QACAJ;UACA;UACA;UACAZ;UAEA;YACA;YACA;cACAiB;cACA;gBACAtB;gBAAA;cACA;YACA;UACA;;UAEA;UACAA;UACAK;UACAU;YACAC;YACAE;YACAK;YACAN;cACAjB;cACA;cACA;gBACAD;gBAEAN;gBACAM;gBACAA;gBACAA;cACA;gBACAC;cACA;YACA;YACAwB;cACAxB;cACAA;YACA;UACA;UACA;QACA;QACAyB;UACApB;QACA;MACA;IAEA;IACAqB;MACA;MACA;MACArB;MACA;MACA;MACA;MACAZ;MACAM;MACAA;MACAA;MACAA;IACA;IACA4B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAZ;QACAa;QACAX;UACA;UACA;YACA;YACA;cACAK;cACA;gBACAtB;gBAAA;cACA;YACA;UACA;UACAA;UACAK;UACAU;YACAC;YACAE;YACAK;YACAN;cACAjB;cACA;cACA;gBACAD;gBAEAN;gBACAM;gBACAA;gBACAA;cACA;gBACAC;cACA;YACA;YACAwB;cACAxB;cACAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACroBA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/workorder/jindu.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/workorder/jindu.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./jindu.vue?vue&type=template&id=2e856060&\"\nvar renderjs\nimport script from \"./jindu.vue?vue&type=script&lang=js&\"\nexport * from \"./jindu.vue?vue&type=script&lang=js&\"\nimport style0 from \"./jindu.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/workorder/jindu.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jindu.vue?vue&type=template&id=2e856060&\"", "var components\ntry {\n  components = {\n    parse: function () {\n      return import(\n        /* webpackChunkName: \"components/parse/parse\" */ \"@/components/parse/parse.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.t(\"color1\")\n  var m1 = _vm.t(\"color1rgb\")\n  var g0 = _vm.data.length\n  var l0 =\n    _vm.detail.status != 2\n      ? _vm.__map(_vm.lclist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g1 = _vm.data.length\n          return {\n            $orig: $orig,\n            g1: g1,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jindu.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jindu.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"banner\" :style=\"'background: linear-gradient(180deg, '+t('color1')+' 0%, rgba('+t('color1rgb')+',0) 100%);'\"></view>\r\n\t\t<view class=\"page\">\r\n\t\t\t<view class=\"header fa\">\r\n\t\t\t\t<view class=\"f1\">\r\n\t\t\t\t\t<view class=\"header_title\" v-if=\"detail.status==0\">\r\n\t\t\t\t\t\t待处理\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"header_title\" v-if=\"detail.status==1\">\r\n\t\t\t\t\t\t{{detail.clname}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"clusername\" style=\"color: #fff;margin-top: 10rpx;\" v-if=\"detail.clusername\"> <text>处理人：{{detail.clusername}}</text></view>\r\n\t\t\t\t\t<view class=\"header_text fa\" @tap=\"goto\" :data-url=\"'formdetail?id='+id\">\r\n\t\t\t\t\t\t查看工单详情<image class=\"header_icon\" :src=\"pre_url+'/static/imgsrc/work_detail.png'\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<image class=\"header_tag\" :src=\"pre_url+'/static/imgsrc/work_tag.png'\" mode=\"widthFix\"></image>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t<view class=\"desc\">\t<parse :content=\"form.desc\"></parse></view>\r\n\t\t\t\r\n\t\t\t\r\n\r\n\r\n\t\t\t<view class=\"body\">\r\n\t\t\t\t<view class=\"body_title\">\r\n\t\t\t\t\t<text>工单进度</text>\r\n\t\t\t\t\t<image class=\"body_icon\" :src=\"pre_url+'/static/imgsrc/work_title.png'\" mode=\"widthFix\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<block v-if=\"data.length>0\">\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"module\"  v-for=\"(item,index) in data\" :key=\"index\">\r\n\t\t\t\t\t\t\t<image class=\"module_tag\" :src=\"pre_url+'/static/imgsrc/'+(index>0?'work_dot':'work_dotA')+'.jpg'\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view :class=\"'module_title '+(index>0?'module_null':'')\">\r\n\t\t\t\t\t\t\t\t{{item.desc}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view class=\"module_text\">\r\n\t\t\t\t\t\t\t\t{{item.remark}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"module_img\" v-if=\"item.content_pic\">\r\n\t\t\t\t\t\t\t\t<view v-for=\"(pic,picindex) in item.content_pic\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"pic\" mode=\"widthFix\" style=\"width: 100rpx;\" @tap=\"previewImage\" :data-url=\"pic\">\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view v-for=\"(formitem, formindex) in form.contentuser\"  class=\"hfitem\" :style=\"(dataindex!=index && formindex>1)?'display:none':''\">\r\n\t\t\t\t\t\t\t\t<text class=\"t1\" :class=\"formitem.key=='separate'?'title':''\">{{formitem.val1}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"formitem.key!='upload' && formitem.key!='upload_file' && formitem.key!='upload_video'\" >{{item['form'+formindex]}}</text>\r\n\t\t\t\t\t\t\t\t<view class=\"t2\" style=\"display: flex; justify-content: flex-end;\"  v-if=\"formitem.key=='upload'\">\r\n\t\t\t\t\t\t\t\t\t<view v-for=\"(sub, indx) in item['form'+formindex]\" :key=\"indx\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"sub\" style=\"width:50px; margin-left: 10rpx;\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"sub\"></image>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<!-- #ifdef !H5 && !MP-WEIXIN -->\r\n\t\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"formitem.key=='upload_file' && item['form'+formindex]\" style=\"overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;\">\r\n\t\t\t\t\t\t\t\t\t\t{{hf['form'+formindex]}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t\t<!-- #ifdef H5 || MP-WEIXIN -->\r\n\t\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"formitem.key=='upload_file' && item['form'+formindex]\"  @tap=\"download\" :data-file=\"item['form'+formindex]\" style=\"overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;\">\r\n\t\t\t\t\t\t\t\t\t\t点击下载查看\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<view class=\"t2\"  v-if=\"formitem.key=='upload_video' && item['form'+formindex]\">\r\n\t\t\t\t\t\t\t\t\t\t<video  :src=\"item['form'+formindex]\" style=\"width: 100%;\"/></video>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view v-if=\"dataindex==formindex\" class=\"zktext\" @tap=\"zhankai1\" :data-dataindex=\"index\" data-type=\"zhedie\">收起\r\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/workorder/shoqi.png'\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view v-else class=\"zktext\" @tap=\"zhankai1\" :data-dataindex=\"index\"  data-type=\"zhankai\">展开\r\n\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/workorder/more.png' \"/>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view class=\"module_time\">\r\n\t\t\t\t\t\t\t\t{{item.time}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!--<view class=\"module_opt fx\">\r\n\t\t\t\t\t\t\t\t<view class=\"module_btn module_r\">同意</view>\r\n\t\t\t\t\t\t\t\t<view class=\"module_btn\">拒绝</view>\r\n\t\t\t\t\t\t\t\t<view class=\"module_btn\">协商</view>\r\n\t\t\t\t\t\t\t</view>-->\r\n\t\t\t\t\t\t\t<view class=\"module_opt fx\">\r\n\t\t\t\t\t\t\t\t<view v-for=\"(hf,hfindex) in item.hflist\" :key=\"hfindex\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"t3\" v-if=\"hf.hfremark\" ><text class=\"t3_1\">我的回复：</text>{{hf.hfremark}} </view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"t4\" v-if=\"hf.hftime\" ><text class=\"t4_1\">回复时间：</text>{{hf.hftime}} </view>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view v-for=\"(item, index) in form.contentuser\" :key=\"index\" class=\"hfitem\" :style=\"(curindex!=hfindex && index>1)?'display:none':''\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t1\" :class=\"item.key=='separate'?'title':''\">{{item.val1}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.key!='upload' && item.key!='upload_file' && item.key!='upload_video'\" >{{hf['form'+index]}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"t2\" style=\"display: flex; justify-content: flex-end;\"  v-if=\"item.key=='upload'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view v-for=\"(sub, indx) in hf['form'+index]\" :key=\"indx\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"sub\" style=\"width:50px; margin-left: 10rpx;\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"sub\"></image>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t<!-- #ifdef !H5 && !MP-WEIXIN -->\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"item.key=='upload_file' && hf['form'+index]\" style=\"overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{hf['form'+index]}}\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t\t\t\t<!-- #ifdef H5 || MP-WEIXIN -->\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"t2\" v-if=\"item.key=='upload_file' && hf['form'+index]\"  @tap=\"download\" :data-file=\"hf['form'+index]\" style=\"overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t点击下载查看\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"t2\"  v-if=\"item.key=='upload_video' && hf['form'+index]\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<video  :src=\"hf['form'+index]\" style=\"width: 100%;\"/></video>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"curindex==hfindex\" class=\"zktext\" @tap=\"zhankai\" :data-curindex=\"hfindex\" data-type=\"zhedie\">收起\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/workorder/shoqi.png'\" />\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else class=\"zktext\" @tap=\"zhankai\" :data-curindex=\"hfindex\"  data-type=\"zhankai\">展开\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/workorder/more.png' \"/>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<view class=\"module\">\r\n\t\t\t\t\t\t\t\t<image class=\"module_tag\" :src=\"pre_url+'/static/imgsrc/work_dot.jpg'\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t\t\t\t<view class=\"module_title module_null\">\r\n\t\t\t\t\t\t\t\t\t等待处理\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<view class=\"modal\"  v-if=\"detail.status!=2\" >\r\n\t\t\t\t\t\t<view class=\"modal_jindu\">\r\n\t\t\t\t\t\t\t<form   @submit=\"formsubmit\" :data-formcontent=\"form.contentuser\" >\r\n\t\t\t\t\t\t\t\t\t<view class=\"title\">选择流程</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"uni-list\">\r\n\t\t\t\t\t\t\t\t\t\t\t<radio-group name=\"liucheng\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<label class=\"uni-list-cell uni-list-cell-pd\" v-for=\"(item, index) in lclist\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"data.length>0\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<radio :value=\"''+item.id\" style=\"transform:scale(0.7)\" :checked=\"data[0].lcid==item.id?true:false\"/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<radio :value=\"''+item.id\" style=\"transform:scale(0.7)\" />\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view>{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t<block v-for=\"(item,index) in form.contentuser\" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view  class=\"label\">{{item.val1}}<text v-if=\"item.val3==1\" style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='input'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" :name=\"'form'+index\" class=\"input\"  :placeholder=\"item.val2\" :data-formidx=\"'form'+index\" placeholder-style=\"font-size:28rpx\"/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='upload'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form'+index\" :value=\"editorFormdata['content_pics'+index]\"/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"form-imgbox\" v-if=\"editorFormdata['content_pic'+index]\" v-for=\"(item1, index1) in editorFormdata['content_pic'+index]\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"form-imgbox-close\" @tap=\"removeimg\" :data-pindex=\"index1\"  :data-field=\"'content_pic'+index\"  :data-idx=\"index\"><image :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"form-imgbox-img\"><image  :src=\"item1\" @click=\"previewImage\" :data-url=\"item1\" mode=\"widthFix\" /></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3',backgroundPosition: 'center'}\" @click=\"editorChooseImage\" :data-field=\"'content_pic'+index\"  :data-idx=\"index\" :data-formidx=\"'form'+index\"></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<!-- #ifdef H5 || MP-WEIXIN -->\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='upload_file'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form'+index\" :value=\"editorFormdata[index]\"/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"flex-y-center\" style=\"flex-wrap:wrap;padding-top:20rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"dp-form-imgbox\" v-if=\"editorFormdata[index]\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"dp-form-imgbox-close\" @tap=\"removeimg2\" :data-idx=\"index\" :data-formidx=\"'form'+index\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/ico-del.png'\" class=\"image\"></image>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view  style=\"overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;\" @tap=\"download\" :data-file=\"editorFormdata[index]\" >\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t文件已上传成功\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view  class=\"dp-form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 30rpx',backgroundSize:'50rpx 50rpx',backgroundColor:'#F3F3F3'}\" @click=\"chooseFile\" :data-idx=\"index\" :data-id=\"index\" :data-formidx=\"'form'+index\" style=\"margin-right:20rpx;\"></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.val2\" style=\"color:#999\">{{item.val2}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"item.key=='upload_video'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" style=\"display:none\" :name=\"'form'+index\" :value=\"editorFormdata[index]\"/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"flex-y-center\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"dp-form-imgbox\" v-if=\"editorFormdata[index]\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"dp-form-imgbox-close\" @tap=\"removeimg2\" :data-idx=\"index\" :data-formidx=\"'form'+index\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/ico-del.png'\" class=\"image\"></image>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view  style=\"overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;width: 230rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<video  :src=\"editorFormdata[index]\" style=\"width: 100%;\"/></video>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"dp-form-uploadbtn\" :style=\"{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 30rpx',backgroundSize:'50rpx 50rpx',backgroundColor:'#F3F3F3'}\" @tap=\"upVideo\" :data-idx=\"index\"  :data-id=\"index\" :data-formidx=\"'form'+index\" style=\"margin-right:20rpx;\"></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.val2\" style=\"color:#999\">{{item.val2}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</block>\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t<!--<view class=\"form-item4 flex-col\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"label\">上传图片</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view id=\"content_picpreview\" class=\"flex\" style=\"flex-wrap:wrap;padding-top:20rpx\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view v-for=\"(item, index) in content_pic\" :key=\"index\" class=\"layui-imgbox\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-close\" @tap=\"removeimg\" :data-index=\"index\" data-field=\"content_pic\" ><image :src=\"pre_url+'/static/img/ico-del.png'\"></image></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"item\" @tap=\"previewImage\" :data-url=\"item\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"uploadbtn\" :style=\"'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'\" @tap=\"uploadimg\" data-field=\"content_pic\" v-if=\"content_pic.length<5\"></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"beizhu flex\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<label>备注:</label><textarea placeholder=\"输入内容\"  name=\"content\" maxlength=\"-1\" style=\"height: 200rpx;\"></textarea>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>-->\t\t\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<button class=\"btn\" form-type=\"submit\" >提交</button>\r\n\t\t\t\t\t\t\t</form>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\topt:{},\r\n\t\t\t\tloading:false,\r\n\t\t\t\tisload: false,\r\n\t\t\t\tmenuindex:-1,\r\n\t\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\t\tnodata:false,\r\n\t\t\t\tdata:[],\r\n\t\t\t\tdetail:[],\r\n\t\t\t\tcontent_pic: [],\r\n\t\t\t\tlclist:[],\r\n\t\t\t\tjdlist:[],\r\n\t\t\t\tform:[],\r\n\t\t\t\tshowmore:false,\r\n\t\t\t\tcurindex:0,\r\n\t\t\t\tcid:0,\r\n\t\t\t\teditorFormdata:{},\r\n\t\t\t\tformdata:{},\r\n\t\t\t\tformvaldata:{},\r\n\t\t\t\tcurindex:-1,\r\n\t\t\t\tdataindex:-1,\r\n\t\t\t\tformindex:0,\r\n\t\t\t\tid:0\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(opt) {\r\n\t\t\tvar that=this\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.id =  that.opt.id;\r\n\t\t\tthis.getdata();\r\n\t\t\tthis.getliucheng();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetdata: function () {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.id = that.opt.id;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tapp.post('ApiWorkorder/selectjindu', { id:that.id }, function (res) {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tvar data = res.data;\r\n\t\t\t\t\tthat.data = data;\r\n\t\t\t\t\tthat.detail = res.detail\r\n\t\t\t\t\tthat.form = res.form\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tgetliucheng:function(e){\r\n\t\t\t\tvar that=this\r\n\t\t\t\tapp.post('ApiAdminWorkorder/getliucheng', {cid: that.opt.cid}, function (res) {\r\n\t\t\t\t\t\tvar lclist = res.datalist;\r\n\t\t\t\t\t\tthat.lclist = lclist;\r\n\t\t\t\t\t\t\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/*uploadimg:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\t\tvar pics = that[field]\r\n\t\t\t\tif(!pics) pics = [];\r\n\t\t\t\r\n\t\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\t\tfor(var i=0;i<urls.length;i++){\r\n\t\t\t\t\t\tpics.push(urls[i]);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconsole.log(pics);\r\n\t\t\t\t},5)\r\n\t\t\t},\r\n\t\t\tremoveimg:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\t\tvar pics = that[field]\r\n\t\t\t\tpics.splice(index,1)\r\n\t\t\t},*/\r\n\t\t\tformsubmit: function (e) {\r\n\t\t\t  var that = this;\t\t\r\n\t\t\t  var formdata = e.detail.value;\r\n\t\t\t  var content = formdata.content;\r\n\t\t\t  var liucheng = formdata.liucheng;\r\n\t\t\t  if (liucheng == '') {\r\n\t\t\t    app.error('请选择流程');\r\n\t\t\t    return false;\r\n\t\t\t  }\r\n\t\t\t\tvar content_pic = that.content_pic;\r\n\t\t\t\tvar formcontent = e.currentTarget.dataset.formcontent;\r\n\t\t\t\tvar formid = e.currentTarget.dataset.formid;\r\n\t\t\t\tvar formdataval = e.detail.value;\r\n\t\t\t\tvar newformdata = {};\r\n\t\t\t\tfor (var i = 0; i < formcontent.length;i++){\r\n\t\t\t\t\t//console.log(subdata['form' + i]);\r\n\t\t\t\t\tif (formcontent[i].val3 == 1 && (formdataval['form' + i ] === '' || formdataval['form' + i] === null || formdataval['form' + i] === undefined || formdataval['form' + i].length==0)){\r\n\t\t\t\t\t\t\tapp.alert(formcontent[i].val1+' 必填');return;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (formcontent[i].key == 'input' && formcontent[i].val4 && formdataval['form' + i]!==''){\r\n\t\t\t\t\t\tif(formcontent[i].val4 == '2'){ //手机号\r\n\t\t\t\t\t\t\tif (!app.isPhone(formdataval['form' + i])) {\r\n\t\t\t\t\t\t\t\tapp.alert(formcontent[i].val1+' 格式错误');return;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(formcontent[i].val4 == '3'){ //身份证号\r\n\t\t\t\t\t\t\tif (!/(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/.test(formdataval['form' + i])) {\r\n\t\t\t\t\t\t\t\tapp.alert(formcontent[i].val1+' 格式错误');return;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(formcontent[i].val4 == '4'){ //邮箱\r\n\t\t\t\t\t\t\tif (!/^(.+)@(.+)$/.test(formdataval['form' + i])) {\r\n\t\t\t\t\t\t\t\tapp.alert(formcontent[i].val1+' 格式错误');return;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tnewformdata['form'+i] = formdataval['form' + i];\r\n\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\tconsole.log(newformdata);\r\n\t\t\t\t\r\n\t\t\t\tapp.showLoading();\r\n\t\t\t  app.post('ApiAdminWorkorder/addjindu', {logid:that.opt.id,lcid:liucheng,formdata:newformdata}, function (res) {\r\n\t\t\t\t\tvar res  = res\r\n\t\t\t  \tapp.showLoading(false);\r\n\t\t\t    if (res.status == 1) {\r\n\t\t\t      app.alert('处理成功');\r\n\t\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\t  //that.getdata();\r\n\t\t\t\t\t\t\t\tapp.goto('formdetail?id='+that.opt.id);\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t    } else {\r\n\t\t\t      app.alert(res.msg);\r\n\t\t\t    }\r\n\t\t\t\r\n\t\t\t  });\r\n\t\t\t},\r\n\t\t\tzhankai:function(e){\r\n\t\t\t\tvar that=this\r\n\t\t\t  var type = e.currentTarget.dataset.type;\r\n\t\t\t\tvar curindex = e.currentTarget.dataset.curindex;\r\n\t\t\t\tif(type=='zhedie'){\r\n\t\t\t\t\tvar curindex = -1\r\n\t\t\t\t\tthat.curindex=curindex\r\n\t\t\t\t}\r\n\t\t\t\tif(type=='zhankai'){\r\n\t\t\t\t\tvar showmore = that.showmore\r\n\t\t\t\t\tthat.showmore = !showmore\r\n\t\t\t\t\tthat.curindex = curindex\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tzhankai1:function(e){\r\n\t\t\t\tvar that=this\r\n\t\t\t\tvar type = e.currentTarget.dataset.type;\r\n\t\t\t\tvar dataindex = e.currentTarget.dataset.dataindex;\r\n\t\t\t\tif(type=='zhedie'){\r\n\t\t\t\t\tvar dataindex = -1\r\n\t\t\t\t\tthat.dataindex=dataindex\r\n\t\t\t\t}\r\n\t\t\t\tif(type=='zhankai'){\r\n\t\t\t\t\tvar showmore1 = that.showmore1\r\n\t\t\t\t\tthat.showmore1 = !showmore1\r\n\t\t\t\t\tthat.dataindex = dataindex\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\teditorChooseImage: function (e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar field = e.currentTarget.dataset.field;\r\n\t\t\t\tvar pics = that.editorFormdata[field]\r\n\t\t\t\tconsole.log(field);\r\n\t\t\t\tif(!pics) pics = [];\r\n\t\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\t\tfor(var i=0;i<urls.length;i++){\r\n\t\t\t\t\t\tpics.push(urls[i]);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.editorFormdata[field] = pics;\r\n\t\t\t\t\tthat.editorFormdata['content_pics'+idx] = that.editorFormdata[field].join(',');\r\n\t\t\t\t\tthat.formvaldata[field] = pics;\r\n\t\t\t\t\tconsole.log(that.editorFormdata);\r\n\t\t\t\t\tthat.getdata();\r\n\t\t\t\t},5)\r\n\t\t\t},\r\n\t\t\tremoveimg:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tvar pindex= e.currentTarget.dataset.pindex\r\n\t\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\t\tvar pics = that.editorFormdata[field]\r\n\t\t\t\tthat.editorFormdata[field].splice(pindex,1)\r\n\t\t\t\tthat.editorFormdata['content_pics'+idx] = that.editorFormdata[field].join(',');\r\n\t\t\t\t//console.log(that.editorFormdata[field]);\r\n\t\t\t\tthat.getdata();\r\n\t\t\t},\r\n\t\t\tdownload:function(e){\r\n\t\t\t    var that = this;\r\n\t\t\t    var file = e.currentTarget.dataset.file;\r\n\t\t\t    // #ifdef H5\r\n\t\t\t        window.location.href= file;\r\n\t\t\t    // #endif\r\n\t\t\t    \r\n\t\t\t    // #ifdef MP-WEIXIN\r\n\t\t\t    uni.downloadFile({\r\n\t\t\t    \turl: file, \r\n\t\t\t    \tsuccess: (res) => {\r\n\t\t\t            var filePath = res.tempFilePath;\r\n\t\t\t    \t\tif (res.statusCode === 200) {\r\n\t\t\t    \t\t\tuni.openDocument({\r\n\t\t\t                  filePath: filePath,\r\n\t\t\t                  showMenu: true,\r\n\t\t\t                  success: function (res) {\r\n\t\t\t                    console.log('打开文档成功');\r\n\t\t\t                  }\r\n\t\t\t                });\r\n\t\t\t    \t\t}\r\n\t\t\t    \t}\r\n\t\t\t    });\r\n\t\t\t    // #endif\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tchooseFile:function(e){\r\n\t\t\t    var that = this;\r\n\t\t\t    var idx = e.currentTarget.dataset.idx;\r\n\t\t\t\t  var id = e.currentTarget.dataset.id;\r\n\t\t\t    var field = e.currentTarget.dataset.formidx;\r\n\t\t\t    var editorFormdata = this.editorFormdata;\r\n\t\t\t    if(!editorFormdata) editorFormdata = [];\r\n\t\t\t\t\tvar currentindex= that.currentindex\r\n\t\t\t    //console.log( that.formdata.content);\r\n\t\t\t    var up_url = app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id;\r\n\t\t\t    // #ifdef H5\r\n\t\t\t    uni.chooseFile({\r\n\t\t\t        count: 1, //默认100\r\n\t\t\t        success: function (res) {\r\n\t\t\t            const tempFilePaths = res.tempFiles;\r\n\t\t\t\t\t\t\t\t\tif(tempFilePaths[0].size > 0){\r\n\t\t\t\t\t\t\t\t\t\tvar maxsize = that.form.content[idx].val6;\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(maxsize);\r\n\t\t\t\t\t\t\t\t\t\tif(maxsize){\r\n\t\t\t\t\t\t\t\t\t\t\tmaxsize = parseFloat(maxsize);\r\n\t\t\t\t\t\t\t\t\t\t\tif(maxsize > 0 && maxsize * 1024 * 1024 < tempFilePaths[0].size){\r\n\t\t\t\t\t\t\t\t\t\t\t\tapp.alert('文件过大');return;\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t            //for (var i = 0; i < tempFilePaths.length; i++) {\r\n\t\t\t            \tapp.showLoading('上传中');\r\n\t\t\t            \tuni.uploadFile({\r\n\t\t\t            \t\turl: up_url,\r\n\t\t\t            \t\tfilePath: tempFilePaths[0]['path'],\r\n\t\t\t            \t\tname: 'file',\r\n\t\t\t            \t\tsuccess: function(res) {\r\n\t\t\t            \t\t\tapp.showLoading(false);\r\n\t\t\t            \t\t\tvar data = JSON.parse(res.data);\r\n\t\t\t            \t\t\tif (data.status == 1) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.formvaldata[field] = data.url;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\teditorFormdata[id] = data.url;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.editorFormdata = editorFormdata;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.$set(that.editorFormdata, idx,data.url)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.getdata();\r\n\t\t\t            \t\t\t} else {\r\n\t\t\t            \t\t\t\tapp.alert(data.msg);\r\n\t\t\t            \t\t\t}\r\n\t\t\t            \t\t},\r\n\t\t\t            \t\tfail: function(res) {\r\n\t\t\t            \t\t\tapp.showLoading(false);\r\n\t\t\t            \t\t\tapp.alert(res.errMsg);\r\n\t\t\t            \t\t}\r\n\t\t\t            \t});\r\n\t\t\t            //}\r\n\t\t\t        }\r\n\t\t\t    });\r\n\t\t\t    // #endif\r\n\t\t\t    // #ifdef MP-WEIXIN\r\n\t\t\t        wx.chooseMessageFile({\r\n\t\t\t          count: 1,\r\n\t\t\t          //type: 'file',\r\n\t\t\t          success (res) {\r\n\t\t\t            // tempFilePath可以作为 img 标签的 src 属性显示图片\r\n\t\t\t            const tempFilePaths = res.tempFiles\r\n\t\t\t            console.log(tempFilePaths);\r\n\t\t\t            \r\n\t\t\t\t\t\t\t\t\tif(tempFilePaths[0].size > 0){\r\n\t\t\t\t\t\t\t\t\t\tvar maxsize = that.formdetail.content[idx].val11;\r\n\t\t\t\t\t\t\t\t\t\tif(maxsize){\r\n\t\t\t\t\t\t\t\t\t\t\tmaxsize = parseFloat(maxsize);\r\n\t\t\t\t\t\t\t\t\t\t\tif(maxsize > 0 && maxsize * 1024 * 1024 < tempFilePaths[0].size){\r\n\t\t\t\t\t\t\t\t\t\t\t\tapp.alert('文件过大');return;\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t           \r\n\t\t\t            //for (var i = 0; i < tempFilePaths.length; i++) {\r\n\t\t\t            \tapp.showLoading('上传中');\r\n\t\t\t                console.log(tempFilePaths[0]);\r\n\t\t\t            \tuni.uploadFile({\r\n\t\t\t            \t\turl: up_url,\r\n\t\t\t            \t\tfilePath: tempFilePaths[0]['path'],\r\n\t\t\t            \t\tname: 'file',\r\n\t\t\t            \t\tsuccess: function(res) {\r\n\t\t\t            \t\t\tapp.showLoading(false);\r\n\t\t\t            \t\t\tvar data = JSON.parse(res.data);\r\n\t\t\t            \t\t\tif (data.status == 1) {\r\n\t\t\t                            that.formvaldata[field] = data.url;\r\n\t\t\t                            \r\n\t\t\t                            editorFormdata[idx] = data.url;\r\n\t\t\t                            that.editorFormdata = editorFormdata;\r\n\t\t\t                            that.$set(that.editorFormdata, idx,data.url)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.getdata();\r\n\t\t\t            \t\t\t} else {\r\n\t\t\t            \t\t\t\tapp.alert(data.msg);\r\n\t\t\t            \t\t\t}\r\n\t\t\t            \t\t},\r\n\t\t\t            \t\tfail: function(res) {\r\n\t\t\t            \t\t\tapp.showLoading(false);\r\n\t\t\t            \t\t\tapp.alert(res.errMsg);\r\n\t\t\t            \t\t}\r\n\t\t\t            \t});\r\n\t\t\t            //}\r\n\t\t\t          },\r\n\t\t\t          complete(res){\r\n\t\t\t              console.log(res)\r\n\t\t\t          }\r\n\t\t\t        })\r\n\t\t\t    // #endif\r\n\t\t\t},\r\n\t\t\tremoveimg2:function(e){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar idx = e.currentTarget.dataset.idx;\r\n\t\t\t\tconsole.log(idx);\r\n\t\t\t\tvar field = e.currentTarget.dataset.formidx;\r\n\t\t\t\tvar editorFormdata = this.editorFormdata;\r\n\t\t\t\tif(!editorFormdata) editorFormdata = [];\r\n\t\t\t\teditorFormdata[idx] = '';\r\n\t\t\t\tthat.editorFormdata = editorFormdata\r\n\t\t\t\tthat.test = Math.random();\r\n\t\t\t\tthat.formvaldata[field] = '';\r\n\t\t\t\tthat.getdata()\r\n\t\t\t},\r\n\t\t\tupVideo:function(e){\r\n\t\t\t    var that = this;\r\n\t\t\t    var that = this;\r\n\t\t\t    var idx = e.currentTarget.dataset.idx;\r\n\t\t\t    var field = e.currentTarget.dataset.formidx;\r\n\t\t\t\t\tvar currentindex = that.currentindex;\r\n\t\t\t    var editorFormdata = this.editorFormdata;\r\n\t\t\t    if(!editorFormdata) editorFormdata = [];\r\n\t\t\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\t    var up_url = app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform +'/session_id/' +app.globalData.session_id;\r\n\t\t\t    uni.chooseVideo({\r\n\t\t\t      sourceType: ['camera', 'album'],\r\n\t\t\t      success: function (res) {\r\n\t\t\t        var path = res.tempFilePath;\r\n\t\t\t\t\t\t\tif(res.size > 0){\r\n\t\t\t\t\t\t\t\tvar maxsize =  that.form.content[idx].val6;\r\n\t\t\t\t\t\t\t\tif(maxsize){\r\n\t\t\t\t\t\t\t\t\tmaxsize = parseFloat(maxsize);\r\n\t\t\t\t\t\t\t\t\tif(maxsize > 0 && maxsize * 1024 * 1024 < res.size){\r\n\t\t\t\t\t\t\t\t\t\tapp.alert('视频文件过大');return;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t        app.showLoading('上传中');\r\n\t\t\t        console.log(path );\r\n\t\t\t        uni.uploadFile({\r\n\t\t\t          url: up_url,\r\n\t\t\t          filePath: path,\r\n\t\t\t          name: 'file',\r\n\t\t\t          success: function(res) {\r\n\t\t\t            app.showLoading(false);\r\n\t\t\t            var data = JSON.parse(res.data);\r\n\t\t\t            if (data.status == 1) {\r\n\t\t\t              that.formvaldata[field] = data.url;\r\n\t\t\t\t\r\n\t\t\t              editorFormdata[id] = data.url;\r\n\t\t\t              that.editorFormdata = editorFormdata;\r\n\t\t\t\t\t\t\t\t\t\tthat.getdata();\r\n\t\t\t              that.$set(that.editorFormdata, idx,data.url)\r\n\t\t\t            } else {\r\n\t\t\t              app.alert(data.msg);\r\n\t\t\t            }\r\n\t\t\t          },\r\n\t\t\t          fail: function(res) {\r\n\t\t\t            app.showLoading(false);\r\n\t\t\t            app.alert(res.errMsg);\r\n\t\t\t          }\r\n\t\t\t        });\r\n\t\t\t      }\r\n\t\t\t    });\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\tpage{\tbackground: #f6f6f6;}\r\n\t.banner{\tposition: absolute;\twidth: 100%;height: 700rpx;}\r\n\t.page{\tposition: relative;\tpadding: 30rpx;}\r\n\t.header{\tpadding: 30rpx;display: flex;justify-content: space-between;}\r\n\t.header_title{\tfont-size: 40rpx;\tcolor: #fff;\tfont-weight: bold;}\r\n\t.header_text{\tcolor: rgba(255, 255, 255, 0.8);\tfont-size: 24rpx;margin-top: 20rpx;\t\tdisplay: flex;align-items: center;}\r\n\t.header_icon{\t\theight: 30rpx;width: 30rpx;\tmargin: 0 0 0 10rpx;}\r\n\t.header_tag{\twidth: 80rpx;\t}\r\n\t.body{position: relative;\tpadding: 30rpx 50rpx;\tbackground: #fff;\tborder-radius: 20rpx;\tmargin-top: 20rpx;}\r\n\t.body_title{\tposition: relative;font-size: 35rpx;color: #333;\ttext-align: center;\tfont-weight: bold;}\r\n\t.body_title text{position: relative;}\r\n\t.body_icon{position: absolute;\twidth: 120rpx;\tleft: 0;\tright: 0;\tbottom: 0;\tmargin: 0 auto;}\r\n\t.content{\tmargin-top: 50rpx;}\r\n\t.module{\t\tposition: relative;\tpadding: 0 0 0 50rpx;\tmin-height: 200rpx;\tborder-left: 2px dashed #e4e5e7;}\r\n\t.module_title{\tfont-size: 28rpx;\tcolor: #333;\tfont-weight: bold;}\r\n\t.module_time{\tfont-size: 24rpx;\tcolor: #999;margin-top: 10rpx;}\r\n\t.module_text{\t\tfont-size: 26rpx;\tcolor: #666;\tmargin-top: 10rpx;}\r\n\t.module_opt{\tpadding: 30rpx 0 50rpx 0;}\r\n\t.module_btn{\tfont-size: 24rpx;color: #333;border: 1px solid #f0f0f0;\tpadding: 15rpx 30rpx;\tborder-radius: 100rpx;\tmargin: 0 10rpx 0 0;}\r\n\t.module_r{background: #fd3b60;color: #fff;}\r\n\t.module_tag{\tposition: absolute;\theight: 26px;width: 26px;left: -14px;\ttop: 0;\t}\r\n\t.module_active{\tborder-color: #fd3b60;}\r\n\t.module:last-child{\tborder-color: #fff;}\r\n\t.module_null{\t\tcolor: #999;}\r\n\t\r\n\t.module_opt .t3{ margin-bottom: 10rpx; font-size:26rpx; color:#666}\r\n\t.module_opt .t3_1{  color: #999;}\r\n\t.module_opt .t4{ margin-bottom: 10rpx; font-size:26rpx ;color: #999;}\r\n\t\r\n\t\r\n\t\r\n\t\r\n\t.modal .modal_jindu{ background: #fff;align-items: center; margin: auto; width: 100%;  border-radius: 10rpx; padding: 40rpx;margin-top: 30rpx;}\r\n\t.modal_jindu .close image { width: 20rpx; height: 20rpx; position: absolute; top:10rpx; right: 20rpx;}\r\n\t.modal_jindu .title{ font-size: 32rpx; font-weight: bold;}\r\n\t.uni-list{ margin-top: 30rpx;}\r\n\t.uni-list-cell{ display: flex; height: 80rpx;}\r\n\t.beizhu label{ width: 100rpx;}\r\n\t.modal_jindu .btn{  background: #1658c6; border-radius: 3px;line-height: 24px; border: none; padding: 0 10px;color: #fff;font-size: 20px; text-align: center; width: 300px;  display: flex; height: 40px; justify-content: center;align-items: center;}\r\n\t\r\n\t\r\n\t.modal_jindu .item .f1{ width:60rpx;position:relative}\r\n\t/*.logistics img{width: 15px; height: 15px; position: absolute; left: -8px; top:11px;}*/\r\n\t.modal_jindu .item .f1 image{width: 30rpx; height: 100%; position: absolute; left: -16rpx; top: 0rpx;}\r\n\t.modal_jindu .item .f2{display:flex;flex-direction:column;flex:auto;padding:10rpx 0}\r\n\t.modal_jindu .item .f2 .t1{font-size: 30rpx;}\r\n\t.modal_jindu .item .f2 .t1{font-size: 26rpx;}\r\n\t\r\n\t\r\n\t\r\n\t.form-item {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center;justify-content:space-between;border-bottom: 1px #ededed solid;}\r\n\t.form-item .label {color: #333;width: 150rpx;flex-shrink:0}\r\n\t.form-item .radio{transform:scale(.7);}\r\n\t.form-item .checkbox{transform:scale(.7);}\r\n\t.form-item .input {height: 70rpx;padding-left: 10rpx;text-align: right;flex:1;border:1px solid #eee;padding:0 8rpx;border-radius:2px;}\r\n\t.form-item .textarea{height:140rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}\r\n\t.form-item .radio-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\r\n\t.form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n\t.form-item .radio2{display:flex;align-items:center;}\r\n\t.form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}\r\n\t.form-item .checkbox-group{display:flex;flex-wrap:wrap;justify-content:flex-end}\r\n\t.form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}\r\n\t.form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}\r\n\t.form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}\r\n\t.form-item .picker{height: 70rpx;line-height:70rpx;flex:1;text-align:right}\r\n\t\r\n\t.form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n\t.form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff;z-index: 2;}\r\n\t.form-imgbox-close image{width:100%;height:100%}\r\n\t.form-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n\t.form-imgbox-img>image{max-width:100%;}\r\n\t.form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n\t.form-uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n\t\r\n\t.dp-form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n\t.dp-form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-10rpx;top:-26rpx;color:#999;font-size:32rpx;background:#999;z-index:9;border-radius:50%}\r\n\t.dp-form-imgbox-close .image{width:100%;height:100%}\r\n\t.dp-form-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n\t.dp-form-imgbox-img>.image{max-width:100%;}\r\n\t.dp-form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n\t.dp-form-uploadbtn{position:relative;height:100rpx;width:100rpx}\r\n\t\r\n\t\r\n\r\n\t.form-item4{width:100%;background: #fff; padding: 20rpx 0rpx;margin-top:1px}\r\n\t.form-item4 .label{ width:150rpx;}\r\n\t.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n\t.layui-imgbox-img{display: block;width:150rpx;height:150rpx;padding:2px;background-color: #f6f6f6;overflow:hidden ;margin-top: 20rpx;}\r\n\t.layui-imgbox-img>image{max-width:100%;}\r\n\t.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n\t.uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n\t\r\n\t.hfitem{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}\r\n\t.hfitem:last-child{ border-bottom: 0;}\r\n\t.hfitem .t1{width:200rpx;color: #999;}\r\n\t.hfitem .t1.title{font-size: 36rpx;font-weight: 600;line-height: 80rpx;width:100%}\r\n\t.hfitem .t2{flex:1;text-align:right}\r\n\t.hfitem .red{color:red}\r\n\t.zktext{ text-align: right;margin-top: 20rpx;  color: #1296db;\tfont-size: 28rpx; display:flex; align-items: center; justify-content: end; }\r\n\t.zktext image{ width:30rpx; height:30rpx}\r\n\t\r\n</style>\r\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jindu.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jindu.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213040613\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}