{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/formdetail.vue?f3d2", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/formdetail.vue?b2c3", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/formdetail.vue?c1ce", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/formdetail.vue?980f", "uni-app:///admin/workorder/formdetail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/formdetail.vue?eba9", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/workorder/formdetail.vue?2f75"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "detail", "formcontent", "showstatus", "ishowjindu", "jdlist", "lclist", "content_pic", "tempFilePaths", "pre_url", "content1", "currentindex", "onLoad", "onPullDownRefresh", "onShow", "methods", "getdata", "that", "app", "id", "console", "getliucheng", "cid", "setst", "close", "formsubmit", "logid", "lcid", "content", "setTimeout", "setst2", "setst2confirm", "st", "reason", "del", "uploadimg", "pics", "removeimg", "jindu", "<PERSON>jd", "formChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrFA;AAAA;AAAA;AAAA;AAAw0B,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuL51B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EAEA;EACAC;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QACAA;QACAA;QACAA;QACAG;QACAH;QACAA;MACA;IACA;IACAI;MACA;MACAH;QAAAI;MAAA;QACA;QACAL;MAEA;IACA;IACAM;MACA;MACA;MACAN;MACAA;IACA;IACAO;MACA;MACAP;IACA;IAEAQ;MACA;MACA;MACA;MACA;MACA;QACAP;QACA;MACA;MACA;MACAA;MACAA;QAAAQ;QAAAC;QAAAC;QAAArB;MAAA;QACA;QACAW;QACA;UACAA;UACAW;YACA;YACAX;UACA;QACA;UACAA;QACA;MAEA;IACA;IAEAY;MACA;IACA;IACAC;MACA;MACA;MACA;MACAb;QAAAC;QAAAa;QAAAC;QAAA1B;MAAA;QACAW;QACAW;UACAZ;QACA;MACA;IACA;IACAiB;MACA;MACA;MACAhB;QACAA;UAAAC;QAAA;UACAD;UACAW;YACAX;UACA;QACA;MACA;IACA;IACAiB;MACA;MACA;MACA;MACA;MAEAjB;QACA;UACAkB;QACA;QACAhB;MACA;IACA;IACAiB;MACA;MACA;MACA;MACA;MACAD;IACA;IACAE;MACA;MACArB;MACA;MACA;MACAC;QAAAC;MAAA;QACA;UACA;UACAF;QACA;MAEA;IACA;IACAsB;MACA;MACAtB;IACA;IACAuB;MACA;MACA;MACAvB;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC3VA;AAAA;AAAA;AAAA;AAAqrC,CAAgB,qmCAAG,EAAC,C;;;;;;;;;;;ACAzsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/workorder/formdetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/workorder/formdetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./formdetail.vue?vue&type=template&id=5e884bd5&\"\nvar renderjs\nimport script from \"./formdetail.vue?vue&type=script&lang=js&\"\nexport * from \"./formdetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./formdetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/workorder/formdetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./formdetail.vue?vue&type=template&id=5e884bd5&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"会员\") : null\n  var g0 = _vm.ishowjindu ? _vm.jdlist.length : null\n  var l1 =\n    _vm.ishowjindu && g0 > 0\n      ? _vm.__map(_vm.jdlist, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g1 = item.content_pic.length\n          var l0 = _vm.__map(item.hflist, function (hf, hfindex) {\n            var $orig = _vm.__get_orig(hf)\n            var g2 = hf.hfcontent_pic.length\n            return {\n              $orig: $orig,\n              g2: g2,\n            }\n          })\n          return {\n            $orig: $orig,\n            g1: g1,\n            l0: l0,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./formdetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./formdetail.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"orderinfo\">\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">提交人</text>\r\n\t\t\t\t<text class=\"flex1\"></text>\r\n\t\t\t\t<image :src=\"detail.headimg\" style=\"width:80rpx;height:80rpx;margin-right:8rpx\"/>\r\n\t\t\t\t<text  style=\"height:80rpx;line-height:80rpx\">{{detail.nickname}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">{{t('会员')}}ID</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.mid}}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">工单分类</text>\r\n\t\t\t\t<view class=\"t2\" @tap=\"goto\" :data-url=\"'updatecate?id='+detail.id\">{{detail.cname}}\r\n\t\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t<view class=\"item\">\r\n\t\t\t<text class=\"t1\">工单名称</text>\r\n\t\t\t<text class=\"t2\">{{detail.title}}</text>\r\n\t\t</view>\r\n\t\t\t\r\n\t\t\t<block v-for=\"(fitem,findex) in formcontent\" class=\"item\" >\r\n\t\t\t\t<view class=\"dp-form-separate\" @tap.stop=\"formChange\" :data-index=\"findex\" >{{fitem.name}}<image :src=\"pre_url+'/static/img/workorder/'+(currentindex==findex?'down':'up')+'.png'\"></view>\r\n\t\t\t\t<view class=\"parentitem\" :style=\"(currentindex!=findex?'display:none':'')\">\r\n\t\t\t\t\t<view class=\"item\" v-for=\"(item,idx) in fitem.list\" >\r\n\t\t\t\t\t\t<text class=\"t1\">{{item.val1}}</text>\r\n\t\t\t\t\t\t<text class=\"t2\" v-if=\"item.key!='upload' && item.key!='upload_file' && item.key!='upload_video'\" >{{detail['form'+idx]}}</text>\r\n\t\t\t\t\t\t<view class=\"t2\" style=\"display: flex; justify-content: flex-end;\"  v-if=\"item.key=='upload'\">\r\n\t\t\t\t\t\t\t<view v-for=\"(sub, indx) in detail['form'+idx]\" :key=\"indx\">\r\n\t\t\t\t\t\t\t\t<image :src=\"sub\" style=\"width:50px; margin-left: 10rpx;\" mode=\"widthFix\" @tap=\"previewImage\" :data-url=\"sub\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- #ifdef !H5 && !MP-WEIXIN -->\r\n\t\t\t\t\t\t<view class=\"t2\" v-if=\"item.key=='upload_file'\" style=\"overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;\">\r\n\t\t\t\t\t\t\t\t{{detail['form'+idx]}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<!-- #ifdef H5 || MP-WEIXIN -->\r\n\t\t\t\t\t\t<view class=\"t2\" v-if=\"item.key=='upload_file'\"  @tap=\"download\" :data-file=\"detail['form'+idx]\" style=\"overflow: hidden;white-space: pre-wrap;word-wrap: break-word;color: #4786BC;\">\r\n\t\t\t\t\t\t\t\t点击下载查看\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<view class=\"t2\"  v-if=\"item.key=='upload_video'\">\r\n\t\t\t\t\t\t\t\t<video  :src=\"detail['form'+idx]\" style=\"width: 100%;\"/></video>\r\n\t\t\t\t\t\t</view>\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\t\r\n\t\t\t</block>\r\n\t\t\t\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">提交时间</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.createtime}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">审核状态</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0 && (!detail.payorderid||detail.paystatus==1)\" style=\"color:#88e\">待处理</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==0 && detail.payorderid && detail.paystatus==0\" style=\"color:red\">待支付</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==1\" style=\"color:green\">处理中</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==2\" style=\"color:green\">已完成</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.status==-1\" style=\"color:red\">已驳回</text>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.status==-1\">\r\n\t\t\t\t<text class=\"t1\">驳回原因</text>\r\n\t\t\t\t<text class=\"t2\" style=\"color:red\">{{detail.reason}}</text>\r\n\t\t\t</view>\r\n\t\t\t<block v-if=\"form.payset==1\">\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">付款金额</text>\r\n\t\t\t\t<text class=\"t2\" style=\"font-size:32rpx;color:#e94745\">￥{{detail.money}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">付款方式</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.paytype}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<text class=\"t1\">付款状态</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.paystatus==1 && detail.isrefund==0\" style=\"color:green\">已付款</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.paystatus==1 && detail.isrefund==1\" style=\"color:red\">已退款</text>\r\n\t\t\t\t<text class=\"t2\" v-if=\"detail.paystatus==0\" style=\"color:red\">未付款</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"detail.paystatus>0 && detail.paytime\">\r\n\t\t\t\t<text class=\"t1\">付款时间</text>\r\n\t\t\t\t<text class=\"t2\">{{detail.paytime}}</text>\r\n\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"detail.iscomment==1\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<text class=\"t1\">满意度</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.comment_status==1\" style=\"color:red\">不满意</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.comment_status==2\" style=\"color:#88e\">一般</text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"detail.comment_status==3\" style=\"color:green\">满意</text>\r\n\t\t\t\t\t</text>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\t\r\n\t\t\t\r\n\t\t</view>\r\n\r\n\t\t<view style=\"width:100%;height:160rpx\"></view>\r\n\t\t\r\n\t\t<view class=\"bottom notabbarbot\">\r\n\t\t\t<view v-if=\"detail.status>0\" class=\"btn2\" @tap=\"goto\"  :data-url=\"'jindu?id='+detail.id+'&cid='+detail.cid\">查看进度</view>\r\n\t\t\t<view v-if=\"detail.status==0\" class=\"btn2\" @tap=\"setst2\" :data-st=\"-1\" :data-id=\"detail.id\">驳回</view>\r\n\t\t\t<view class=\"btn2\" @tap=\"del\" :data-id=\"detail.id\">删除</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t<uni-popup id=\"dialogSetst2\" ref=\"dialogSetst2\" type=\"dialog\">\r\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"驳回原因\" :value=\"detail.reason\" placeholder=\"请输入驳回原因\"  @confirm=\"setst2confirm\"></uni-popup-dialog>\r\n\t\t</uni-popup>\r\n\t</block>\r\n\t\r\n\t<view class=\"modaljd\" v-if=\"ishowjindu\">\r\n\t\t<view class=\"modal_jindu2\">\r\n\t\t\t\t<view class=\"close\" @tap=\"closejd\"><image :src=\"pre_url+'/static/img/close.png'\" /></view>\r\n\t\t\t\t<block v-if=\"jdlist.length>0\">\r\n\t\t\t\t\t<view class=\"item \" v-for=\"(item,index) in jdlist\" :key=\"index\" style=\"display: flex;\">\r\n\t\t\t\t\t\t<view class=\"f1\"><image :src=\"'/static/img/jindu' + (index==0?'2':'1') + '.png'\"></image></view>\r\n\t\t\t\t\t\t<view class=\"f2\">\r\n\t\t\t\t\t\t\t<text class=\"t2\"> 时间：{{item.time}}</text>\r\n\t\t\t\t\t\t\t<text class=\"t1\">{{item.desc}}({{item.remark}}) </text>\r\n\t\t\t\t\t\t\t<view v-if=\"item.content_pic.length>0\" v-for=\"(pic, ind) in item.content_pic\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"pic\" @tap=\"previewImage\" :data-url=\"pic\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view v-for=\"(hf,hfindex) in item.hflist\" :key=\"hfindex\">\r\n\t\t\t\t\t\t\t\t<view class=\"t3\" v-if=\"hf.hfremark\" >用户回复：{{hf.hfremark}} </view>\r\n\t\t\t\t\t\t\t\t<view class=\"t4\" v-if=\"hf.hftime\" >回复时间：{{hf.hftime}} </view>\r\n\t\t\t\t\t\t\t\t<view v-if=\"hf.hfcontent_pic.length>0\" v-for=\"(pic2, ind2) in hf.hfcontent_pic\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"layui-imgbox-img\"><image :src=\"pic2\" @tap=\"previewImage\" :data-url=\"pic2\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t<view style=\"font-size:14px;color:#f05555;padding:10px;\">等待处理</view>\r\n\t\t\t\t</block>\r\n\t\t</view>\r\n\t</view>\r\n\t\r\n\t\r\n\t<view class=\"modal\" v-if=\"showstatus\">\r\n\t\t<view class=\"modal_jindu\">\r\n\t\t\t<form   @submit=\"formsubmit\">\r\n\t\t\t\t\t<view class=\"close\" @tap=\"close\"><image :src=\"pre_url+'/static/img/close.png'\" /></view>\r\n\t\t\t\t\t<view class=\"title\">选择处理流程</view>\r\n\t\t\t\t\t<view class=\"uni-list\">\r\n\t\t\t\t\t\t\t<radio-group name=\"liucheng\">\r\n\t\t\t\t\t\t\t\t\t<label class=\"uni-list-cell uni-list-cell-pd\" v-for=\"(item, index) in lclist\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<radio :value=\"''+item.id\" style=\"transform:scale(0.7)\"/>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view>{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t\t\t<view class=\"beizhu flex\">\r\n\t\t\t\t\t\t\t\t\t<label>备注1:</label><textarea placeholder=\"输入内容\"  style=\"height: 100rpx;\" name=\"content\" maxlength=\"-1\"></textarea>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class=\"btn\" form-type=\"submit\">提交</button>\r\n\t\t\t</form>\r\n\t\t</view>\r\n\t</view>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n\t\t\tdetail:{},\r\n\t\t\tformcontent:[],\r\n\t\t\tshowstatus:0,\r\n\t\t\tishowjindu:false,\r\n\t\t\tjdlist:[],\r\n\t\t\tlclist:[],\r\n\t\t\tcontent_pic: [],\r\n\t\t\ttempFilePaths: \"\",\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n\t\t\tcontent1:'11',\r\n\t\t\tcurrentindex:0\r\n    };\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n\t\t\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonShow:function(){\r\n\t\t\r\n\t},\r\n  methods: {\r\n\t\tgetdata: function () {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiAdminWorkorder/formdetail', {id: that.opt.id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.form = res.form;\r\n\t\t\t\tthat.formcontent = res.formcontent;\r\n\t\t\t\tthat.detail = res.detail;\r\n\t\t\t\tthat.content_pic=[]\r\n\t\t\t\tthat.content1 = '';\r\n\t\t\t\tconsole.log(that.content1 );\r\n\t\t\t\tthat.loaded();\r\n\t\t\t\tthat.getliucheng();\r\n\t\t\t});\r\n\t\t},\r\n\t\tgetliucheng:function(e){\r\n\t\t\tvar that=this\r\n\t\t\tapp.post('ApiAdminWorkorder/getliucheng', {cid: that.detail.formid}, function (res) {\r\n\t\t\t\t\tvar lclist = res.datalist;\r\n\t\t\t\t\tthat.lclist = lclist;\r\n\t\t\t\t\t\r\n\t\t\t});\r\n\t\t},\r\n\t\tsetst:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\tthat.id=id\r\n\t\t\tthat.showstatus=true;\r\n\t\t},\r\n\t\tclose:function(e){\r\n\t\t\tvar that=this\r\n\t\t\tthat.showstatus=false\r\n\t\t},\r\n\t\t\r\n\t\tformsubmit: function (e) {\r\n\t\t  var that = this;\t\t\r\n\t\t  var formdata = e.detail.value;\r\n\t\t  var content = formdata.content;\r\n\t\t  var liucheng = formdata.liucheng;\r\n\t\t  if (liucheng == '') {\r\n\t\t    app.error('请选择流程');\r\n\t\t    return false;\r\n\t\t  }\r\n\t\t\tvar content_pic = that.content_pic;\r\n\t\t\tapp.showLoading();\r\n\t\t  app.post('ApiAdminWorkorder/addjindu', {logid:that.opt.id,lcid:liucheng,content: content,content_pic: content_pic.join(',')}, function (res) {\r\n\t\t\t\tvar res  = res\r\n\t\t  \tapp.showLoading(false);\r\n\t\t    if (res.status == 1) {\r\n\t\t      app.alert('处理成功');\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t  //that.getdata();\r\n\t\t\t\t\t\t\tapp.goto('formdetail?id='+that.opt.id);\r\n\t\t\t\t\t}, 1000);\r\n\t\t    } else {\r\n\t\t      app.alert(res.msg);\r\n\t\t    }\r\n\r\n\t\t  });\r\n\t\t},\r\n\t\t\r\n\t\tsetst2:function(e){\r\n\t\t\tthis.$refs.dialogSetst2.open();\r\n\t\t},\r\n\t\tsetst2confirm:function(done,value){\r\n\t\t\tthis.$refs.dialogSetst2.close();\r\n      var that = this;\r\n\t\t\tvar content_pic = that.content_pic;\r\n      app.post('ApiAdminWorkorder/formsetst', {id: that.opt.id,st:-1,reason:value,content_pic: content_pic.join(',')}, function (data) {\r\n        app.success(data.msg);\r\n        setTimeout(function () {\r\n          that.getdata();\r\n        }, 1000);\r\n      });\r\n\t\t},\r\n\t\tdel:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = e.currentTarget.dataset.id;\r\n\t\t\tapp.confirm('确定要删除吗?',function(){\r\n\t\t\t\tapp.post('ApiAdminWorkorder/formdel', {id:id}, function (res) {\r\n\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tapp.goto('/adminCore/index/index');\r\n\t\t\t\t\t},1000);\r\n\t\t\t\t})\r\n\t\t\t});\r\n\t\t},\r\n\t\tuploadimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tvar pics = that[field]\r\n\t\t\tif(!pics) pics = [];\r\n\t\t\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tfor(var i=0;i<urls.length;i++){\r\n\t\t\t\t\tpics.push(urls[i]);\r\n\t\t\t\t}\r\n\t\t\t\t\t\t\t\tconsole.log(pics);\r\n\t\t\t},5)\r\n\t\t},\r\n\t\tremoveimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tvar pics = that[field]\r\n\t\t\tpics.splice(index,1)\r\n\t\t},\r\n\t\tjindu:function(e){\r\n\t\t\tvar that=this\r\n\t\t\tthat.ishowjindu=true\r\n\t\t\tvar id = e.currentTarget.dataset.id\r\n\t\t\t//读取进度表\r\n\t\t\tapp.post('ApiWorkorder/selectjindu', { id: id }, function (res) {\r\n\t\t\t\t\tif(res.status==1){\r\n\t\t\t\t\t\tvar data = res.data\r\n\t\t\t\t\t\tthat.jdlist =data\r\n\t\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t})\r\n\t\t},\r\n\t\tclosejd:function(e){\r\n\t\t\tvar that=this\r\n\t\t\tthat.ishowjindu=false\r\n\t\t},\r\n\t\tformChange:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tthat.currentindex = index\r\n\t\t},\r\n  }\r\n};\r\n</script>\r\n<style>\r\n\r\n.orderinfo{ width:100%;margin-top:10rpx;padding: 14rpx 3%;background: #FFF;}\r\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}\r\n.orderinfo .item:last-child{ border-bottom: 0;}\r\n.orderinfo .item .t1{width:200rpx;}\r\n.orderinfo .item .t1.title{font-size: 36rpx;font-weight: 600;line-height: 80rpx;width:100%}\r\n\r\n.orderinfo .item .t2{flex:1;text-align:right}\r\n.orderinfo .item .red{color:red}\r\n\r\n.bottom{ width: 100%;height:92rpx;padding: 0 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;}\r\n\r\n.btn1{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center}\r\n.btn2{margin-left:20rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\r\n.btn3{position:absolute;top:60rpx;right:10rpx;font-size:24rpx;width:120rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center}\r\n\r\n.modaljd{ position: fixed; background:rgba(0,0,0,0.3); width: 100%; height: 100%; top:0; z-index: 100;}\r\n.modaljd .modal_jindu2{ background: #fff; position: absolute; top: 20%; align-items: center; margin: auto; width: 90%; left: 30rpx; border-radius: 10rpx; padding: 40rpx; max-height: 600rpx; overflow-y:auto; display: flex; flex-wrap: wrap;}\r\n.modal_jindu2 .close image { width: 30rpx; height: 30rpx; position: fixed; top:21%;right: 60rpx;}\r\n.modal_jindu2 .title{ font-size: 32rpx; font-weight: bold;}\r\n.modal_jindu2 .item .f1{position:relative; }\r\n/*.logistics img{width: 15px; height: 15px; position: absolute; left: -8px; top:11px;}*/\r\n.modal_jindu2 .item .f1 image{width: 30rpx; height:100rpx;padding:10rpx 0;position: absolute; left: -16rpx; top: 0rpx;}\r\n.modal_jindu2 .item .f2{display:flex;flex-direction:column;flex:auto;padding:10rpx 0; margin-left:30rpx; }\r\n.modal_jindu2 .item .f2 .t1{font-size: 30rpx;word-break: break-word; width: 90%;}\r\n.modal_jindu2 .item .f2 .t1{font-size: 26rpx;}\r\n.modal_jindu2 .item .f2 .t3{font-size: 24rpx; color:#008000; margin-top: 10rpx;}\r\n.modal_jindu2 .item .f2 .t4{font-size: 24rpx;  color:#008000;}\r\n\r\n/*流程处理*/\r\n.modal .modal_jindu{ background: #fff;  align-items: center; margin: auto; width: 100%; margin-top: 10rpx; padding: 40rpx;}\r\n.modal_jindu .title{ font-size: 32rpx; font-weight: bold;}\r\n.modal_jindu .btn{  background: #1658c6; border-radius: 3px;line-height: 24px; border: none; padding: 0 10px;color: #fff;font-size: 20px; text-align: center; width: 300px;  display: flex; height: 40px; justify-content: center;align-items: center;}\r\n\r\n.uni-list{ margin-top: 30rpx;}\r\n.uni-list-cell{ display: flex; height: 80rpx;}\r\n.beizhu label{ width: 100rpx;}\r\n\r\n\r\n\r\n.form-item4{width:100%;background: #fff; padding: 20rpx 0rpx;margin-top:1px}\r\n.form-item4 .label{ width:150rpx;}\r\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.layui-imgbox-img{display: block;width:150rpx;height:150rpx;padding:2px;background-color: #f6f6f6;overflow:hidden ;margin-top: 20rpx;}\r\n.layui-imgbox-img>image{max-width:100%;}\r\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n\r\n.dp-form-separate{width: 100%;padding: 20rpx 0;font-size: 30rpx;font-weight: bold;color: #454545; display:flex; justify-content: space-between;align-items: center;}\r\n.dp-form-separate image{ width:40rpx; height:40rpx }\r\n\t\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./formdetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./formdetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213040358\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}