{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/detail.vue?f9c9", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/detail.vue?f863", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/detail.vue?9089", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/detail.vue?76dc", "uni-app:///admin/member/detail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/detail.vue?1fe8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/detail.vue?872f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "member", "index2", "levelList", "levelList2", "ordershow", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "mid", "uni", "title", "recharge", "rechargeConfirm", "rechargemid", "rechargemoney", "setTimeout", "consume", "consumeConfirm", "type", "addscore", "addscoreConfirm", "rechargescore", "remark", "remarkConfirm", "<PERSON><PERSON><PERSON>", "changelv", "dialogChangelvClose", "levelChange", "confirmChangelv", "console", "changemid", "levelid"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjEA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+Gx1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QACAG;UACAC;QACA;QAEA;QACA;UACAV;QACA;QACAM;QACAA;QAEAA;MACA;IACA;IACAK;MACA;IACA;IACAC;MACA;MACA;MACAL;QAAAM;QAAAC;MAAA;QACA;UACAP;UACA;QACA;QACAA;QACAQ;UACAT;QACA;MACA;IACA;IACAU;MACA;IACA;IACAC;MACA;MACA;MACAV;QAAAM;QAAAC;QAAAI;MAAA;QACA;UACAX;UACA;QACA;QACAA;QACAQ;UACAT;QACA;MACA;IACA;IACAa;MACA;IACA;IACAC;MACA;MACA;MACAb;QAAAM;QAAAQ;MAAA;QACAd;QACAQ;UACAT;QACA;MACA;IACA;IACAgB;MACA;IACA;IACAC;MACA;MACA;MACAhB;QAAAiB;QAAAF;MAAA;QACAf;QACAQ;UACAT;QACA;MACA;IACA;IACAmB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAC;MACAA;MACA;MACAtB;QAAAuB;QAAAC;MAAA;QACAxB;QACAD;QACAS;UACAT;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5OA;AAAA;AAAA;AAAA;AAAirC,CAAgB,imCAAG,EAAC,C;;;;;;;;;;;ACArsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/member/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/member/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=8f47e4f0&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/member/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=8f47e4f0&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"余额\") : null\n  var m1 = _vm.isload ? _vm.t(\"积分\") : null\n  var m2 = _vm.isload ? _vm.t(\"积分\") : null\n  var m3 = _vm.isload ? _vm.t(\"积分\") : null\n  var m4 = _vm.isload ? _vm.t(\"积分\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n<view>\n\t<block v-if=\"isload\">\n\t\t<view class=\"orderinfo\">\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">ID</text>\n\t\t\t\t<text class=\"t2\">{{member.id}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">头像</text>\n\t\t\t\t<view class=\"t2\"><image :src=\"member.headimg\" style=\"width:80rpx;height:80rpx\"></image></view>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">昵称</text>\n\t\t\t\t<text class=\"t2\">{{member.nickname}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">地区</text>\n\t\t\t\t<text class=\"t2\">{{member.province ? member.province : '' }}{{member.city ? member.city : '' }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">加入时间</text>\n\t\t\t\t<text class=\"t2\">{{member.createtime}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">姓名</text>\n\t\t\t\t<text class=\"t2\">{{member.realname ? member.realname : '' }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">电话</text>\n\t\t\t\t<text class=\"t2\">{{member.tel ? member.tel : '' }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">{{t('余额')}}</text>\n\t\t\t\t<text class=\"t2\">{{member.money}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">{{t('积分')}}</text>\n\t\t\t\t<text class=\"t2\">{{member.score}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\">\n\t\t\t\t<text class=\"t1\">等级</text>\n\t\t\t\t<text class=\"t2\">{{member.levelname}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"item\" v-if=\"member.remark\">\n\t\t\t\t<text class=\"t1\">备注</text>\n\t\t\t\t<text class=\"t2\">{{member.remark}}</text>\n\t\t\t</view>\n      <view class=\"item\" v-if=\"member.mendian_member_levelup_fenhong\">\n        <text class=\"t1\">门店</text>\n        <text class=\"t2\">{{member.mdname?member.mdname:'无'}}</text>\n      </view>\n\t\t\t<view class=\"item\" v-if=\"ordershow\" style=\"justify-content: space-between;\">\n\t\t\t\t<text class=\"t1\" style=\"color: #007aff;\">商城订单</text>\n\t\t\t\t<view class=\"flex\" @tap=\"goto\" :data-url=\"'/admin/order/shoporder?mid='+member.id\" >{{member.ordercount}}\t<text class=\"iconfont iconjiantou\" style=\"color:#999;font-weight:normal; margin-top: 2rpx;\"></text></view>\n\t\t\t</view>\t\n\t\t</view>\n\t\t<view style=\"width:100%;height:120rpx\"></view>\n\t\t<view class=\"bottom\">\n\t\t\t<view class=\"btn\" @tap=\"recharge\" :data-id=\"member.id\">充值</view>\n      <view class=\"btn\" @tap=\"consume\" :data-id=\"member.id\">消费</view>\n\t\t\t<view class=\"btn\" @tap=\"addscore\" :data-id=\"member.id\">加{{t('积分')}}</view>\n\t\t\t<view class=\"btn\" @tap=\"changelv\" :data-id=\"member.id\">修改等级</view>\n\t\t\t<view class=\"btn\" @tap=\"remark\" :data-id=\"member.id\">备注</view>\n\t\t\t<view class=\"btn\" @tap=\"goto\" :data-url=\"'/admin/member/history?id='+member.id\">足迹</view>\n\t\t\t<view class=\"btn\" @tap=\"goto\" :data-url=\"'richinfo?id='+member.id\" v-if=\"member.showrichinfo\">介绍</view>\n\t\t</view>\n\t\t\n\t\t<uni-popup id=\"rechargeDialog\" ref=\"rechargeDialog\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"充值\" value=\"\" placeholder=\"请输入充值金额\" @confirm=\"rechargeConfirm\"></uni-popup-dialog>\n\t\t</uni-popup>\n    <uni-popup id=\"consumeDialog\" ref=\"consumeDialog\" type=\"dialog\">\n    \t<uni-popup-dialog mode=\"input\" title=\"消费\" value=\"\" placeholder=\"请输入消费金额\" @confirm=\"consumeConfirm\"></uni-popup-dialog>\n    </uni-popup>\n    \n\t\t<uni-popup id=\"addscoreDialog\" ref=\"addscoreDialog\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" :title=\"'加'+t('积分')\" value=\"\" :placeholder=\"'请输入增加'+t('积分')+'数'\" @confirm=\"addscoreConfirm\"></uni-popup-dialog>\n\t\t</uni-popup>\n\t\t<uni-popup id=\"remarkDialog\" ref=\"remarkDialog\" type=\"dialog\">\n\t\t\t<uni-popup-dialog mode=\"input\" title=\"设置备注\" value=\"\" placeholder=\"请输入备注\" @confirm=\"remarkConfirm\"></uni-popup-dialog>\n\t\t</uni-popup>\n\n\t\t\n\t\t<uni-popup id=\"dialogChangelv\" ref=\"dialogChangelv\" type=\"dialog\">\n\t\t\t<view class=\"uni-popup-dialog\">\n\t\t\t\t<view class=\"uni-dialog-title\">\n\t\t\t\t\t<text class=\"uni-dialog-title-text\">请选择等级</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-content\">\n\t\t\t\t\t\t<picker @change=\"levelChange\" :value=\"index2\" :range=\"levelList2\" style=\"width:100%;font-size:28rpx;border: 1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1\">\n\t\t\t\t\t\t\t<view class=\"picker\">{{levelList2[index2]}}</view>\n\t\t\t\t\t\t</picker>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"uni-dialog-button-group\">\n\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogChangelvClose\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"confirmChangelv\">\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\n\t</block>\n\t<popmsg ref=\"popmsg\"></popmsg>\n\t<loading v-if=\"loading\"></loading>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n  data() {\n    return {\n      isload: false,\n      member: \"\",\n\t\t\tindex2:0,\n\t\t\tlevelList:[],\n\t\t\tlevelList2:[],\n\t\t\tordershow:false\n    };\n  },\n  onLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n  },\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n  methods: {\n\t\tgetdata:function(){\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.post('ApiAdminMember/detail', {mid: that.opt.mid}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tthat.member = res.member;\n\t\t\t\tthat.ordershow = res.ordershow\n\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\ttitle: that.t('会员') + '信息'\n\t\t\t\t});\n\n\t\t\t\tvar levelList2 = [];\n\t\t\t\tfor(var i in res.levelList){\n\t\t\t\t\tlevelList2.push(res.levelList[i].name);\n\t\t\t\t}\n\t\t\t\tthat.levelList = res.levelList;\n\t\t\t\tthat.levelList2 = levelList2;\n\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n    recharge: function (e) {\n      this.$refs.rechargeDialog.open();\n    },\n    rechargeConfirm: function (done,value) {\n\t\t\tthis.$refs.rechargeDialog.close();\n      var that = this;\n      app.post('ApiAdminMember/recharge', {rechargemid:that.opt.mid,rechargemoney:value}, function (data) {\n\t\t\t\tif (data.status == 0) {\n\t\t\t\t  app.error(data.msg);\n\t\t\t\t  return;\n\t\t\t\t}\n        app.success(data.msg);\n        setTimeout(function () {\n          that.getdata();\n        }, 1000);\n      });\n    },\n    consume: function (e) {\n      this.$refs.consumeDialog.open();\n    },\n    consumeConfirm: function (done,value) {\n    \tthis.$refs.consumeDialog.close();\n      var that = this;\n      app.post('ApiAdminMember/recharge', {rechargemid:that.opt.mid,rechargemoney:-value,type:'consume'}, function (data) {\n    \t\tif (data.status == 0) {\n    \t\t  app.error(data.msg);\n    \t\t  return;\n    \t\t}\n        app.success(data.msg);\n        setTimeout(function () {\n          that.getdata();\n        }, 1000);\n      });\n    },\n    addscore: function (e) {\n      this.$refs.addscoreDialog.open();\n    },\n   addscoreConfirm: function (done,value) {\n\t\t\tthis.$refs.addscoreDialog.close();\n      var that = this;\n      app.post('ApiAdminMember/addscore', {rechargemid:that.opt.mid,rechargescore:value}, function (data) {\n        app.success(data.msg);\n        setTimeout(function () {\n          that.getdata();\n        }, 1000);\n      });\n    },\n\t\tremark:function(e){\n\t\t\tthis.$refs.remarkDialog.open();\n\t\t},\n\t\tremarkConfirm: function (done,value) {\n\t\t\tthis.$refs.remarkDialog.close();\n      var that = this;\n      app.post('ApiAdminMember/remark', {remarkmid:that.opt.mid,remark:value}, function (data) {\n        app.success(data.msg);\n        setTimeout(function () {\n          that.getdata();\n        }, 1000);\n      });\n    },\n\t\tchangelv:function(){\n\t\t\tthis.$refs.dialogChangelv.open();\n\t\t},\n\t\tdialogChangelvClose:function(){\n\t\t\tthis.$refs.dialogChangelv.close();\n\t\t},\n\t\tlevelChange:function(e){\n\t\t\tthis.index2 = e.detail.value;\n\t\t},\n\t\tconfirmChangelv:function(){\n\t\t\tvar that = this\n\t\t\tconsole.log(this.index2);\n\t\t\tconsole.log(this.levelList[this.index2]);\n\t\t\tvar levelid = this.levelList[this.index2].id\n\t\t\tapp.post('ApiAdminMember/changelv', { changemid:that.opt.mid,levelid:levelid}, function (res) {\n\t\t\t\tapp.success(res.msg);\n\t\t\t\tthat.$refs.dialogChangelv.close();\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tthat.getdata();\n\t\t\t\t}, 1000)\n\t\t\t})\n\t\t},\n  }\n};\n</script>\n<style>\n\n.orderinfo{ width:94%;margin:20rpx 3%;border-radius:16rpx;padding: 14rpx 3%;background: #FFF;}\n.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}\n.orderinfo .item:last-child{ border-bottom: 0;}\n.orderinfo .item .t1{width:200rpx;}\n.orderinfo .item .t2{flex:1;text-align:right}\n.orderinfo .item .red{color:red}\n\n.bottom{ width: 100%; padding: 16rpx 20rpx;background: #fff; position: fixed; bottom: 0px; left: 0px;display:flex;justify-content:flex-end;align-items:center;flex-wrap:wrap}\n.bottom .btn{ border-radius:10rpx; padding:10rpx 16rpx;margin-left: 10rpx;border: 1px #999 solid;}\n\n.uni-popup-dialog {width: 300px;border-radius: 5px;background-color: #fff;}\n.uni-dialog-title {display: flex;flex-direction: row;justify-content: center;padding-top: 15px;padding-bottom: 5px;}\n.uni-dialog-title-text {font-size: 16px;font-weight: 500;}\n.uni-dialog-content {display: flex;flex-direction: row;justify-content: center;align-items: center;padding: 5px 15px 15px 15px;width:100%}\n.uni-dialog-content-text {font-size: 14px;color: #6e6e6e;}\n.uni-dialog-button-group {display: flex;flex-direction: row;border-top-color: #f5f5f5;border-top-style: solid;border-top-width: 1px;}\n.uni-dialog-button {display: flex;flex: 1;flex-direction: row;justify-content: center;align-items: center;height: 45px;}\n.uni-border-left {border-left-color: #f0f0f0;border-left-style: solid;border-left-width: 1px;}\n.uni-dialog-button-text {font-size: 14px;}\n.uni-button-color {color: #007aff;}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213046206\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}