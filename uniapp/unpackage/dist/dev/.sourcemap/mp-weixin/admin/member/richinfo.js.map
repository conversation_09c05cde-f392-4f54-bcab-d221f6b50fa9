{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/richinfo.vue?e256", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/richinfo.vue?a683", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/richinfo.vue?9dad", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/richinfo.vue?c8eb", "uni-app:///admin/member/richinfo.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/richinfo.vue?5d30", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/member/richinfo.vue?1569"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "loading", "pre_url", "info", "pagecontent", "edit_index", "test", "onLoad", "methods", "getdata", "that", "app", "mid", "subform", "setTimeout", "detailAddtxt", "dialogDetailtxtClose", "catcheDetailtxt", "console", "dialogDetailtxtConfirm", "detailAddpic", "detailAddpicConfirm", "pics", "detailAddvideo", "detailAddvideoConfirm", "uni", "sourceType", "success", "url", "filePath", "name", "fail", "detailMoveup", "detailMovedown", "detailMovedel", "detailMoveEdit", "uploadimg", "removeimg"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,iNAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,qMAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,2MAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,iQAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjNA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiK11B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAC;QAAAC;MAAA;QACAF;QACAA;QACAA;QACAA;MACA;IACA;IACAG;MACA;MACA;MACA;MACAF;QAAAC;QAAAT;QAAAC;MAAA;QACA;UACAO;QACA;UACAA;UACAG;YACAH;UACA;QACA;MACA;IACA;IACAI;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAC;MACA;IACA;IACAC;MACA;MACAD;MACA;MACA;MACA;QACAd;QACAA;MACA;QACAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;cAAA;YAAA;YAAA;cAAA;YAAA;UAAA;UAAA;UAAA;UAAA;QAAA;MACA;MACA;MACA;MACA;IACA;IACAgB;MACA;MACA;IACA;IACAC;MACA;MACAV;QACA;QACA;QACA;UACA;UACAW;YAAA;YAAA;YAAA;YAAA;UAAA;QACA;QACA;QACA;UACAlB;QACA;UACAA;YAAA;YAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;gBAAA;cAAA;cAAA;gBAAA;cAAA;YAAA;YAAA;YAAA;YAAA;UAAA;QACA;QACAM;QACAA;MACA;IACA;IACAa;MACA;MACA;IACA;IACAC;MACA;MACAC;QACAC;QACAC;UACA;UACAhB;UACAc;YACAG;YACAC;YACAC;YACAH;cACAhB;cACA;cACA;gBACAD;gBACA;gBACA;gBACA;kBACAN;gBACA;kBACAA;oBAAA;oBAAA;oBAAA;sBAAA;sBAAA;sBAAA;sBAAA;sBAAA;sBAAA;sBAAA;wBAAA;sBAAA;sBAAA;wBAAA;sBAAA;oBAAA;oBAAA;oBAAA;oBAAA;kBAAA;gBACA;gBACAM;gBACAA;cACA;gBACAC;cACA;YACA;YACAoB;cACApB;cACAA;YACA;UACA;QACA;QACAoB;UACAb;QACA;MACA;IACA;;IACAc;MACA;MACA;MACA,eACA5B;IACA;IACA6B;MACA;MACA;MACA,oCACA7B;IACA;IACA8B;MACA;MACA;MACA9B;IACA;IACA+B;MACA;MACA;MACAjB;MACA;MACAA;MACA;QACA;MACA;QACA;MACA;QACA;MACA,QAEA;IACA;IACAkB;MACA;MACA;MACA;MACA;MACA;MACA;MACAzB;QACA;UACAW;QACA;QACA;QACA;MACA;IACA;IACAe;MACA;MACA;MACA;MACA;QACA;QACAf;QACAZ;MACA;QACA;QACAY;QACAZ;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3WA;AAAA;AAAA;AAAA;AAAmrC,CAAgB,mmCAAG,EAAC,C;;;;;;;;;;;ACAvsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/member/richinfo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/member/richinfo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./richinfo.vue?vue&type=template&id=6eb06481&\"\nvar renderjs\nimport script from \"./richinfo.vue?vue&type=script&lang=js&\"\nexport * from \"./richinfo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./richinfo.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/member/richinfo.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./richinfo.vue?vue&type=template&id=6eb06481&\"", "var components\ntry {\n  components = {\n    dpNotice: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-notice/dp-notice\" */ \"@/components/dp-notice/dp-notice.vue\"\n      )\n    },\n    dpBanner: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-banner/dp-banner\" */ \"@/components/dp-banner/dp-banner.vue\"\n      )\n    },\n    dpSearch: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-search/dp-search\" */ \"@/components/dp-search/dp-search.vue\"\n      )\n    },\n    dpText: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-text/dp-text\" */ \"@/components/dp-text/dp-text.vue\"\n      )\n    },\n    dpTitle: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-title/dp-title\" */ \"@/components/dp-title/dp-title.vue\"\n      )\n    },\n    dpDhlist: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-dhlist/dp-dhlist\" */ \"@/components/dp-dhlist/dp-dhlist.vue\"\n      )\n    },\n    dpLine: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-line/dp-line\" */ \"@/components/dp-line/dp-line.vue\"\n      )\n    },\n    dpBlank: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-blank/dp-blank\" */ \"@/components/dp-blank/dp-blank.vue\"\n      )\n    },\n    dpMenu: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-menu/dp-menu\" */ \"@/components/dp-menu/dp-menu.vue\"\n      )\n    },\n    dpMap: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-map/dp-map\" */ \"@/components/dp-map/dp-map.vue\"\n      )\n    },\n    dpCube: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-cube/dp-cube\" */ \"@/components/dp-cube/dp-cube.vue\"\n      )\n    },\n    dpPicture: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-picture/dp-picture\" */ \"@/components/dp-picture/dp-picture.vue\"\n      )\n    },\n    dpPictures: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-pictures/dp-pictures\" */ \"@/components/dp-pictures/dp-pictures.vue\"\n      )\n    },\n    dpVideo: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-video/dp-video\" */ \"@/components/dp-video/dp-video.vue\"\n      )\n    },\n    dpShop: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-shop/dp-shop\" */ \"@/components/dp-shop/dp-shop.vue\"\n      )\n    },\n    dpProduct: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-product/dp-product\" */ \"@/components/dp-product/dp-product.vue\"\n      )\n    },\n    dpCollage: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-collage/dp-collage\" */ \"@/components/dp-collage/dp-collage.vue\"\n      )\n    },\n    dpKanjia: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-kanjia/dp-kanjia\" */ \"@/components/dp-kanjia/dp-kanjia.vue\"\n      )\n    },\n    dpSeckill: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-seckill/dp-seckill\" */ \"@/components/dp-seckill/dp-seckill.vue\"\n      )\n    },\n    dpScoreshop: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-scoreshop/dp-scoreshop\" */ \"@/components/dp-scoreshop/dp-scoreshop.vue\"\n      )\n    },\n    dpCoupon: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-coupon/dp-coupon\" */ \"@/components/dp-coupon/dp-coupon.vue\"\n      )\n    },\n    dpArticle: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-article/dp-article\" */ \"@/components/dp-article/dp-article.vue\"\n      )\n    },\n    dpBusiness: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-business/dp-business\" */ \"@/components/dp-business/dp-business.vue\"\n      )\n    },\n    dpLiveroom: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-liveroom/dp-liveroom\" */ \"@/components/dp-liveroom/dp-liveroom.vue\"\n      )\n    },\n    dpButton: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-button/dp-button\" */ \"@/components/dp-button/dp-button.vue\"\n      )\n    },\n    dpHotspot: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-hotspot/dp-hotspot\" */ \"@/components/dp-hotspot/dp-hotspot.vue\"\n      )\n    },\n    dpCover: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-cover/dp-cover\" */ \"@/components/dp-cover/dp-cover.vue\"\n      )\n    },\n    dpRichtext: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-richtext/dp-richtext\" */ \"@/components/dp-richtext/dp-richtext.vue\"\n      )\n    },\n    dpForm: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-form/dp-form\" */ \"@/components/dp-form/dp-form.vue\"\n      )\n    },\n    dpUserinfo: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-userinfo/dp-userinfo\" */ \"@/components/dp-userinfo/dp-userinfo.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n    wxxieyi: function () {\n      return import(\n        /* webpackChunkName: \"components/wxxieyi/wxxieyi\" */ \"@/components/wxxieyi/wxxieyi.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./richinfo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./richinfo.vue?vue&type=script&lang=js&\"", "<template>\r\n<view>\r\n\t<block v-if=\"isload\">\r\n\t\t<form @submit=\"subform\">\r\n\t\t\t<!-- <view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">昵称<text style=\"color:red\"> *</text></view>\r\n\t\t\t\t\t<view class=\"f2\"><input type=\"text\" name=\"nickname\" :value=\"info.nickname\" placeholder=\"请填写昵称\" placeholder-style=\"color:#888\"></input></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">ID</view>\r\n\t\t\t\t\t<view class=\"f2\">{{info.id}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<view class=\"f1\">昵称</view>\r\n\t\t\t\t\t<view class=\"f2\">{{info.nickname}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-box\">\r\n\t\t\t\t<view class=\"form-item flex-col\">\r\n\t\t\t\t\t<text>详细介绍</text>\r\n\t\t\t\t\t<view class=\"detailop\"><view class=\"btn\" @tap=\"detailAddtxt\">+文本</view><view class=\"btn\" @tap=\"detailAddpic\">+图片</view><view class=\"btn\" @tap=\"detailAddvideo\">+视频</view></view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<block v-for=\"(setData, index) in pagecontent\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"detaildp\">\r\n\t\t\t\t\t\t\t<view class=\"op\"><view class=\"flex1\"></view><view class=\"btn\" @tap=\"detailMoveup\" :data-index=\"index\">上移</view><view class=\"btn\" @tap=\"detailMovedown\" :data-index=\"index\">下移</view><view class=\"btn\" @tap=\"detailMoveEdit\" :data-index=\"index\">编辑</view><view class=\"btn\" @tap=\"detailMovedel\" :data-index=\"index\">删除</view></view>\r\n\t\t\t\t\t\t\t<view class=\"detailbox\">\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='notice'\">\r\n\t\t\t\t\t\t\t\t\t<dp-notice :params=\"setData.params\" :data=\"setData.data\"></dp-notice>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='banner'\">\r\n\t\t\t\t\t\t\t\t\t<dp-banner :params=\"setData.params\" :data=\"setData.data\"></dp-banner> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='search'\">\r\n\t\t\t\t\t\t\t\t\t<dp-search :params=\"setData.params\" :data=\"setData.data\"></dp-search>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='text'\">\r\n\t\t\t\t\t\t\t\t\t<dp-text :params=\"setData.params\" :data=\"setData.data\" :selectable=\"true\"></dp-text>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='title'\">\r\n\t\t\t\t\t\t\t\t\t<dp-title :params=\"setData.params\" :data=\"setData.data\"></dp-title>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='dhlist'\">\r\n\t\t\t\t\t\t\t\t\t<dp-dhlist :params=\"setData.params\" :data=\"setData.data\"></dp-dhlist>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='line'\">\r\n\t\t\t\t\t\t\t\t\t<dp-line :params=\"setData.params\" :data=\"setData.data\"></dp-line>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='blank'\">\r\n\t\t\t\t\t\t\t\t\t<dp-blank :params=\"setData.params\" :data=\"setData.data\"></dp-blank>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='menu'\">\r\n\t\t\t\t\t\t\t\t\t<dp-menu :params=\"setData.params\" :data=\"setData.data\"></dp-menu> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='map'\">\r\n\t\t\t\t\t\t\t\t\t<dp-map :params=\"setData.params\" :data=\"setData.data\"></dp-map> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='cube'\">\r\n\t\t\t\t\t\t\t\t\t<dp-cube :params=\"setData.params\" :data=\"setData.data\"></dp-cube> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='picture'\">\r\n\t\t\t\t\t\t\t\t\t<dp-picture :params=\"setData.params\" :data=\"setData.data\" :preview=\"true\"></dp-picture> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='pictures'\"> \r\n\t\t\t\t\t\t\t\t\t<dp-pictures :params=\"setData.params\" :data=\"setData.data\"></dp-pictures> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='video'\">\r\n\t\t\t\t\t\t\t\t\t<dp-video :params=\"setData.params\" :data=\"setData.data\"></dp-video> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='shop'\">\r\n\t\t\t\t\t\t\t\t\t<dp-shop :params=\"setData.params\" :data=\"setData.data\" :shopinfo=\"setData.shopinfo\"></dp-shop> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='product'\">\r\n\t\t\t\t\t\t\t\t\t<dp-product :params=\"setData.params\" :data=\"setData.data\" :menuindex=\"menuindex\"></dp-product> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='collage'\">\r\n\t\t\t\t\t\t\t\t\t<dp-collage :params=\"setData.params\" :data=\"setData.data\"></dp-collage> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='kanjia'\">\r\n\t\t\t\t\t\t\t\t\t<dp-kanjia :params=\"setData.params\" :data=\"setData.data\"></dp-kanjia> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='seckill'\">\r\n\t\t\t\t\t\t\t\t\t<dp-seckill :params=\"setData.params\" :data=\"setData.data\"></dp-seckill> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='scoreshop'\">\r\n\t\t\t\t\t\t\t\t\t<dp-scoreshop :params=\"setData.params\" :data=\"setData.data\"></dp-scoreshop> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='coupon'\">\r\n\t\t\t\t\t\t\t\t\t<dp-coupon :params=\"setData.params\" :data=\"setData.data\"></dp-coupon> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='article'\">\r\n\t\t\t\t\t\t\t\t\t<dp-article :params=\"setData.params\" :data=\"setData.data\"></dp-article> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='business'\">\r\n\t\t\t\t\t\t\t\t\t<dp-business :params=\"setData.params\" :data=\"setData.data\"></dp-business> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='liveroom'\">\r\n\t\t\t\t\t\t\t\t\t<dp-liveroom :params=\"setData.params\" :data=\"setData.data\"></dp-liveroom> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='button'\">\r\n\t\t\t\t\t\t\t\t\t<dp-button :params=\"setData.params\" :data=\"setData.data\"></dp-button> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='hotspot'\">\r\n\t\t\t\t\t\t\t\t\t<dp-hotspot :params=\"setData.params\" :data=\"setData.data\"></dp-hotspot> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='cover'\">\r\n\t\t\t\t\t\t\t\t\t<dp-cover :params=\"setData.params\" :data=\"setData.data\"></dp-cover> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='richtext'\">\r\n\t\t\t\t\t\t\t\t\t<dp-richtext :params=\"setData.params\" :data=\"setData.data\" :content=\"setData.content\"></dp-richtext> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='form'\">\r\n\t\t\t\t\t\t\t\t\t<dp-form :params=\"setData.params\" :data=\"setData.data\" :content=\"setData.content\"></dp-form> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<block v-if=\"setData.temp=='userinfo'\">\r\n\t\t\t\t\t\t\t\t\t<dp-userinfo :params=\"setData.params\" :data=\"setData.data\" :content=\"setData.content\"></dp-userinfo> \r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\r\n\t\t\t<button class=\"savebtn\" :style=\"'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'\" form-type=\"submit\">提交</button>\r\n\t\t\t<view style=\"height:50rpx\"></view>\r\n\t\t</form>\r\n\r\n\t\t<uni-popup id=\"dialogDetailtxt\" ref=\"dialogDetailtxt\" type=\"dialog\">\r\n\t\t\t<view class=\"uni-popup-dialog\">\r\n\t\t\t\t<view class=\"uni-dialog-title\">\r\n\t\t\t\t\t<text class=\"uni-dialog-title-text\">请输入文本内容</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-dialog-content\">\r\n\t\t\t\t\t<textarea :value=\"edit_index != -1 ? pagecontent[edit_index].params.showcontent : ''\" placeholder=\"请输入文本内容\" @input=\"catcheDetailtxt\"></textarea>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-dialog-button-group\">\r\n\t\t\t\t\t<view class=\"uni-dialog-button\" @click=\"dialogDetailtxtClose\">\r\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text\">取消</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-dialog-button uni-border-left\" @click=\"dialogDetailtxtConfirm\">\r\n\t\t\t\t\t\t<text class=\"uni-dialog-button-text uni-button-color\">确定</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-popup-dialog__close\" @click=\"dialogDetailtxtClose\">\r\n\t\t\t\t\t<span class=\"uni-popup-dialog__close-icon \"></span>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</block>\r\n\t<view style=\"display:none\">{{test}}</view>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n\t<wxxieyi></wxxieyi>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\tisload:false,\r\n\t\t\tloading:false,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n      info:{},\r\n\t\t\tpagecontent:[],\r\n\t\t\tedit_index:-1,\r\n\t\t\ttest:'',\r\n    };\r\n  },\r\n\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.getdata();\r\n  },\r\n  methods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tvar id = that.opt.id ? that.opt.id : '';\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.get('ApiAdminMember/richinfo',{mid:id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.info = res.info;\r\n\t\t\t\tthat.pagecontent = res.pagecontent;\r\n\t\t\t\tthat.loaded();\r\n\t\t\t});\r\n\t\t},\r\n    subform: function (e) {\r\n      var that = this;\r\n      var formdata = e.detail.value;\r\n      var id = that.opt.id ? that.opt.id : '';\r\n      app.post('ApiAdminMember/richinfo', {mid:id,info:formdata,pagecontent:that.pagecontent}, function (res) {\r\n        if (res.status == 0) {\r\n          app.error(res.msg);\r\n        } else {\r\n          app.success(res.msg);\r\n          setTimeout(function () {\r\n            app.goback(true);\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n\t\tdetailAddtxt:function(){\r\n\t\t\tthis.edit_index = -1;\r\n\t\t\tthis.$refs.dialogDetailtxt.open();\r\n\t\t},\r\n\t\tdialogDetailtxtClose:function(){\r\n\t\t\tthis.$refs.dialogDetailtxt.close();\r\n\t\t},\r\n\t\tcatcheDetailtxt:function(e){\r\n\t\t\tconsole.log(e)\r\n\t\t\tthis.catche_detailtxt = e.detail.value;\r\n\t\t},\r\n\t\tdialogDetailtxtConfirm:function(e){\r\n\t\t\tvar detailtxt = this.catche_detailtxt;\r\n\t\t\tconsole.log(detailtxt)\r\n\t\t\tvar Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tif(this.edit_index != -1){\r\n\t\t\t\tpagecontent[this.edit_index].params.showcontent = detailtxt;\r\n\t\t\t\tpagecontent[this.edit_index].params.content = detailtxt;\r\n\t\t\t}else{\r\n\t\t\t\tpagecontent.push({\"id\":Mid,\"temp\":\"text\",\"params\":{\"content\":detailtxt,\"showcontent\":detailtxt,\"bgcolor\":\"#ffffff\",\"fontsize\":\"14\",\"lineheight\":\"20\",\"letter_spacing\":\"0\",\"bgpic\":\"\",\"align\":\"left\",\"color\":\"#000\",\"margin_x\":\"0\",\"margin_y\":\"0\",\"padding_x\":\"5\",\"padding_y\":\"5\",\"quanxian\":{\"all\":true},\"platform\":{\"all\":true}},\"data\":\"\",\"other\":\"\",\"content\":\"\"});\r\n\t\t\t}\r\n\t\t\tthis.pagecontent = pagecontent;\r\n\t\t\tthis.test = Math.random();\r\n\t\t\tthis.$refs.dialogDetailtxt.close();\r\n\t\t},\r\n\t\tdetailAddpic:function(){\r\n\t\t\tthis.edit_index = -1;\r\n\t\t\tthis.detailAddpicConfirm();\r\n\t\t},\r\n\t\tdetailAddpicConfirm:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tvar Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\t\tvar pics = [];\r\n\t\t\t\tfor(var i in urls){\r\n\t\t\t\t\tvar picid = 'p' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\t\t\tpics.push({\"id\":picid,\"imgurl\":urls[i],\"hrefurl\":\"\",\"option\":\"0\"})\r\n\t\t\t\t}\r\n\t\t\t\tvar pagecontent = that.pagecontent;\r\n\t\t\t\tif(that.edit_index != -1){\r\n\t\t\t\t\tpagecontent[that.edit_index].data = pics;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tpagecontent.push({\"id\":Mid,\"temp\":\"picture\",\"params\":{\"bgcolor\":\"#FFFFFF\",\"margin_x\":\"0\",\"margin_y\":\"0\",\"padding_x\":\"0\",\"padding_y\":\"0\",\"quanxian\":{\"all\":true},\"platform\":{\"all\":true}},\"data\":pics,\"other\":\"\",\"content\":\"\"});\r\n\t\t\t\t}\r\n\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t\tthat.test = Math.random();\r\n\t\t\t},9);\r\n\t\t},\r\n\t\tdetailAddvideo:function(){\r\n\t\t\tthis.edit_index = -1;\r\n\t\t\tthis.detailAddvideoConfirm();\r\n\t\t},\r\n\t\tdetailAddvideoConfirm(){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.chooseVideo({\r\n        sourceType: ['album', 'camera'],\r\n        success: function (res) {\r\n          var tempFilePath = res.tempFilePath;\r\n          app.showLoading('上传中');\r\n          uni.uploadFile({\r\n            url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id,\r\n            filePath: tempFilePath,\r\n            name: 'file',\r\n            success: function (res) {\r\n              app.showLoading(false);\r\n              var data = JSON.parse(res.data);\r\n              if (data.status == 1) {\r\n                that.video = data.url;\r\n\t\t\t\t\t\t\t\tvar pagecontent = that.pagecontent;\r\n\t\t\t\t\t\t\t\tvar Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);\r\n\t\t\t\t\t\t\t\tif(that.edit_index != -1){\r\n\t\t\t\t\t\t\t\t\tpagecontent[that.edit_index].params.src = data.url;\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tpagecontent.push({\"id\":Mid,\"temp\":\"video\",\"params\":{\"bgcolor\":\"#FFFFFF\",\"margin_x\":\"0\",\"margin_y\":\"0\",\"padding_x\":\"0\",\"padding_y\":\"0\",\"src\":data.url,\"quanxian\":{\"all\":true},\"platform\":{\"all\":true}},\"data\":\"\",\"other\":\"\",\"content\":\"\"});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tthat.pagecontent = pagecontent;\r\n\t\t\t\t\t\t\t\tthat.test = Math.random();\r\n              } else {\r\n                app.alert(data.msg);\r\n              }\r\n            },\r\n            fail: function (res) {\r\n              app.showLoading(false);\r\n              app.alert(res.errMsg);\r\n            }\r\n          });\r\n        },\r\n        fail: function (res) {\r\n          console.log(res); //alert(res.errMsg);\r\n        }\r\n      });\r\n\t\t},\r\n\t\tdetailMoveup:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tif(index > 0)\r\n\t\t\t\tpagecontent[index] = pagecontent.splice(index-1, 1, pagecontent[index])[0];\r\n\t\t},\r\n\t\tdetailMovedown:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tif(index < pagecontent.length-1)\r\n\t\t\t\tpagecontent[index] = pagecontent.splice(index+1, 1, pagecontent[index])[0];\r\n\t\t},\r\n\t\tdetailMovedel:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tpagecontent.splice(index,1);\r\n\t\t},\r\n\t\tdetailMoveEdit:function(e){\r\n\t\t\tvar index = e.currentTarget.dataset.index;\r\n\t\t\tvar pagecontent = this.pagecontent;\r\n\t\t\tconsole.log(pagecontent[index]);\r\n\t\t\tthis.edit_index = index;\r\n\t\t\tconsole.log(this.edit_index)\r\n\t\t\tif(pagecontent[index].temp == \"picture\"){\r\n\t\t\t\tthis.detailAddpicConfirm();\r\n\t\t\t}else if(pagecontent[index].temp == \"video\"){\r\n\t\t\t\tthis.detailAddvideoConfirm();\r\n\t\t\t}else if(pagecontent[index].temp == \"text\"){\r\n\t\t\t\tthis.$refs.dialogDetailtxt.open();\r\n\t\t\t}else{\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t},\r\n\t\tuploadimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar pernum = parseInt(e.currentTarget.dataset.pernum);\r\n\t\t\tif(!pernum) pernum = 1;\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tvar pics = that[field]\r\n\t\t\tif(!pics) pics = [];\r\n\t\t\tapp.chooseImage(function(urls){\r\n\t\t\t\tfor(var i=0;i<urls.length;i++){\r\n\t\t\t\t\tpics.push(urls[i]);\r\n\t\t\t\t}\r\n\t\t\t\tif(field == 'pic') that.pic = pics;\r\n\t\t\t\tif(field == 'pics') that.pics = pics;\r\n\t\t\t},pernum);\r\n\t\t},\r\n\t\tremoveimg:function(e){\r\n\t\t\tvar that = this;\r\n\t\t\tvar index= e.currentTarget.dataset.index\r\n\t\t\tvar field= e.currentTarget.dataset.field\r\n\t\t\tif(field == 'pic'){\r\n\t\t\t\tvar pics = that.pic\r\n\t\t\t\tpics.splice(index,1);\r\n\t\t\t\tthat.pic = pics;\r\n\t\t\t}else if(field == 'pics'){\r\n\t\t\t\tvar pics = that.pics\r\n\t\t\t\tpics.splice(index,1);\r\n\t\t\t\tthat.pics = pics;\r\n\t\t\t}\r\n\t\t},\r\n  }\r\n};\r\n</script>\r\n<style>\r\nradio{transform: scale(0.6);}\r\ncheckbox{transform: scale(0.6);}\r\n.form-box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}\r\n.form-item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }\r\n.form-item .f1{color:#222;width:200rpx;flex-shrink:0}\r\n.form-item .f2{display:flex;align-items:center}\r\n.form-box .form-item:last-child{ border:none}\r\n.form-box .flex-col{padding-bottom:20rpx}\r\n.form-item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}\r\n.form-item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}\r\n.form-item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }\r\n.form-item .upload_pic image{ width: 32rpx;height: 32rpx; }\r\n.savebtn{ width: 90%; height:96rpx; line-height: 96rpx; text-align:center;border-radius:48rpx; color: #fff;font-weight:bold;margin: 0 5%; margin-top:60rpx; border: none; }\r\n\r\n.ggtitle{height:60rpx;line-height:60rpx;color:#111;font-weight:bold;font-size:26rpx;display:flex;border-bottom:1px solid #f4f4f4}\r\n.ggtitle .t1{width:200rpx;}\r\n.ggcontent{line-height:60rpx;margin-top:10rpx;color:#111;font-size:26rpx;display:flex}\r\n.ggcontent .t1{width:200rpx;display:flex;align-items:center;flex-shrink:0}\r\n.ggcontent .t1 .edit{width:40rpx;height:40rpx}\r\n.ggcontent .t2{display:flex;flex-wrap:wrap;align-items:center}\r\n.ggcontent .ggname{background:#f55;color:#fff;height:40rpx;line-height:40rpx;padding:0 20rpx;border-radius:8rpx;margin-right:20rpx;margin-bottom:10rpx;font-size:24rpx;position:relative}\r\n.ggcontent .ggname .close{position:absolute;top:-14rpx;right:-14rpx;background:#fff;height:28rpx;width:28rpx;border-radius:14rpx}\r\n.ggcontent .ggnameadd{background:#ccc;font-size:36rpx;color:#fff;height:40rpx;line-height:40rpx;padding:0 20rpx;border-radius:8rpx;margin-right:20rpx;margin-left:10rpx;position:relative}\r\n.ggcontent .ggadd{font-size:26rpx;color:#558}\r\n\r\n.ggbox{line-height:50rpx;}\r\n\r\n\r\n.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}\r\n.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}\r\n.layui-imgbox-img>image{max-width:100%;}\r\n.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}\r\n.uploadbtn{position:relative;height:200rpx;width:200rpx}\r\n\r\n\r\n.clist-item{display:flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 30rpx;}\r\n.radio{flex-shrink:0;width: 32rpx;height: 32rpx;background: #FFFFFF;border: 2rpx solid #BFBFBF;border-radius: 50%;margin-right:30rpx}\r\n.radio .radio-img{width:100%;height:100%;display:block}\r\n\r\n.freightitem{width:100%;height:60rpx;display:flex;align-items:center;margin-left:40rpx}\r\n.freightitem .f1{color:#666;flex:1}\r\n\r\n.detailop{display:flex;line-height:60rpx}\r\n.detailop .btn{border:1px solid #ccc;margin-right:10rpx;padding:0 16rpx;color:#222;border-radius:10rpx}\r\n.detaildp{position:relative;line-height:50rpx}\r\n.detaildp .op{width:100%;display:flex;justify-content:flex-end;font-size:24rpx;height:60rpx;line-height:60rpx;margin-top:10rpx}\r\n.detaildp .op .btn{background:rgba(0,0,0,0.4);margin-right:10rpx;padding:0 10rpx;color:#fff}\r\n.detaildp .detailbox{border:2px dashed #00a0e9}\r\n\r\n.uni-popup-dialog {width: 300px;border-radius: 5px;background-color: #fff;}\r\n.uni-dialog-title {display: flex;flex-direction: row;justify-content: center;padding-top: 15px;padding-bottom: 5px;}\r\n.uni-dialog-title-text {font-size: 16px;font-weight: 500;}\r\n.uni-dialog-content {display: flex;flex-direction: row;justify-content: center;align-items: center;padding: 5px 15px 15px 15px;}\r\n.uni-dialog-content-text {font-size: 14px;color: #6e6e6e;}\r\n.uni-dialog-button-group {display: flex;flex-direction: row;border-top-color: #f5f5f5;border-top-style: solid;border-top-width: 1px;}\r\n.uni-dialog-button {display: flex;flex: 1;flex-direction: row;justify-content: center;align-items: center;height: 45px;}\r\n.uni-border-left {border-left-color: #f0f0f0;border-left-style: solid;border-left-width: 1px;}\r\n.uni-dialog-button-text {font-size: 14px;}\r\n.uni-button-color {color: #007aff;}\r\n\r\n.stockwarning{ position: absolute; right:0rpx; bottom:0;display:flex; align-items:center;font-size:24rpx;color:red;   }\r\n.stockwarning image{  margin-right:10rpx}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./richinfo.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./richinfo.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213046224\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}