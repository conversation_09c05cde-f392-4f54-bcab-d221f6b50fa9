{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/yuyue/selectworker.vue?2ca0", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/yuyue/selectworker.vue?2837", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/yuyue/selectworker.vue?6c7a", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/yuyue/selectworker.vue?f405", "uni-app:///admin/yuyue/selectworker.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/yuyue/selectworker.vue?110e", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/yuyue/selectworker.vue?cd71"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "menuindex", "datalist", "type", "keyword", "nodata", "sindex", "linkman", "fwname", "workerid", "pagenum", "nomore", "pre_url", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "that", "app", "id", "changeradio", "confirm", "orderid", "worker_id", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3EA;AAAA;AAAA;AAAA;AAA00B,CAAgB,0yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwC91B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;QACA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;QAAAC;QAAAV;MAAA;QACAQ;QACA;QAEA;UACAA;UACA;YACAA;UACA;UACAA;QACA;UACA;YACAA;UACA;YACA;YACA;YACAA;UACA;QACA;MAEA;IACA;IACA;IACAG;MACA;MACA;MACA;MACAH;MACAA;IACA;IACAI;MACA;MACA;QACAH;QAAA;MACA;MACAA;MACA;MACAA;QAAAhB;QAAAoB;QAAAC;MAAA;QACA;UACAN;UACAC;UACAM;YACAN;UACA;QACA;UACAA;QACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC1IA;AAAA;AAAA;AAAA;AAAurC,CAAgB,umCAAG,EAAC,C;;;;;;;;;;;ACA3sC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/yuyue/selectworker.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/yuyue/selectworker.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./selectworker.vue?vue&type=template&id=8b2fcf2c&\"\nvar renderjs\nimport script from \"./selectworker.vue?vue&type=script&lang=js&\"\nexport * from \"./selectworker.vue?vue&type=script&lang=js&\"\nimport style0 from \"./selectworker.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/yuyue/selectworker.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./selectworker.vue?vue&type=template&id=8b2fcf2c&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.datalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.sindex == index ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  var m1 = _vm.isload && _vm.fwname ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload && _vm.fwname ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./selectworker.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./selectworker.vue?vue&type=script&lang=js&\"", "<template>\r\n<view class=\"container\">\r\n\t<block v-if=\"isload\">\r\n\t\t<view v-for=\"(item, index) in datalist\" :key=\"index\" class=\"content flex\" @tap.stop=\"changeradio\" :data-id=\"item.id\"  :data-index=\"index\" :data-yystatus='item.yystatus'>\r\n\t\t\t\r\n\t\t\t<view class=\"btitle\">\r\n\t\t\t\t<view class=\"radio\" :style=\"sindex==index ? 'background:'+t('color1')+';border:0' : ''\"><image class=\"radio-img\" :src=\"pre_url+'/static/img/checkd.png'\"/></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f1\">\r\n\t\t\t\t<view class=\"headimg\"><image :src=\"item.headimg\" /></view>\r\n\t\t\t\t<view class=\"text1\">\t\r\n\t\t\t\t\t<text class=\"t1\">{{item.realname}} </text>\r\n\t\t\t\t\t<text class=\"t2\" v-if=\"item.typename\">{{item.typename}}</text>\r\n\t\t\t\t\t<view class=\"tags\">\r\n\t\t\t\t\t\t<text class=\"t3\"  v-if=\"item.citys\">{{item.citys}}</text> \r\n\t\t\t\t\t\t<text class=\"t3\" v-if=\"item.age\"> {{item.age}}岁</text> \r\n\t\t\t\t\t\t<text class=\"t3\">评分{{item.comment_score}}</text> \r\n\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f3\">\r\n\t\t\t\t\t\t<text class=\"juli\">距离<text class=\"t4\">{{item.juli}}</text>{{item.juli_unit}}</text>\r\n\t\t\t\t\t\t<image :src=\"pre_url+'/static/img/tel2.png'\"  class=\"tel\" @tap.stop=\"goto\" :data-url=\"'tel::'+item.tel\"/>\t</view>\t\r\n\t\t\t\t</view>\r\n\t\t\r\n\t\t\t</view>\r\n\t\t\t\t\r\n\t\t</view>\r\n\t\t<view class=\"bottom\"  v-if=\"fwname\"><view class=\"btn\" @tap=\"confirm\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\" >确定指派<text v-if=\"fwname\">{{fwname}}</text></view></view>\r\n\t\t<nodata v-if=\"nodata\"></nodata>\r\n\t\t<nomore v-if=\"nomore\"></nomore>\r\n\t\t<view style=\"height:140rpx\"></view>\r\n\t</block>\r\n\t\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<dp-tabbar :opt=\"opt\" @getmenuindex=\"getmenuindex\"></dp-tabbar>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n      isload: false,\r\n\t\t\tmenuindex:-1,\r\n      datalist: [],\r\n      type: \"\",\r\n\t\t\tkeyword:'',\r\n\t\t\tnodata:false,\r\n\t\t\tsindex:'-1',\r\n\t\t\tlinkman:'',\r\n\t\t\tfwname:'',\r\n\t\t\tworkerid:0,\r\n\t\t\tpagenum: 1,\r\n\t\t\tnomore: false,\r\n\t\t\tpre_url:app.globalData.pre_url,\r\n    }\r\n  },\r\n  onLoad: function (opt) {\r\n\t\tthis.opt = app.getopts(opt);\r\n\t\tthis.id = opt.id\r\n\t\tthis.type = opt.type\r\n\t\tthis.getdata();\r\n  },\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tonReachBottom: function () {\r\n\t  if (!this.nodata && !this.nomore) {\r\n\t    this.pagenum = this.pagenum + 1;\r\n\t    this.getdata(true);\r\n\t  }\r\n\t},\r\n  methods: {\r\n\t\tgetdata:function(loadmore){\r\n\t\t\tvar that = this;\r\n\t\t\tif(!loadmore){\r\n\t\t\t\tthis.pagenum = 1;\r\n\t\t\t\tthis.datalist = [];\r\n\t\t\t}\r\n\t\t\tvar pagenum = that.pagenum;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.nodata = false;\r\n\t\t\tthat.nomore = false;\r\n\t\t\tapp.get('ApiAdminOrder/selectworker', {id:that.id,pagenum: pagenum}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tvar data = res.data;\r\n\t\t\t\r\n\t\t\t\tif (pagenum == 1) {\r\n\t\t\t\t\tthat.datalist = data;\r\n\t\t\t\t  if (data.length == 0) {\r\n\t\t\t\t    that.nodata = true;\r\n\t\t\t\t  }\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t}else{\r\n\t\t\t\t  if (data.length == 0) {\r\n\t\t\t\t    that.nomore = true;\r\n\t\t\t\t  } else {\r\n\t\t\t\t    var datalist = that.datalist;\r\n\t\t\t\t    var newdata = datalist.concat(data);\r\n\t\t\t\t    that.datalist = newdata;\r\n\t\t\t\t  }\r\n\t\t\t\t}\r\n\r\n\t\t\t});\r\n\t\t},\r\n    //选择人员\r\n\t\tchangeradio: function (e) {\r\n\t\t  var that = this;\r\n\t\t  var index = e.currentTarget.dataset.index;\r\n\t\t\tthis.sindex = index\r\n\t\t\tthat.workerid = that.datalist[index].id\r\n\t\t\tthat.fwname = that.datalist[index].realname\r\n\t\t},\r\n\t\tconfirm:function(e){\r\n\t\t\tvar that=this\r\n\t\t\tif(!that.id || !that.fwname ) {\r\n\t\t\t\t app.error('请选择服务人员');return;\r\n\t\t\t}\r\n\t\t\tapp.showLoading('提交中');\r\n\t\t\tvar type = this.type;\r\n\t\t\tapp.post('ApiAdminOrder/yuyuepeisong', { type:'yuyue_order',orderid: that.id,worker_id:that.workerid,type:type}, function (res) {\r\n\t\t\t\tif(res.status==1){\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tapp.success(res.msg);\r\n\t\t\t\t\tsetTimeout(function () {\r\n\t\t\t\t\t\tapp.goback()\r\n\t\t\t\t\t}, 1000)\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.error(res.msg);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t})\r\n\t\t}\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.content{width:94%;margin:20rpx 3%;background:#fff;border-radius:5px;padding:20rpx;}\r\n.content .f1{display:flex; width: 100%;}\r\n.content .f1 image{ width:120rpx; height: 120rpx;border-radius: 10rpx; margin: 0 20rpx;}\r\n.content .f1 .text1{ width: 100%;}\r\n.content .f1 .t1{color:#2B2B2B;font-weight:bold;font-size:30rpx;margin-left:10rpx;}\r\n.content .f1 .t2{color:#999999;font-size:28rpx; background: #ECF5F3;color:#6DBCC9;  margin-left: 10rpx; padding:3rpx 20rpx; font-size: 20rpx; border-radius: 18rpx;}\r\n.content .btitle{ display: flex; align-items: center;}\r\n.content .radio{flex-shrink:0;width: 36rpx;height: 36rpx;background: #FFFFFF;border: 3rpx solid #BFBFBF;border-radius: 50%;}\r\n.content .radio .radio-img{width:100%;height:100%}\r\n.content .tags { margin: 10rpx 0;}\r\n.content .tags .t3{ font-size: 20rpx;; background: #ECF5F3;color:#6DBCC9; margin:10rpx; padding: 5rpx 10rpx; }\r\n.content .juli{ margin-top: 10rpx; font-size: 20rpx; margin-left: 10rpx;}\r\n.content .juli .t4{ color: red;}\r\n.content .f3{ display: flex; justify-content: space-between; width: 100%;}\r\n.content .f3 image{ width:40rpx; height: 40rpx;}\r\n\r\n.bottom{ position: fixed; bottom: 0; background: #fff; width: 100%;}\r\n.btn{ border:0;height:80rpx;line-height:80rpx;margin:20rpx auto;border-radius:6rpx;color:#fff; width: 90%; text-align: center; }\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./selectworker.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./selectworker.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213040918\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}