{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/health/result.vue?8a2f", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/health/result.vue?38ef", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/health/result.vue?74f7", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/health/result.vue?99f2", "uni-app:///admin/health/result.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/health/result.vue?526c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/admin/health/result.vue?d175"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "detail", "pagecontent", "id", "menuindex", "health", "custom", "themeColor", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qJAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpDA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkFx1B;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EAEAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;QAAAV;MAAA;QACAS;QACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;YACAA;UACA;UACAA;QACA;UACAC;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACrIA;AAAA;AAAA;AAAA;AAAirC,CAAgB,imCAAG,EAAC,C;;;;;;;;;;;ACArsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "admin/health/result.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './admin/health/result.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./result.vue?vue&type=template&id=ec511b5c&\"\nvar renderjs\nimport script from \"./result.vue?vue&type=script&lang=js&\"\nexport * from \"./result.vue?vue&type=script&lang=js&\"\nimport style0 from \"./result.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"admin/health/result.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./result.vue?vue&type=template&id=ec511b5c&\"", "var components\ntry {\n  components = {\n    dp: function () {\n      return import(\n        /* webpackChunkName: \"components/dp/dp\" */ \"@/components/dp/dp.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.detail.child_result.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./result.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./result.vue?vue&type=script&lang=js&\"", "<template>\r\n<view >\r\n\t<block v-if=\"isload\">\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"enter\">\r\n\t\t\t\t<view class=\"gobtn\" @tap=\"goto\" :data-url=\"'recordlog?rid='+detail.id\" :style=\"{background:themeColor,color:'#FFF'}\">评测详情</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"box\">\r\n\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t<view class=\"lable\">评测量表：</view>\r\n\t\t\t\t\t<view class=\"value\">{{detail.ha_name}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t<view class=\"lable\">评测时间：</view>\r\n\t\t\t\t\t<view class=\"value\">{{detail.createtime}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t<view class=\"lable\">联系方式：</view>\r\n\t\t\t\t\t<view class=\"value\">{{detail.tel}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t<view class=\"lable\">姓名：</view>\r\n\t\t\t\t\t<view class=\"value\">{{detail.name}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t<view class=\"lable\">年龄：</view>\r\n\t\t\t\t\t<view class=\"value\">{{detail.age}}岁</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t<view class=\"lable\">性别：</view>\r\n\t\t\t\t\t<view class=\"value\">{{detail.sex==2?'女':'男'}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"row\" v-if=\"detail.bname\">\r\n\t\t\t\t\t<view class=\"lable\">门店：</view>\r\n\t\t\t\t\t<view class=\"value\">{{detail.bname}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t<view class=\"lable\">家庭地址：</view>\r\n\t\t\t\t\t<view class=\"value\">{{detail.address}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"box\">\r\n\t\t\t\t<block v-if=\"health.type!=3\">\r\n\t\t\t\t\t<view class=\"tag\"  :style=\"{color:themeColor}\">{{detail.score_tag}}</view>\r\n\t\t\t\t\t<view class=\"score\" :style=\"{color:themeColor}\">{{detail.score}}分</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<view class=\"child-result\" v-if=\"detail.child_result.length>0\">\r\n\t\t\t\t\t<view class=\"child-item\" :style=\"'border:1rpx solid '+themeColor+''\" v-for=\"(itemC,indexC) in detail.child_result\">\r\n\t\t\t\t\t\t<view class=\"child-title\">{{itemC.name}}</view>\r\n\t\t\t\t\t\t<view class=\"child-score txt1\">\r\n\t\t\t\t\t\t\t<view class=\"score\" :style=\"{color:themeColor}\">{{itemC.score}}分</view>\r\n\t\t\t\t\t\t\t<view>{{itemC.score_tag}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"txt1\"><rich-text :nodes=\"itemC.score_desc\"></rich-text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"desc\" v-if=\"health.type!=3\">\r\n\t\t\t\t\t<view class=\"title\">评测概述</view>\r\n\t\t\t\t\t<view class=\"content\"><rich-text :nodes=\"detail.score_desc\"></rich-text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t<view class=\"title\">评测说明</view>\r\n\t\t\t\t\t<view class=\"content\"><rich-text :nodes=\"detail.desc\"></rich-text></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"box\">\r\n\t\t\t\t<dp :pagecontent=\"pagecontent\" :menuindex=\"menuindex\" @getdata=\"getdata\"></dp>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view style=\"height: 90rpx;\"></view>\r\n\t\t<view class=\"bottom\">\r\n\t\t\t\t<button  class=\"btn\" @tap=\"goback\" :style=\"{background:themeColor,color:'#FFF'}\">\r\n\t\t\t\t\t返 回\r\n\t\t\t\t</button>\r\n\t\t</view>\r\n\t</block>\r\n\t<loading v-if=\"loading\"></loading>\r\n\t<popmsg ref=\"popmsg\"></popmsg>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp();\r\nvar interval = null;\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\topt:{},\r\n\t\t\tloading:false,\r\n\t\t\tisload: false,\r\n\t\t\tdetail:[],\r\n\t\t\tpagecontent:[],\r\n\t\t\tid:0,\r\n\t\t\tmenuindex:-1,\r\n\t\t\thealth:{},\r\n\t\t\tcustom:{},\r\n\t\t\tthemeColor:''\r\n\t\t};\r\n\t},\r\n\t  onLoad: function (opt) {\r\n\t\t\tthis.opt = app.getopts(opt);\r\n\t\t\tthis.id = this.opt.id || 0;\r\n\t\t\tthis.getdata();\r\n\t  },\r\n\t\t\r\n\tonPullDownRefresh: function () {\r\n\t\tthis.getdata();\r\n\t},\r\n\tmethods: {\r\n\t\tgetdata:function(){\r\n\t\t\tvar that = this;\r\n\t\t\tthat.loading = true;\r\n\t\t\tapp.post('ApiAdminHealth/questionResult', {id:that.id}, function (res) {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tif (res.status == 1) {\r\n\t\t\t\t  that.detail = res.detail\r\n\t\t\t\t\tthat.pagecontent = res.pagecontent\r\n\t\t\t\t\tthat.health = res.health\r\n\t\t\t\t\tthat.health = res.health\r\n\t\t\t\t\tthat.custom = res.custom\r\n\t\t\t\t\tif(that.custom.PSQI){\r\n\t\t\t\t\t\tthat.themeColor = '#229989'\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.themeColor = that.themeColor;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.loaded();\r\n\t\t\t\t}else{\r\n\t\t\t\t\tapp.alert(res.msg);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n<style>\r\n.container{padding: 30rpx;}\r\n.box{margin-bottom: 30rpx;border: 1rpx solid #F6F6F6;border-radius: 20rpx;background: #FFFFFF;padding: 30rpx; /* font-size: 12px; */ color: #666;}\r\n.row{display: flex;align-items: center;padding-top: 20rpx;}\r\n.row .lable{width: 180rpx;flex-shrink: 0;text-align: right;padding-right: 20rpx;color: #666;}\r\n.tag{font-size: 40rpx;font-weight: bold;text-align: center;}\r\n.score{font-size: 32rpx;text-align: center;padding: 10rpx 0;}\r\n.desc{font-size: 28rpx;margin-top: 20rpx;color: #666;}\r\n.desc .title{line-height: 50rpx;/* font-size: 30rpx;*/font-weight: bold; color: #222222; }\r\n.desc .content{line-height: 40rpx;/* color: #909090; font-size: 26rpx; */}\r\n.bottom{display: flex;justify-content: center;align-items: center;background: #f6f6f6;padding: 20rpx;position: fixed;bottom: 0;width: 100%;}\r\n\r\n.bottom .btn{height: 80rpx;line-height: 80rpx;padding: 0 20rpx;border-radius: 16rpx;width: 90%;background: #bbb;color: #FFFFFF;}\r\n.enter{display: flex;justify-content: flex-end;font-size: 24rpx;}\r\n.gobtn{width: 150rpx;height: 50rpx;line-height: 50rpx;text-align: center;right: 0;z-index: 999;border-radius: 40px 0 0 40px;margin-right: -30rpx;margin-bottom: 20rpx;}\r\n\r\n.child-score{display: flex;justify-content: flex-start;align-items: center;}\r\n.child-result{margin: 20rpx 0;}\r\n.child-title{/* font-size: 30rpx;font-weight: bold; */}\r\n.child-item{border: 1rpx solid #EEEEEE;padding: 20rpx;border-radius: 10rpx;margin-bottom: 20rpx;}\r\n.child-score .score{margin-right: 10rpx;font-size: 28rpx;}\r\n.child-result .txt1{font-size: 24rpx;}\r\n.content img{object-fit: contain;}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./result.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./result.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754213040728\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}