require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["admin/scoreproduct/edit"],{

/***/ 2695:
/*!***********************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/main.js?{"page":"admin%2Fscoreproduct%2Fedit"} ***!
  \***********************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _edit = _interopRequireDefault(__webpack_require__(/*! ./admin/scoreproduct/edit.vue */ 2696));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_edit.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 2696:
/*!****************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/admin/scoreproduct/edit.vue ***!
  \****************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _edit_vue_vue_type_template_id_99dcda44___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./edit.vue?vue&type=template&id=99dcda44& */ 2697);
/* harmony import */ var _edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./edit.vue?vue&type=script&lang=js& */ 2699);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _edit_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./edit.vue?vue&type=style&index=0&lang=css& */ 2701);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 36);

var renderjs





/* normalize component */

var component = Object(_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _edit_vue_vue_type_template_id_99dcda44___WEBPACK_IMPORTED_MODULE_0__["render"],
  _edit_vue_vue_type_template_id_99dcda44___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _edit_vue_vue_type_template_id_99dcda44___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "admin/scoreproduct/edit.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 2697:
/*!***********************************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/admin/scoreproduct/edit.vue?vue&type=template&id=99dcda44& ***!
  \***********************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_99dcda44___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=template&id=99dcda44& */ 2698);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_99dcda44___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_99dcda44___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_99dcda44___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_99dcda44___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 2698:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!/Users/<USER>/Downloads/tcphp/uniapp/admin/scoreproduct/edit.vue?vue&type=template&id=99dcda44& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    dpNotice: function () {
      return Promise.all(/*! import() | components/dp-notice/dp-notice */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/dp-notice/dp-notice")]).then(__webpack_require__.bind(null, /*! @/components/dp-notice/dp-notice.vue */ 7373))
    },
    dpBanner: function () {
      return __webpack_require__.e(/*! import() | components/dp-banner/dp-banner */ "components/dp-banner/dp-banner").then(__webpack_require__.bind(null, /*! @/components/dp-banner/dp-banner.vue */ 7380))
    },
    dpSearch: function () {
      return __webpack_require__.e(/*! import() | components/dp-search/dp-search */ "components/dp-search/dp-search").then(__webpack_require__.bind(null, /*! @/components/dp-search/dp-search.vue */ 7387))
    },
    dpText: function () {
      return __webpack_require__.e(/*! import() | components/dp-text/dp-text */ "components/dp-text/dp-text").then(__webpack_require__.bind(null, /*! @/components/dp-text/dp-text.vue */ 7299))
    },
    dpTitle: function () {
      return __webpack_require__.e(/*! import() | components/dp-title/dp-title */ "components/dp-title/dp-title").then(__webpack_require__.bind(null, /*! @/components/dp-title/dp-title.vue */ 7394))
    },
    dpDhlist: function () {
      return __webpack_require__.e(/*! import() | components/dp-dhlist/dp-dhlist */ "components/dp-dhlist/dp-dhlist").then(__webpack_require__.bind(null, /*! @/components/dp-dhlist/dp-dhlist.vue */ 7401))
    },
    dpLine: function () {
      return __webpack_require__.e(/*! import() | components/dp-line/dp-line */ "components/dp-line/dp-line").then(__webpack_require__.bind(null, /*! @/components/dp-line/dp-line.vue */ 7408))
    },
    dpBlank: function () {
      return __webpack_require__.e(/*! import() | components/dp-blank/dp-blank */ "components/dp-blank/dp-blank").then(__webpack_require__.bind(null, /*! @/components/dp-blank/dp-blank.vue */ 7415))
    },
    dpMenu: function () {
      return __webpack_require__.e(/*! import() | components/dp-menu/dp-menu */ "components/dp-menu/dp-menu").then(__webpack_require__.bind(null, /*! @/components/dp-menu/dp-menu.vue */ 7420))
    },
    dpMap: function () {
      return __webpack_require__.e(/*! import() | components/dp-map/dp-map */ "components/dp-map/dp-map").then(__webpack_require__.bind(null, /*! @/components/dp-map/dp-map.vue */ 7427))
    },
    dpCube: function () {
      return __webpack_require__.e(/*! import() | components/dp-cube/dp-cube */ "components/dp-cube/dp-cube").then(__webpack_require__.bind(null, /*! @/components/dp-cube/dp-cube.vue */ 7434))
    },
    dpPicture: function () {
      return __webpack_require__.e(/*! import() | components/dp-picture/dp-picture */ "components/dp-picture/dp-picture").then(__webpack_require__.bind(null, /*! @/components/dp-picture/dp-picture.vue */ 7306))
    },
    dpPictures: function () {
      return __webpack_require__.e(/*! import() | components/dp-pictures/dp-pictures */ "components/dp-pictures/dp-pictures").then(__webpack_require__.bind(null, /*! @/components/dp-pictures/dp-pictures.vue */ 7441))
    },
    dpVideo: function () {
      return __webpack_require__.e(/*! import() | components/dp-video/dp-video */ "components/dp-video/dp-video").then(__webpack_require__.bind(null, /*! @/components/dp-video/dp-video.vue */ 7448))
    },
    dpShop: function () {
      return __webpack_require__.e(/*! import() | components/dp-shop/dp-shop */ "components/dp-shop/dp-shop").then(__webpack_require__.bind(null, /*! @/components/dp-shop/dp-shop.vue */ 7455))
    },
    dpProduct: function () {
      return __webpack_require__.e(/*! import() | components/dp-product/dp-product */ "components/dp-product/dp-product").then(__webpack_require__.bind(null, /*! @/components/dp-product/dp-product.vue */ 7462))
    },
    dpCollage: function () {
      return __webpack_require__.e(/*! import() | components/dp-collage/dp-collage */ "components/dp-collage/dp-collage").then(__webpack_require__.bind(null, /*! @/components/dp-collage/dp-collage.vue */ 7469))
    },
    dpKanjia: function () {
      return __webpack_require__.e(/*! import() | components/dp-kanjia/dp-kanjia */ "components/dp-kanjia/dp-kanjia").then(__webpack_require__.bind(null, /*! @/components/dp-kanjia/dp-kanjia.vue */ 7476))
    },
    dpSeckill: function () {
      return __webpack_require__.e(/*! import() | components/dp-seckill/dp-seckill */ "components/dp-seckill/dp-seckill").then(__webpack_require__.bind(null, /*! @/components/dp-seckill/dp-seckill.vue */ 7483))
    },
    dpScoreshop: function () {
      return __webpack_require__.e(/*! import() | components/dp-scoreshop/dp-scoreshop */ "components/dp-scoreshop/dp-scoreshop").then(__webpack_require__.bind(null, /*! @/components/dp-scoreshop/dp-scoreshop.vue */ 7490))
    },
    dpCoupon: function () {
      return __webpack_require__.e(/*! import() | components/dp-coupon/dp-coupon */ "components/dp-coupon/dp-coupon").then(__webpack_require__.bind(null, /*! @/components/dp-coupon/dp-coupon.vue */ 7497))
    },
    dpArticle: function () {
      return __webpack_require__.e(/*! import() | components/dp-article/dp-article */ "components/dp-article/dp-article").then(__webpack_require__.bind(null, /*! @/components/dp-article/dp-article.vue */ 7504))
    },
    dpBusiness: function () {
      return __webpack_require__.e(/*! import() | components/dp-business/dp-business */ "components/dp-business/dp-business").then(__webpack_require__.bind(null, /*! @/components/dp-business/dp-business.vue */ 7511))
    },
    dpLiveroom: function () {
      return __webpack_require__.e(/*! import() | components/dp-liveroom/dp-liveroom */ "components/dp-liveroom/dp-liveroom").then(__webpack_require__.bind(null, /*! @/components/dp-liveroom/dp-liveroom.vue */ 7518))
    },
    dpButton: function () {
      return __webpack_require__.e(/*! import() | components/dp-button/dp-button */ "components/dp-button/dp-button").then(__webpack_require__.bind(null, /*! @/components/dp-button/dp-button.vue */ 7525))
    },
    dpHotspot: function () {
      return __webpack_require__.e(/*! import() | components/dp-hotspot/dp-hotspot */ "components/dp-hotspot/dp-hotspot").then(__webpack_require__.bind(null, /*! @/components/dp-hotspot/dp-hotspot.vue */ 7532))
    },
    dpCover: function () {
      return __webpack_require__.e(/*! import() | components/dp-cover/dp-cover */ "components/dp-cover/dp-cover").then(__webpack_require__.bind(null, /*! @/components/dp-cover/dp-cover.vue */ 7539))
    },
    dpRichtext: function () {
      return __webpack_require__.e(/*! import() | components/dp-richtext/dp-richtext */ "components/dp-richtext/dp-richtext").then(__webpack_require__.bind(null, /*! @/components/dp-richtext/dp-richtext.vue */ 7313))
    },
    dpForm: function () {
      return __webpack_require__.e(/*! import() | components/dp-form/dp-form */ "components/dp-form/dp-form").then(__webpack_require__.bind(null, /*! @/components/dp-form/dp-form.vue */ 7546))
    },
    dpUserinfo: function () {
      return Promise.all(/*! import() | components/dp-userinfo/dp-userinfo */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/dp-userinfo/dp-userinfo")]).then(__webpack_require__.bind(null, /*! @/components/dp-userinfo/dp-userinfo.vue */ 7553))
    },
    uniPopup: function () {
      return Promise.all(/*! import() | components/uni-popup/uni-popup */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/uni-popup/uni-popup")]).then(__webpack_require__.bind(null, /*! @/components/uni-popup/uni-popup.vue */ 7096))
    },
    uniPopupDialog: function () {
      return __webpack_require__.e(/*! import() | components/uni-popup-dialog/uni-popup-dialog */ "components/uni-popup-dialog/uni-popup-dialog").then(__webpack_require__.bind(null, /*! @/components/uni-popup-dialog/uni-popup-dialog.vue */ 7240))
    },
    loading: function () {
      return __webpack_require__.e(/*! import() | components/loading/loading */ "components/loading/loading").then(__webpack_require__.bind(null, /*! @/components/loading/loading.vue */ 7131))
    },
    popmsg: function () {
      return __webpack_require__.e(/*! import() | components/popmsg/popmsg */ "components/popmsg/popmsg").then(__webpack_require__.bind(null, /*! @/components/popmsg/popmsg.vue */ 7124))
    },
    wxxieyi: function () {
      return __webpack_require__.e(/*! import() | components/wxxieyi/wxxieyi */ "components/wxxieyi/wxxieyi").then(__webpack_require__.bind(null, /*! @/components/wxxieyi/wxxieyi.vue */ 7138))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.isload ? _vm.cids.length : null
  var g1 = _vm.isload ? _vm.pic.length : null
  var g2 = _vm.isload ? _vm.pic.join(",") : null
  var g3 = _vm.isload ? _vm.pics.length : null
  var g4 = _vm.isload ? _vm.pics.join(",") : null
  var l0 = _vm.isload
    ? _vm.__map(_vm.gglist, function (item, index) {
        var $orig = _vm.__get_orig(item)
        var m0 = _vm.t("积分")
        var m1 = _vm.t("积分")
        return {
          $orig: $orig,
          m0: m0,
          m1: m1,
        }
      })
    : null
  var l1 =
    _vm.isload && _vm.freightindex == 1
      ? _vm.__map(_vm.freightList, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m2 = _vm.inArray(item.id, _vm.freightIds)
          var m3 = m2 ? _vm.t("color1") : null
          return {
            $orig: $orig,
            m2: m2,
            m3: m3,
          }
        })
      : null
  var m4 = _vm.isload ? _vm.t("color1") : null
  var m5 = _vm.isload ? _vm.t("color1rgb") : null
  var l4 =
    _vm.isload && _vm.clistshow
      ? _vm.__map(_vm.clist, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m6 = _vm.inArray(item.id, _vm.cids)
          var m7 = m6 ? _vm.t("color1") : null
          var g5 = item.child.length
          var l3 = _vm.__map(item.child, function (item2, index2) {
            var $orig = _vm.__get_orig(item2)
            var m8 = _vm.inArray(item2.id, _vm.cids)
            var m9 = m8 ? _vm.t("color1") : null
            var g6 = item2.child.length
            var l2 = _vm.__map(item2.child, function (item3, index3) {
              var $orig = _vm.__get_orig(item3)
              var m10 = _vm.inArray(item3.id, _vm.cids)
              var m11 = m10 ? _vm.t("color1") : null
              return {
                $orig: $orig,
                m10: m10,
                m11: m11,
              }
            })
            return {
              $orig: $orig,
              m8: m8,
              m9: m9,
              g6: g6,
              l2: l2,
            }
          })
          return {
            $orig: $orig,
            m6: m6,
            m7: m7,
            g5: g5,
            l3: l3,
          }
        })
      : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        g1: g1,
        g2: g2,
        g3: g3,
        g4: g4,
        l0: l0,
        l1: l1,
        m4: m4,
        m5: m5,
        l4: l4,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 2699:
/*!*****************************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/admin/scoreproduct/edit.vue?vue&type=script&lang=js& ***!
  \*****************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js& */ 2700);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 2700:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!/Users/<USER>/Downloads/tcphp/uniapp/admin/scoreproduct/edit.vue?vue&type=script&lang=js& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

var app = getApp();
var _default = {
  data: function data() {
    return {
      isload: false,
      loading: false,
      pre_url: app.globalData.pre_url,
      info: {},
      pagecontent: [],
      aglevellist: [],
      levellist: [],
      clist: [],
      cateArr: [],
      freighttypeArr: ['全部模板', '指定模板', '自动发货', '在线卡密'],
      freightindex: 0,
      freightList: [],
      freightdata: [],
      freightIds: [],
      guigedata: [],
      pic: [],
      pics: [],
      cids: [],
      cnames: '',
      clistshow: false,
      ggname: '',
      ggindex: 0,
      ggindex2: 0,
      oldgglist: [],
      gglist: [],
      catche_detailtxt: '',
      product_showset: 1,
      commission_canset: 1,
      bid: 0,
      paramList: [],
      paramdata: [],
      resparamdata: {},
      editorFormdata: [],
      test: '',
      scoreshop_guige: false
    };
  },
  onLoad: function onLoad(opt) {
    this.opt = app.getopts(opt);
    this.getdata();
  },
  methods: {
    getdata: function getdata() {
      var that = this;
      var id = that.opt.id ? that.opt.id : '';
      that.loading = true;
      app.get('ApiAdminScoreshopProduct/edit', {
        id: id
      }, function (res) {
        that.loading = false;
        that.info = res.info;
        that.scoreshop_guige = res.scoreshop_guige;
        that.product_showset = res.product_showset;
        that.commission_canset = res.commission_canset;
        that.pagecontent = res.pagecontent;
        that.aglevellist = res.aglevellist;
        that.levellist = res.levellist;
        that.oldgglist = res.newgglist;
        that.clist = res.clist;
        that.cateArr = res.cateArr;
        that.freightList = res.freightList;
        that.freightdata = res.freightdata;
        if (res.info.freighttype == 1) that.freightindex = 0;
        if (res.info.freighttype == 0) {
          that.freightindex = 1;
          if (res.info.freightdata) {
            that.freightIds = res.info.freightdata.split(',');
          }
        }
        if (res.info.freighttype == 3) that.freightindex = 2;
        if (res.info.freighttype == 4) that.freightindex = 3;
        that.pic = res.pic;
        that.pics = res.pics;
        if (res.info.cid) that.cids = [res.info.cid];
        that.guigedata = res.guigedata;
        that.paramList = res.paramList;
        that.resparamdata = res.paramdata;
        var paramList = res.paramList;
        var editorFormdata = [];
        var paramdata = {};
        for (var i in paramList) {
          var thisval = res.paramdata[paramList[i].name];
          if (!thisval) {
            if (paramList[i].type == 2) {
              thisval = [];
            } else {
              thisval = '';
            }
          }
          if (paramList[i].type == '1') {
            for (var j in paramList[i].params) {
              if (paramList[i].params[j] == thisval) {
                thisval = j;
              }
            }
          }
          editorFormdata.push(thisval);
          paramdata['form' + i] = thisval;
        }
        console.log(editorFormdata);
        console.log(paramdata);
        that.editorFormdata = editorFormdata;
        that.paramdata = paramdata;
        if (res.bset) that.bset = res.bset;
        that.bid = res.bid;
        that.getcnames();
        that.getgglist();
        that.loaded();
      });
    },
    editorBindPickerChange: function editorBindPickerChange(e) {
      var idx = e.currentTarget.dataset.idx;
      var tplindex = e.currentTarget.dataset.tplindex;
      var val = e.detail.value;
      var editorFormdata = this.editorFormdata;
      if (!editorFormdata) editorFormdata = [];
      editorFormdata[idx] = val;
      console.log(editorFormdata);
      this.editorFormdata = editorFormdata;
      this.test = Math.random();
      var field = e.currentTarget.dataset.formidx;
      this.paramdata[field] = val;
    },
    setfield: function setfield(e) {
      var field = e.currentTarget.dataset.formidx;
      var value = e.detail.value;
      this.paramdata[field] = value;
    },
    subform: function subform(e) {
      var that = this;
      var formdata = e.detail.value;
      formdata.cid = that.cids.join(',');
      var guigedata = that.guigedata;
      if (guigedata.length == 0) {
        app.alert('至少需要添加一个规格');
        return;
      }
      for (var i in guigedata) {
        if (guigedata[i].items.length == 0) {
          app.alert('规格分组[' + guigedata[i].title + ']至少需要添加一个规格');
          return;
        }
      }
      var freightindex = this.freightindex;
      if (freightindex == 0) {
        formdata.freighttype = 1;
      } else if (freightindex == 1) {
        formdata.freighttype = 0;
        formdata.freightdata = this.freightIds.join(',');
      } else if (freightindex == 2) {
        formdata.freighttype = 3;
      } else if (freightindex == 3) {
        formdata.freighttype = 4;
      }
      var id = that.opt.id ? that.opt.id : '';
      app.post('ApiAdminScoreshopProduct/save', {
        id: id,
        info: formdata,
        guigedata: guigedata,
        gglist: that.gglist,
        pagecontent: that.pagecontent
      }, function (res) {
        if (res.status == 0) {
          app.error(res.msg);
        } else {
          app.success(res.msg);
          setTimeout(function () {
            app.goto('index', 'redirect');
          }, 1000);
        }
      });
    },
    detailAddtxt: function detailAddtxt() {
      this.$refs.dialogDetailtxt.open();
    },
    dialogDetailtxtClose: function dialogDetailtxtClose() {
      this.$refs.dialogDetailtxt.close();
    },
    catcheDetailtxt: function catcheDetailtxt(e) {
      console.log(e);
      this.catche_detailtxt = e.detail.value;
    },
    dialogDetailtxtConfirm: function dialogDetailtxtConfirm(e) {
      var detailtxt = this.catche_detailtxt;
      console.log(detailtxt);
      var Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);
      var pagecontent = this.pagecontent;
      pagecontent.push({
        "id": Mid,
        "temp": "text",
        "params": {
          "content": detailtxt,
          "showcontent": detailtxt,
          "bgcolor": "#ffffff",
          "fontsize": "14",
          "lineheight": "20",
          "letter_spacing": "0",
          "bgpic": "",
          "align": "left",
          "color": "#000",
          "margin_x": "0",
          "margin_y": "0",
          "padding_x": "5",
          "padding_y": "5",
          "quanxian": {
            "all": true
          },
          "platform": {
            "all": true
          }
        },
        "data": "",
        "other": "",
        "content": ""
      });
      this.pagecontent = pagecontent;
      this.$refs.dialogDetailtxt.close();
    },
    detailAddpic: function detailAddpic() {
      var that = this;
      app.chooseImage(function (urls) {
        var Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);
        var pics = [];
        for (var i in urls) {
          var picid = 'p' + new Date().getTime() + parseInt(Math.random() * 1000000);
          pics.push({
            "id": picid,
            "imgurl": urls[i],
            "hrefurl": "",
            "option": "0"
          });
        }
        var pagecontent = that.pagecontent;
        pagecontent.push({
          "id": Mid,
          "temp": "picture",
          "params": {
            "bgcolor": "#FFFFFF",
            "margin_x": "0",
            "margin_y": "0",
            "padding_x": "0",
            "padding_y": "0",
            "quanxian": {
              "all": true
            },
            "platform": {
              "all": true
            }
          },
          "data": pics,
          "other": "",
          "content": ""
        });
        that.pagecontent = pagecontent;
      }, 9);
    },
    detailAddvideo: function detailAddvideo() {
      var that = this;
      uni.chooseVideo({
        sourceType: ['album', 'camera'],
        success: function success(res) {
          var tempFilePath = res.tempFilePath;
          app.showLoading('上传中');
          uni.uploadFile({
            url: app.globalData.baseurl + 'ApiImageupload/uploadImg/aid/' + app.globalData.aid + '/platform/' + app.globalData.platform + '/session_id/' + app.globalData.session_id,
            filePath: tempFilePath,
            name: 'file',
            success: function success(res) {
              app.showLoading(false);
              var data = JSON.parse(res.data);
              if (data.status == 1) {
                that.video = data.url;
                var pagecontent = that.pagecontent;
                var Mid = 'M' + new Date().getTime() + parseInt(Math.random() * 1000000);
                pagecontent.push({
                  "id": Mid,
                  "temp": "video",
                  "params": {
                    "bgcolor": "#FFFFFF",
                    "margin_x": "0",
                    "margin_y": "0",
                    "padding_x": "0",
                    "padding_y": "0",
                    "src": data.url,
                    "quanxian": {
                      "all": true
                    },
                    "platform": {
                      "all": true
                    }
                  },
                  "data": "",
                  "other": "",
                  "content": ""
                });
                that.pagecontent = pagecontent;
              } else {
                app.alert(data.msg);
              }
            },
            fail: function fail(res) {
              app.showLoading(false);
              app.alert(res.errMsg);
            }
          });
        },
        fail: function fail(res) {
          console.log(res); //alert(res.errMsg);
        }
      });
    },

    detailMoveup: function detailMoveup(e) {
      var index = e.currentTarget.dataset.index;
      var pagecontent = this.pagecontent;
      if (index > 0) pagecontent[index] = pagecontent.splice(index - 1, 1, pagecontent[index])[0];
    },
    detailMovedown: function detailMovedown(e) {
      var index = e.currentTarget.dataset.index;
      var pagecontent = this.pagecontent;
      if (index < pagecontent.length - 1) pagecontent[index] = pagecontent.splice(index + 1, 1, pagecontent[index])[0];
    },
    detailMovedel: function detailMovedel(e) {
      var index = e.currentTarget.dataset.index;
      var pagecontent = this.pagecontent;
      pagecontent.splice(index, 1);
    },
    changeFrieght: function changeFrieght(e) {
      var id = e.currentTarget.dataset.id;
      var index = e.currentTarget.dataset.index;
      var freightIds = this.freightIds;
      var newfreightIds = [];
      var ischecked = false;
      for (var i in freightIds) {
        if (freightIds[i] != id) {
          newfreightIds.push(freightIds[i]);
        } else {
          ischecked = true;
        }
      }
      if (!ischecked) newfreightIds.push(id);
      this.freightIds = newfreightIds;
    },
    freighttypeChange: function freighttypeChange(e) {
      this.freightindex = e.detail.value;
    },
    bindStatusChange: function bindStatusChange(e) {
      this.info.status = e.detail.value;
    },
    bindguigesetChange: function bindguigesetChange(e) {
      console.log(e.detail.value);
      this.info.guigeset = e.detail.value;
      if (this.info.guigeset == 0) {
        this.guigedata = [{
          "k": 0,
          "title": "规格",
          "items": [{
            "k": 0,
            "title": "默认规格"
          }]
        }];
        for (var k in this.gglist) {
          this.gglist = [this.gglist[k]];
          break;
        }
      }
      this.test = Math.random();
    },
    gglistInput: function gglistInput(e) {
      var index = e.currentTarget.dataset.index;
      var field = e.currentTarget.dataset.field;
      var gglist = this.gglist;
      gglist[index][field] = e.detail.value;
      this.gglist = gglist;
      console.log(gglist);
    },
    getgglist: function getgglist() {
      var oldgglist = this.oldgglist;
      var guigedata = this.guigedata;
      var gglist = [];
      var len = guigedata.length;
      var newlen = 1;
      var h = new Array(len);
      for (var i = 0; i < len; i++) {
        var itemlen = guigedata[i].items.length;
        if (itemlen <= 0) {
          return;
        }
        ;
        newlen *= itemlen;
        h[i] = new Array(itemlen);
        for (var j = 0; j < itemlen; j++) {
          h[i][j] = {
            k: guigedata[i].items[j].k,
            title: guigedata[i].items[j].title
          };
        }
      }

      //排列组合算法
      var arr = h; //原二维数组
      var sarr = [[]]; //排列组合后的数组
      for (var i = 0; i < arr.length; i++) {
        var tarr = [];
        for (var j = 0; j < sarr.length; j++) {
          for (var k = 0; k < arr[i].length; k++) {
            tarr.push(sarr[j].concat(arr[i][k]));
          }
        }
        sarr = tarr;
      }
      console.log(sarr);
      console.log(' ------ ');
      for (var i = 0; i < sarr.length; i++) {
        var ks = [];
        var titles = [];
        for (var j = 0; j < sarr[i].length; j++) {
          ks.push(sarr[i][j].k);
          titles.push(sarr[i][j].title);
        }
        ks = ks.join(',');
        titles = titles.join(',');
        //console.log(ks);
        //console.log(titles);
        if (typeof oldgglist[ks] != 'undefined') {
          var val = oldgglist[ks];
        } else {
          var val = {
            ks: ks,
            name: titles,
            market_price: '',
            cost_price: '',
            sell_price: '',
            weight: '100',
            stock: '1000',
            pic: '',
            givescore: '0',
            lvprice_data: null
          };
        }
        gglist.push(val);
      }
      this.gglist = gglist;
      console.log(gglist);
    },
    addgggroupname: function addgggroupname(e) {
      this.ggname = '';
      this.ggindex = -1;
      this.$refs.dialogInput2.open();
    },
    delgggroupname: function delgggroupname(e) {
      var that = this;
      var ggindex = e.currentTarget.dataset.index;
      var title = e.currentTarget.dataset.title;
      uni.showActionSheet({
        itemList: ['修改', '删除'],
        success: function success(res) {
          if (res.tapIndex >= 0) {
            if (res.tapIndex == 0) {
              //修改规格项
              that.ggname = title;
              that.ggindex = ggindex;
              that.$refs.dialogInput2.open();
              return;
            } else if (res.tapIndex == 1) {
              //删除规格项
              var guigedata = that.guigedata;
              var newguigedata = [];
              for (var i in guigedata) {
                if (i != ggindex) {
                  newguigedata.push(guigedata[i]);
                }
              }
              that.guigedata = newguigedata;
              console.log(newguigedata);
              that.getgglist();
            }
          }
        }
      });
    },
    setgggroupname: function setgggroupname(done, val) {
      var guigedata = this.guigedata;
      var ggindex = this.ggindex;
      if (ggindex == -1) {
        //新增规格分组
        ggindex = guigedata.length;
        guigedata.push({
          k: ggindex,
          title: val,
          items: []
        });
        this.guigedata = guigedata;
      } else {
        //修改规格分组名称
        guigedata[ggindex].title = val;
        this.guigedata = guigedata;
      }
      this.$refs.dialogInput2.close();
      this.getgglist();
    },
    addggname: function addggname(e) {
      var ggindex = e.currentTarget.dataset.index;
      this.ggname = '';
      this.ggindex = ggindex;
      this.ggindex2 = -1;
      this.$refs.dialogInput.open();
    },
    delggname: function delggname(e) {
      var that = this;
      var ggindex = e.currentTarget.dataset.index;
      var ggindex2 = e.currentTarget.dataset.index2;
      var k = e.currentTarget.dataset.k;
      var title = e.currentTarget.dataset.title;
      uni.showActionSheet({
        itemList: ['修改', '删除'],
        success: function success(res) {
          if (res.tapIndex >= 0) {
            if (res.tapIndex == 0) {
              //修改规格项
              that.ggname = title;
              that.ggindex = ggindex;
              that.ggindex2 = ggindex2;
              that.$refs.dialogInput.open();
              return;
            } else if (res.tapIndex == 1) {
              //删除规格项
              var guigedata = that.guigedata;
              var newguigedata = [];
              for (var i in guigedata) {
                if (i == ggindex) {
                  var newitems = [];
                  var index2 = 0;
                  for (var j in guigedata[i].items) {
                    if (j != ggindex2) {
                      newitems.push({
                        k: index2,
                        title: guigedata[i].items[j].title
                      });
                      index2++;
                    }
                  }
                  guigedata[i].items = newitems;
                }
                newguigedata.push(guigedata[i]);
              }
              that.guigedata = newguigedata;
              console.log(newguigedata);
              that.getgglist();
            }
          }
        }
      });
    },
    setggname: function setggname(done, val) {
      var guigedata = this.guigedata;
      var ggindex = this.ggindex;
      var ggindex2 = this.ggindex2;
      if (ggindex2 == -1) {
        //新增规格名称
        var items = guigedata[ggindex].items;
        ggindex2 = items.length;
        items.push({
          k: ggindex2,
          title: val
        });
        guigedata[ggindex].items = items;
        this.guigedata = guigedata;
      } else {
        //修改规格名称
        guigedata[ggindex].items[ggindex2].title = val;
        this.guigedata = guigedata;
      }
      this.$refs.dialogInput.close();
      this.getgglist();
    },
    cidsChange: function cidsChange(e) {
      var clist = this.clist;
      var cids = this.cids;
      var cid = e.currentTarget.dataset.id;
      this.cids = [cid];
      this.getcnames();
    },
    getcnames: function getcnames() {
      var cateArr = this.cateArr;
      var cids = this.cids;
      console.log(cids);
      var cnames = [];
      for (var i in cids) {
        cnames.push(cateArr[cids[i]]);
      }
      this.cnames = cnames.join(',');
    },
    changeClistDialog: function changeClistDialog() {
      this.clistshow = !this.clistshow;
    },
    uploadimg: function uploadimg(e) {
      var that = this;
      var pernum = parseInt(e.currentTarget.dataset.pernum);
      if (!pernum) pernum = 1;
      var field = e.currentTarget.dataset.field;
      var pics = that[field];
      if (!pics) pics = [];
      app.chooseImage(function (urls) {
        for (var i = 0; i < urls.length; i++) {
          pics.push(urls[i]);
        }
        if (field == 'pic') that.pic = pics;
        if (field == 'pics') that.pics = pics;
      }, pernum);
    },
    removeimg: function removeimg(e) {
      var that = this;
      var index = e.currentTarget.dataset.index;
      var field = e.currentTarget.dataset.field;
      if (field == 'pic') {
        var pics = that.pic;
        pics.splice(index, 1);
        that.pic = pics;
      } else if (field == 'pics') {
        var pics = that.pics;
        pics.splice(index, 1);
        that.pics = pics;
      }
    },
    uploadimg2: function uploadimg2(e) {
      var that = this;
      var index = e.currentTarget.dataset.index;
      app.chooseImage(function (urls) {
        that.gglist[index].pic = urls[0];
      }, 1);
    },
    removeimg2: function removeimg2(e) {
      var that = this;
      var index = e.currentTarget.dataset.index;
      that.gglist[index].pic = '';
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 2701:
/*!*************************************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/admin/scoreproduct/edit.vue?vue&type=style&index=0&lang=css& ***!
  \*************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&lang=css& */ 2702);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 2702:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!/Users/<USER>/Downloads/tcphp/uniapp/admin/scoreproduct/edit.vue?vue&type=style&index=0&lang=css& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[2695,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/admin/scoreproduct/edit.js.map