<view class="container"><block wx:if="{{isload}}"><block><view class="topbannerbg" style="{{(business.pic?'background:url('+business.pic+') 100%':'')}}"></view><view class="topbannerbg2"></view><view class="topbanner"><view class="left"><image class="img" src="{{business.logo}}"></image></view><view class="right"><view class="f1">{{business.name}}</view><view class="f2">{{business.desc}}</view></view></view><block wx:if="{{1}}"><view class="myqueue"><view class="title"><text class="t1">当前叫号</text></view><view class="head"><block wx:if="{{lastQueue}}"><view class="f1">{{lastQueue.queue_no}}</view></block><block wx:else><view class="f2">暂无</view></block><block wx:if="{{lastQueue}}"><view class="flex"><button class="btn-mini" data-id="{{lastQueue.id}}" data-event-opts="{{[['tap',[['guohao',['$event']]]]]}}" bindtap="__e">过号</button><button class="btn-mini" data-id="{{lastQueue.id}}" data-queue_no="{{lastQueue.queue_no}}" data-event-opts="{{[['tap',[['repeat',['$event']]]]]}}" bindtap="__e">重复</button></view></block></view><view class="content"><block wx:for="{{clist}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="item"><view class="f1"><view class="t1">{{item.name}}</view><view class="t2">{{item.seat_min+"-"+item.seat_max+"人"}}</view></view><view class="f2">等待<text style="color:#FC5729;padding:0 6rpx;font-size:28rpx;">{{item.waitnum}}</text>桌</view><view class="f3"><block wx:if="{{item.waitnum}}"><button class="btn-mini" data-cid="{{item.id}}" data-event-opts="{{[['tap',[['jiaohao',['$event']]]]]}}" bindtap="__e">下一桌</button></block><block wx:else><button class="btn-mini" disabled="true">下一桌</button></block></view></view></block></view></view></block><block wx:if="{{is_show_quhao}}"><view class="btn" data-url="{{'/adminService/admin/quhao?bid='+bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">取号排队</view></block></block></block><block wx:if="{{loading}}"><loading vue-id="322a1499-1" bind:__l="__l"></loading></block></view>