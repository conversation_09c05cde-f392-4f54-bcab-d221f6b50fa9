.tabbar{height: auto; position: relative;}
.tabbar-icon {width: 50rpx;height: 50rpx;}
.tabbar-bar {display: flex;flex-direction: row;width: 100%;height:100rpx;position: fixed;bottom: 0;padding:10rpx 0 0 0;background: #fff;font-size: 24rpx;color: #999;border-top: 1px solid #e5e5e5;z-index: 999999;box-sizing:content-box}
.tabbar-item {flex: 1;text-align: center;overflow: hidden;}
.tabbar-image-box {height: 54rpx;margin-bottom: 4rpx;}
.tabbar-text {line-height: 30rpx;font-size: 24rpx;color:#222222}
.tabbar-text.active{color:#3d7af7}
.tabbar-bot{height:110rpx;width:100%;box-sizing:content-box}
@supports(bottom: env(safe-area-inset-bottom)){
.tabbar-bot{padding-bottom:env(safe-area-inset-bottom);}
.tabbar-bar{padding-bottom:env(safe-area-inset-bottom);}
}
page{background:#fff}
.width{width: 95%;margin: 0 auto;}
.view-width{width: 100%;height: auto;padding-bottom:60rpx}
.head-class{}
.head-view{

		padding: 10rpx 40rpx 15rpx 40rpx;
}
.head-view .avat-view{display:flex;align-items: center;justify-content: flex-start;}
.head-view .avat-view .avat-img-view {width: 80rpx;height:80rpx;border-radius: 50%;overflow: hidden;border:2rpx #fff solid;}
.head-view .avat-view .avat-img-view image{width: 100%;height: 100%;}
.head-view .avat-view .user-info{display: flex;align-items: flex-start;flex-direction: column;}
.head-view .avat-view .user-info .nickname{font-size: 32rpx;font-weight: bold;}
.head-view .avat-view .user-info .un-text{font-size: 24rpx;color: rgba(34, 34, 34, 0.7);}
.head-view .avat-view .imgback{width: 40rpx;height: 40rpx;margin-top: 4rpx;}
.head-view .option-img-view{width: 160rpx;display: flex;align-items: center;justify-content: flex-end;}
.head-view .recharge{background: #fff; width: 100rpx;color: #FB6534; text-align: center; font-size: 24rpx; padding: 5rpx; border-radius: 10rpx;margin-left: 20rpx;}
.head-view .setup-view{position:relative;width: 64rpx;height:64rpx;}
.head-view .setup-view image{width: 64rpx;height: 64rpx;}
.head-view .setup-view .setup-img{
}
.today-data{padding: 20rpx 65rpx 40rpx;justify-content:space-around
}
.option-view{width: 50%;}
.option-view text{text-align: center;}
.option-view .title-text{font-size: 24rpx;color: #5f6064;padding-bottom: 15rpx;}
.option-view .title-text .title-icon{width: 30rpx;height: 30rpx;margin-left: 10rpx;}
.option-view .num-text{font-size: 28rpx;font-weight: 700}
.option-view .unit-money{font-size: 24rpx;font-weight: 700;}
.mall-orders{border-radius:12rpx;overflow: hidden;}
.order-title{padding: 32rpx 40rpx;align-items: center;background: linear-gradient(to right, #c4dfff , #d7e8ff);}
.order-title .title-text{font-size: 26rpx;font-weight: 500;color: #222;}
.order-title .all-text{font-size: 24rpx;color: #5f6064;}
.order-title .title-text .left-img{width: 6rpx;height: 24rpx;margin-right: 12rpx;}
.order-title .all-text .right-img{width: 10rpx;height: 20rpx;margin-left: 20rpx;}
.order-list{justify-content: space-around;padding:40rpx 0rpx;background: #D5E8FF;}
.order-list .option-order{align-items: center;}
.order-list .option-order .num-text{font-size: 28rpx;font-weight: bold;padding-bottom:10rpx;}
.order-list .option-order .title-text{font-size: 24rpx;color: #5f6064;}
.meun-view{padding:40rpx;}
.meun-view .meun-options .menu-img{width: 88rpx;height:88rpx;}
.meun-view .meun-options .menu-text{font-size: 24rpx;color: #242424;margin-top:12rpx;}
.menu-manage{margin-bottom:50rpx}
.menu-manage .menu-title{font-size: 30rpx;color: #242424;font-weight:bold;padding: 10rpx 40rpx;}
.menu-manage .menu-list{display: flex;align-items: center;flex-wrap: wrap;justify-items: flex-start;}
.menu-manage .menu-list .meun-list-options{width: 16%;margin:4% 2%;}
.menu-manage .menu-list .meun-list-options .menu-img{width:60rpx;height:60rpx;}
.menu-manage .menu-list .meun-list-options .menu-text{font-size: 24rpx;color: #242424;margin-top: 20rpx;white-space: nowrap;}
.menu-manage .divider-div{width: 100%;height:20rpx;background: #F2F3F4;}
.menu-manage .tab-div{padding-top: 20rpx;}
.menu-manage .tab-div .tab-options {height:100rpx;font-size:26rpx;color: #666666;justify-content:flex-start;align-items:center;padding:20rpx 40rpx 0rpx}
.menu-manage .tab-div .tab-options-active{color:#3d7af7}
.menu-manage .tab-div .tab-options .color-bar{width:48rpx;height:3px;background: #3d7af7;margin-top: 20rpx;}
.menu-manage .data-div{padding: 0rpx 30rpx;align-items:center;justify-content:space-between;}
.data-div-list{display: flex;flex-direction: row;width: 100%;align-items: center;justify-content: flex-start;flex-wrap:wrap;}
.data-div-list .data-div-options{display: flex;flex-direction: column;align-items: center;justify-content: space-around;width: 22%;padding: 30rpx 0rpx;}
.data-div-list .data-div-options .title-text{font-size: 24rpx;color: #5f6064;}
.data-div-list .data-div-options .num-text{font-size: 28rpx;font-weight: bold;color: #222222;margin-top: 20rpx;}
.data-div-list .border-bar-div{height: 40rpx;width: 3rpx;background:#e5e5e5;margin: 0rpx 12rpx;}
.data-div-list .border-bar-div:nth-child(8n){height: 40rpx;width: 0rpx;background:red;margin: 0rpx 0rpx;}
.data-div-list.hotelorder .data-div-options{ width: 20%;}
.mer-list{padding: 20rpx 40rpx;display: flex;align-items: center;justify-content: space-between;}
.mer-list .merchant-view {display: flex;flex-direction: column;align-items: flex-start;
	width: 31%;height: 380rpx;border-radius:16rpx;padding: 0rpx 18rpx;background-repeat: no-repeat; background-size: cover;}
.mer-list .merchant-view .mer-title{font-size: 24rpx;color: #242424;padding: 26rpx 0rpx;font-weight: 500;}
.mer-list .merchant-view .mer-options{font-size: 20rpx;color: #7B7B7B;padding-bottom:20rpx;white-space: nowrap;}
.mer-list .merchant-view .mer-options text{padding: 0rpx 10rpx;font-size: 20rpx;text-align: left;}
.cysj-text .mer-options text{color: #3F71E5;font-weight: 500;}
.scdd-text .mer-options text{color: #FF9000;font-weight: 500;}
.wmdd-text .mer-options text{color: #02B56A;font-weight: 500;}
.scroll-Y{height: 280rpx;}

