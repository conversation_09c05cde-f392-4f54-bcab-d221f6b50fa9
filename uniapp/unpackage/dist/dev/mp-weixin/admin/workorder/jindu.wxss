
page{	background: #f6f6f6;}
.banner{	position: absolute;	width: 100%;height: 700rpx;}
.page{	position: relative;	padding: 30rpx;}
.header{	padding: 30rpx;display: flex;justify-content: space-between;}
.header_title{	font-size: 40rpx;	color: #fff;	font-weight: bold;}
.header_text{	color: rgba(255, 255, 255, 0.8);	font-size: 24rpx;margin-top: 20rpx;		display: flex;align-items: center;}
.header_icon{		height: 30rpx;width: 30rpx;	margin: 0 0 0 10rpx;}
.header_tag{	width: 80rpx;
}
.body{position: relative;	padding: 30rpx 50rpx;	background: #fff;	border-radius: 20rpx;	margin-top: 20rpx;}
.body_title{	position: relative;font-size: 35rpx;color: #333;	text-align: center;	font-weight: bold;}
.body_title text{position: relative;}
.body_icon{position: absolute;	width: 120rpx;	left: 0;	right: 0;	bottom: 0;	margin: 0 auto;}
.content{	margin-top: 50rpx;}
.module{		position: relative;	padding: 0 0 0 50rpx;	min-height: 200rpx;	border-left: 2px dashed #e4e5e7;}
.module_title{	font-size: 28rpx;	color: #333;	font-weight: bold;}
.module_time{	font-size: 24rpx;	color: #999;margin-top: 10rpx;}
.module_text{		font-size: 26rpx;	color: #666;	margin-top: 10rpx;}
.module_opt{	padding: 30rpx 0 50rpx 0;}
.module_btn{	font-size: 24rpx;color: #333;border: 1px solid #f0f0f0;	padding: 15rpx 30rpx;	border-radius: 100rpx;	margin: 0 10rpx 0 0;}
.module_r{background: #fd3b60;color: #fff;}
.module_tag{	position: absolute;	height: 26px;width: 26px;left: -14px;	top: 0;
}
.module_active{	border-color: #fd3b60;}
.module:last-child{	border-color: #fff;}
.module_null{		color: #999;}
.module_opt .t3{ margin-bottom: 10rpx; font-size:26rpx; color:#666}
.module_opt .t3_1{  color: #999;}
.module_opt .t4{ margin-bottom: 10rpx; font-size:26rpx ;color: #999;}
.modal .modal_jindu{ background: #fff;align-items: center; margin: auto; width: 100%;  border-radius: 10rpx; padding: 40rpx;margin-top: 30rpx;}
.modal_jindu .close image { width: 20rpx; height: 20rpx; position: absolute; top:10rpx; right: 20rpx;}
.modal_jindu .title{ font-size: 32rpx; font-weight: bold;}
.uni-list{ margin-top: 30rpx;}
.uni-list-cell{ display: flex; height: 80rpx;}
.beizhu label{ width: 100rpx;}
.modal_jindu .btn{  background: #1658c6; border-radius: 3px;line-height: 24px; border: none; padding: 0 10px;color: #fff;font-size: 20px; text-align: center; width: 300px;  display: flex; height: 40px; justify-content: center;align-items: center;}
.modal_jindu .item .f1{ width:60rpx;position:relative}
/*.logistics img{width: 15px; height: 15px; position: absolute; left: -8px; top:11px;}*/
.modal_jindu .item .f1 image{width: 30rpx; height: 100%; position: absolute; left: -16rpx; top: 0rpx;}
.modal_jindu .item .f2{display:flex;flex-direction:column;flex:auto;padding:10rpx 0}
.modal_jindu .item .f2 .t1{font-size: 30rpx;}
.modal_jindu .item .f2 .t1{font-size: 26rpx;}
.form-item {width: 100%;padding: 16rpx 0;background: #fff;display: flex;align-items: center;justify-content:space-between;border-bottom: 1px #ededed solid;}
.form-item .label {color: #333;width: 150rpx;flex-shrink:0}
.form-item .radio{-webkit-transform:scale(.7);transform:scale(.7);}
.form-item .checkbox{-webkit-transform:scale(.7);transform:scale(.7);}
.form-item .input {height: 70rpx;padding-left: 10rpx;text-align: right;flex:1;border:1px solid #eee;padding:0 8rpx;border-radius:2px;}
.form-item .textarea{height:140rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}
.form-item .radio-group{display:flex;flex-wrap:wrap;justify-content:flex-end}
.form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}
.form-item .radio2{display:flex;align-items:center;}
.form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}
.form-item .checkbox-group{display:flex;flex-wrap:wrap;justify-content:flex-end}
.form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}
.form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}
.form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}
.form-item .picker{height: 70rpx;line-height:70rpx;flex:1;text-align:right}
.form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff;z-index: 2;}
.form-imgbox-close image{width:100%;height:100%}
.form-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.form-imgbox-img>image{max-width:100%;}
.form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.form-uploadbtn{position:relative;height:200rpx;width:200rpx}
.dp-form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.dp-form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-10rpx;top:-26rpx;color:#999;font-size:32rpx;background:#999;z-index:9;border-radius:50%}
.dp-form-imgbox-close .image{width:100%;height:100%}
.dp-form-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.dp-form-imgbox-img>.image{max-width:100%;}
.dp-form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.dp-form-uploadbtn{position:relative;height:100rpx;width:100rpx}
.form-item4{width:100%;background: #fff; padding: 20rpx 0rpx;margin-top:1px}
.form-item4 .label{ width:150rpx;}
.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.layui-imgbox-img{display: block;width:150rpx;height:150rpx;padding:2px;background-color: #f6f6f6;overflow:hidden ;margin-top: 20rpx;}
.layui-imgbox-img>image{max-width:100%;}
.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.uploadbtn{position:relative;height:200rpx;width:200rpx}
.hfitem{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}
.hfitem:last-child{ border-bottom: 0;}
.hfitem .t1{width:200rpx;color: #999;}
.hfitem .t1.title{font-size: 36rpx;font-weight: 600;line-height: 80rpx;width:100%}
.hfitem .t2{flex:1;text-align:right}
.hfitem .red{color:red}
.zktext{ text-align: right;margin-top: 20rpx;  color: #1296db;	font-size: 28rpx; display:flex; align-items: center; justify-content: end;
}
.zktext image{ width:30rpx; height:30rpx}


