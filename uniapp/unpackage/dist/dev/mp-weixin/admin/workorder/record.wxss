
.content{ width:100%;margin:0;}
.content .item{ width:94%;margin:20rpx 3%;background:#fff;border-radius:16rpx;padding:30rpx 30rpx;display:flex;align-items:center;}
.content .item:last-child{border:0}
.content .item .f1{width:100%;display:flex;flex-direction:column}
.content .item .f1 .t1{color:#000000;font-size:30rpx;word-break:break-all;overflow:hidden;text-overflow:ellipsis; height:50rpx}
.content .item .f1 .t2{color:#666666;margin-top:10rpx}
.content .item .f1 .t3{color:#666666}
.content .item .f2{width:20%;font-size:32rpx;text-align:right}
.content .item .f2 .t1{color:#03bc01}
.content .item .f2 .t2{color:#000000}
.content .item .f3{ flex:1;font-size:30rpx;text-align:right}
.content .item .f3 .t1{color:#03bc01}
.content .item .f3 .t2{color:#000000}
.content .item .f1 .itembox{ display: flex; justify-content: space-between;}
.jindu{ border: 1rpx solid #ccc; font-size: 24rpx; padding: 5rpx 10rpx; border-radius: 10rpx; color: #555;}
.topsearch{width:94%;margin:10rpx 3%;}
.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}
.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}
.searchbox{ width: 100%;background: #FFFFFF;padding:20rpx 26rpx;z-index: 9999;display: flex;justify-content: space-between;align-items: center;
}
.searchbox .picker{display: flex;justify-content: space-between;align-items: center;border:1rpx solid #e0e0e0;border-radius: 6rpx;padding:0 12rpx;height: 64rpx;line-height: 70rpx;
}
.searchbox .picker .picker-txt{text-overflow: ellipsis;white-space: nowrap;overflow: hidden;height: 70rpx; width: 300rpx;}
.searchbox .down{width: 20rpx;height: 20rpx;margin-left: 10rpx;flex-shrink: 0;}
.pickerD{width: 50%;margin-left: 20rpx;}
.searchbox .cates{ width: 30%;overflow: hidden;}
.date{ display: flex;align-items: center;}
.date .begindate{
}
.date .begindate .uni-input{ color: grey;}
.date .enddate { margin:0 10rpx;}
.date .enddate .uni-input{  color: grey;}
.searchbtn{ display: flex; width: 120rpx;height: 50rpx;color: #fff;line-height: 50rpx; border-radius:50rpx;align-items: center;justify-content: center;}
.modal{ position: fixed; background:rgba(0,0,0,0.3); width: 100%; height: 100%; top:0; z-index: 100;}
.modal .modal_jindu{ background: #fff; position: absolute; top: 20%; align-items: center; margin: auto; width: 90%; left: 30rpx; border-radius: 10rpx; padding: 40rpx;overflow-y:auto; display: flex; flex-wrap: wrap; max-height: 600rpx;}
.modal_jindu .close image { width: 30rpx; height: 30rpx;position: fixed; top:21%;right: 60rpx;}
.modal_jindu .title{ font-size: 32rpx; font-weight: bold;}
.uni-list{ margin-top: 30rpx;}
.uni-list-cell{ display: flex; height: 80rpx;}
.beizhu label{ width: 100rpx;}
.modal_jindu .btn{  background: #1658c6; border-radius: 3px;line-height: 24px; border: none; padding: 0 10px;color: #fff;font-size: 20px; text-align: center; width: 300px;  display: flex; height: 40px; justify-content: center;align-items: center;}
.beizhu textarea{  height: 100rpx;}
.modal_jindu .item{
}
.modal_jindu .item .f1{ position:relative}
/*.logistics img{width: 15px; height: 15px; position: absolute; left: -8px; top:11px;}*/
.modal_jindu .item .f1 image{width: 30rpx; height: 100rpx; position: absolute; left: -16rpx; top: 0rpx;}
.modal_jindu .item .f2{display:flex;flex-direction:column;flex:auto;padding:10rpx 0; margin-left: 30rpx;}
.modal_jindu .item .f2 .t1{font-size: 30rpx; width: 100%;word-break:break-all;
}
.modal_jindu .item .f2 .t1{font-size: 26rpx;}
.modal_jindu .item .f2 .t3{font-size: 24rpx; color:#008000; margin-top: 10rpx;}
.modal_jindu .item .f2 .t4{font-size: 24rpx;  color:#008000;}
.layui-imgbox-img{display: block;width:150rpx;height:150rpx;padding:2px;background-color: #f6f6f6;overflow:hidden ;margin-top: 20rpx;}
.layui-imgbox-img>image{max-width:100%;}

