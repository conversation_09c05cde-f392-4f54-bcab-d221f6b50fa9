<view class="container"><block wx:if="{{isload}}"><block><view class="content-container"><view class="nav_left"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['nav_left_items '+(curIndex==index?'active':'')]}}" style="{{'color:'+(curIndex==index?item.m0:'#333')+';'}}" data-index="{{index}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['switchRightTab',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+(item.m1)+';'}}"></view>{{item.$orig.name}}</view></block></block></view><view class="nav_right"><view class="nav_right-content"><scroll-view class="classify-box" scroll-y="true" data-event-opts="{{[['scrolltolower',[['scrolltolower',['$event']]]]]}}" bindscrolltolower="__e"><view class="product-itemlist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="idx2" wx:key="id"><block wx:if="{{iscate==1}}"><view class="item"><view class="product-info"><view class="img" data-id="{{item.id}}" data-index="{{idx2}}" data-event-opts="{{[['tap',[['changeCTab',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/workorder/'+(curIndex2==idx2?'down':'up')+'.png?v1'}}" mode="widthFix"></image></view><view class="p1" data-url="{{'record?cid='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>{{item.name}}</text><block wx:if="{{item.count>0}}"><text class="count">{{item.count}}</text></block></view></view><block wx:for="{{item.list}}" wx:for-item="subitem" wx:for-index="subindex"><block wx:if="{{curIndex2==idx2}}"><view class="list" data-url="{{'record?cateid='+subitem.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><label>{{''+subitem.name}}</label><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></block></block></view></block><block wx:else><view class="item"><view class="product-info"><block wx:for="{{datalist}}" wx:for-item="subitem" wx:for-index="subindex"><view class="list" style="width:100%;position:relative;" data-url="{{'record?cateid='+subitem.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><label>{{''+subitem.name}}</label><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text><block wx:if="{{subitem.count>0}}"><text class="count" style="position:absolute;top:10rpx;right:30rpx;">{{subitem.count}}</text></block></view></block></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="6b6456f0-1" text="没有更多商品了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="6b6456f0-2" text="暂无相关商品" bind:__l="__l"></nodata></block><view style="width:100%;height:100rpx;"></view></scroll-view></view></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="6b6456f0-3" loadstyle="left:62.5%" bind:__l="__l"></loading></block><dp-tabbar vue-id="6b6456f0-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="6b6456f0-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>