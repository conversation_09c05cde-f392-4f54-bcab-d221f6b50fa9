<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="form-box"><view class="form-item"><view class="f1">工单分类<text style="color:red;">*</text></view><view data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" class="f2" bindtap="__e"><block wx:if="{{$root.g0>0}}"><text>{{cnames}}</text></block><block wx:else><text style="color:#888;">请选择</text></block><image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></view><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">提交</button></form><block wx:if="{{clistshow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择工单分类</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><view class="clist-item" data-id="{{item.$orig.id}}"><view class="flex1">{{item.$orig.name}}</view></view><block wx:for="{{item.l0}}" wx:for-item="item2" wx:for-index="index2" wx:key="id"><block><view class="clist-item" style="padding-left:80rpx;" data-id="{{item2.$orig.id}}" data-event-opts="{{[['tap',[['cidsChange',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.g1-1==index2}}"><view class="flex1">{{"└ "+item2.$orig.name}}</view></block><block wx:else><view class="flex1">{{"├ "+item2.$orig.name}}</view></block><view class="radio" style="{{(item2.m2?'background:'+item2.m3+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></block></block></block></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="3f21f7a4-1" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="3f21f7a4-2" data-ref="popmsg" bind:__l="__l"></popmsg></view>