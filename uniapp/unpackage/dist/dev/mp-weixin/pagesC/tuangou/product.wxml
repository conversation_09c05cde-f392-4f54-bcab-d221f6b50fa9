<view><block wx:if="{{isload}}"><block><block wx:if="{{sysset.showgzts}}"><block><view style="width:100%;height:88rpx;"></view><view class="follow_topbar"><view class="headimg"><image src="{{sysset.logo}}"></image></view><view class="info"><view class="i">欢迎进入<text style="{{'color:'+($root.m0)+';'}}">{{sysset.name}}</text></view><view class="i">关注公众号享更多专属服务</view></view><view data-event-opts="{{[['tap',[['showsubqrcode',['$event']]]]]}}" class="sub" style="{{'background-color:'+($root.m1)+';'}}" bindtap="__e">立即关注</view></view><uni-popup class="vue-ref" vue-id="5f2f3be9-1" id="qrcodeDialog" type="dialog" data-ref="qrcodeDialog" bind:__l="__l" vue-slots="{{['default']}}"><view class="qrcodebox"><image class="img" src="{{sysset.qrcode}}" data-url="{{sysset.qrcode}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">长按识别二维码关注</view><view data-event-opts="{{[['tap',[['closesubqrcode',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="{{pre_url+'/static/img/close2.png'}}"></image></view></view></uni-popup></block></block><block wx:if="{{$root.g0>0}}"><view style="position:fixed;top:15vh;left:20rpx;z-index:9;background:rgba(0,0,0,0.6);border-radius:20rpx;color:#fff;padding:0 10rpx;"><swiper style="position:relative;height:54rpx;width:350rpx;" autoplay="{{true}}" interval="{{5000}}" vertical="{{true}}"><block wx:for="{{bboglist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="flex-y-center" data-url="{{'product?id='+item.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px;" src="{{item.headimg}}"></image><view style="width:300rpx;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:22rpx;" class="_div">{{item.nickname+" "+item.showtime+"购买了该商品"}}</view></swiper-item></block></swiper></view></block><block wx:if="{{isplay==0}}"><view class="swiper-container"><swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" interval="{{5000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{product.pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item class="swiper-item"><view class="swiper-item-view"><image class="img" src="{{item}}" mode="widthFix"></image></view></swiper-item></block></block></swiper><view class="imageCount">{{current+1+"/"+$root.g1}}</view><block wx:if="{{product.video}}"><view data-event-opts="{{[['tap',[['payvideo',['$event']]]]]}}" class="provideo" bindtap="__e"><image src="{{pre_url+'/static/img/video.png'}}"></image><view class="txt">{{product.video_duration}}</view></view></block></view></block><block wx:if="{{isplay==1}}"><view class="videobox"><video class="video" autoplay="true" id="video" src="{{product.video}}"></video><view data-event-opts="{{[['tap',[['parsevideo',['$event']]]]]}}" class="parsevideo" bindtap="__e">退出播放</view></view></block><view class="{{[tuangou_status==2?'seckill-title-end':'','seckill_title']}}"><image class="f0" src="{{pre_url+'/static/img/tghd.png'}}"></image><view class="f1"><view class="t1"><block wx:if="{{showprice_dollar&&product.usd_sellprice}}"><block><text style="font-size:24rpx;">$</text>{{product.usd_sellprice+''}}<text style="margin-left:6rpx;font-size:36rpx;"><text style="font-size:24rpx;">￥</text>{{product.min_price}}</text></block></block><block wx:else><block><text style="font-size:24rpx;">￥</text>{{product.min_price+''}}</block></block><text class="x2">{{"￥"+product.market_price}}</text></view><block wx:if="{{tuangou_status==0}}"><view class="t2">团购未开始</view></block><block wx:if="{{tuangou_status==1}}"><view class="t2">火爆团购中</view></block><block wx:if="{{tuangou_status==2}}"><view class="t2">团购已结束</view></block></view><block wx:if="{{tuangou_status!=2}}"><view class="f3"><view class="t1">{{"距团购"+(tuangou_status==0?'开始':'结束')+"还剩"}}</view><view class="t2" id="djstime"><text class="djsspan">{{djshour}}</text>:<text class="djsspan">{{djsmin}}</text>:<text class="djsspan">{{djssec}}</text></view></view></block></view><view class="header"><view class="price_share"><view class="price"><view class="f1" style="{{'color:'+($root.m2)+';'}}"><block wx:if="{{showprice_dollar&&product.usd_sellprice}}"><block><text style="font-size:24rpx;">$</text>{{product.usd_sellprice+''}}<text style="margin-left:6rpx;font-size:36rpx;"><text style="font-size:28rpx;">￥</text>{{product.sell_price}}</text></block></block><block wx:else><block><text style="font-size:36rpx;">￥</text>{{product.sell_price+''}}</block></block></view><block wx:if="{{product.market_price*1>product.sell_price*1}}"><view class="f2">{{"￥"+product.market_price}}</view></block></view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/share.png'}}"></image><text class="txt">分享</text></view></view><view class="title">{{product.name}}</view><view class="sales_stock"><view class="f1">{{"已参团："+product.buynum+"人次"}}</view><view class="f2">{{"库存："+product.stock}}</view></view></view><view class="tg_pricedata"><view class="tg_pricetitle">团购价格</view><block wx:for="{{product.pricedata}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tg_price flex"><view class="t1">{{"满"+item.num+"人次"}}</view><block wx:if="{{showprice_dollar&&item.usd_money}}"><block><view class="t2 flex1" style="color:#e94745;margin-left:10rpx;"><text style="font-size:24rpx;">$</text>{{item.usd_money+" "+item.money+"元"}}</view></block></block><block wx:else><block><view class="t2 flex1">{{item.money+"元"}}</view></block></block><view class="t3"><block wx:if="{{product.buynum>=item.num}}"><text style="color:#f50;">已达成</text></block><block wx:else><text style="color:#333;">{{"还差"+(item.num-product.buynum)+"人次"}}</text></block></view></view></block><view style="padding:5px 0;font-size:22rpx;color:red;">注：付款金额并非实际花费金额，活动结束后所有参与人均按照已达成的最低价格计算，多付的金额将全部退还</view></view><block wx:if="{{product.givescore>0}}"><view class="cuxiaodiv"><view class="cuxiaopoint"><view class="f0">{{"送"+$root.m3}}</view><view class="f1" style="font-size:26rpx;">{{"购买可得"+$root.m4+product.givescore+"个"}}</view></view></view></block><block wx:if="{{$root.g2}}"><view class="cuxiaodiv"><block wx:if="{{$root.g3>0}}"><view class="cuxiaopoint"><view class="f0">促销</view><view class="f1"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="t" style="{{'background:'+('rgba('+item.m5+',0.1)')+';'+('color:'+(item.m6)+';')}}"><text class="t0">{{item.$orig.tip}}</text><text class="t1">{{item.$orig.name}}</text></view></block></view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></block><block wx:if="{{$root.g4>0}}"><view class="cuxiaopoint"><view class="f0">优惠</view><view class="f1"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="t" style="{{'background:'+('rgba('+item.m7+',0.1)')+';'+('color:'+(item.m8)+';')}}"><text class="t0" style="padding:0 6px;">券</text><text class="t1">{{item.$orig.name}}</text></view></block></view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></block></view></block><block wx:if="{{showcuxiaodialog}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hidecuxiaodetail',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">优惠促销</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hidecuxiaodetail',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{cuxiaolist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="service-item"><view class="suffix"><view class="type-name"><text style="border-radius:4px;border:1px solid #f05423;color:#ff550f;font-size:20rpx;padding:2px 5px;">{{item.tip}}</text><text style="color:#333;margin-left:20rpx;">{{item.name}}</text></view></view></view></block><couponlist vue-id="5f2f3be9-2" couponlist="{{couponlist}}" data-event-opts="{{[['^getcoupon',[['getcoupon']]]]}}" bind:getcoupon="__e" bind:__l="__l"></couponlist></view></view></view></block><block wx:if="{{tuangouset.comment==1&&commentcount>0}}"><view class="commentbox"><view class="title"><view class="f1">{{"评价("+commentcount+")"}}</view><view class="f2" data-url="{{'commentlist?proid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">好评度<text style="{{'color:'+($root.m9)+';'}}">{{product.comment_haopercent+"%"}}</text><image style="width:32rpx;height:32rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view><view class="comment"><block wx:if="{{$root.g5>0}}"><view class="item"><view class="f1"><image class="t1" src="{{commentlist[0].headimg}}"></image><view class="t2">{{commentlist[0].nickname}}</view><view class="flex1"></view><view class="t3"><block wx:for="{{[0,1,2,3,4]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><image class="img" src="{{pre_url+'/static/img/star'+(commentlist[0].score>item2?'2native':'')+'.png'}}"></image></block></view></view><view class="f2"><text class="t1">{{commentlist[0].content}}</text><view class="t2"><block wx:if="{{commentlist[0].content_pic!=''}}"><block><block wx:for="{{commentlist[0].content_pic}}" wx:for-item="itemp" wx:for-index="index" wx:key="index"><block><view data-url="{{itemp}}" data-urls="{{commentlist[0].content_pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"><image src="{{itemp}}" mode="widthFix"></image></view></block></block></block></block></view></view><view class="f3" data-url="{{'commentlist?proid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看全部评价</view></view></block><block wx:else><view class="nocomment">暂无评价~</view></block></view></view></block><block wx:if="{{tuangouset.showjd==1}}"><view class="shop"><image class="p1" src="{{business.logo}}"></image><view class="p2 flex1"><view class="t1">{{business.name}}</view><view class="t2">{{business.desc}}</view></view><button class="p4" style="{{'background:'+('linear-gradient(90deg,'+$root.m10+' 0%,rgba('+$root.m11+',0.8) 100%)')+';'}}" data-url="{{product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid}}" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">进入店铺</button></view></block><view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">商品描述</view><view class="t2"></view><view class="t1"></view></view><view class="detail"><dp vue-id="5f2f3be9-3" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></view><view style="width:100%;height:140rpx;"></view><block wx:if="{{product.status==1}}"><view class="{{['bottombar','flex-row',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view class="f1"><block wx:if="{{kfurl!='contact::'}}"><view class="item" data-url="{{kfurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/kefu.png'}}"></image><view class="t1">客服</view></view></block><block wx:else><button class="item" open-type="contact" show-message-card="true"><image class="img" src="{{pre_url+'/static/img/kefu.png'}}"></image><view class="t1">客服</view></button></block><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="item flex1" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/share2.png'}}"></image><view class="t1">分享</view></view><view data-event-opts="{{[['tap',[['addfavorite',['$event']]]]]}}" class="item" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/shoucang.png'}}"></image><view class="t1">{{isfavorite?'已收藏':'收藏'}}</view></view></view><view class="op"><block wx:if="{{tuangou_status==1}}"><view class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m12)+';'}}" data-controller="ApiTuangou" data-btntype="2" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" bindtap="__e">立即团购</view></block><block wx:else><view class="tobuy flex-x-center flex-y-center" style="background:#ccc;">{{tuangou_status==0?'未开始':'已结束'}}</view></block></view></view></block><scrolltop vue-id="5f2f3be9-4" isshow="{{scrolltopshow}}" bind:__l="__l"></scrolltop><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m13=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/sharefriends.png'}}"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m14=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/sharefriends.png'}}"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m15!='h5'}}"><button class="f1" open-type="share"><image class="img" src="{{pre_url+'/static/img/sharefriends.png'}}"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/sharepic.png'}}"></image><text class="t1">生成分享图片</text></view></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/close.png'}}"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block><block wx:if="{{buydialogShow}}"><buydialog-tuangou vue-id="5f2f3be9-5" proid="{{product.id}}" btntype="{{btntype}}" menuindex="{{menuindex}}" controller="ApiTuangou" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]]]}}" bind:buydialogChange="__e" bind:__l="__l"></buydialog-tuangou></block><scrolltop vue-id="5f2f3be9-6" isshow="{{scrolltopshow}}" bind:__l="__l"></scrolltop></block></block><block wx:if="{{loading}}"><loading vue-id="5f2f3be9-7" bind:__l="__l"></loading></block><dp-tabbar vue-id="5f2f3be9-8" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5f2f3be9-9" data-ref="popmsg" bind:__l="__l"></popmsg></view>