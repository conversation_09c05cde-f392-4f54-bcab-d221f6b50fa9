<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="28c2057e-1" itemdata="{{['全部','待审核','已审核']}}" itemst="{{['all','0','1']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'orderdetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="head"><text class="flex1">{{"订单号："+item.ordernum}}</text><block wx:if="{{item.l_status==0}}"><text class="st0">待审核</text></block><block wx:if="{{item.l_status==1}}"><text class="st2">已收款</text></block><block wx:if="{{item.l_status==2}}"><text class="st3">已取消</text></block><block wx:if="{{item.l_status==3}}"><text class="st3">已转给上级</text></block></view><block wx:for="{{item.prolist}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><block><view class="content" style="border-bottom:none;"><view data-url="{{'detail?id='+item1.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item1.pic}}"></image></view><view class="detail"><text class="t1">{{item1.name}}</text><text class="t2">{{item1.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item1.sell_price+" × "+item1.num}}</text></view></view></view></block></block><view class="bottom"><text>{{"共计"+item.procount+"件商品 实付:￥"+item.totalprice}}</text></view><view class="bottom"><text>{{'收款金额￥'+item.money}}</text></view><view class="bottom"><text>{{'提交审核金额￥'+item.tj_totalprice}}</text></view><view class="op"><view class="btn2" data-url="{{'orderdetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">详情</view><block wx:if="{{item.l_status==1&&item.status!=4}}"><block><view class="btn2" style="width:200rpx;" data-id="{{item.id}}" data-type="{{3}}" data-event-opts="{{[['tap',[['todoorder',['$event']]]]]}}" catchtap="__e">转给上级审核</view></block></block><block wx:if="{{item.l_status==0&&item.status!=4}}"><block><view class="btn2" data-id="{{item.id}}" data-type="{{1}}" data-event-opts="{{[['tap',[['todoorder',['$event']]]]]}}" catchtap="__e">确认收款</view><view class="btn2" data-id="{{item.id}}" data-type="{{2}}" data-event-opts="{{[['tap',[['todoorder',['$event']]]]]}}" catchtap="__e">取消订单</view></block></block></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="28c2057e-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="28c2057e-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="28c2057e-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="28c2057e-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="28c2057e-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>