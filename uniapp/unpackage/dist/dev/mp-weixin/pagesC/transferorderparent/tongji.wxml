<view class="container"><block wx:if="{{isload}}"><block><view class="content"><block wx:if="{{1}}"><view class="topsearch flex-y-center sx"><text class="t1">日期筛选：</text><view class="f2 flex" style="line-height:30px;"><picker mode="date" value="{{startDate}}" data-event-opts="{{[['change',[['bindStartDateChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{startDate}}</view></picker><view style="padding:0 10rpx;color:#222;font-weight:bold;">至</view><picker mode="date" value="{{endDate}}" data-event-opts="{{[['change',[['bindEndDateChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{endDate}}</view></picker><view class="t_date"><block wx:if="{{startDate}}"><view data-event-opts="{{[['tap',[['clearDate',['$event']]]]]}}" class="x1" bindtap="__e">清除</view></block></view></view></view></block></view><view class="content"><view class="label"><text class="t1">{{"分销数据统计("+userinfo.nickname+" ID:"+userinfo.id+")"}}</text></view><view class="yejilabel"><text class="t1">{{"订单总量："+(is_end?userinfo.order_num||0:'计算中')+" 单"}}</text><text class="t1">{{"销售金额："+(is_end?userinfo.teamyeji||0:'计算中')+" 元"}}</text><text class="t1">{{"团队人数："+(is_end?userinfo.team_down_total||0:'计算中')+''}}</text><text class="t1">{{"上交金额："+(is_end?userinfo.submission_amount||0:'计算中')+" 元"}}</text><text class="t1">{{"差价："+(is_end?userinfo.differential||0:'计算中')+" 元"}}</text><text class="t1">{{"劳务推广："+(is_end?userinfo.labor||0:'计算中')+" 元"}}</text></view></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入昵称/ID/手机号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><block wx:if="{{$root.g0}}"><view class="content"><view class="label"><text class="t1">分销对账单</text></view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item"><view data-event-opts="{{[['tap',[['toteam',['$0'],[[['datalist','',index,'id']]]]]]]}}" class="f1" bindtap="__e"><image src="{{item.headimg}}"></image><view class="t2"><text class="x1">{{item.nickname+"(ID:"+item.id+")"}}</text><text class="x2">{{"等级："+item.level_name}}</text><block wx:if="{{item.tel}}"><text class="x2">{{"手机号："+item.tel}}</text></block></view></view><view class="f2"><view class="f2"><text class="t4" style="font-size:23rpx !important;">{{"订单量："+item.order_num+''}}</text><text class="t4" style="font-size:23rpx !important;">{{"销售金额："+item.teamyeji+''}}</text><text class="t4" style="font-size:23rpx !important;">{{"团队人数："+item.team_down_total+''}}</text><text class="t4" style="font-size:23rpx !important;">{{"上交金额："+item.submission_amount+''}}</text><text class="t4" style="font-size:23rpx !important;">{{"差价："+item.differential+''}}</text><block wx:if="{{item.labor!=null}}"><text class="t4" style="font-size:23rpx !important;">{{"劳务推广："+item.labor+''}}</text></block></view></view></view></block></block></view></block><block wx:else><view class="content"><view class="label"><text class="t1">分销对账单</text></view><block><view class="item"><view class="live-box empty"></view></view></block></view></block></block></block><block wx:if="{{nodata}}"><nodata vue-id="7f130f9f-1" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="7f130f9f-2" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="7f130f9f-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="7f130f9f-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="7f130f9f-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>