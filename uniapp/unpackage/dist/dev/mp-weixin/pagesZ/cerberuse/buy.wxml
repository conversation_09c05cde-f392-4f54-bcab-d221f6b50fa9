<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['topay',['$event']]]]]}}" bindsubmit="__e"><view class="address-add"><view class="linkitem"><text class="f1">联 系 人：</text><input class="input" type="text" placeholder="请输入您的姓名" placeholder-style="color:#757575;font-size:28rpx;" data-event-opts="{{[['input',[['inputLinkman',['$event']]]]]}}" value="{{linkman}}" bindinput="__e"/></view><view class="linkitem"><text class="f1">联系电话：</text><input class="input" type="text" placeholder="请输入您的手机号" placeholder-style="color:#757575;font-size:28rpx;" data-event-opts="{{[['input',[['inputTel',['$event']]]]]}}" value="{{tel}}" bindinput="__e"/></view></view><view class="buydata"><view class="bcontent"><view class="btitle">产品信息</view><view class="product"><view class="item flex"><view class="img" data-url="{{'product?id='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{product.pic}}"></image></view><view class="info flex1"><view class="f1">{{product.title}}</view><view class="f3"><text style="font-weight:bold;">{{"￥"+product.price}}</text></view></view></view></view><view class="price"><text class="f1">价格</text><text class="f2">{{"¥"+product.price+'/小时'}}</text></view></view><view class="bcontent2"><block wx:if="{{userinfo.leveldk_money>0}}"><view class="price"><text class="f1">{{$root.m0+"折扣("+userinfo.discount+"折)"}}</text><text class="f2">{{"-¥"+userinfo.leveldk_money}}</text></view></block><view class="price"><view class="f1">{{$root.m1}}</view><block wx:if="{{couponCount>0}}"><view data-event-opts="{{[['tap',[['showCouponList',['$event']]]]]}}" class="f2" bindtap="__e"><text style="{{'color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx;'+('background:'+($root.m2)+';')}}">{{couponrid!=0?couponList[couponkey].couponname:couponCount+'张可用'}}</text><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></block><block wx:else><text class="f2" style="color:#999;">{{"无可用"+$root.m3}}</text></block></view><block wx:if="{{opt.type==1}}"><view class="price"><view class="f1" style="flex:1;">预约开始时间</view><view class="choose f2"><view data-event-opts="{{[['tap',[['toStartYuyue',['$event']]]]]}}" class="choosetime" bindtap="__e"><block wx:if="{{start_time!=''}}"><text>{{start_time}}</text></block><block wx:else><text>请选预约开始时间</text></block></view></view></view></block><block wx:if="{{opt.type==1}}"><view class="price"><view class="f1" style="flex:1;">预约结束时间</view><view class="choose f2"><view class="choosetime"><block wx:if="{{end_time}}"><text>{{end_time}}</text></block></view></view></view></block><view class="buynum flex flex-y-center"><view class="flex1">购买时长(小时)：</view><view class="addnum"><view data-event-opts="{{[['tap',[['gwcminus',['$event']]]]]}}" class="minus" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/cart-minus.png'}}"></image></view><input class="input" type="number" data-event-opts="{{[['input',[['gwcinput',['$event']]]]]}}" value="{{gwcnum}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['gwcplus',['$event']]]]]}}" class="plus" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/cart-plus.png'}}"></image></view></view></view><block wx:if="{{opt.type==2}}"><view class="price"><view class="f1" style="flex:1;">结束时间</view><view class="choose f2"><view class="choosetime"><block wx:if="{{end_time}}"><text>{{end_time}}</text></block></view></view></view></block></view></view><view style="width:100%;height:182rpx;"></view><view class="footer flex"><view class="text1 flex1">总计：<text style="font-weight:bold;font-size:36rpx;">{{"￥"+totalprice}}</text></view><block wx:if="{{issubmit}}"><button class="op" style="background:#999;">确认提交</button></block><block wx:else><button class="op" style="{{'background:'+('linear-gradient(-90deg,'+$root.m4+' 0%,rgba('+$root.m5+',0.8) 100%)')+';'}}" form-type="submit">确认提交</button></block></view></form><block wx:if="{{couponvisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">{{"请选择"+$root.m6}}</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><couponlist vue-id="30bf0852-1" couponlist="{{couponList}}" choosecoupon="{{true}}" selectedrid="{{couponrid}}" data-event-opts="{{[['^chooseCoupon',[['chooseCoupon']]]]}}" bind:chooseCoupon="__e" bind:__l="__l"></couponlist></view></view></view></block><block wx:if="{{yuyuevisible}}"><view class="picker-time"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="picker-hide" bindtap="__e"></view><view class="picker-module"><picker-view class="picker-view" value="{{value}}" data-event-opts="{{[['change',[['bindChange',['$event']]]]]}}" bindchange="__e"><picker-view-column><block wx:for="{{years}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="flex-xy-center">{{item}}</view></block></picker-view-column><picker-view-column><block wx:for="{{hour}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="flex-xy-center">{{item}}</view></block></picker-view-column><picker-view-column><block wx:for="{{min}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="flex-xy-center">{{item}}</view></block></picker-view-column></picker-view><view class="picker-opt flex-xy-center"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="picker-btn" style="{{'background:'+('linear-gradient(-90deg,'+$root.m7+' 0%,rgba('+$root.m8+',0.8) 100%)')+';'}}" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['getTime',['$event']]]]]}}" class="picker-btn" style="{{'background:'+('linear-gradient(-90deg,'+$root.m9+' 0%,rgba('+$root.m10+',0.8) 100%)')+';'}}" bindtap="__e">确定</view></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="30bf0852-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="30bf0852-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="30bf0852-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>