<view class="container"><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="搜索感兴趣的商品" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><view class="dp-product-item"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" style="width:48%;margin:15rpx  1%;" data-url="{{'detail?type=1&id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image></view><view class="product-info"><view class="p1">{{item.$orig.title}}</view><view class="p2"><view class="p2-1"><text class="t1" style="{{'color:'+(item.m0)+';'}}"><text style="font-size:24rpx;">￥</text>{{item.$orig.price+''}}<text style="font-size:24rpx;">/小时</text></text></view></view></view></view></block></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="72e62b06-1" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="72e62b06-2" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="72e62b06-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="72e62b06-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="72e62b06-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>