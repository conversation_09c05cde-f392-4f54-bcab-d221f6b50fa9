<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{$root.g0>0}}"><block><view class="cartmain"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="bid"><block><view class="item"><view class="btitle"><view class="radio" style="{{(item.$orig.checked?'background:'+item.m0+';border:0':'')}}" data-index="{{index}}" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view><view class="btitle-name" data-url="{{item.$orig.bid==0?indexurl:'/pagesExt/business/index?id='+item.$orig.business.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{item.$orig.business.name}}</view><view class="flex1"></view><view class="btitle-del" data-bid="{{item.$orig.bid}}" data-event-opts="{{[['tap',[['cartdeleteb',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/del.png'}}"></image><text style="margin-left:10rpx;">删除</text></view></view><block wx:for="{{item.l0}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><view class="content" data-url="{{'/pages/shop/product?id='+item2.$orig.product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="radio" style="{{(item2.$orig.checked?'background:'+item2.m1+';border:0':'')}}" data-index="{{index}}" data-index2="{{index2}}" data-event-opts="{{[['tap',[['changeradio2',['$event']]]]]}}" catchtap="__e"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view><view class="proinfo" style="{{(item.g1==index2+1?'border:0':'')}}"><image class="img" src="{{item2.$orig.guige.pic?item2.$orig.guige.pic:item2.$orig.product.pic}}"></image><view class="detail"><view class="title"><text>{{item2.$orig.product.name}}</text></view><view class="desc"><text>{{item2.$orig.guige.name}}</text></view><block wx:if="{{item2.$orig.product.product_type==3}}"><view class="desc"><text>{{"手工费:￥"+item2.$orig.guige.hand_fee}}</text></view></block><view class="price" style="{{'color:'+(item.m2)+';'}}"><block wx:if="{{item2.m3}}"><view style="line-height:initial;font-size:26rpx;"><text style="display:block;"><text style="font-size:24rpx;">￥</text>{{item2.$orig.guige.sell_price}}</text><text style="display:block;">{{"+"+item2.$orig.guige.service_fee+item2.m4}}</text></view></block><block wx:else><view><text style="font-size:24rpx;">￥</text>{{item2.$orig.guige.sell_price+''}}</view></block></view><view class="addnum"><view class="minus" data-index="{{index}}" data-index2="{{index2}}" data-cartid="{{item2.$orig.id}}" data-num="{{item2.$orig.num}}" data-limit_start="{{item2.$orig.product.limit_start}}" data-limit_start_guige="{{item2.$orig.guige.limit_start}}" data-event-opts="{{[['tap',[['gwcminus',['$event']]]]]}}" catchtap="__e"><image class="img" src="{{pre_url+'/static/img/cart-minus.png'}}"></image></view><input class="input" type="number" data-max="{{item2.$orig.guige.store_nums}}" data-index="{{index}}" data-index2="{{index2}}" data-cartid="{{item2.$orig.id}}" data-num="{{item2.$orig.num}}" data-limit_start="{{item2.$orig.product.limit_start}}" data-limit_start_guige="{{item2.$orig.guige.limit_start}}" data-event-opts="{{[['tap',[['',['$event']]]],['blur',[['gwcinput',['$event']]]]]}}" value="{{item2.$orig.num}}" catchtap="__e" bindblur="__e"/><view class="plus" data-index="{{index}}" data-index2="{{index2}}" data-max="{{item2.$orig.guige.store_nums}}" data-num="{{item2.$orig.num}}" data-cartid="{{item2.$orig.id}}" data-limit_start="{{item2.$orig.product.limit_start}}" data-limit_start_guige="{{item2.$orig.guige.limit_start}}" data-event-opts="{{[['tap',[['gwcplus',['$event']]]]]}}" catchtap="__e"><image class="img" src="{{pre_url+'/static/img/cart-plus.png'}}"></image></view></view></view><view class="prodel" data-cartid="{{item2.$orig.id}}" data-event-opts="{{[['tap',[['cartdelete',['$event']]]]]}}" catchtap="__e"><image class="prodel-img" src="{{pre_url+'/static/img/del.png'}}"></image></view></view></view></block></view></block></block></view></block></block><block wx:if="{{$root.g2}}"><block><view class="data-empty"><image class="data-empty-img" style="width:120rpx;height:120rpx;" src="{{pre_url+'/static/img/cartnull.png'}}"></image><view class="data-empty-text" style="margin-top:20rpx;font-size:24rpx;">购物车空空如也~</view><view style="{{'width:400rpx;border:0;height:80rpx;line-height:80rpx;margin:40rpx auto;border-radius:6rpx;color:#fff;'+('background:'+('linear-gradient(90deg,'+$root.m5+' 0%,rgba('+$root.m6+',0.8) 100%)')+';')}}" data-url="{{indexurl}}" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">去选购</view></view></block></block></block></block><block wx:if="{{$root.g3>0}}"><view><view class="xihuan"><view class="xihuan-line"></view><view class="xihuan-text"><image class="img" src="{{pre_url+'/static/img/xihuan.png'}}"></image><text class="txt">为您推荐</text></view><view class="xihuan-line"></view></view><view class="prolist"><dp-product-item vue-id="50400516-1" data="{{tjdatalist}}" menuindex="{{menuindex}}" data-event-opts="{{[['^addcart',[['addcart']]]]}}" bind:addcart="__e" bind:__l="__l"></dp-product-item></view></view></block><block wx:if="{{loading}}"><loading vue-id="50400516-2" bind:__l="__l"></loading></block><block wx:if="{{$root.g4}}"><block><view style="height:auto;position:relative;"><view style="width:100%;height:110rpx;"></view><view class="{{['footer','flex',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view data-event-opts="{{[['tap',[['changeradioAll',['$event']]]]]}}" class="radio" style="{{(allchecked?'background:'+$root.m7+';border:0':'')}}" catchtap="__e"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view><view data-event-opts="{{[['tap',[['changeradioAll',['$event']]]]]}}" class="text0" catchtap="__e">{{"全选（"+selectedcount+"）"}}</view><view class="flex1"></view><view class="text1">合计：</view><block wx:if="{{showprice_dollar}}"><block><view class="text2" style="font-size:32rpx;"><text style="font-size:20rpx;">$</text>{{usdtotalprice}}</view><view class="text3"><text style="font-size:18rpx;">￥</text>{{totalprice}}</view></block></block><block wx:else><block wx:if="{{allservice_fee>0}}"><block><view class="text2"><view class="text3"><text style="font-size:18rpx;">￥</text>{{totalprice}}</view><view class="text3"><text style="font-size:18rpx;">+</text>{{allservice_fee+$root.m8}}</view></view></block></block><block wx:else><block><view class="text2"><text style="font-size:20rpx;">￥</text>{{totalprice}}</view></block></block></block><view data-event-opts="{{[['tap',[['toOrder',['$event']]]]]}}" class="op" style="{{'background:'+('linear-gradient(-90deg,'+$root.m9+' 0%,rgba('+$root.m10+',0.8) 100%)')+';'}}" bindtap="__e">去结算</view></view></view></block></block><dp-tabbar vue-id="50400516-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="50400516-4" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="50400516-5" bind:__l="__l"></wxxieyi></view>