<view class="container"><block wx:if="{{isload}}"><block><view class="search-container" style="{{(history_show?'height:100%;':'')}}"><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="搜索感兴趣的商品" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]],['focus',[['searchFocus',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e" bindfocus="__e"/><block wx:if="{{set.image_search==1}}"><view class="camera" style="{{('background-image:url('+pre_url+'/static/img/camera.png)')}}" data-url="{{'/pagesExt/shop/imgsearch?bid='+bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></view></block></view><view data-event-opts="{{[['tap',[['searchbtn',['$event']]]]]}}" class="search-btn" bindtap="__e"><block wx:if="{{!history_show&&productlisttype=='itemlist'}}"><image style="height:36rpx;width:36rpx;" src="{{pre_url+'/static/img/show-cascades.png'}}"></image></block><block wx:if="{{!history_show&&productlisttype=='item2'}}"><image style="height:36rpx;width:36rpx;" src="{{pre_url+'/static/img/show-list.png'}}"></image></block><block wx:if="{{history_show}}"><text>搜索</text></block></view></view><view hidden="{{!(history_show)}}" class="search-history"><view><text class="search-history-title">最近搜索</text><view data-event-opts="{{[['tap',[['deleteSearchHistory',['$event']]]]]}}" class="delete-search-history" bindtap="__e"><image style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/del.png'}}"></image></view></view><view class="search-history-list"><block wx:for="{{history_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="search-history-item" data-value="{{item}}" data-event-opts="{{[['tap',[['historyClick',['$event']]]]]}}" bindtap="__e">{{item+''}}</view></block><block wx:if="{{$root.g0}}"><view class="flex-y-center"><image style="width:36rpx;height:36rpx;margin-right:10rpx;" src="{{pre_url+'/static/img/tanhao.png'}}"></image>暂无记录</view></block></view><block wx:if="{{group_multi_select==1}}"><view style="margin-top:35rpx;"><view><text class="search-history-title">商品分组</text></view><view class="search-history-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="search-history-item tjgroup" style="{{(catchegid==item.$orig.id?'color:'+item.m0+';background:rgba('+item.m1+',0.1)':'')}}" data-gid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['groupClickSearch',['$event']]]]]}}" catchtap="__e">{{item.$orig.name}}</view></block></block></view></view></block><view class="retract-box"><view class="horizontal-line _div"></view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="retract" bindtap="__e">收起<text class="iconfont iconshangla" style="color:#888;margin-left:10rpx;"></text></view><view class="horizontal-line _div"></view></view></view><view hidden="{{!(!history_show)}}" class="search-navbar"><view class="search-navbar-item" style="{{(!field||field=='sort'?'color:'+$root.m2:'')}}" data-field="sort" data-order="desc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">综合</view><block wx:if="{{$root.g1}}"><view class="search-navbar-item" style="{{(field=='sales'?'color:'+$root.m3:'')}}" data-field="sales" data-order="desc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">销量</view></block><block wx:if="{{$root.g2}}"><view class="search-navbar-item" style="{{(field=='stock'?'color:'+$root.m4:'')}}" data-field="stock" data-order="desc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">现货</view></block><view class="search-navbar-item" data-field="sell_price" data-order="{{order=='asc'?'desc':'asc'}}" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e"><text style="{{(field=='sell_price'?'color:'+$root.m5:'')}}">价格</text><text class="iconfont iconshangla" style="{{(field=='sell_price'&&order=='asc'?'color:'+$root.m6:'')}}"></text><text class="iconfont icondaoxu" style="{{(field=='sell_price'&&order=='desc'?'color:'+$root.m7:'')}}"></text></view><view data-event-opts="{{[['tap',[['showDrawer',['showRight']]]]]}}" class="search-navbar-item flex-x-center flex-y-center" catchtap="__e">筛选<text class="{{['iconfont iconshaixuan '+(showfilter?'active':'')]}}"></text></view></view><uni-drawer class="vue-ref" vue-id="e50c44e4-1" mode="right" width="{{280}}" data-ref="showRight" data-event-opts="{{[['^change',[['change',['$event','showRight']]]]]}}" bind:change="__e" bind:__l="__l" vue-slots="{{['default']}}"><scroll-view class="filter-scroll-view filter-page" scroll-y="true"><view class="filter-scroll-view-box"><view class="search-filter" style="padding-bottom:150rpx;"><view class="filter-title">筛选</view><view class="filter-content-title">商品分组</view><block wx:if="{{group_multi_select==1}}"><view class="search-filter-content"><view class="filter-item" style="{{($root.g3==0?'color:'+$root.m8+';background:rgba('+$root.m9+',0.1)':'')}}" data-gid data-event-opts="{{[['tap',[['groupClick',['$event']]]]]}}" catchtap="__e">全部</view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="filter-item" style="{{(item.g4!=-1?'color:'+item.m10+';background:rgba('+item.m11+',0.1)':'')}}" data-gid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['groupClick',['$event']]]]]}}" catchtap="__e">{{item.$orig.name}}</view></block></block></view></block><block wx:else><view class="search-filter-content"><view class="filter-item" style="{{(catchegid==''?'color:'+$root.m12+';background:rgba('+$root.m13+',0.1)':'')}}" data-gid data-event-opts="{{[['tap',[['groupClick',['$event']]]]]}}" catchtap="__e">全部</view><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="filter-item" style="{{(catchegid==item.$orig.id?'color:'+item.m14+';background:rgba('+item.m15+',0.1)':'')}}" data-gid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['groupClick',['$event']]]]]}}" catchtap="__e">{{item.$orig.name}}</view></block></block></view></block><block wx:if="{{!bid||bid<=0}}"><block><view class="filter-content-title">商品分类</view><view class="search-filter-content"><view class="filter-item" style="{{(catchecid==oldcid?'color:'+$root.m16+';background:rgba('+$root.m17+',0.1)':'')}}" data-cid="{{oldcid}}" data-event-opts="{{[['tap',[['cateClick',['$event']]]]]}}" catchtap="__e">全部</view><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="filter-item" style="{{(catchecid==item.$orig.id?'color:'+item.m18+';background:rgba('+item.m19+',0.1)':'')}}" data-cid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['cateClick',['$event']]]]]}}" catchtap="__e">{{item.$orig.name}}</view></block></block></view></block></block><block wx:else><block><view class="filter-content-title">商品分类</view><view class="search-filter-content"><view class="filter-item" style="{{(catchecid2==oldcid2?'color:'+$root.m20+';background:rgba('+$root.m21+',0.1)':'')}}" data-cid2="{{oldcid2}}" data-event-opts="{{[['tap',[['cate2Click',['$event']]]]]}}" catchtap="__e">全部</view><block wx:for="{{$root.l4}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="filter-item" style="{{(catchecid2==item.$orig.id?'color:'+item.m22+';background:rgba('+item.m23+',0.1)':'')}}" data-cid2="{{item.$orig.id}}" data-event-opts="{{[['tap',[['cate2Click',['$event']]]]]}}" catchtap="__e">{{item.$orig.name}}</view></block></block></view></block></block><view class="search-filter-btn"><view data-event-opts="{{[['tap',[['filterReset',['$event']]]]]}}" class="btn" bindtap="__e">重置</view><view data-event-opts="{{[['tap',[['filterConfirm',['$event']]]]]}}" class="btn2" style="{{'background:'+($root.m24)+';'}}" bindtap="__e">确定</view></view></view></view></scroll-view></uni-drawer></view><view class="product-container"><block wx:if="{{$root.g5}}"><block><block wx:if="{{productlisttype=='item2'}}"><dp-product-item vue-id="e50c44e4-2" data="{{datalist}}" menuindex="{{menuindex}}" showsales="{{hide_sales}}" showstock="{{hide_stock}}" bind:__l="__l"></dp-product-item></block><block wx:if="{{productlisttype=='itemlist'}}"><dp-product-itemlist vue-id="e50c44e4-3" data="{{datalist}}" menuindex="{{menuindex}}" showsales="{{hide_sales}}" showstock="{{hide_stock}}" bind:__l="__l"></dp-product-itemlist></block></block></block><block wx:if="{{nomore}}"><nomore vue-id="e50c44e4-4" text="没有更多商品了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="e50c44e4-5" text="没有查找到相关商品" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="e50c44e4-6" bind:__l="__l"></loading></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="e50c44e4-7" bind:__l="__l"></loading></block><dp-tabbar vue-id="e50c44e4-8" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="e50c44e4-9" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="e50c44e4-10" bind:__l="__l"></wxxieyi></view>