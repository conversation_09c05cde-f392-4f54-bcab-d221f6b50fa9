<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{logintype>0}}"><view style="width:100%;height:100%;"><view class="bg_div1" style="{{(loginset_data.bgtype==1?'background:'+loginset_data.bgcolor:'background:url('+loginset_data.bgimg+') no-repeat center;background-size:100% 100%')}}"><view style="overflow:hidden;"><block wx:if="{{loginset_type==1}}"><view style="width:700rpx;margin:0 auto;"><view class="title1" style="{{('color:'+loginset_data.titlecolor+';text-align: '+loginset_data.titletype)}}">{{''+loginset_data.title+''}}</view><block wx:if="{{loginset_data.subhead}}"><view class="subhead1" style="{{('color:'+loginset_data.subheadcolor+';text-align: '+loginset_data.titletype)}}">{{''+loginset_data.subhead+''}}</view></block></view></block><block wx:if="{{loginset_type==2}}"><view class="logo2"><image style="width:100%;height:100%;" src="{{loginset_data.logo}}" class="_img"></image></view></block><view class="content_div1"><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="card_div1" style="{{('background:'+loginset_data.cardcolor)}}"><block wx:if="{{loginset_type==2}}"><view><view class="title1" style="{{('color:'+loginset_data.titlecolor+';text-align:'+loginset_data.titletype+';margin-top: 20rpx;font-size: 40rpx;')}}">{{''+loginset_data.title+''}}</view><block wx:if="{{loginset_data.subhead}}"><view class="subhead1" style="{{('color:'+loginset_data.subheadcolor+';text-align:'+loginset_data.titletype)}}">{{''+loginset_data.subhead+''}}</view></block></view></block><block wx:if="{{logintype==1||logintype==2}}"><view class="tel1"><input class="input_val" type="text" name="tel" value="" placeholder="请输入手机号码" placeholder-style="color:#CACACA;line-height: 100rpx;" data-event-opts="{{[['input',[['telinput',['$event']]]]]}}" bindinput="__e"/></view></block><block wx:if="{{logintype==1}}"><view class="tel1"><input class="input_val" type="text" name="pwd" value="" password="{{true}}" placeholder="请输入密码" placeholder-style="color:#CACACA;line-height: 100rpx;"/></view></block><block wx:if="{{logintype==2}}"><view class="tel1"><input class="inputcode" type="text" name="smscode" value="" placeholder="请输入验证码" placeholder-style="color:#CACACA;line-height: 100rpx;"/><text data-event-opts="{{[['tap',[['smscode',['$event']]]]]}}" class="code1" style="{{('color:'+loginset_data.codecolor)}}" bindtap="__e">{{smsdjs||'获取验证码'}}</text></view></block><block wx:if="{{logintype_2&&logintype==2&&reg_invite_code&&!parent}}"><view class="tel1"><input class="input_val" type="text" name="yqcode" placeholder="{{'请输入邀请人'+reg_invite_code_text}}" placeholder-style="color:#CACACA;line-height: 100rpx;" data-event-opts="{{[['input',[['yqinput',['$event']]]]]}}" value="{{yqcode}}" bindinput="__e"/></view></block><block wx:if="{{reg_invite_code&&parent&&reg_invite_code_show==1&&logintype!=6}}"><view class="tel1" style="display:flex;padding-top:8rpx;align-items:center;"><view style="white-space:nowrap;">邀请人：</view><image style="width:80rpx;height:80rpx;border-radius:50%;margin-right:20rpx;" src="{{parent.headimg}}"></image><view class="overflow_ellipsis">{{parent.nickname+''}}</view></view></block><block wx:if="{{(logintype==1||logintype==2||logintype==3)&&xystatus==1}}"><view class="xycss1"><checkbox-group data-event-opts="{{[['change',[['isagreeChange',['$event']]]]]}}" style="display:inline-block;position:relative;" bindchange="__e"><checkbox style="transform:scale(0.6);" value="1" checked="{{isagree}}"></checkbox><text style="{{('color:'+loginset_data.xytipcolor)}}">{{loginset_data.xytipword}}</text><block wx:if="{{xyagree_type==1&&!reading_completed}}"><view data-event-opts="{{[['tap',[['promptRead',['$event']]]]]}}" style="height:60rpx;width:60rpx;position:absolute;top:0;" bindtap="__e"></view></block></checkbox-group><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" style="{{('color:'+loginset_data.xycolor)}}" bindtap="__e">{{xyname}}</text><block wx:if="{{xyname2}}"><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" style="{{('color:'+loginset_data.xytipcolor)}}" bindtap="__e">和</text></block><block wx:if="{{xyname2}}"><text data-event-opts="{{[['tap',[['showxieyiFun2',['$event']]]]]}}" style="{{(loginset_data.xycolor?'color:'+loginset_data.xycolor:'color:'+$root.m0)}}" bindtap="__e">{{xyname2}}</text></block></view></block><block wx:if="{{logintype==1||logintype==2||logintype==3}}"><view style="margin-top:40rpx;"><block wx:if="{{loginset_data.btntype==1}}"><block><block wx:if="{{logintype==3}}"><button class="btn1" style="{{('background:rgba('+$root.m1+');color: '+loginset_data.btnwordcolor)}}" data-type="{{alih5?1:0}}" data-event-opts="{{[['tap',[['authlogin',['$event']]]]]}}" bindtap="__e"><block wx:if="{{!alih5}}"><block>授权登录</block></block><block wx:else><block>授权登录</block></block></button></block><block wx:else><button class="btn1" style="{{('background:rgba('+$root.m2+');color: '+loginset_data.btnwordcolor)}}" form-type="submit">登录</button></block></block></block><block wx:if="{{loginset_data.btntype==2}}"><block><block wx:if="{{logintype==3}}"><button class="btn1" style="{{('background-color:'+loginset_data.btncolor+';color:'+loginset_data.btnwordcolor)}}" data-type="{{alih5?1:0}}" data-event-opts="{{[['tap',[['authlogin',['$event']]]]]}}" bindtap="__e"><block wx:if="{{!alih5}}"><block>授权登录</block></block><block wx:else><block>授权登录</block></block></button></block><block wx:else><button class="btn1" style="{{('background-color:'+loginset_data.btncolor+';color:'+loginset_data.btnwordcolor)}}" form-type="submit">登录</button></block></block></block><block wx:if="{{!login_mast}}"><button data-event-opts="{{[['tap',[['noLogin',['$event']]]]]}}" class="btn1" style="font-size:28rpx;height:50rpx;line-height:50rpx;" bindtap="__e">暂不登录</button></block><block wx:if="{{platform2=='ios'&&logintype_4==true}}"><button data-event-opts="{{[['tap',[['ioslogin',['$event']]]]]}}" class="ioslogin-btn" style="width:100%;" bindtap="__e"><image src="{{pre_url+'/static/img/apple.png'}}"></image>通过Apple登录</button></block><block wx:if="{{logintype==1}}"><view style="line-height:50rpx;float:24rpx;overflow:hidden;"><text style="{{('color: '+loginset_data.regpwdbtncolor+';float:left')}}" data-url="{{'reg?frompage='+opt.frompage}}" data-opentype="redirect" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">注册账号</text><block wx:if="{{needsms}}"><text style="{{('color: '+loginset_data.regpwdbtncolor+';float:right')}}" data-url="getpwd" data-opentype="redirect" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">忘记密码</text></block></view></block></view></block><block wx:if="{{logintype==4}}"><block><block wx:if="{{loginset_data.btntype==1}}"><block><button class="btn1" style="{{('background:rgba('+$root.m3+');color: '+loginset_data.btnwordcolor)}}" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">授权绑定手机号</button></block></block><block wx:if="{{loginset_data.btntype==2}}"><block><button class="btn1" style="{{('background-color:'+loginset_data.btncolor+';color:'+loginset_data.btnwordcolor)}}" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">授权绑定手机号</button></block></block><block wx:if="{{login_bind==1}}"><button data-event-opts="{{[['tap',[['nobindregister',['$event']]]]]}}" class="btn1" style="background-color:#EEEEEE;font-size:28rpx;" bindtap="__e">暂不绑定</button></block></block></block><block wx:if="{{logintype==5}}"><block><form data-event-opts="{{[['submit',[['setnicknameregister',['$event']]]]]}}" bindsubmit="__e"><view style="font-size:30rpx;font-weight:bold;line-height:68rpx;">请设置头像昵称</view><view class="loginform" style="padding:0;"><view class="form-item" style="height:120rpx;line-height:120rpx;"><view class="flex1">头像</view><button style="width:100rpx;height:100rpx;" open-type="chooseAvatar" hover-class="none" data-event-opts="{{[['chooseavatar',[['onChooseAvatar',['$event']]]]]}}" bindchooseavatar="__e"><image style="width:100%;height:100%;border-radius:50%;" src="{{headimg||default_headimg}}"></image></button></view><view class="form-item" style="height:120rpx;line-height:120rpx;"><view class="flex1">昵称</view><input class="input" style="text-align:right;" type="nickname" placeholder="请输入昵称" name="nickname" placeholder-style="font-size:30rpx;color:#B2B5BE"/></view><block wx:if="{{loginset_data.btntype==1}}"><block><button class="btn1" style="{{('background:rgba('+$root.m4+');color: '+loginset_data.btnwordcolor)}}" form-type="submit">确定</button></block></block><block wx:if="{{loginset_data.btntype==2}}"><block><button class="btn1" style="{{('background-color:'+loginset_data.btncolor+';color:'+loginset_data.btnwordcolor)}}" form-type="submit">确定</button></block></block><block wx:if="{{login_setnickname==1}}"><button data-event-opts="{{[['tap',[['nosetnicknameregister',['$event']]]]]}}" class="btn1" style="background-color:#EEEEEE;font-size:28rpx;" bindtap="__e">暂不设置</button></block></view></form></block></block><block wx:if="{{logintype==6}}"><block><form data-event-opts="{{[['submit',[['setRegisterInvite',['$event']]]]]}}" bindsubmit="__e"><block wx:if="{{reg_invite_code&&(parent&&reg_invite_code_show==1||!parent)}}"><view style="font-size:30rpx;font-weight:bold;line-height:68rpx;">请填写邀请码</view></block><view class="loginform" style="padding:0;"><block wx:if="{{reg_invite_code&&!parent}}"><view class="form-item"><input class="input" type="text" name="yqcode" placeholder="{{'请输入邀请人'+reg_invite_code_text}}" placeholder-style="font-size:30rpx;color:#B2B5BE" data-event-opts="{{[['input',[['yqinput',['$event']]]]]}}" value="{{yqcode}}" bindinput="__e"/></view></block><block wx:if="{{reg_invite_code&&parent&&reg_invite_code_show==1}}"><view class="form-item" style="display:flex;padding-top:8rpx;"><view style="white-space:nowrap;">邀请人：</view><image style="width:80rpx;height:80rpx;border-radius:50%;" src="{{parent.headimg}}"></image><view class="overflow_ellipsis">{{parent.nickname+''}}</view></view></block><block wx:if="{{loginset_data.btntype==1}}"><block><button class="btn1" style="{{('background:rgba('+$root.m5+');color: '+loginset_data.btnwordcolor)}}" form-type="submit">确定</button></block></block><block wx:if="{{loginset_data.btntype==2}}"><block><button class="btn1" style="{{('background-color:'+loginset_data.btncolor+';color:'+loginset_data.btnwordcolor)}}" form-type="submit">确定</button></block></block><block wx:if="{{reg_invite_code==1}}"><button data-event-opts="{{[['tap',[['setRegisterInvitePass',['$event']]]]]}}" class="btn1" style="background-color:#EEEEEE;font-size:28rpx;" bindtap="__e">暂不设置</button></block></view></form></block></block><block wx:if="{{logintype==1||logintype==2}}"><block><block wx:if="{{logintype==1&&(logintype_2||logintype_3)||logintype==2&&(logintype_1||logintype_3)}}"><view class="other_login"><view style="display:flex;width:100%;"><view class="other_line"></view><view style="margin:0 20rpx;color:#888888;white-space:nowrap;">其他登录方式</view><view class="other_line"></view></view><view class="other_content"><block wx:if="{{logintype_3&&platform=='wx'}}"><button style="width:50%;margin:0 auto;" hover-class="none" data-event-opts="{{[['tap',[['weixinlogin',['$event']]]]]}}" bindtap="__e"><view style="width:100rpx;height:104rpx;margin:0 auto;"><image style="width:100rpx;height:100rpx;" src="{{wxformimgurl?wxformimgurl:pre_url+'/static/img/login-'+platformimg+'.png'}}" class="_img"></image></view><view style="font-size:24rpx;line-height:40rpx;color:#888888;">{{wxtext||'授权登录'}}</view></button></block><block wx:if="{{logintype_3&&platform!='wx'}}"><button style="width:50%;margin:0 auto;" hover-class="none" data-event-opts="{{[['tap',[['weixinlogin',['$event']]]]]}}" bindtap="__e"><view style="width:100rpx;height:104rpx;margin:0 auto;"><image style="width:100rpx;height:100rpx;" src="{{wxformimgurl?wxformimgurl:pre_url+'/static/img/login-'+platformimg+'.png'}}" class="_img"></image></view><view style="font-size:24rpx;line-height:40rpx;color:#888888;">{{wxtext||platformname+'登录'}}</view></button></block><block wx:if="{{logintype_7&&alih5}}"><button style="width:50%;margin:0 auto;" hover-class="none" data-event-opts="{{[['tap',[['alih5login',['$event']]]]]}}" bindtap="__e"><view style="width:100rpx;height:104rpx;margin:0 auto;"><image style="width:100rpx;height:100rpx;" src="{{pre_url+'/static/img/login-alipay.png'}}" class="_img"></image></view><view style="font-size:24rpx;line-height:40rpx;color:#888888;">支付宝登录</view></button></block><block wx:if="{{logintype==1&&logintype_2}}"><view style="width:50%;margin:0 auto;" data-type="2" data-event-opts="{{[['tap',[['changelogintype',['$event']]]]]}}" bindtap="__e"><view style="width:100rpx;height:104rpx;margin:0 auto;"><image style="width:100rpx;height:100rpx;" src="{{pre_url+'/static/img/reg-tellogin.png'}}" class="_img"></image></view><view style="font-size:24rpx;color:#888888;line-height:40rpx;">手机号快捷登录</view></view></block><block wx:if="{{logintype==2&&logintype_1}}"><view style="width:50%;margin:0 auto;" data-type="1" data-event-opts="{{[['tap',[['changelogintype',['$event']]]]]}}" bindtap="__e"><view style="width:100rpx;height:104rpx;margin:0 auto;"><image style="width:100rpx;height:100rpx;" src="{{pre_url+'/static/img/reg-tellogin.png'}}" class="_img"></image></view><view style="font-size:24rpx;color:#888888;line-height:40rpx;">密码登录</view></view></block></view></view></block></block></block></view></form></view></view></view></view></block><block wx:if="{{showxieyi}}"><view class="xieyibox"><view class="xieyibox-content"><scroll-view class="myElement2" style="height:100%;" scroll-y="{{true}}" data-event-opts="{{[['scrolltolower',[['xieyiTolower',['$event']]]]]}}" bindscrolltolower="__e"><parse vue-id="24611f63-1" content="{{xycontent}}" bind:__l="__l"></parse><view class="myElement" style="width:100%;height:1px;"></view></scroll-view><view class="xieyibut-view flex-y-center"><view data-event-opts="{{[['tap',[['closeXieyi',['$event']]]]]}}" class="but-class" style="background:#A9A9A9;" bindtap="__e">不同意并退出</view><view data-event-opts="{{[['tap',[['hidexieyi',['$event']]]]]}}" class="but-class" style="{{'background:'+(reading_completed?'linear-gradient(90deg,'+$root.m6+' 0%,rgba('+$root.m7+',0.8) 100%)':'#A9A9A9')+';'}}" bindtap="__e">已阅读并同意</view></view></view></view></block><block wx:if="{{showxieyi2}}"><view class="xieyibox"><view class="xieyibox-content"><view style="overflow:scroll;height:100%;"><parse vue-id="24611f63-2" content="{{xycontent2}}" bind:__l="__l"></parse></view><view class="xieyibut-view flex-y-center"><view data-event-opts="{{[['tap',[['closeXieyi',['$event']]]]]}}" class="but-class" style="background:#A9A9A9;" bindtap="__e">不同意并退出</view><view data-event-opts="{{[['tap',[['hidexieyi2',['$event']]]]]}}" class="but-class" style="{{'background:'+('linear-gradient(90deg,'+$root.m8+' 0%,rgba('+$root.m9+',0.8) 100%)')+';'}}" bindtap="__e">已阅读并同意</view></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="24611f63-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="24611f63-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="24611f63-5" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="24611f63-6" bind:__l="__l"></wxxieyi></view>