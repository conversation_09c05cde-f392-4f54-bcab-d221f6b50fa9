<scroll-view class="pageContainer" style="{{'background-color:'+(pageinfo.bgcolor)+';'}}" scroll-y="{{sysset.mode==3?isshowmendianmodal?false:true:true}}" data-event-opts="{{[['scroll',[['pagescroll',['$event']]]]]}}" bindscroll="__e"><block wx:if="{{!show_nearbyarea}}"><block><block wx:if="{{platform=='wx'&&homeNavigationCustom>1}}"><block><view class="navigation" style="{{'background:'+(navigationBarBackgroundColor)+';'}}"><view class="navcontent" style="{{'color:'+(navigationBarTextStyle)+';'+('margin-top:'+(navigationMenu.top+'px')+';')+('width:'+(navigationMenu.left-5+'px')+';')}}"><view class="header-location-top" style="{{'height:'+(navigationMenu.height+'px')+';'+('background:'+(navigationBarBackgroundColor)+';')}}"><block wx:if="{{homeNavigationCustom==2}}"><block><view class="topinfo"><image class="topinfoicon" src="{{sysset.logo}}"></image><view class="topinfotxt" style="{{'color:'+(navigationBarTextStyle)+';'}}">{{sysset.name}}</view></view><view class="topsearch" style="{{'width:'+(screenWidth-210+'px')+';'}}" data-url="/pages/shop/search" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/search.png'}}"></image><text style="font-size:24rpx;color:#999;">搜索感兴趣的商品</text></view></block></block><block wx:if="{{(homeNavigationCustom==3||homeNavigationCustom==5||homeNavigationCustom==7)&&show_location==1}}"><block><block wx:if="{{sysset.loc_area_type==0&&curent_address}}"><view class="{{[homeNavigationCustom>3?'header-location-weixin-fixedwidth':'header-location-weixin']}}"><uni-data-picker class="header-address header-area-picker" vue-id="8dd740cc-1" localdata="{{arealist}}" popup-title="地区" placeholder="地区" data-event-opts="{{[['^change',[['areachange']]]]}}" bind:change="__e" bind:__l="__l" vue-slots="{{['default']}}"><view>{{curent_address?curent_address:'请选择定位'}}</view></uni-data-picker><image class="header-more" src="{{pre_url+'/static/img/location/down-'+navigationBarTextStyle+'.png'}}"></image></view></block><block wx:if="{{sysset.loc_area_type==1&&curent_address}}"><view class="{{[homeNavigationCustom>3?'header-location-weixin-fixedwidth':'header-location-weixin']}}"><view data-event-opts="{{[['tap',[['showNearbyBox',['$event']]]]]}}" class="flex-y-center" bindtap="__e"><view class="header-address">{{curent_address?curent_address:'请选择定位'}}</view><image class="header-more" src="{{pre_url+'/static/img/location/down-'+navigationBarTextStyle+'.png'}}"></image></view></view></block><block wx:if="{{homeNavigationCustom==5}}"><view class="header-location-title">{{sysset.name}}</view></block><block wx:if="{{homeNavigationCustom==7}}"><view class="header-location-search" style="{{'height:'+(navigationMenu.height+'px')+';'}}" data-url="/pages/shop/search" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/search.png'}}"></image><text style="font-size:24rpx;color:#999;">搜索感兴趣的商品</text></view></block></block></block><block wx:if="{{(homeNavigationCustom==4||homeNavigationCustom==6||homeNavigationCustom==8)&&show_mendian==1}}"><block><view class="{{[homeNavigationCustom>4?'header-location-weixin-fixedwidth':'header-location-weixin']}}"><view data-event-opts="{{[['tap',[['showMendianModal',['$event']]]]]}}" class="flex-y-center header-location-f1" bindtap="__e"><view class="header-address">{{locationCache.mendian_name?locationCache.mendian_name:'请选择门店'}}</view><image class="header-more" src="{{pre_url+'/static/img/location/down-'+navigationBarTextStyle+'.png'}}"></image></view></view><block wx:if="{{homeNavigationCustom==6}}"><view class="header-location-title">{{sysset.name}}</view></block><block wx:if="{{homeNavigationCustom==8}}"><view class="header-location-search" style="{{'height:'+(navigationMenu.height+'px')+';'}}" data-url="/pages/shop/search" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/search.png'}}"></image><text style="font-size:24rpx;color:#999;">搜索感兴趣的商品</text></view></block></block></block></view></view></view><view style="{{'width:100%;'+('height:'+(44+statusBarHeight+'px')+';')}}"></view></block></block><block wx:if="{{sysset.official_account_status==1}}"><block><official-account></official-account></block></block><block wx:if="{{mendian_upgrade&&show_mendian==1}}"><view class="mendianupbg" style="{{('background:rgba('+$root.m0+',0.8);color:#FFF')}}"></view></block><block wx:if="{{mendian_upgrade&&show_mendian==1}}"><view class="mendianup"><view class="left"><block wx:if="{{mendian_change}}"><view class="f1" data-url="/pagesB/mendianup/list" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{locationCache.mendian_name+''}}<view class="t1">切换<text class="iconfont iconjiantou" style="font-size:40rpx;"></text></view></view></block><block wx:else><view class="f1">{{locationCache.mendian_name}}</view></block><block wx:if="{{mendian_show_address}}"><view class="f2" data-latitude="{{mendian.latitude}}" data-longitude="{{mendian.longitude}}" data-address="{{locationCache.mendian_address}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e"><view>{{locationCache.mendian_address}}</view></view></block><block wx:if="{{mendian_show_address&&mendian.tel}}"><view class="f2" data-phone="{{mendian.tel}}" data-event-opts="{{[['tap',[['callphone',['$event']]]]]}}" catchtap="__e"><view><text style="margin-right:10rpx;">{{mendian.tel}}</text><image src="{{pre_url+'/static/img/telwhite.png'}}"></image></view></view></block></view><view class="right"><view class="f1"><image src="{{locationCache.headimg?locationCache.headimg:locationCache.mendian_pic}}"></image></view><view class="f2">{{locationCache.mendian_name}}</view></view></view></block><block wx:if="{{mendian_upgrade&&show_mendian==1}}"><view style="height:150rpx;"></view></block><block wx:if="{{sysset.agent_card==1&&sysset.agent_card_info}}"><block><view style="height:10rpx;"></view><view class="agent-card"><view class="flex-y-center row1"><image class="logo" src="{{sysset.agent_card_info.logo}}"></image><view class="text"><view class="flex"><view class="title limitText">{{sysset.agent_card_info.shopname}}</view><view class="flex right" data-name="{{sysset.agent_card_info.shopname}}" data-address="{{sysset.agent_card_info.address}}" data-longitude="{{sysset.agent_card_info.longitude}}" data-latitude="{{sysset.agent_card_info.latitude}}" data-event-opts="{{[['tap',[['showMap',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/b_addr.png'}}"></image>{{"导航到店"+agent_juli}}</view></view><view class="limitText grey-text">{{sysset.agent_card_info.address}}</view><view class="grey-text flex-y-center"><image class="img" src="{{pre_url+'/static/img/my.png'}}"></image><view>{{sysset.agent_card_info.name}}</view><image class="img" style="margin-left:30rpx;" src="{{pre_url+'/static/img/tel.png'}}"></image><view style="position:relative;" data-url="{{'tel::'+sysset.agent_card_info.tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{sysset.agent_card_info.tel+''}}<view class="btn" data-url="{{'tel::'+sysset.agent_card_info.tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">拨打</view></view></view></view></view><view class="flex-y-center flex-x-center agent-card-b" style="{{'background:'+($root.m1)+';'}}"><view data-url="/pagesExt/agent/card" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/shop.png'}}"></image>店铺信息</view><view data-url="/pages/commission/poster" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img img2" src="{{pre_url+'/static/img/card.png'}}"></image>店铺海报</view></view></view></block></block><block wx:if="{{sysset.mode==1}}"><block><view class="header" style="{{'background:'+(navigationBarBackgroundColor)+';'}}"><view class="header_title flex-y-center flex-bt"><view class="flex-y-center" data-url="/pagesExt/business/clist2" data-event-opts="{{[['tap',[['toBusiness',['$event']]]]]}}" bindtap="__e">{{''+sysset.name+''}}<image class="header_detail" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view><view class="header_address flex"><view class="flex1">{{sysset.address}}</view><text style="margin-left:20rpx;flex-shrink:0;">{{"距离 "+sysset.juli+" km"}}</text></view></view><view class="topbannerbg" style="{{(sysset.banner_show&&business.pic?'background:url('+business.pic+') center no-repeat;background-size:cover;':'')}}"></view></block></block><block wx:if="{{sysset.showgzts}}"><block><view style="width:100%;height:88rpx;"></view><view class="follow_topbar"><view class="headimg"><image src="{{sysset.logo}}"></image></view><view class="info"><view class="i">欢迎进入<text style="{{'color:'+($root.m2)+';'}}">{{sysset.name}}</text></view><view class="i">关注公众号享更多专属服务</view></view><view data-event-opts="{{[['tap',[['showsubqrcode',['$event']]]]]}}" class="sub" style="{{'background-color:'+($root.m3)+';'}}" bindtap="__e">立即关注</view></view><uni-popup class="vue-ref" vue-id="8dd740cc-2" id="qrcodeDialog" type="dialog" data-ref="qrcodeDialog" bind:__l="__l" vue-slots="{{['default']}}"><view class="qrcodebox"><image class="img" src="{{sysset.qrcode}}" data-url="{{sysset.qrcode}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">长按识别二维码关注</view><view data-event-opts="{{[['tap',[['closesubqrcode',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="{{pre_url+'/static/img/close2.png'}}"></image></view></view></uni-popup></block></block><dp vue-id="8dd740cc-3" pagecontent="{{pagecontent}}" menuindex="{{menuindex}}" latitude="{{latitude}}" longitude="{{longitude}}" navigationHieght="{{navigationHieght}}" data-event-opts="{{[['^getdata',[['getdata']]]]}}" bind:getdata="__e" bind:__l="__l"></dp><block wx:if="{{$root.g0}}"><view class="{{[sysset.ddbb_position=='bottom'?'bobaobox_bottom':'bobaobox']}}"><swiper style="position:relative;height:54rpx;width:450rpx;" autoplay="true" interval="{{5000}}" vertical="true"><block wx:for="{{oglist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="flex-y-center" data-url="{{item.tourl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px;" src="{{item.headimg}}"></image><view style="width:400rpx;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:22rpx;"><text style="padding-right:2px;">{{item.nickname}}</text><text style="padding-right:4px;">{{item.showtime}}</text><block wx:if="{{item.type=='collage'&&item.buytype=='2'}}"><text style="padding-right:2px;">发起拼团</text></block><block wx:else><block wx:if="{{item.type=='business'}}"><text>入驻</text></block><block wx:else><block wx:if="{{item.type=='maidan'}}"><text></text></block><block wx:else><text>购买了</text></block></block></block><text>{{item.name}}</text></view></swiper-item></block></swiper></view></block><block wx:if="{{copyright!=''}}"><view class="copyright" data-url="{{copyright_link}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{copyright}}</view></block><dp-tabbar vue-id="8dd740cc-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><block wx:if="{{ggsx}}"><block><dp-guanggao vue-id="8dd740cc-5" guanggaopic="{{guanggaopic}}" guanggaourl="{{guanggaourl}}" guanggaotype="{{guanggaotype}}" param="{{guanggaoparam}}" bind:__l="__l"></dp-guanggao></block></block></block></block><popmsg class="vue-ref" vue-id="8dd740cc-6" data-ref="popmsg" bind:__l="__l"></popmsg><block wx:if="{{loading}}"><loading vue-id="8dd740cc-7" bind:__l="__l"></loading></block><block wx:if="{{show_location==1&&sysset.loc_area_type==1&&show_nearbyarea}}"><block><view style="{{'height:'+(34+statusBarHeight+'px')+';'}}"></view><view class="header-nearby-box"><view class="header-nearby-body"><view class="header-nearby-search"><view data-event-opts="{{[['tap',[['closeNearbyBox',['$event']]]]]}}" class="header-nearby-close" bindtap="__e"><image src="{{pre_url+'/static/img/location/close-dark.png'}}"></image></view><view class="header-nearby-input"><input class="input" type="text" placeholder="商圈/大厦/住宅" placeholder-style="font-size:26rpx" data-event-opts="{{[['input',[['placekeywordInput',['$event']]]],['confirm',[['searchPlace',['$event']]]]]}}" value="{{placekeyword}}" bindinput="__e" bindconfirm="__e"/><button data-event-opts="{{[['tap',[['searchPlace',['$event']]]]]}}" class="searchbtn" style="{{'border-color:'+($root.m4)+';'+('color:'+('#FFF')+';')+('background-color:'+($root.m5)+';')}}" bindtap="__e">搜索</button></view></view><block wx:if="{{$root.g1>0}}"><view class="suggestion-box"><block wx:for="{{suggestionplacelist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="suggestion-place" data-index="{{index}}" data-event-opts="{{[['tap',[['chooseSuggestionAddress',['$event']]]]]}}" bindtap="__e"><view class="flex-y-center"><image src="{{pre_url+'/static/img/address3.png'}}"></image><text class="s-title">{{item.title}}</text></view><view class="s-info flex-y-center"><text class="s-area">{{item.city+" "+item.district+''}}</text><text class="s-address">{{item.address}}</text></view></view></block></block></view></block><view class="header-nearby-content flex-bt"><view>{{"已选："+curent_address}}</view><view data-event-opts="{{[['tap',[['refreshAddress',['$event']]]]]}}" class="flex-xy-center" bindtap="__e"><image class="header-nearby-imgicon" src="{{pre_url+'/static/img/location/location-dark.png'}}"></image><text class="header-nearby-tip">重新定位</text></view></view><view class="header-nearby-content" style="margin-top:20rpx;"><view class="header-nearby-title flex-y-center"><image class="header-nearby-imgicon" src="{{pre_url+'/static/img/location/home-dark.png'}}"></image><text>我的地址</text></view><block wx:for="{{myaddresslist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><block wx:if="{{index>3?isshowalladdress?1==1:1==2:1==1}}"><view class="header-nearby-info" data-index="{{index}}" data-event-opts="{{[['tap',[['chooseMyAddress',['$event']]]]]}}" bindtap="__e"><view>{{item.address}}</view><view class="header-nearby-txt">{{item.name+" "+item.tel}}</view></view></block></block></block><view data-event-opts="{{[['tap',[['showAllAddress',['$event']]]]]}}" class="header-nearby-all flex-y-center" bindtap="__e"><block wx:if="{{$root.g2>0}}"><block><text>{{(isshowalladdress?'收起全部地址':'展开更多地址')+''}}</text><image src="{{pre_url+'/static/img/location/'+(isshowalladdress?'up-grey.png':'down-grey.png')}}"></image></block></block><block wx:else><text>-暂无地址-</text></block></view></view><view class="header-nearby-content" style="margin-top:20rpx;"><view class="header-nearby-title flex-y-center"><image class="header-nearby-imgicon" src="{{pre_url+'/static/img/location/address-dark.png'}}"></image><text>附近地址</text></view><block wx:for="{{nearbyplacelist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="header-nearby-info" data-index="{{index}}" data-event-opts="{{[['tap',[['chooseNearbyAddress',['$event']]]]]}}" bindtap="__e"><view>{{item.title}}</view><view class="header-nearby-txt">{{item.address}}</view></view></block></block></view></view><view data-event-opts="{{[['tap',[['addMyAddress',['$event']]]]]}}" class="header-location-bottom flex-xy-center" style="{{'color:'+($root.m6)+';'}}" bindtap="__e"><text class="location-add-address">+</text><text style="padding-top:10rpx;">新增收货地址</text></view></view></block></block><block wx:if="{{show_mendian}}"><block><block wx:if="{{isshowmendianmodal}}"><view class="popup__container popup_mendian" style="z-index:999999;"><view data-event-opts="{{[['tap',[['hideMendianModal',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择门店</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hideMendianModal',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="mendian-info" style="{{'background:'+(item.$orig.id==mendian.id?'rgba('+item.m7+',0.1)':'')+';'}}" data-index="{{index}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['changeMendian',['$event']]]]]}}" bindtap="__e"><view class="b1"><image src="{{item.$orig.pic}}"></image></view><view class="b2"><view class="t1">{{item.$orig.name}}</view><view class="t2 flex-y-center"><view class="mendian-distance">{{item.$orig.distance}}</view><block wx:if="{{item.$orig.address||item.$orig.area}}"><block><block wx:if="{{item.$orig.distance}}"><view class="line"></view></block><view class="mendian-address">{{''+(item.$orig.address?item.$orig.address:item.$orig.area)}}</view></block></block></view></view></view></block></block></view></view></view></block></block></block><block wx:if="{{show_indextip&&!indextipstatus}}"><block><view data-event-opts="{{[['tap',[['closeindextip',['$event']]]]]}}" style="position:fixed;right:0rpx;top:0rpx;width:100%;height:100%;background-color:#000;opacity:0.45;z-index:998;" bindtap="__e"></view><image style="position:fixed;right:20rpx;top:0rpx;width:360rpx;height:424rpx;z-index:999;" src="{{pre_url+'/static/img/indextip.png'}}" data-event-opts="{{[['tap',[['closeindextip',['$event']]]]]}}" bindtap="__e"></image></block></block><wxxieyi vue-id="8dd740cc-8" bind:__l="__l"></wxxieyi></scroll-view>