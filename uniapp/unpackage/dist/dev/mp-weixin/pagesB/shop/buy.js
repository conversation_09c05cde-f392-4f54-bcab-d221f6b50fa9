(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pagesB/shop/buy"],{

/***/ 4828:
/*!***************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/main.js?{"page":"pagesB%2Fshop%2Fbuy"} ***!
  \***************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _buy = _interopRequireDefault(__webpack_require__(/*! ./pagesB/shop/buy.vue */ 4829));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_buy.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 4829:
/*!********************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/pagesB/shop/buy.vue ***!
  \********************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _buy_vue_vue_type_template_id_036609c0___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./buy.vue?vue&type=template&id=036609c0& */ 4830);
/* harmony import */ var _buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./buy.vue?vue&type=script&lang=js& */ 4832);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _buy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./buy.vue?vue&type=style&index=0&lang=css& */ 4834);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 36);

var renderjs





/* normalize component */

var component = Object(_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _buy_vue_vue_type_template_id_036609c0___WEBPACK_IMPORTED_MODULE_0__["render"],
  _buy_vue_vue_type_template_id_036609c0___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _buy_vue_vue_type_template_id_036609c0___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pagesB/shop/buy.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 4830:
/*!***************************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/pagesB/shop/buy.vue?vue&type=template&id=036609c0& ***!
  \***************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_template_id_036609c0___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=template&id=036609c0& */ 4831);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_template_id_036609c0___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_template_id_036609c0___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_template_id_036609c0___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_template_id_036609c0___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 4831:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!/Users/<USER>/Downloads/tcphp/uniapp/pagesB/shop/buy.vue?vue&type=template&id=036609c0& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    couponlist: function () {
      return __webpack_require__.e(/*! import() | components/couponlist/couponlist */ "components/couponlist/couponlist").then(__webpack_require__.bind(null, /*! @/components/couponlist/couponlist.vue */ 7167))
    },
    uniDataPicker: function () {
      return Promise.all(/*! import() | components/uni-data-picker/uni-data-picker */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/uni-data-picker/uni-data-picker")]).then(__webpack_require__.bind(null, /*! @/components/uni-data-picker/uni-data-picker.vue */ 7079))
    },
    uniPopup: function () {
      return Promise.all(/*! import() | components/uni-popup/uni-popup */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/uni-popup/uni-popup")]).then(__webpack_require__.bind(null, /*! @/components/uni-popup/uni-popup.vue */ 7096))
    },
    dpMap: function () {
      return __webpack_require__.e(/*! import() | components/dp-map/dp-map */ "components/dp-map/dp-map").then(__webpack_require__.bind(null, /*! @/components/dp-map/dp-map.vue */ 7427))
    },
    parse: function () {
      return Promise.all(/*! import() | components/parse/parse */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/parse/parse")]).then(__webpack_require__.bind(null, /*! @/components/parse/parse.vue */ 7152))
    },
    nodata: function () {
      return __webpack_require__.e(/*! import() | components/nodata/nodata */ "components/nodata/nodata").then(__webpack_require__.bind(null, /*! @/components/nodata/nodata.vue */ 7233))
    },
    loading: function () {
      return __webpack_require__.e(/*! import() | components/loading/loading */ "components/loading/loading").then(__webpack_require__.bind(null, /*! @/components/loading/loading.vue */ 7131))
    },
    dpTabbar: function () {
      return __webpack_require__.e(/*! import() | components/dp-tabbar/dp-tabbar */ "components/dp-tabbar/dp-tabbar").then(__webpack_require__.bind(null, /*! @/components/dp-tabbar/dp-tabbar.vue */ 7110))
    },
    popmsg: function () {
      return __webpack_require__.e(/*! import() | components/popmsg/popmsg */ "components/popmsg/popmsg").then(__webpack_require__.bind(null, /*! @/components/popmsg/popmsg.vue */ 7124))
    },
    wxxieyi: function () {
      return __webpack_require__.e(/*! import() | components/wxxieyi/wxxieyi */ "components/wxxieyi/wxxieyi").then(__webpack_require__.bind(null, /*! @/components/wxxieyi/wxxieyi.vue */ 7138))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l9 = _vm.isload
    ? _vm.__map(_vm.allbuydata, function (buydata, groupBid) {
        var $orig = _vm.__get_orig(buydata)
        var l1 = _vm.__map(buydata.prodata, function (item, index2) {
          var $orig = _vm.__get_orig(item)
          var m0 = !_vm.order_change_price
            ? !_vm.isNull(item.guige.service_fee) && item.guige.service_fee > 0
            : null
          var m1 = !_vm.order_change_price && m0 ? _vm.t("服务费") : null
          var g0 =
            item.product.weighttype > 0 && item.product.weightlist.length > 0
          var m2 = g0 ? _vm.t("包装费") : null
          var l0 = g0
            ? _vm.__map(item.product.weightlist, function (item1, idx3) {
                var $orig = _vm.__get_orig(item1)
                var m3 = item.product.weightkey == idx3 ? _vm.t("color1") : null
                var m4 =
                  item.product.weightkey == idx3 ? _vm.t("color1rgb") : null
                return {
                  $orig: $orig,
                  m3: m3,
                  m4: m4,
                }
              })
            : null
          var m5 = item.product.product_type == 1 ? _vm.t("color1rgb") : null
          return {
            $orig: $orig,
            m0: m0,
            m1: m1,
            g0: g0,
            m2: m2,
            l0: l0,
            m5: m5,
          }
        })
        var l2 = !_vm.usegiveorder
          ? _vm.__map(buydata.freightList, function (item, idx2) {
              var $orig = _vm.__get_orig(item)
              var m6 = buydata.freightkey == idx2 ? _vm.t("color1") : null
              var m7 = buydata.freightkey == idx2 ? _vm.t("color1rgb") : null
              return {
                $orig: $orig,
                m6: m6,
                m7: m7,
              }
            })
          : null
        var g1 =
          !_vm.usegiveorder &&
          buydata.freightList[buydata.freightkey].minpriceset == 1 &&
          buydata.freightList[buydata.freightkey].minprice > 0 &&
          buydata.freightList[buydata.freightkey].minprice * 1 >
            buydata.product_price * 1
            ? (
                buydata.freightList[buydata.freightkey].minprice -
                buydata.product_price
              ).toFixed(2)
            : null
        var g2 =
          !_vm.usegiveorder &&
          buydata.freightList[buydata.freightkey].pstype == 1 &&
          buydata.freightList[buydata.freightkey].isbusiness == 1
            ? _vm.storeshowall == false &&
              buydata.freightList[buydata.freightkey].storedata.length > 5
            : null
        var g3 =
          !_vm.usegiveorder &&
          !_vm.mendianShowType &&
          buydata.freightList[buydata.freightkey].pstype == 1 &&
          buydata.freightList[buydata.freightkey].isbusiness != 1 &&
          _vm.mendian_no_select == 0
            ? buydata.freightList[buydata.freightkey].storedata.length
            : null
        var l3 =
          !_vm.usegiveorder &&
          !_vm.mendianShowType &&
          buydata.freightList[buydata.freightkey].pstype == 1 &&
          buydata.freightList[buydata.freightkey].isbusiness != 1 &&
          !_vm.mendian_upgrade &&
          !_vm.mendian_no_select
            ? _vm.__map(
                buydata.freightList[buydata.freightkey].storedata,
                function (item, idx) {
                  var $orig = _vm.__get_orig(item)
                  var m8 =
                    (idx < 5 || _vm.storeshowall == true) &&
                    buydata.freightList[buydata.freightkey].storekey == idx
                      ? _vm.t("color1")
                      : null
                  return {
                    $orig: $orig,
                    m8: m8,
                  }
                }
              )
            : null
        var g4 =
          !_vm.usegiveorder &&
          !_vm.mendianShowType &&
          buydata.freightList[buydata.freightkey].pstype == 1 &&
          buydata.freightList[buydata.freightkey].isbusiness != 1
            ? _vm.storeshowall == false &&
              buydata.freightList[buydata.freightkey].storedata.length > 5
            : null
        var g5 =
          !_vm.usegiveorder &&
          !_vm.mendianShowType &&
          buydata.freightList[buydata.freightkey].pstype == 2 &&
          buydata.freightList[buydata.freightkey].storedata
            ? buydata.freightList[buydata.freightkey].storedata.length
            : null
        var l4 =
          !_vm.usegiveorder &&
          !_vm.mendianShowType &&
          buydata.freightList[buydata.freightkey].pstype == 2 &&
          buydata.freightList[buydata.freightkey].storedata
            ? _vm.__map(
                buydata.freightList[buydata.freightkey].storedata,
                function (item, idx) {
                  var $orig = _vm.__get_orig(item)
                  var m9 =
                    (idx < 5 || _vm.storeshowall == true) &&
                    buydata.freightList[buydata.freightkey].storekey == idx
                      ? _vm.t("color1")
                      : null
                  return {
                    $orig: $orig,
                    m9: m9,
                  }
                }
              )
            : null
        var g6 =
          !_vm.usegiveorder &&
          !_vm.mendianShowType &&
          buydata.freightList[buydata.freightkey].pstype == 2 &&
          buydata.freightList[buydata.freightkey].storedata
            ? _vm.storeshowall == false &&
              buydata.freightList[buydata.freightkey].storedata.length > 5
            : null
        var m10 =
          !_vm.usegiveorder &&
          !_vm.mendianShowType &&
          buydata.freightList[buydata.freightkey].pstype == 5
            ? _vm.t("门店")
            : null
        var g7 =
          !_vm.usegiveorder &&
          !_vm.mendianShowType &&
          buydata.freightList[buydata.freightkey].pstype == 5
            ? buydata.freightList[buydata.freightkey].storedata.length
            : null
        var l5 =
          !_vm.usegiveorder &&
          !_vm.mendianShowType &&
          buydata.freightList[buydata.freightkey].pstype == 5 &&
          !_vm.mendian_upgrade
            ? _vm.__map(
                buydata.freightList[buydata.freightkey].storedata,
                function (item, idx) {
                  var $orig = _vm.__get_orig(item)
                  var m11 =
                    (idx < 5 || _vm.storeshowall == true) &&
                    buydata.freightList[buydata.freightkey].storekey == idx
                      ? _vm.t("color1")
                      : null
                  return {
                    $orig: $orig,
                    m11: m11,
                  }
                }
              )
            : null
        var g8 =
          !_vm.usegiveorder &&
          !_vm.mendianShowType &&
          buydata.freightList[buydata.freightkey].pstype == 5 &&
          !_vm.mendian_upgrade
            ? _vm.storeshowall == false &&
              buydata.freightList[buydata.freightkey].storedata.length > 5
            : null
        var g9 =
          !_vm.usegiveorder &&
          _vm.mendianShowType &&
          ((buydata.freightList[buydata.freightkey].pstype == 1 &&
            buydata.freightList[buydata.freightkey].isbusiness != 1) ||
            buydata.freightList[buydata.freightkey].pstype == 5)
            ? buydata.freightList[buydata.freightkey].storedata.length
            : null
        var m12 =
          buydata.leveldk_money > 0 && buydata.leveldk_show
            ? _vm.t("会员")
            : null
        var m13 = buydata.weight_price > 0 ? _vm.t("包装费") : null
        var m14 = _vm.t("优惠券")
        var g10 = buydata.couponCount > 0 ? buydata.coupons.length : null
        var g11 =
          buydata.couponCount > 0 && g10 > 0 && _vm.is_coupon_auto_multi
            ? buydata.coupons.length
            : null
        var l6 =
          buydata.couponCount > 0 && g10 > 0 && !_vm.is_coupon_auto_multi
            ? _vm.__map(buydata.coupons, function (item, index) {
                var $orig = _vm.__get_orig(item)
                var m15 = _vm.t("color1")
                return {
                  $orig: $orig,
                  m15: m15,
                }
              })
            : null
        var m16 = buydata.couponCount > 0 && !(g10 > 0) ? _vm.t("color1") : null
        var m17 = !(buydata.couponCount > 0) ? _vm.t("优惠券") : null
        var g12 =
          buydata.cuxiaoCount > 0
            ? buydata.cuxiaonameArr && buydata.cuxiaonameArr.length > 0
            : null
        var l7 =
          buydata.cuxiaoCount > 0 && g12
            ? _vm.__map(buydata.cuxiaonameArr, function (item, index) {
                var $orig = _vm.__get_orig(item)
                var m18 = _vm.t("color1")
                return {
                  $orig: $orig,
                  m18: m18,
                }
              })
            : null
        var m19 = buydata.cuxiaoCount > 0 && !g12 ? _vm.t("color1") : null
        var m20 = _vm.inArray("discount_code_zc", _vm.custom)
        var m21 =
          _vm.business_selfscore == 1 &&
          _vm.scoredkdataArr[groupBid].score2money > 0 &&
          (_vm.scoredkdataArr[groupBid].scoremaxtype == 0 ||
            (_vm.scoredkdataArr[groupBid].scoremaxtype == 1 &&
              _vm.scoredkdataArr[buydata.bid].scoredkmaxmoney > 0))
            ? _vm.t("积分")
            : null
        var m22 =
          _vm.business_selfscore == 1 &&
          _vm.scoredkdataArr[groupBid].score2money > 0 &&
          (_vm.scoredkdataArr[groupBid].scoremaxtype == 0 ||
            (_vm.scoredkdataArr[groupBid].scoremaxtype == 1 &&
              _vm.scoredkdataArr[buydata.bid].scoredkmaxmoney > 0))
            ? _vm.t("积分")
            : null
        var m23 =
          _vm.moneydec &&
          _vm.shop_num > 1 &&
          _vm.userinfo.money > 0 &&
          ((!buydata.money_dec_type && buydata.money_dec_rate > 0) ||
            (buydata.money_dec_type == 1 && buydata.money_dec_money > 0))
            ? _vm.t("余额")
            : null
        var m24 =
          _vm.moneydec &&
          _vm.shop_num > 1 &&
          _vm.userinfo.money > 0 &&
          ((!buydata.money_dec_type && buydata.money_dec_rate > 0) ||
            (buydata.money_dec_type == 1 && buydata.money_dec_money > 0))
            ? _vm.t("余额")
            : null
        var m25 =
          _vm.moneydec &&
          _vm.shop_num > 1 &&
          _vm.userinfo.money > 0 &&
          ((!buydata.money_dec_type && buydata.money_dec_rate > 0) ||
            (buydata.money_dec_type == 1 && buydata.money_dec_money > 0))
            ? _vm.t("余额")
            : null
        var l8 = _vm.__map(
          buydata.freightList[buydata.freightkey].formdata,
          function (item, idx) {
            var $orig = _vm.__get_orig(item)
            var g13 =
              item.key == "upload_pics" &&
              buydata.editorFormdata &&
              buydata.editorFormdata[idx]
                ? buydata.editorFormdata[idx].join(",")
                : null
            return {
              $orig: $orig,
              g13: g13,
            }
          }
        )
        return {
          $orig: $orig,
          l1: l1,
          l2: l2,
          g1: g1,
          g2: g2,
          g3: g3,
          l3: l3,
          g4: g4,
          g5: g5,
          l4: l4,
          g6: g6,
          m10: m10,
          g7: g7,
          l5: l5,
          g8: g8,
          g9: g9,
          m12: m12,
          m13: m13,
          m14: m14,
          g10: g10,
          g11: g11,
          l6: l6,
          m16: m16,
          m17: m17,
          g12: g12,
          l7: l7,
          m19: m19,
          m20: m20,
          m21: m21,
          m22: m22,
          m23: m23,
          m24: m24,
          m25: m25,
          l8: l8,
        }
      })
    : null
  var g14 = _vm.isload ? _vm.fenqiData.length : null
  var l10 =
    _vm.isload && g14 > 0 && _vm.fenqi_type == 1
      ? _vm.__map(_vm.fenqiData, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m26 = item.fenqi_give_num ? _vm.t("优惠券") : null
          var m27 = !item.fenqi_give_num ? _vm.t("优惠券") : null
          return {
            $orig: $orig,
            m26: m26,
            m27: m27,
          }
        })
      : null
  var m28 =
    _vm.isload &&
    _vm.business_selfscore == 0 &&
    _vm.userinfo.score2money > 0 &&
    (_vm.userinfo.scoremaxtype == 0 ||
      (_vm.userinfo.scoremaxtype == 1 && _vm.userinfo.scoredkmaxmoney > 0))
      ? _vm.t("积分")
      : null
  var m29 =
    _vm.isload &&
    _vm.business_selfscore == 0 &&
    _vm.userinfo.score2money > 0 &&
    (_vm.userinfo.scoremaxtype == 0 ||
      (_vm.userinfo.scoremaxtype == 1 && _vm.userinfo.scoredkmaxmoney > 0))
      ? _vm.t("积分")
      : null
  var m30 =
    _vm.isload &&
    _vm.moneydec &&
    _vm.shop_num <= 1 &&
    _vm.userinfo.money > 0 &&
    ((!_vm.allbuydata[_vm.shop_bid].money_dec_type &&
      _vm.allbuydata[_vm.shop_bid].money_dec_rate > 0) ||
      (_vm.allbuydata[_vm.shop_bid].money_dec_type == 1 &&
        _vm.allbuydata[_vm.shop_bid].money_dec_money > 0))
      ? _vm.t("余额")
      : null
  var m31 =
    _vm.isload &&
    _vm.moneydec &&
    _vm.shop_num <= 1 &&
    _vm.userinfo.money > 0 &&
    ((!_vm.allbuydata[_vm.shop_bid].money_dec_type &&
      _vm.allbuydata[_vm.shop_bid].money_dec_rate > 0) ||
      (_vm.allbuydata[_vm.shop_bid].money_dec_type == 1 &&
        _vm.allbuydata[_vm.shop_bid].money_dec_money > 0))
      ? _vm.t("余额")
      : null
  var m32 =
    _vm.isload &&
    _vm.moneydec &&
    _vm.shop_num <= 1 &&
    _vm.userinfo.money > 0 &&
    ((!_vm.allbuydata[_vm.shop_bid].money_dec_type &&
      _vm.allbuydata[_vm.shop_bid].money_dec_rate > 0) ||
      (_vm.allbuydata[_vm.shop_bid].money_dec_type == 1 &&
        _vm.allbuydata[_vm.shop_bid].money_dec_money > 0))
      ? _vm.t("余额")
      : null
  var m33 =
    _vm.isload &&
    _vm.userinfo.tongzheng2money > 0 &&
    (_vm.userinfo.tongzhengmaxtype == 0 ||
      (_vm.userinfo.tongzhengmaxtype == 1 &&
        _vm.userinfo.tongzhengdkmaxmoney > 0))
      ? _vm.t("通证")
      : null
  var m34 =
    _vm.isload &&
    _vm.userinfo.tongzheng2money > 0 &&
    (_vm.userinfo.tongzhengmaxtype == 0 ||
      (_vm.userinfo.tongzhengmaxtype == 1 &&
        _vm.userinfo.tongzhengdkmaxmoney > 0))
      ? _vm.t("通证")
      : null
  var g15 = _vm.isload
    ? _vm.goldsilverlist && _vm.goldsilverlist.length > 0
    : null
  var m35 =
    _vm.isload &&
    _vm.userinfo.shopscore > 0 &&
    _vm.userinfo.shopscoredk_money > 0 &&
    _vm.userinfo.shopscoredkmaxpercent > 0
      ? _vm.t("产品积分")
      : null
  var m36 =
    _vm.isload &&
    _vm.userinfo.shopscore > 0 &&
    _vm.userinfo.shopscoredk_money > 0 &&
    _vm.userinfo.shopscoredkmaxpercent > 0
      ? _vm.t("产品积分")
      : null
  var m37 =
    _vm.isload &&
    _vm.userinfo.shopscore > 0 &&
    _vm.userinfo.shopscoredk_money > 0 &&
    _vm.userinfo.shopscoredkmaxpercent > 0
      ? _vm.t("产品积分")
      : null
  var m38 = _vm.isload && _vm.ishand && _vm.hwset ? _vm.t("color1") : null
  var l11 =
    _vm.isload && _vm.show_product_xieyi && _vm.product_xieyi
      ? _vm.__map(_vm.product_xieyi, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m39 = _vm.t("color1")
          return {
            $orig: $orig,
            m39: m39,
          }
        })
      : null
  var m40 =
    _vm.isload && _vm.order_discount_rand && _vm.order_discount_rand.status == 1
      ? _vm.t("color1")
      : null
  var m41 =
    _vm.isload && _vm.order_discount_rand && _vm.order_discount_rand.status == 1
      ? _vm.t("color1rgb")
      : null
  var m42 =
    _vm.isload &&
    !(_vm.showprice_dollar && _vm.usdalltotalprice) &&
    _vm.allservice_fee > 0
      ? _vm.t("服务费")
      : null
  var m43 = _vm.isload ? _vm.t("color1") : null
  var m44 = _vm.isload ? _vm.t("color1rgb") : null
  var m45 =
    _vm.isload && _vm.invoiceShow ? _vm.inArray(1, _vm.invoice_type) : null
  var m46 =
    _vm.isload && _vm.invoiceShow ? _vm.inArray(2, _vm.invoice_type) : null
  var m47 = _vm.isload && _vm.invoiceShow ? _vm.t("color1") : null
  var m48 = _vm.isload && _vm.couponvisible ? _vm.t("优惠券") : null
  var m49 =
    _vm.isload && _vm.couponvisible && _vm.coupon_not_used_discount == 1
      ? _vm.t("优惠券")
      : null
  var m50 =
    _vm.isload && _vm.couponvisible && _vm.coupon_not_used_discount == 1
      ? _vm.t("会员")
      : null
  var l12 =
    _vm.isload && _vm.pstimeDialogShow
      ? _vm.__map(
          _vm.allbuydata[_vm.nowbid].freightList[
            _vm.allbuydata[_vm.nowbid].freightkey
          ].pstimeArr,
          function (item, index) {
            var $orig = _vm.__get_orig(item)
            var m51 =
              _vm.allbuydata[_vm.nowbid].freight_time == item.value
                ? _vm.t("color1")
                : null
            return {
              $orig: $orig,
              m51: m51,
            }
          }
        )
      : null
  var m52 =
    _vm.isload && _vm.cuxiaovisible && _vm.multi_promotion && _vm.cxid === 0
      ? _vm.t("color1")
      : null
  var l13 =
    _vm.isload && _vm.cuxiaovisible && _vm.multi_promotion
      ? _vm.__map(_vm.allbuydata[_vm.bid].cuxiaolist, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var g16 = _vm.cxids.indexOf(item.id)
          var m53 = g16 !== -1 ? _vm.t("color1") : null
          return {
            $orig: $orig,
            g16: g16,
            m53: m53,
          }
        })
      : null
  var m54 =
    _vm.isload && _vm.cuxiaovisible && !_vm.multi_promotion && _vm.cxid == 0
      ? _vm.t("color1")
      : null
  var l14 =
    _vm.isload && _vm.cuxiaovisible && !_vm.multi_promotion
      ? _vm.__map(_vm.allbuydata[_vm.bid].cuxiaolist, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m55 = _vm.cxid == item.id ? _vm.t("color1") : null
          return {
            $orig: $orig,
            m55: m55,
          }
        })
      : null
  var m56 = _vm.isload && _vm.cuxiaovisible ? _vm.t("color1") : null
  var l15 =
    _vm.isload && _vm.type11visible
      ? _vm.__map(
          _vm.allbuydata[_vm.bid].freightList[
            _vm.allbuydata[_vm.bid].freightkey
          ].type11pricedata,
          function (item, index) {
            var $orig = _vm.__get_orig(item)
            var m57 =
              _vm.address.id &&
              _vm.address.province == item.province &&
              _vm.address.city == item.city &&
              _vm.address.district == item.area &&
              _vm.type11key == index
                ? _vm.t("color1")
                : null
            return {
              $orig: $orig,
              m57: m57,
            }
          }
        )
      : null
  var m58 = _vm.isload && _vm.type11visible ? _vm.t("color1") : null
  var l16 =
    _vm.isload && _vm.membervisible
      ? _vm.__map(_vm.memberList, function (item2, i) {
          var $orig = _vm.__get_orig(item2)
          var m59 = _vm.checkMem.id == item2.id ? _vm.t("color1") : null
          return {
            $orig: $orig,
            m59: m59,
          }
        })
      : null
  var l17 =
    _vm.isload && _vm.isshowglass
      ? _vm.__map(_vm.glassrecordlist, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m60 = _vm.t("color1")
          return {
            $orig: $orig,
            m60: m60,
          }
        })
      : null
  var m61 = _vm.isload && _vm.isshowglass ? _vm.t("color1") : null
  var a0 = _vm.isload
    ? {
        bgcolor: "#fff",
        margin_y: 0,
        margin_x: 0,
        padding_x: 0,
        padding_y: 0,
        height: "150",
        latitude: _vm.allbuydata[_vm.bid].business.latitude,
        longitude: _vm.allbuydata[_vm.bid].business.longitude,
        address: _vm.allbuydata[_vm.bid].business.name,
      }
    : null
  var m62 = _vm.isload && _vm.storevisible ? _vm.t("color1") : null
  var l18 =
    _vm.isload && _vm.storevisible
      ? _vm.__map(_vm.storedata, function (item, idx) {
          var $orig = _vm.__get_orig(item)
          var m63 =
            item.searchkey == idx && _vm.storekey == idx
              ? _vm.t("color1")
              : null
          return {
            $orig: $orig,
            m63: m63,
          }
        })
      : null
  var m64 = _vm.isload && _vm.storevisible ? _vm.t("color1") : null
  var m65 = _vm.isload && _vm.showxieyi ? _vm.t("color1") : null
  var m66 = _vm.isload && _vm.showxieyi ? _vm.t("color1rgb") : null
  var m67 = _vm.isload && _vm.showproxieyi ? _vm.t("color1") : null
  var m68 = _vm.isload && _vm.showproxieyi ? _vm.t("color1rgb") : null
  var m69 = _vm.isload && _vm.memberOtherShow ? _vm.t("color1") : null
  var g17 = _vm.isload && _vm.memberOtherShow ? _vm.teamMemberList.length : null
  var l19 =
    _vm.isload && _vm.memberOtherShow && g17 > 0
      ? _vm.__map(_vm.teamMemberList, function (item2, i) {
          var $orig = _vm.__get_orig(item2)
          var m70 = _vm.teamMember.id == item2.id ? _vm.t("color1") : null
          return {
            $orig: $orig,
            m70: m70,
          }
        })
      : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l9: l9,
        g14: g14,
        l10: l10,
        m28: m28,
        m29: m29,
        m30: m30,
        m31: m31,
        m32: m32,
        m33: m33,
        m34: m34,
        g15: g15,
        m35: m35,
        m36: m36,
        m37: m37,
        m38: m38,
        l11: l11,
        m40: m40,
        m41: m41,
        m42: m42,
        m43: m43,
        m44: m44,
        m45: m45,
        m46: m46,
        m47: m47,
        m48: m48,
        m49: m49,
        m50: m50,
        l12: l12,
        m52: m52,
        l13: l13,
        m54: m54,
        l14: l14,
        m56: m56,
        l15: l15,
        m58: m58,
        l16: l16,
        l17: l17,
        m61: m61,
        a0: a0,
        m62: m62,
        l18: l18,
        m64: m64,
        m65: m65,
        m66: m66,
        m67: m67,
        m68: m68,
        m69: m69,
        g17: g17,
        l19: l19,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 4832:
/*!*********************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/pagesB/shop/buy.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=script&lang=js& */ 4833);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 4833:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!/Users/<USER>/Downloads/tcphp/uniapp/pagesB/shop/buy.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

var app = getApp();
var _default = {
  data: function data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      scoredkChecked: 0,
      pre_url: app.globalData.pre_url,
      test: 'test',
      havetongcheng: 0,
      address: [],
      memberList: [],
      checkMem: {},
      usescore: 0,
      scoredk_money: 0,
      totalprice: '0.00',
      couponvisible: false,
      cuxiaovisible: false,
      membervisible: false,
      memberinfovisible: false,
      business_payconfirm: false,
      businessinfoConfirm: false,
      topayparams: {},
      selectmemberinfo: {},
      bid: 0,
      nowbid: 0,
      needaddress: 1,
      linkman: '',
      tel: '',
      userinfo: {},
      pstimeDialogShow: false,
      pstimeIndex: -1,
      manjian_money: 0,
      cxid: 0,
      cxids: [],
      latitude: "",
      longitude: "",
      allbuydata: {},
      allbuydatawww: {},
      alltotalprice: "",
      cuxiaoinfo: false,
      cuxiaoList: {},
      type11visible: false,
      type11key: -1,
      regiondata: '',
      items: [],
      editorFormdata: [],
      buy_selectmember: false,
      multi_promotion: 0,
      storeshowall: false,
      order_change_price: false,
      invoiceShow: false,
      invoice: {},
      invoice_type: [],
      invoice_type_select: 1,
      name_type_select: 1,
      name_type_personal_disabled: false,
      inputDisabled: false,
      submitDisabled: false,
      pstype3needAddress: false,
      isshowglass: false,
      glassrecordlist: [],
      grid: 0,
      curindex: -1,
      curindex2: -1,
      showprice_dollar: false,
      usdrate: 0,
      usdalltotalprice: 0,
      hasglassproduct: 0,
      business_selfscore: 0,
      scoredkdataArr: {},
      moneydec: false,
      shop_num: 0,
      //bid不同类型
      shop_bid: 0,
      //最后一个bid
      moneydecArr: {},
      //抵扣金抵扣比例或抵扣金额
      moneydecArrType: {},
      //抵扣金抵扣类型 0：比例 1：金额
      worknum_status: false,
      worknum: '',
      worknumtip: '请输入您的工号',
      mendianShowType: 0,
      // 商城购买页门店展示方式，1单条展示，点击弹窗展示列表可搜索
      storevisible: false,
      storedata: [],
      storefreightkey: 0,
      storekey: 0,
      storebid: 0,
      storename: '',
      showweight: false,
      mendian_id: 0,
      custom: [],
      discount_code_zc: '',
      freightkey_bid: '',
      freightkey_index: '',
      coupon_not_used_discount: 0,
      order_discount_rand: {},
      ishand: 0,
      hwset: '',
      showxieyi: false,
      isagree: false,
      fenqiData: [],
      fenqi_type: 0,
      mendian_upgrade: false,
      mendian_change: true,
      mendian_no_select: 0,
      memberOtherShow: false,
      //选择会员
      teamMemberList: [],
      //选择团队会员
      show_other_order: false,
      //控制显示选择会员
      teamMember: {},
      //选择的会员信息
      tmid: '',
      contact_require: 0,
      ismultiselect: false,
      show_product_xieyi: 0,
      product_xieyi: [],
      showproxieyi: 0,
      proxieyi_content: '',
      isagree_pro: false,
      show_service_fee: false,
      allservice_fee: 0,
      //总服务费
      devicedata: '',
      //商品柜商品组合

      //来源直播间
      roomid: 0,
      usetongzheng: 0,
      mustuseaddress: false,
      //必须使用地址
      needusercard: false,
      usercard: '',
      usercardtip: '请输入您的身份证号',
      goldsilverlist: '',
      goldsilverdec: 0,
      goldsilvertype: 0,
      goldsilverindex: 0,
      xdjf: 0,
      full_piece_package: false,
      name: '',
      phone: '',
      pcemail: '',
      is_pingce: false,
      age: '',
      gender: 0,
      school: '',
      major: '',
      education: '',
      enrol: '',
      class_name: '',
      faculties_name: '',
      //院系
      sexlist: ['男', '女'],
      edulist: ['本科', '硕士', '博士', '专科', '专升本', '中专', '博士后', '双学位', '结业', '肄业', '进修生', '访问学者', '其他'],
      usededamount: false,
      //抵扣金

      //产品积分抵扣
      useshopscore: 0,
      shopscoredk_money: 0,
      is_coupon_auto_multi: 0,
      //兑换券 自动多选

      giveorder: 0,
      //是否开启赠送礼物功能 0:未开启 1：开启
      giveorder_validtime: 0,
      //赠送有效时间
      usegiveorder: false,
      //是否使用赠送礼物功能
      giveordertitle: '',
      giveorderpic: ''
    };
  },
  onLoad: function onLoad(opt) {
    this.opt = app.getopts(opt);
    var locationCache = app.getLocationCache();
    if (locationCache.mendian_id) {
      this.mendian_id = locationCache.mendian_id;
    }
    if (this.opt.devicedata) {
      this.devicedata = this.opt.devicedata;
    }
    this.roomid = this.opt.roomid || 0;
    this.getdata();
  },
  onShow: function onShow(e) {
    if (this.hasglassproduct == 1) {
      this.getglassrecord();
    }
    if (this.mendian_upgrade) {
      var locationCache = app.getLocationCache();
      if (locationCache.mendian_id) {
        this.mendian_id = locationCache.mendian_id;
      }
      this.getdata();
    }
  },
  onPullDownRefresh: function onPullDownRefresh() {
    this.getdata();
  },
  methods: {
    getdata: function getdata() {
      var that = this;
      that.loading = true;
      var jldata = that.opt.jldata ? JSON.parse(that.opt.jldata) : [];
      app.post('ApiShop/buy', {
        prodata: that.opt.prodata,
        mendian_id: that.mendian_id,
        devicedata: that.devicedata,
        jldata: jldata
      }, function (res) {
        that.loading = false;
        if (res.status == 0) {
          if (res.msg) {
            app.alert(res.msg, function () {
              if (res.url) {
                app.goto(res.url);
              } else {
                app.goback();
              }
            });
          } else if (res.url) {
            app.goto(res.url);
          } else {
            app.alert('您没有权限购买该商品');
          }
          return;
        }
        that.fenqiData = res.fenqi_data ? res.fenqi_data : [];
        that.havetongcheng = res.havetongcheng;
        that.address = res.address;
        if (that.address && that.address.latitude) {
          that.latitude = that.address.latitude;
          that.longitude = that.address.longitude;
        }
        that.is_pingce = res.is_pingce;
        that.linkman = res.linkman;
        that.tel = res.tel;
        that.userinfo = res.userinfo;
        that.buy_selectmember = res.buy_selectmember;
        that.order_change_price = res.order_change_price;
        that.pstype3needAddress = res.pstype3needAddress;
        //优惠不同享
        that.coupon_not_used_discount = res.coupon_not_used_discount;
        that.contact_require = res.contact_require;
        if (that.buy_selectmember) {
          uni.request({
            url: app.globalData.pre_url + '/static/area2.json',
            data: {},
            method: 'GET',
            header: {
              'content-type': 'application/json'
            },
            success: function success(res2) {
              that.items = res2.data;
            }
          });
        }
        that.allbuydata = res.allbuydata;
        if (that.freightkey_bid !== '') that.allbuydata[that.freightkey_bid].freightkey = that.freightkey_index;
        that.bid = res.bid;
        that.business_payconfirm = res.business_payconfirm || false;
        that.allbuydatawww = JSON.parse(JSON.stringify(res.allbuydata));
        that.needLocation = res.needLocation;
        that.scorebdkyf = res.scorebdkyf;
        that.multi_promotion = res.multi_promotion;
        that.showprice_dollar = res.price_dollar;
        that.usdrate = res.usdrate;
        that.hasglassproduct = res.hasglassproduct;
        that.business_selfscore = res.business_selfscore || 0;
        that.scoredkdataArr = res.scoredkdataArr || {};
        that.order_discount_rand = res.order_discount_rand || {};
        that.show_other_order = res.show_other_order || false;
        that.full_piece_package = res.full_piece_package || false;
        if (res.moneydec) {
          that.moneydec = res.moneydec;
          var shop_num = 0;
          var shop_bid = 0;
          //获取对象数量
          for (var key in res.allbuydata) {
            shop_num++;
            shop_bid = key;
          }
          that.shop_num = shop_num;
          that.shop_bid = shop_bid;
        }
        if (res.worknum_status) {
          that.worknum_status = res.worknum_status;
        }
        if (res.worknumtip) {
          that.worknumtip = res.worknumtip;
        }
        if (res.mendianShowType) {
          //商城购买页门店展示方式，1单条展示，点击弹窗展示列表可搜索
          that.mendianShowType = res.mendianShowType;
        }
        if (res.ishand) {
          that.ishand = res.ishand; //手工商品
        }

        if (res.hwset) {
          that.hwset = res.hwset; //手工商品
        }

        that.custom = res.custom;
        that.ismultiselect = res.ismultiselect;
        that.show_product_xieyi = res.show_product_xieyi;
        that.product_xieyi = res.product_xieyi;
        if (res.mustuseaddress) {
          that.mustuseaddress = res.mustuseaddress;
          that.needaddress = 1;
        }
        if (res.needusercard) {
          that.needusercard = res.needusercard;
          if (res.usercard) {
            that.usercard = res.usercard;
          }
          if (res.usercardtip) {
            that.usercardtip = res.usercardtip;
          }
        }
        if (res.goldsilverlist) {
          that.goldsilverlist = res.goldsilverlist;
          that.goldsilverindex = 1;
          that.goldsilvertype = res.goldsilverlist[1]['type'];
        }
        if (res.usededamount) {
          that.usededamount = res.usededamount;
        }
        if (res.is_coupon_auto_multi) {
          that.is_coupon_auto_multi = res.is_coupon_auto_multi;
        }
        if (res.giveorder) {
          //赠送礼物
          that.giveorder = res.giveorder;
          that.giveorder_validtime = res.giveorder_validtime;
        }
        //自动选择抵扣券
        that.autoSelectCoupon();
        that.calculatePrice();
        that.loaded();

        // var allbuydata = that.allbuydata;
        // for (var i in allbuydata) {
        // 	allbuydata[i].tempInvoice = uni.getStorageSync('temp_invoice_' + allbuydata[i].bid);
        // }
        // that.allbuydata = allbuydata;
        that.mendian_upgrade = res.mendian_upgrade;
        that.mendian_change = res.mendian_change;
        that.mendian_no_select = res.mendian_no_select;
        if (res.needLocation == 1) {
          app.getLocation(function (res) {
            var latitude = res.latitude;
            var longitude = res.longitude;
            that.latitude = latitude;
            that.longitude = longitude;
            var allbuydata = that.allbuydata;
            for (var i in allbuydata) {
              var freightList = allbuydata[i].freightList;
              for (var j in freightList) {
                if (freightList[j].pstype == 1 || freightList[j].pstype == 5) {
                  var storedata = freightList[j].storedata;
                  if (storedata) {
                    for (var x in storedata) {
                      if (latitude && longitude && storedata[x].latitude && storedata[x].longitude) {
                        var juli = that.getDistance(latitude, longitude, storedata[x].latitude, storedata[x].longitude);
                        storedata[x].juli = juli;
                      }
                    }
                    storedata.sort(function (a, b) {
                      return a["juli"] - b["juli"];
                    });
                    for (var x in storedata) {
                      if (storedata[x].juli) {
                        storedata[x].juli = storedata[x].juli + '千米';
                      }
                    }
                    allbuydata[i].freightList[j].storedata = storedata;
                  }
                }
              }
            }
            that.allbuydata = allbuydata;
          });
        }
      });
    },
    // 分期支付
    checkFenqi: function checkFenqi(e) {
      this.fenqi_type = e.detail.value.length;
    },
    //积分抵扣
    scoredk: function scoredk(e) {
      var usescore = e.detail.value[0];
      if (!usescore) usescore = 0;
      this.usescore = usescore;
      this.scoredkChecked = usescore;
      this.sdjf = 1;
      this.calculatePrice();
    },
    scoredk2: function scoredk2(e) {
      var usescore = e.detail.value[0];
      if (!usescore) usescore = 0;
      var thisbid = e.currentTarget.dataset.bid;
      this.scoredkdataArr[thisbid].usescore = usescore;
      //this.test = Math.random();
      this.calculatePrice();
    },
    //通证抵扣
    tongzhengdk: function tongzhengdk(e) {
      console.log('选择通证抵扣');
      var usetongzheng = e.detail.value[0];
      if (!usetongzheng) usetongzheng = 0;
      this.usetongzheng = usetongzheng;
      this.calculatePrice();
    },
    goldsilverdk: function goldsilverdk(e) {
      console.log(e);
      var that = this;
      var userinfo = that.userinfo;
      that.goldsilverindex = e.currentTarget.dataset.index;
      that.goldsilvertype = e.currentTarget.dataset.type;
      this.calculatePrice();
    },
    inputLinkman: function inputLinkman(e) {
      this.linkman = e.detail.value;
    },
    inputTel: function inputTel(e) {
      this.tel = e.detail.value;
    },
    inputWorknum: function inputWorknum(e) {
      this.worknum = e.detail.value;
    },
    inputEmail: function inputEmail(e) {
      this.pcemail = e.detail.value;
    },
    inputSchool: function inputSchool(e) {
      this.school = e.detail.value;
    },
    inputMajor: function inputMajor(e) {
      this.major = e.detail.value;
    },
    inputEduction: function inputEduction(e) {
      this.education = e.detail.value;
    },
    inputClassName: function inputClassName(e) {
      this.class_name = e.detail.value;
    },
    inputFaculties: function inputFaculties(e) {
      this.faculties_name = e.detail.value;
    },
    inputfield: function inputfield(e) {
      var bid = e.currentTarget.dataset.bid;
      var field = e.currentTarget.dataset.field;
      allbuydata2[bid][field] = e.detail.value;
      this.allbuydata2 = allbuydata2;
    },
    //选择收货地址
    chooseAddress: function chooseAddress() {
      app.goto('/pagesB/address/address?fromPage=buy&type=' + (this.havetongcheng == 1 ? '1' : '0'));
    },
    inputPrice: function inputPrice(e) {
      var that = this;
      var index = e.currentTarget.dataset.index;
      var index2 = e.currentTarget.dataset.index2;
      var allbuydata = that.allbuydata;
      var allbuydatawww = that.allbuydatawww;
      var oldprice = allbuydatawww[index]['prodata'][index2].guige.sell_price;
      if (e.detail.value == '' || parseFloat(e.detail.value) < parseFloat(oldprice)) {
        that.submitDisabled = true;
        app.error('不能小于原价:' + oldprice);
        return;
      }
      that.submitDisabled = false;
      allbuydata[index]['prodata'][index2].guige.sell_price = e.detail.value;
      allbuydata[index]['product_price'] = (e.detail.value * allbuydata[index]['prodata'][index2].num).toFixed(2);
      // allbuydata[index].prodatastr = allbuydata[index].prodatastr
      that.allbuydata = allbuydata;
      that.calculatePrice();
    },
    //计算价格
    calculatePrice: function calculatePrice() {
      var that = this;
      var address = that.address;
      var allbuydata = that.allbuydata;
      var alltotalprice = 0;
      var allfreight_price = 0;
      var needaddress = 0;
      var worknum_status = false;
      var allservice_fee = 0;
      if (that.moneydec) {
        var money_dec_type = 0; //余额抵扣 0：系统设置最大抵扣 1：商品单独设置最大抵扣金额
        var userinfo = that.userinfo;
        var usermoney = userinfo && userinfo.money ? parseFloat(userinfo.money) : 0;
      }
      for (var k in allbuydata) {
        var product_price = parseFloat(allbuydata[k].product_price);
        var leveldk_money = parseFloat(allbuydata[k].leveldk_money); //会员折扣
        var manjian_money = parseFloat(allbuydata[k].manjian_money); //满减活动
        var coupon_money = parseFloat(allbuydata[k].coupon_money); //-优惠券抵扣 
        var cuxiao_money = parseFloat(allbuydata[k].cuxiao_money); //+促销活动 
        var invoice_money = parseFloat(allbuydata[k].invoice_money); //+发票
        var service_fee_price = parseFloat(allbuydata[k].service_fee_price);
        var scoredk_money = 0;
        var tongzhengdk_money = 0;

        //算运费
        if (!that.usegiveorder) {
          var freightdata = allbuydata[k].freightList[allbuydata[k].freightkey];
          var freight_price = freightdata['freight_price'];
          if (freightdata.pstype != 1 && freightdata.pstype != 3 && freightdata.pstype != 4) {
            needaddress = 1;
          }
          if (freightdata.pstype == 1 && freightdata.select_address_status == 1) {
            needaddress = 1;
          }
        } else {
          //赠送好友，不填写配送费方式和不计算配送费
          var freightdata = [];
          var freight_price = 0;
        }
        var weight_price = 0;
        var product_price_discount_code = 0;
        //优惠不同享
        allbuydata[k].leveldk_show = 1;
        if (that.coupon_not_used_discount == 1 || allbuydata[k].not_used_discount == 1) {
          if (coupon_money > 0) {
            //选择优惠券 不计算折扣
            leveldk_money = 0;
            allbuydata[k].leveldk_show = 0;
          } else {
            leveldk_money = parseFloat(allbuydata[k].leveldk_money);
            allbuydata[k].leveldk_show = 1;
          }
        }
        for (var j in allbuydata[k].prodata) {
          if (allbuydata[k].prodata[j].product.weighttype > 0 && allbuydata[k].prodata[j].product.weightlist.length > 0) {
            var weightkey = allbuydata[k].prodata[j].product.weightkey;
            //console.log(weightkey);
            weight_price += allbuydata[k].prodata[j].product.weightlist[weightkey].weight_price;
          }
          if (allbuydata[k].discount_code_zc_status) {
            if (parseFloat(allbuydata[k].prodata[j].product.price_discount_code_zc) > 0) product_price_discount_code += parseFloat(allbuydata[k].prodata[j].product.price_discount_code_zc) * allbuydata[k].prodata[j].num;else product_price_discount_code += parseFloat(allbuydata[k].prodata[j].guige.sell_price) * allbuydata[k].prodata[j].num;
          }
        }
        if (product_price_discount_code > 0) product_price = product_price_discount_code;
        allbuydata[k].weight_price = weight_price;
        if (freightdata.worknum_status) {
          worknum_status = true;
        }
        if (that.pstype3needAddress && (freightdata.pstype == 3 || freightdata.pstype == 4 || freightdata.pstype == 5)) {
          needaddress = 1;
        }
        if (allbuydata[k].coupontype == 4) {
          freight_price = 0;
        }
        var totalprice = product_price - leveldk_money - manjian_money - coupon_money + cuxiao_money;
        if (totalprice < 0) totalprice = 0; //优惠券不抵扣运费

        totalprice = totalprice + freight_price + weight_price;
        allbuydata[k].freight_price = freight_price.toFixed(2);
        if (allbuydata[k].business.invoice && allbuydata[k].business.invoice_rate > 0 && allbuydata[k].tempInvoice) {
          var invoice_money = totalprice * parseFloat(allbuydata[k].business.invoice_rate) / 100;
          allbuydata[k].invoice_money = invoice_money.toFixed(2);
          totalprice = totalprice + invoice_money;
        }
        if (that.business_selfscore == 1 && that.scoredkdataArr[k].usescore == 1) {
          //business_selfscore 多商户积分
          var scoredkdata = that.scoredkdataArr[k];
          var oldtotalprice = totalprice;
          scoredk_money = parseFloat(scoredkdata.scoredk_money);
          if (scoredkdata.scorebdkyf == '1' && scoredk_money > 0 && totalprice < freight_price) {
            totalprice = freight_price;
            scoredk_money = totalprice - freight_price;
          }
          var scoredkmaxpercent = parseFloat(scoredkdata.scoredkmaxpercent); //最大抵扣比例
          var scoremaxtype = parseInt(scoredkdata.scoremaxtype);
          var scoredkmaxmoney = parseFloat(scoredkdata.scoredkmaxmoney);
          if (scoremaxtype == 0 && scoredk_money > 0 && scoredkmaxpercent > 0 && scoredkmaxpercent < 100 && scoredk_money > oldtotalprice * scoredkmaxpercent * 0.01) {
            scoredk_money = oldtotalprice * scoredkmaxpercent * 0.01;
            totalprice = oldtotalprice - scoredk_money;
          } else if (scoremaxtype == 1 && scoredk_money > scoredkmaxmoney) {
            scoredk_money = scoredkmaxmoney;
            totalprice = oldtotalprice - scoredk_money;
          }
        }
        totalprice = totalprice - scoredk_money;
        if (that.moneydec) {
          var moneydecArrType = that.moneydecArrType; //抵扣类型
          if (moneydecArrType && moneydecArrType[k] && moneydecArrType[k] == 1) {
            var moneydecType = 1;
            money_dec_type = 1;
          } else {
            var moneydecType = 0;
          }
          //如果商家数量大于1 或者 商家商品存在单独设置，则在这里处理
          if (that.shop_num > 1 || that.shop_num == 1 && money_dec_type == 1) {
            var moneydecArr = that.moneydecArr; //抵扣数据
            if (moneydecArr && moneydecArr[k] && moneydecArr[k] > 0) {
              var decrate = 0; //抵扣比例 
              var decmoney = 0; //抵扣金额
              if (moneydecType == 0) {
                //抵扣比例
                decrate = moneydecArr[k];
              } else {
                //抵扣金额
                decmoney = moneydecArr[k];
              }
              if (usermoney > 0 && (decrate > 0 || decmoney > 0)) {
                var dec_money = totalprice * decrate / 100 + decmoney;
                dec_money = Math.round(dec_money * 100) / 100;
                if (dec_money >= usermoney) {
                  dec_money = usermoney;
                  usermoney = 0;
                } else {
                  usermoney -= dec_money;
                }
                totalprice -= dec_money;
              }
            }
          }
        }
        if (totalprice < 0) totalprice = 0;
        allbuydata[k].totalprice = totalprice.toFixed(2);
        alltotalprice += totalprice;
        allfreight_price += freight_price;
        allservice_fee += service_fee_price;
      }
      that.needaddress = that.mustuseaddress ? 1 : needaddress;
      that.worknum_status = worknum_status;
      if (that.business_selfscore == 0 && that.usescore) {
        var scoredk_money = parseFloat(that.userinfo.scoredk_money); //-积分抵扣
      } else {
        var scoredk_money = 0;
      }
      var oldalltotalprice = alltotalprice;
      alltotalprice = alltotalprice - scoredk_money;
      if (alltotalprice < 0) alltotalprice = 0;
      if (that.usetongzheng) {
        var tongzhengdk_money = parseFloat(that.userinfo.tongzhengdk_money); //-通证抵扣
        var tongzhengdkmaxpercent = parseFloat(that.userinfo.tongzhengdkmaxpercent); //最大抵扣比例
        var tongzhengmaxtype = parseInt(that.userinfo.tongzhengmaxtype);
        var tongzhengdkmaxmoney = parseFloat(that.userinfo.tongzhengdkmaxmoney);
        if (tongzhengmaxtype == 0 && tongzhengdk_money > 0 && tongzhengdkmaxpercent > 0 && tongzhengdkmaxpercent < 100 && tongzhengdk_money > alltotalprice * tongzhengdkmaxpercent * 0.01) {
          tongzhengdk_money = alltotalprice * tongzhengdkmaxpercent * 0.01;
        } else if (tongzhengmaxtype == 1 && tongzhengdk_money > tongzhengdkmaxmoney) {
          tongzhengdk_money = tongzhengdkmaxmoney;
        }
        alltotalprice = alltotalprice - tongzhengdk_money;
        //tongzhengdk_money = parseFloat(that.userinfo.tongzhengdk_money);
      }

      if (that.scorebdkyf == '1' && scoredk_money > 0 && alltotalprice < allfreight_price) {
        //积分不抵扣运费
        alltotalprice = allfreight_price;
        scoredk_money = oldalltotalprice - allfreight_price;
      }
      var scoredkmaxpercent = parseFloat(that.userinfo.scoredkmaxpercent); //最大抵扣比例
      var scoremaxtype = parseInt(that.userinfo.scoremaxtype); //0按系统，1独立设置
      var scoredkmaxmoney = parseFloat(that.userinfo.scoredkmaxmoney);
      if (scoremaxtype == 0 && scoredk_money > 0 && scoredkmaxpercent > 0 && scoredkmaxpercent < 100 && scoredk_money > oldalltotalprice * scoredkmaxpercent * 0.01) {
        if (that.scorebdkyf == '1') {
          //积分不抵扣运费
          scoredk_money = (oldalltotalprice - allfreight_price) * scoredkmaxpercent * 0.01;
        } else {
          scoredk_money = oldalltotalprice * scoredkmaxpercent * 0.01;
        }
        alltotalprice = oldalltotalprice - scoredk_money;
      } else if (scoremaxtype == 1 && scoredk_money > scoredkmaxmoney) {
        scoredk_money = scoredkmaxmoney;
        alltotalprice = oldalltotalprice - scoredk_money;
      }
      if (that.moneydec) {
        //如果仅一个商家，且商品无单独设置，则走这里
        if (that.shop_num == 1 && money_dec_type == 0) {
          var moneydecArr = that.moneydecArr;
          for (var k in allbuydata) {
            if (moneydecArr && moneydecArr[k] && moneydecArr[k] > 0) {
              var decrate = moneydecArr[k];
              if (usermoney > 0 && decrate > 0) {
                var dec_money = alltotalprice * decrate / 100;
                dec_money = Math.round(dec_money * 100) / 100;
                if (dec_money >= usermoney) {
                  dec_money = usermoney;
                  usermoney = 0;
                } else {
                  usermoney -= dec_money;
                }
                alltotalprice -= dec_money;
              }
            }
          }
        }
      }

      //如果使用抵扣金
      if (that.usededamount && that.userinfo['dedamount_dkmoney'] && that.userinfo['dedamount_dkmoney'] > 0 && alltotalprice > 0) {
        alltotalprice -= that.userinfo['dedamount_dkmoney'];
      }
      if (that.goldsilverlist) {
        var goldsilverdec = that.goldsilverlist[that.goldsilverindex]['value'];
        if (goldsilverdec >= alltotalprice) {
          goldsilverdec = alltotalprice;
        }
        alltotalprice -= goldsilverdec;
        that.goldsilverdec = goldsilverdec;
      }

      //产品积分抵扣
      if (that.useshopscore && alltotalprice > 0 && that.userinfo.shopscore > 0 && that.userinfo.shopscoredk_money > 0 && that.userinfo.shopscoredkmaxpercent > 0) {
        var shopscoredk_money = parseFloat(that.userinfo.shopscoredk_money); //会员产品积分换算最大可抵扣数值

        var shopscoremaxtype = parseInt(that.userinfo.shopscoremaxtype); //兑换类型
        if (shopscoremaxtype == 0) {
          var shopscoredkmaxpercent = parseFloat(that.userinfo.shopscoredkmaxpercent); //最大抵扣比例
          var nowshopscoredk_money = alltotalprice * shopscoredkmaxpercent * 0.01; //现在可最大抵扣数值
        } else {
          var nowshopscoredk_money = parseFloat(that.userinfo.shopscoredkmaxmoney); //现在可最大抵扣数值
        }

        if (nowshopscoredk_money > 0) {
          nowshopscoredk_money = nowshopscoredk_money.toFixed(2);
          if (nowshopscoredk_money <= shopscoredk_money) {
            alltotalprice -= nowshopscoredk_money;
          } else {
            alltotalprice -= shopscoredk_money;
          }
        }
      }
      if (alltotalprice < 0) alltotalprice = 0;
      alltotalprice = alltotalprice.toFixed(2);
      that.alltotalprice = alltotalprice;
      that.allservice_fee = allservice_fee;
      if (that.showprice_dollar && that.usdrate) {
        that.usdalltotalprice = (that.alltotalprice / that.usdrate).toFixed(2);
      }
      that.allbuydata = allbuydata;
    },
    changeFreight: function changeFreight(e) {
      var that = this;
      var allbuydata = that.allbuydata;
      var bid = e.currentTarget.dataset.bid;
      var index = e.currentTarget.dataset.index;
      var freightList = allbuydata[bid].freightList;
      if (freightList[index].pstype == 1 && freightList[index].storedata.length < 1) {
        app.error('无可自提门店');
        return;
      }
      if (freightList[index].pstype == 5 && freightList[index].storedata.length < 1) {
        app.error('无可配送门店');
        return;
      }
      allbuydata[bid].freightkey = index;
      this.freightkey_bid = bid;
      this.freightkey_index = index;
      that.allbuydata = allbuydata;
      that.calculatePrice();
      that.allbuydata[bid].editorFormdata = [];
    },
    chooseFreight: function chooseFreight(e) {
      var that = this;
      var allbuydata = that.allbuydata;
      var bid = e.currentTarget.dataset.bid;
      // console.log(bid);
      // console.log(allbuydata);
      var freightList = allbuydata[bid].freightList;
      var itemlist = [];
      for (var i = 0; i < freightList.length; i++) {
        itemlist.push(freightList[i].name);
      }
      uni.showActionSheet({
        itemList: itemlist,
        success: function success(res) {
          if (res.tapIndex >= 0) {
            allbuydata[bid].freightkey = res.tapIndex;
            that.allbuydata = allbuydata;
            that.calculatePrice();
          }
        }
      });
    },
    changeWeight: function changeWeight(e) {
      var that = this;
      var allbuydata = that.allbuydata;
      var bid = e.currentTarget.dataset.bid;
      var proindex = e.currentTarget.dataset.proindex;
      var index = e.currentTarget.dataset.index;
      var weightList = allbuydata[bid].prodata[proindex].product.weightlist;
      allbuydata[bid].prodata[proindex].product.weightkey = index;
      that.allbuydata = allbuydata;
      that.calculatePrice();
      that.allbuydata[bid].editorFormdata = [];
    },
    choosePstime: function choosePstime(e) {
      var that = this;
      var allbuydata = that.allbuydata;
      var bid = e.currentTarget.dataset.bid;
      var freightkey = allbuydata[bid].freightkey;
      var freightList = allbuydata[bid].freightList;
      var freight = freightList[freightkey];
      var pstimeArr = freightList[freightkey].pstimeArr;
      var itemlist = [];
      for (var i = 0; i < pstimeArr.length; i++) {
        itemlist.push(pstimeArr[i].title);
      }
      if (itemlist.length == 0) {
        app.alert('当前没有可选' + (freightList[freightkey].pstype == 1 ? '取货' : '配送') + '时间段');
        return;
      }
      that.nowbid = bid;
      that.pstimeDialogShow = true;
      that.pstimeIndex = -1;
    },
    pstimeRadioChange: function pstimeRadioChange(e) {
      var that = this;
      var allbuydata = that.allbuydata;
      var pstimeIndex = e.currentTarget.dataset.index;

      // console.log(pstimeIndex)
      var nowbid = that.nowbid;
      var freightkey = allbuydata[nowbid].freightkey;
      var freightList = allbuydata[nowbid].freightList;
      var freight = freightList[freightkey];
      var pstimeArr = freightList[freightkey].pstimeArr;
      var choosepstime = pstimeArr[pstimeIndex];
      allbuydata[nowbid].pstimetext = choosepstime.title;
      allbuydata[nowbid].freight_time = choosepstime.value;
      that.allbuydata = allbuydata;
      that.pstimeDialogShow = false;
    },
    hidePstimeDialog: function hidePstimeDialog() {
      this.pstimeDialogShow = false;
    },
    autoSelectCoupon: function autoSelectCoupon() {
      var allbuydata = this.allbuydata;
      for (var bid in allbuydata) {
        var couponlist = allbuydata[bid].couponList;
        var coupons = [];
        var coupon_money = 0;
        var xianxia_proid = {}; //线下券 {proid:num} {123:3,3,124:2}
        var couponrids = [];
        for (var ci in couponlist) {
          if (couponlist[ci]['type'] == 11) {
            var is_checked = 0;
            var proids = couponlist[ci]['productids'];
            for (var i in allbuydata[bid]['prodata']) {
              var product = allbuydata[bid]['prodata'][i].product;
              var pronum = allbuydata[bid]['prodata'][i].num;
              var proid = product.id;
              if (app.inArray(proid, proids)) {
                //如果不存在，存入
                if (!xianxia_proid[proid]) {
                  xianxia_proid[proid] = 0;
                }
                if (xianxia_proid[proid] < pronum) {
                  var newnum = Number(xianxia_proid[proid]) + 1;
                  xianxia_proid[proid] = newnum;
                  coupon_money += Number(allbuydata[bid]['prodata'][i]['guige']['sell_price']);
                  is_checked = 1;
                  coupons.push(couponlist[ci]);
                  couponrids.push(couponlist[ci].id);
                  console.log(couponlist[ci], 'couponlistcouponlist');
                  console.log(couponrids, 'couponridscouponridscouponrids');
                  break;
                } else {
                  continue;
                }
              }
            }
            if (is_checked == 0) {
              var thiscouponrids = [];
              var thiscoupons = [];
              for (var i in allbuydata[bid].couponrids) {
                if (allbuydata[bid].couponrids[i] != couponrid) {
                  thiscoupons.push(couponlist[i]);
                  thiscouponrids.push(couponlist[i].id);
                }
              }
              allbuydata[bid].couponrids = thiscouponrids;
              allbuydata[bid].coupons = thiscoupons;
            }
          }
        }
        allbuydata[bid].coupon_money = coupon_money;
        allbuydata[bid].coupons = coupons;
        allbuydata[bid].couponrids = couponrids;
      }
      this.allbuydata = allbuydata;
      this.calculatePrice();
    },
    chooseCoupon: function chooseCoupon(e) {
      var allbuydata = this.allbuydata;
      var bid = e.bid;
      var couponrid = e.rid;
      var couponkey = e.key;
      var oldcoupons = allbuydata[bid].coupons;
      var oldcouponrids = allbuydata[bid].couponrids;
      var couponList = allbuydata[bid].couponList;
      var is_use_coupon = 1;
      if (app.inArray(couponrid, oldcouponrids)) {
        var coupons = [];
        var couponrids = [];
        for (var i in oldcoupons) {
          if (oldcoupons[i].id != couponrid) {
            coupons.push(oldcoupons[i]);
            couponrids.push(oldcoupons[i].id);
          }
        }
        is_use_coupon = 0;
      } else {
        coupons = oldcoupons;
        couponrids = oldcouponrids;
        if (allbuydata[bid].coupon_peruselimit > oldcouponrids.length) {
          if (this.ismultiselect) {
            if (oldcouponrids.length == 1) {
              if (coupons[0].is_multiselect == 0 || coupons[0].type != 1) {
                app.error('不可一起使用');
                return;
              }
            }
            if (oldcouponrids.length > 0) {
              if (couponList[couponkey].is_multiselect == 0 || couponList[couponkey].type != 1) {
                app.error('不可一起使用');
                return;
              }
            }
          }
          coupons.push(couponList[couponkey]);
          couponrids.push(couponList[couponkey].id);
        } else {
          if (allbuydata[bid].coupon_peruselimit > 1) {
            app.error('最多只能选用' + allbuydata[bid].coupon_peruselimit + '张');
            return;
          } else {
            coupons = [couponList[couponkey]];
            couponrids = [couponrid];
          }
        }
      }
      allbuydata[bid].coupons = coupons;
      allbuydata[bid].couponrids = couponrids;
      //实际 抵扣金额已超过 实际产品金额，
      if (allbuydata[bid].coupon_money >= allbuydata[bid].product_price && is_use_coupon) {
        var thiscouponrids = [];
        var thiscoupons = [];
        for (var i in allbuydata[bid].couponrids) {
          if (allbuydata[bid].couponrids[i] != couponrid) {
            thiscoupons.push(coupons[i]);
            thiscouponrids.push(coupons[i].id);
          }
        }
        allbuydata[bid].couponrids = thiscouponrids;
        allbuydata[bid].coupons = thiscoupons;
        app.error('已抵扣完成');
      }
      var coupon_money = 0;
      var coupontype = 1;
      var not_used_discount = 0;
      var xianxia_proid = {}; //线下券 {proid:num} {123:3,3,124:2}
      for (var i in coupons) {
        not_used_discount = coupons[i]['not_select_coupon'];
        if (coupons[i]['type'] == 4) {
          //运费券
          coupontype = 4;
        } else if (coupons[i]['type'] == 3) {
          var proids = coupons[i]['productids'];
          var is_checked = 0;
          for (var k in allbuydata[bid]['prodata']) {
            var _coupons$i$sy_limit_p;
            var product = allbuydata[bid]['prodata'][k].product;
            var pronum = allbuydata[bid]['prodata'][k].num;
            var proid = product.id;
            var limit_count = coupons[i]['limit_count']; //计次次数
            var limit_perday = coupons[i]['limit_perday']; //每日可使用次数
            var sy_limit_perday = (_coupons$i$sy_limit_p = coupons[i]['sy_limit_perday']) !== null && _coupons$i$sy_limit_p !== void 0 ? _coupons$i$sy_limit_p : 0; //每日剩余可使用次数

            var dknum = 0;
            if (app.inArray(proid, proids)) {
              //产品数量 > 计次次数
              var sy_limit_count = limit_count - coupons[i]['used_count'];
              if (pronum > sy_limit_count) {
                dknum = sy_limit_count;
              } else {
                dknum = pronum;
              }
              //每日限制 开启
              if (limit_perday > 0) {
                dknum = dknum > sy_limit_perday ? sy_limit_perday : dknum;
              }
            }
            if (dknum > 0) {
              var product_sell_price = allbuydata[bid]['prodata'][k]['guige']['sell_price'];
              console.log(product_sell_price, 'product_sell_price');
              if (this.userinfo.discount > 0 && this.userinfo.discount < 10) {
                product_sell_price = Number(product_sell_price * this.userinfo.discount * 0.1);
                console.log(product_sell_price, 'product_sell_price');
              }
              coupon_money += Number(product_sell_price * dknum);
              is_checked = 1;
            }
          }
          if (is_checked == 0) {
            //取消选择
            var thiscouponrids = [];
            var thiscoupons = [];
            for (var i in allbuydata[bid].couponrids) {
              if (allbuydata[bid].couponrids[i] != couponrid) {
                thiscoupons.push(coupons[i]);
                thiscouponrids.push(coupons[i].id);
              }
            }
            allbuydata[bid].couponrids = thiscouponrids;
            allbuydata[bid].coupons = thiscoupons;
          }
        } else if (coupons[i]['type'] == 10) {
          //折扣券
          coupon_money += coupons[i]['thistotalprice'] * (100 - coupons[i]['discount']) * 0.01;
        } else if (coupons[i]['type'] == 11) {
          //线下兑换券，对应商品全部兑换
          //统计已选优惠券和科兑换
          //判断该优惠券中绑定的产品是否在下面商品中，且已兑换xianxia_proid中的数量不足 
          var is_checked = 0;
          var proids = coupons[i]['productids'];
          for (var i in allbuydata[bid]['prodata']) {
            var product = allbuydata[bid]['prodata'][i].product;
            var pronum = allbuydata[bid]['prodata'][i].num;
            var proid = product.id;
            if (app.inArray(proid, proids)) {
              //如果不存在，存入
              if (!xianxia_proid[proid]) {
                xianxia_proid[proid] = 0;
              }
              if (xianxia_proid[proid] < pronum) {
                var newnum = Number(xianxia_proid[proid]) + 1;
                xianxia_proid[proid] = newnum;
                coupon_money += Number(allbuydata[bid]['prodata'][i]['guige']['sell_price']);
                is_checked = 1;
                break;
              } else {
                continue;
              }
            }
          }
          //因为上面已加入了已选择的优惠券中，这里不能再抵扣的删除掉
          if (is_checked == 0) {
            var thiscouponrids = [];
            var thiscoupons = [];
            for (var i in allbuydata[bid].couponrids) {
              if (allbuydata[bid].couponrids[i] != couponrid) {
                thiscoupons.push(coupons[i]);
                thiscouponrids.push(coupons[i].id);
              }
            }
            allbuydata[bid].couponrids = thiscouponrids;
            allbuydata[bid].coupons = thiscoupons;
            app.error('已抵扣完成');
            return;
          }
        } else {
          coupon_money += coupons[i]['couponmoney'];
        }
      }
      if (not_used_discount == 1) {
        var title = '使用该' + this.t('优惠券') + '时，' + this.t('会员') + '折扣不生效';
        uni.showToast({
          title: title,
          icon: 'none',
          duration: 3000,
          success: function success(res) {}
        });
      }
      allbuydata[bid].not_used_discount = not_used_discount;
      allbuydata[bid].coupontype = coupontype;
      allbuydata[bid].coupon_money = coupon_money;
      this.allbuydata = allbuydata;
      if (allbuydata[bid].coupon_peruselimit < 2) {
        this.couponvisible = false;
      }
      this.calculatePrice();
    },
    choosestore: function choosestore(e) {
      var that = this;
      var bid = e.currentTarget.dataset.bid;
      var storekey = e.currentTarget.dataset.index;
      that.storekey = storekey;
      var allbuydata = that.allbuydata;
      var buydata = allbuydata[bid];
      var freightkey = buydata.freightkey;
      allbuydata[bid].freightList[freightkey].storekey = storekey;
      that.allbuydata = allbuydata;
      that.closestore();
    },
    //提交并支付
    topay: function topay(e) {
      var that = this;
      var needaddress = that.needaddress;
      var addressid = this.address && this.address.id ? this.address.id : 0;
      var checkmemid = this.checkMem.id;
      var linkman = this.linkman;
      var pcemail = this.pcemail;
      var tel = this.tel;
      var age = this.age;
      var gender = this.gender;
      var school = this.school;
      var major = this.major;
      var education = that.education;
      var enrol = this.enrol;
      var class_name = this.class_name;
      var faculties_name = this.faculties_name;
      var usescore = this.usescore;
      var frompage = that.opt.frompage ? that.opt.frompage : '';
      var allbuydata = that.allbuydata;
      var moneydecArr = that.moneydecArr;
      var fenqi_type = that.fenqi_type;
      var teammid = that.teamMember ? that.teamMember.id : 0;
      if (that.ishand == 1 && that.hwset && !that.isagree) {
        app.error('请先阅读并同意协议');
        return false;
      }

      //赠送好友，无需填写地址
      var usefreight = true;
      if (!that.usegiveorder) {
        if (needaddress == 0) addressid = 0;
        if (needaddress == 1 && (addressid == undefined || addressid <= 0)) {
          app.error('请选择收货地址');
          return;
        }
        if (this.contact_require == 1 && (linkman.trim() == '' || tel.trim() == '')) {
          return app.error("请填写联系人信息");
        }
        if (this.is_pingce) {
          if (pcemail.trim() == '') {
            return app.error("请填写邮箱");
          }
          if (!/^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/.test(pcemail)) {
            return app.error("邮箱有误，请重填");
          }
          if (age == '') {
            return app.error("请选择生日");
          }
          if (gender == 0) {
            return app.error("请选择性别");
          }
          if (school.trim() == '') {
            return app.error("请填写学校");
          }
          if (major.trim() == '') {
            return app.error("请填写专业");
          }
          if (education.trim() == '') {
            return app.error("请填写学历");
          }
          if (enrol.trim() == '') {
            return app.error("请填写入学年份");
          }
        }
        if (tel.trim() != '' && !app.isPhone(tel)) {
          return app.error("请填写正确的手机号");
        }
      } else {
        usefreight = false;
      }
      var buydata = [];
      for (var i in allbuydata) {
        //赠送好友，无需填写地址
        if (usefreight) {
          var freightkey = allbuydata[i].freightkey;
          if (allbuydata[i].freightList[freightkey].pstimeset == 1 && allbuydata[i].freight_time == '') {
            app.error('请选择' + (allbuydata[i].freightList[freightkey].pstype == 1 ? '取货' : '配送') + '时间');
            return;
          }
          if (allbuydata[i].freightList[freightkey].pstype == 1 || allbuydata[i].freightList[freightkey].pstype == 2 || allbuydata[i].freightList[freightkey].pstype == 5) {
            var storekey = allbuydata[i].freightList[freightkey].storekey;
            var storeid = typeof allbuydata[i].freightList[freightkey].storekey != 'undefined' && allbuydata[i].freightList[freightkey].storedata.length > 0 ? allbuydata[i].freightList[freightkey].storedata[storekey].id : 0;
          } else {
            var storeid = 0;
          }
          if (allbuydata[i].freightList[freightkey].pstype == 11) {
            var type11key = allbuydata[i].type11key;
            if (type11key == 0 || !type11key) {
              app.error('请选择物流');
              return;
            }
            type11key = type11key - 1;
          } else {
            var type11key = 0;
          }
        } else {
          var freightkey = -1;
          var storekey = -1;
          var storeid = 0;
          var type11key = 0;
        }
        var weightids = {};
        for (var j in allbuydata[i].prodata) {
          if (allbuydata[i].prodata[j].product.weighttype > 0 && allbuydata[i].prodata[j].product.weightlist.length > 0) {
            var weightkey = allbuydata[i].prodata[j].product.weightkey;
            weightids[j] = allbuydata[i].prodata[j].product.weightlist[weightkey].id;
          }
        }

        //赠送好友，无需填写地址
        if (usefreight) {
          var formdata_fields = allbuydata[i].freightList[freightkey].formdata;
          var formdata = e.detail.value;
          var newformdata = {};
          var editorFormdata = allbuydata[i].editorFormdata;
          for (var j = 0; j < formdata_fields.length; j++) {
            var thisfield = 'form' + allbuydata[i].bid + '_' + j;
            if (formdata_fields[j].val3 == 1 && (formdata[thisfield] === '' || formdata[thisfield] === undefined || formdata[thisfield] == null || formdata[thisfield].length == 0)) {
              app.alert(formdata_fields[j].val1 + ' 必填');
              return;
            }
            if (formdata_fields[j].key == 'selector') {
              if (formdata_fields[j].val3 == 1 && (Number.isNaN(formdata[thisfield]) || editorFormdata[j] === '' || editorFormdata[j] == 'null' || editorFormdata[j] == undefined)) {
                app.alert(formdata_fields[j].val1 + ' 必选');
                return;
              }
              // if(formdata_fields[j].val3 == 1 && Number.isNaN(formdata[thisfield])){
              // 	app.alert(formdata_fields[j].val1+' 必选');return;
              // }
              formdata[thisfield] = formdata_fields[j].val2[editorFormdata[j]];
            }
            if (j > 0 && formdata_fields[j].val1 == '确认账号' && formdata_fields[j - 1].val1 == '充值账号' && formdata[thisfield] != formdata['form' + allbuydata[i].bid + '_' + (j - 1)]) {
              app.alert('两次输入账号不一致');
              return;
            }
            newformdata['form' + j] = formdata[thisfield];
          }
          if (formdata.name == '') {
            app.alert('请填写姓名');
            return;
          }
          if (formdata.phone == '') {
            app.alert('请填写手机号');
            return;
          }
          if (formdata.email == '') {
            app.alert('请填写邮箱');
            return;
          }
          var freight_id = allbuydata[i].freightList[freightkey].id;
          var freight_time = allbuydata[i].freight_time;
        } else {
          var formdata = {
            'name': '',
            'phone': ''
          };
          var freight_id = 0;
          var freight_time = '';
          var newformdata = {};
        }
        var couponrid = allbuydata[i].couponrids.join(',');
        var buydatatemp = {
          bid: allbuydata[i].bid,
          bidGroup: i,
          prodata: allbuydata[i].prodatastr,
          cuxiaoid: allbuydata[i].cuxiaoid,
          couponrid: couponrid,
          freight_id: freight_id,
          freight_time: freight_time,
          storeid: storeid,
          formdata: newformdata,
          type11key: type11key,
          moneydec_rate: moneydecArr[i] ? moneydecArr[i] : 0,
          weightids: weightids
        };
        if (allbuydata[i].jldata) {
          buydatatemp.jldata = allbuydata[i].jldata;
        }
        if (that.order_change_price) {
          buydatatemp.prodataList = allbuydata[i].prodata;
        }
        if (allbuydata[i].business.invoice) {
          buydatatemp.invoice = allbuydata[i].tempInvoice;
        }
        if (that.business_selfscore == 1) {
          buydatatemp.usescore = that.scoredkdataArr[i].usescore;
        }
        buydata.push(buydatatemp);
      }
      if (that.business_payconfirm && !that.businessinfoConfirm) {
        that.$refs.dialogbusinessinfo.open();
        that.topayparams = e;
        return;
      }
      // name,phone,email必填

      app.showLoading('提交中');
      app.post('ApiShop/createOrder', {
        frompage: frompage,
        buydata: buydata,
        addressid: addressid,
        linkman: linkman,
        pcemail: pcemail,
        tel: tel,
        checkmemid: checkmemid,
        usescore: usescore,
        latitude: that.latitude,
        longitude: that.longitude,
        worknum: that.worknum,
        discount_code_zc: that.discount_code_zc,
        fenqi_type: fenqi_type,
        teammid: teammid,
        isagree_pro: that.isagree_pro,
        devicedata: that.devicedata,
        roomid: that.roomid,
        usetongzheng: that.usetongzheng,
        usercard: that.usercard,
        goldsilvertype: that.goldsilvertype,
        name: formdata && formdata.name ? formdata.name : '',
        phone: formdata && formdata.phone ? formdata.phone : '',
        email: that.email,
        age: that.age,
        gender: that.gender,
        school: that.school,
        major: that.major,
        education: that.education,
        enrol: that.enrol,
        class_name: that.class_name,
        faculties_name: that.faculties_name,
        usededamount: that.usededamount,
        useshopscore: that.useshopscore,
        usegiveorder: that.usegiveorder,
        giveordertitle: that.giveordertitle,
        giveorderpic: that.giveorderpic
      }, function (res) {
        app.showLoading(false);
        if (res.status == 0) {
          //that.showsuccess(res.data.msg);
          app.error(res.msg);
          return;
        }
        //app.error('订单编号：' +res.payorderid);
        if (res.payorderid) app.goto('/pagesExt/pay/pay?id=' + res.payorderid, 'redirectTo');
      });
    },
    showCouponList: function showCouponList(e) {
      this.couponvisible = true;
      this.bid = e.currentTarget.dataset.bid;
    },
    showInvoice: function showInvoice(e) {
      this.invoiceShow = true;
      this.bid = e.currentTarget.dataset.bid;
      var index = e.currentTarget.dataset.index;
      this.invoice_type = this.allbuydata[index].business.invoice_type;
      this.invoice = this.allbuydata[index].tempInvoice;
    },
    changeOrderType: function changeOrderType(e) {
      var that = this;
      var value = e.detail.value;
      if (value == 2) {
        that.name_type_select = 2;
        that.name_type_personal_disabled = true;
      } else {
        that.name_type_personal_disabled = false;
      }
      that.invoice_type_select = value;
    },
    changeNameType: function changeNameType(e) {
      var that = this;
      var value = e.detail.value;
      that.name_type_select = value;
    },
    invoiceFormSubmit: function invoiceFormSubmit(e) {
      var that = this;
      var formdata = e.detail.value;
      if (formdata.invoice_name == '') {
        app.error('请填写抬头名称');
        return;
      }
      if ((formdata.name_type == 2 || formdata.invoice_type == 2) && formdata.tax_no == '') {
        ///^[A-Z0-9]{15}$|^[A-Z0-9]{17}$|^[A-Z0-9]{18}$|^[A-Z0-9]{20}$/
        app.error('请填写公司税号');
        return;
      }
      if (formdata.invoice_type == 2) {
        if (formdata.address == '') {
          app.error('请填写注册地址');
          return;
        }
        if (formdata.tel == '') {
          app.error('请填写注册电话');
          return;
        }
        if (formdata.bank_name == '') {
          app.error('请填写开户银行');
          return;
        }
        if (formdata.bank_account == '') {
          app.error('请填写银行账号');
          return;
        }
      }
      if (formdata.mobile != '') {
        if (!app.isPhone(formdata.mobile)) {
          app.error("手机号码有误，请重填");
          return;
        }
      }
      if (formdata.email != '') {
        if (!/^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/.test(formdata.email)) {
          app.error("邮箱有误，请重填");
          return;
        }
      }
      if (formdata.mobile == '' && formdata.email == '') {
        app.error("手机号和邮箱请填写其中一个");
        return;
      }
      // console.log(formdata);
      var allbuydata = that.allbuydata;
      for (var i in allbuydata) {
        if (allbuydata[i].bid == that.bid) allbuydata[i].tempInvoice = formdata;
      }
      that.allbuydata = allbuydata;
      that.invoiceShow = false;
      // that.loading = true;
      // uni.setStorageSync('temp_invoice_' + that.opt.bid, formdata);
      that.calculatePrice();
    },
    handleClickMask: function handleClickMask() {
      this.couponvisible = false;
      this.cuxiaovisible = false;
      this.type11visible = false;
      this.membervisible = false;
      this.invoiceShow = false;
    },
    showCuxiaoList: function showCuxiaoList(e) {
      this.cuxiaovisible = true;
      this.bid = e.currentTarget.dataset.bid;
    },
    changecx: function changecx(e) {
      var that = this;
      var cxid = e.currentTarget.dataset.id;
      var cxindex = e.currentTarget.dataset.index;
      that.cxid = cxid;
      if (cxid == 0) {
        that.cuxiaoinfo = false;
        return;
      }
      var cuxiaoinfo = that.allbuydata[that.bid].cuxiaolist[cxindex];
      app.post("ApiShop/getcuxiaoinfo", {
        id: cxid
      }, function (res) {
        if (cuxiaoinfo.type == 4 || cuxiaoinfo.type == 5) {
          res.cuxiaomoney = cuxiaoinfo.cuxiaomoney;
        }
        that.cuxiaoinfo = res;
      });
    },
    changecxMulti: function changecxMulti(e) {
      var that = this;
      var cxid = e.currentTarget.dataset.id;
      var cxindex = e.currentTarget.dataset.index;
      that.cuxiaoList.length = 0;
      if (cxid == 0) {
        that.cuxiaoinfo = false;
        that.cxids.length = 0;
        that.cxid = 0;
        return;
      }
      var index = that.cxids.indexOf(cxid);
      if (index === -1) {
        that.cxids.push(cxid);
      } else {
        that.cxids.splice(index);
      }
      if (that.cxids.length == 0) {
        that.cxid = 0;
        that.cuxiaoinfo = false;
        return;
      }
      that.cxid = '';
      var cuxiaoinfo = that.allbuydata[that.bid].cuxiaolist[cxindex];
      app.showLoading();
      app.post("ApiShop/getcuxiaoinfo", {
        id: that.cxids
      }, function (res) {
        // if (cuxiaoinfo.type == 4 || cuxiaoinfo.type == 5) {
        // 	res.cuxiaomoney = cuxiaoinfo.cuxiaomoney
        // }
        app.showLoading(false);
        that.cuxiaoList = res;
      });
    },
    chooseCuxiao: function chooseCuxiao() {
      var that = this;
      var allbuydata = that.allbuydata;
      var bid = that.bid;
      var cxid = that.cxid;
      var cxids = that.cxids;
      if (cxid == 0 || cxid == '') {
        allbuydata[bid].cuxiaoid = '';
        allbuydata[bid].cuxiao_money = 0;
        allbuydata[bid].cuxiaoname = '不使用促销';
        allbuydata[bid].cuxiaonameArr = [];
      } else {
        allbuydata[bid].cuxiaoid = [];
        allbuydata[bid].cuxiao_money = 0;
        allbuydata[bid].cuxiaotype = [];
        allbuydata[bid].cuxiaonameArr = [];
        if (that.cuxiaoList.info && that.cuxiaoList.info.length > 0) {
          for (var i in that.cuxiaoList.info) {
            var cxtype = that.cuxiaoList.info[i].type;
            if (cxtype == 1 || cxtype == 6) {
              //满额立减 满件立减
              allbuydata[bid].cuxiao_money += that.cuxiaoList.info[i]['money'] * -1;
            } else if (cxtype == 2) {
              //满额赠送
              allbuydata[bid].cuxiao_money += 0;
            } else if (cxtype == 3) {
              //加价换购  27.8+15.964+41.4
              allbuydata[bid].cuxiao_money += that.cuxiaoList.info[i]['money'];
            } else if (cxtype == 4 || cxtype == 5) {
              //满额打折 满件打折
              var cuxiaoMoney = 0;
              for (var y in that.allbuydata[bid].cuxiaolist) {
                if (that.cuxiaoList.info[i].id == that.allbuydata[bid].cuxiaolist[y].id) {
                  cuxiaoMoney = that.allbuydata[bid].cuxiaolist[y].cuxiaomoney;
                }
              }
              allbuydata[bid].cuxiao_money += cuxiaoMoney * -1;
            }
            allbuydata[bid].cuxiaoid.push(that.cuxiaoList.info[i].id);
            allbuydata[bid].cuxiaotype.push(cxtype);
            allbuydata[bid].cuxiaonameArr.push(that.cuxiaoList.info[i]['name']);
          }
        } else {
          var cxtype = that.cuxiaoinfo.info.type;
          if (cxtype == 1 || cxtype == 6) {
            //满额立减 满件立减
            allbuydata[bid].cuxiao_money = that.cuxiaoinfo.info['money'] * -1;
          } else if (cxtype == 2) {
            //满额赠送
            allbuydata[bid].cuxiao_money = 0;
          } else if (cxtype == 3) {
            //加价换购  27.8+15.964+41.4
            allbuydata[bid].cuxiao_money = that.cuxiaoinfo.info['money'];
          } else if (cxtype == 4 || cxtype == 5) {
            //var product_price = parseFloat(allbuydata[bid].product_price);
            //var leveldk_money = parseFloat(allbuydata[bid].leveldk_money); //会员折扣
            //var manjian_money = parseFloat(allbuydata[bid].manjian_money); //满减活动
            //满额打折 满件打折
            //allbuydata[bid].cuxiao_money = (1 - that.cuxiaoinfo.info['zhekou'] * 0.1) * (product_price - leveldk_money - manjian_money) * -1;
            allbuydata[bid].cuxiao_money = that.cuxiaoinfo.cuxiaomoney * -1;
          }
          allbuydata[bid].cuxiaoid = cxid;
          allbuydata[bid].cuxiaotype = cxtype;
          allbuydata[bid].cuxiaoname = that.cuxiaoinfo.info['name'];
        }
      }
      this.allbuydata = allbuydata;
      this.cuxiaovisible = false;
      this.calculatePrice();
    },
    showType11List: function showType11List(e) {
      this.type11visible = true;
      this.bid = e.currentTarget.dataset.bid;
    },
    changetype11: function changetype11(e) {
      var that = this;
      var allbuydata = that.allbuydata;
      var bid = that.bid;
      that.type11key = e.currentTarget.dataset.index;
      // console.log(that.type11key)
    },

    chooseType11: function chooseType11(e) {
      var that = this;
      var allbuydata = that.allbuydata;
      var bid = that.bid;
      var type11key = that.type11key;
      if (type11key == -1) {
        app.error('请选择物流');
        return;
      }
      allbuydata[bid].type11key = type11key + 1;
      // console.log(allbuydata[bid].type11key)
      var freightkey = allbuydata[bid].freightkey;
      var freightList = allbuydata[bid].freightList;
      var freight_price = parseFloat(freightList[freightkey].type11pricedata[type11key].price);
      var product_price = parseFloat(allbuydata[bid].product_price);
      // console.log(freightList[freightkey].freeset);
      // console.log(parseFloat(freightList[freightkey].free_price));
      // console.log(product_price);
      if (freightList[freightkey].freeset == 1 && parseFloat(freightList[freightkey].free_price) <= product_price) {
        freight_price = 0;
      }
      allbuydata[bid].freightList[freightkey].freight_price = freight_price;
      this.allbuydata = allbuydata;
      this.type11visible = false;
      this.calculatePrice();
    },
    openMendian: function openMendian(e) {
      var allbuydata = this.allbuydata;
      var bid = e.currentTarget.dataset.bid;
      var freightkey = e.currentTarget.dataset.freightkey;
      var storekey = e.currentTarget.dataset.storekey;
      // this.storekey = storekey?storekey:0;
      var frightinfo = allbuydata[bid].freightList[freightkey];
      var storeinfo = frightinfo.storedata[storekey];
      // console.log(storeinfo)
      app.goto('mendian?id=' + storeinfo.id);
    },
    openLocation: function openLocation(e) {
      var allbuydata = this.allbuydata;
      var bid = e.currentTarget.dataset.bid;
      var freightkey = e.currentTarget.dataset.freightkey;
      var storekey = e.currentTarget.dataset.storekey;
      // this.storekey = storekey?storekey:0;
      var frightinfo = allbuydata[bid].freightList[freightkey];
      var storeinfo = frightinfo.storedata[storekey];
      // console.log(storeinfo)
      var latitude = parseFloat(storeinfo.latitude);
      var longitude = parseFloat(storeinfo.longitude);
      var address = storeinfo.name;
      // address 地址的详细说明  支付宝小程序必填
      uni.openLocation({
        latitude: latitude,
        longitude: longitude,
        name: address,
        scale: 13,
        address: address
      });
    },
    //单图上传
    editorChooseImage: function editorChooseImage(e) {
      var that = this;
      var idx = e.currentTarget.dataset.idx;
      var bid = e.currentTarget.dataset.bid;
      var editorFormdata = that.allbuydata[bid].editorFormdata;
      ;
      if (!editorFormdata) editorFormdata = [];
      var type = e.currentTarget.dataset.type;
      app.chooseImage(function (data) {
        editorFormdata[idx] = data[0];
        // console.log(editorFormdata)
        that.editorFormdata = editorFormdata;
        that.allbuydata[bid].editorFormdata = editorFormdata;
        that.test = Math.random();
      });
    },
    //多图上传，一次最多选8个
    editorChooseImages: function editorChooseImages(e) {
      var that = this;
      var idx = e.currentTarget.dataset.idx;
      var bid = e.currentTarget.dataset.bid;
      var editorFormdata = that.allbuydata[bid].editorFormdata;
      ;
      if (!editorFormdata) editorFormdata = [];
      var type = e.currentTarget.dataset.type;
      app.chooseImage(function (data) {
        var pics = editorFormdata[idx];
        if (!pics) {
          pics = [];
        }
        for (var i = 0; i < data.length; i++) {
          pics.push(data[i]);
        }
        editorFormdata[idx] = pics;
        that.allbuydata[bid].editorFormdata = editorFormdata;
        that.test = Math.random();
      }, 8);
    },
    removeimg: function removeimg(e) {
      var that = this;
      var idx = e.currentTarget.dataset.idx;
      var bid = e.currentTarget.dataset.bid;
      var editorFormdata = this.editorFormdata;
      if (!editorFormdata) editorFormdata = [];
      var type = e.currentTarget.dataset.type;
      var index = e.currentTarget.dataset.index;
      if (type == 'pics') {
        var pics = editorFormdata[idx];
        pics.splice(index, 1);
        editorFormdata[idx] = pics;
        that.allbuydata[bid].editorFormdata = editorFormdata;
        that.test = Math.random();
      } else {
        editorFormdata[idx] = '';
        that.editorFormdata = editorFormdata;
        that.test = Math.random();
        that.allbuydata[bid].editorFormdata = that.editorFormdata;
      }
    },
    editorBindPickerChange: function editorBindPickerChange(e) {
      var that = this;
      var bid = e.currentTarget.dataset.bid;
      var idx = e.currentTarget.dataset.idx;
      var val = e.detail.value;
      var editorFormdata = that.allbuydata[bid].editorFormdata;
      if (!editorFormdata) editorFormdata = [];
      editorFormdata[idx] = val;
      that.allbuydata[bid].editorFormdata = editorFormdata;
      that.test = Math.random();
    },
    editorBindPickerChangeAge: function editorBindPickerChangeAge(e) {
      var val = e.detail.value;
      this.age = val;
    },
    bindPickerChangeSex: function bindPickerChangeSex(e) {
      var val = e.detail.value;
      this.gender = val == 0 ? '男' : '女';
    },
    bindPickerChangeEdu: function bindPickerChangeEdu(e) {
      var val = e.detail.value;
      this.education = this.edulist[val];
    },
    editorBindPickerChangeEnrol: function editorBindPickerChangeEnrol(e) {
      var val = e.detail.value;
      console.log(val);
      this.enrol = val;
    },
    showMemberList: function showMemberList(e) {
      this.membervisible = true;
    },
    regionchange2: function regionchange2(e) {
      var value = e.detail.value;
      // console.log(value[0].text + ',' + value[1].text + ',' + value[2].text);
      this.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text;
    },
    memberSearch: function memberSearch() {
      var that = this;
      // console.log(that.regiondata)
      app.post('ApiShop/memberSearch', {
        diqu: that.regiondata
      }, function (res) {
        app.showLoading(false);
        if (res.status == 0) {
          app.error(res.msg);
          return;
        }
        var data = res.memberList;
        that.memberList = data;
      });
    },
    checkMember: function checkMember(e) {
      var that = this;
      that.checkMem = e.currentTarget.dataset.info;
      this.membervisible = false;
    },
    showmemberinfo: function showmemberinfo(e) {
      var that = this;
      var mid = e.currentTarget.dataset.mid;
      app.showLoading('提交中');
      app.post('ApiShop/getmemberuplvinfo', {
        mid: mid
      }, function (res) {
        app.showLoading(false);
        if (res.status == 0) {
          app.error(res.msg);
          return;
        }
        that.selectmemberinfo = res.info;
        that.memberinfovisible = true;
      });
    },
    memberinfoClickMask: function memberinfoClickMask() {
      this.memberinfovisible = false;
    },
    businessinfoClose: function businessinfoClose() {
      this.$refs.dialogbusinessinfo.close();
    },
    businessinfoOk: function businessinfoOk() {
      this.businessinfoConfirm = true;
      this.$refs.dialogbusinessinfo.close();
      this.topay(this.topayparams);
    },
    doStoreShowAll: function doStoreShowAll() {
      this.storeshowall = true;
    },
    showglass: function showglass(e) {
      var that = this;
      var grid = e.currentTarget.dataset.grid;
      var index = e.currentTarget.dataset.index;
      var index2 = e.currentTarget.dataset.index2;
      // console.log(that.glassrecordlist)
      if (that.glassrecordlist.length < 1) {
        //没有数据 就重新请求
        that.getglassrecord();
      } else {
        that.isshowglass = true;
      }
      that.curindex = index;
      that.curindex2 = index2;
      that.grid = grid;
    },
    getglassrecord: function getglassrecord(e) {
      var that = this;
      if (that.hasglassproduct == 1) {
        that.loading = true;
        app.post('ApiGlass/myrecord', {
          pagenum: 1,
          listrow: 100
        }, function (res) {
          that.loading = false;
          var datalist = res.data;
          that.glassrecordlist = datalist;
          that.isshowglass = true;
        });
      }
    },
    hideglass: function hideglass(e) {
      var that = this;
      that.isshowglass = false;
    },
    chooseglass: function chooseglass(e) {
      var that = this;
      var gindex = e.detail.value;
      var allbuydata = that.allbuydata;
      var grid = that.grid;
      var index = that.curindex;
      var index2 = that.curindex2;
      var glassrecordlist = that.glassrecordlist;
      var product = allbuydata[index]['prodata'][index2].product;
      var prodata = allbuydata[index]['prodata'];
      var prodataArr = [];
      var sid = glassrecordlist[gindex].id;
      for (var i in prodata) {
        var pid = prodata[i].product.id;
        var guigeid = prodata[i].guige.id;
        var num = prodata[i].num;
        var pgrid = 0;
        if (prodata[i].product.has_glassrecord == 1 && prodata[i].product.glassrecord) {
          var pgrid = prodata[i].product.glassrecord.id;
        }
        if (index2 == i) {
          if (grid == sid) {
            product.glassrecord = {};
            product.has_glassrecord = 0;
            that.grid = 0;
            pgrid = 0;
          } else {
            product.glassrecord = glassrecordlist[gindex];
            product.has_glassrecord = 1;
            that.grid = glassrecordlist[gindex].id;
            pgrid = glassrecordlist[gindex].id;
          }
        }
        var pchild = pid + ',' + guigeid + ',' + num + ',' + pgrid;
        prodataArr.push(pchild);
      }
      var prodatastr = prodataArr.join('-');
      that.allbuydata[index]['prodata'][index2]['product'] = product;
      that.allbuydata[index]['prodatastr'] = prodatastr;
      that.isshowglass = false;
    },
    moneydk: function moneydk(e) {
      var that = this;
      var moneydec = that.moneydec;
      if (moneydec) {
        var bid = e.currentTarget.dataset.bid;
        var type = e.currentTarget.dataset.type;
        //设置积分抵扣类型 0：系统设置比例 1：商品存在单独设置固定金额
        var moneydecArrType = that.moneydecArrType;
        moneydecArrType[bid] = type;
        that.moneydecArrType = moneydecArrType;
        var rate = e.currentTarget.dataset.rate - 0;
        var money = e.currentTarget.dataset.money - 0;
        var moneydecArr = that.moneydecArr;
        if (moneydecArr[bid] && moneydecArr[bid] > 0) {
          moneydecArr[bid] = 0;
        } else {
          if (type == 1) {
            moneydecArr[bid] = parseFloat(money);
          } else {
            moneydecArr[bid] = parseFloat(rate);
          }
        }
        that.moneydecArr = moneydecArr;
        this.calculatePrice();
      }
    },
    showstore: function showstore(e) {
      var that = this;
      var storedata = e.currentTarget.dataset.storedata;
      if (storedata && storedata.length > 0) {
        var len = storedata.length;
        for (var i = 0; i < len; i++) {
          storedata[i]['searchkey'] = i;
        }
      }
      var storefreightkey = e.currentTarget.dataset.storefreightkey;
      var storekey = e.currentTarget.dataset.storekey;
      var storebid = e.currentTarget.dataset.storebid;
      that.storedata = storedata ? storedata : '';
      that.storekey = storekey ? storekey : 0;
      that.storefreightkey = storefreightkey ? storefreightkey : 0;
      that.storebid = storebid ? storebid : 0;
      that.storevisible = true;
    },
    closestore: function closestore(e) {
      var that = this;
      that.storevisible = false;
    },
    inputStorename: function inputStorename(e) {
      var that = this;
      that.storename = e.detail.value;
    },
    searchStore: function searchStore() {
      var that = this;
      var storename = that.storename;
      var storedata = that.storedata;
      if (!storename) {
        if (storedata && storedata.length > 0) {
          var len = storedata.length;
          for (var i = 0; i < len; i++) {
            storedata[i]['searchkey'] = i;
          }
        }
      } else {
        if (storedata && storedata.length > 0) {
          var len = storedata.length;
          for (var i = 0; i < len; i++) {
            //查询位置
            var namestr = storedata[i]['name'];
            var pos = namestr.indexOf(storename);
            if (pos >= 0) {
              storedata[i]['searchkey'] = i;
            } else {
              storedata[i]['searchkey'] = -1;
            }
          }
        }
      }
      that.storedata = storedata;
    },
    inputCouponCode: function inputCouponCode(e) {
      var that = this;
      var index = e.currentTarget.dataset.index;
      var allbuydata = that.allbuydata;
      var allbuydatawww = that.allbuydatawww;
      that.discount_code_zc = e.detail.value;
      allbuydata[index].discount_code_zc_status = false;
      if (e.detail.value) {
        app.post("ApiShop/checkDiscountCodeZc", {
          discount_code_zc: e.detail.value
        }, function (res) {
          console.log(res);
          if (res.status == 1) {
            allbuydata[index].discount_code_zc_status = true;
          }
          that.allbuydata = allbuydata;
          that.calculatePrice();
        });
      } else {
        that.allbuydata = allbuydata;
        that.calculatePrice();
      }
    },
    isagreeChange: function isagreeChange(e) {
      var val = e.detail.value;
      if (val.length > 0) {
        this.isagree = true;
      } else {
        this.isagree = false;
      }
    },
    showxieyiFun: function showxieyiFun() {
      this.showxieyi = true;
    },
    hidexieyi: function hidexieyi() {
      this.showxieyi = false;
      this.isagree = true;
    },
    showTeamMemberList: function showTeamMemberList() {
      var that = this;
      app.showLoading();
      var tmid = that.tmid;
      app.post('ApiShop/getTeamMemberList', {
        tmid: tmid
      }, function (res) {
        app.showLoading(false);
        if (res.status == 0) {
          app.error(res.msg);
          return;
        }
        that.teamMemberList = res.data;
        that.memberOtherShow = true;
      });
    },
    handlMememberOther: function handlMememberOther() {
      this.memberOtherShow = false;
    },
    checkTeamMember: function checkTeamMember(e) {
      var that = this;
      that.teamMember = e.currentTarget.dataset.info;
      this.memberOtherShow = false;
    },
    showproductxieyi: function showproductxieyi(index) {
      var that = this;
      var product_xieyi = that.product_xieyi;
      that.proxieyi_content = product_xieyi[index].content;
      that.showproxieyi = 1;
    },
    isagreeProChange: function isagreeProChange(e) {
      var val = e.detail.value;
      if (val.length > 0) {
        this.isagree_pro = true;
      } else {
        this.isagree_pro = false;
      }
    },
    hideproxieyi: function hideproxieyi() {
      this.showproxieyi = 0;
      this.isagree_pro = true;
    },
    dedamountdk: function dedamountdk() {
      this.usededamount = !this.usededamount;
      this.calculatePrice();
    },
    //产品积分抵扣
    shopscoredk: function shopscoredk(e) {
      var useshopscore = e.detail.value[0];
      if (!useshopscore) useshopscore = 0;
      this.useshopscore = useshopscore;
      this.calculatePrice();
    },
    changeusegiveorder: function changeusegiveorder() {
      this.usegiveorder = !this.usegiveorder;
      this.calculatePrice();
    },
    inputGiveordertitle: function inputGiveordertitle(e) {
      this.giveordertitle = e.detail.value;
    },
    uploadimg2: function uploadimg2(e) {
      var that = this;
      var pernum = parseInt(e.currentTarget.dataset.pernum);
      if (!pernum) pernum = 1;
      var field = e.currentTarget.dataset.field;
      var pics = that[field];
      if (!pics) pics = [];
      app.chooseImage(function (urls) {
        for (var i = 0; i < urls.length; i++) {
          pics.push(urls[i]);
        }
        if (field == 'giveorderpic') that.giveorderpic = pics[0];
      }, pernum);
    },
    removeimg2: function removeimg2(e) {
      var that = this;
      var index = e.currentTarget.dataset.index;
      var field = e.currentTarget.dataset.field;
      if (field == 'giveorderpic') {
        that.giveorderpic = '';
      }
    },
    gotoMendianList: function gotoMendianList() {
      if (this.mendian_change == true) {
        app.goto('/pagesB/mendianup/list');
      }
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 4834:
/*!*****************************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/pagesB/shop/buy.vue?vue&type=style&index=0&lang=css& ***!
  \*****************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=style&index=0&lang=css& */ 4835);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_buy_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 4835:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!/Users/<USER>/Downloads/tcphp/uniapp/pagesB/shop/buy.vue?vue&type=style&index=0&lang=css& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[4828,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pagesB/shop/buy.js.map