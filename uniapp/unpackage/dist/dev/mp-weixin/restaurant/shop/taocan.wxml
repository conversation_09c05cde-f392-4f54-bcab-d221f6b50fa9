<view class="container"><block wx:if="{{isload}}"><block><scroll-view class="scroll-class" scroll-y="{{true}}"><block wx:if="{{tacanSellPoint}}"><view class="options-view"><view class="option-title"><text class="text-name">简介</text></view><view class="shop-list flex-row" style="font-size:28rpx;color:#666;">{{''+tacanSellPoint+''}}</view></view></block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="options-view"><view class="option-title"><text class="text-name">{{item.$orig.category_name+" "+(item.$orig.type==2?'(请选择'+item.$orig.selectnum+'份)':'')}}</text></view><view class="shop-list flex-row"><block wx:for="{{item.l0}}" wx:for-item="items" wx:for-index="indexs" wx:key="indexs"><block><view data-event-opts="{{[['tap',[['selectProduct',[index,'$0','$1'],[[['datalist','',index]],[['datalist','',index],['prolist','',indexs]]]]]]]}}" class="options-shop flex-col" style="{{'border-color:'+(items.$orig.checked?items.m0:'#f7f7f7')+';'}}" bindtap="__e"><view class="shop-image flex-x-center flex-y-center"><image src="{{items.$orig.pic}}"></image></view><view class="shop-info-view" style="{{'background-color:'+(item.$orig.type==0?'#fff':'')+';'}}"><view class="shop-name">{{items.$orig.proname+" x "+(items.$orig.num||1)}}</view><block wx:if="{{items.$orig.guigedata&&items.$orig.checked}}"><block><view class="gg-name">(默认规格)</view></block></block><block wx:else><block><block wx:if="{{items.$orig.ggname!='默认规格'}}"><view class="gg-name">{{"("+items.$orig.ggname+")"}}</view></block></block></block><block wx:if="{{item.$orig.type==2}}"><view class="addnum"><block wx:if="{{items.$orig.num>0}}"><view data-event-opts="{{[['tap',[['choiceMinusPlus',[index,'$0','$1','Minus'],[[['datalist','',index]],[['datalist','',index],['prolist','',indexs]]]]]]]}}" class="minus" bindtap="__e">-</view></block><block wx:if="{{items.$orig.num>0}}"><text class="i">{{items.$orig.num}}</text></block><block wx:if="{{items.m1}}"><view data-event-opts="{{[['tap',[['choiceMinusPlus',[index,'$0','$1','Plus'],[[['datalist','',index]],[['datalist','',index],['prolist','',indexs]]]]]]]}}" class="plus" bindtap="__e">+</view></block><block wx:else><view class="plus no">+</view></block></view></block></view><block wx:if="{{items.m2}}"><view class="jia-price">{{'+'+items.$orig.add_price+''}}</view></block><block wx:if="{{items.$orig.checked&&item.$orig.type!=2}}"><view class="choose-view" style="{{'background:'+(items.m3)+';'+('border:'+(items.m4)+';')}}"><image src="{{pre_url+'/static/img/checkd.png'}}"></image></view></block><block wx:if="{{items.g0}}"><block><view data-event-opts="{{[['tap',[['customizedChange',[index,indexs,'$0','$1'],[[['datalist','',index,'type']],[['datalist','',index],['prolist','',indexs]]]]]]]}}" class="gg-name-but" style="{{'border-color:'+(items.m5)+';'+('background:'+('rgb('+items.m6+',0.8'+')')+';')}}" bindtap="__e">定制规格</view></block></block></view></block></block></view></view></block></block><view class="occupy-box" style="{{'height:'+('calc(env(safe-area-inset-bottom) + '+footerheight+'px)')+';'}}"></view></scroll-view><view class="{{['footer','flex-col',menuindex>-1?'tabbarbot':'']}}"><scroll-view class="scrollview-class" scroll-y="{{true}}"><view class="select-display"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index"><block><view data-event-opts="{{[['tap',[['productCustom',['$0',index],[[['addcartList','',index]]]]]]]}}" class="product-pit-view" style="{{'border-color:'+(item.m7)+';'}}" bindtap="__e"><view class="select-shop-image"><image src="{{item.$orig.pic}}"></image></view><block wx:if="{{item.g1}}"><view class="num-view" style="{{'background:'+('#fff')+';'+('border:'+('1px solid '+item.m8)+';')+('color:'+(item.m9)+';')+('font-size:'+('18rpx')+';')}}">定制</view></block><block wx:else><view class="num-view" style="{{'background:'+('linear-gradient(90deg,'+item.m10+' 0%,rgba('+item.m11+',0.8) 100%)')+';'}}">x1</view></block></view></block></block></view></scroll-view><block wx:if="{{addcartListLength==$root.g2}}"><view data-event-opts="{{[['tap',[['selectquantities',[true]]]]]}}" class="addcart-view" style="{{'background:'+($root.m12)+';'}}" bindtap="__e">加入购物车</view></block><block wx:else><view data-event-opts="{{[['tap',[['tispChange',['$event']]]]]}}" class="addcart-view please-select" bindtap="__e">请选择餐品</view></block></view></block></block><uni-popup class="vue-ref" style="height:88vh;" vue-id="218a4afa-1" id="popup" type="bottom" data-ref="popup" bind:__l="__l" vue-slots="{{['default']}}"><view class="hotelpopup__content"><view data-event-opts="{{[['tap',[['popupClose',['$event']]]]]}}" class="popup-close" bindtap="__e"><image src="{{pre_url+'/static/img/hotel/popupClose.png'}}"></image></view><scroll-view style="height:auto;max-height:80vh;" scroll-y="{{true}}"><view class="custom-image-view"><image src="{{customShopRes.pic}}"></image></view><block wx:if="{{$root.g3}}"><view class="selectproduct-list"><scroll-view style="width:100%;white-space:nowrap;" scroll-x="{{true}}"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index"><block><view data-event-opts="{{[['tap',[['shopSwitch',[index]]]]]}}" class="options-list" style="{{(item.$orig.popupCheck?'color:'+item.m13+';'+'border-color:'+item.m14:'')}}" bindtap="__e">{{'第 '+(index+1)+' 份'}}</view></block></block></scroll-view></view></block><view class="customproname-view">{{''+customShopRes.proname+''}}</view><view class="gglist-view flex flex-col"><block wx:for="{{$root.l5}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="gglist-view-options flex-col"><view class="name">{{item.$orig.title}}</view><view class="item flex flex-y-center"><block wx:for="{{item.l4}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><block><view class="item2" style="{{(customShopRes.ggselected[item.$orig.k]==item2.$orig.k?'color:'+item2.m15+';'+'border-color:'+item2.m16:'')}}" data-itemk="{{item.$orig.k}}" data-idx="{{item2.$orig.k}}" data-event-opts="{{[['tap',[['ggchange',['$event']]]]]}}" bindtap="__e">{{item2.$orig.title}}</view></block></block></view></view></block></view></scroll-view><view class="popup-but-view flex flex-col"><view class="page_content">{{"已选规格: "+nowguige.ggname}}</view><view data-event-opts="{{[['tap',[['popupClose',['$event']]]]]}}" class="but-class" style="{{('background: linear-gradient(90deg,rgba('+$root.m17+',1) 0%,rgba('+$root.m18+',1) 100%)')}}" bindtap="__e">确定</view></view></view></uni-popup><block wx:if="{{loading}}"><loading vue-id="218a4afa-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="218a4afa-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><wxxieyi vue-id="218a4afa-4" bind:__l="__l"></wxxieyi></view>