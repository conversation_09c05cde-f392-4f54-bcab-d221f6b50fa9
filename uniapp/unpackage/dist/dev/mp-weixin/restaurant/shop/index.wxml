<view class="container"><block wx:if="{{isload}}"><block><view class="view-show"><block wx:if="{{sysset.mode==1&&sysset.is_loc_business==1}}"><block><view class="header"><view class="header_title flex-y-center flex-bt"><view data-event-opts="{{[['tap',[['toBusiness',['$event']]]]]}}" class="flex-y-center shop_title" bindtap="__e">{{''+business.name+''}}<image class="header_detail" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view><image class="header_serach" src="{{pre_url+'/static/img/search_ico.png'}}" data-event-opts="{{[['tap',[['goSearch',['$event']]]]]}}" bindtap="__e"></image></view><view class="header_address">{{'距离你 '+juli+''}}</view></view><view class="topbannerbg" style="{{(business.pic?'background:url('+business.pic+') 100%;background-size:100% auto;':'')}}"></view></block></block><block wx:if="{{(sysset.bar_table_order||sysset.bar_table_order==1)&&mdinfo.name}}"><block><view class="header" style="margin-bottom:25rpx;"><view class="header_title flex-y-center flex-bt"><view class="flex-y-center shop_title">{{''+mdinfo.name+''}}</view></view><view class="header_address flex-y-center flex-bt"><text>{{"距离你 "+mdjuli}}</text><block wx:if="{{!checkState}}"><view data-event-opts="{{[['tap',[['checkClick',[true]]]]]}}" class="flex-y-center" bindtap="__e">查看<image class="header_detail header_down" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:if="{{checkState}}"><view data-event-opts="{{[['tap',[['checkClick',[false]]]]]}}" class="flex-y-center" bindtap="__e">收起<image class="header_detail header_top" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block></view><block wx:if="{{checkState}}"><view class="header_content"><view class="header_lable">门店信息</view><view>地址：<block wx:if="{{mdinfo.address}}"><text>{{mdinfo.province+mdinfo.city+mdinfo.district+mdinfo.address}}</text></block><block wx:else><text>暂无</text></block></view><view>电话：<block wx:if="{{mdinfo.tel}}"><text>{{mdinfo.tel}}</text></block><block wx:else><text>暂无</text></block></view></view></block></view></block></block><block wx:if="{{(sysset.mode!=1||sysset.is_loc_business!=1)&&(!sysset.bar_table_order||sysset.bar_table_order==0)}}"><block><view class="topbannerbg" style="{{(business.pic?'background:url('+business.pic+') 100%;background-size:100% auto;':'')}}"></view><view class="topbannerbg2"></view><view class="topbanner"><view class="left"><image class="img" src="{{business.logo}}"></image></view><view class="right"><view class="f1">{{business.name+''}}<block wx:if="{{table.name&&table.show_table_name}}"><text>{{"("+table.name+")"}}</text></block></view><view class="f2">{{business.desc}}</view></view><view class="searchIcon"><view class="searchIcon-options" style="margin-right:25rpx;"><image src="{{pre_url+'/static/img/sousuo.png'}}" data-event-opts="{{[['tap',[['goSearch',['$event']]]]]}}" bindtap="__e"></image></view><view class="searchIcon-options" data-url="{{'orderlist?bid='+bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/dingdan.png'}}"></image></view></view></view></block></block><block wx:if="{{sysset&&sysset.designer_content}}"><block><dp vue-id="60d21e39-1" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></block></block><view class="{{['container','body',menuindex>-1?'body_status':'']}}" style="{{($root.g0?'margin-top:0rpx':'')}}"><view class="navtab"><view class="{{['item',st==0?'on':'']}}" data-st="0" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e"><block wx:if="{{sysset.diancan_text}}"><text>{{sysset.diancan_text}}</text></block><block wx:else><text>点餐</text></block><view class="after" style="{{'background:'+($root.m0)+';'}}"></view></view><block wx:if="{{sysset.business_info_show}}"><view class="{{['item',st==1?'on':'']}}" data-st="1" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e">商家信息<view class="after" style="{{'background:'+($root.m1)+';'}}"></view></view></block><block wx:if="{{sysset.comment_show}}"><view class="{{['item',st==2?'on':'']}}" data-st="2" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e">评价<view class="after" style="{{'background:'+($root.m2)+';'}}"></view></view></block><view class="my-order-class" style="{{'background:'+('rgba('+$root.m3+',0.12)')+';'+('color:'+($root.m4)+';')}}" data-url="{{'orderlist?bid='+bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">我的订单</view></view><block wx:if="{{st==0}}"><view class="content flex" style="{{'height:'+('calc(100% - '+(menuindex>-1?320:210)+'rpx)')+';'}}"><scroll-view class="{{['nav_left',menuindex>-1?'tabbarbot':'']}}" scrollWithAnimation="{{animation}}" scroll-y="true"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['nav_left_items',index===currentActiveIndex?'active':'']}}" data-root-item-id="{{item.$orig.id}}" data-root-item-index="{{index}}" data-event-opts="{{[['tap',[['clickRootItem',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+(item.m5)+';'}}"></view><block wx:if="{{item.$orig.pic}}"><image src="{{item.$orig.pic}}" mode="widthFix"></image></block><view>{{item.$orig.name}}</view><block wx:if="{{numCat[item.$orig.id]>0}}"><view class="cartnum" style="{{'background:'+(item.m6)+';'}}">{{''+numCat[item.$orig.id]+''}}</view></block><block wx:if="{{numCat[item.$orig.id]<=0&&item.$orig.tag}}"><view class="carttag" style="{{'background:'+('rgba('+item.m7+',0.12)')+';'+('color:'+(item.m8)+';')}}">{{item.$orig.tag}}</view></block></view></block></block></scroll-view><view class="nav_right"><view class="nav_right-content"><block wx:if="{{$root.g1}}"><scroll-view class="{{['detail-list',menuindex>-1?'tabbarbot':'']}}" scrollIntoView="{{scrollToViewId}}" scrollWithAnimation="{{animation}}" scroll-y="true" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e"><block wx:for="{{$root.l2}}" wx:for-item="detail" wx:for-index="index" wx:key="index"><view class="classification-detail-item"><view class="head" data-id="{{detail.$orig.id}}" id="{{'detail-'+detail.$orig.id}}"><view class="txt">{{detail.$orig.name}}</view></view><view class="product-itemlist"><block wx:for="{{detail.l1}}" wx:for-item="item" wx:for-index="indexs" wx:key="indexs"><view class="{{['item',item.$orig.stock<=0||item.$orig.stock_daily<=item.$orig.sales_daily?'soldout':'']}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['toSelect',['$event']]]]]}}" bindtap="__e"><view class="product-pic" data-id="{{item.$orig.id}}" data-name="{{item.$orig.name}}" data-producttype="{{item.$orig.product_type}}" data-event-opts="{{[['tap',[['toDetail',['$event']]]]]}}" bindtap="__e"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image><view class="overlay"><view class="text">售罄</view></view></view><view class="product-info" data-id="{{item.$orig.id}}" data-name="{{item.$orig.name}}" data-producttype="{{item.$orig.product_type}}" data-event-opts="{{[['tap',[['toDetail',['$event']]]]]}}" bindtap="__e"><view class="p1"><text>{{item.$orig.name}}</text></view><block wx:if="{{item.$orig.product_type&&item.$orig.product_type==1}}"><view style="color:#999;font-size:20rpx;">称重商品，总价以实际重量为准</view></block><view class="p2"><text class="t1" style="{{'color:'+(detail.m9)+';'}}"><text style="font-size:20rpx;padding-right:1px;">￥</text>{{item.$orig.sell_price+''}}<block wx:if="{{item.$orig.product_type&&item.$orig.product_type==1}}"><text style="font-size:20rpx;">/斤</text></block><block wx:if="{{item.$orig.price_show&&item.$orig.price_show_text}}"><text style="margin:0 15rpx;font-size:24rpx;font-weight:400;">{{item.$orig.price_show_text}}</text></block></text><block wx:if="{{item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view><block wx:if="{{item.$orig.price_show&&item.$orig.price_show==1}}"><view style="line-height:46rpx;"><text style="font-size:26rpx;">{{"￥"+item.$orig.sell_putongprice}}</text></view></block><block wx:if="{{item.g2}}"><view><block wx:for="{{item.$orig.priceshows}}" wx:for-item="item2" wx:for-index="index2"><view style="line-height:46rpx;"><text style="font-size:26rpx;">{{"￥"+item2.sell_price}}</text><text style="margin-left:15rpx;font-size:22rpx;font-weight:400;">{{item2.price_show_text}}</text></view></block></view></block><view class="p3"><block wx:if="{{item.$orig.sales>0}}"><view class="p3-1"><text style="overflow:hidden;">{{"已售"+item.$orig.sales+"件"}}</text></view></block><block wx:if="{{item.$orig.limit_start>0}}"><view class="p3-1"><text style="overflow:hidden;">{{item.$orig.limit_start+"件起售"}}</text></view></block></view><block wx:if="{{item.$orig.stock>0&&item.$orig.stock_daily>item.$orig.sales_daily&&item.$orig.product_type!=2}}"><view class="addnum"><block wx:if="{{numtotal[item.$orig.id]>0}}"><view class="minus" data-num="-1" data-proid="{{item.$orig.id}}" data-stock="{{item.$orig.stock}}" data-havejl="{{item.$orig.have_jialiao}}" data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" catchtap="__e">-</view></block><block wx:if="{{numtotal[item.$orig.id]>0}}"><text class="i">{{numtotal[item.$orig.id]}}</text></block><block wx:if="{{item.$orig.ggcount>1||item.$orig.have_jialiao==1}}"><view class="plus" data-proid="{{item.$orig.id}}" data-stock="{{item.$orig.stock}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e">+</view></block><block wx:else><view class="plus" data-num="1" data-proid="{{item.$orig.id}}" data-ggid="{{item.$orig.gglist[0].id}}" data-stock="{{item.$orig.stock}}" data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" catchtap="__e">+</view></block></view></block><block wx:if="{{item.$orig.product_type==2}}"><view data-event-opts="{{[['tap',[['selectGuige',['$0'],[[['datalist','',index],['prolist','',indexs]]]]]]]}}" class="select-guige" style="{{'background:'+(item.m10)+';'}}" bindtap="__e">选套餐</view></block></view></view></block></view></view></block></scroll-view></block><block wx:if="{{nodata}}"><nodata vue-id="60d21e39-2" bind:__l="__l"></nodata></block></view></view></view></block><block wx:if="{{st==1}}"><view class="content1"><view class="item flex-col"><text class="t1">联系电话</text><view class="t2"><block wx:if="{{business.tel}}"><text>{{business.tel}}</text></block><block wx:else><text>暂无</text></block></view></view><view class="item flex-col"><text class="t1">商家地址</text><view class="t2"><block wx:if="{{business.address}}"><text>{{business.address}}</text></block><block wx:else><text>暂无</text></block></view></view><view class="item flex-col"><text class="t1">商家简介</text><view class="t2"><block wx:if="{{business.desc}}"><text>{{business.desc}}</text></block><block wx:else><text>暂无</text></block></view></view><view class="item flex-col"><text class="t1">营业时间</text><text class="t2">{{sysset.start_hours+" 至 "+sysset.end_hours}}</text></view><block wx:if="{{$root.g3}}"><view class="item flex-col"><text class="t1">证照公示</text><view class="flex t2" style="flex-wrap:wrap;padding-top:20rpx;" id="content_picpreview"><block wx:for="{{business.zhengming}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></view></view></block></view></block><block wx:if="{{st==2}}"><view class="content2"><view class="comment"><block wx:if="{{$root.g4>0}}"><block><block wx:for="{{commentlist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><image class="t1" src="{{item.headimg}}"></image><view class="t2">{{item.nickname}}</view><view class="flex1"></view><view class="t3"><block wx:for="{{[0,1,2,3,4]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><image class="img" src="{{pre_url+'/static/img/star'+(item.score>item2?'2native':'')+'.png'}}"></image></block></view></view><view style="color:#777;font-size:22rpx;">{{item.createtime}}</view><view class="f2"><text class="t1">{{item.content}}</text><view class="t2"><block wx:if="{{item.content_pic!=''}}"><block><block wx:for="{{item.content_pic}}" wx:for-item="itemp" wx:for-index="index" wx:key="index"><block><view data-url="{{itemp}}" data-urls="{{item.content_pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"><image src="{{itemp}}" mode="widthFix"></image></view></block></block></block></block></view></view><block wx:if="{{item.reply_content}}"><view class="f3"><view class="arrow"></view><view class="t1">{{"商家回复："+item.reply_content}}</view></view></block></view></block></block></block><block wx:else><block><nodata data-custom-hidden="{{!(comment_nodata)}}" vue-id="60d21e39-3" bind:__l="__l"></nodata></block></block></view></view></block></view></view><block wx:if="{{buydialogShow}}"><buydialog-restaurant vue-id="60d21e39-4" proid="{{proid}}" menuindex="{{menuindex}}" btntype="1" needaddcart="{{false}}" controller="ApiRestaurantShop" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]],['^addcart',[['afteraddcart']]]]}}" bind:buydialogChange="__e" bind:addcart="__e" bind:__l="__l"></buydialog-restaurant></block><view class="{{['footer','flex',menuindex>-1?'tabbarbot':'']}}"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="cart_ico" style="{{'background:'+('linear-gradient(0deg,'+$root.m11+' 0%,rgba('+$root.m12+',0.8) 100%)')+';'}}" catchtap="__e"><image class="img" src="{{pre_url+'/static/img/cart.png'}}"></image><block wx:if="{{cartList.total>0}}"><view class="cartnum" style="{{'background:'+($root.m13)+';'}}">{{cartList.total+''}}</view></block></view><view class="text1">合计</view><view class="text2 flex1" style="{{'color:'+($root.m14)+';'}}"><text style="font-size:20rpx;">￥</text>{{cartList.totalprice}}</view><view data-event-opts="{{[['tap',[['gopay',['$event']]]]]}}" class="op" style="{{'background:'+('linear-gradient(270deg,'+$root.m15+' 0%,rgba('+$root.m16+',0.8) 100%)')+';'}}" bindtap="__e">下单</view><block wx:if="{{addmsgShow&&addmsg!=''}}"><view class="addmsg">{{'小伙伴点了 '+addmsg+''}}<view class="sanjiao"></view></view></block></view><block wx:if="{{cartListShow}}"><view class="{{['popup__container',menuindex>-1?'tabbarbot':'']}}" style="margin-bottom:100rpx;"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="{{['popup__overlay',menuindex>-1?'tabbarbot':'']}}" style="margin-bottom:100rpx;" catchtap="__e"></view><view class="popup__modal" style="min-height:400rpx;padding:0;"><view class="popup__title" style="border-bottom:1px solid #EFEFEF;"><text class="popup__title-text" style="color:#323232;font-weight:bold;font-size:32rpx;">购物车</text><view data-event-opts="{{[['tap',[['clearShopCartFn',['$event']]]]]}}" class="popup__close flex-y-center" style="color:#999999;font-size:24rpx;" catchtap="__e"><image style="width:24rpx;height:24rpx;margin-right:6rpx;" src="{{pre_url+'/static/img/del.png'}}"></image>清空</view></view><view class="popup__content" style="padding:0;"><scroll-view class="prolist" scroll-y="{{true}}"><block wx:for="{{$root.l3}}" wx:for-item="cart" wx:for-index="index" wx:key="index"><block><view class="proitem"><image class="pic flex0" src="{{cart.$orig.guige.pic?cart.$orig.guige.pic:cart.$orig.product.pic}}"></image><view class="con"><view class="f1">{{cart.$orig.product.name}}</view><block wx:if="{{cart.g5}}"><view class="flex-col"><block wx:for="{{cart.$orig.ggtext}}" wx:for-item="item" wx:for-index="index"><block><text class="f2">{{item}}</text></block></block></view></block><block wx:if="{{cart.$orig.guige.name!='默认规格'&&!cart.$orig.ggtext}}"><view class="f2">{{cart.$orig.guige.name+''}}<block wx:if="{{cart.$orig.jltitle}}"><text>{{cart.$orig.jltitle}}</text></block></view></block><block wx:if="{{cart.$orig.jlprice}}"><view class="f3" style="color:#ff5555;margin-top:10rpx;font-size:28rpx;">{{'￥'+cart.g6+''}}<block wx:if="{{cart.$orig.product.product_type&&cart.$orig.product.product_type==1}}"><text style="font-size:20rpx;">/斤</text></block></view></block><block wx:else><view class="f3" style="color:#ff5555;margin-top:10rpx;font-size:28rpx;">{{'￥'+cart.$orig.guige.sell_price+''}}<block wx:if="{{cart.$orig.product.product_type&&cart.$orig.product.product_type==1}}"><text style="font-size:20rpx;">/斤</text></block></view></block></view><view class="addnum"><view class="minus"><image class="img" src="{{pre_url+'/static/img/cart-minus.png'}}" data-num="-1" data-proid="{{cart.$orig.proid}}" data-ggid="{{cart.$orig.ggid}}" data-stock="{{cart.$orig.guige.stock}}" data-jltitle="{{cart.$orig.jltitle}}" data-jlprice="{{cart.$orig.jlprice}}" data-jldata="{{cart.$orig.jldata}}" data-add_price="{{cart.$orig.add_price}}" data-package_data="{{cart.$orig.package_data}}" data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" bindtap="__e"></image></view><text class="i">{{cart.$orig.num}}</text><view class="plus"><image class="img" src="{{pre_url+'/static/img/cart-plus.png'}}" data-num="1" data-proid="{{cart.$orig.proid}}" data-ggid="{{cart.$orig.ggid}}" data-stock="{{cart.$orig.guige.stock}}" data-jltitle="{{cart.$orig.jltitle}}" data-jlprice="{{cart.$orig.jlprice}}" data-jldata="{{cart.$orig.jldata}}" data-add_price="{{cart.$orig.add_price}}" data-package_data="{{cart.$orig.package_data}}" data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block></block><block wx:if="{{!$root.g7}}"><block><text class="nopro">暂时没有商品喔~</text></block></block></scroll-view></view></view></view></block><block wx:if="{{showTimingTip}}"><view class="timingtip"><view class="moudle"><view class="title">消费提示</view><block wx:if="{{sysset.table_minprice_tip}}"><view class="minpricetip">{{''+sysset.table_minprice_tip+''}}</view></block><block wx:if="{{table.timing_fee_type>0}}"><view style="overflow:scroll;height:75%;"><view class="tip"><block wx:if="{{table.timing_fee_type==1}}"><text>阶梯计费规则</text></block><block wx:else><block wx:if="{{table.timing_fee_type==2}}"><text>时段计费规则</text></block></block>：</view><scroll-view scroll-y="true"><view class="item"><block wx:if="{{table.timing_fee_type==1}}"><block><block wx:for="{{table.timing_data1}}" wx:for-item="item" wx:for-index="index"><view class="item-list"><view style="min-width:40%;text-align:right;">{{''+item.start+" ~ "+item.end+" 分钟"}}</view><view class="money" style="min-width:23%;">{{"每"+item.minute+"分钟"}}</view><view class="money">{{"￥"+item.money}}</view></view></block></block></block><block wx:if="{{table.timing_fee_type==2}}"><block><block wx:for="{{table.timing_data2}}" wx:for-item="item" wx:for-index="index"><view class="item-list"><view>{{item.start+" ~ "+item.end}}</view><view class="money" style="min-width:23%;">{{"每"+item.minute+"分钟"}}</view><view class="money">{{"￥"+item.money}}</view></view></block></block></block></view></scroll-view></view></block><view data-event-opts="{{[['tap',[['hideTimingTip',['$event']]]]]}}" class="btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m17+' 0%,rgba('+$root.m18+',0.8) 100%)')+';'}}" bindtap="__e">我已知晓</view></view></view></block><block wx:if="{{showRenshu}}"><view class="popup"><view class="moudle"><view class="title">欢迎光临</view><view class="tip">当前桌号<text style="font-size:38rpx;color:#ffc107;margin-left:10rpx;">{{table.name}}</text></view><view class="content"><view class="t1">请选择正确的到店人数</view><view class="list flex" style="min-height:310rpx;"><block wx:for="{{$root.l4}}" wx:for-item="item" wx:for-index="index"><view class="number" style="{{(index==renshuindex?'color:'+item.m19+';border:1rpx solid '+item.m20+';background-color:rgb('+item.m21+',0.1)':'')}}" data-index="{{index}}" data-event-opts="{{[['tap',[['changeRenshu',['$event']]]]]}}" bindtap="__e">{{index+1}}</view></block><input class="input_number" style="{{(renshuindex==11?'color:'+$root.m22+';border:1rpx solid '+$root.m23+';background-color:rgb('+$root.m24+',0.1)':'')}}" data-index="{{11}}" type="number" placeholder="输入人数" data-event-opts="{{[['tap',[['changeRenshu',['$event']]]],['input',[['__set_model',['','renshu_input','$event',[]]]]]]}}" value="{{renshu_input}}" bindtap="__e" bindinput="__e"/></view></view><view data-event-opts="{{[['tap',[['enterRenshu',['$event']]]]]}}" class="btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m25+' 0%,rgba('+$root.m26+',0.8) 100%)')+';'}}" bindtap="__e">确定</view></view></view></block><block wx:if="{{nomore}}"><nomore vue-id="60d21e39-5" bind:__l="__l"></nomore></block></block></block><block wx:if="{{loading}}"><loading vue-id="60d21e39-6" bind:__l="__l"></loading></block><dp-tabbar vue-id="60d21e39-7" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><wxxieyi vue-id="60d21e39-8" bind:__l="__l"></wxxieyi></view>