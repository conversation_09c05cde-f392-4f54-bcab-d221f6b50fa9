<view class="container"><block wx:if="{{isload}}"><block><view class="ordertop" style="{{('background:url('+pre_url+'/static/img/ordertop.png);background-size:100%')}}"><block wx:if="{{detail.status==0}}"><view class="f1"><view class="t1">等待买家付款</view><block wx:if="{{djs}}"><view class="t2">{{"剩余时间："+djs}}</view></block></view></block><block wx:if="{{detail.status==1&&detail.paytypeid!=4}}"><view class="f1"><view class="t1">已成功付款</view><view><block wx:if="{{detail.pickup_number}}"><text style="font-weight:bold;margin-left:10rpx;font-size:38rpx;">{{"取餐号:"+detail.pickup_number+''}}</text></block></view></view></block><block wx:if="{{detail.status==1&&detail.paytypeid==4}}"><view class="f1"><view class="t1">请线下支付</view></view></block><block wx:if="{{detail.status==12}}"><view class="f1"><view class="t1">商家已接单</view><block wx:if="{{detail.freight_type!=1}}"><view class="t2">请等待配送</view></block><block wx:if="{{detail.freight_type==1}}"><view class="t2">请尽快前往自提地点取货</view></block></view></block><block wx:if="{{detail.status==2}}"><view class="f1"><view class="t1">订单配送中</view><block wx:if="{{detail.freight_type!=3}}"><view class="t2">{{"发货信息："+detail.express+" "+detail.express_no}}</view></block></view></block><block wx:if="{{detail.status==3}}"><view class="f1"><view class="t1">订单已完成</view></view></block><block wx:if="{{detail.status==4}}"><view class="f1"><view class="t1">订单已取消</view></view></block><block wx:if="{{detail.show_printdaynum}}"><view class="f1"><block wx:if="{{detail.printdaynum}}"><view style="font-weight:bold;margin-left:10rpx;font-size:38rpx;">{{'日流水号:#'+detail.printdaynum+''}}</view></block></view></block></view><block wx:if="{{detail.is_bar_table_order==0}}"><view class="address"><view class="info"><block wx:if="{{detail.linkman}}"><text class="t1">{{detail.linkman+" "+detail.tel}}</text></block><text class="t2">{{detail.tabletext+"："+detail.tableName}}</text></view></view></block><view class="btitle flex-y-center"><image style="width:36rpx;height:36rpx;" src="{{detail.binfo.logo}}" data-url="{{'index?bid='+detail.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></image><text class="flex1" style="padding-left:16rpx;" decode="true" space="true" data-url="{{'index?bid='+detail.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{detail.binfo.name}}</text></view><view class="product"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><block wx:if="{{shopset.is_times_prolist==0}}"><view class="content"><view data-url="{{'product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.$orig.pic}}"></image></view><view class="detail"><text class="t1">{{item.$orig.name}}</text><view class="t2 flex flex-y-center flex-bt"><block wx:if="{{item.g0}}"><view class="flex-col"><block wx:for="{{item.$orig.ggtext}}" wx:for-item="item2" wx:for-index="index"><block><text class="t2">{{item2}}</text></block></block></view></block><block wx:if="{{item.$orig.ggname}}"><text>{{item.$orig.ggname+''}}</text></block><block wx:if="{{item.$orig.jltitle}}"><text>{{item.$orig.jltitle}}</text></block><block wx:if="{{detail.status==3&&item.$orig.iscomment==0&&shopset.comment==1&&item.$orig.is_show_comment==1}}"><view class="btn3" data-url="{{'comment?ogid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去评价</view></block><block wx:if="{{detail.status==3&&item.$orig.iscomment==1}}"><view class="btn3" data-url="{{'comment?ogid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">查看评价</view></block></view><view class="t3"><block wx:if="{{item.$orig.jlprice}}"><text class="x1 flex1">{{"￥"+item.g1}}<block wx:if="{{item.$orig.product_type&&item.$orig.product_type==1}}"><text style="font-size:20rpx;">斤</text></block></text></block><block wx:else><text class="x1 flex1">{{"￥"+item.$orig.sell_price}}<block wx:if="{{item.$orig.product_type&&item.$orig.product_type==1}}"><text style="font-size:20rpx;">斤</text></block></text></block><text class="x2">{{"×"+item.$orig.num}}<block wx:if="{{item.$orig.product_type&&item.$orig.product_type==1}}"><text style="font-size:20rpx;">斤</text></block></text></view></view></view></block></block><block wx:for="{{$root.l2}}" wx:for-item="times" wx:for-index="index" wx:key="*this"><block wx:if="{{shopset.is_times_prolist==1}}"><view class="timeslist"><view class="tip flex flex-y-center"><view class="t1">{{"第 "+times.$orig.times+" 次下单"}}</view><view class="t1">下单成功，坐等开吃<text style="color:#999;margin-left:20rpx;">{{times.$orig.createtime}}</text></view></view><block wx:for="{{times.l1}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="content" style="border:none;"><view data-url="{{'product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.$orig.pic}}"></image></view><view class="detail"><text class="t1">{{item.$orig.name}}</text><view class="t2 flex flex-y-center flex-bt"><block wx:if="{{item.g2}}"><view class="flex-col"><block wx:for="{{item.$orig.ggtext}}" wx:for-item="item2" wx:for-index="index"><block><text class="t2">{{item2}}</text></block></block></view></block><block wx:if="{{item.$orig.ggname}}"><text>{{item.$orig.ggname+''}}</text></block><block wx:if="{{item.$orig.jltitle}}"><text>{{item.$orig.jltitle}}</text></block><block wx:if="{{detail.status==3&&item.$orig.iscomment==0&&shopset.comment==1&&item.$orig.is_show_comment==1}}"><view class="btn3" data-url="{{'comment?ogid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去评价</view></block><block wx:if="{{detail.status==3&&item.$orig.iscomment==1}}"><view class="btn3" data-url="{{'comment?ogid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">查看评价</view></block></view><view class="t3"><block wx:if="{{item.$orig.jlprice}}"><text class="x1 flex1">{{"￥"+item.g3}}<block wx:if="{{item.$orig.product_type&&item.$orig.product_type==1}}"><text style="font-size:20rpx;">斤</text></block></text></block><block wx:else><text class="x1 flex1">{{"￥"+item.$orig.sell_price}}<block wx:if="{{item.$orig.product_type&&item.$orig.product_type==1}}"><text style="font-size:20rpx;">斤</text></block></text></block><text class="x2">{{"×"+item.$orig.num}}<block wx:if="{{item.$orig.product_type&&item.$orig.product_type==1}}"><text style="font-size:20rpx;">斤</text></block></text></view></view></view></block></view></block></block></view><block wx:if="{{(detail.status==3||detail.status==2)&&(detail.freight_type==3||detail.freight_type==4)}}"><view class="orderinfo"><view class="item flex-col"><text class="t1" style="color:#111;">发货信息</text><text class="t2" style="text-align:left;margin-top:10rpx;padding:0 10rpx;" user-select="true" selectable="true">{{detail.freight_content}}</text></view></view></block><view class="orderinfo"><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{detail.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{detail.createtime}}</text></view><block wx:if="{{detail.status>0&&detail.paytypeid!='4'&&detail.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{detail.paytime}}</text></view></block><block wx:if="{{detail.status>0&&detail.paytime}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{detail.paytype}}</text></view></block><block wx:if="{{detail.status>1&&detail.send_time}}"><view class="item"><text class="t1">发货时间</text><text class="t2">{{detail.send_time}}</text></view></block><block wx:if="{{detail.status==3&&detail.collect_time}}"><view class="item"><text class="t1">收货时间</text><text class="t2">{{detail.collect_time}}</text></view></block></view><view class="orderinfo"><view class="item"><text class="t1">商品金额</text><text class="t2 red">{{"¥"+detail.product_price}}</text></view><block wx:if="{{detail.pack_fee>0}}"><view class="item"><text class="t1">打包费</text><text class="t2 red">{{"+¥"+detail.pack_fee}}</text></view></block><block wx:if="{{detail.leveldk_money>0}}"><view class="item"><text class="t1">{{$root.m0+"折扣"}}</text><text class="t2 red">{{"-¥"+detail.leveldk_money}}</text></view></block><block wx:if="{{detail.manjian_money>0}}"><view class="item"><text class="t1">满减活动</text><text class="t2 red">{{"-¥"+detail.manjian_money}}</text></view></block><block wx:if="{{detail.tea_fee>0}}"><view class="item"><text class="t1">{{shopset.tea_fee_text}}</text><text class="t2 red">{{"+¥"+detail.tea_fee}}</text></view></block><block wx:if="{{detail.couponmoney>0}}"><view class="item"><text class="t1">{{$root.m1+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.coupon_money}}</text></view></block><block wx:if="{{detail.scoredk>0}}"><view class="item"><text class="t1">{{$root.m2+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.scoredk_money}}</text></view></block><block wx:if="{{detail.discount_money>0}}"><view class="item"><text class="t1">优惠</text><text class="t2 red">{{"-¥"+detail.discount_money}}</text></view></block><block wx:if="{{detail.scoredk>0}}"><view class="item"><text class="t1">{{$root.m3+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.scoredk_money}}</text></view></block><block wx:if="{{detail.timing_money>0}}"><view class="item"><text class="t1">{{shopset.timing_fee_text}}</text><text class="t2 red">{{"+¥"+detail.timing_money}}</text></view></block><block wx:if="{{detail.service_money>0}}"><view class="item"><text class="t1">服务费</text><text class="t2 red">{{"¥"+detail.service_money}}</text></view></block><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+detail.totalprice}}</text></view><view class="item"><text class="t1">订单状态</text><block wx:if="{{detail.status==0}}"><text class="t2">未付款</text></block><block wx:if="{{detail.status==1&&detail.paytypeid!=4}}"><text class="t2">已付款</text></block><block wx:if="{{detail.status==1&&detail.paytypeid==4}}"><text class="t2">线下支付</text></block><block wx:if="{{detail.status==12}}"><text class="t2">商家已接单</text></block><block wx:if="{{detail.status==2}}"><text class="t2">已发货</text></block><block wx:if="{{detail.status==3}}"><text class="t2">已收货</text></block><block wx:if="{{detail.status==4}}"><text class="t2">已关闭</text></block></view><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款状态</text><block wx:if="{{detail.refund_status==1}}"><text class="t2 red">{{"审核中,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==2}}"><text class="t2 red">{{"已退款,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==3}}"><text class="t2 red">{{"已驳回,¥"+detail.refund_money}}</text></block></view></block><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款时间</text><text class="t2 red">{{detail.refund_time}}</text></view></block><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款原因</text><text class="t2 red">{{detail.refund_reason}}</text></view></block><block wx:if="{{detail.refund_checkremark}}"><view class="item"><text class="t1">审核备注</text><text class="t2 red">{{detail.refund_checkremark}}</text></view></block><block wx:if="{{detail.isfuwu&&detail.fuwuendtime>0}}"><view class="item"><text class="t1">到期时间</text><text class="t2 red">{{$root.g4}}</text></view></block><view class="item"><text class="t1">备注</text><text class="t2 red">{{detail.message?detail.message:'无'}}</text></view><block wx:if="{{detail.field1}}"><view class="item"><text class="t1">{{detail.field1data[0]}}</text><text class="t2 red">{{detail.field1data[1]}}</text></view></block><block wx:if="{{detail.field2}}"><view class="item"><text class="t1">{{detail.field2data[0]}}</text><text class="t2 red">{{detail.field2data[1]}}</text></view></block><block wx:if="{{detail.field3}}"><view class="item"><text class="t1">{{detail.field3data[0]}}</text><text class="t2 red">{{detail.field3data[1]}}</text></view></block><block wx:if="{{detail.field4}}"><view class="item"><text class="t1">{{detail.field4data[0]}}</text><text class="t2 red">{{detail.field4data[1]}}</text></view></block><block wx:if="{{detail.field5}}"><view class="item"><text class="t1">{{detail.field5data[0]}}</text><text class="t2 red">{{detail.field5data[1]}}</text></view></block></view><view style="width:100%;height:calc(160rpx + env(safe-area-inset-bottom));"></view><view class="bottom notabbarbot"><block wx:if="{{detail.status==0}}"><block><block wx:if="{{shopset.pay_after!=1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['toclose',['$event']]]]]}}" bindtap="__e">关闭订单</view></block><view class="btn1" style="{{'background:'+($root.m4)+';'}}" data-id="{{detail.payorderid}}" data-istopay="{{detail.is_topay}}" data-event-opts="{{[['tap',[['toPay',['$event']]]]]}}" bindtap="__e">去付款</view></block></block><block wx:if="{{detail.status==12}}"><block><block wx:if="{{detail.refund_status==0||detail.refund_status==3}}"><view class="btn2" data-url="{{'refund?orderid='+detail.id+'&price='+detail.totalprice}}" data-event-opts="{{[['tap',[['toRefund',['$event']]]]]}}" bindtap="__e">申请退款</view></block></block></block><block wx:if="{{detail.status==2||detail.status==1}}"><block><block wx:if="{{detail.refund_status==0||detail.refund_status==3}}"><view class="btn2" data-url="{{'refund?orderid='+detail.id+'&price='+detail.totalprice}}" data-event-opts="{{[['tap',[['toRefund',['$event']]]]]}}" bindtap="__e">申请退款</view></block></block></block><block wx:if="{{(detail.status==1||detail.status==2)&&detail.freight_type==1}}"><block><view data-event-opts="{{[['tap',[['showhxqr',['$event']]]]]}}" class="btn2" bindtap="__e">核销码</view></block></block><block wx:if="{{detail.status==3}}"><block><block wx:if="{{iscommentdp==0}}"><view class="btn1" style="{{'background:'+($root.m5)+';'}}" data-url="{{'commentdp?orderid='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">评价店铺</view></block><block wx:if="{{iscommentdp==1}}"><view class="btn2" data-url="{{'commentdp?orderid='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看评价</view></block></block></block></view><uni-popup class="vue-ref" vue-id="6802884c-1" id="dialogHxqr" type="dialog" data-ref="dialogHxqr" bind:__l="__l" vue-slots="{{['default']}}"><view class="hxqrbox"><image class="img" src="{{detail.hexiao_qr}}" data-url="{{detail.hexiao_qr}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">请出示核销码给核销员进行核销</view><view data-event-opts="{{[['tap',[['closeHxqr',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="{{pre_url+'/static/img/close2.png'}}"></image></view></view></uni-popup><uni-popup class="vue-ref" vue-id="6802884c-2" id="dialogRefundTip" type="dialog" data-ref="dialogRefundTip" bind:__l="__l" vue-slots="{{['default']}}"><view class="hxqrbox"><view class="txt">您可以联系店内服务人员</view><view class="txt">或者点击下方按钮联系门店电话客服</view><view class="callphone" style="{{'background:'+($root.m6)+';'}}" data-tel="{{shopset.tel}}" data-event-opts="{{[['tap',[['call',['$event']]]]]}}" bindtap="__e">联系客服</view><view data-event-opts="{{[['tap',[['closeRefundTip',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="{{pre_url+'/static/img/close2.png'}}"></image></view></view></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="6802884c-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="6802884c-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>