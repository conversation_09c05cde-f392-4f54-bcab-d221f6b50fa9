
page {position: relative;width: 100%;height: 100%;background: #fff;}
.container {height: 100vh;width:100%;position: relative;}
.scroll-class{height: 100%;width: 100%;}
.container .options-view{width: 95%;margin: 30rpx auto;}
.options-view .option-title{width: 100%;font-size: 28rpx;position: relative;}
.option-title .text-name{font-weight: bold;color: black;position: relative;margin-left: 15rpx;}
.options-view .option-title::after{content: "";width: 6rpx;height: 64%;background: black;position: absolute;left: 0;top: 22%;border-radius: 10px;}
.options-view .shop-list{width: 100%;align-items: center;flex-wrap: wrap;margin: 30rpx 0rpx;justify-content: flex-start;}
.shop-list .options-shop{width: 30%;height:305rpx;overflow: hidden;background: #f7f7f7;margin-bottom: 20rpx;border-radius: 10rpx;border: 3rpx solid;margin: 0rpx 5px;margin-bottom: 15rpx;position: relative;}
.options-shop .shop-image{width: 100%;height:180rpx;background: #fff;}
.options-shop .shop-image image{width: 100%;height:180rpx;overflow: hidden;border-radius: 2rpx;}
.options-shop .shop-info-view{width: 100%;height:110rpx;position: relative;margin-top: 5rpx;overflow: hidden;}
.shop-info-view .shop-name{text-align: center;font-size:22rpx;color: #848385;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;}
.shop-info-view .gg-name{text-align: center;font-size:22rpx;color: #848385;}
.gg-name-but{border: 1px solid;border-radius:36rpx;text-align: center;font-size: 24rpx;width: 76%;color: #fff;
position: absolute;top: 60rpx;left:50%;padding: 16rpx 0rpx;-webkit-transform: translateX(-50%);transform: translateX(-50%);}
.jia-price{font-size: 24rpx;font-weight: bold;color: orangered;position: absolute;left: 5rpx;bottom: 0rpx;}
.choose-view{width: 50rpx;height: 40rpx;position: absolute;right: -5rpx;bottom: -5rpx; display: flex;align-items: center;justify-content: center;
border-radius: 28rpx 0rpx 0rpx 0rpx;}
.choose-view image{width: 65%;height: 75%;}
.addnum {width: 100%;position: absolute;right:0rpx;bottom: 3rpx;font-size: 28rpx;color: #666;display: flex;align-items: center;justify-content:flex-end;padding:0rpx 8rpx;}
.addnum .plus {width: 45rpx;height: 45rpx;background: #FD4A46;color: #FFFFFF;	border-radius: 50%;display: flex;align-items: center;justify-content: center;font-size: 28rpx}
.addnum .no{background: #dddddd !important;}
.addnum .minus {width: 45rpx;height: 45rpx;background: #FFFFFF;	color: #FD4A46;	border: 1px solid #FD4A46;border-radius: 50%;display: flex;align-items: center;
	justify-content: center;font-size: 28rpx}
.addnum .i {padding: 0 20rpx;color: #999999;font-size: 28rpx}
.occupy-box{width: 100%;height: calc(env(safe-area-inset-bottom));}
.footer {width: 96%;position: fixed;left: 50%;z-index: 8;display: flex;align-items: center;-webkit-transform: translate(-50%);transform: translate(-50%);
margin-bottom: env(safe-area-inset-bottom);bottom: 0;border-radius: 30rpx 30rpx 15rpx 15rpx;overflow: hidden;background-color: rgba(0,0,0,.8);}
.select-display{width: 100%;padding: 20rpx 35rpx;display: flex;align-items: center;justify-content: flex-start;flex-wrap: wrap;margin-bottom: 20rpx;}
.scrollview-class{max-height: 290rpx;height: auto;}
.select-display .product-pit-view{width: 80rpx;height: 80rpx;border-radius: 50%;margin: 10rpx 15rpx;border: 4rpx solid;position: relative;}
.select-display .select-shop-image{width: 100%;height: 100%;overflow: hidden;border-radius: 50%;}
.select-display .select-shop-image image{width: 100%;height: 100%;}
.select-display .num-view{width: 60rpx;font-size: 24rpx;color: #fff;border-radius: 20rpx;position: absolute;bottom: -15rpx;
text-align: center;left: 50%;-webkit-transform: translate(-50%);transform: translate(-50%);height: 30rpx;line-height: 30rpx;}
.addcart-view{width: 100%;height: 100rpx;line-height: 100rpx;border-radius: 15rpx 15rpx 0rpx 0rpx;text-align: center;color: #fff;font-size: 32rpx;font-weight: bold;}
.please-select{background-color: #a1a1a1 !important;}
.uni-popup__wrapper-box{background: #f7f8fa;border-radius: 20rpx 20rpx 0rpx 0rpx;overflow: hidden;}
.hotelpopup__content{width: 100%;height:auto;position: relative;background: #fff;}
.hotelpopup__content .popup-close{position: fixed;right: 20rpx;top: 20rpx;width: 56rpx;height: 56rpx;z-index: 11;}
.hotelpopup__content .popup-close image{width: 100%;height: 100%;}
.hotelpopup__content .popup-but-view{width: 100%;position: -webkit-sticky;position: sticky;bottom: 0rpx;padding: 0rpx 40rpx 20rpx;}
.hotelpopup__content .popup-but-view .page_content{	font-size: 24rpx;color: #888888;font-weight: normal;padding: 20rpx 0rpx;}
.hotelpopup__content .popup-but-view .but-class{width: 100%;padding: 22rpx;text-align: center;color: #FFFFFF;font-size: 32rpx;font-weight: bold;border-radius: 20rpx;background: linear-gradient(90deg, #06D470 0%, #06D4B9 100%);
}
.hotelpopup__content .popup-but-view .price-statistics{padding-bottom: 15rpx;}
.hotelpopup__content .popup-but-view .price-statistics .title-text{font-size: 24rpx;}
.hotelpopup__content .popup-but-view .price-statistics .price-text{padding: 0rpx 10rpx;align-items: center;}
.custom-image-view{padding: 0rpx 0rpx 10rpx;}
.custom-image-view image{width: 100%;overflow: hidden;}
.customproname-view{padding: 10rpx 40rpx 20rpx;font-size: 32rpx;font-weight: bold;}
.gglist-view{padding: 0rpx 40rpx;margin-bottom: 20rpx;}
.gglist-view .gglist-view-options{margin-bottom: 30rpx;padding:0rpx 15rpx;}
.gglist-view .gglist-view-options .name{font-size: 28rpx;color: #333;font-weight: bold;margin-bottom: 20rpx;}
.gglist-view .gglist-view-options .item{ font-size: 30rpx;color: #333;flex-wrap:wrap}
.gglist-view .gglist-view-options .item2{ height:60rpx;line-height:60rpx;margin-bottom:4px;border:0; border-radius:100rpx; padding:0 40rpx;color:#666666; 
margin-right: 10rpx; font-size:26rpx;border: 1px solid #eee;}
.selectproduct-list{width: 100%;padding: 20rpx 30rpx;}
.selectproduct-list .options-list{height:60rpx;line-height:60rpx;margin-bottom:4px;border:0; border-radius:100rpx; padding:0 40rpx;color:#666666; 
margin-right: 10rpx; font-size:26rpx;border: 1px solid #eee;display: inline-block;}

