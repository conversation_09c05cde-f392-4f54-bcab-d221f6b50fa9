
page {
	position: relative;
	width: 100%;
	height: 100%;
	background: #fff;
}
.container {
	height: 100vh;
	position: relative;
}
.topbannerbg {
	width: 100%;
	height: 264rpx;
	background: #fff;
}
.topbannerbg2 {
	position: absolute;
	width: 100%;
	height: 264rpx;
	background: rgba(0, 0, 0, 0.3);
	top: 0
}
.topbanner {
	position: absolute;
	width: 100%;
	display: flex;
	padding: 40rpx 20rpx;
	top: 0
}
.topbanner .left {
	width: 160rpx;
	height: 160rpx;
	flex-shrink: 0;
	margin-right: 20rpx
}
.topbanner .left .img {
	width: 100%;
	height: 100%;
	border-radius: 50%
}
.topbanner .right {
	display: flex;
	flex-direction: column;
	padding: 20rpx 0
}
.topbanner .right .f1 {
	font-size: 36rpx;
	font-weight: bold;
	color: #fff
}
.topbanner .right .f2 {
	font-size: 22rpx;
	color: #fff;
	opacity: 0.7;
	margin-top: 20rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	overflow: hidden;
	line-height: 30rpx;
}
.topbanner .right .f3 {
	width: 100%;
	display: flex;
	padding-right: 20rpx;
	margin-top: 10rpx
}
.topbanner .right .f3 .t2 {
	display: flex;
	align-items: center;
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.9)
}
.topbanner .right .f3 .img {
	width: 32rpx;
	height: 32rpx;
	margin-left: 10rpx
}
.topbanner .searchIcon {
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: absolute;
	right: 40rpx;
	top: 50rpx;
}
.topbanner .searchIcon .searchIcon-options {
	width: 64rpx;
	height: 64rpx;
	background: rgba(0,0,0,.4);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}
.searchIcon .searchIcon-options  image{
	width: 100%;height: 100%;
}
.navtab {
	display: flex;
	align-items: center;
	width: 100%;
	height: 110rpx;
	background: #fff;
	position: relative;
	padding: 0 50rpx;
	border-radius: 24rpx 24rpx 0 0;
}
.navtab .item {
	flex: 1;
	font-size: 28rpx;
	text-align: center;
	color: #222222;
	height: 110rpx;
	line-height: 110rpx;
	overflow: hidden;
	position: relative
}
.navtab  .my-order-class{
	font-size: 24rpx;
	padding: 10rpx 25rpx;
	border-radius:30rpx;
}
.navtab .item .after {
	display: none;
	position: absolute;
	left: 50%;
	margin-left: -20rpx;
	bottom: 20rpx;
	height: 4px;
	border-radius: 2px;
	width: 40rpx
}
.navtab .on {
	font-size: 30rpx;
	font-weight: bold
}
.navtab .on .after {
	display: block
}
.body {
	margin-top: -30rpx;
	z-index: 5;
}
.body_status {
	padding: 0 0 env(safe-area-inset-bottom) 0;
}
.content1 .item {
	display: flex;
	flex-direction: column;
	width: 100%;
	padding: 0 40rpx;
	margin-top: 40rpx
}
.content1 .item:last-child {
	border-bottom: 0;
}
.content1 .item .t1 {
	width: 200rpx;
	color: #2B2B2B;
	font-weight: bold;
	font-size: 30rpx;
	height: 60rpx;
	line-height: 60rpx
}
.content1 .item .t2 {
	color: #2B2B2B;
	font-size: 24rpx;
	line-height: 30rpx
}
.content2 .comment {
	padding: 0 10rpx
}
.content2 .comment .item {
	background-color: #fff;
	padding: 10rpx 20rpx;
	display: flex;
	flex-direction: column;
}
.content2 .comment .item .f1 {
	display: flex;
	width: 100%;
	align-items: center;
	padding: 10rpx 0;
}
.content2 .comment .item .f1 .t1 {
	width: 70rpx;
	height: 70rpx;
	border-radius: 50%;
}
.content2 .comment .item .f1 .t2 {
	padding-left: 10rpx;
	color: #333;
	font-weight: bold;
	font-size: 30rpx;
}
.content2 .comment .item .f1 .t3 {
	text-align: right;
}
.content2 .comment .item .f1 .t3 .img {
	width: 24rpx;
	height: 24rpx;
	margin-left: 10rpx
}
.content2 .comment .item .score {
	font-size: 24rpx;
	color: #f99716;
}
.content2 .comment .item .score image {
	width: 140rpx;
	height: 50rpx;
	vertical-align: middle;
	margin-bottom: 6rpx;
	margin-right: 6rpx;
}
.content2 .comment .item .f2 {
	display: flex;
	flex-direction: column;
	width: 100%;
	padding: 10rpx 0;
}
.content2 .comment .item .f2 .t1 {
	color: #333;
	font-size: 28rpx;
}
.content2 .comment .item .f2 .t2 {
	display: flex;
	width: 100%
}
.content2 .comment .item .f2 .t2 image {
	width: 100rpx;
	height: 100rpx;
	margin: 10rpx;
}
.content2 .comment .item .f2 .t3 {
	color: #aaa;
	font-size: 24rpx;
}
.content2 .comment .item .f2 .t3 {
	color: #aaa;
	font-size: 24rpx;
}
.content2 .comment .item .f3 {
	width: 100%;
	padding: 10rpx 0;
	position: relative
}
.content2 .comment .item .f3 .arrow {
	width: 16rpx;
	height: 16rpx;
	background: #eee;
	-webkit-transform: rotate(45deg);
	        transform: rotate(45deg);
	position: absolute;
	top: 0rpx;
	left: 36rpx
}
.content2 .comment .item .f3 .t1 {
	width: 100%;
	border-radius: 10rpx;
	padding: 10rpx;
	font-size: 22rpx;
	color: #888;
	background: #eee
}
.view-show {
	width: 100%;
	height: 100%;
}
.search-container {
	width: 100%;
	height: 94rpx;
	padding: 16rpx 23rpx 14rpx 23rpx;
	background-color: #fff;
	position: relative;
	overflow: hidden;
	border-bottom: 1px solid #f5f5f5
}
.search-box {
	display: flex;
	align-items: center;
	height: 60rpx;
	border-radius: 30rpx;
	border: 0;
	background-color: #f7f7f7;
	flex: 1
}
.search-box .img {
	width: 24rpx;
	height: 24rpx;
	margin-right: 10rpx;
	margin-left: 30rpx
}
.search-box .search-text {
	font-size: 24rpx;
	color: #C2C2C2;
	width: 100%;
}
.nav_left {
	width: 25%;
	height: 100%;
	background: #F6F6F6;
	overflow-y: scroll;
}
.nav_left .nav_left_items {
	line-height: 50rpx;
	color: #999999;
	border-bottom: 0px solid #E6E6E6;
	font-size: 24rpx;
	position: relative;
	border-right: 0 solid #E6E6E6;
	padding: 25rpx 30rpx;
	text-align: center;
}
.nav_left .nav_left_items.active {
	background: #fff;
	color: #222222;
	font-size: 28rpx;
	font-weight: bold
}
.nav_left .nav_left_items image {
	height: 40rpx;
	width: 40rpx;
	display: block;
	margin: 0 auto;
}
.nav_left .nav_left_items .before {
	display: none;
	position: absolute;
	top: 50%;
	margin-top: -22rpx;
	left: 0rpx;
	height: 44rpx;
	border-radius: 4rpx;
	width: 6rpx
}
.nav_left .nav_left_items.active .before {
	display: block
}
.nav_left .nav_left_items .cartnum {
	position: absolute;
	top: 8rpx;
	right: 8rpx;
	width: 36rpx;
	height: 36rpx;
	border: 1px solid #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
	font-size: 18rpx;
	color: #fff
}
.nav_left .nav_left_items .carttag {
	position: absolute;
	top: 10rpx;
	right: -8rpx;
	line-height: 30rpx;
	padding: 0 15rpx;
	border-radius: 50rpx;
	background: #ffebea;
	color: #c95e3e;
	font-weight: normal;
	font-size: 17rpx;
}
.nav_right {
	width: 75%;
	height: 100%;
	display: flex;
	flex-direction: column;
	background: #fff;
	box-sizing: border-box;
}
.nav_right-content {
	background: #ffffff;
	padding: 20rpx 10rpx 0 20rpx;
	height: 100%;
	position: relative
}
.detail-list {
	height: 100%;
	overflow: scroll
}
.classification-detail-item {
	width: 100%;
	overflow: visible;
	background: #fff
}
.classification-detail-item .head {
	height: 82rpx;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.classification-detail-item .head .txt {
	color: #222222;
	font-weight: bold;
	font-size: 28rpx;
}
.classification-detail-item .head .show-all {
	font-size: 22rpx;
	color: #949494;
	display: flex;
	align-items: center
}
.product-itemlist {
	height: auto;
	position: relative;
	overflow: hidden;
	padding: 0px;
	display: flex;
	flex-wrap: wrap
}
.product-itemlist .item {
	width: 100%;
	display: inline-block;
	position: relative;
	margin-bottom: 12rpx;
	background: #fff;
	display: flex;
	padding: 14rpx 0;
	border-radius: 10rpx;
	border-bottom: 1px solid #F8F8F8
}
.product-itemlist .product-pic {
	width: 30%;
	height: 0;
	overflow: hidden;
	background: #ffffff;
	padding-bottom: 30%;
	position: relative;
	border-radius: 4px;
}
.product-itemlist .product-pic .image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: auto
}
.product-itemlist .product-pic .saleimg {
	position: absolute;
	width: 120rpx;
	height: auto;
	top: -6rpx;
	left: -6rpx;
}
.product-itemlist .product-info {
	width: 70%;
	padding: 0 10rpx 5rpx 20rpx;
	position: relative;
}
.product-itemlist .product-info .p1 {
	color: #323232;
	font-weight: bold;
	font-size: 28rpx;
	line-height: 30rpx;
	margin-bottom: 0;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	height: 60rpx
}
.product-itemlist .product-info .p2 {
	margin-top: 10rpx;
	height: 36rpx;
	line-height: 36rpx;
	overflow: hidden;
}
.product-itemlist .product-info .p2 .t1 {
	font-size: 32rpx;
}
.product-itemlist .product-info .p2 .t2 {
	margin-left: 10rpx;
	font-size: 24rpx;
	color: #aaa;
	text-decoration: line-through;
	/*letter-spacing:-1px*/
}
.product-itemlist .product-info .p3 {
	display: flex;
	align-items: center;
	overflow: hidden;
	margin-top: 10rpx
}
.product-itemlist .product-info .p3-1 {
	font-size: 20rpx;
	height: 30rpx;
	line-height: 30rpx;
	text-align: right;
	color: #999;
	margin-left: 6rpx;
}
.product-itemlist .product-info .p3-1:nth-child(1) {
	margin: 0;
}
.product-itemlist .product-info .p4 {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	position: absolute;
	display: relative;
	bottom: 6rpx;
	right: 4rpx;
	text-align: center;
}
.product-itemlist .product-info .p4 .icon_gouwuche {
	font-size: 28rpx;
	height: 48rpx;
	line-height: 48rpx
}
.product-itemlist .select-guige{
	position: absolute;
	right: 10rpx;
	bottom: 20rpx;
	font-size: 26rpx;
	color: #fff;
	padding: 7rpx 18rpx;
	border-radius: 30rpx;
}
.product-itemlist .addnum {
	position: absolute;
	right: 20rpx;
	bottom: 20rpx;
	font-size: 30rpx;
	color: #666;
	width: auto;
	display: flex;
	align-items: center
}
.product-itemlist .addnum .plus {
	width: 50rpx;
	height: 50rpx;
	line-height: 50rpx;
	background: #FD4A46;
	color: #FFFFFF;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	font-weight: bold
}
.product-itemlist .addnum .minus {
	width: 50rpx;
	height: 50rpx;
	line-height: 50rpx;
	background: #FFFFFF;
	color: #FD4A46;
	border: 1px solid #FD4A46;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	font-weight: bold
}
.product-itemlist .addnum .img {
	width: 24rpx;
	height: 24rpx
}
.product-itemlist .addnum .i {
	padding: 0 20rpx;
	color: #999999;
	font-size: 28rpx
}
.overlay {
	background-color: rgba(0, 0, 0, .5);
	position: absolute;
	width: 60%;
	height: 60%;
	border-radius: 50%;
	display: none;
	top: 20%;
	left: 20%;
	line-height: 100%;
}
.overlay .text {
	color: #fff;
	text-align: center;
	-webkit-transform: translateY(100%);
	        transform: translateY(100%);
}
.product-itemlist .soldout .product-pic .overlay {
	display: block;
}
.prolist {
	max-height: 620rpx;
	min-height: 320rpx;
	overflow: hidden;
	padding: 0rpx 20rpx;
	font-size: 28rpx;
	border-bottom: 1px solid #e6e6e6;
}
.prolist .nopro {
	text-align: center;
	font-size: 26rpx;
	display: block;
	margin: 80rpx auto;
}
.prolist .proitem {
	position: relative;
	padding: 10rpx 0;
	display: flex;
	border-bottom: 1px solid #eee
}
.prolist .proitem .pic {
	width: 120rpx;
	height: 120rpx;
	margin-right: 20rpx;
}
.prolist .proitem .con {
	padding-right: 180rpx;
	padding-top: 10rpx
}
.prolist .proitem .con .f1 {
	color: #323232;
	font-size: 26rpx;
	line-height: 32rpx;
	margin-bottom: 10rpx;
	margin-top: -6rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
}
.prolist .proitem .con .f2 {
	font-size: 24rpx;
	line-height: 28rpx;
	color: #999;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	overflow: hidden;
}
.prolist .proitem .addnum {
	position: absolute;
	right: 20rpx;
	bottom: 50rpx;
	font-size: 30rpx;
	color: #666;
	width: auto;
	display: flex;
	align-items: center
}
.prolist .proitem .addnum .plus {
	width: 48rpx;
	height: 36rpx;
	background: #F6F8F7;
	display: flex;
	align-items: center;
	justify-content: center
}
.prolist .proitem .addnum .minus {
	width: 48rpx;
	height: 36rpx;
	background: #F6F8F7;
	display: flex;
	align-items: center;
	justify-content: center
}
.prolist .proitem .addnum .img {
	width: 24rpx;
	height: 24rpx
}
.prolist .proitem .addnum .i {
	padding: 0 20rpx;
	color: #2B2B2B;
	font-weight: bold;
	font-size: 24rpx
}
.prolist .tips {
	font-size: 22rpx;
	color: #666;
	text-align: center;
	line-height: 56rpx;
	background: #f5f5f5;
}
.footer {
	width: 100%;
	background: #fff;
	margin-top: 5px;
	position: fixed;
	left: 0px;
	bottom: 0px;
	z-index: 8;
	display: flex;
	align-items: center;
	padding: 0 20rpx;
	border-top: 1px solid #EFEFEF
}
.footer .cart_ico {
	width: 64rpx;
	height: 64rpx;
	border-radius: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative
}
.footer .cart_ico .img {
	width: 36rpx;
	height: 36rpx;
}
.footer .cart_ico .cartnum {
	position: absolute;
	top: -17rpx;
	right: -17rpx;
	width: 34rpx;
	height: 34rpx;
	border: 1px solid #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
	font-size: 20rpx;
	font-weight: bold;
	color: #fff
}
.footer .text1 {
	height: 100rpx;
	line-height: 100rpx;
	color: #555555;
	font-weight: bold;
	font-size: 30rpx;
	margin-left: 40rpx;
	margin-right: 10rpx
}
.footer .text2 {
	font-size: 32rpx;
	font-weight: bold
}
.footer .op {
	width: 200rpx;
	height: 72rpx;
	line-height: 72rpx;
	border-radius: 36rpx;
	font-weight: bold;
	color: #fff;
	font-size: 28rpx;
	text-align: center
}
.layui-imgbox {
	margin-right: 16rpx;
	margin-bottom: 10rpx;
	font-size: 24rpx;
	position: relative;
}
.layui-imgbox-img {
	display: block;
	width: 200rpx;
	height: 200rpx;
	padding: 2px;
	border: #d3d3d3 1px solid;
	background-color: #f6f6f6;
	overflow: hidden
}
.layui-imgbox-img>image {
	max-width: 100%;
}
.header {
	position: relative;
	padding: 30rpx;
	border-bottom: 15rpx solid #F0f0f0;
}
.header_title {
	font-size: 28rpx;
	color: #333;
}
.header_detail {
	height: 30rpx;
	width: 30rpx;
	margin-left: 10rpx;
}
.header_serach {
	height: 30rpx;
	width: 30rpx;
}
.header_address {
	font-size: 24rpx;
	color: #999;
	margin-top: 20rpx;
}
.shop_title {
	font-weight: 700;
	font-size: 35rpx;
}
.header_content{
	font-size: 24rpx;
	color: #666;
	padding: 20rpx 0 0 0;
}
.header_lable{
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
	padding: 0 0 10rpx 0;
}
.timingtip{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)}
.timingtip .title{color: #000000;text-align: center;padding: 10rpx 0;font-size: 19px;font-weight: 700;}
.timingtip .moudle{width:90%;margin:0 auto;height:70%;margin-top:25%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px}
.timingtip .moudle .item-list{line-height: 80rpx ;justify-content:space-evenly;display: flex;font-size: 32rpx;
}
.timingtip .moudle .tip{font-weight: 700;line-height: 60rpx;}
.timingtip .btn{position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center; width: 50%;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;}
.timingtip .moudle .item-list .money{color: #ff5722;}
.minpricetip{letter-spacing: 4rpx;line-height: 40rpx;padding: 20rpx 0;}
.popup{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)}
.popup .title{color: #000000;text-align: center;padding: 10rpx 0;font-size: 19px;font-weight: 700;}
.popup .moudle{width:90%;margin:0 auto;margin-top:50%;background:#fff;color:#333;padding:10px 10px 10px 10px;position:relative;border-radius: 20rpx;}
.popup .moudle .tip{text-align: center;line-height: 40rpx;padding: 20rpx 0;margin-bottom: 40rpx;}
.popup .moudle .content{overflow:scroll;height:70%;}
.popup .moudle .content .t1{color: #949494;font-size: 26rpx;margin-bottom: 20rpx;margin-left: 1.5%;}
.popup .moudle .content .list {flex-wrap: wrap;}
.popup .moudle .content .list .number{width: 22%;line-height: 85rpx;text-align: center;background: #F6F6F6;border-radius: 15rpx;margin: 1.5%;margin-bottom: 10rpx;font-weight: 700;border: 1rpx solid #F6F6F6;}
.popup .moudle .content .list .on{border: ;}
.popup .moudle .content .list .input_number{width: 47%;background: #F6F6F6;line-height: 85rpx;border-radius: 15rpx;height: 90rpx;margin: 1.5% 0 0 1.5%;text-align: center;padding: 0 5%;}
.popup .moudle .btn{margin:0 auto;text-align:center; width: 40%;height: 80rpx; line-height: 80rpx; color: #fff; border-radius: 40rpx;margin-top:20rpx}
.addmsg{
	opacity: 0.5;
	background: #000000;
	border-radius: 50rpx;
	padding: 20rpx 30rpx;
	color: #fff;
	font-size: 32rpx;
	position: absolute;
	left: 0;
	top: -110rpx;
	border: none;
}
.sanjiao{

	border-top: 20rpx solid #000000 ; 
	border-right: 20rpx solid transparent;
	border-left: 20rpx solid transparent;
	border-bottom: 20rpx solid transparent;
	position: absolute;
	bottom: -40rpx;
	left: 20%;
}

