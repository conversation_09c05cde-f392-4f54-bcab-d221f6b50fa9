<view class="container"><block wx:if="{{isload}}"><block><view class="address-add flex-y-center"><view class="f1">桌台信息</view><block wx:if="{{tableinfo.id}}"><view class="f2 flex1"><view style="font-weight:bold;color:#111111;font-size:30rpx;">{{tableinfo.name}}<text style="font-size:24rpx;font-weight:normal;color:#666;margin-left:10rpx;">{{tableinfo.seat+"人桌"}}</text></view></view></block><block wx:else><view class="f2 flex1">请扫描桌台二维码</view></block><image class="f3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view><block wx:for="{{$root.l1}}" wx:for-item="buydata" wx:for-index="index" wx:key="index"><view class="buydata"><view class="buystatus flex flex-y-center"><view class="leftline" style="{{'background-color:'+(buydata.m0)+';'}}"></view>待下单</view><view class="bcontent"><view class="product"><block wx:for="{{buydata.l0}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><block wx:if="{{index2<4||waitordershowall==true}}"><view class="item flex"><view class="img" data-url="{{'product?id='+item.$orig.product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.$orig.product.pic}}"></image></view><view class="info flex1"><view class="f1">{{item.$orig.product.name}}</view><block wx:if="{{item.$orig.guige.name}}"><view class="f2">{{"规格："+item.$orig.guige.name+item.$orig.jldata.jltitle+''}}<block wx:if="{{item.$orig.jldata.njltitle}}"><text>{{"（"+item.$orig.jldata.njltitle+")"}}</text></block></view></block><block wx:if="{{item.g0}}"><view class="f2"><block wx:for="{{item.$orig.guige.ggtext}}" wx:for-item="gitem" wx:for-index="index"><view>{{gitem}}</view></block></view></block><view class="f3"><text style="font-weight:bold;">{{'￥'+item.g1+''}}<block wx:if="{{item.$orig.product.product_type&&item.$orig.product.product_type==1}}"><text style="font-size:20rpx;">/斤</text></block></text><text style="padding-left:20rpx;">{{'× '+item.$orig.num}}</text><block wx:if="{{item.$orig.product.product_type&&item.$orig.product.product_type==1}}"><text style="font-size:20rpx;">斤</text></block></view></view></view></block></block><block wx:if="{{buydata.g2}}"><view data-event-opts="{{[['tap',[['doWaitorderShowAll',['$event']]]]]}}" class="storeviewmore" bindtap="__e">- 查看更多 -</view></block></view></view></view></block><block wx:for="{{$root.l3}}" wx:for-item="buydata" wx:for-index="index"><view class="buydata"><view class="buystatus flex flex-y-center"><view class="leftline" style="{{'background-color:'+(buydata.m1)+';'}}"></view>已下单</view><view class="bcontent"><view class="product"><block wx:for="{{buydata.l2}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><block wx:if="{{index2<4||ordershowall==true}}"><view class="item flex"><view class="img" data-url="{{'product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.$orig.pic}}"></image></view><view class="info flex1"><view class="f1">{{item.$orig.name}}</view><block wx:if="{{item.$orig.ggname}}"><view class="f2">{{"规格："+item.$orig.ggname}}<block wx:if="{{item.$orig.njltitle}}"><text>{{"（"+item.$orig.njltitle+")"}}</text></block></view></block><view class="f3"><text style="font-weight:bold;">{{'￥'+item.g3+''}}<block wx:if="{{item.$orig.product.product_type&&item.$orig.product.product_type==1}}"><text style="font-size:20rpx;">/斤</text></block></text><text style="padding-left:20rpx;">{{'× '+item.$orig.num}}</text><block wx:if="{{item.$orig.product.product_type&&item.$orig.product.product_type==1}}"><text style="font-size:20rpx;">斤</text></block></view></view></view></block></block><block wx:if="{{buydata.g4}}"><view data-event-opts="{{[['tap',[['doOrderShowAll',['$event']]]]]}}" class="storeviewmore" bindtap="__e">- 查看更多 -</view></block></view></view></view></block><view style="width:100%;height:182rpx;"></view><view class="{{['bottombar','flex-row',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view data-event-opts="{{[['tap',[['goback',['$event']]]]]}}" class="btn" bindtap="__e">继续点餐</view><view data-event-opts="{{[['tap',[['toBuy',['$event']]]]]}}" class="btn" style="{{'background-color:'+($root.m2)+';'+('border-color:'+($root.m3)+';')+('color:'+('#fff')+';')}}" bindtap="__e">现在下单</view></view></block></block><block wx:if="{{loading}}"><loading vue-id="51fed37b-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="51fed37b-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>