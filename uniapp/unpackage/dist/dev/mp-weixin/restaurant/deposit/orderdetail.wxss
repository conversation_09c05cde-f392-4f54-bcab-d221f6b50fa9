
.container{ width:100%;}
.topbg{width: 94%;margin:20rpx 3%;border-radius:8rpx;overflow:hidden}
.topbg .img{width:100%;height:auto}
.order-content{display:flex;flex-direction:column}
.order-box{ width: 94%;margin:0 3%;margin-bottom:20rpx;padding:10rpx 0 10rpx 20rpx; background: #fff;border-radius:8px;display:flex;position:relative}
.order-box .pic{ width: 120rpx; height: 120rpx;}
.order-box .pic .img{ width: 120rpx; height: 120rpx;}
.order-box .detail{display:flex;flex-direction:column;margin-left:20rpx;flex:1;margin-top:6rpx}
.order-box .detail .t1{font-size:28rpx;font-weight:bold;height:40rpx;line-height:40rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.order-box .detail .t2{height: 36rpx;line-height: 36rpx;color: #999;overflow: hidden;font-size: 22rpx;}
.order-box .detail .t3{display:flex;height:40rpx;line-height:40rpx;color: #ff4246;}
.order-box .takeout{display:flex;align-items:center;justify-content:center;padding:0 24rpx;height:52rpx;position:absolute;top:50%;margin-top:-26rpx;right:0;border-radius:26rpx 0 0 26rpx;background:#FFE8E1;color:#222222;font-size:24rpx;font-weight:bold}
.order-box .takeout .img{width:28rpx;height:28rpx;margin-right:6rpx}
.order-box .takeout.st0{color:#f55}
.order-box .takeout.st2{background:#F7F7F7;color:#BBBBBB}
.order-box .takeout.st3{background:#F7F7F7;color:#888}
.orderinfo{width:94%;margin:0 3%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}
.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:0px dashed #ededed;overflow:hidden}
.orderinfo .item{ border-bottom: 0;}
.orderinfo .item .t1{width:200rpx;flex-shrink:0}
.orderinfo .item .t2{flex:1;text-align:right}
.orderinfo .item .red{color:red}
.bottom{ width: 100%; padding: 16rpx 20rpx;background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-between;align-items:center;
box-shadow: 0px 10px 15px 0px rgba(0, 0, 0, 0.06);}
.btn1{margin-left:20rpx;width:370rpx;height:88rpx;line-height:88rpx;color:#fff;border-radius:44rpx;text-align:center;font-weight:bold}
.btn2{margin-left:20rpx;width:280rpx;height:88rpx;line-height:88rpx;color:#333;background:#fff;border:1px solid #cdcdcd;font-weight:bold;border-radius:44rpx;text-align:center}
.takeoutBox .btn {border-radius:44rpx; margin: 0 auto; width: 96%; color: #FFF;}
.takeoutBox { padding-bottom: 30rpx;}
.popup__modal{ min-height: 0;position: fixed;} 


