<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{needaddress==0}}"><view class="address-add"><view class="linkitem"><text class="f1">联 系 人：</text><input class="input" type="text" placeholder="请输入您的姓名" placeholder-style="color:#626262;font-size:28rpx" data-event-opts="{{[['input',[['inputLinkman',['$event']]]]]}}" value="{{linkman}}" bindinput="__e"/></view><view class="linkitem"><text class="f1">联系电话：</text><input class="input" type="text" placeholder="请输入您的手机号" placeholder-style="color:#626262;font-size:28rpx" data-event-opts="{{[['input',[['inputTel',['$event']]]]]}}" value="{{tel}}" bindinput="__e"/></view></view></block><block wx:else><view class="address-add flex-y-center" data-url="{{'/pagesB/address/'+(address.id?'address':'addressadd')+'?fromPage=buy&type='+(havetongcheng==1?'1':'0')}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image class="img" src="{{pre_url+'/static/img/address.png'}}"></image></view><block wx:if="{{address.id}}"><view class="f2 flex1"><view style="font-weight:bold;color:#111111;font-size:30rpx;">{{address.name+" "+address.tel+''}}<block wx:if="{{address.company}}"><text>{{address.company}}</text></block></view><view style="font-size:24rpx;">{{address.area+" "+address.address}}</view></view></block><block wx:else><view class="f2 flex1">请选择收货地址</view></block><image class="f3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:for="{{$root.l4}}" wx:for-item="buydata" wx:for-index="index" wx:key="index"><view class="buydata"><view class="btitle"><image class="img" src="{{pre_url+'/static/img/ico-shop.png'}}"></image>{{buydata.$orig.business.name}}</view><view class="bcontent"><view class="product"><block wx:for="{{buydata.l0}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><view class="item flex"><view class="img" data-url="{{'product?id='+item.$orig.product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.$orig.product.pic}}"></image></view><view class="info flex1"><view class="f1">{{item.$orig.product.name}}</view><view class="f2">{{"规格："+item.$orig.guige.name}}<block wx:if="{{!item.m0}}"><text>{{item.$orig.jldata.jltitle}}</text></block></view><view class="f3"><block wx:if="{{!item.m1}}"><text style="font-weight:bold;">{{"￥"+item.g0}}</text></block><block wx:else><text style="font-weight:bold;">{{"￥"+item.g1}}</text></block><text style="padding-left:20rpx;">{{'× '+item.$orig.num}}</text></view></view></view></block></view><view class="freight"><view class="f1">配送方式</view><view class="freight-ul"><view class="flex" style="width:100%;overflow-y:hidden;overflow-x:scroll;"><block wx:for="{{buydata.l1}}" wx:for-item="item" wx:for-index="idx2" wx:key="idx2"><block><view class="freight-li" style="{{(buydata.$orig.freightkey==idx2?'color:'+item.m2+';background:rgba('+item.m3+',0.2)':'')}}" data-bid="{{buydata.$orig.bid}}" data-index="{{idx2}}" data-event-opts="{{[['tap',[['changeFreight',['$event']]]]]}}" bindtap="__e">{{item.$orig.name}}</view></block></block></view></view><block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].minpriceset==1&&buydata.$orig.freightList[buydata.$orig.freightkey].minprice>0&&buydata.$orig.freightList[buydata.$orig.freightkey].minprice>buydata.$orig.product_price}}"><view class="freighttips">{{"满"+buydata.$orig.freightList[buydata.$orig.freightkey].minprice+"元起送，还差"+buydata.g2+"元"}}</view></block><block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].isoutjuli==1}}"><view class="freighttips">超出配送范围</view></block></view><block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].pstimeset==1}}"><view class="price"><view class="f1">{{(buydata.$orig.freightList[buydata.$orig.freightkey].pstype==1?'取货':'配送')+"时间"}}</view><view class="f2" data-bid="{{buydata.$orig.bid}}" data-event-opts="{{[['tap',[['choosePstime',['$event']]]]]}}" bindtap="__e">{{buydata.$orig.pstimetext==''?'请选择时间':buydata.$orig.pstimetext}}<text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></view></block><block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].pstype==1}}"><view class="storeitem"><view class="panel"><view class="f1">取货地点</view><view class="f2" data-bid="{{buydata.$orig.bid}}" data-freightkey="{{buydata.$orig.freightkey}}" data-storekey="{{buydata.$orig.freightList[buydata.$orig.freightkey].storekey}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e"><text class="iconfont icondingwei"></text>{{buydata.$orig.freightList[buydata.$orig.freightkey].storedata[buydata.$orig.freightList[buydata.$orig.freightkey].storekey].name}}</view></view><block wx:for="{{buydata.l2}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><block wx:if="{{idx<5||qhstoreshowall==true}}"><block><view class="radio-item" data-bid="{{buydata.$orig.bid}}" data-index="{{idx}}" data-event-opts="{{[['tap',[['choosestore',['$event']]]]]}}" catchtap="__e"><view class="f1">{{item.$orig.name+''}}</view><text style="color:#f50;">{{item.$orig.juli}}</text><view class="radio" style="{{(buydata.$orig.freightList[buydata.$orig.freightkey].storekey==idx?'background:'+item.m4+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></block></block><block wx:if="{{buydata.g3}}"><view data-event-opts="{{[['tap',[['doQhStoreShowAll',['$event']]]]]}}" class="storeviewmore" bindtap="__e">- 查看更多 -</view></block></view></block><block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].pstype==5}}"><view class="storeitem"><view class="panel"><view class="f1">配送门店</view><view class="f2" data-bid="{{buydata.$orig.bid}}" data-freightkey="{{buydata.$orig.freightkey}}" data-storekey="{{buydata.$orig.freightList[buydata.$orig.freightkey].storekey}}" data-event-opts="{{[['tap',[['openMendian',['$event']]]]]}}" bindtap="__e"><text class="iconfont icondingwei"></text>{{buydata.$orig.freightList[buydata.$orig.freightkey].storedata[buydata.$orig.freightList[buydata.$orig.freightkey].storekey].name+''}}</view></view><block wx:for="{{buydata.l3}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><block><block wx:if="{{idx<5||storeshowall==true}}"><view class="radio-item" data-bid="{{buydata.$orig.bid}}" data-index="{{idx}}" data-event-opts="{{[['tap',[['choosestore',['$event']]]]]}}" catchtap="__e"><view class="f1">{{item.$orig.name+''}}</view><text style="color:#f50;">{{item.$orig.juli}}</text><view class="radio" style="{{(buydata.$orig.freightList[buydata.$orig.freightkey].storekey==idx?'background:'+item.m5+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></block></block><block wx:if="{{buydata.g4}}"><view data-event-opts="{{[['tap',[['doQhStoreShowAll',['$event']]]]]}}" class="storeviewmore" bindtap="__e">- 查看更多 -</view></block></view></block><view class="price"><text class="f1">商品金额</text><text class="f2">{{"¥"+buydata.$orig.product_price}}</text></view><block wx:if="{{buydata.$orig.leveldk_money>0}}"><view class="price"><text class="f1">{{buydata.m6+"折扣("+userinfo.discount+"折)"}}</text><text class="f2">{{"-¥"+buydata.$orig.leveldk_money}}</text></view></block><block wx:if="{{buydata.$orig.manjian_money>0}}"><view class="price"><text class="f1">满减活动</text><text class="f2">{{"-¥"+buydata.$orig.manjian_money}}</text></view></block><view class="price"><text class="f1">{{buydata.$orig.freightList[buydata.$orig.freightkey].freight_price_txt||'运费'}}</text><text class="f2">{{"+¥"+buydata.$orig.freightList[buydata.$orig.freightkey].freight_price}}</text></view><block wx:if="{{buydata.$orig.pack_fee>0}}"><view class="price"><text class="f1">打包费</text><text class="f2">{{"+¥"+buydata.$orig.pack_fee}}</text></view></block><view class="price"><view class="f1">{{buydata.m7}}</view><block wx:if="{{buydata.$orig.couponCount>0}}"><view class="f2" data-bid="{{buydata.$orig.bid}}" data-event-opts="{{[['tap',[['showCouponList',['$event']]]]]}}" bindtap="__e"><text style="{{'color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx;'+('background:'+(buydata.m8)+';')}}">{{buydata.$orig.couponrid!=0?buydata.$orig.couponList[buydata.$orig.couponkey].couponname:buydata.$orig.couponCount+'张可用'}}</text><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></block><block wx:else><text class="f2" style="color:#999;">{{"无可用"+buydata.m9}}</text></block></view><block wx:if="{{buydata.$orig.cuxiaoCount>0}}"><view class="price"><view class="f1">促销活动</view><view class="f2" data-bid="{{buydata.$orig.bid}}" data-event-opts="{{[['tap',[['showCuxiaoList',['$event']]]]]}}" bindtap="__e"><text style="{{'color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx;'+('background:'+(buydata.m10)+';')}}">{{buydata.$orig.cuxiaoname?buydata.$orig.cuxiaoname:buydata.$orig.cuxiaoCount+'个可用'}}</text><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></view></block><block wx:for="{{buydata.$orig.freightList[buydata.$orig.freightkey].field_list}}" wx:for-item="item" wx:for-index="idx"><block><block wx:if="{{item.isshow==1}}"><view class="remark"><text class="f1">{{item.name}}</text><input class="flex1" type="text" placeholder="{{item.tips||'请输入'+item.name}}" data-field="{{idx}}" data-bid="{{buydata.$orig.bid}}" placeholder-style="color:#cdcdcd;font-size:28rpx" data-event-opts="{{[['input',[['inputfield',['$event']]]]]}}" bindinput="__e"/></view></block></block></block></view></view></block><block wx:if="{{userinfo.score2money>0&&(userinfo.scoremaxtype==0||userinfo.scoremaxtype==1&&userinfo.scoredkmaxmoney>0)}}"><view class="scoredk"><checkbox-group data-event-opts="{{[['change',[['scoredk',['$event']]]]]}}" class="flex" style="width:100%;" bindchange="__e"><view class="f1"><view>{{userinfo.score*1+" "+$root.m11+'可抵扣'}}<text style="color:#e94745;">{{userinfo.scoredk_money*1}}</text>元</view><block wx:if="{{userinfo.scoremaxtype==0&&userinfo.scoredkmaxpercent>0&&userinfo.scoredkmaxpercent<100}}"><view style="font-size:22rpx;color:#999;">{{"最多可抵扣订单金额的"+userinfo.scoredkmaxpercent+"%"}}</view></block><block wx:else><block wx:if="{{userinfo.scoremaxtype==1}}"><view style="font-size:22rpx;color:#999;">{{"最多可抵扣"+userinfo.scoredkmaxmoney+"元"}}</view></block></block></view><view class="f2">{{"使用"+$root.m12+'抵扣'}}<checkbox style="margin-left:6px;transform:scale(.8);" value="1"></checkbox></view></checkbox-group></view></block><view style="width:100%;height:182rpx;"></view><view class="footer flex"><view class="text1 flex1">总计：<text style="font-weight:bold;font-size:36rpx;">{{"￥"+alltotalprice}}</text></view><view data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" class="op" style="{{'background:'+('linear-gradient(-90deg,'+$root.m13+' 0%,rgba('+$root.m14+',0.8) 100%)')+';'}}" bindtap="__e">提交订单</view></view><block wx:if="{{couponvisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">{{"请选择"+$root.m15}}</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><couponlist vue-id="719f4826-1" couponlist="{{allbuydata[bid].couponList}}" choosecoupon="{{true}}" selectedrid="{{allbuydata[bid].couponrid}}" bid="{{bid}}" data-event-opts="{{[['^chooseCoupon',[['chooseCoupon']]]]}}" bind:chooseCoupon="__e" bind:__l="__l"></couponlist></view></view></view></block><block wx:if="{{pstimeDialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hidePstimeDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">{{"请选择"+(allbuydata[nowbid].freightList[allbuydata[nowbid].freightkey].pstype==1?'取货':'配送')+"时间"}}</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hidePstimeDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l5}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="pstime-item" data-index="{{index}}" data-event-opts="{{[['tap',[['pstimeRadioChange',['$event']]]]]}}" bindtap="__e"><view class="flex1">{{item.$orig.title}}</view><view class="radio" style="{{(allbuydata[nowbid].freight_time==item.$orig.value?'background:'+item.m16+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></view></view></view></block><block wx:if="{{cuxiaovisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">优惠促销</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><view class="cuxiao-desc"><view class="cuxiao-item" data-id="0" data-event-opts="{{[['tap',[['changecx',['$event']]]]]}}" bindtap="__e"><view class="type-name"><text style="color:#333;">不使用促销</text></view><view class="radio" style="{{(cxid==0?'background:'+$root.m17+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view><block wx:for="{{$root.l6}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="cuxiao-item" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['changecx',['$event']]]]]}}" bindtap="__e"><view class="type-name"><text style="border-radius:4px;border:1px solid #f05423;color:#ff550f;font-size:20rpx;padding:2px 5px;">{{item.$orig.tip}}</text><text style="color:#333;padding-left:20rpx;">{{item.$orig.name}}</text></view><view class="radio" style="{{(cxid==item.$orig.id?'background:'+item.m18+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></view><block wx:if="{{cuxiaoinfo.product}}"><view style="padding:0 40rpx;" id="cxproinfo"><view class="product"><view class="item flex" style="background:#f5f5f5;"><view class="img" data-url="{{'product?id='+cuxiaoinfo.product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{cuxiaoinfo.product.pic}}"></image></view><view class="info flex1"><view class="f1">{{cuxiaoinfo.product.name}}</view><view class="f2">{{"规格："+cuxiaoinfo.guige.name}}</view><view class="f3"><text style="font-weight:bold;">{{"￥"+cuxiaoinfo.guige.sell_price}}</text><text style="padding-left:20rpx;">× 1</text></view></view></view></view></view></block><view style="width:100%;height:120rpx;"></view><view style="width:100%;position:absolute;bottom:0;padding:20rpx 5%;background:#fff;"><view data-event-opts="{{[['tap',[['chooseCuxiao',['$event']]]]]}}" style="{{'width:100%;height:80rpx;line-height:80rpx;border-radius:40rpx;text-align:center;color:#fff;'+('background:'+($root.m19)+';')}}" bindtap="__e">确 定</view></view></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="719f4826-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="719f4826-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><wxxieyi vue-id="719f4826-4" bind:__l="__l"></wxxieyi></view>