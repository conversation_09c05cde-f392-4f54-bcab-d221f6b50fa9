<view class="container"><block wx:if="{{isload}}"><block><view class="tab-box shopping"><view class="page-tab"><view class="page-tab2"><view class="{{['item '+(curTopIndex==-1?'on':'')]}}" data-index="{{-1}}" data-id="{{0}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">全部</view><block wx:for="{{clist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['item '+(curTopIndex==index?'on':'')]}}" data-index="{{index}}" data-id="{{item.id}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">{{item.name}}</view></block></block></view></view><view class="shop-box"><block wx:for="{{contentList}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="shop-item"><image class="shop-img" src="{{item.pic?item.pic:logo}}" mode="aspectFill"></image><view class="f2 flex-col flex1"><view class="shop-name multi-ellipsis-2">{{''+item.name+''}}</view><view class="desc">{{textset['座位数']+"："+item.seat}}</view><view class="desc"><block wx:if="{{set.show_booking_fee}}"><text>{{"预定费："+item.booking_fee}}</text></block>{{"最低消费："+item.limit_fee}}</view></view><view class="f3 button" data-url="{{'add?bid='+opt.bid+'&tableId='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">选择</view></view></block></view></view><block wx:if="{{nomore}}"><nomore vue-id="841c4c98-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="841c4c98-2" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="841c4c98-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="841c4c98-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>