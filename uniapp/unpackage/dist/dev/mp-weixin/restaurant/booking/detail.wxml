<view class="container"><block wx:if="{{isload}}"><block><view class="head-bg"><block wx:if="{{detail.check_status==0&&detail.status==0}}"><view class="title"><block wx:if="{{is_book_order==1&&!hasproinfo}}"><view>预定成功，请继续下单</view></block><block wx:else><block wx:if="{{is_book_order==1&&hasproinfo}}"><view>预定成功，等待审核</view></block><block wx:else><view>预定成功，请支付</view></block></block></view></block><block wx:if="{{detail.check_status==0&&(detail.status==1||detail.status==2||detail.status==3)}}"><view class="title">预定成功，等待审核</view></block><block wx:if="{{(detail.check_status==0||detail.check_status==1)&&detail.status==4}}"><view class="title">预定已关闭</view></block><block wx:if="{{detail.check_status==1&&detail.status>0&&detail.status!=4}}"><view class="title">预定成功</view></block><block wx:if="{{detail.check_status==1&&detail.status==0}}"><view class="title">预定成功，请支付</view></block><block wx:if="{{detail.check_status==-1&&detail.status>0}}"><view class="title">预定失败，商家驳回</view></block><block wx:if="{{detail.check_status!=-1&&business.show_tip==1&&detail.status!=4}}"><view class="text-center mt20">请在预定时间20分钟内到店。</view></block><block wx:if="{{detail.check_status==-1&&detail.status==0}}"><view class="text-center mt20">请重新预定。</view></block></view><view class="card-view"><view class="card-wrap"><view class="card-title">{{business.name}}</view><view class="mt20">{{business.address}}</view></view><view class="card-wrap"><view class="card-title">预定信息</view><view class="info-item mt"><view class="t1">预定人</view><view class="t2">{{detail.linkman}}</view></view><view class="info-item"><view class="t1">手机号</view><view class="t2">{{detail.tel}}</view></view><view class="info-item"><view class="t1">预定时间</view><view class="t2">{{detail.booking_time}}</view></view><view class="info-item"><view class="t1">{{textset['用餐人数']}}</view><view class="t2">{{detail.seat}}</view></view><view class="info-item"><view class="t1">{{textset['预定桌台']}}</view><view class="t2">{{detail.tableName}}</view></view><view class="info-item info-textarea"><view class="t1">备注信息</view><view class="t2">{{detail.message}}</view></view></view><block wx:if="{{hasproinfo&&is_book_order==1}}"><view class="card-wrap"><view class="card-title">菜品列表</view><view class="product"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="content"><view data-url="{{'product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.$orig.pic}}"></image></view><view class="detail"><text class="t1">{{item.$orig.name}}</text><view class="t2 flex flex-y-center flex-bt"><block wx:if="{{item.g0}}"><view class="flex-col"><block wx:for="{{item.$orig.ggtext}}" wx:for-item="item2" wx:for-index="index"><block><text class="t2">{{item2}}</text></block></block></view></block><block wx:if="{{item.$orig.ggname}}"><text>{{item.$orig.ggname+''}}</text></block><block wx:if="{{item.$orig.jltitle}}"><text>{{item.$orig.jltitle}}</text></block></view><view class="t3"><block wx:if="{{item.$orig.jlprice}}"><text class="x1 flex1">{{"￥"+item.g1}}<block wx:if="{{item.$orig.product_type&&item.$orig.product_type==1}}"><text style="font-size:20rpx;">斤</text></block></text></block><block wx:else><text class="x1 flex1">{{"￥"+item.$orig.sell_price}}<block wx:if="{{item.$orig.product_type&&item.$orig.product_type==1}}"><text style="font-size:20rpx;">斤</text></block></text></block><text class="x2">{{"×"+item.$orig.num}}<block wx:if="{{item.$orig.product_type&&item.$orig.product_type==1}}"><text style="font-size:20rpx;">斤</text></block></text></view></view></view></block></view><block wx:if="{{hasproinfo&&is_book_order==1}}"><view class="orderinfo"><view class="item"><text class="t1">商品金额</text><text class="t2 red">{{"¥"+proinfo.product_price}}</text></view><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+proinfo.totalprice}}</text></view><view class="item"><text class="t1">订单状态</text><block wx:if="{{proinfo.status==0}}"><text class="t2">未付款</text></block><block wx:if="{{proinfo.status==1&&proinfo.paytypeid!=4}}"><text class="t2">已付款</text></block><block wx:if="{{proinfo.status==1&&proinfo.paytypeid==4}}"><text class="t2">线下支付</text></block><block wx:if="{{proinfo.status==12}}"><text class="t2">商家已接单</text></block><block wx:if="{{proinfo.status==2}}"><text class="t2">已发货</text></block><block wx:if="{{proinfo.status==3}}"><text class="t2">已收货</text></block><block wx:if="{{proinfo.status==4}}"><text class="t2">已关闭</text></block></view><block wx:if="{{proinfo.refund_status>0}}"><view class="item"><text class="t1">退款状态</text><block wx:if="{{proinfo.refund_status==1}}"><text class="t2 red">{{"审核中,¥"+proinfo.refund_money}}</text></block><block wx:if="{{proinfo.refund_status==2}}"><text class="t2 red">{{"已退款,¥"+proinfo.refund_money}}</text></block><block wx:if="{{proinfo.refund_status==3}}"><text class="t2 red">{{"已驳回,¥"+proinfo.refund_money}}</text></block></view></block><block wx:if="{{proinfo.refund_status>0}}"><view class="item"><text class="t1">退款时间</text><text class="t2 red">{{proinfo.refund_time}}</text></view></block><block wx:if="{{proinfo.refund_status>0}}"><view class="item"><text class="t1">退款原因</text><text class="t2 red">{{proinfo.refund_reason}}</text></view></block><block wx:if="{{proinfo.refund_checkremark}}"><view class="item"><text class="t1">审核备注</text><text class="t2 red">{{proinfo.refund_checkremark}}</text></view></block></view></block></view></block></view><view style="width:100%;height:calc(160rpx + env(safe-area-inset-bottom));"></view><view class="bottom notabbarbot"><block wx:if="{{hasproinfo&&proinfo.status==0&&is_book_order==1}}"><view class="btn1" data-id="{{proinfo.payorderid}}" data-event-opts="{{[['tap',[['toPay',['$event']]]]]}}" bindtap="__e">去付款</view></block><block wx:if="{{!hasproinfo&&is_book_order==1}}"><view data-event-opts="{{[['tap',[['toOrder',['$event']]]]]}}" class="btn1" bindtap="__e">继续点餐</view></block><block wx:if="{{detail.status!=4}}"><view data-event-opts="{{[['tap',[['cancel',['$event']]]]]}}" class="btn1" bindtap="__e">取消</view></block><block wx:if="{{detail.status==0&&is_book_order==0}}"><view class="btn1" data-url="{{'/pagesExt/pay/pay?id='+detail.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">去支付</view></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="11d89aab-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="11d89aab-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>