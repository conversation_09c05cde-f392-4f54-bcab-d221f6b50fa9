
.page-tab{display:flex;width:100%;overflow-x:scroll;border-bottom: 1px #f5f5f5 solid;padding:0 10rpx; background-color: #FFFFFF;}
.page-tab2{display:flex;width:auto;min-width:100%}
.page-tab2 .item{width:auto;padding:0 20rpx;font-size:28rpx;text-align: center; color:#333; height:90rpx; line-height:90rpx; overflow: hidden;position:relative;flex-shrink:0;flex-grow: 1;}
.page-tab2 .on{color:#FE5B07; font-size: 30rpx;}
.shop-box {padding: 0 30rpx 30rpx;}
.shop-box .shop-item {width: 100%;margin-top: 20rpx; display: flex;background: #fff;border-radius: 8rpx; padding: 20rpx; position: relative;}
.shop-box .shop-item .shop-img {width: 200rpx;height: 200rpx;border-radius: 8rpx; background-color: #eee;}
.shop-box .shop-item .f2 {margin-top: 12rpx;justify-content: space-between; padding: 10rpx;}
.shop-box .shop-item .shop-name {font-size: 32rpx;color: #333;}
.shop-box .shop-item .desc {color: #999;}
.shop-box .shop-item .desc text{margin-right: 16rpx;}
.shop-box .shop-item .f3 {width: 120rpx; align-items: center;position: absolute;top: 20rpx; right: 20rpx;}
.button{width: 100rpx;height:70rpx;line-height:70rpx;font-size:28rpx;color:#FFFFFF;  background: linear-gradient(90deg, #FF7D15 0%, #FC5729 100%);
border-radius: 10rpx; text-align: center;}


