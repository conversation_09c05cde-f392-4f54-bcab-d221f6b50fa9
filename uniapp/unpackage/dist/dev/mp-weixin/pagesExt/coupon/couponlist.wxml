<view class="container"><block wx:if="{{isload}}"><block><view class="coupon-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="coupon" data-url="{{'coupondetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="pt_left"><view class="pt_left-content"><block wx:if="{{item.$orig.type==1}}"><view class="f1" style="{{'color:'+(item.m0)+';'}}"><text class="t0">￥</text><text class="t1">{{item.$orig.money}}</text></view></block><block wx:if="{{item.$orig.type==10}}"><view class="f1" style="{{'color:'+(item.m1)+';'}}"><text class="t1">{{item.$orig.discount/10}}</text><text class="t2">折</text></view></block><block wx:if="{{item.$orig.type==3}}"><view class="f1" style="{{'color:'+(item.m2)+';'}}"><text class="t1">{{item.$orig.limit_count}}</text><text class="t2">次</text></view></block><block wx:if="{{item.$orig.type==5}}"><view class="f1" style="{{'color:'+(item.m3)+';'}}"><text class="t0">￥</text><text class="t1">{{item.$orig.money}}</text></view></block><block wx:if="{{item.$orig.type!=1&&item.$orig.type!=10&&item.$orig.type!=3&&item.$orig.type!=5}}"><block><view class="f1" style="{{'color:'+(item.m4)+';'}}">{{item.$orig.type_txt}}</view></block></block><block wx:if="{{item.$orig.type==1||item.$orig.type==10||item.$orig.type==4||item.$orig.type==5||item.$orig.type==10}}"><view class="f2" style="{{'color:'+(item.m5)+';'}}"><block wx:if="{{item.$orig.minprice>0}}"><text>{{"满"+item.$orig.minprice+"元可用"}}</text></block><block wx:else><text>无门槛</text></block></view></block></view></view><view class="pt_right"><view class="f1"><view class="t1">{{item.$orig.name}}</view><text class="t2" style="{{'background:'+('rgba('+item.m6+',0.1)')+';'+('color:'+(item.m7)+';')}}">{{item.$orig.type_txt}}</text><block wx:if="{{item.$orig.house_status}}"><view class="t4">一户仅限一次</view></block><view class="t3" style="{{(item.$orig.bid>0?'margin-top:0':'margin-top:10rpx')}}">{{"有效期至 "+item.$orig.yxqdate}}</view><block wx:if="{{item.$orig.bid>0}}"><view class="t4">{{"适用商家："+item.$orig.bname}}</view></block></view><block wx:if="{{item.$orig.perlimit>0&&item.$orig.haveget>=item.$orig.perlimit}}"><button class="btn" style="background:#9d9d9d;">已领取</button></block><block wx:else><block wx:if="{{item.$orig.stock<=0}}"><button class="btn" style="background:#9d9d9d;">已抢光了</button></block><block wx:else><block wx:if="{{item.$orig.use_tongzheng==1}}"><button class="btn" style="{{'background:'+('linear-gradient(270deg,'+item.m8+' 0%,rgba('+item.m9+',0.8) 100%)')+';'}}" data-id="{{item.$orig.id}}" data-tongzheng="{{item.$orig.tongzheng}}" data-key="{{index}}" data-event-opts="{{[['tap',[['getcouponbytongzheng',['$event']]]]]}}" catchtap="__e">兑换</button></block><block wx:else><block wx:if="{{item.$orig.is_birthday_coupon==1&&item.$orig.birthday_coupon_status>0}}"><block><block wx:if="{{item.$orig.birthday_coupon_status==1}}"><button class="btn" style="background:#9d9d9d;">不可领取</button></block><block wx:else><button class="btn" style="{{'background:'+('linear-gradient(270deg,'+item.m10+' 0%,rgba('+item.m11+',0.8) 100%)')+';'}}" data-url="/pagesExt/my/setbirthday" data-key="{{index}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">设置生日</button></block></block></block><block wx:else><button class="btn" style="{{'background:'+('linear-gradient(270deg,'+item.m12+' 0%,rgba('+item.m13+',0.8) 100%)')+';'}}" data-id="{{item.$orig.id}}" data-price="{{item.$orig.price}}" data-score="{{item.$orig.score}}" data-key="{{index}}" data-event-opts="{{[['tap',[['getcoupon',['$event']]]]]}}" catchtap="__e">{{item.$orig.price>0?'购买':item.$orig.score>0?'兑换':'领取'}}</button></block></block></block></block></view></view></block></view><block wx:if="{{nodata}}"><nodata vue-id="5a3a761d-1" text="{{'暂无可领'+$root.m14}}" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="5a3a761d-2" bind:__l="__l"></nomore></block></block></block><block wx:if="{{loading}}"><loading vue-id="5a3a761d-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="5a3a761d-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5a3a761d-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>