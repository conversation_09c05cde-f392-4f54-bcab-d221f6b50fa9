<view class="container"><block wx:if="{{isload}}"><block><view class="couponbg" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}"></view><view class="orderinfo" style="{{'background-color:'+(coupon.bg_color)+';'}}"><block wx:if="{{record.id}}"><block><view class="topitem"><block wx:if="{{record.type==1}}"><view class="f1" style="{{'color:'+($root.m2)+';'}}"><text style="font-size:32rpx;">￥</text><text class="t1">{{record.money}}</text></view></block><block wx:else><block wx:if="{{record.type==10}}"><view class="f1" style="{{'color:'+($root.m3)+';'}}"><text class="t1">{{$root.g0}}</text><text style="font-size:32rpx;">折</text></view></block><block wx:else><block wx:if="{{record.type==2}}"><view class="f1" style="{{'color:'+($root.m4)+';'}}">礼品券</view></block><block wx:else><block wx:if="{{record.type==3}}"><view class="f1" style="{{'color:'+($root.m5)+';'}}"><text class="t1">{{record.limit_count}}</text><text class="t2">次</text></view></block><block wx:else><block wx:if="{{record.type==4}}"><view class="f1" style="{{'color:'+($root.m6)+';'}}">抵运费</view></block><block wx:else><block wx:if="{{record.type==5}}"><view class="f1" style="{{'color:'+($root.m7)+';'}}">餐饮券</view></block><block wx:else><block wx:if="{{record.type==20}}"><view class="f1" style="{{'color:'+($root.m8)+';'}}">券包</view></block></block></block></block></block></block></block><view class="f2"><view class="t1" style="{{'color:'+(coupon.title_color)+';'}}">{{record.couponname}}</view><block wx:if="{{record.type==1||record.type==4||record.type==5}}"><view class="t2"><block wx:if="{{record.minprice>0}}"><text style="{{'color:'+(coupon.font_color)+';'}}">{{"满"+record.minprice+"元可用"}}</text></block><block wx:else><text style="{{'color:'+(coupon.font_color)+';'}}">无门槛</text></block></view></block><block wx:if="{{record.type==1}}"><view class="t2" style="{{'color:'+(coupon.font_color?coupon.font_color:'#2B2B2B')+';'}}">代金券<block wx:if="{{coupon.isgive==1||coupon.isgive==2}}"><text>（可赠送）</text></block></view></block><block wx:if="{{record.type==10}}"><view class="t2" style="{{'color:'+(coupon.font_color?coupon.font_color:'#2B2B2B')+';'}}">折扣券<block wx:if="{{coupon.isgive==1||coupon.isgive==2}}"><text>（可赠送）</text></block></view></block><block wx:if="{{record.type==2}}"><view class="t2" style="{{'color:'+(coupon.font_color?coupon.font_color:'#2B2B2B')+';'}}">礼品券<block wx:if="{{coupon.isgive==1||coupon.isgive==2}}"><text>（可赠送）</text></block></view></block><block wx:if="{{record.type==3}}"><view class="t2" style="{{'color:'+(coupon.font_color?coupon.font_color:'#2B2B2B')+';'}}">计次券<block wx:if="{{coupon.isgive==1||coupon.isgive==2}}"><text>（可赠送）</text></block></view></block><block wx:if="{{record.type==4}}"><view class="t2" style="{{'color:'+(coupon.font_color?coupon.font_color:'#2B2B2B')+';'}}">运费抵扣券<block wx:if="{{coupon.isgive==1||coupon.isgive==2}}"><text>（可赠送）</text></block></view></block><block wx:if="{{record.type==5}}"><view class="t2" style="{{'color:'+(coupon.font_color?coupon.font_color:'#2B2B2B')+';'}}">餐饮券<block wx:if="{{coupon.isgive==1||coupon.isgive==2}}"><text>（可赠送）</text></block></view></block><block wx:if="{{record.type==20}}"><view class="t2" style="{{'color:'+(coupon.font_color?coupon.font_color:'#2B2B2B')+';'}}">券包<block wx:if="{{coupon.isgive==1||coupon.isgive==2}}"><text>（可赠送）</text></block></view></block></view></view><block wx:if="{{coupon.bid!=0}}"><view class="item"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">适用商家</text><text class="t2" style="{{'color:'+(coupon.font_color)+';'}}">{{coupon.bname}}</text></view></block><block wx:if="{{record.type==3&&record.status==1}}"><view class="item"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">次数</text><view class="flex-x-start" style="{{'color:'+(coupon.title_color)+';'}}">共计<view style="{{'color:'+(coupon.font_color)+';'}}">{{record.limit_count}}</view>次  剩余<view style="{{'color:'+($root.m9)+';'}}" data-url="{{'/pagesExt/coupon/record?crid='+record.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{record.surplus_count}}</view>次</view></view></block><block wx:if="{{record.status==0&&record.hexiaoqr&&coupon.isgive!=2}}"><view class="item flex-col"><block wx:if="{{(coupon.type==5||coupon.type==10)&&record.cashdesk_hexiaoqr}}"><view class="flex flex-bt"><text data-event-opts="{{[['tap',[['changeqrcode',['nomal']]]]]}}" class="t1" style="{{'text-align:center;'+('color:'+(qrcodetype=='nomal'?'red':'')+';')}}" bindtap="__e">核销码</text><text data-event-opts="{{[['tap',[['changeqrcode',['cashdesk']]]]]}}" class="t1" style="{{'text-align:center;'+('color:'+(qrcodetype=='cashdesk'?'red':'')+';')}}" bindtap="__e">收银核销码</text></view></block><block wx:else><block wx:if="{{coupon.type==51}}"><view class="flex flex-bt"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">收银核销码</text></view></block><block wx:else><view class="flex flex-bt"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">核销码</text></view></block></block><block wx:if="{{record.type==3}}"><view style="margin-bottom:20rpx;"><view class="flex-x-center" style="{{'color:'+(coupon.title_color)+';'}}">共计<view style="{{'color:'+(coupon.font_color)+';'}}">{{record.limit_count}}</view>次  剩余<view style="{{'color:'+($root.m10)+';'}}" data-url="{{'/pagesExt/coupon/record?crid='+record.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{record.surplus_count}}</view>次</view></view></block><view class="flex-x-center"><image style="width:250rpx;height:250rpx;" src="{{hexiaoqr}}" data-url="{{hexiaoqr}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view><block wx:if="{{coupon.type==51||qrcodetype=='cashdesk'}}"><block><text class="flex-x-center" style="{{'color:'+(coupon.title_color)+';'+('font-weight:'+('700')+';')}}">{{"券码："+record.code}}</text><text class="flex-x-center" style="{{'color:'+(coupon.title_color)+';'}}">(仅限店内收银台使用)</text></block></block><text class="flex-x-center" style="{{'color:'+(coupon.title_color)+';'}}">到店使用时请出示核销码进行核销</text></view></block><view class="item"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">领取时间</text><text class="t2" style="{{'color:'+(coupon.font_color)+';'}}">{{record.createtime}}</text></view><block wx:if="{{record.status==1}}"><block><view class="item"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">使用时间</text><text class="t2" style="{{'color:'+(coupon.font_color)+';'}}">{{record.usetime}}</text></view></block></block><view class="item flex-col"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">有效期</text><text class="t2" style="{{'color:'+(coupon.font_color)+';'}}">{{record.starttime+" 至 "+record.endtime}}</text></view><block wx:if="{{$root.g1>0}}"><view class="item flex-col"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">包含</text><block wx:for="{{coupon.pack_coupon_list}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="t2" style="{{'color:'+(coupon.font_color)+';'}}">{{item.name}}</view></block></view></block><view class="item flex-col"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">使用说明</text><view class="t2" style="{{'color:'+(coupon.font_color)+';'}}">{{coupon.usetips}}</view></view></block></block><block wx:else><block><view class="topitem"><block wx:if="{{coupon.type==1}}"><view class="f1" style="{{'color:'+($root.m11)+';'}}"><text style="font-size:32rpx;">￥</text><text class="t1">{{coupon.money}}</text></view></block><block wx:if="{{coupon.type==10}}"><view class="f1" style="{{'color:'+($root.m12)+';'}}"><text class="t1">{{coupon.discount/10}}</text><text style="font-size:32rpx;">折</text></view></block><block wx:else><block wx:if="{{coupon.type==2}}"><view class="f1" style="{{'color:'+($root.m13)+';'}}">礼品券</view></block><block wx:else><block wx:if="{{coupon.type==3}}"><view class="f1" style="{{'color:'+($root.m14)+';'}}"><text class="t1">{{coupon.limit_count}}</text><text class="t2">次</text></view></block><block wx:else><block wx:if="{{coupon.type==4}}"><view class="f1" style="{{'color:'+($root.m15)+';'}}">抵运费</view></block><block wx:else><block wx:if="{{coupon.type==5}}"><view class="f1" style="{{'color:'+($root.m16)+';'}}">餐饮券</view></block><block wx:else><block wx:if="{{coupon.type==20}}"><view class="f1" style="{{'color:'+($root.m17)+';'}}">券包</view></block></block></block></block></block></block><view class="f2"><view class="t1" style="{{'color:'+(coupon.title_color)+';'}}">{{coupon.name}}</view><block wx:if="{{coupon.type==1||coupon.type==4||coupon.type==5}}"><view class="t2"><block wx:if="{{coupon.minprice>0}}"><text style="{{'color:'+(coupon.font_color?coupon.font_color:'#2B2B2B')+';'}}">{{"满"+coupon.minprice+"元可用"}}</text></block><block wx:else><text style="{{'color:'+(coupon.font_color?coupon.font_color:'#2B2B2B')+';'}}">无门槛</text></block></view></block><block wx:if="{{coupon.type==1}}"><text class="t2" style="{{'color:'+(coupon.font_color?coupon.font_color:'#2B2B2B')+';'}}">代金券</text></block><block wx:if="{{coupon.type==10}}"><text class="t2" style="{{'color:'+(coupon.font_color?coupon.font_color:'#2B2B2B')+';'}}">折扣券</text></block><block wx:if="{{coupon.type==2}}"><text class="t2" style="{{'color:'+(coupon.font_color?coupon.font_color:'#2B2B2B')+';'}}">礼品券</text></block><block wx:if="{{coupon.type==3}}"><text class="t2" style="{{'color:'+(coupon.font_color?coupon.font_color:'#2B2B2B')+';'}}">计次券</text></block><block wx:if="{{coupon.type==4}}"><text class="t2" style="{{'color:'+(coupon.font_color?coupon.font_color:'#2B2B2B')+';'}}">运费抵扣券</text></block><block wx:if="{{coupon.type==5}}"><text class="t2" style="{{'color:'+(coupon.font_color?coupon.font_color:'#2B2B2B')+';'}}">餐饮券</text></block><block wx:if="{{coupon.type==20}}"><text class="t2" style="{{'color:'+(coupon.font_color?coupon.font_color:'#2B2B2B')+';'}}">券包</text></block></view></view><block wx:if="{{coupon.bid!=0}}"><view class="item"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">适用商家</text><text class="t2" style="{{'color:'+(coupon.font_color)+';'}}">{{coupon.bname}}</text></view></block><block wx:if="{{coupon.house_status}}"><view class="item"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">领取限制</text><text class="t2" style="{{'color:'+(coupon.font_color)+';'}}">一户仅限一次</text></view></block><block wx:if="{{coupon.type==3}}"><block><view class="item"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">共计次数</text><text class="t2" style="{{'color:'+(coupon.font_color)+';'}}">{{coupon.limit_count+"次"}}</text></view><block wx:if="{{coupon.limit_perday>0}}"><block><view class="item"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">每天限制使用</text><text class="t2" style="{{'color:'+(coupon.font_color)+';'}}">{{coupon.limit_perday+"次"}}</text></view></block></block></block></block><block wx:if="{{coupon.use_tongzheng==1}}"><block><view class="item"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">{{"所需"+$root.m18}}</text><text class="t2" style="{{'color:'+(coupon.font_color)+';'}}">{{coupon.tongzheng+$root.m19}}</text></view></block></block><block wx:if="{{coupon.price>0}}"><block><view class="item"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">所需金额</text><text class="t2" style="{{'color:'+(coupon.font_color)+';'}}">{{"￥"+coupon.price}}</text></view></block></block><block wx:if="{{coupon.score>0}}"><block><view class="item"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">{{"所需"+$root.m20}}</text><text class="t2" style="{{'color:'+(coupon.font_color)+';'}}">{{coupon.score+$root.m21}}</text></view></block></block><view class="item"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">活动时间</text><text class="t2" style="{{'color:'+(coupon.font_color)+';'}}">{{coupon.starttime+" ~ "+coupon.endtime}}</text></view><block wx:if="{{coupon.type!=20}}"><view class="item"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">有效期</text><block wx:if="{{coupon.yxqtype==1}}"><block><text class="t2" style="{{'color:'+(coupon.font_color)+';'}}">{{coupon.yxqtime}}</text></block></block><block wx:else><block wx:if="{{coupon.yxqtype==2}}"><block><text class="t2" style="{{'color:'+(coupon.font_color)+';'}}">{{"领取后"+coupon.yxqdate+"天内有效"}}</text></block></block><block wx:else><block wx:if="{{coupon.yxqtype==3}}"><block><text class="t2" style="{{'color:'+(coupon.font_color)+';'}}">{{"领取后"+coupon.yxqdate+"天内有效（次日0点生效）"}}</text></block></block></block></block></view></block><block wx:if="{{$root.g2>0}}"><view class="item"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">包含</text><block wx:for="{{coupon.pack_coupon_list}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="t2" style="{{'color:'+(coupon.font_color)+';'}}">{{item.name}}</view></block></view></block><view class="item"><text class="t1" style="{{'color:'+(coupon.title_color)+';'}}">使用说明</text><view class="t2" style="{{'color:'+(coupon.font_color)+';'}}">{{coupon.usetips}}</view></view></block></block></view><block wx:if="{{coupon.use_tongzheng==1}}"><bloack vue-id="35f4770a-1" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{!record.id}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m22+' 0%,rgba('+$root.m23+',0.8) 100%)')+';'}}" data-id="{{coupon.id}}" data-price="{{coupon.price}}" data-tongzheng="{{coupon.tongzheng}}" data-event-opts="{{[['tap',[['getcouponbytongzheng',['$event']]]]]}}" bindtap="__e">立即兑换</view></block></bloack></block><block wx:if="{{coupon.is_birthday_coupon==1&&coupon.birthday_coupon_status>0}}"><bloack vue-id="35f4770a-2" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{coupon.birthday_coupon_status==3}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m24+' 0%,rgba('+$root.m25+',0.8) 100%)')+';'}}" data-url="/pagesExt/my/setbirthday" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">设置生日</view></block></bloack></block><block wx:else><block><block wx:if="{{!record.id}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m26+' 0%,rgba('+$root.m27+',0.8) 100%)')+';'}}" data-id="{{coupon.id}}" data-price="{{coupon.price}}" data-score="{{coupon.score}}" data-event-opts="{{[['tap',[['getcoupon',['$event']]]]]}}" bindtap="__e">{{coupon.price>0?'立即购买':coupon.score>0?'立即兑换':'立即领取'}}</view></block></block></block><block wx:if="{{mid==record.mid}}"><block><block wx:if="{{coupon.isgive!=2}}"><block><block wx:if="{{record.id&&(coupon.type==1||coupon.type==10)&&record.status==0}}"><block><block wx:if="{{$root.m28}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m29+' 0%,rgba('+$root.m30+',0.8) 100%)')+';'}}" data-url="{{'/pages/shop/prolist?cpid='+record.couponid+(coupon.bid?'&bid='+coupon.bid:'')}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去使用</view></block><block wx:if="{{coupon.fwtype==4}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m31+' 0%,rgba('+$root.m32+',0.8) 100%)')+';'}}" data-url="{{'/activity/yuyue/prolist?cpid='+record.couponid+(coupon.bid?'&bid='+coupon.bid:'')}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去使用</view></block></block></block><block wx:if="{{record.id&&coupon.type==3&&record.status==0&&record.yuyue_proid>0}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m33+' 0%,rgba('+$root.m34+',0.8) 100%)')+';'}}" data-url="{{'/activity/yuyue/product?id='+record.yuyue_proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去预约</view></block><block wx:if="{{record.id&&coupon.type==20&&record.status==0}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m35+' 0%,rgba('+$root.m36+',0.8) 100%)')+';'}}" data-id="{{coupon.id}}" data-event-opts="{{[['tap',[['getcouponpack',['$event']]]]]}}" catchtap="__e">使用</view></block></block></block><block wx:if="{{record.id&&record.status==0&&!record.from_mid&&(coupon.isgive==1||coupon.isgive==2)}}"><block><block wx:if="{{$root.m37=='app'}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m38+' 0%,rgba('+$root.m39+',0.8) 100%)')+';'}}" data-id="{{record.id}}" data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" bindtap="__e">转赠好友</view></block><block wx:else><block wx:if="{{$root.m40=='mp'}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m41+' 0%,rgba('+$root.m42+',0.8) 100%)')+';'}}" data-id="{{record.id}}" data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" bindtap="__e">转赠好友</view></block><block wx:else><block wx:if="{{$root.m43=='h5'}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m44+' 0%,rgba('+$root.m45+',0.8) 100%)')+';'}}" data-id="{{record.id}}" data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" bindtap="__e">转赠好友</view></block><block wx:else><button class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m46+' 0%,rgba('+$root.m47+',0.8) 100%)')+';'}}" open-type="share" data-id="{{record.id}}">转赠好友</button></block></block></block></block></block></block></block><block wx:else><block><block wx:if="{{(coupon.isgive==1||coupon.isgive==2)&&opt.pid==record.mid&&opt.pid>0}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m48+' 0%,rgba('+$root.m49+',0.8) 100%)')+';'}}" data-id="{{record.id}}" data-event-opts="{{[['tap',[['receiveCoupon',['$event']]]]]}}" bindtap="__e">立即领取</view></block></block></block><view class="text-center" style="margin-top:40rpx;line-height:60rpx;" data-url="/pages/index/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>返回首页</text></view></block></block><block wx:if="{{loading}}"><loading vue-id="35f4770a-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="35f4770a-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="35f4770a-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>