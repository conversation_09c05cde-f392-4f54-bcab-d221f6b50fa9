<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="b6d5bee2-1" itemdata="{{['未使用','已使用','已过期']}}" itemst="{{['0','1','2']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view class="coupon-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="coupon" style="{{(!item.$orig.from_mid&&(item.$orig.isgive==1||item.$orig.isgive==2)?'padding-left:40rpx':'')}}" data-url="{{'coupondetail?rid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']],['gtdetail',['$0'],[[['datalist','',index,'type']]]]]]]}}" catchtap="__e"><view class="radiobox" data-index="{{index}}" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><block wx:if="{{!item.$orig.from_mid&&(item.$orig.isgive==1||item.$orig.isgive==2)}}"><view class="radio" style="{{(item.$orig.checked?'background:'+item.m0+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></block></view><view class="pt_left"><view class="pt_left-content"><block wx:if="{{item.$orig.type==1}}"><view class="f1" style="{{'color:'+(item.m1)+';'}}"><text class="t0">￥</text><text class="t1">{{item.$orig.money}}</text></view></block><block wx:if="{{item.$orig.type==10}}"><view class="f1" style="{{'color:'+(item.m2)+';'}}"><text class="t1">{{item.$orig.discount/10}}</text><text class="t0">折</text></view></block><block wx:if="{{item.$orig.type==3}}"><view class="f1" style="{{'color:'+(item.m3)+';'}}"><text class="t1">{{item.$orig.limit_count}}</text><text class="t2">次</text></view></block><block wx:if="{{item.$orig.type==5}}"><view class="f1" style="{{'color:'+(item.m4)+';'}}"><text class="t0">￥</text><text class="t1">{{item.$orig.money}}</text></view></block><block wx:if="{{item.$orig.type==6}}"><view class="f1" style="{{'color:'+(item.m5)+';'}}"><text class="t0">￥</text><text class="t1">{{item.$orig.money}}</text></view></block><block wx:if="{{item.$orig.type!=1&&item.$orig.type!=10&&item.$orig.type!=3&&item.$orig.type!=5&&item.$orig.type!=6}}"><block><view class="f1" style="{{'color:'+(item.m6)+';'}}">{{item.$orig.type_txt}}</view></block></block><block wx:if="{{item.$orig.type==1||item.$orig.type==4||item.$orig.type==5||item.$orig.type==10||item.$orig.type==6}}"><view class="f2" style="{{'color:'+(item.m7)+';'}}"><block wx:if="{{item.$orig.minprice>0}}"><text>{{"满"+item.$orig.minprice+"元可用"}}</text></block><block wx:else><text>无门槛</text></block></view></block></view></view><view class="pt_right"><view class="f1"><view class="t1">{{item.$orig.couponname}}</view><text class="t2" style="{{'background:'+('rgba('+item.m8+',0.1)')+';'+('color:'+(item.m9)+';')}}">{{item.$orig.type_txt}}</text><block wx:if="{{!item.$orig.from_mid&&(item.$orig.isgive==1||item.$orig.isgive==2)}}"><text class="t2" style="{{'background:'+('rgba('+item.m10+',0.1)')+';'+('color:'+(item.m11)+';')}}">可赠送</text></block><view class="t3" style="{{(item.$orig.bid>0?'margin-top:0':'margin-top:10rpx')}}">{{"有效期至 "+item.$orig.endtime}}</view><block wx:if="{{item.$orig.bid>0}}"><view class="t4">{{"适用商家："+item.$orig.bname}}</view></block></view><block wx:if="{{item.$orig.isgive!=2&&st==0}}"><block><block wx:if="{{item.$orig.type==1||item.$orig.type==10}}"><block><block wx:if="{{item.m12}}"><button class="btn" style="{{'background:'+('linear-gradient(270deg,'+item.m13+' 0%,rgba('+item.m14+',0.8) 100%)')+';'}}" data-url="{{'/pages/shop/prolist?cpid='+item.$orig.couponid+(item.$orig.bid?'&bid='+item.$orig.bid:'')}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去使用</button></block><block wx:if="{{item.$orig.fwtype==4}}"><button class="btn" style="{{'background:'+('linear-gradient(270deg,'+item.m15+' 0%,rgba('+item.m16+',0.8) 100%)')+';'}}" data-url="{{'/activity/yuyue/prolist?cpid='+item.$orig.couponid+(item.$orig.bid?'&bid='+item.$orig.bid:'')}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去使用</button></block></block></block><block wx:else><block wx:if="{{item.$orig.type==6}}"><block><block wx:if="{{item.m17}}"><button class="btn" style="{{'background:'+('linear-gradient(270deg,'+item.m18+' 0%,rgba('+item.m19+',0.8) 100%)')+';'}}" data-url="/hotel/index/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去使用</button></block></block></block><block wx:else><block><button class="btn" style="{{'background:'+('linear-gradient(270deg,'+item.m20+' 0%,rgba('+item.m21+',0.8) 100%)')+';'}}" data-url="{{'coupondetail?rid='+item.$orig.id}}" data-event-opts="{{[['tap',[['gtdetail',['$0'],[[['datalist','',index,'type']]]],['goto',['$event']]]]]}}" catchtap="__e">去使用</button></block></block></block></block></block><block wx:if="{{st==1}}"><image class="sygq" src="{{pre_url+'/static/img/ysy.png'}}"></image></block><block wx:if="{{st==2}}"><image class="sygq" src="{{pre_url+'/static/img/ygq.png'}}"></image></block></view></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="b6d5bee2-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="b6d5bee2-3" bind:__l="__l"></nodata></block><block wx:if="{{checkednum>0}}"><view class="{{['giveopbox',menuindex>-1?'tabbarbot':'notabbarbot3']}}"><block wx:if="{{rdata.is_direct_give}}"><block><button data-event-opts="{{[['tap',[['showDirectGiveShow',['$event']]]]]}}" class="btn-give" style="{{'background:'+('linear-gradient(90deg,'+$root.m22+' 0%,rgba('+$root.m23+',0.8) 100%)')+';'}}" bindtap="__e">{{"转赠好友("+checkednum+"张)"}}</button></block></block><block wx:else><block><block wx:if="{{$root.m24=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="btn-give" style="{{'background:'+('linear-gradient(90deg,'+$root.m25+' 0%,rgba('+$root.m26+',0.8) 100%)')+';'}}" bindtap="__e">{{"转赠好友("+checkednum+"张)"}}</view></block><block wx:else><block wx:if="{{$root.m27=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="btn-give" style="{{'background:'+('linear-gradient(90deg,'+$root.m28+' 0%,rgba('+$root.m29+',0.8) 100%)')+';'}}" bindtap="__e">{{"转赠好友("+checkednum+"张)"}}</view></block><block wx:else><block wx:if="{{$root.m30=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="btn-give" style="{{'background:'+('linear-gradient(90deg,'+$root.m31+' 0%,rgba('+$root.m32+',0.8) 100%)')+';'}}" bindtap="__e">{{"转赠好友("+checkednum+"张)"}}</view></block><block wx:else><button class="btn-give" style="{{'background:'+('linear-gradient(90deg,'+$root.m33+' 0%,rgba('+$root.m34+',0.8) 100%)')+';'}}" open-type="share">{{"转赠好友("+checkednum+"张)"}}</button></block></block></block></block></block></view></block><view style="display:none;">{{test}}</view><block wx:if="{{directGiveShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['showDirectGiveShow',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">- 转赠给好友 -</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['showDirectGiveShow',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content invoiceBox"><form report-submit="true" data-event-opts="{{[['submit',[['directGiveSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="orderinfo"><view class="item"><text class="t1">转赠ID</text><input class="t2" type="text" placeholder="请输入转增ID" placeholder-style="font-size:28rpx;color:#BBBBBB" name="tomid" value="{{tomid}}"/></view></view><button class="btn" style="{{'background:'+($root.m35)+';'}}" form-type="submit">确定</button><view style="padding-top:30rpx;"></view></form></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="b6d5bee2-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="b6d5bee2-5" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="b6d5bee2-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>