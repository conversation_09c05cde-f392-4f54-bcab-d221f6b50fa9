<view><block wx:if="{{isload}}"><block><map class="map" longitude="{{info.longitude}}" latitude="{{info.latitude}}" scale="14" markers="{{markers}}"></map><view style="position:absolute;right:10px;top:10px;background-color:rgba(0, 0, 0, 0.6);color:#fff;padding:4px 6px;border-radius:5px;" data-name="{{info.shopname}}" data-address="{{info.address}}" data-longitude="{{info.longitude}}" data-latitude="{{info.latitude}}" data-event-opts="{{[['tap',[['showMap',['$event']]]]]}}" bindtap="__e">导航到店</view><view class="agent-card"><view class="flex-y-center row1"><image class="logo" src="{{info.logo}}"></image><view class="text"><view class="title limitText flex">{{info.shopname}}</view><view class="limitText grey-text">{{info.address}}</view><view class="grey-text flex-y-center"><image class="img" src="{{pre_url+'/static/img/my.png'}}"></image><view>{{info.name}}</view><image class="img" style="margin-left:30rpx;" src="{{pre_url+'/static/img/tel.png'}}"></image><view style="position:relative;" data-url="{{'tel::'+info.tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{info.tel}}<view class="btn" data-url="{{'tel::'+info.tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">拨打</view></view></view></view><view class="right"><image style="width:180rpx;height:48.5rpx;" src="{{pre_url+'/static/img/shop_vip.png'}}" mode="aspectFit"></image></view></view></view><view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">店铺介绍</view><view class="t2"></view><view class="t1"></view></view><view class="detail"><dp vue-id="e90e4510-1" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></view></block></block><block wx:if="{{loading}}"><loading vue-id="e90e4510-2" bind:__l="__l"></loading></block></view>