<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form"><view class="form-item"><text class="label">店铺名称</text><input class="input" type="text" placeholder="输入店铺名称" placeholder-style="font-size:28rpx;color:#BBBBBB" name="shopname" value="{{info.shopname}}"/></view><view class="form-item"><text class="label">坐标</text><input class="input" type="text" disabled="{{true}}" placeholder="请选择坐标" placeholder-style="font-size:28rpx;color:#BBBBBB" name="zuobiao" data-event-opts="{{[['tap',[['locationSelect',['$event']]]]]}}" value="{{latitude?latitude+','+longitude:''}}" bindtap="__e"/></view><input type="text" hidden="true" name="latitude" value="{{latitude}}"/><input type="text" hidden="true" name="longitude" value="{{longitude}}"/><view class="form-item"><text class="label">地址</text><input class="input" type="text" placeholder="输入地址" placeholder-style="font-size:28rpx;color:#BBBBBB" name="address" data-event-opts="{{[['input',[['inputAddress',['$event']]]]]}}" value="{{address}}" bindinput="__e"/></view><view class="form-item"><text class="label">联系人</text><input class="input" type="text" placeholder="输入联系人姓名" placeholder-style="font-size:28rpx;color:#BBBBBB" name="name" value="{{info.name}}"/></view><view class="form-item"><text class="label">联系电话</text><input class="input" type="text" placeholder="输入联系电话" placeholder-style="font-size:28rpx;color:#BBBBBB" name="tel" value="{{info.tel}}"/></view></view><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><text>店铺LOGO</text></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{pic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0==0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pic" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="logo" maxlength="-1" value="{{$root.g1}}"/></view><view class="form-box"><view class="form-item flex-col"><text>店铺介绍</text><view class="detailop"><view data-event-opts="{{[['tap',[['detailAddtxt',['$event']]]]]}}" class="btn" bindtap="__e">+文本</view><view data-event-opts="{{[['tap',[['detailAddpic',['$event']]]]]}}" class="btn" bindtap="__e">+图片</view></view><view><block wx:for="{{pagecontent}}" wx:for-item="setData" wx:for-index="index" wx:key="index"><block><view class="detaildp"><view class="op"><view class="flex1"></view><view class="btn" data-index="{{index}}" data-event-opts="{{[['tap',[['detailMoveup',['$event']]]]]}}" bindtap="__e">上移</view><view class="btn" data-index="{{index}}" data-event-opts="{{[['tap',[['detailMovedown',['$event']]]]]}}" bindtap="__e">下移</view><view class="btn" data-index="{{index}}" data-event-opts="{{[['tap',[['detailMovedel',['$event']]]]]}}" bindtap="__e">删除</view></view><block wx:if="{{setData}}"><view class="detailbox"><block wx:if="{{setData.temp=='text'}}"><block><dp-text vue-id="{{'67e09ee4-1-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-text></block></block><block wx:if="{{setData.temp=='picture'}}"><block><dp-picture vue-id="{{'67e09ee4-2-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-picture></block></block><block wx:if="{{setData.temp=='richtext'}}"><block><dp-richtext vue-id="{{'67e09ee4-3-'+index}}" params="{{setData.params}}" data="{{setData.data}}" content="{{setData.content}}" bind:__l="__l"></dp-richtext></block></block></view></block></view></block></block></view></view></view><input type="text" hidden="true" name="id" value="{{info.id}}"/><button class="set-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" form-type="submit">确 认</button></form><uni-popup class="vue-ref" vue-id="67e09ee4-4" id="dialogDetailtxt" type="dialog" data-ref="dialogDetailtxt" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">请输入文本内容</text></view><view class="uni-dialog-content"><textarea value="" placeholder="请输入文本内容" data-event-opts="{{[['input',[['catcheDetailtxt',['$event']]]]]}}" bindinput="__e"></textarea></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['dialogDetailtxtClose',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><view data-event-opts="{{[['tap',[['dialogDetailtxtConfirm',['$event']]]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">确定</text></view></view><view data-event-opts="{{[['tap',[['dialogDetailtxtClose',['$event']]]]]}}" class="uni-popup-dialog__close" bindtap="__e"><label class="uni-popup-dialog__close-icon _span"></label></view></view></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="67e09ee4-5" bind:__l="__l"></loading></block><dp-tabbar vue-id="67e09ee4-6" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="67e09ee4-7" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="67e09ee4-8" bind:__l="__l"></wxxieyi></view>