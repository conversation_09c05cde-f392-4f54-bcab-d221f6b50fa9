
.map{width:100%;height:500rpx;overflow:hidden}
.limitText{flex: 1;display: -webkit-box;
-webkit-box-orient: vertical;
-webkit-line-clamp: 1;
overflow: hidden; color: #666;}
.agent-card{height: auto; position: relative;background-color: #fff; margin: 30rpx 20rpx 10rpx; font-size: 24rpx; border-radius: 0 10rpx 10rpx 10rpx; overflow: hidden;box-shadow: 0 0 8rpx 0px rgb(0 0 0 / 30%);}
.agent-card .row1 {padding:20rpx 10rpx 20rpx 20rpx;}
.agent-card .logo{ width:120rpx;height:120rpx; border-radius: 50%;}
.agent-card .text { flex: 1; margin-left: 20rpx;color:#666; line-height: 180%;}
.agent-card .title { color: #333;font-weight: bold; font-size: 32rpx;}
.agent-card .right {height: 120rpx;}
.agent-card .btn {position: absolute; right: -100rpx;padding:0 14rpx; top:0; border: 1px solid #B6C26E; border-radius: 10rpx; color: #B6C26E;}
.agent-card .img { margin-right: 6rpx;width: 30rpx; height: 30rpx}
.agent-card .img2 {width: 32rpx; height: 32rpx}
.grey-text{color: #999;font-weight: normal;}
.detail{min-height:200rpx;}
.detail_title{width:100%;display:flex;align-items:center;justify-content:center;margin-top:60rpx;margin-bottom:30rpx}
.detail_title .t0{font-size:28rpx;font-weight:bold;color:#222222;margin:0 20rpx}
.detail_title .t1{width:12rpx;height:12rpx;background:rgba(253, 74, 70, 0.2);-webkit-transform:rotate(45deg);transform:rotate(45deg);margin:0 4rpx;margin-top:6rpx}
.detail_title .t2{width:18rpx;height:18rpx;background:rgba(253, 74, 70, 0.4);-webkit-transform:rotate(45deg);transform:rotate(45deg);margin:0 4rpx}


