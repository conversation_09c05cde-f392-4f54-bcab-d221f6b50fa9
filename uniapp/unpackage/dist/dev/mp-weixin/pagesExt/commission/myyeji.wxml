<view><block wx:if="{{isload}}"><block><view class="banner" style="{{'background:'+('linear-gradient(180deg,'+$root.m0+' 0%,rgba('+$root.m1+',0) 100%)')+';'}}"></view><view class="contentdata"><view class="form-box"><view class="form-item flex"><view class="f2" style="line-height:30px;"><picker mode="date" value="{{start_time1}}" data-event-opts="{{[['change',[['bindStartTime1Change',['$event']]]]]}}" bindchange="__e"><view class="picker">{{start_time1}}</view></picker><view style="padding:0 10rpx;color:#333;">到</view><picker mode="date" value="{{end_time1}}" data-event-opts="{{[['change',[['bindEndTime1Change',['$event']]]]]}}" bindchange="__e"><view class="picker">{{end_time1}}</view></picker></view><view data-event-opts="{{[['tap',[['search',['$event']]]]]}}" class="data_btn2" style="{{('background:'+$root.m2+';border:0')}}" bindtap="__e">查询</view></view></view><view class="data"><view class="data_module flex"><view class="flex1"><view class="data_lable">查询业绩(元)</view><view class="data_value">{{yeji}}</view></view></view></view></view><view style="width:100%;height:20rpx;"></view></block></block><block wx:if="{{loading}}"><loading vue-id="f28828e2-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="f28828e2-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="f28828e2-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>