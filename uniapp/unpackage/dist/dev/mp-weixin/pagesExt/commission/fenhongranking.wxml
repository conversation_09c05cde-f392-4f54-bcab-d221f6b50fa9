<block wx:if="{{isload}}"><block><view class="container" style="{{'background-color:'+($root.m0)+';'}}"><view class="contentbox"><image src="{{pre_url+'/static/imgsrc/rank_notext.png'}}" mode="widthFix"></image><view class="title"><view class="t1">{{sysset.fenhong_rank_title||'分红排行榜'}}</view><view class="t2">{{sysset.fenhong_rank_desc||'历史分红累积榜单'}}</view></view><view class="content"><view class="top"><view class="t1">{{"我的："+totalcommission}}</view></view><view class="tab"><view class="t1">排名</view><view class="t2">姓名</view><view class="t3" style="{{'color:'+($root.m1)+';'}}">累计</view></view><view class="itembox"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item"><block wx:if="{{index<3}}"><view class="t1"><image src="{{pre_url+'/static/img/comrank'+index+'.png'}}"></image></view></block><block wx:else><view class="t1">{{index+1}}</view></block><view class="t2"><image src="{{item.headimg}}"></image>{{item.nickname}}</view><text class="t3">{{''+item.sumcommission}}</text></view></block></block></view></view></view><block wx:if="{{nodata}}"><nodata vue-id="cc660b82-1" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="cc660b82-2" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="cc660b82-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="cc660b82-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="cc660b82-5" data-ref="popmsg" bind:__l="__l"></popmsg></view></block></block>