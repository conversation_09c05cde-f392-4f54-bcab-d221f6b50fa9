
.contentdata{display:flex;flex-direction:column;width:100%;padding:40rpx 30rpx 0;position:relative;margin-bottom:20rpx;background:#fff;}
.data{padding:30rpx;margin-top:30rpx;border-radius:16rpx}
.data_title{font-size: 28rpx;color: #333;font-weight: bold;}
.data_icon{height: 35rpx;width: 35rpx;margin-right: 15rpx;}
.data_text{font-size: 26;color: #999;margin-top: 60rpx;}
.data_price{font-size: 64rpx;color: #333;font-weight: bold;margin-top: 10rpx;}
.data_btn{height: 56rpx;padding: 0 30rpx;font-size: 24rpx;color: #fff;font-weight: normal;border-radius: 100rpx;}
.data_btn image{height: 24rpx;width: 24rpx;margin-left: 6rpx;}
.data_module{margin-top: 60rpx;}
.data_lable{font-size: 26;color: #999;}
.data_value{font-size: 44rpx;font-weight: bold;color: #333;margin-top: 10rpx;}
.form-box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}
.form-item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee
}
.form-item .f1{color:#222;width:200rpx;flex-shrink:0}
.form-item .f2{display:flex;align-items:center}
.form-box .form-item:last-child{ border:none}
.form-box .flex-col{padding-bottom:20rpx;display: flex;}
.picker { background-color: #f7f7f7; padding: 0 30rpx; border-radius: 10rpx;}
.data_btn2 { line-height: 56rpx;padding: 0 30rpx;font-size: 24rpx;color: #fff;font-weight: normal;border-radius: 100rpx;}

