<view class="container"><block wx:if="{{isload}}"><block><view class="topfix"><view class="toplabel"><text class="t1">{{"商家个数（"+count+"）"}}</text></view></view><view class="ind_business"><view class="ind_buslist" id="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view data-url="{{'/pagesExt/business/index?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="ind_busbox flex1 flex-row"><view class="ind_buspic flex0"><image src="{{item.logo}}"></image></view><view class="flex1"><view class="bus_title">{{item.name}}</view><view style="padding-top:10px;">{{"商户ID:"+item.id}}</view><view class="bus_sales">{{"销量："+item.sales}}</view></view></view></view></block></block><block wx:if="{{nomore}}"><nomore vue-id="380ace9c-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="380ace9c-2" bind:__l="__l"></nodata></block></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="380ace9c-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="380ace9c-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="380ace9c-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>