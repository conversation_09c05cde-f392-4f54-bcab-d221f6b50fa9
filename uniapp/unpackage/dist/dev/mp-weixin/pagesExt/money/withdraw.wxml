<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="mymoney" style="{{'background:'+($root.m0)+';'}}"><view class="f1">{{"我的"+$root.m1}}</view><view class="f2"><text style="font-size:26rpx;">￥</text>{{userinfo.money}}</view><view class="f3" data-url="moneylog?st=2" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>提现记录</text><text class="iconfont iconjiantou" style="font-size:20rpx;"></text></view></view><view class="content2"><view class="item2"><view class="f1">提现金额(元)</view></view><view class="item3"><view class="f1">￥</view><view class="f2"><input class="input" type="digit" name="money" value="" placeholder="请输入提现金额" placeholder-style="color:#999;font-size:40rpx" data-event-opts="{{[['input',[['moneyinput',['$event']]]]]}}" bindinput="__e"/></view></view><view class="item4"><block wx:if="{{sysset.withdrawmin>0}}"><view>{{"最低提现金额"+sysset.withdrawmin+'元'}}</view></block><block wx:if="{{sysset.withdrawfee>0}}"><view>{{"提现手续费"+sysset.withdrawfee+'%'}}</view></block><block wx:if="{{sysset.withdrawmul>0}}"><view>{{"提现金额需为"+sysset.withdrawmul+'倍数'}}</view></block></view></view><view class="withdrawtype"><view class="f1">选择提现方式：</view><view class="f2"><block wx:if="{{sysset.withdraw_weixin==1}}"><view class="item" data-paytype="微信钱包" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-weixin.png'}}"></image>微信钱包</view><view class="radio" style="{{(paytype=='微信钱包'?'background:'+$root.m2+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{sysset.withdraw_aliaccount==1}}"><view class="item" data-paytype="支付宝" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-alipay.png'}}"></image>支付宝</view><view class="radio" style="{{(paytype=='支付宝'?'background:'+$root.m3+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{sysset.withdraw_bankcard==1}}"><view class="item" data-paytype="银行卡" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-cash.png'}}"></image>银行卡</view><view class="radio" style="{{(paytype=='银行卡'?'background:'+$root.m4+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{sysset.withdraw_adapay==1}}"><view class="item" data-paytype="汇付天下银行卡" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-cash.png'}}"></image>汇付天下银行卡</view><view class="radio" style="{{(paytype=='汇付天下银行卡'?'background:'+$root.m5+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{sysset.withdraw_aliaccount_xiaoetong==1}}"><view class="item" data-paytype="小额通支付宝" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-cash.png'}}"></image>小额通支付宝</view><view class="radio" style="{{(paytype=='小额通支付宝'?'background:'+$root.m6+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{sysset.withdraw_bankcard_xiaoetong==1}}"><view class="item" data-paytype="小额通银行卡" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-cash.png'}}"></image>小额通银行卡</view><view class="radio" style="{{(paytype=='小额通银行卡'?'background:'+$root.m7+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{sysset.withdraw_aliaccount_linghuoxin==1}}"><view class="item" data-paytype="灵活薪支付宝" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-cash.png'}}"></image>灵活薪支付宝</view><view class="radio" style="{{(paytype=='灵活薪支付宝'?'background:'+$root.m8+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{sysset.withdraw_bankcard_linghuoxin==1}}"><view class="item" data-paytype="灵活薪银行卡" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-cash.png'}}"></image>灵活薪银行卡</view><view class="radio" style="{{(paytype=='灵活薪银行卡'?'background:'+$root.m9+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{sysset.withdraw_bankcard_allinpayYunst==1}}"><view class="item" data-paytype="云商通银行卡" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-cash.png'}}"></image>云商通银行卡</view><view class="radio" style="{{(paytype=='云商通银行卡'?'background:'+$root.m10+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{sysset.withdraw_paycode==1}}"><view class="item" data-paytype="收款码" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-cash.png'}}"></image>收款码</view><view class="radio" style="{{(paytype=='收款码'?'background:'+$root.m11+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{sysset.custom_status==1&&sysset.custom_name}}"><view class="item" data-paytype="{{sysset.custom_name}}" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-cash.png'}}"></image>{{sysset.custom_name}}</view><view class="radio" style="{{(paytype==sysset.custom_name?'background:'+$root.m12+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></view><block wx:if="{{selectbank&&paytype=='银行卡'&&bank}}"><view class="banklist"><view class="f1">{{"默认银行卡："+bank.bankname+" "+bank.bankcardnum}}</view><view class="t2" data-url="/pagesA/banklist/bank?fromPage=commission" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">修改</view></view></block></view><button data-event-opts="{{[['tap',[['formSubmit',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m13)+';'}}" bindtap="__e">立即提现</button><block wx:if="{{paytype=='支付宝'}}"><view style="width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center;" data-url="/pagesExt/my/setaliaccount" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">设置支付宝账户<image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:if="{{paytype=='银行卡'&&!sysset.withdraw_huifu}}"><view style="width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center;" data-url="/pagesExt/my/setbankinfo" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">设置银行卡账户<image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:if="{{paytype=='银行卡'&&sysset.withdraw_huifu==1}}"><view style="width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center;" data-url="/pagesExt/my/sethuifuinfo" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">设置银行卡账户<image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:if="{{paytype=='小额通支付宝'}}"><view style="width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center;" data-url="/pagesExt/my/setaliaccount" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">设置支付宝账户<image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:if="{{paytype=='小额通银行卡'}}"><view style="width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center;" data-url="/pagesExt/my/setbankinfo" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">设置银行卡账户<image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:if="{{paytype=='灵活薪支付宝'}}"><view style="width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center;" data-url="/pagesExt/my/setaliaccount" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">设置支付宝账户<image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:if="{{paytype=='灵活薪银行卡'}}"><view style="width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center;" data-url="/pagesExt/my/setbankinfo" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">设置银行卡账户<image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:if="{{paytype=='收款码'}}"><view style="width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center;" data-url="/pagesB/my/setpaycode" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">设置收款码<image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:if="{{paytype==sysset.custom_name}}"><view style="width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center;" data-url="/pagesB/my/setcustomaccount" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{"设置"+sysset.custom_name+"账户"}}<image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block></form><block wx:if="{{sysset.withdraw_desc}}"><view class="withdraw_desc"><view class="title">说明</view><textarea value="{{sysset.withdraw_desc}}"></textarea></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="18fbd739-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="18fbd739-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="18fbd739-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>