<view class="container"><block wx:if="{{isload}}"><block><form autocomplete="off" data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="content2"><block wx:if="{{$root.m0}}"><block><view class="item2"><view class="f1">对方手机号</view></view><view class="item3"><block wx:if="{{member_info2.id}}"><view class="member-info member-info-oneline"><view class="info-view flex-y-center"><block wx:if="{{member_info2.headimg}}"><image class="head-img" src="{{member_info2.headimg}}"></image></block><block wx:else><image class="head-img" src="{{pre_url+'/static/img/wxtx.png'}}"></image></block><view class="member-text-view"><view class="member-nickname">{{member_info2.nickname}}</view></view></view><view data-event-opts="{{[['tap',[['switchMember2',['$event']]]]]}}" class="query-button" style="{{'color:'+($root.m1)+';'}}" bindtap="__e">切换</view></view></block><block wx:else><view class="member-info"><input class="input" type="number" name="mobile" placeholder="请输入手机号" placeholder-style="color:#999;font-size:36rpx" data-event-opts="{{[['input',[['mobileinput',['$event']]]]]}}" value="{{mobile}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['changeQuery2',['$0'],['mobile']]]]]}}" class="query-button" style="{{'color:'+($root.m2)+';'}}" bindtap="__e">查询</view></view></block></view><view class="item4" style="height:1rpx;"></view></block></block><block wx:if="{{$root.m3}}"><block><view class="item2"><view class="f1">对方ID</view></view><view class="item3"><block wx:if="{{member_info.id}}"><view class="member-info"><view class="info-view flex-y-center"><block wx:if="{{member_info.headimg}}"><image class="head-img" src="{{member_info.headimg}}"></image></block><block wx:else><image class="head-img" src="{{pre_url+'/static/img/wxtx.png'}}"></image></block><view class="member-text-view"><view class="member-nickname">{{member_info.nickname}}</view><view class="member-id">{{"ID："+member_info.id}}</view></view></view><view data-event-opts="{{[['tap',[['switchMember',['$event']]]]]}}" class="query-button" style="{{'color:'+($root.m4)+';'}}" bindtap="__e">切换</view></view></block><block wx:else><view class="member-info"><input class="input" type="number" name="mid" placeholder="请输入对方ID" placeholder-style="color:#999;font-size:36rpx" data-event-opts="{{[['input',[['memberInput',['$event']]]]]}}" value="{{mid}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['changeQuery',['$0'],['mid']]]]]}}" class="query-button" style="{{'color:'+($root.m5)+';'}}" bindtap="__e">查询</view></view></block></view><view class="item4" style="height:1rpx;"></view></block></block><view class="item2"><view class="f1">转账金额</view></view><view class="item3"><view class="f2"><input class="input" type="number" name="money" value="" placeholder="请输入转账金额" placeholder-style="color:#999;font-size:36rpx" data-event-opts="{{[['input',[['moneyinput',['$event']]]]]}}" bindinput="__e"/></view></view><block wx:if="{{paycheck}}"><view class="item2"><view class="f1">支付密码</view></view></block><block wx:if="{{paycheck}}"><view class="item3"><view class="f2"><input class="input" type="password" name="paypwd" value="" placeholder="请输入支付密码" placeholder-style="color:#999;font-size:36rpx" data-event-opts="{{[['input',[['getpwd',['$event']]]]]}}" bindinput="__e"/></view></view></block><view class="item4"></view><view class="desText">{{"您的当前"+$root.m6+"："+mymoney+'，转账后不可退回'}}</view><block wx:if="{{money_transfer_min>0}}"><view class="desText">{{"最低转账金额："+money_transfer_min}}</view></block></view><button class="btn" style="{{'background:'+($root.m7)+';'}}" form-type="submit">转账</button><view class="text-center" style="margin-top:40rpx;line-height:60rpx;" data-url="/pages/my/usercenter" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>{{"返回"+$root.m8+"中心"}}</text></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="a41d1872-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="a41d1872-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>