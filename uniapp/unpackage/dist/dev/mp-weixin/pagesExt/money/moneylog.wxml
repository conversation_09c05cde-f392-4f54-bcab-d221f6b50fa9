<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="1f6eb3ff-1" itemdata="{{[$root.m0+'明细','充值记录','提现记录']}}" itemst="{{['0','1','2']}}" st="{{st}}" showstatus="{{showstatus}}" ismoney="{{1}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view class="content"><block wx:if="{{st==0}}"><block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.$orig.remark}}</text><text class="t2">{{item.$orig.createtime}}</text><text class="t3">{{"变更后"+item.m1+": "+item.$orig.after}}</text></view><view class="f2"><block wx:if="{{item.$orig.money>0}}"><text class="t1">{{"+"+item.$orig.money}}</text></block><block wx:else><text class="t2">{{item.$orig.money}}</text></block></view></view></block></block></block><block wx:if="{{st==1}}"><block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{"充值金额："+item.$orig.money+"元"}}</text><text class="t2">{{item.$orig.createtime}}</text></view><block wx:if="{{item.$orig.money_recharge_transfer&&item.$orig.paytypeid==5}}"><view class="f3"><block wx:if="{{item.$orig.transfer_check==1}}"><view><block wx:if="{{item.$orig.payorder.check_status==1&&item.$orig.status==0}}"><text class="t2" style="line-height:60rpx;font-size:13px;">充值失败</text></block><block wx:if="{{item.$orig.payorder.check_status==2&&item.$orig.status==0}}"><text class="t2" style="line-height:60rpx;font-size:13px;">凭证被驳回</text></block><block wx:if="{{!item.$orig.payorder.check_status&&!item.$orig.payorder.paypics&&item.$orig.status==0}}"><text class="t2" style="line-height:60rpx;font-size:13px;">凭证待上传</text></block><block wx:if="{{!item.$orig.payorder.check_status&&item.$orig.payorder.paypics&&item.$orig.status==0}}"><text class="t2" style="line-height:60rpx;font-size:13px;">凭证审核中</text></block><block wx:if="{{(item.$orig.payorder.check_status==1||item.$orig.payorder.check_status==2)&&item.$orig.status==1}}"><text class="t1" style="line-height:60rpx;font-size:13px;">充值成功</text></block><text class="btn1" style="{{'background:'+(item.m2)+';'}}" data-url="{{'/pages/pay/transfer?id='+item.$orig.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">付款凭证</text></view></block><block wx:else><view style="font-size:13px;"><block wx:if="{{item.$orig.transfer_check==0}}"><text>转账待审核</text></block><block wx:if="{{item.$orig.transfer_check==-1}}"><text>转账已驳回</text></block></view></block></view></block><block wx:else><view class="f3" style="font-size:13px;"><block wx:if="{{item.$orig.status==0}}"><text class="t2">充值失败</text></block><block wx:if="{{item.$orig.status==1}}"><text class="t1">充值成功</text></block></view></block></view></block></block></block><block wx:if="{{st==2}}"><block><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{"提现金额："+item.$orig.money+"元"}}</text><text class="t2">{{item.$orig.createtime}}</text><block wx:if="{{item.$orig.status==2}}"><text class="t2">{{"驳回原因："+(item.$orig.reason||'无')}}</text></block></view><view class="f3"><block wx:if="{{item.$orig.status==0}}"><text class="t1">审核中</text></block><block wx:if="{{item.$orig.status==1}}"><text class="t1">已审核</text></block><block wx:if="{{item.$orig.status==4}}"><block><block wx:if="{{item.$orig.wx_state=='WAIT_USER_CONFIRM'||item.$orig.wx_state=='TRANSFERING'}}"><view data-event-opts="{{[['tap',[['confirm_shoukuan',['$0'],[[['datalist','',index,'id']]]]]]]}}" class="btn1" style="{{'background:'+(item.m3)+';'}}" bindtap="__e">确认收款</view></block><block wx:else><block wx:if="{{item.$orig.wx_state=='FAIL'}}"><view class="t1">转账失败</view></block><block wx:else><view class="t1">处理中</view></block></block></block></block><block wx:if="{{item.$orig.status==2}}"><text class="t2">已驳回</text></block><block wx:if="{{item.$orig.status==3}}"><text class="t1">已打款</text></block></view></view></block></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="1f6eb3ff-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="1f6eb3ff-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="1f6eb3ff-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="1f6eb3ff-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="1f6eb3ff-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>