<view class="container"><block wx:if="{{isload}}"><block><view class="content" id="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item" data-url="{{'detail?tel='+item.tel+'&posterid='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><text class="t1">{{item.name}}</text><text class="t2">{{"姓名："+item.realname}}</text><text class="t2">{{"身份证号："+item.tel}}</text></view></view></block></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="e7ccbd02-1" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="e7ccbd02-2" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="e7ccbd02-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="e7ccbd02-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="e7ccbd02-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>