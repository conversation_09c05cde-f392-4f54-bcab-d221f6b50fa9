<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{set.header_text}}"><view><parse vue-id="7ee0ec4c-1" content="{{set.header_text}}" bind:__l="__l"></parse></view></block><form style="width:100%;margin:10rpx;" report-submit="true" data-event-opts="{{[['submit',[['subconfirm',['$event']]]]]}}" bindsubmit="__e"><view class="title">{{"请输入身份证号查询"+certificate_text}}</view><view class="inputdiv"><input id="dhcode" type="text" name="tel" placeholder-style="color:#666;" placeholder="请输入您的身份证号" value="{{tel}}"/></view><button class="btn" form-type="submit">查询</button></form><block wx:if="{{set.footer_text}}"><view><parse vue-id="7ee0ec4c-2" content="{{set.footer_text}}" bind:__l="__l"></parse></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="7ee0ec4c-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="7ee0ec4c-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="7ee0ec4c-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>