<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{!set||set.status==0}}"><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form"><view class="form-item"><input class="input" type="text" placeholder="请输入姓名" placeholder-style="color:#BBBBBB;font-size:28rpx" name="realname" value=""/></view></view><button class="set-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" form-type="submit">保 存</button></form></block><block wx:if="{{set&&set.status==1}}"><form data-event-opts="{{[['submit',[['formVerifySubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><block wx:if="{{info.realname_status==1}}"><block><view class="h2 font-big">您已通过实名认证</view><view class="form"><view class="form-item"><text class="label">姓名</text><text class="input">{{info.realname}}</text></view><view class="form-item"><text class="label">身份证号</text><text class="input">{{info.usercard}}</text></view></view></block></block><block wx:else><view><view class="h2 font-big">请上传身份证的正反面</view><view class="flex row"><view class="row-l"><view class="font-big">头像面</view><view class="font-desc">上传您的身份证头像面</view></view><view class="row-r"><block wx:for="{{idcard}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="idcard" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image class="img" src="{{item}}" data-url="{{item}}" mode="aspectFit" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0==0}}"><image class="img" src="{{pre_url+'/static/img/idcard.png'}}" mode="aspectFit" data-field="idcard" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></image></block><input type="text" hidden="true" name="idcard" maxlength="-1" value="{{$root.g1}}"/></view></view><view class="flex row"><view class="row-l"><view class="font-big">国徽面</view><view class="font-desc">上传您的身份证国徽面</view></view><view class="row-r"><block wx:for="{{idcard_back}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="idcard_back" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g2==0}}"><image class="img" src="{{pre_url+'/static/img/idcard_back.png'}}" mode="aspectFit" data-field="idcard_back" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></image></block><input type="text" hidden="true" name="idcard_back" maxlength="-1" value="{{$root.g3}}"/></view></view><button class="set-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m2+' 0%,rgba('+$root.m3+',0.8) 100%)')+';'}}" form-type="submit">保 存</button></view></block></form></block></block></block><block wx:if="{{loading}}"><loading vue-id="5c20e86a-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="5c20e86a-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5c20e86a-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>