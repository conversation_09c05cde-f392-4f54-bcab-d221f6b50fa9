
.form{ width:94%;margin:20rpx 3%;border-radius:5px;padding:20rpx 20rpx;padding: 0 3%;background: #FFF;}
.form-item{display:flex;align-items:center;width:100%;border-bottom: 1px #ededed solid;height:98rpx;line-height:98rpx;}
.form-item:last-child{border:0}
.form-item .label{color: #000;width:200rpx;}
.form-item .input{flex:1;color: #000;}
.form-item .picker{height: 60rpx;line-height:60rpx;margin-left: 0;flex:1;color: #000;}
.set-btn{width: 90%;margin:60rpx 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}
.placeholder {
		color:#BBBBBB;font-size:28rpx
}
.flex-sb {
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.form-title {
		font-size: 32rpx;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
		margin-bottom: 30rpx;
}
.form-value {
		flex: 1;
		/* padding: 0 10rpx; */
}
.form-value .picker {
		font-size: 24rpx;
		border-radius: 8rpx;
		height: 70rpx;
		line-height: 70rpx;
		border: 1rpx solid #f0f0f0;
		padding: 0 10rpx;
		flex: 1;
}
.picker-range{border: 1rpx solid #F0F0F0;height: 60rpx;line-height: 60rpx;border-radius: 6rpx;padding: 0 10rpx;}
.form-tips {
		font-size: 24rpx;
		flex-shrink: 0;
		color: #999;
}
.form-tips-block {
		font-size: 24rpx;
		flex-shrink: 0;
		color: #999;
		background: #f8f8f8;
		padding: 20rpx;
		margin-top: -30rpx;
		margin-bottom: 20rpx;
}
.down {
		width: 24rpx;
		height: 24rpx;
		margin-left: 10rpx;
}
.form-value .form-select
{
		align-items: center;
		justify-content: flex-start;
}

