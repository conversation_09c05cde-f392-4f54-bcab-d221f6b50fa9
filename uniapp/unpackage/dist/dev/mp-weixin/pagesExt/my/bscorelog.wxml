<view class="container"><block wx:if="{{isload}}"><block><view class="myscore" style="{{'background:'+($root.m0)+';'}}"><view class="f1">{{"我的"+$root.m1+"（"+bname+"）"}}</view><view class="f2">{{mybscore}}</view></view><view class="content" id="datalist"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item"><view class="f1"><text class="t1">{{item.$orig.remark}}</text><text class="t2">{{item.m2}}</text></view><view class="f2"><block wx:if="{{item.$orig.score>0}}"><block><text class="t1">{{"+"+item.$orig.score}}</text></block></block><block wx:else><block><text class="t2">{{item.$orig.score}}</text></block></block></view></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="f3d38cfe-1" bind:__l="__l"></nodata></block></view><block wx:if="{{nomore}}"><nomore vue-id="f3d38cfe-2" bind:__l="__l"></nomore></block></block></block><block wx:if="{{loading}}"><loading vue-id="f3d38cfe-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="f3d38cfe-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="f3d38cfe-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>