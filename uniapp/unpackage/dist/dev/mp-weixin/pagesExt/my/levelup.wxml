<view class="container" style="{{(bgset&&bgset.bgcolor?'background-color:'+bgset.bgcolor:'')}}"><block wx:if="{{errmsg==''&&isload}}"><block><image class="banner" src="{{bgset&&bgset.bgimg?bgset.bgimg:pre_url+'/static/img/lv-upbanner.png'}}" mode="widthFix"></image><view class="contentbox"><view class="title">{{"欢迎加入"+sysset.name}}</view><view class="title">{{"您的当前"+(bgset&&bgset.level_name?bgset.level_name:'等级')+"："}}<text style="font-weight:bold;">{{userlevel.name}}</text></view><block wx:if="{{mendian_member_levelup_fenhong&&sysset.mdname}}"><view class="title">扫码门店：<text style="font-weight:bold;">{{sysset.mdname}}</text></view></block></view><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><block wx:if="{{type=='ycode'}}"><view><view class="contentbox"><view class="form-item1"><view style="width:100%;overflow:hidden;margin:10rpx 0;"><input style="width:300rpx;float:left;line-height:60rpx;border:2rpx solid #eee;height:60rpx;padding-left:20rpx;" placeholder="输入验证码" data-event-opts="{{[['input',[['inputycode',['$event']]]]]}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['getlevel',['$event']]]]]}}" style="width:140rpx;float:right;line-height:60rpx;border:2rpx solid #eee;height:60rpx;background-color:red;text-align:center;color:#fff;" bindtap="__e">确认</view></view></view></view><block wx:if="{{nolevel}}"><view class="contentbox"><view class="noup"><text class="fa fa-check"></text><text>等级不存在或不符合此级别升级条件</text></view></view></block></view></block><block wx:else><view class="contentbox"><block wx:if="{{aglevelCount>0}}"><view class="form-item1"><view class="panel">{{"请选择升级"+(bgset&&bgset.level_name?bgset.level_name:'等级')+"："}}</view><radio-group name="levelid" data-event-opts="{{[['change',[['changelevel',['$event']]]]]}}" bindchange="__e"><block wx:for="{{aglevelList}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><block><label class="radio-item"><view class="flex1"><text style="font-weight:bold;">{{item.name}}</text></view><block wx:if="{{item.id!=userlevel.id}}"><radio value="{{item.id+''}}"></radio></block><block wx:if="{{item.id==userlevel.id}}"><text class="curlevel_tag">当前等级</text></block></label><block wx:if="{{item.apply_code}}"><view style="width:100%;overflow:hidden;margin:10rpx 0;"><input style="width:300rpx;float:left;line-height:60rpx;border:2rpx solid #eee;height:60rpx;padding-left:20rpx;" data-index="{{idx}}" data-id="{{item.id}}" placeholder="输入验证码" data-event-opts="{{[['input',[['applycode',['$event']]]]]}}" bindinput="__e"/></view></block></block></block></radio-group></view></block><block wx:else><view class="noup"><text class="fa fa-check"></text><block wx:if="{{levelid>0}}"><block><block wx:if="{{levelid==userinfo.levelid}}"><text>您已达到此级别</text></block><block wx:else><text>暂不符合此级别升级条件</text></block></block></block><block wx:else><block><text>您已达到最高可升级级别</text></block></block></view></block></view></block><block wx:if="{{!nolevel&&selectedLevel.can_apply==1&&selectedLevel.id!=userinfo.levelid}}"><view class="contentbox"><view class="applytj"><view class="f1"><text>{{selectedLevel.name}}</text><text class="t2">申请条件：</text></view><view class="f2"><block wx:if="{{selectedLevel.applytj!=''}}"><view class="t1">{{selectedLevel.applytj}}</view></block><block wx:if="{{selectedLevel.apply_paymoney>0}}"><view class="t2">{{selectedLevel.apply_paytxt+"：￥"+selectedLevel.apply_paymoney}}</view></block><block wx:if="{{selectedLevel.applytj_reach==0}}"><view class="t3">您暂未达到申请条件，请继续努力！</view></block><block wx:if="{{selectedLevel.applytj_reach==1}}"><view class="t4"><block wx:if="{{selectedLevel.applytj!=''}}"><text>您已达到申请条件，</text></block>请填写申请资料</view></block></view></view><block wx:if="{{selectedLevel.applytj_reach==1||need_school==1}}"><view class="applydata"><block wx:for="{{selectedLevel.apply_formdata}}" wx:for-item="item" wx:for-index="idx" wx:key="id"><view class="form-item"><view class="label">{{item.val1}}<block wx:if="{{item.val3==1}}"><text style="color:red;">*</text></block></view><block wx:if="{{item.key=='input'}}"><block><input class="input" type="text" name="{{'form'+idx}}" placeholder="{{item.val2}}" placeholder-style="font-size:28rpx" data-key="{{idx}}" data-event-opts="{{[['input',[['inputchange',['$event']]]]]}}" bindinput="__e"/><block wx:if="{{item.val4==2&&item.val6==1}}"><block><view style="display:flex;align-items:center;margin-top:10rpx;"><view style="width:180rpx;">验证码</view><input class="inputcode" type="text" name="smscode" value="" placeholder="请输入验证码" placeholder-style="padding:0 10rpx"/><text class="code1" data-key="{{idx}}" data-event-opts="{{[['tap',[['smscode',['$event']]]]]}}" bindtap="__e">获取验证码</text></view></block></block></block></block><block wx:if="{{item.key=='textarea'}}"><block><textarea class="textarea" name="{{'form'+idx}}" placeholder="{{item.val2}}" placeholder-style="font-size:28rpx"></textarea></block></block><block wx:if="{{item.key=='radio'}}"><block><radio-group class="flex" style="flex-wrap:wrap;" name="{{'form'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><radio class="radio" value="{{item1}}"></radio>{{item1+''}}</label></block></radio-group></block></block><block wx:if="{{item.key=='checkbox'}}"><block><checkbox-group class="flex" style="flex-wrap:wrap;" name="{{'form'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><checkbox class="checkbox" value="{{item1}}"></checkbox>{{item1+''}}</label></block></checkbox-group></block></block><block wx:if="{{item.key=='selector'}}"><block><picker class="picker" mode="selector" name="{{'form'+idx}}" value="" range="{{item.val2}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]||editorFormdata[idx]===0}}"><view>{{''+item.val2[editorFormdata[idx]]}}</view></block><block wx:else><view>请选择</view></block></picker></block></block><block wx:if="{{item.key=='time'}}"><block><picker class="picker" mode="time" name="{{'form'+idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view>{{editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker></block></block><block wx:if="{{item.key=='date'}}"><block><picker class="picker" mode="date" name="{{'form'+idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view>{{editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker></block></block><block wx:if="{{item.key=='region'}}"><block><uni-data-picker vue-id="{{'c4a43254-1-'+idx}}" localdata="{{items}}" popup-title="请选择省市区" styleData="width:100%" data-event-opts="{{[['^change',[['onchange']]]]}}" bind:change="__e" bind:__l="__l"></uni-data-picker><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{regiondata}}"/></block></block><block wx:if="{{item.key=='region2'}}"><block><view class="flex-y-center"><text data-event-opts="{{[['tap',[['toggleMaskLocation',['$event']]]]]}}" class="cell-tip" bindtap="__e"><text class="choose-text">{{addressByPcrs}}</text><text class="iconfont icon-xiangxia"></text></text></view><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{addressByPcrs}}"/></block></block><block wx:if="{{item.key=='upload'}}"><block><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{editorFormdata[idx]}}"><view class="form-imgbox"><view class="layui-imgbox-close" style="z-index:2;" data-idx="{{idx}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="form-imgbox-img"><image class="image" src="{{editorFormdata[idx]}}" data-url="{{editorFormdata[idx]}}" mode="aspectFit" data-idx="{{idx}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:else><view class="form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-idx="{{idx}}" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></block></view></block></block><block wx:if="{{item.key=='zuobiao'}}"><block><view class="gradeitem"><view data-event-opts="{{[['tap',[['selectzuobiao',['$event']]]]]}}" class="flex-y-center" bindtap="__e"><input type="text" name="{{'form'+idx}}" placeholder="请选择坐标" value="{{latitude?latitude+','+longitude:''}}"/></view></view></block></block></view></block><block wx:if="{{selectedLevel.areafenhong==1}}"><view class="form-item"><view class="label">代理区域<text style="color:red;">*</text></view><uni-data-picker vue-id="c4a43254-2" localdata="{{provincedata}}" popup-title="请选择代理区域" placeholder="{{areafenhong_province||'请选择代理区域'}}" styleData="width:100%" data-event-opts="{{[['^change',[['onchange1']]]]}}" bind:change="__e" bind:__l="__l"></uni-data-picker></view></block><block wx:if="{{selectedLevel.areafenhong==2}}"><view class="form-item"><view class="label">代理区域<text style="color:red;">*</text></view><uni-data-picker vue-id="c4a43254-3" localdata="{{citydata}}" popup-title="请选择代理区域" placeholder="{{areafenhong_city?areafenhong_province+'/'+areafenhong_city:'请选择代理区域'}}" styleData="width:100%" data-event-opts="{{[['^change',[['onchange2']]]]}}" bind:change="__e" bind:__l="__l"></uni-data-picker></view></block><block wx:if="{{selectedLevel.areafenhong==3}}"><view class="form-item"><view class="label">代理区域<text style="color:red;">*</text></view><uni-data-picker vue-id="c4a43254-4" localdata="{{items}}" popup-title="请选择代理区域" placeholder="{{areafenhong_area?areafenhong_province+'/'+areafenhong_city+'/'+areafenhong_area:'请选择代理区域'}}" styleData="width:100%" data-event-opts="{{[['^change',[['onchange3']]]]}}" bind:change="__e" bind:__l="__l"></uni-data-picker></view></block><block wx:if="{{selectedLevel.areafenhong==10}}"><view class="form-item"><view class="label">代理区域<text style="color:red;">*</text></view><picker class="picker" mode="selector" value="" range="{{largearea}}" data-event-opts="{{[['change',[['largeareaBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{largeareaindex!=-1}}"><view>{{largearea[largeareaindex]}}</view></block><block wx:else><view>请选择</view></block></picker></view></block><block wx:if="{{need_school}}"><view class="form-item"><view class="label">班级年级<text style="color:red;">*</text></view><view class="gradeitem"><picker class="picker" mode="selector" value="{{gradeindex}}" name="grade" range="{{gradelist}}" range-key="name" data-event-opts="{{[['change',[['gradeChange',['$event']]]]]}}" bindchange="__e"><view class="{{[gradeindex>-1?'':'hui']}}">{{''+(gradeindex>-1?gradelist[gradeindex].name:'请选择年级')}}</view></picker><block wx:if="{{gradeindex>-1}}"><picker class="picker" mode="selector" value="{{classindex}}" name="grade" range="{{classlist}}" range-key="name" data-event-opts="{{[['change',[['classChange',['$event']]]]]}}" bindchange="__e"><view class="{{[classindex>-1?'':'hui']}}"><text class="hui">/</text>{{''+(classindex>-1?classlist[classindex].name:'请选择班级')}}</view></picker></block></view></view></block></view></block><block wx:if="{{selectedLevel.is_agree==1}}"><view class="xycss1" style="text-align:center;"><checkbox-group data-event-opts="{{[['change',[['isagreeChange',['$event']]]]]}}" style="display:inline-block;" bindchange="__e"><checkbox style="transform:scale(0.6);" value="1" checked="{{isagree}}"></checkbox><text style="color:#000;">请先阅读并同意</text></checkbox-group><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" style="color:red;" bindtap="__e">《升级协议》</text></view></block><block wx:if="{{selectedLevel.applytj_reach==1}}"><button class="form-btn" form-type="submit">{{"申请成为"+selectedLevel.name}}</button></block></view></block></form><block wx:if="{{!nolevel&&selectedLevel.can_up==1&&selectedLevel.up_condition_show==1&&selectedLevel.id!=userinfo.levelid}}"><view class="contentbox"><view class="uplvtj"><view class="f1"><text>{{selectedLevel.name}}</text><text class="t2">升级条件：</text></view><block wx:if="{{changeState}}"><view class="f2"><parse vue-id="c4a43254-5" content="{{selectedLevel.autouptj}}" bind:__l="__l"></parse><view class="t3">{{"您达到升级条件后将自动升级为"+selectedLevel.name+"，请继续努力！"}}</view></view></block></view></view></block><view class="contentbox"><view class="explain"><view class="f1"><text>{{selectedLevel.name}}</text><text class="t2">{{(bgset&&bgset.level_name?bgset.level_name:'等级')+"特权："}}</text></view><view class="f2"><block wx:if="{{userlevel.id==selectedLevel.id}}"><parse vue-id="c4a43254-6" content="{{userlevel.explain}}" bind:__l="__l"></parse></block><block wx:for="{{aglevelList}}" wx:for-item="item" wx:for-index="index"><block><block wx:if="{{item.id==selectedLevel.id}}"><parse vue-id="{{'c4a43254-7-'+index}}" content="{{item.explain}}" bind:__l="__l"></parse></block></block></block></view></view></view></block></block><block wx:if="{{showxieyi}}"><view class="xieyibox"><view class="xieyibox-content"><view style="overflow:scroll;height:100%;"><parse vue-id="c4a43254-8" content="{{selectedLevel.agree_content}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view><view data-event-opts="{{[['tap',[['hidexieyi',['$event']]]]]}}" style="{{'position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center;width:50%;height:60rpx;line-height:60rpx;color:#fff;border-radius:8rpx;'+('background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';')}}" bindtap="__e">已阅读并同意</view></view></view></block><view style="display:none;">{{test}}</view><block wx:if="{{errmsg!=''&&isload}}"><block><view class="zan-box"><image class="zan-img" src="/static/img/zan.png"></image><view class="zan-text">{{errmsg}}</view></view></block></block><gk-city class="vue-ref" vue-id="c4a43254-9" headtitle="{{headtitle}}" provincedata="{{provincedata2}}" data="{{selfData}}" mode="cityPicker" pickerSize="{{4}}" data-ref="cityPicker" data-event-opts="{{[['^funcvalue',[['getpickerParentValue']]]]}}" bind:funcvalue="__e" bind:__l="__l"></gk-city><block wx:if="{{loading}}"><loading vue-id="c4a43254-10" bind:__l="__l"></loading></block><dp-tabbar vue-id="c4a43254-11" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>