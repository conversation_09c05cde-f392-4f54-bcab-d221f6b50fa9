<view class="container"><block wx:if="{{isload}}"><block><view class="content"><view class="info-item" style="height:136rpx;line-height:136rpx;"><view class="t1" style="flex:1;">头像</view><image style="width:88rpx;height:88rpx;" src="{{userinfo.headimg}}" data-event-opts="{{[['tap',[['uploadHeadimg',['$event']]]]]}}" bindtap="__e"></image><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view><view class="info-item" data-url="setnickname" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">昵称</view><view class="t2">{{userinfo.nickname}}</view><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view><block wx:if="{{userinfo.haslabel}}"><view class="info-item" style="height:auto;"><view class="t1">标签</view><view class="t2" style="display:block;line-height:44rpx;">{{''+userinfo.labelnames+''}}</view></view></block></view><view class="content"><view class="info-item" data-url="setrealname" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">姓名</view><view class="t2">{{userinfo.realname}}</view><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view><view class="info-item" data-url="settel" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">手机号</view><view class="t2">{{userinfo.tel}}</view><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view><view class="info-item" data-url="setsex" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t1">性别</text><block wx:if="{{userinfo.sex==1}}"><text class="t2">男</text></block><block wx:else><block wx:if="{{userinfo.sex==2}}"><text class="t2">女</text></block><block wx:else><text class="t2">未知</text></block></block><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view><view class="info-item" data-url="setbirthday" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t1">生日</text><text class="t2">{{userinfo.birthday}}</text><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view><block wx:if="{{userinfo.set_receive_notice}}"><view class="content"><block wx:if="{{$root.m0=='mp'}}"><view class="info-item"><view class="t1" style="width:290rpx;">消费通知</view><view class="t2"><switch value="1" checked="{{is_receive_finance_tmpl}}" data-type="tmpl" data-event-opts="{{[['change',[['switchchange',['$event']]]]]}}" bindchange="__e"></switch></view></view></block><view class="info-item"><view class="t1" style="width:290rpx;">消费通知（手机短信）</view><view class="t2"><switch value="1" checked="{{is_receive_finance_sms}}" data-type="sms" data-event-opts="{{[['change',[['switchchange',['$event']]]]]}}" bindchange="__e"></switch></view></view></view></block><block wx:if="{{userinfo.set_alipay||userinfo.set_bank}}"><view class="content"><block wx:if="{{userinfo.set_alipay}}"><view class="info-item" data-url="setaliaccount" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">支付宝账号</view><view class="t2">{{userinfo.aliaccount}}</view><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:if="{{userinfo.set_bank}}"><view class="info-item" data-url="setbankinfo" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t1">银行卡</text><text class="t2">{{userinfo.bankname?'已设置':''}}</text><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block></view></block><block wx:if="{{register_forms}}"><view class="content"><block wx:for="{{register_forms}}" wx:for-item="item" wx:for-index="index"><block><block wx:if="{{item.key!='upload_file'}}"><block><view class="info-item" data-url="{{'setother?from=regist&index='+index}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">{{item.val1}}</view><block wx:if="{{item.key!='upload'&&item.key!='upload_file'}}"><view class="t2">{{item.content}}</view></block><block wx:if="{{item.key=='upload'}}"><view class="t2" style="height:90rpx;"><block wx:if="{{item.content}}"><image style="height:70rpx;margin-top:10rpx;" src="{{item.content}}" mode="heightFix" data-url="{{item.content}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" catchtap="__e"></image></block></view></block><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block></block><block wx:else><block><view class="info-item" data-url="{{'setother?from=regist&index='+index}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">{{item.val1}}</view><view class="t2" style="overflow:hidden;white-space:pre-wrap;word-wrap:break-word;color:#4786BC;"><button style="float:right;width:120rpx;" type="button" data-file="{{item.content}}" data-event-opts="{{[['tap',[['download',['$event']]]]]}}" catchtap="__e">查看</button></view><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block></block></block></block></view></block><block wx:if="{{otherdata}}"><view class="content"><block wx:for="{{otherdata}}" wx:for-item="item" wx:for-index="index"><block><block wx:if="{{item.key!='upload_file'}}"><block><block wx:if="{{member_edit_switch==1}}"><view class="info-item" data-url="{{'setother?from=set&index='+index}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">{{item.val1}}</view><block wx:if="{{item.key!='upload'&&item.key!='upload_file'}}"><view class="t2">{{item.content}}</view></block><block wx:if="{{item.key=='upload'}}"><view class="t2" style="height:90rpx;"><block wx:if="{{item.content}}"><image style="height:70rpx;margin-top:10rpx;" src="{{item.content}}" mode="heightFix" data-url="{{item.content}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" catchtap="__e"></image></block></view></block><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:else><view class="info-item"><view class="t1">{{item.val1}}</view><block wx:if="{{item.key!='upload'&&item.key!='upload_file'}}"><view class="t2">{{item.content}}</view></block><block wx:if="{{item.key=='upload'}}"><view class="t2" style="height:90rpx;"><block wx:if="{{item.content}}"><image style="height:70rpx;margin-top:10rpx;" src="{{item.content}}" mode="heightFix" data-url="{{item.content}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" catchtap="__e"></image></block></view></block></view></block></block></block><block wx:else><block><block wx:if="{{member_edit_switch==1}}"><view class="info-item" data-url="{{'setother?from=set&index='+index}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">{{item.val1}}</view><view class="t2" style="overflow:hidden;white-space:pre-wrap;word-wrap:break-word;color:#4786BC;"><button style="float:right;width:120rpx;" type="button" data-file="{{item.content}}" data-event-opts="{{[['tap',[['download',['$event']]]]]}}" catchtap="__e">查看</button></view><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:else><view class="info-item"><view class="t1">{{item.val1}}</view><view class="t2" style="overflow:hidden;white-space:pre-wrap;word-wrap:break-word;color:#4786BC;"><button style="float:right;width:120rpx;" type="button" data-file="{{item.content}}" data-event-opts="{{[['tap',[['download',['$event']]]]]}}" catchtap="__e">查看</button></view></view></block></block></block></block></block></view></block><view class="content"><view class="info-item" data-url="/pagesB/address/address" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">收货地址</view><view class="t2"></view><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view><block wx:if="{{userinfo.haspaypwd==1}}"><view class="content"><view class="info-item" data-url="/pagesExt/my/paypwd" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">修改支付密码</view><view class="t2"></view><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></block><view class="content"><view class="info-item" data-url="/pagesExt/my/setpwd" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">修改密码</view><view class="t2"></view><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view><view class="content"><view class="info-item" data-url="/pages/index/login" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">切换账号</view><view class="t2"></view><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view><view class="content"><view data-event-opts="{{[['tap',[['logout',['$event']]]]]}}" class="info-item" bindtap="__e"><view class="t1">退出登录</view><view class="t2"></view><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></block></block><block wx:if="{{showTip}}"><view data-event-opts="{{[['tap',[['hideTip2',['$event']]]]]}}" class="popalert" catchtap="__e"><view class="moudle"><view class="title">短信通知开启提示</view><view class="minpricetip">请询问商家是否支持短信通知，商家未储值短信费用则不会有任何通知</view><view data-event-opts="{{[['tap',[['hideTip',['$event']]]]]}}" class="btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m1+' 0%,rgba('+$root.m2+',0.8) 100%)')+';'}}" bindtap="__e">我已知晓</view></view></view></block><block wx:if="{{loading}}"><loading vue-id="12f540f3-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="12f540f3-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="12f540f3-3" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="12f540f3-4" bind:__l="__l"></wxxieyi></view>