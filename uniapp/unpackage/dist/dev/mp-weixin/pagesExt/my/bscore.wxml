<view class="container"><block wx:if="{{isload}}"><block><view class="myscore" style="{{'background:'+($root.m0)+';'}}"><view class="flex1"><view class="f1">{{"商家"+$root.m1}}</view><view class="f2">{{mybscore}}</view></view><view class="flex1"><view class="f1">{{"平台"+$root.m2}}</view><view class="f2" data-url="scorelog" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{myscore}}</view></view></view><view class="content" id="datalist"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item" data-url="{{'bscorelog?bid='+item.$orig.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image style="width:60rpx;height:60rpx;border-radius:10rpx;margin-right:10rpx;" src="{{item.$orig.logo}}"></image><text class="t1">{{item.$orig.name}}</text></view><view class="f2"><view class="t1">{{item.$orig.score}}<text>{{item.m3}}</text></view><image style="width:40rpx;height:40rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="02eb5012-1" bind:__l="__l"></nodata></block></view><block wx:if="{{nomore}}"><nomore vue-id="02eb5012-2" bind:__l="__l"></nomore></block></block></block><block wx:if="{{loading}}"><loading vue-id="02eb5012-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="02eb5012-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="02eb5012-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>