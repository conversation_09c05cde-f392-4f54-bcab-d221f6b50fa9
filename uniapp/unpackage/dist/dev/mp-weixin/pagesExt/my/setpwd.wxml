<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form"><block wx:if="{{haspwd==1}}"><view class="form-item"><text class="label">原密码</text><input class="input" type="text" placeholder="输入您的原密码" placeholder-style="font-size:28rpx;color:#BBBBBB" name="oldpwd" value="" password="true"/></view></block><view class="form-item"><text class="label">新密码</text><input class="input" type="text" placeholder="输入您的新密码" placeholder-style="font-size:28rpx;color:#BBBBBB" name="pwd" value="" password="true"/></view><view class="form-item"><text class="label">确认新密码</text><input class="input" type="text" placeholder="再次输入新密码" placeholder-style="font-size:28rpx;color:#BBBBBB" name="repwd" value="" password="true"/></view></view><button class="set-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" form-type="submit">确 认</button></form></block></block><block wx:if="{{loading}}"><loading vue-id="66263aa8-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="66263aa8-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="66263aa8-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>