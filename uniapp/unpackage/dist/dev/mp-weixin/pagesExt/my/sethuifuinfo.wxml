<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form"><view class="form-item"><text class="label">姓名</text><input class="input" type="text" placeholder="请输入姓名" placeholder-class="placeholder" name="realname" disabled="{{inputDisabled}}" data-event-opts="{{[['input',[['__set_model',['$0','realname','$event',[]],['form']]]]]}}" value="{{form.realname}}" bindinput="__e"/></view><view class="form-item"><text class="label">身份证号</text><input class="input" type="text" placeholder="请输入身份证号" placeholder-class="placeholder" name="usercard" disabled="{{inputDisabled}}" data-event-opts="{{[['input',[['__set_model',['$0','usercard','$event',[]],['form']]]]]}}" value="{{form.usercard}}" bindinput="__e"/></view><view class="form-item" style="height:200rpx;"><text class="label">身份证有效期</text><view class="form-value flex-sb"><view class="flex1"><view class="{{['picker-range',form.usercard_begin_date?'':'placeholder']}}"><picker mode="date" name="usercard_begin_date" data-field="usercard_begin_date" disabled="{{inputDisabled}}" value="{{form.usercard_begin_date}}" data-event-opts="{{[['change',[['itemChange',['$event']]]],['input',[['__set_model',['$0','usercard_begin_date','$event',[]],['form']]]]]}}" bindchange="__e" bindinput="__e">{{''+(form.usercard_begin_date?form.usercard_begin_date:'请选择开始日期')+''}}</picker></view><view class="{{['picker-range',form.usercard_end_date?'':'placeholder']}}" style="margin-top:6rpx;"><picker mode="date" name="usercard_end_date" data-field="usercard_end_date" disabled="{{inputDisabled}}" value="{{form.usercard_end_date}}" data-event-opts="{{[['change',[['itemChange',['$event']]]],['input',[['__set_model',['$0','usercard_end_date','$event',[]],['form']]]]]}}" bindchange="__e" bindinput="__e">{{''+(form.usercard_end_date?form.usercard_end_date:'请选择结束日期')+''}}</picker></view></view><checkbox-group name="usercard_date_type" data-field="usercard_date_type" value="{{form.usercard_date_type}}" data-event-opts="{{[['change',[['itemChange',['$event']]]],['input',[['__set_model',['$0','usercard_date_type','$event',[]],['form']]]]]}}" bindchange="__e" bindinput="__e"><label class="form-tips"><checkbox style="transform:scale(0.7);" value="1" checked="{{form.usercard_date_type?true:false}}" disabled="{{inputDisabled}}"></checkbox>长期</label></checkbox-group></view></view><view class="form-item"><text class="label">开户银行</text><picker class="picker" mode="selector" name="bankname" value="0" range="{{banklist}}" disabled="{{inputDisabled}}" data-event-opts="{{[['change',[['bindBanknameChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{bankname}}"><view>{{bankname}}</view></block><block wx:else><view>请选择开户行</view></block></picker></view><view class="form-item"><view class="label">银行所属地区</view><view class="form-value"><uni-data-picker vue-id="5586a130-1" localdata="{{citylist}}" popup-title="地区" placeholder="地区" readonly="{{inputDisabled}}" value="{{city}}" data-event-opts="{{[['^change',[['cityChange']]],['^input',[['__set_model',['','city','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="form-select flex"><view class="select-txt">{{$root.g0>0?$root.g1:'请选择地区'}}</view><image class="down" src="{{pre_url+'/static/img/arrowdown.png'}}"></image></view></uni-data-picker></view></view><view class="form-item"><text class="label">所属分支行</text><input class="input" type="text" placeholder="请输入分支行" name="bankaddress" placeholder-class="placeholder" disabled="{{inputDisabled}}" data-event-opts="{{[['input',[['__set_model',['$0','bankaddress','$event',[]],['form']]]]]}}" value="{{form.bankaddress}}" bindinput="__e"/></view><view class="form-item"><text class="label">银行卡号</text><input class="input" type="text" placeholder="请输入银行卡号" name="bankcardnum" placeholder-class="placeholder" disabled="{{inputDisabled}}" data-event-opts="{{[['input',[['__set_model',['$0','bankcardnum','$event',[]],['form']]]]]}}" value="{{form.bankcardnum}}" bindinput="__e"/></view><view class="form-item"><text class="label">手机号</text><input class="input" type="text" placeholder="请输入手机号" name="tel" placeholder-class="placeholder" disabled="{{inputDisabled}}" data-event-opts="{{[['input',[['__set_model',['$0','tel','$event',[]],['form']]]]]}}" value="{{form.tel}}" bindinput="__e"/></view></view><block wx:if="{{!userinfo.huifu_id}}"><button class="set-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" form-type="submit">提 交</button></block></form><view style="display:none;">{{''+txt+''}}</view></block></block><block wx:if="{{loading}}"><loading vue-id="5586a130-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="5586a130-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5586a130-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>