<view class="container"><block wx:if="{{isload}}"><block><view class="topbg" style="{{('background:url('+pre_url+'/static/img/lv-topbg.png) no-repeat;background-size:100%')}}"><view class="topinfo" style="{{('background:url('+pre_url+'/static/img/lv-top.png);background-size:100% 320rpx;')}}"><view class="topuserinfo"><image class="headimg" src="{{userinfo.headimg}}" background-size="cover"></image><view class="info"><view class="nickname">{{userinfo.nickname}}</view><view class="flex"><block wx:if="{{userlevel}}"><view class="user-level"><block wx:if="{{userlevel.icon}}"><image class="level-img" src="{{userlevel.icon}}"></image></block><view class="level-name">{{userlevel.name}}</view></view></block></view><block wx:if="{{showleveldown}}"><view class="downlevel" style="margin-top:10rpx;color:#fff;font-size:24rpx;"><block wx:if="{{userinfo.isauto_down==1}}"><view>{{"已降级，需购买"+userinfo.buyproname+"才可恢复等级"}}</view></block><block wx:else><view>{{"还差"+userinfo.leveldowncommission+"佣金，降为"+userinfo.leveldownname}}</view></block></view></block><block wx:if="{{userinfo.levelendtime>0}}"><view class="endtime">{{"到期时间："+$root.m0}}</view></block><block wx:if="{{userlevel&&userlevel.areafenhong==1&&userinfo.province}}"><view class="endtime">{{"代理区域："+userinfo.province}}</view></block><block wx:if="{{userlevel&&userlevel.areafenhong==2&&userinfo.province&&userinfo.city}}"><view class="endtime">{{"代理区域："+userinfo.province+","+userinfo.city}}</view></block><block wx:if="{{userlevel&&userlevel.areafenhong==3&&userinfo.province&&userinfo.city&&userinfo.area}}"><view class="endtime">{{"代理区域："+userinfo.province+","+userinfo.city+","+userinfo.area}}</view></block><block wx:if="{{userlevel&&userlevel.areafenhong==10&&userinfo.largearea}}"><view class="endtime">{{"代理区域："+userinfo.largearea}}</view></block></view><view class="set" data-url="set" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="fa fa-cog"></text></view></view><block wx:if="{{hasnext&&showprogress}}"><view class="progressinfo"><view class="t1"><view class="n1">{{nextlevel.now_team_yeji}}</view>{{"距"+nextlevel.name+"还需"+up_yeji+''}}</view><view class="t2"><view class="tname">{{$root.m1}}</view><progress class="pinfo" percent="{{progress}}" border-radius="3" activeColor="#fff" backgroundColor="#FFD1C9" active="true"></progress></view></view></block></view><view class="upbtn" style="{{('background:url('+pre_url+'/static/img/lv-upbtn.png) no-repeat;background-size:100%')}}" data-url="{{opt.id?'levelup?id='+opt.id:'levelup'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">我要升级</view></view><view style="width:100%;height:20rpx;background-color:#f6f6f6;"></view><view class="explain"><view class="f1">— 等级特权 —</view><block wx:if="{{userlevel}}"><view class="f2"><parse vue-id="b0ffb03a-1" content="{{userlevel.explain}}" bind:__l="__l"></parse></view></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="b0ffb03a-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="b0ffb03a-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="b0ffb03a-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>