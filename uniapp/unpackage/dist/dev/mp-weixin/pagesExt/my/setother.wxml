<block wx:if="{{detail}}"><view class="dp-form"><view style="width:700rpx;margin:0 auto;padding-bottom:40rpx;"><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="item" style="margin-top:20rpx;"><view class="dp-form-item"><block wx:if="{{detail.key=='separate'}}"><block><view class="dp-form-separate">{{detail.val1}}</view></block></block><block wx:if="{{detail.key!='separate'}}"><view class="label">{{detail.val1}}<block wx:if="{{detail.val3==1}}"><text style="color:red;">*</text></block></view></block><block wx:if="{{detail.key=='input'}}"><block><block wx:if="{{detail.val5}}"><text style="margin-right:10rpx;">{{detail.val5}}</text></block><block wx:if="{{detail.val4==2&&detail.val6==1&&(platform=='mp'||platform=='wx')}}"><block><input class="input disabled" style="background-color:#efefef;" type="{{detail.val4==1||detail.val4==2?'digit':'text'}}" disabled="true" name="{{'form'+index}}" placeholder="{{detail.val2}}" placeholder-style="font-size:28rpx" data-formindex="{{'form'+index}}" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{detail.content}}" bindinput="__e"/><button class="authtel" open-type="getPhoneNumber" type="primary" data-index="{{index}}" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">获取手机号码</button></block></block><block wx:else><block><input class="input" type="{{detail.val4==1||detail.val4==2?'digit':'text'}}" readonly="{{true}}" name="{{'form'+index}}" placeholder="{{detail.val2}}" placeholder-style="font-size:28rpx" data-formindex="{{'form'+index}}" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{detail.content}}" bindinput="__e"/></block></block></block></block><block wx:if="{{detail.key=='usercard'}}"><block><block wx:if="{{detail.val5}}"><text style="margin-right:10rpx;">{{detail.val5}}</text></block><input class="input" type="idcard" readonly="{{true}}" name="{{'form'+index}}" placeholder="{{detail.val2}}" placeholder-style="font-size:28rpx" data-formindex="{{'form'+index}}" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{detail.content}}" bindinput="__e"/></block></block><block wx:if="{{detail.key=='textarea'}}"><block><textarea class="textarea" name="{{'form'+index}}" placeholder="{{detail.val2}}" placeholder-style="font-size:28rpx" data-formindex="{{'form'+index}}" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{detail.content}}" bindinput="__e"></textarea></block></block><block wx:if="{{detail.key=='radio'}}"><block><radio-group class="flex" style="flex-wrap:wrap;" name="{{'form'+index}}" data-formindex="{{'form'+index}}" data-event-opts="{{[['change',[['setfield',['$event']]]]]}}" bindchange="__e"><block wx:for="{{detail.val2}}" wx:for-item="item1" wx:for-index="index1" wx:key="id"><label class="flex-y-center"><radio class="radio" value="{{item1}}" checked="{{detail.content&&detail.content==item1?true:false}}"></radio>{{item1+''}}</label></block></radio-group></block></block><block wx:if="{{detail.key=='checkbox'}}"><block><checkbox-group class="flex" style="flex-wrap:wrap;" name="{{'form'+index}}" data-formindex="{{'form'+index}}" data-event-opts="{{[['change',[['setfield',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l0}}" wx:for-item="item1" wx:for-index="index1" wx:key="id"><label class="flex-y-center"><checkbox class="checkbox" value="{{item1.$orig}}" checked="{{item1.m0?true:false}}"></checkbox>{{item1.$orig+''}}</label></block></checkbox-group></block></block><block wx:if="{{detail.key=='selector'}}"><block><picker class="picker" mode="selector" name="{{'form'+index}}" value="{{editorFormdata[index]}}" range="{{detail.val2}}" data-index="{{index}}" data-formindex="{{'form'+index}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[index]||editorFormdata[index]===0}}"><view>{{''+detail.val2[editorFormdata[index]]+''}}</view></block><block wx:else><view>请选择</view></block></picker></block></block><block wx:if="{{detail.key=='time'}}"><block><picker class="picker" mode="time" name="{{'form'+index}}" value="{{detail.content}}" start="{{detail.val2[0]}}" end="{{detail.val2[1]}}" range="{{detail.val2}}" data-index="{{index}}" data-formindex="{{'form'+index}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[index]}}"><view>{{editorFormdata[index]}}</view></block><block wx:else><view>请选择</view></block></picker></block></block><block wx:if="{{detail.key=='date'}}"><block><picker class="picker" mode="date" name="{{'form'+index}}" value="{{detail.content}}" start="{{detail.val2[0]}}" end="{{detail.val2[1]}}" range="{{detail.val2}}" data-index="{{index}}" data-formindex="{{'form'+index}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[index]}}"><view>{{editorFormdata[index]}}</view></block><block wx:else><view>请选择</view></block></picker></block></block><block wx:if="{{detail.key=='region'}}"><block><uni-data-picker vue-id="f6e5500e-1" localdata="{{items}}" popup-title="请选择省市区" placeholder="{{detail.content||'请选择省市区'}}" data-formindex="{{'form'+index}}" data-event-opts="{{[['^change',[['onchange']]]]}}" bind:change="__e" bind:__l="__l"></uni-data-picker><input style="display:none;" type="text" name="{{'form'+index}}" value="{{regiondata?regiondata:detail.content}}"/></block></block><block wx:if="{{detail.key=='upload'}}"><block><input style="display:none;" type="text" name="{{'form'+index}}" value="{{editorFormdata[index]}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{editorFormdata[index]}}"><view class="dp-form-imgbox"><view class="dp-form-imgbox-close" data-index="{{index}}" data-formindex="{{'form'+index}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image class="image" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="dp-form-imgbox-img"><image class="image" src="{{editorFormdata[index]}}" data-url="{{editorFormdata[index]}}" mode="widthFix" data-index="{{index}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:else><view class="dp-form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-index="{{index}}" data-formindex="{{'form'+index}}" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></block></view></block></block><block wx:if="{{detail.key=='upload_file'}}"><block><input style="display:none;" type="text" name="{{'form'+index}}" value="{{editorFormdata[index]}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{editorFormdata[index]}}"><view class="dp-form-imgbox"><view class="dp-form-imgbox-close" data-index="{{index}}" data-formindex="{{'form'+index}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image class="image" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view style="overflow:hidden;white-space:pre-wrap;word-wrap:break-word;color:#4786BC;width:450rpx;" data-file="{{editorFormdata[index]}}" data-event-opts="{{[['tap',[['download',['$event']]]]]}}" bindtap="__e">{{''+editorFormdata[index]+''}}</view></view></block><block wx:else><view class="dp-form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-index="{{index}}" data-formindex="{{'form'+index}}" data-event-opts="{{[['tap',[['chooseFile',['$event']]]]]}}" bindtap="__e"></view></block></view></block></block></view></view><button class="set-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m1+' 0%,rgba('+$root.m2+',0.8) 100%)')+';'}}" form-type="submit">保存</button></form></view><wxxieyi vue-id="f6e5500e-2" bind:__l="__l"></wxxieyi></view></block>