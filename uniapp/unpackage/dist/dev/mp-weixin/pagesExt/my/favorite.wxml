<view class="container"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><block wx:if="{{item.$orig.type=='zhaopin'}}"><view class="product-item2" data-url="{{'/zhaopin/zhaopin/detail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image src="{{item.$orig.product.thumb}}" mode="widthFix"></image></view><view class="product-info"><view class="p2">{{item.$orig.product.title}}</view><view class="p3"><text class="t1">{{item.$orig.product.cname}}</text></view><view class="p3"><text class="t1">{{item.$orig.product.salary}}</text></view></view></view></block><block wx:else><block wx:if="{{item.$orig.type=='qiuzhi'}}"><view class="product-item2" data-url="{{'/zhaopin/qiuzhi/detail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image src="{{item.$orig.product.thumb}}" mode="widthFix"></image></view><view class="product-info"><view class="p2">{{item.$orig.product.title}}</view><view class="p3"><text class="t1">{{item.$orig.product.name+"/"+item.$orig.product.age+"岁/"+(item.$orig.product.sex==1?'男':'女')+"/"+(item.$orig.product.has_job==1?'在职':'离职')}}</text></view><view class="p3"><text class="t1">{{item.$orig.product.cnames}}</text></view></view></view></block><block wx:else><block wx:if="{{item.$orig.type=='car_hailing'}}"><view class="module"><view class="module_data" data-url="{{'/carhailing/product?id='+item.$orig.product.id+'&dateIndex=0	'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="module_img" src="{{item.$orig.product.pic}}" alt></image><view class="module_content"><view class="module_title">{{item.$orig.product.name}}</view><block wx:if="{{item.$orig.product.cid==2}}"><view class="module_item"><view class="module_time">{{item.$orig.product.starttime+"~"+item.$orig.product.endtime}}</view></view></block><block wx:if="{{item.$orig.product.cid!=2}}"><view class="module_item"><block wx:if="{{item.$orig.product.cid==1}}"><text>租车</text></block><block wx:else><text>包车</text></block>{{"费用："+item.$orig.product.sell_price+"元 /天"}}</view></block><block wx:else><view class="module_item">{{"拼车费用："+item.$orig.product.sell_price+"元 /人"}}</view></block><block wx:if="{{area!==''}}"><view class="module_item">{{"所在城市："+area}}</view></block></view><block wx:if="{{item.$orig.product.cid==2}}"><view><block wx:if="{{item.$orig.product.isend}}"><view class="module_btn module_end">已结束</view></block><block wx:else><block wx:if="{{item.$orig.product.leftnum>0}}"><view class="module_btn">预约</view></block><block wx:else><view class="module_btn module_end">满员</view></block></block></view></block><block wx:else><view><view class="module_btn"><block wx:if="{{item.$orig.product.cid==1}}"><text>租车</text></block><block wx:if="{{item.$orig.product.cid==3}}"><text>包车</text></block></view></view></block></view><block wx:if="{{item.$orig.product.cid==2}}"><view class="module_num"><view class="module_lable"><view>当前</view><view>预约</view></view><view class="module_view"><block wx:for="{{item.$orig.product.yyorderlist}}" wx:for-item="item2" wx:for-index="index2"><block><image src="{{item2.headimg}}"></image></block></block></view><block wx:if="{{item.$orig.product.leftnum>0}}"><view class="module_tag">{{"剩余"+item.$orig.product.leftnum+"个名额"}}</view></block><block wx:else><view class="module_tag module_end">满员</view></block></view></block></view></block><block wx:else><view class="product-item2" data-url="{{(item.$orig.type=='shop'?'/pages/':item.$orig.type=='yueke'||item.$orig.type=='cycle'?'/pagesExt/':'/activity/')+item.$orig.type+'/product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image src="{{item.$orig.product.pic}}" mode="widthFix"></image></view><block wx:if="{{item.$orig.type=='scoreshop'}}"><view class="product-info"><view class="p1">{{item.$orig.product.name}}</view><view class="p2"><text class="t1" style="{{'color:'+(item.m0)+';'}}">{{item.$orig.product.score_price+item.m1}}</text><text class="t2">{{"市场价￥"+item.$orig.product.sell_price}}</text></view><view class="p3"><block wx:if="{{item.$orig.product.sales>0}}"><text class="t1">已兑换<text style="font-size:24rpx;color:#f40;padding:0 2rpx;">{{item.$orig.product.sales}}</text>件</text></block><block wx:else><block wx:if="{{item.$orig.product.sellpoint}}"><text class="t2">{{item.$orig.product.sellpoint}}</text></block></block></view></view></block><block wx:else><view class="product-info"><view class="p1">{{item.$orig.product.name}}</view><view class="p2"><text class="t1" style="{{'color:'+(item.m2)+';'}}"><text style="font-size:22rpx;">￥</text>{{item.$orig.type=='kecheng'?item.$orig.product.price:item.$orig.product.sell_price}}</text><block wx:if="{{item.$orig.product.market_price*1>item.$orig.product.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.product.market_price}}</text></block></view><view class="p3"><block wx:if="{{item.$orig.product.sales>0}}"><text class="t1">已售<text style="font-size:24rpx;color:#f40;padding:0 2rpx;">{{item.$orig.product.sales}}</text>件</text></block><block wx:else><block wx:if="{{item.$orig.product.sellpoint}}"><text class="t2">{{item.$orig.product.sellpoint}}</text></block></block></view></view></block></view></block></block></block><view class="foot"><text class="flex1">{{"收藏时间："+item.$orig.createtime}}</text><text class="btn" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['favoritedel',['$event']]]]]}}" bindtap="__e">取消收藏</text></view></view></block><block wx:if="{{nomore}}"><nomore vue-id="94e0c9ea-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="94e0c9ea-2" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="94e0c9ea-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="94e0c9ea-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="94e0c9ea-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>