<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form"><view class="form-item"><text class="label">银行卡号</text><input class="input" type="text" placeholder="请输入银行卡号" name="bankcardnum" placeholder-style="color:#BBBBBB;font-size:28rpx" value="{{bankcardnum}}"/></view><view class="form-item"><text class="label">手机号</text><input class="input" type="text" placeholder="请输入手机号" name="tel_no" placeholder-style="color:#BBBBBB;font-size:28rpx" value="{{tel_no}}"/></view><view class="form-item"><text class="label">姓名</text><input class="input" type="text" placeholder="请输入姓名" name="realname" placeholder-style="color:#BBBBBB;font-size:28rpx" value="{{realname}}"/></view><view class="form-item"><text class="label">身份证号</text><input class="input" type="text" placeholder="请输入身份证号" name="idcard" placeholder-style="color:#BBBBBB;font-size:28rpx" value="{{idcard}}"/></view></view><button class="set-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" form-type="submit">保 存</button></form></block></block><block wx:if="{{loading}}"><loading vue-id="4aa5bebb-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="4aa5bebb-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="4aa5bebb-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>