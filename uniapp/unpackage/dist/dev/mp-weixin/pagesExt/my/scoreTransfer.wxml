<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="content2"><view class="item2"><view class="f1">接收人ID</view></view><view class="item3"><block wx:if="{{member_info.id}}"><view class="member-info"><view class="info-view flex-y-center"><block wx:if="{{member_info.headimg}}"><image class="head-img" src="{{member_info.headimg}}"></image></block><block wx:else><image class="head-img" src="{{pre_url+'/static/img/wxtx.png'}}"></image></block><view class="member-text-view"><view class="member-nickname">{{member_info.nickname}}</view><view class="member-id">{{"ID："+member_info.id}}</view></view></view><view data-event-opts="{{[['tap',[['switchMember',['$event']]]]]}}" class="query-button" style="{{'color:'+($root.m0)+';'}}" bindtap="__e">切换</view></view></block><block wx:else><view class="member-info"><input class="input" type="number" name="mid" placeholder="请输入对方ID" placeholder-style="color:#999;font-size:36rpx" data-event-opts="{{[['input',[['memberInput',['$event']]]],['blur',[['changeQuery',['$0'],['mid']]]]]}}" value="{{mid}}" bindinput="__e" bindblur="__e"/></view></block></view><view class="item4" style="height:1rpx;"></view><view class="item2"><view class="f1">转赠数量</view></view><view class="item3"><view class="f2"><input class="input" type="digit" name="integral" value="" placeholder="请输入转赠数量" placeholder-style="color:#999;font-size:36rpx" data-event-opts="{{[['input',[['moneyinput',['$event']]]]]}}" bindinput="__e"/></view></view><block wx:if="{{paycheck}}"><view class="item2"><view class="f1">支付密码</view></view></block><block wx:if="{{paycheck}}"><view class="item3"><view class="f2"><input class="input" type="password" name="paypwd" value="" placeholder="请输入支付密码" placeholder-style="color:#999;font-size:36rpx" data-event-opts="{{[['input',[['getpwd',['$event']]]]]}}" bindinput="__e"/></view></view></block><view class="item5"><text class="{{[mid>0?'redtxt':'']}}">{{"您的当前"+$root.m1+"："+myscore+"，转赠后不可退回"}}</text><block wx:if="{{set.score_transfer_sxf_ratio>0}}"><text class="redtxt">{{"转赠手续费"+set.score_transfer_sxf_ratio+"%"}}</text></block></view><block wx:if="{{transfer_sxf==1&&score_sxf>0}}"><view class="item4"><text style="margin-right:10rpx;">{{"支付手续费："+score_sxf+'元'}}</text></view></block></view><button class="btn" style="{{'background:'+($root.m2)+';'}}" form-type="submit">转赠</button><view class="text-center" data-url="/pages/my/usercenter" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>{{"返回"+$root.m3+"中心"}}</text></view><block wx:if="{{set&&set.score_transfer_wxqrcode}}"><view data-event-opts="{{[['tap',[['showPoster',['wx']]]]]}}" class="text-center" bindtap="__e"><text>生成转赠小程序码</text></view></block><block wx:if="{{set&&set.score_transfer_qrcode}}"><view data-event-opts="{{[['tap',[['showPoster',['mp']]]]]}}" class="text-center" bindtap="__e"><text>生成转赠二维码</text></view></block></form><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/close.png'}}"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="2b504650-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="2b504650-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>