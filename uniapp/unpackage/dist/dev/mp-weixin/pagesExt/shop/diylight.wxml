<view><block wx:if="{{isload}}"><block><view class="navigation" style="{{('top:'+topBtn+'px')}}"><image class="navigation_item _img" src="{{pre_url+'/static/img/diylight_n1.png'}}" alt data-event-opts="{{[['tap',[['goback',['$event']]]]]}}" bindtap="__e"></image><image class="navigation_item _img" src="{{pre_url+'/static/img/diylight_n2.png'}}" alt data-url="/pages/shop/cart" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></image><image class="navigation_item _img" src="{{pre_url+'/static/img/diylight_n3.png'}}" alt data-event-opts="{{[['tap',[['uploadImg']]]]}}" bindtap="__e"></image><image class="navigation_item _img" src="{{pre_url+'/static/img/diylight_n4.png'}}" alt data-event-opts="{{[['tap',[['draw',['$event']]]]]}}" bindtap="__e"></image><block wx:if="{{$root.m0=='wx'}}"><button class="navigation_icon" open-type="share"><image class="navigation_item _img" src="{{pre_url+'/static/img/diylight_n5.png'}}" alt></image></button></block></view><block wx:for="{{dataList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['moveClick',[index]]]]]}}" class="{{['move_module','move_item',item.active?'move_active':'']}}" style="{{'top:'+(item.y-item.size/2+'px')+';'+('left:'+(item.x-item.size/2+'px')+';')+('height:'+(item.size+'px')+';')+('width:'+(item.size+'px')+';')+('transform:'+('rotate('+item.rotate+'deg)')+';')}}" bindtap="__e"><image class="move_image _img" src="{{item.url[item.lightIndex]}}" alt data-event-opts="{{[['touchstart',[['siteStart',['$event',index]]]],['touchmove',[['siteMove',['$event',index]]]],['touchend',[['siteEnd',['$event',index]]]]]}}" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e"></image><block wx:if="{{item.active}}"><view data-event-opts="{{[['touchstart',[['spinStart',['$event',index]]]],['touchmove',[['spinMove',['$event',index]]]],['touchend',[['spinEnd',['$event',index]]]]]}}" class="move_spin" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e"><image src="{{pre_url+'/static/img/diylight_spin.png'}}" alt class="_img"></image></view></block><block wx:if="{{item.active}}"><view data-event-opts="{{[['tap',[['moveDelete',[index]]]]]}}" class="move_close" bindtap="__e"><image src="{{pre_url+'/static/img/diylight_close.png'}}" alt class="_img"></image></view></block><block wx:if="{{item.active}}"><view data-event-opts="{{[['touchstart',[['sizeStart',['$event',index]]]],['touchmove',[['sizeMove',['$event',index]]]],['touchend',[['sizeEnd',['$event',index]]]]]}}" class="move_size" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e"><image src="{{pre_url+'/static/img/diylight_size.png'}}" alt class="_img"></image></view></block></view></block><view class="{{['table',menuindex>-1?'table_opt':'']}}"><view data-event-opts="{{[['tap',[['sceneClick',['$event']]]]]}}" class="table_item" bindtap="__e"><image class="table_icon _img" src="{{pre_url+'/static/img/diylight_image.png'}}" alt></image>更换场景</view><view data-event-opts="{{[['tap',[['lightClick',['$event']]]]]}}" class="table_item" bindtap="__e"><image class="table_icon _img" src="{{pre_url+'/static/img/diylight_lamp.png'}}" alt></image>选择产品</view><block wx:if="{{switchState}}"><view data-event-opts="{{[['tap',[['switchClick',['$event']]]]]}}" class="table_item" bindtap="__e"><image class="table_icon _img" src="{{pre_url+'/static/img/diylight_closeL.png'}}" alt></image>关灯</view></block><block wx:if="{{!switchState}}"><view data-event-opts="{{[['tap',[['switchClick',['$event']]]]]}}" class="table_item" bindtap="__e"><image class="table_icon _img" src="{{pre_url+'/static/img/diylight_openL.png'}}" alt></image>开灯</view></block></view><block wx:if="{{!switchState}}"><view class="page_cut"></view></block><block wx:if="{{sceneState}}"><view class="alert"><view data-event-opts="{{[['tap',[['sceneClick',['$event']]]]]}}" class="alert_hide" bindtap="__e"></view><view class="{{['alert_module',menuindex>-1?'alert_opt':'']}}"><view class="alert_head"><view data-event-opts="{{[['tap',[['uploadImg',['$event']]]]]}}" class="alert_btn" bindtap="__e">+上传场景</view><image class="alert_close _img" src="{{pre_url+'/static/img/diylight_closeA.png'}}" alt data-event-opts="{{[['tap',[['sceneClick',['$event']]]]]}}" bindtap="__e"></image></view><scroll-view class="alert_content" scroll-y="true" data-event-opts="{{[['scrolltolower',[['moreScene',['$event']]]]]}}" bindscrolltolower="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.g0}}"><view data-event-opts="{{[['tap',[['sceneChoose',['$0'],[[['sceneList','',index]]]]]]]}}" class="alert_item" bindtap="__e"><image src="{{item.$orig}}" mode="widthFix" alt class="_img"></image></view></block></block><block wx:if="{{!$root.g1}}"><nodata vue-id="0d07b7a2-1" text="没有查找到相关商品" type="small" bind:__l="__l"></nodata></block></scroll-view></view></view></block><block wx:if="{{lightState}}"><view class="alert"><view data-event-opts="{{[['tap',[['lightClick',['$event']]]]]}}" class="alert_hide" bindtap="__e"></view><view class="{{['alert_module',menuindex>-1?'alert_opt':'']}}"><view class="alert_title"><text>选择产品</text><image class="alert_close _img" src="{{pre_url+'/static/img/diylight_closeA.png'}}" alt data-event-opts="{{[['tap',[['lightClick',['$event']]]]]}}" bindtap="__e"></image></view><scroll-view class="alert_table" scroll-x="true"><block wx:for="{{tableList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tableClick',[index]]]]]}}" class="{{[tableIndex==index?'alert_active':'']}}" bindtap="__e">{{''+item.name+''}}</view></block></scroll-view><scroll-view class="alert_content" scroll-y="true" data-event-opts="{{[['scrolltolower',[['moreLight',['$event']]]]]}}" bindscrolltolower="__e"><block wx:for="{{lightList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['lightChoose',['$0'],[[['lightList','',index]]]]]]]}}" class="alert_item" bindtap="__e"><image src="{{item.url[0]}}" mode="widthFix" alt class="_img"></image></view></block><block wx:if="{{!$root.g2}}"><nodata vue-id="0d07b7a2-2" text="没有查找到相关商品" type="small" bind:__l="__l"></nodata></block></scroll-view></view></view></block><image class="scene _img" src="{{scene}}" mode="widthFix" alt data-event-opts="{{[['tap',[['areaClick',['$event']]]]]}}" bindtap="__e"></image><block wx:for="{{dataList}}" wx:for-item="item" wx:for-index="index"><block wx:if="{{item.active}}"><scroll-view class="{{['class',menuindex>-1?'class_opt':'']}}" scroll-x="true" wx:key="index"><block wx:for="{{item.url}}" wx:for-item="itemS" wx:for-index="indexS"><image class="{{['_img',item.lightIndex==indexS?'class_item class_active':'class_item']}}" wx:key="indexS" src="{{itemS}}" alt data-event-opts="{{[['tap',[['lightType',[index,indexS]]]]]}}" bindtap="__e"></image></block></scroll-view></block></block><block wx:for="{{dataList}}" wx:for-item="item" wx:for-index="index"><block wx:if="{{item.real}}"><view class="cart" style="{{('top:'+topCart+'px')}}" wx:key="index" data-url="{{'/pages/shop/product?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="cart_item _img" src="{{item.realUrl}}" alt></image><view class="cart_title">加购物车</view></view></block></block></block></block><block wx:if="{{loading}}"><loading vue-id="0d07b7a2-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="0d07b7a2-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="0d07b7a2-5" data-ref="popmsg" bind:__l="__l"></popmsg><block wx:if="{{canvasState}}"><canvas class="canvasDraw" canvas-id="myCanvas" id="myCanvas"></canvas></block><wxxieyi vue-id="0d07b7a2-6" bind:__l="__l"></wxxieyi></view>