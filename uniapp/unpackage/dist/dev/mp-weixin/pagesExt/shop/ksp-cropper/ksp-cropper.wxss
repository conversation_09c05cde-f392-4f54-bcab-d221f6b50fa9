
.panel.data-v-3807875c {
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	bottom: 0;
	z-index: 9999999;
	overflow: hidden;
}
.canvas.data-v-3807875c {
	position: absolute;
	top: 5000px;
	left: 5000px;
}
.toolbar.data-v-3807875c {
	position: absolute;
	width: 100%;
	height: 100rpx;
	left: 0rpx;
	bottom: 0rpx;
	display: flex;
	justify-content: space-around;
	align-items: center;
}
.btn-cancel.data-v-3807875c {
	font-size: 40rpx;
	color: #d5dfe5;
	font-weight: bold;
}
.btn-ok.data-v-3807875c {
	font-size: 40rpx;
	color: #FFFFFF;
	font-weight: bold;
}
.btn-rotate.data-v-3807875c {
	font-size: 40rpx;
	color: #d5dfe5;
	font-weight: bold;
}
.body.data-v-3807875c {
	position: absolute;
	left: 0rpx;
	right: 0rpx;
	top: 0rpx;
	bottom: 0rpx;
	background: black;
	overflow: hidden;
}
.mask.data-v-3807875c {
	position: absolute;
	left: 0rpx;
	right: 0rpx;
	top: 0rpx;
	bottom: 0rpx;
	background: black;
	opacity: 0.4;
}
.plank.data-v-3807875c {
	position: absolute;
	left: 0rpx;
	right: 0rpx;
	top: 0rpx;
	bottom: 0rpx;
}
.image-wrap.data-v-3807875c {
	position: absolute;
}
.image-rect.data-v-3807875c {
	position: absolute;
}
.image.data-v-3807875c {
	position: absolute;
}
.frame.data-v-3807875c {
	position: absolute;
	left: 100px;
	top: 100px;
	width: 200px;
	height: 200px;
}
.rect.data-v-3807875c {
	position: absolute;
	left: -2px;
	top: -2px;
	width: 100%;
	height: 100%;
	border: 2px solid white;
	overflow: hidden;
	box-sizing:content-box;
}
.line-one.data-v-3807875c {
	position: absolute;
	width: 100%;
	height: 1px;
	background: white;
	left: 0;
	top: 33.3%;
	box-sizing:content-box;
}
.line-two.data-v-3807875c {
	position: absolute;
	width: 100%;
	height: 1px;
	background: white;
	left: 0;
	top: 66.7%;
	box-sizing:content-box;
}
.line-three.data-v-3807875c {
	position: absolute;
	width: 1px;
	height: 100%;
	background: white;
	top: 0;
	left: 33.3%;
	box-sizing:content-box;
}
.line-four.data-v-3807875c {
	position: absolute;
	width: 1px;
	height: 100%;
	background: white;
	top: 0;
	left: 66.7%;
	box-sizing:content-box;
}
.frame-left-top.data-v-3807875c {
	position: absolute;
	width: 20px;
	height: 20px;
	left: -6px;
	top: -6px;
	border-left: 4px solid red;
	border-top: 4px solid red;
	box-sizing:content-box;
}
.frame-left-bottom.data-v-3807875c {
	position: absolute;
	width: 20px;
	height: 20px;
	left: -6px;
	bottom: -6px;
	border-left: 4px solid red;
	border-bottom: 4px solid red;
	box-sizing:content-box;
}
.frame-right-top.data-v-3807875c {
	position: absolute;
	width: 20px;
	height: 20px;
	right: -6px;
	top: -6px;
	border-right: 4px solid red;
	border-top: 4px solid red;
	box-sizing:content-box;
}
.frame-right-bottom.data-v-3807875c {
	position: absolute;
	width: 20px;
	height: 20px;
	right: -6px;
	bottom: -6px;
	border-right: 4px solid red;
	border-bottom: 4px solid red;
	box-sizing:content-box;
}
.transit.data-v-3807875c {
	transition: width 0.3s, height 0.3s, left 0.3s, top 0.3s, -webkit-transform 0.3s;
	transition: width 0.3s, height 0.3s, left 0.3s, top 0.3s, transform 0.3s;
	transition: width 0.3s, height 0.3s, left 0.3s, top 0.3s, transform 0.3s, -webkit-transform 0.3s;
}

