<view class="container"><block wx:if="{{isload}}"><block><view class="search-container" style="{{(history_show?'height:100%;':'')}}"><view hidden="{{!(history_show)}}" class="search-history"><block wx:if="{{info.banner}}"><view class="topimg" style="{{('background-image:url('+info.banner+');background-size:100% 100%;')}}"></view></block><view data-event-opts="{{[['tap',[['uploadImg',['$event']]]]]}}" class="centerimg" bindtap="__e"><block wx:if="{{info.image_search_pic}}"><image src="{{info.image_search_pic}}"></image></block><block wx:else><image src="{{pre_url+'/static/img/camera2.png'}}"></image></block><view class="title">以图搜索</view></view></view></view><ksp-cropper vue-id="fe3b9200-1" mode="free" width="{{200}}" height="{{140}}" maxWidth="{{1024}}" maxHeight="{{1024}}" url="{{imgurl}}" data-event-opts="{{[['^cancel',[['oncancel']]],['^ok',[['onok']]]]}}" bind:cancel="__e" bind:ok="__e" bind:__l="__l"></ksp-cropper></block></block><block wx:if="{{loading}}"><loading vue-id="fe3b9200-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="fe3b9200-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="fe3b9200-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>