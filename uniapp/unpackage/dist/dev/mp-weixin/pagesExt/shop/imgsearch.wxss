
.search-container {position: fixed;width: 100%;background: #fff;z-index:9;top:0px}
.search-navbar-img { height: 70rpx; text-align: center;background-color: #fff; border-radius: 20rpx; border-bottom-left-radius: 0; border-bottom-right-radius: 0;}
.search-navbar-img image { border: 3px solid #fff; border-radius: 20rpx; margin-top: -30rpx;width: 100rpx;height:100rpx;}
.search-navbar {display: flex;text-align: center;align-items:center;padding:5rpx 0;
    background-color: #fff;
    margin-bottom: 20rpx;
    border-radius: 20rpx; border-top-left-radius: 0; border-top-right-radius: 0;}
.search-navbar-item {flex: 1;height: 70rpx;line-height: 70rpx;position: relative;font-size:28rpx;font-weight:bold;color:#323232}
.search-navbar-item .iconshangla{position: absolute;top:-4rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}
.search-navbar-item .icondaoxu{position: absolute;top: 8rpx;padding: 0 6rpx;font-size: 20rpx;color:#7D7D7D}
.search-navbar-item .iconshaixuan{margin-left:10rpx;font-size:22rpx;color:#7d7d7d}
.filter-scroll-view{margin-top:0px}
.search-filter{display: flex;flex-direction: column;text-align: left;width:100%;flex-wrap:wrap;padding:0;}
.filter-content-title{color:#999;font-size:28rpx;height:30rpx;line-height:30rpx;padding:0 30rpx;margin-top:30rpx;margin-bottom:10rpx}
.filter-title{color:#BBBBBB;font-size:32rpx;background:#F8F8F8;padding:60rpx 0 30rpx 20rpx;}
.search-filter-content{display: flex;flex-wrap:wrap;padding:10rpx 20rpx;}
.search-filter-content .filter-item{background:#F4F4F4;border-radius:28rpx;color:#2B2B2B;font-weight:bold;margin:10rpx 10rpx;min-width:140rpx;height:56rpx;line-height:56rpx;text-align:center;font-size: 24rpx;padding:0 30rpx}
.search-filter-content .close{text-align: right;font-size:24rpx;color:#ff4544;width:100%;padding-right:20rpx}
.search-filter button .icon{margin-top:6rpx;height:54rpx;}
.search-filter-btn{display:flex;padding:30rpx 30rpx;justify-content: space-between}
.search-filter-btn .btn{width:240rpx;height:66rpx;line-height:66rpx;background:#fff;border:1px solid #e5e5e5;border-radius:33rpx;color:#2B2B2B;font-weight:bold;font-size:24rpx;text-align:center}
.search-filter-btn .btn2{width:240rpx;height:66rpx;line-height:66rpx;border-radius:33rpx;color:#fff;font-weight:bold;font-size:24rpx;text-align:center}
.product-container {width: 100%;margin-top: 100rpx;font-size:26rpx;padding:0 24rpx}
.topimg {width:96%;height:316rpx;margin: auto;margin-top: 10rpx;}
.centerimg { width: 480rpx; margin: 80rpx auto 0; height: 340rpx; border: 2px dashed #ccc; border-radius: 20rpx;background-position: center;background-repeat: no-repeat; background-size:180rpx; text-align: center;}
.centerimg image{width: 200rpx;height: 200rpx; margin-top: 40rpx;}
.centerimg .title { font-size: 32rpx;}

