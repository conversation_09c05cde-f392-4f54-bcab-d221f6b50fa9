
page {
	position: absolute;
	width: 100%;
	height: 100%;
	overflow: hidden;
}
.scene {
	position: absolute;
	left: -100%;
	right: -100%;
	bottom: -100%;
	top: -100%;
	display: block;
	width: 100%;
	margin: auto auto;
}
movable-area {
	position: absolute;
	height: 100%;
	width: 100%;
	overflow: hidden;
}
.move_module {
	position: absolute;
	z-index: 5;
}
.move_item {
	position: absolute;
	height: 100%;
	width: 100%;
	border: 2px dashed rgba(0, 0, 0, 0);
}
.move_active {
	border: 2px dashed #fff;
}
.move_image {
	position: absolute;
	height: 100%;
	width: 100%;
}
.move_spin {
	position: absolute;
	height: 40rpx;
	width: 40rpx;
	top: -20rpx;
	right: -20rpx;
	border-radius: 100rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #666;
}
.move_spin ._img {
	height: 30rpx;
	width: 30rpx;
	display: block;
}
.move_close {
	position: absolute;
	height: 40rpx;
	width: 40rpx;
	left: -20rpx;
	bottom: -20rpx;
	border-radius: 100rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #666;
}
.move_close ._img {
	height: 30rpx;
	width: 30rpx;
	display: block;
}
.move_size {
	position: absolute;
	height: 40rpx;
	width: 40rpx;
	right: -20rpx;
	bottom: -20rpx;
	border-radius: 100rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #666;
}
.move_size ._img {
	height: 30rpx;
	width: 30rpx;
	-webkit-transform: rotate(90deg);
	        transform: rotate(90deg);
	display: block;
}
.class {
	position: absolute;
	width: 100%;
	padding: 0 50rpx;
	bottom: 100rpx;
	white-space: nowrap;
	align-items: center;
}
.class_item {
	height: 110rpx;
	width: 110rpx;
	display: inline-block;
	border-radius: 10rpx;
	margin-right: 20rpx;
	border: 2px solid rgba(0, 0, 0, 0);
}
.class_active {
	border: 2px solid #e8506f;
}
.class_opt {
	bottom: calc(env(safe-area-inset-bottom) + 230rpx);
}
.cart {
	position: absolute;
	left: 50rpx;
}
.cart_item {
	height: 110rpx;
	width: 110rpx;
	border-radius: 10rpx;
	border: 2px solid #e8506f;
}
.cart_title {
	font-size: 28rpx;
	color: #333;
	text-align: center;
	margin: 5rpx 0 0 0;
}
.navigation {
	position: absolute;
	padding: 10rpx 30rpx;
	width: 100%;
	box-sizing: border-box;
	display: flex;
	z-index: 10;
}
.navigation_item {
	margin-right: 30rpx;
	width: 50rpx;
	height: 50rpx;
}
.navigation_icon {
	padding: 0 !important;
	margin: 0 !important;
	font-size: 0 !important;
	background: rgba(0, 0, 0, 0) !important;
}
.table {
	position: fixed;
	bottom: 0;
	height: 80rpx;
	width: 100%;
	font-size: 26rpx;
	color: #a9a19e;
	display: flex;
	align-items: center;
	z-index: 10;
	background: rgba(0, 0, 0, 0.7);
}
.table_opt {
	bottom: calc(env(safe-area-inset-bottom) + 110rpx);
}
.table_item {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
}
.table_icon {
	height: 50rpx;
	width: 50rpx;
	margin-right: 15rpx;
}
.page_cut {
	position: fixed;
	height: 100%;
	width: 100%;
	z-index: 5;
	background: rgba(0, 0, 0, 0.3);
}
.alert {
	position: fixed;
	height: 100%;
	width: 100%;
	background: rgba(0, 0, 0, 0.7);
	z-index: 15;
}
.alert_hide {
	position: absolute;
	height: 100%;
	width: 100%;
}
.alert_module {
	position: absolute;
	padding: 30rpx;
	width: 100%;
	box-sizing: border-box;
	background: #fff;
	bottom: 0;
	border-radius: 18rpx 18rpx 0 0;
}
.alert_opt {
	bottom: 110rpx;
}
.alert_head {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.alert_btn {
	position: relative;
	height: 60rpx;
	width: 180rpx;
	border: 1px solid #65c498;
	color: #65c498;
	font-size: 26rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 100rpx;
	background: rgba(104, 196, 153, 0.15);
}
.alert_close {
	height: 40rpx;
	width: 40rpx;
}
.alert_content {
	position: relative;
	display: flex;
	flex-wrap: wrap;
	white-space: normal;
	max-height: 450rpx;
	padding-top: 30rpx;
}
.alert_item {
	position: relative;
	width: 30%;
	height: 200rpx;
	margin-right: 5%;
	margin-bottom: 30rpx;
	overflow: hidden;
	display: inline-block;
}
.alert_item:nth-child(3n) {
	margin-right: 0;
}
.alert_item ._img {
	position: absolute;
	left: -100%;
	right: -100%;
	bottom: -100%;
	top: -100%;
	display: block;
	width: 100%;
	margin: auto auto;
}
.alert_title {
	position: relative;
	font-size: 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	color: #333;
}
.alert_table {
	position: relative;
	padding: 30rpx 0 0 0;
	white-space: nowrap;
}
.alert_table view {
	position: relative;
	padding-right: 30rpx;
	font-size: 28rpx;
	display: inline-block;
}
.alert_active {
	color: #e0110a;
	font-weight: bold;
}
.canvasDraw {
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
}

