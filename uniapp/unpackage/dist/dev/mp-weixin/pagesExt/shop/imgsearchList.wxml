<view class="container"><block wx:if="{{isload}}"><block><view class="search-container" style="{{(history_show?'height:100%;':'')}}"><uni-drawer class="vue-ref" vue-id="0e377ffc-1" mode="right" width="{{280}}" data-ref="showRight" data-event-opts="{{[['^change',[['change',['$event','showRight']]]]]}}" bind:change="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="filter-scroll-view"><scroll-view class="filter-scroll-view-box" scroll-y="true"><view class="search-filter"><view class="filter-title">筛选</view><view class="filter-content-title">商品分组</view><view class="search-filter-content"><view class="filter-item" style="{{(catchegid==''?'color:'+$root.m0+';background:rgba('+$root.m1+',0.1)':'')}}" data-gid data-event-opts="{{[['tap',[['groupClick',['$event']]]]]}}" catchtap="__e">全部</view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="filter-item" style="{{(catchegid==item.$orig.id?'color:'+item.m2+';background:rgba('+item.m3+',0.1)':'')}}" data-gid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['groupClick',['$event']]]]]}}" catchtap="__e">{{item.$orig.name}}</view></block></block></view><block wx:if="{{!bid||bid<=0}}"><block><view class="filter-content-title">商品分类</view><view class="search-filter-content"><view class="filter-item" style="{{(catchecid==oldcid?'color:'+$root.m4+';background:rgba('+$root.m5+',0.1)':'')}}" data-cid="{{oldcid}}" data-event-opts="{{[['tap',[['cateClick',['$event']]]]]}}" catchtap="__e">全部</view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="filter-item" style="{{(catchecid==item.$orig.id?'color:'+item.m6+';background:rgba('+item.m7+',0.1)':'')}}" data-cid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['cateClick',['$event']]]]]}}" catchtap="__e">{{item.$orig.name}}</view></block></block></view></block></block><block wx:else><block><view class="filter-content-title">商品分类</view><view class="search-filter-content"><view class="filter-item" style="{{(catchecid2==oldcid2?'color:'+$root.m8+';background:rgba('+$root.m9+',0.1)':'')}}" data-cid2="{{oldcid2}}" data-event-opts="{{[['tap',[['cate2Click',['$event']]]]]}}" catchtap="__e">全部</view><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="filter-item" style="{{(catchecid2==item.$orig.id?'color:'+item.m10+';background:rgba('+item.m11+',0.1)':'')}}" data-cid2="{{item.$orig.id}}" data-event-opts="{{[['tap',[['cate2Click',['$event']]]]]}}" catchtap="__e">{{item.$orig.name}}</view></block></block></view></block></block><view class="search-filter-btn"><view data-event-opts="{{[['tap',[['filterReset',['$event']]]]]}}" class="btn" bindtap="__e">重置</view><view data-event-opts="{{[['tap',[['filterConfirm',['$event']]]]]}}" class="btn2" style="{{'background:'+($root.m12)+';'}}" bindtap="__e">确定</view></view></view></scroll-view></view></uni-drawer></view><view class="product-container"><block wx:if="{{imgurl}}"><view class="search-navbar-img"><image src="{{imgurl}}" mode="aspectFill" data-event-opts="{{[['tap',[['goback']]]]}}" bindtap="__e"></image></view></block><view hidden="{{!(!history_show)}}" class="search-navbar"><view class="search-navbar-item" style="{{(!field||field=='sort'?'color:'+$root.m13:'')}}" data-field="sort" data-order="desc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">综合</view><block wx:if="{{$root.g0}}"><view class="search-navbar-item" style="{{(field=='sales'?'color:'+$root.m14:'')}}" data-field="sales" data-order="desc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">销量</view></block><block wx:if="{{$root.g1}}"><view class="search-navbar-item" style="{{(field=='stock'?'color:'+$root.m15:'')}}" data-field="stock" data-order="desc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">现货</view></block><view class="search-navbar-item" data-field="sell_price" data-order="{{order=='asc'?'desc':'asc'}}" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e"><text style="{{(field=='sell_price'?'color:'+$root.m16:'')}}">价格</text><text class="iconfont iconshangla" style="{{(field=='sell_price'&&order=='asc'?'color:'+$root.m17:'')}}"></text><text class="iconfont icondaoxu" style="{{(field=='sell_price'&&order=='desc'?'color:'+$root.m18:'')}}"></text></view><view data-event-opts="{{[['tap',[['showDrawer',['showRight']]]]]}}" class="search-navbar-item flex-x-center flex-y-center" catchtap="__e">筛选<text class="{{['iconfont iconshaixuan '+(showfilter?'active':'')]}}"></text></view></view><block wx:if="{{$root.g2}}"><block><block wx:if="{{productlisttype=='item2'}}"><dp-product-item vue-id="0e377ffc-2" data="{{datalist}}" showsales="{{hide_sales}}" showstock="{{hide_stock}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-product-item></block><block wx:if="{{productlisttype=='itemlist'}}"><dp-product-itemlist vue-id="0e377ffc-3" data="{{datalist}}" showsales="{{hide_sales}}" showstock="{{hide_stock}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-product-itemlist></block></block></block><block wx:if="{{nomore}}"><nomore vue-id="0e377ffc-4" text="没有更多商品了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="0e377ffc-5" text="没有查找到相关商品" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="0e377ffc-6" bind:__l="__l"></loading></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="0e377ffc-7" bind:__l="__l"></loading></block><dp-tabbar vue-id="0e377ffc-8" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="0e377ffc-9" data-ref="popmsg" bind:__l="__l"></popmsg></view>