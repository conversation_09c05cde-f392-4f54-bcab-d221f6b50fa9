
.page {
	height: 100%;
	padding-bottom: 200rpx;
}
.header {
	z-index: 10;
	position: fixed;
	height: 80rpx;
	width: 100%;
	padding: 0 30rpx;
	box-sizing: border-box;
	font-size: 26rpx;
	color: #fff;
	background: rgba(0, 0, 0, 0.6);
}
.header_item {
	padding: 0 15rpx;
}
.header_item image {
	width: 35rpx;
	height: 35rpx;
	margin-right: 10rpx;
}
.header_btn {
	width: 150rpx;
	text-align: center;
	height: 60rpx;
	font-size: 26rpx;
	color: #333;
	background-color: #FDDF1B;
	border-radius: 10rpx;
}
.slides {
	height: auto;
}
.slides_img {
	width: 100%;
	margin-bottom: -10rpx;
}
.countdown {
	display: flex;
	justify-content: space-between;
	height: 90rpx;
	padding: 0rpx 20rpx;
}
.countdown .xsqg {
	padding: 10rpx 20rpx;
	color: #fff;
	background-color: #fe4440;
	flex: 1.5;
	border-radius: 36rpx 0 0 0rpx;
}
.countdown .xsqg view {
	padding: 0 20rpx;
}
.countdown .djs {
	padding: 10rpx 20rpx;
	color: #fff;
	background: #ffed7d;
	flex: 2;
	border-radius: 0 36rpx 0rpx 0;
}
.activity_end{
	line-height: 80rpx;
	font-weight: 700;
	text-align: center;
}
.countdown .djs .time .timetext {
	background: #fff;
	border-radius: 8rpx;
	padding: 4rpx 10rpx;
	font-size: 24rpx;
	font-weight: bolder;
	margin: 0 8rpx;
}
.detail {
	margin: 0 20rpx;
	background-color: #fff;
	border-radius: 0 0 32rpx 32rpx;
	padding: 30rpx;
}
.detail_title{
	color: #000;
	font-weight: bold;
	text-align: center;
	font-size: 38rpx;
}
.detail_data{
	color: #999;
	font-size: 24rpx;
	text-align: center;
	margin-top: 30rpx;
}
.detail_data text{
	margin: 0 20rpx;
}
.liucheng {
	margin: 0 20rpx;
	padding: 20rpx;
	display: flex;
	align-items: center;
	background: #fff;
	margin-top: 20rpx;
	border-radius: 10rpx;
}
.liucheng .number {
	background: #f8a024;
	color: #fff;
	font-size: 24rpx;
	width: 30rpx;
	height: 30rpx;
	line-height: 30rpx;
	text-align: center;
	display: block;
	border-radius: 50%;
	margin-right: 10rpx;
}
.liucheng_module {
	flex: 1;
}
.step{
	padding: 30rpx;
	margin: 20rpx;
	background: #fff;
	border-radius: 32rpx;
}
.step_title{
	text-align: center;
	height: 60rpx;
	line-height: 60rpx;
	font-size: 34rpx;
}
.step_item{
	padding: 20rpx 0;
}
.step_icon{
	width: 90rpx;
	height: 90rpx;
	overflow: hidden;
	margin: 0 auto;
}
.step_text{
	font-size: 22rpx;
	text-align: center;
	margin-top: 15rpx;
}
.step_icon image{
	width: 90rpx;
	height: 90rpx;
	-webkit-transform: translateX(90rpx);
	        transform: translateX(90rpx);
}
.step_tag{
	width: 30rpx;
	height: 30rpx;
	margin-top: -40rpx;
	overflow: hidden;
}
.step_tag image{
	width: 30rpx;
	height: 30rpx;
	-webkit-transform: translateX(30rpx);
	        transform: translateX(30rpx);
}
.join {}
.join .join_number {
	text-align: center;
	height: 80rpx;
	line-height: 80rpx;
	font-size: 34rpx;
}
.join_list {
	padding: 10rpx;
	display: flex;
	flex-wrap: wrap;
	overflow: hidden;
}
.join_list .join_user {
	padding: 14rpx;
}
.join_list .img {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
}
.join_list .join_user .nickname {
	width: 80rpx;
	overflow: hidden;
	text-align: center;
}
.join_head{
	height: 80rpx;
	width: 80rpx;
	border-radius: 100rpx;
	margin-left: -20rpx;
	border: 4rpx solid #fff;
	display: block;
}
.buy_swiper {
	position: relative;
	height: 180rpx;
}
.buy .buy_list {
	display: flex;
	justify-content: space-between
}
.buy .buy_list .img {
	width: 70rpx;
	height: 70rpx;
	border-radius: 50%;
	align-items: center;
	margin-right: 10rpx;
}
.buy .buy_list text {
	line-height: 80rpx;
	text-align: center;
}
.moudle {
	margin: 0 20rpx;
	padding: 20rpx;
	background: #fff;
	margin-top: 20rpx;
	border-radius: 32rpx;
}
.moudle .moudle_title {
	text-align: center;
	height: 80rpx;
	line-height: 80rpx;
	font-size: 34rpx;
}
.ranking .ranking_list {
	display: flex;
	justify-content: space-between;
	padding: 15rpx 0;
}
.ranking .ranking_list text {
	line-height: 80rpx;
	text-align: center;
}
.ranking .ranking_list .img {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	align-items: center;
	margin-right: 10rpx;
}
.ranking_number {
	font-size: 34rpx;
	padding: 0 20rpx;
}
.module_btn {
	position: fixed;
	bottom: 10rpx;
	left: 0;
	width: 100%;
	padding: 0 20rpx;
	height: 90rpx;
}
.btn {
	justify-content: center;
	width: 100%;
	border-radius: 50rpx;
	overflow: hidden;
	display: flex;
}
.btn_bar {
	flex: 1;
	color: #fff;
	font-size: 28rpx;
	align-items: center;
	text-align: center;
	line-height: 90rpx;
	font-size: 36rpx;
}
.btn_left {
	background: #fe4440;
}
.btn_right {
	background: #ffed7d;
	color: #fff;
}
.red {
	color: red;
	font-weight: 700;
	padding: 0 5rpx;
}
.f1 {
	flex: 1;
}
.posterDialog .main {
    width: 80%;
    margin: 31px 10% 15px 10%;
    background: #fff;
    position: relative;
    border-radius: 10px;
    top: 11%;
}
.covermy{position:absolute;z-index:99999;cursor:pointer;display:flex;flex-direction:column;align-items:center;justify-content:center;overflow:hidden;z-index:9999;top:160rpx;right:0;color:#fff;background-color:rgba(17,17,17,0.3);width:140rpx;height:60rpx;font-size:26rpx;border-radius:30rpx 0px 0px 30rpx;}
.floating-button {
  position: fixed;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(17,17,17,0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0,0,0,0.2);
  z-index: 1000;
  touch-action: none;
}
.floating-button.playing {
  background-color: rgba(17,17,17,0.3);
}
.play-icon::before {
  content: "\25B6";
  color: white;
  font-size: 24px;
}
.pause-icon::before {
  content: "\275A\275A";
  color: white;
  font-size: 24px;
}

