<view><block wx:if="{{isload}}"><view class="page" style="{{('background:'+info.bgcolor+';background-size:100%')}}"><view class="slides"><image class="slides_img" src="{{info.fmpic}}" mode="widthFix"></image></view><view class="countdown"><view class="xsqg flex-y-center" style="{{'background:'+(info.color2)+';'}}"><text style="font-size:24rpx;">抢购价：</text><text style="font-size:38rpx;font-weight:500;margin-right:5rpx;">{{"￥"+info.price}}</text></view><view class="djs flex-y-center" style="{{'background:'+(info.color1)+';'}}"><block wx:if="{{time.days>0||time.hours>0||time.min>0||time.sec>0}}"><view class="time flex-y-center flex"><view style="font-size:24rpx;">距结束：</view><block wx:if="{{time.days>0}}"><text class="timetext" style="{{'color:'+(info.color1?info.color1:$root.m0)+';'}}">{{time.days}}</text></block><block wx:if="{{time.days>0}}"><text>:</text></block><text class="timetext" style="{{'color:'+(info.color1?info.color1:$root.m1)+';'}}">{{time.hours}}</text>:<text class="timetext" style="{{'color:'+(info.color1?info.color1:$root.m2)+';'}}">{{time.min}}</text>:<text class="timetext" style="{{'color:'+(info.color1?info.color1:$root.m3)+';'}}">{{time.sec}}</text></view></block><block wx:else><view class="activity_end">活动已结束</view></block></view></view><view class="detail"><view class="detail_title">{{info.name}}</view><view class="detail_data" style="{{'color:'+(info.color1?info.color1:$root.m4)+';'}}">{{''+info.viewnum+'浏览'}}<text>·</text>{{''+info.buy_count+'购买'}}<text>·</text>{{''+info.join_count+'参与'}}<text>·</text>{{''+info.share_count+'分享'}}</view></view><view class="step"><view class="step_title">参入方式</view><view class="flex-y-center"><view class="step_item flex1"><view class="step_icon"><image style="{{('transform: translateZ(0);filter: drop-shadow('+info.color1+' -90rpx 0rpx 0rpx);')}}" src="{{pre_url+'/static/img/hbtk_i1.svg'}}"></image></view><view class="step_text" style="{{'color:'+(info.color1?info.color1:$root.m5)+';'}}">1.点击立即抢购</view></view><view class="step_tag"><image style="{{('transform: translateZ(0);filter: drop-shadow('+info.color1+' -30rpx 0rpx 0rpx);')}}" src="{{pre_url+'/static/img/hbtk_tag.svg'}}"></image></view><view class="step_item flex1"><view class="step_icon"><image style="{{('transform: translateZ(0);filter: drop-shadow('+info.color1+' -90rpx 0rpx 0rpx);')}}" src="{{pre_url+'/static/img/hbtk_i2.svg'}}"></image></view><view class="step_text" style="{{'color:'+(info.color1?info.color1:$root.m6)+';'}}">2.好友扫码抢购</view></view><view class="step_tag"><image style="{{('transform: translateZ(0);filter: drop-shadow('+info.color1+' -30rpx 0rpx 0rpx);')}}" src="{{pre_url+'/static/img/hbtk_tag.svg'}}"></image></view><view class="step_item flex1"><view class="step_icon"><image style="{{('transform: translateZ(0);filter: drop-shadow('+info.color1+' -90rpx 0rpx 0rpx);')}}" src="{{pre_url+'/static/img/hbtk_i3.svg'}}"></image></view><view class="step_text" style="{{'color:'+(info.color1?info.color1:$root.m7)+';'}}">3.到店进行核销</view></view></view></view><view class="join moudle"><view class="join_number"><text class="f1">已参与<text class="red">{{info.join_count}}</text>人</text></view><view class="join_list flex-xy-center"><view class="flex" style="padding-left:20rpx;"><block wx:for="{{join_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><image class="join_head" src="{{item.headimg}}"></image></block></view></view></view><block wx:if="{{info.show_buylog!=2}}"><view class="buy moudle"><view class="buy_number moudle_title">已有<text class="red">{{info.buy_count}}</text>人购买</view><swiper class="buy_swiper" circular="{{true}}" vertical="{{true}}" autoplay="true" display-multiple-items="{{info.buy_count<2?info.buy_count:2}}" interval="3000" duration="500"><block wx:for="{{buy_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item style="height:95rpx;"><view class="buy_list"><view class="flex-y-center" style="width:200rpx;overflow:hidden;"><image class="img" src="{{item.headimg}}"></image><block wx:if="{{!info.show_buylog}}"><text>{{item.nickname}}</text></block></view><text>{{"支付"+item.price+"元"}}</text><text>{{item.createtime}}</text></view></swiper-item></block></block></swiper></view></block><block wx:if="{{info.content}}"><view class="desc moudle"><view class="moudle_title">活动介绍</view><rich-text style="padding:0 20rpx;" nodes="{{info.content}}"></rich-text></view></block><block wx:if="{{info.guize}}"><view class="desc moudle"><view class="moudle_title">活动规则</view><rich-text style="padding:0 20rpx;" nodes="{{info.guize}}"></rich-text></view></block><block wx:if="{{info.show_ranking}}"><view class="ranking moudle"><view class="moudle_title">邀请排行榜</view><block wx:for="{{yq_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="ranking_list"><view class="flex-y-center"><text class="ranking_number">{{index+1}}</text><image class="img" src="{{item.headimg}}"></image>{{''+item.nickname+''}}</view><text>{{"邀请"+item.yqnum+"人"}}</text></view></block></view></block><view class="module_btn"><view class="btn"><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="btn_left btn_bar" style="{{'background:'+(info.color2)+';'}}" bindtap="__e">专属海报</view><block wx:if="{{time.days>0||time.hours>0||time.min>0||time.sec>0}}"><view data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" class="btn_right btn_bar" style="{{'background:'+(info.color1)+';'}}" bindtap="__e">支付抢购</view></block><block wx:else><view class="btn_right btn_bar" style="background:#eee;color:#333;">活动已结束</view></block></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/close.png'}}"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block><block wx:if="{{musicurl}}"><view data-event-opts="{{[['tap',[['togglePlayPause',['$event']]]]]}}" class="{{['floating-button',(isPlaying)?'playing':'']}}" style="top:260rpx;right:10rpx;" bindtap="__e"><block wx:if="{{isPlaying}}"><image class="img" style="width:100%;height:100%;" src="{{pre_url+'/static/img/music.png'}}"></image></block><block wx:if="{{!isPlaying}}"><image class="img" style="width:100%;height:100%;" src="{{pre_url+'/static/img/music_pause.png'}}"></image></block></view></block><button class="covermy" style="top:80rpx;" data-url="/pages/index/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">首页</button><button class="covermy" data-url="orderlist" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">我的记录</button><block wx:if="{{loading}}"><loading vue-id="8c802144-1" bind:__l="__l"></loading></block><wxxieyi vue-id="8c802144-2" bind:__l="__l"></wxxieyi></view>