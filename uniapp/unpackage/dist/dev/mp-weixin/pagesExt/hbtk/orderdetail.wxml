<view class="container"><block wx:if="{{isload}}"><block><view class="ordertop" style="{{('background:url('+pre_url+'/static/img/ordertop.png);background-size:100%')}}"><block wx:if="{{detail.status==1}}"><view class="f1"><view class="t1">活动待核销</view></view></block><block wx:if="{{detail.status==2}}"><view class="f1"><view class="t1">活动已核销</view></view></block></view><view class="product"><view class="content"><view data-url="{{'index?id='+detail.hid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{detail.pic}}"></image></view><view class="detail"><text class="t1">{{detail.name}}</text><view class="t3"><text class="x1 flex1">{{"￥"+detail.price}}</text></view><view class="t2"><text>{{"已邀请"+detail.yqnum+"人"}}</text></view></view></view></view><block wx:if="{{detail.yqnum>0}}"><view class="orderinfo"><view class="item flex-y-center" style="padding:10rpx 0;"><text class="t1">邀请人员</text><view class="t2" style="overflow:hidden;" user-select="true" selectable="true"><block wx:for="{{detail.yqlist}}" wx:for-item="item" wx:for-index="index"><block><image class="yq_image" src="{{item.headimg}}"></image></block></block></view></view></view></block><view class="orderinfo"><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{detail.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{detail.createtime}}</text></view><block wx:if="{{detail.status>0&&detail.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{detail.paytime}}</text></view></block><block wx:if="{{detail.status==2&&detail.hxtime}}"><view class="item"><text class="t1">核销时间</text><text class="t2">{{detail.hxtime}}</text></view></block></view><view class="orderinfo"><block wx:if="{{detail.price>0}}"><view class="item"><text class="t1">支付金额</text><text class="t2 red">{{"¥"+detail.price}}</text></view></block><view class="item"><text class="t1">订单状态</text><block wx:if="{{detail.status==1}}"><text class="t2">待核销</text></block><block wx:if="{{detail.status==2}}"><text class="t2">已核销</text></block></view></view><view style="width:100%;height:160rpx;"></view><block wx:if="{{detail.status!=2}}"><view class="bottom notabbarbot"><view data-event-opts="{{[['tap',[['showhxqr',['$event']]]]]}}" class="btn2" bindtap="__e">核销码</view></view></block><uni-popup class="vue-ref" vue-id="36d629b1-1" id="dialogHxqr" type="dialog" data-ref="dialogHxqr" bind:__l="__l" vue-slots="{{['default']}}"><view class="hxqrbox"><image class="img" src="{{detail.hexiaoqr}}" data-url="{{detail.hexiaoqr}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">请出示核销码给核销员进行核销</view><view data-event-opts="{{[['tap',[['closeHxqr',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="{{pre_url+'/static/img/close2.png'}}"></image></view></view></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="36d629b1-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="36d629b1-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="36d629b1-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>