<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="7566a684-1" itemdata="{{['全部','未核销','已核销']}}" itemst="{{['all','1','2']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="order-content"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'orderdetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="content" style="border-bottom:none;"><view data-url="{{'orderdetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item.pic}}"></image></view><view class="detail"><text class="t1">{{item.name}}</text><view class="t4 flex"><view><view class="t3"><text class="x1 flex1">{{item.price==0||item.price==null?'免费':'支付金额：￥'+item.price}}</text></view><view class="t2"><text>{{"已邀请"+item.yqnum+"人"}}</text></view></view><block wx:if="{{item.status==1}}"><view class="btn2">待核销</view></block><block wx:if="{{item.status==2}}"><view class="btn2">已核销</view></block></view></view></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="7566a684-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="7566a684-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="7566a684-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="7566a684-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="7566a684-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>