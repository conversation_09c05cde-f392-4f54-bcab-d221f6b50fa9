<block wx:if="{{pageState}}"><view class="data-v-8188c30e"><calendar vue-id="2838b1ad-1" is-show="{{true}}" between-start="{{disabledStartDate}}" startDate="{{disabledEndDate}}" between-end="{{disabledEndDate}}" initMonth="24" ys-num="{{opt.ys}}" choose-type="{{opt.type}}" start-date="{{startDate}}" themeColor="{{$root.m0}}" end-date="{{endDate}}" tip-data="{{t_data}}" mode="{{t_mode}}" data-event-opts="{{[['^callback',[['getDate']]]]}}" bind:callback="__e" class="data-v-8188c30e" bind:__l="__l"></calendar><block wx:if="{{noticeState}}"><view class="date-notice data-v-8188c30e">点击日期选择时间</view></block><view class="date-footer data-v-8188c30e"><view data-event-opts="{{[['tap',[['clearChoose',['$event']]]]]}}" class="btn btn1 data-v-8188c30e" bindtap="__e">清除</view><view data-event-opts="{{[['tap',[['toDetail',['$event']]]]]}}" class="btn btn2 data-v-8188c30e" style="{{'background:'+($root.m1)+';'}}" bindtap="__e">确定</view></view></view></block>