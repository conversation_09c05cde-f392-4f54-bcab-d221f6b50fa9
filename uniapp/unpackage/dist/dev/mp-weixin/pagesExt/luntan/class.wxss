
page{width: 100%;height: auto;min-height:100%;background-color: #fff;}
.topsearch{width:100%;padding:20rpx 0rpx;margin-bottom:10rpx;margin-bottom:10rpx;background:#fff}
.topsearch .f1{height:70rpx;border-radius:35rpx;border:0;background-color:#f5f5f5;flex:1}
.topsearch .f1 image{width:30rpx;height:30rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}
.list_view{overflow: hidden;}
.category-card{width: 330rpx;float: left;margin-top: 40rpx;}
.list_content{width: 330rpx;float: left;}
/* 二级分类样式 */
.sub-categories {
    margin-top: 20rpx;
    padding: 20rpx;
    background-color: #f8f9fa;
    border-radius: 8rpx;
}
.sub-category-label {
    font-size: 24rpx;
    color: #666;
    margin-bottom: 16rpx;
    font-weight: 500;
}
.sub-category-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
}
.sub-category-btn {
    display: inline-flex;
    align-items: center;
    padding: 8rpx 16rpx;
    font-size: 22rpx;
    color: #333;
    background-color: #fff;
    border: 1rpx solid #e9ecef;
    border-radius: 20rpx;
    text-align: center;
    min-width: 80rpx;
    line-height: 1.4;
}
.sub-category-btn:active {
    background-color: #e9ecef;
}
.view-all-btn {
    color: #3B7CFF;
    border-color: #3B7CFF;
    font-weight: 500;
}
.view-all-btn:active {
    background-color: #3B7CFF;
    color: #fff;
}
.sub-category-icon {
    width: 24rpx;
    height: 24rpx;
    margin-right: 8rpx;
    border-radius: 50%;
}

