<view style="width:100%;height:100%;background-color:#fff;"><block wx:if="{{isload}}"><block><view style="width:700rpx;margin:0 auto;"><view class="topsearch flex-y-center" data-url="list?cpid=0&displaytype=0" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="搜索感兴趣的帖子" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><block wx:if="{{$root.g0}}"><view class="list_view"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="category-card" style="{{(index%2==0?'margin-right:40rpx':'')}}"><view class="list_content" data-category="{{item.$orig}}" data-event-opts="{{[['tap',[['onCategoryTap',['$event']]]]]}}" bindtap="__e"><view style="width:330rpx;height:330rpx;border-radius:8rpx;overflow:hidden;"><image style="width:330rpx;height:330rpx;" src="{{item.$orig.pic}}" mode="widthFix"></image></view><view style="line-height:60rpx;font-size:28rpx;">{{item.$orig.name}}</view></view><block wx:if="{{item.g1}}"><view class="sub-categories"><view class="sub-category-label">二级分类：</view><view class="sub-category-buttons"><block wx:for="{{item.$orig.children}}" wx:for-item="subItem" wx:for-index="subIndex" wx:key="id"><view class="sub-category-btn" data-url="{{'list?cpid='+subItem.id+'&displaytype='+item.$orig.display_type}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{subItem.pic}}"><image class="sub-category-icon" src="{{subItem.pic}}"></image></block><text>{{subItem.name}}</text></view></block><view class="sub-category-btn view-all-btn" data-url="{{'list?cpid='+item.$orig.id+'&displaytype='+item.$orig.display_type}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看全部</view></view></view></block></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="32f2bc0d-1" bind:__l="__l"></nodata></block></view></block><block wx:if="{{nomore}}"><nomore vue-id="32f2bc0d-2" bind:__l="__l"></nomore></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="32f2bc0d-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="32f2bc0d-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="32f2bc0d-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>