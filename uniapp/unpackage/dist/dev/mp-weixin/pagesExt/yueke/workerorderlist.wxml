<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="a35f10f0-1" itemdata="{{['全部','已付款','已完成']}}" itemst="{{['all','1','3']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'workerorderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="head"><block wx:if="{{item.$orig.bid!=0}}"><view class="f1" data-url="{{'/pagesExt/business/index?id='+item.$orig.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{pre_url+'/static/img/ico-shop.png'}}"></image>{{''+item.$orig.binfo.name}}</view></block><block wx:else><view>{{"订单号："+item.$orig.ordernum}}</view></block><view class="flex1"></view><block wx:if="{{item.$orig.status==0}}"><text class="st0">待付款</text></block><block wx:if="{{item.$orig.status==1&&item.$orig.refund_status==0}}"><text class="st1">已付款</text></block><block wx:if="{{item.$orig.status==1&&item.$orig.refund_status==1}}"><text class="st1">退款审核中</text></block><block wx:if="{{item.$orig.status==3}}"><text class="st4">已完成</text></block><block wx:if="{{item.$orig.status==4}}"><text class="st4">已关闭</text></block></view><view class="content" style="border-bottom:none;"><view data-url="{{'product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item.$orig.propic}}"></image></view><view class="detail"><text class="t1">{{item.$orig.proname}}</text><text class="t2">{{"顾客："+item.$orig.member.nickname}}</text><block wx:if="{{item.g0>4}}"><text class="t2">{{"开课时间："+item.$orig.yy_time}}</text></block><block wx:if="{{item.$orig.kechengnum}}"><text class="t2">{{"课程数量："+item.$orig.kechengnum}}</text></block><block wx:if="{{item.$orig.kechengnum}}"><text class="t2">{{"剩余课程："+item.$orig.surpluskechengnum}}</text></block><view class="t3"><text class="x1 flex1">{{"实付金额：￥"+item.$orig.totalprice}}</text></view></view></view><block wx:if="{{item.$orig.refund_status!=0}}"><view class="bottom"><block wx:if="{{item.$orig.refund_status==1}}"><text style="color:red;">{{'退款中￥'+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==2}}"><text style="color:red;">{{'已退款￥'+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==3}}"><text style="color:red;">退款申请已驳回</text></block></view></block><view class="bottom flex-y-center"><image style="width:40rpx;height:40rpx;border-radius:50%;margin-right:10rpx;" src="{{item.$orig.member.headimg}}"></image><text style="font-weight:bold;color:#333;margin-right:8rpx;">{{item.$orig.member.nickname}}</text>{{"(ID:"+item.$orig.mid+')'}}</view><view class="op"><block wx:if="{{item.$orig.yuyue_model==2}}"><view class="btn2" data-url="{{'/pagesB/yueke/workerstudyrecord?orderid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">学习记录</view></block></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="a35f10f0-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="a35f10f0-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="a35f10f0-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="a35f10f0-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="a35f10f0-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>