<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['topay',['$event']]]]]}}" bindsubmit="__e"><view class="address-add"><view class="linkitem"><text class="f1">联 系 人：</text><input class="input" type="text" placeholder="请输入您的姓名" placeholder-style="color:#626262;font-size:28rpx;" data-event-opts="{{[['input',[['inputLinkman',['$event']]]]]}}" value="{{linkman}}" bindinput="__e"/></view><view class="linkitem"><text class="f1">联系电话：</text><input class="input" type="text" placeholder="请输入您的手机号" placeholder-style="color:#626262;font-size:28rpx;" data-event-opts="{{[['input',[['inputTel',['$event']]]]]}}" value="{{tel}}" bindinput="__e"/></view></view><view class="buydata"><view class="bcontent"><view class="btitle">课程信息</view><view class="product"><view class="item flex"><view class="img" data-url="{{'product?id='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{product.pic}}"></image></view><view class="info flex1"><view class="f1">{{product.name}}</view><block wx:if="{{product.guige}}"><view class="f2">{{"规格："+(product.guige.gg_group_title?product.guige.gg_group_title:'')+" "+product.guige.name}}</view></block><block wx:if="{{workerinfo}}"><view class="f2">{{$root.m0+"："+workerinfo.realname+" "+(workerinfo.dengji||'')}}</view></block><view class="f3"><text style="font-weight:bold;">{{"￥"+product.sell_price}}</text><block wx:if="{{product.yuyue_model&&product.yuyue_model==2}}"><text style="padding-left:20rpx;">{{'× '+product.num}}</text></block></view></view></view></view><block wx:if="{{product.starttime&&product.endtime}}"><view class="price"><view class="f1">课程时间</view><view class="f2">{{product.yy_date+" "+product.starttime+" ~ "+product.endtime}}</view></view></block><view class="price"><text class="f1">课程价格</text><block wx:if="{{product.num&&product.num>0}}"><text class="f2">{{"¥"+product.sell_price*product.num}}</text></block><block wx:else><text class="f2">{{"¥"+product.sell_price}}</text></block></view><block wx:if="{{product.yuyue_model&&product.yuyue_model==2}}"><view class="price"><text class="f1">课程数量</text><text class="f2">{{product.total_kecheng_num+"节"}}</text></view></block><block wx:if="{{product.yuyue_model&&product.yuyue_model==2}}"><view class="price"><text class="f1">{{$root.m1}}</text><text class="f2">{{product.duration+"分钟/节"}}</text></view></block></view><view class="bcontent2"><block wx:if="{{userinfo.leveldk_money>0}}"><view class="price"><text class="f1">{{$root.m2+"折扣("+userinfo.discount+"折)"}}</text><text class="f2">{{"-¥"+userinfo.leveldk_money}}</text></view></block><block wx:if="{{ykset.iscoupon==1}}"><view class="price"><view class="f1">{{$root.m3}}</view><block wx:if="{{couponCount>0}}"><view data-event-opts="{{[['tap',[['showCouponList',['$event']]]]]}}" class="f2" bindtap="__e"><text style="{{'color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx;'+('background:'+($root.m4)+';')}}">{{couponrid!=0?couponList[couponkey].couponname:couponCount+'张可用'}}</text><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></block><block wx:else><text class="f2" style="color:#999;">{{"无可用"+$root.m5}}</text></block></view></block><view style="display:none;">{{test}}</view><block wx:for="{{formdata}}" wx:for-item="item" wx:for-index="idx" wx:key="id"><view class="form-item"><view class="label">{{item.val1}}<block wx:if="{{item.val3==1}}"><text style="color:red;">*</text></block></view><block wx:if="{{item.key=='input'}}"><block><input class="input" type="text" name="{{'form_'+idx}}" placeholder="{{item.val2}}" placeholder-style="font-size:28rpx"/></block></block><block wx:if="{{item.key=='textarea'}}"><block><textarea class="textarea" name="{{'form_'+idx}}" placeholder="{{item.val2}}" placeholder-style="font-size:28rpx"></textarea></block></block><block wx:if="{{item.key=='radio'}}"><block><radio-group class="radio-group" name="{{'form_'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><radio class="radio" value="{{item1}}"></radio>{{item1+''}}</label></block></radio-group></block></block><block wx:if="{{item.key=='checkbox'}}"><block><checkbox-group class="checkbox-group" name="{{'form_'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><checkbox class="checkbox" value="{{item1}}"></checkbox>{{item1+''}}</label></block></checkbox-group></block></block><block wx:if="{{item.key=='selector'}}"><block><picker class="picker" mode="selector" name="{{'form_'+idx}}" value="" range="{{item.val2}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]||editorFormdata[idx]===0}}"><view>{{''+item.val2[editorFormdata[idx]]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='time'}}"><block><picker class="picker" mode="time" name="{{'form_'+idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view>{{editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='date'}}"><block><picker class="picker" mode="date" name="{{'form_'+idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view>{{editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='upload'}}"><block><input style="display:none;" type="text" name="{{'form_'+idx}}" value="{{editorFormdata[idx]}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{editorFormdata[idx]}}"><view class="form-imgbox"><view class="layui-imgbox-close" style="z-index:2;" data-idx="{{idx}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="form-imgbox-img"><image class="image" src="{{editorFormdata[idx]}}" data-url="{{editorFormdata[idx]}}" mode="aspectFit" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:else><view class="form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-idx="{{idx}}" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></block></view></block></block></view></block></view></view><view style="width:100%;height:182rpx;"></view><view class="footer flex"><view class="text1 flex1">总计：<text style="font-weight:bold;font-size:36rpx;">{{"￥"+totalprice}}</text></view><block wx:if="{{issubmit}}"><button class="op" style="background:#999;">确认提交</button></block><block wx:else><button class="op" style="{{'background:'+('linear-gradient(-90deg,'+$root.m6+' 0%,rgba('+$root.m7+',0.8) 100%)')+';'}}" form-type="submit">确认提交</button></block></view></form><block wx:if="{{couponvisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">{{"请选择"+$root.m8}}</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><couponlist vue-id="923f11a0-1" couponlist="{{couponList}}" choosecoupon="{{true}}" selectedrid="{{couponrid}}" data-event-opts="{{[['^chooseCoupon',[['chooseCoupon']]]]}}" bind:chooseCoupon="__e" bind:__l="__l"></couponlist></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="923f11a0-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="923f11a0-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="923f11a0-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>