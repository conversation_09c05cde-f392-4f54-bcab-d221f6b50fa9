<view class="container"><block wx:if="{{isload}}"><block><view class="ordertop flex" style="{{('background:url('+pre_url+'/static/img/orderbg.png);background-size:100%')}}"><block wx:if="{{detail.status==0}}"><view class="f1"><view class="t1">等待买家付款</view><block wx:if="{{djs}}"><view class="t2">{{"剩余时间："+djs}}</view></block></view></block><block wx:if="{{detail.status==1}}"><view class="f1"><view class="t2">订单已付款</view></view></block><block wx:if="{{detail.status==3}}"><view class="f1"><view class="t1">订单已完成</view></view></block><block wx:if="{{detail.status==4}}"><view class="f1"><view class="t1">订单已取消</view></view></block><view class="orderx"><image src="{{pre_url+'/static/img/orderx.png'}}"></image></view></view><view class="orderinfo orderinfotop"><view class="title">订单信息</view><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{detail.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{detail.createtime}}</text></view><view class="item"><text class="t1">预约时间</text><text class="t2">{{detail.yy_time}}</text></view></view><block wx:if="{{$root.g0>0}}"><view class="orderinfo"><block wx:for="{{detail.formdata}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="item"><text class="t1">{{item[0]}}</text><block wx:if="{{item[2]=='upload'}}"><view class="t2"><image style="width:400rpx;height:auto;" src="{{item[1]}}" mode="widthFix" data-url="{{item[1]}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:else><text class="t2" user-select="true" selectable="true">{{item[1]}}</text></block></view></block></view></block><view class="product"><view class="title">课程信息</view><view class="content"><view data-url="{{'product?id='+detail.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{detail.propic}}"></image></view><view class="detail"><text class="t1">{{detail.proname}}</text><view class="t2 flex flex-y-center flex-bt"><text>{{$root.m0+"："+workerinfo.realname+" "+(workerinfo.dengji||'')}}</text></view><view class="t3"><text class="x1 flex1">{{"￥"+detail.product_price}}</text></view></view></view></view><view class="orderinfo"><view class="item"><text class="t1">下单人</text><text class="flex1"></text><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{detail.headimg}}"></image><text style="height:80rpx;line-height:80rpx;">{{detail.nickname}}</text></view><view class="item"><text class="t1">{{$root.m1+"ID"}}</text><text class="t2">{{detail.mid}}</text></view></view><view class="orderinfo"><block wx:if="{{detail.total_kecheng_num}}"><view class="item"><text class="t1">课程数量</text><text class="t2">{{detail.total_kecheng_num+"节"}}</text></view></block><block wx:if="{{detail.product.yuyue_model==2&&detail.total_kecheng_num}}"><view class="item"><text class="t1">剩余课程</text><text class="t2">{{detail.surplus_kecheng_num+"节"}}</text></view></block><block wx:if="{{detail.guige}}"><view class="item"><text class="t1">{{$root.m2}}</text><text class="t2">{{detail.guige.duration+"分钟/节"}}</text></view></block><view class="item"><text class="t1">应付金额</text><text class="t2 red">{{"¥"+detail.product_price}}</text></view><block wx:if="{{detail.leveldk_money>0}}"><view class="item"><text class="t1">{{$root.m3+"折扣"}}</text><text class="t2 red">{{"-¥"+detail.leveldk_money}}</text></view></block><block wx:if="{{detail.manjian_money>0}}"><view class="item"><text class="t1">满减活动</text><text class="t2 red">{{"-¥"+detail.manjian_money}}</text></view></block><block wx:if="{{detail.freight_type==1&&detail.freightprice>0}}"><view class="item"><text class="t1">服务费</text><text class="t2 red">{{"+¥"+detail.freight_price}}</text></view></block><block wx:if="{{detail.freight_time}}"><view class="item"><text class="t1">{{(detail.freight_type!=1?'配送':'提货')+"时间"}}</text><text class="t2">{{detail.freight_time}}</text></view></block><block wx:if="{{detail.coupon_money>0}}"><view class="item"><text class="t1">{{$root.m4+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.coupon_money}}</text></view></block><block wx:if="{{detail.scoredk>0}}"><view class="item"><text class="t1">{{$root.m5+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.scoredk_money}}</text></view></block><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+detail.totalprice}}</text></view><view class="item"><text class="t1">订单状态</text><block wx:if="{{detail.status==0}}"><text class="t2">未付款</text></block><block wx:if="{{detail.status==1}}"><text class="t2">已付款</text></block><block wx:if="{{detail.status==2}}"><text class="t2">服务中</text></block><block wx:if="{{detail.status==3}}"><text class="t2">已完成</text></block><block wx:if="{{detail.status==4}}"><text class="t2">已关闭</text></block><block wx:if="{{detail.refundCount}}"><text style="margin-left:8rpx;">{{"有退款("+detail.refundCount+")"}}</text></block></view><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款状态</text><block wx:if="{{detail.refund_status==1}}"><text class="t2 red">{{"审核中,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==2}}"><text class="t2 red">{{"已退款,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==3}}"><text class="t2 red">{{"已驳回,¥"+detail.refund_money}}</text></block></view></block><block wx:if="{{detail.balance_price>0}}"><view class="item"><text class="t1">尾款</text><text class="t2 red">{{"¥"+detail.balance_price}}</text></view></block><block wx:if="{{detail.balance_price>0}}"><view class="item"><text class="t1">尾款状态</text><block wx:if="{{detail.balance_pay_status==1}}"><text class="t2">已支付</text></block><block wx:if="{{detail.balance_pay_status==0}}"><text class="t2">未支付</text></block></view></block><block wx:if="{{detail.status>0&&detail.paytypeid!='4'&&detail.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{detail.paytime}}</text></view></block><block wx:if="{{detail.paytypeid}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{detail.paytype}}</text></view></block><block wx:if="{{detail.status>1&&detail.send_time}}"><view class="item"><text class="t1">派单时间</text><text class="t2">{{detail.send_time}}</text></view></block><block wx:if="{{detail.status>1&&detail.addmoney>0}}"><view class="item"><text class="t1">补差价</text><text class="t2 red">{{"￥"+detail.addmoney}}</text></view></block><block wx:if="{{detail.status==3&&detail.collect_time}}"><view class="item"><text class="t1">完成时间</text><text class="t2">{{detail.collect_time}}</text></view></block></view><view class="orderinfo"><view class="title">顾客信息</view><view class="item"><text class="t1">姓名</text><text class="t2">{{detail.linkman}}</text></view><view class="item"><text class="t1">手机号</text><text class="t2">{{detail.tel}}</text></view></view><view style="width:100%;height:120rpx;"></view></block></block><block wx:if="{{loading}}"><loading vue-id="46e06c96-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="46e06c96-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="46e06c96-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>