<block wx:if="{{isload}}"><view class="data-v-4bf8bc5b"><view class="screen data-v-4bf8bc5b"><view class="screen_module data-v-4bf8bc5b"><scroll-view class="screen_content data-v-4bf8bc5b" scroll-x="true"><block wx:for="{{datelist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['dateClick',[index]]]]]}}" class="{{['screen_item','data-v-4bf8bc5b',dateIndex==index?'screen_active':'']}}" bindtap="__e"><view class="screen_text data-v-4bf8bc5b">{{item.date}}</view><view class="screen_text data-v-4bf8bc5b">{{item.week}}</view></view></block></scroll-view><view data-event-opts="{{[['tap',[['alertClick',['$event']]]]]}}" class="screen_opt data-v-4bf8bc5b" bindtap="__e"><image src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAgdJREFUWEftlz1vE0EQht9Z+R/QUNBjeWavIIgGkAglEhgK0oAEBfQkUMAvACFI6KEACRoo+IhEm0iQJgKK21nL9BQ0VLTWLVrrjJzg+3BI7Oa2Ot3OvPPs7MfsEubcaM7x0QD8kwER2QBw5oCmZlNVF8e1JwF8AnDqgAA+q+rpUoBOp3PCGPMewGEAb1R16X9gROQ1gMsAfmZZ1u31etulALHTWnsuhBAhWiGENe/9yl4gmHmViJYBDIio65z7uFuncBcw8zUieh4diGjFObc2DYS1djmEsBp9QgjXvfcvJvmXbkMRuQ3gUQ5x1Tn3qg6EtfZKCOFlbntHVR8X+VWeA8x8n4ju5iO54L1fL4Ng5vNE9CG3f+C9v1dmXwkQnUXkKYAbudCiqm5OEhWRuH3jNo7tmarerMpYLYAc4i2AiwB+GGO6aZp+GxdPkuRYlmVx4R4B8E5VL1UFH05tHaNo0263D7VarRjgJICvqnp83FdEvgBYALA1GAy6/X7/Vx3t2gBRLEmSo1mW9eO3qu7wFZEQ/xtj2mmafq8TfKoMjARHgYoAdv+vApkqA/laGI60AWgy0GRg7hkgoifOuVjrh63ofNj3c4CZbxHR6G7w98Y0M4A4ImY+S0QPAfweXTJnClBQiieekPs+BUWCTQbyB00sUjseHjObgqpARf1Tl+O9Biry+wPUJ/MhhuUwKAAAAABJRU5ErkJggg==" alt class="data-v-4bf8bc5b"></image><view class="data-v-4bf8bc5b">筛选</view></view></view></view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="module data-v-4bf8bc5b"><view class="module_data data-v-4bf8bc5b" data-url="{{'product?id='+item.id+'&dateIndex='+dateIndex}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="module_img data-v-4bf8bc5b" src="{{item.pic}}" alt></image><view class="module_content data-v-4bf8bc5b"><view class="module_title data-v-4bf8bc5b">{{item.name}}</view><block wx:if="{{item.workerinfo}}"><view class="module_item data-v-4bf8bc5b">{{item.workerinfo.realname+" "+(item.workerinfo.dengji||'')+''}}<block wx:if="{{item.startime&&item.endtime}}"><view class="module_time data-v-4bf8bc5b">{{item.starttime+"~"+item.endtime}}</view></block></view></block><view class="module_item data-v-4bf8bc5b">{{"报名费"+item.sell_price+"元"}}</view></view><block wx:if="{{item.yuyue_model&&item.yuyue_model==2}}"><view class="data-v-4bf8bc5b"><view class="module_btn data-v-4bf8bc5b">预约</view></view></block><block wx:else><view class="data-v-4bf8bc5b"><block wx:if="{{item.isend}}"><view class="module_btn module_end data-v-4bf8bc5b">已结束</view></block><block wx:else><block wx:if="{{item.leftnum>0}}"><view class="module_btn data-v-4bf8bc5b">预约</view></block><block wx:else><view class="module_btn module_end data-v-4bf8bc5b">满员</view></block></block></view></block></view><view class="module_num data-v-4bf8bc5b"><view class="module_lable data-v-4bf8bc5b"><view class="data-v-4bf8bc5b">当前</view><view class="data-v-4bf8bc5b">预约</view></view><view class="module_view data-v-4bf8bc5b"><block wx:for="{{item.yyorderlist}}" wx:for-item="item2" wx:for-index="index2"><block class="data-v-4bf8bc5b"><image src="{{item2.headimg}}" class="data-v-4bf8bc5b"></image></block></block></view><block wx:if="{{!item.yuyue_model||item.yuyue_model==1}}"><view class="data-v-4bf8bc5b"><block wx:if="{{item.leftnum>0}}"><view class="module_tag data-v-4bf8bc5b">{{"剩余"+item.leftnum+"个名额"}}</view></block><block wx:else><view class="module_tag module_end data-v-4bf8bc5b">满员</view></block></view></block></view></view></block><block wx:if="{{alertState}}"><view class="alert data-v-4bf8bc5b"><view data-event-opts="{{[['tap',[['alertClick',['$event']]]]]}}" class="alert_none data-v-4bf8bc5b" bindtap="__e"></view><view class="alert_module data-v-4bf8bc5b"><view class="alert_opt data-v-4bf8bc5b"><view class="alert_table data-v-4bf8bc5b"><view class="{{['alert_view','data-v-4bf8bc5b',filterType==0?'alert_active':'']}}" data-type="{{0}}" data-event-opts="{{[['tap',[['changefilterType',['$event']]]]]}}" catchtap="__e">课程</view><view class="{{['alert_view','data-v-4bf8bc5b',filterType==1?'alert_active':'']}}" data-type="{{1}}" data-event-opts="{{[['tap',[['changefilterType',['$event']]]]]}}" catchtap="__e">时段</view></view><view data-event-opts="{{[['tap',[['filterClean',['$event']]]]]}}" class="alert_cancel data-v-4bf8bc5b" catchtap="__e">清空</view><view data-event-opts="{{[['tap',[['filterConfirm',['$event']]]]]}}" class="alert_btn data-v-4bf8bc5b" catchtap="__e">确认</view></view><block wx:if="{{filterType==0}}"><scroll-view class="alert_box data-v-4bf8bc5b" scroll-y="true"><block wx:for="{{clist}}" wx:for-item="item" wx:for-index="index"><view class="{{['alert_item','','data-v-4bf8bc5b',cachecid==item.id?'alert_current':'']}}" data-id="{{item.id}}" data-event-opts="{{[['tap',[['changecid',['$event']]]]]}}" catchtap="__e">{{item.name}}</view></block></scroll-view></block><block wx:if="{{filterType==1}}"><scroll-view class="alert_box data-v-4bf8bc5b" scroll-y="true"><block wx:for="{{timerangelist}}" wx:for-item="item" wx:for-index="index"><view class="{{['alert_item','','data-v-4bf8bc5b',cachetimerange==index?'alert_current':'']}}" data-index="{{index}}" data-event-opts="{{[['tap',[['changetimerange',['$event']]]]]}}" catchtap="__e">{{item.rangestr}}</view></block></scroll-view></block></view></view></block></view></block>