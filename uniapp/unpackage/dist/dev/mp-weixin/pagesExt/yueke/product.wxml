<view><block wx:if="{{isload}}"><block><block wx:if="{{isplay==0}}"><view class="swiper-container"><swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" interval="{{500000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{product.pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item class="swiper-item"><view class="swiper-item-view"><image class="img" src="{{item}}" mode="widthFix"></image></view></swiper-item></block></block></swiper><block wx:if="{{$root.g0>1}}"><view class="imageCount">{{current+1+"/"+$root.g1}}</view></block><block wx:if="{{product.video}}"><view data-event-opts="{{[['tap',[['payvideo',['$event']]]]]}}" class="provideo" bindtap="__e"><image src="{{pre_url+'/static/img/video.png'}}"></image><view class="txt">{{product.video_duration}}</view></view></block></view></block><view class="header"><view class="price_share"><view class="title">{{product.name}}</view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/share.png'}}"></image><text class="txt">分享</text></view></view><view class="pricebox flex"><view class="sellpoint">{{"课程价格：￥"+product.sell_price}}</view></view><block wx:if="{{product.sellpoint}}"><view class="sellpoint">{{product.sellpoint}}</view></block><block wx:if="{{product.starttime&&product.endtime}}"><view class="sellpoint">{{"课程时间："+product.yy_date+" "+product.starttime+" ~ "+product.endtime}}</view></block></view><view class="module"><block wx:if="{{workerinfo}}"><view class="module_data" data-url="{{'workerinfo?id='+workerinfo.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="module_img" src="{{workerinfo.headimg}}" alt></image><view class="module_content"><view class="module_title">{{workerinfo.realname+" "+(workerinfo.dengji||'')}}</view><view class="module_item">{{workerinfo.desc}}</view></view></view></block><view class="module_num"><view class="module_lable"><view>当前</view><view>预约</view></view><view class="module_view"><block wx:for="{{yyorderlist}}" wx:for-item="item2" wx:for-index="index2"><block><image src="{{item2.headimg}}"></image></block></block></view><block wx:if="{{!product.yuyue_model||product.yuyue_model==1}}"><view><block wx:if="{{product.leftnum>0}}"><view class="module_tag">{{"剩余"+product.leftnum+"个名额"}}</view></block><block wx:else><view class="module_tag module_end">满员</view></block></view></block></view></view><view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">课程介绍</view><view class="t2"></view><view class="t1"></view></view><view class="detail"><dp vue-id="21263e67-1" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></view><view style="width:100%;height:140rpx;"></view><block wx:if="{{product.status==1}}"><view class="{{['bottombar','flex-row',menuindex>-1?'tabbarbot':'']}}"><view class="f1"><view class="item" data-url="{{'prolist?bid='+product.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/shou.png'}}"></image><view class="t1">首页</view></view><block wx:if="{{kfurl!='contact::'}}"><view class="item" data-url="{{kfurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/kefu.png'}}"></image><view class="t1">客服</view></view></block><block wx:else><button class="item" open-type="contact" show-message-card="true"><image class="img" src="{{pre_url+'/static/img/kefu.png'}}"></image><view class="t1">客服</view></button></block><view data-event-opts="{{[['tap',[['addfavorite',['$event']]]]]}}" class="item" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/shoucang.png'}}"></image><view class="t1">{{isfavorite?'已收藏':'收藏'}}</view></view></view><view class="op"><block wx:if="{{product.yuyue_model&&product.yuyue_model==2}}"><view data-event-opts="{{[['tap',[['buydialogShow',['$event']]]]]}}" class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m0)+';'}}" bindtap="__e">立即预约</view></block><block wx:else><block wx:if="{{product.isend}}"><view class="tobuy flex-x-center flex-y-center" style="{{'background:'+('#aaa')+';'}}">已结束</view></block><block wx:else><view data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m1)+';'}}" bindtap="__e">立即预约</view></block></block></view></view></block><scrolltop vue-id="21263e67-2" isshow="{{scrolltopshow}}" bind:__l="__l"></scrolltop><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m2=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/sharefriends.png'}}"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m3=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/sharefriends.png'}}"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m4!='h5'}}"><button class="f1" open-type="share"><image class="img" src="{{pre_url+'/static/img/sharefriends.png'}}"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/sharepic.png'}}"></image><text class="t1">生成分享图片</text></view></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/close.png'}}"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block><view hidden="{{buydialogHidden}}"><view class="buydialog-mask"><view class="{{['buydialog',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" class="close" bindtap="__e"><image class="image" src="{{pre_url+'/static/img/close.png'}}"></image></view><view class="title"><image class="img" src="{{guigelist[ks].pic?guigelist[ks].pic:product.pic}}" data-url="{{guigelist[ks].pic?guigelist[ks].pic:product.pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="price"><text class="t1">￥</text>{{guigelist[ks].sell_price}}</view><view class="choosename">{{"已选规格: "+guigelist[ks].name}}</view></view><block wx:for="{{guigedata}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="guigelist flex-col"><view class="name">{{item.title}}</view><view class="item flex flex-y-center"><block wx:for="{{item.items}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><block><view class="{{['item2 '+(ggselected[item.k]==item2.k?'on':'')]}}" data-itemk="{{item.k}}" data-idx="{{item2.k}}" data-event-opts="{{[['tap',[['ggchange',['$event']]]]]}}" bindtap="__e">{{item2.title}}</view></block></block></view></view></block><view class="buynum flex flex-y-center"><view class="flex1">购买数量：</view><view class="addnum"><view class="minus"><image class="img" src="{{pre_url+'/static/img/cart-minus.png'}}" data-event-opts="{{[['tap',[['gwcminus',['$event']]]]]}}" bindtap="__e"></image></view><input class="input" type="number" data-event-opts="{{[['input',[['gwcinput',['$event']]]],['blur',[['gwcinputblur',['$event']]]]]}}" value="{{gwcnum}}" bindinput="__e" bindblur="__e"/><view class="plus"><image class="img" src="{{pre_url+'/static/img/cart-plus.png'}}" data-event-opts="{{[['tap',[['gwcplus',['$event']]]]]}}" bindtap="__e"></image></view></view></view><view class="op"><button data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" class="tobuy" style="{{'background:'+($root.m5)+';'}}" bindtap="__e">确定</button></view></view></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="21263e67-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="21263e67-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="21263e67-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>