
page {
	background: #f0f0f0;
}


.screen.data-v-4bf8bc5b {
	position: relative;
	height: 150rpx;
	z-index: 5;
}
.screen_module.data-v-4bf8bc5b {
	position: fixed;
	width: 100%;
	height: 150rpx;
	top: 0;
	left: 0;
	padding: 0 30rpx;
	box-sizing: border-box;
	background: #f0f0f0;
	display: flex;
	align-items: center;
}
.screen_opt.data-v-4bf8bc5b {
	margin: 0 0 0 10rpx;
	font-size: 26rpx;
	color: #333;
	flex-shrink: 0;
	font-size:24rpx;
}
.screen_opt image.data-v-4bf8bc5b {
	height: 34rpx;
	width: 34rpx;
	display: block;
	margin: 10rpx auto 10rpx auto;
}
.screen_opt view.data-v-4bf8bc5b {
	text-align: center;
}
.screen_content.data-v-4bf8bc5b {
	flex: 1;
	width: 500rpx;
	white-space: nowrap;
}
.screen_item.data-v-4bf8bc5b {
	height: 90rpx;
	width: 90rpx;
	display: inline-block;
	border-radius: 12rpx;
	color: #333;
	box-sizing: border-box;
}
.screen_text.data-v-4bf8bc5b {
	font-size: 26rpx;
	text-align: center;
}
.screen_text.data-v-4bf8bc5b:first-child{
	margin-top: 10rpx;
}
.screen_active.data-v-4bf8bc5b {
	background: #454545;
	color: #fff;
}
.address.data-v-4bf8bc5b{
	position: relative;
	width: 700rpx;
	height: 130rpx;
	border-radius: 20rpx;
	overflow: hidden;
	margin: 0 auto 30rpx auto;
}
.address_back.data-v-4bf8bc5b{
	height: 100%;
	width: 100%;
}
.address_data.data-v-4bf8bc5b{
	position: absolute;
	height: 100%;
	width: 100%;
	top: 0;
	left: 0;
	box-sizing: border-box;
	padding: 0 30rpx;
	display: flex;
	align-items: center;
	background: rgba(0, 0, 0, 0.4);
}
.address_content.data-v-4bf8bc5b{
	flex: 1;
}
.address_title.data-v-4bf8bc5b{
	font-size: 35rpx;
	color: #fff;
}
.address_text.data-v-4bf8bc5b{
	font-size: 24rpx;
	color: #fff;
	margin-top: 10rpx;
}
.address_icon.data-v-4bf8bc5b{
	height: 35rpx;
	width: 35rpx;
}
.module.data-v-4bf8bc5b{
	position: relative;
	width: 700rpx;
	padding: 30rpx;
	box-sizing: border-box;
	border-radius: 20rpx;
	margin: 0 auto 30rpx auto;
	background: #fff;
}
.module_data.data-v-4bf8bc5b{
	display: flex;
}
.module_img.data-v-4bf8bc5b{
	height: 130rpx;
	width: 130rpx;
	margin-right: 30rpx;
}
.module_content.data-v-4bf8bc5b{
	flex: 1;
}
.module_btn.data-v-4bf8bc5b{
	height: 65rpx;
	padding: 0 40rpx;
	color: #fff;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 100rpx;
	background: #0993fe;
}
.module_title.data-v-4bf8bc5b{
	font-size: 28rpx;
	color: #333;
}
.module_item.data-v-4bf8bc5b{
	margin-top: 10rpx;
	color: #999;
	display: flex;
	align-items: center;
	font-size: 24rpx;
}
.module_time.data-v-4bf8bc5b{
	padding: 0 10rpx;
	height: 35rpx;
	line-height: 33rpx;
	font-size: 22rpx;
	margin-left: 20rpx;
	color: #d55c5f;
	border: 1rpx solid #d55c5f;
}
.module_num.data-v-4bf8bc5b{
	display: flex;
	align-items: center;
	margin-top: 20rpx;
}
.module_lable.data-v-4bf8bc5b{
	font-size: 24rpx;
	color: #666;
	line-height: 24rpx;
	border-right: 1px solid #e0e0e0;
	padding: 0 15rpx 0 0;
	margin-right: 15rpx;
}
.module_view.data-v-4bf8bc5b{
	display: flex;
	flex: 1;
	align-items: center;
}
.module_view image.data-v-4bf8bc5b{
	height: 60rpx;
	width: 60rpx;
	border-radius: 100rpx;
	margin-right: 10rpx;
}
.module_tag.data-v-4bf8bc5b{
	height: 50rpx;
	background: #fefae8;
	color: #b37e4b;
	font-size: 24rpx;
	padding: 0 10rpx;
	line-height: 50rpx;
}
.module_end.data-v-4bf8bc5b{
	color: #999;
	background: #f0f0f0;
}
.alert.data-v-4bf8bc5b{
	position: fixed;
	height: 100%;
	width: 100%;
	top: 0;
	left: 0;
	z-index: 10;
	background: rgba(0, 0, 0, 0.7);
}
.alert_none.data-v-4bf8bc5b{
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	width: 100%;
}
.alert_module.data-v-4bf8bc5b{
	position: absolute;
	width: 100%;
	box-sizing: border-box;
	bottom: 0;
	left: 0;
	padding: 30rpx;
	background: #fff;
}
.alert_opt.data-v-4bf8bc5b{
	display: flex;
	align-items: center;
}
.alert_table.data-v-4bf8bc5b{
	display: flex;
	flex: 1;
}
.alert_view.data-v-4bf8bc5b{
	font-size: 35rpx;
	color: #333;
	padding-bottom: 5rpx;
	margin-right: 35rpx;
	border-bottom: 1px solid #fff;
}
.alert_active.data-v-4bf8bc5b{
	color: #0993fe;
	border-color: #0993fe;
}
.alert_cancel.data-v-4bf8bc5b{
	color: #999;
	font-size: 35rpx;
	background: #fff;
	padding: 5rpx 20rpx;
}
.alert_btn.data-v-4bf8bc5b{
	color: #333;
	font-size: 35rpx;
	background: #fad450;
	padding: 5rpx 20rpx;
	border-radius: 10rpx;
}
.alert_box.data-v-4bf8bc5b{
	height: 700rpx;
	margin-top: 50rpx;
}
.alert_item.data-v-4bf8bc5b{
	background: #f7f7f7;
	color: #333;
	font-size: 26rpx;
	height: 90rpx;
	display: flex;
	border-radius: 10rpx;
	align-items: center;
	justify-content: center;
	margin-top: 30rpx;
}
.alert_current.data-v-4bf8bc5b{
	border: 1px solid #fad450;
	background: #fefaeb;
}

