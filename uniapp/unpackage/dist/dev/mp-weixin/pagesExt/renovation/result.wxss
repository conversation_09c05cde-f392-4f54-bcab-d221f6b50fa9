
page {
		background: #f0f0f0;
}
.content {
		width: 750rpx;
		padding-top: 30rpx;
		padding-bottom:30rpx;
}
.result {
		width: 710rpx;
		position: relative;
		border-radius: 35rpx;
		background: #fff;
		box-sizing: border-box;
		overflow: hidden;
		margin: 0 auto;
}
.result_table {
		height: 95rpx;
		display: flex;
		background: #e4eff3;
		line-height: 95rpx;
}
.result_table .item {
		color: #434e53;
		flex: 1;
		text-align: center;
		font-size: 26rpx;
}
.result_table .active {
		font-weight: bold;
		font-size: 40rpx;
		background: #fff;
		border-radius: 20rpx 20rpx 0 0;
}
.result_module {
		padding: 30rpx;
}
.result_data {
		position: relative;
		display: flex;
		align-items: center;
		padding-bottom: 10rpx;
}
.result_chart {
		position: relative;
		height: 195rpx;
		width: 280rpx;
		margin-right: 50rpx;
}
.result_chart canvas {
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		display: block;
		bottom: 0;
		margin: auto auto;
		height: 110px;
		width: 110px;
}
.result_num{
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		display: block;
		bottom: 0;
		margin: auto auto;
		height: 110px;
		font-size: 35rpx;
		width: 110px;
		font-weight: bold;
		text-align: center;
		line-height: 110px;
}
.result_unit{
		font-size: 24rpx;
}
.result_content {
		flex: 1;
}
.result_item {
		padding: 15rpx 0;
		display: flex;
		align-items: center;
		font-size: 27rpx;
}
.result_tag {
		height: 15rpx;
		width: 15rpx;
		border-radius: 100rpx;
		margin-right: 25rpx;
}
.result_title {
		color: #999;
		flex: 1;
}
.result_price {
		font-weight: bold;
		color: #333;
}
.result_list {
		padding: 30rpx;
		background: #f5f5f5;
		color: #666;
		font-weight: bold;
		font-size: 27rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-radius: 15rpx;
		margin-top: 20rpx;
}
.result_more {
		position: relative;
		display: flex;
		align-items: center;
}
.result_more image {
		height: 25rpx;
		width: 25rpx;
		display: block;
		margin-left: 10rpx;
}
.result_option {
		padding: 30rpx 30rpx 10rpx 30rpx;
}
.result_option .item {
		font-size: 27rpx;
		display: flex;
		justify-content: space-between;
		color: #333;
		margin: 30rpx 0 0 0;
}
.result_option .item:first-child {
		margin: 0 0 0 0;
}
.result_notice {
		font-size: 23rpx;
		color: #999;
		margin-top: 20rpx;
}
.qd_guize{width:100%;margin:30rpx 0 20rpx 0;}
.qd_guize .gztitle{width:100%;text-align:center;font-size:32rpx;color:#656565;font-weight:bold;height:100rpx;line-height:100rpx}
.guize_txt{box-sizing: border-box;padding:0 30rpx;line-height:42rpx;}


