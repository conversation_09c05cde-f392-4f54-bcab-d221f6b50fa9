
page {
		background: #f0f0f0;
}
.content {
		width: 750rpx;
		padding-top: 30rpx;
		padding-bottom:30rpx;
}
.form {
		padding: 35rpx;
		width: 710rpx;
		position: relative;
		border-radius: 35rpx;
		background: #fff;
		box-sizing: border-box;
		margin: 0 auto;
}
.form_price {
		width: 100%;
		display: block;
}
.form_option {
		padding: 30rpx 15rpx;
		font-size: 26rpx;
		font-weight: bold;
		display: flex;
		justify-content: space-between;
}
.form_item {
		font-size: 30rpx;
		padding: 30rpx;
		background: #f6f7f9;
		border-radius: 10rpx;
		margin-top: 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.form_input {
		flex: 1;
}
.form_item ._img {
		height: 40rpx;
		width: 40rpx;
}
.form_item:first-child {
		margin-top: 0;
}
.form_btn {
		width: 100%;
		display: block;
		margin-top: 30rpx;
}
.agreement {
		position: relative;
		margin-top: 25rpx;
		padding: 0 20rpx;
}
.agreement_icon {
		height: 23rpx;
		width: 23rpx;
		margin: 5rpx 10rpx 0 0;
		display: block;
}
.agreement_text {
		font-size: 22rpx;
		color: #999;
}
.agreement_tag {
		color: #26c256;
}
.select {
		position: fixed;
		height: 100%;
		width: 100%;
		top: 0;
		left: 0;
		display: flex;
		justify-content: flex-end;
		background: rgba(0, 0, 0, 0.7);
}
.select_hide {
		position: absolute;
		height: 100%;
		width: 100%;
		top: 0;
		left: 0;
}
.select_body {
		position: relative;
		height: 100%;
		width: 600rpx;
		display: flex;
		border-top: 1rpx solid #f0f0f0;
		box-sizing: border-box;
		background: #fff;
}
.select_module {
		flex: 1;
		height: 100%;
}
.select_item {
		font-size: 26rpx;
		color: #333;
		height: 90rpx;
		text-align: center;
		line-height: 90rpx;
		margin-top: -2rpx;
		border-top: 1rpx solid #f0f0f0;
		border-right: 1rpx solid #f0f0f0;
		border-bottom: 1rpx solid #f0f0f0;
}
.select_active {
		background: #f8f8f8;
		color: #26c165;
}
.qd_guize{width:100%;margin:30rpx 0 20rpx 0;}
.qd_guize .gztitle{width:100%;text-align:center;font-size:32rpx;color:#656565;font-weight:bold;height:100rpx;line-height:100rpx}
.guize_txt{box-sizing: border-box;padding:0 30rpx;line-height:42rpx;}
.form-btn{margin-top:60rpx;width:100%;height:96rpx;line-height:96rpx;color:#fff;font-size:30rpx;border-radius:8rpx;}
.xieyi-item{display:flex;align-items:center;margin-top:30rpx}
.xieyi-item{font-size:24rpx;color:#B2B5BE}
.xieyi-item .checkbox{-webkit-transform: scale(0.6);transform: scale(0.6);}
.xieyibox{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)}
.xieyibox-content{width:90%;margin:0 auto;height:80%;margin-top:20%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px}

