<block wx:if="{{isload}}"><view class="container"><block wx:if="{{info.banner}}"><image style="width:100%;height:auto;display:table;" src="{{info.banner}}" mode="widthFix"></image></block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="content" style="{{'background:'+('linear-gradient(180deg,'+info.bgcolor+' 0%,rgba('+info.bgcolorrgb.red+','+info.bgcolorrgb.green+','+info.bgcolorrgb.blue+',0) 100%)')+';'}}"><view class="form"><image class="form_price" src="{{pre_url+'/static/img/renovation_calculator/form_price.gif'}}" mode="widthFix" alt></image><view class="form_option"><text>客厅 ?? 元</text><text>厨房 ?? 元</text><text>卧室 ?? 元</text></view><view><view class="form_item" style="padding:10rpx 10rpx;"><uni-data-picker style="width:100%;" vue-id="6a12512c-1" localdata="{{items}}" border="{{false}}" placeholder="{{regiondata||'请选择地区'}}" data-event-opts="{{[['^change',[['regionchange']]]]}}" bind:change="__e" bind:__l="__l"></uni-data-picker></view><view class="form_item"><input class="form_input" type="number" value="90" name="mianji"/><text>㎡</text></view></view><button class="form-btn" style="{{'background:'+('linear-gradient(180deg,'+info.bgcolor+' 0%,rgba('+info.bgcolorrgb.red+','+info.bgcolorrgb.green+','+info.bgcolorrgb.blue+',0.8) 100%)')+';'}}" form-type="submit">立即估算报价</button><block wx:if="{{info.xystatus==1}}"><view class="xieyi-item"><checkbox-group data-event-opts="{{[['change',[['isagreeChange',['$event']]]]]}}" bindchange="__e"><label class="flex-y-center"><checkbox class="checkbox" value="1" checked="{{isagree}}"></checkbox>我已阅读并同意</label></checkbox-group><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" style="{{'color:'+(info.bgcolor)+';'}}" bindtap="__e">《用户使用协议》</text></view></block></view></view><block wx:if="{{showxieyi}}"><view class="xieyibox"><view class="xieyibox-content"><view style="overflow:scroll;height:100%;"><parse vue-id="6a12512c-2" content="{{info.xieyi}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view><view data-event-opts="{{[['tap',[['hidexieyi',['$event']]]]]}}" style="{{'position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center;width:50%;height:60rpx;line-height:60rpx;color:#fff;border-radius:8rpx;'+('background:'+('linear-gradient(90deg,'+info.bgcolor+' 0%,rgba('+info.bgcolorrgb.red+','+info.bgcolorrgb.green+','+info.bgcolorrgb.blue+',0.8) 100%)')+';')}}" bindtap="__e">已阅读并同意</view></view></view></block></form><view class="qd_guize"><view class="guize_txt"><parse vue-id="6a12512c-3" content="{{info.description}}" bind:__l="__l"></parse></view></view></view></block>