<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{express_com=='同城配送'}}"><block><block wx:if="{{psorder.status!=0&&psorder.status!=4}}"><map class="map" longitude="{{binfo.longitude}}" latitude="{{binfo.latitude}}" scale="14" markers="{{[{id:0,latitude:binfo.latitude,longitude:binfo.longitude,iconPath:pre_url+'/static/img/peisong/marker_business.png',width:'44',height:'54'},{id:0,latitude:orderinfo.latitude,longitude:orderinfo.longitude,iconPath:pre_url+'/static/img/peisong/marker_kehu.png',width:'44',height:'54'},{id:0,latitude:psuser.latitude,longitude:psuser.longitude,iconPath:pre_url+'/static/img/peisong/marker_qishou.png',width:'44',height:'54'}]}}"></map></block><block wx:else><map class="map" longitude="{{binfo.longitude}}" latitude="{{binfo.latitude}}" scale="14" markers="{{[{id:0,latitude:binfo.latitude,longitude:binfo.longitude,iconPath:pre_url+'/static/img/peisong/marker_business.png',width:'44',height:'54'},{id:0,latitude:orderinfo.latitude,longitude:orderinfo.longitude,iconPath:pre_url+'/static/img/peisong/marker_kehu.png',width:'44',height:'54'}]}}"></map></block><view class="order-box"><view class="head"><block wx:if="{{type=='express_wx'}}"><block><block wx:if="{{psorder.order_status==101}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>分配骑手</view></block><block wx:else><block wx:if="{{psorder.order_status==102}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>骑手赶往店家</view></block><block wx:else><block wx:if="{{psorder.order_status==201}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>骑手到店</view></block><block wx:else><block wx:if="{{psorder.order_status==202}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>取货成功</view></block><block wx:else><block wx:if="{{psorder.order_status==301}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>配送中</view></block><block wx:else><block wx:if="{{psorder.order_status==302}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>已送达</view></block><block wx:else><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>配送中</view></block></block></block></block></block></block></block></block><block wx:else><block wx:if="{{psorder.psid==-2}}"><block><block wx:if="{{psorder.status==0}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>待接单</view></block><block wx:else><block wx:if="{{psorder.status==1}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>已接单</view></block><block wx:else><block wx:if="{{psorder.status==2}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>已到店</view></block><block wx:else><block wx:if="{{psorder.status==3}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>配送中</view></block><block wx:else><block wx:if="{{psorder.status==4}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>已完成</view></block><block wx:else><block wx:if="{{psorder.status==10}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>已取消</view></block></block></block></block></block></block></block></block><block wx:else><block><block wx:if="{{psorder.status==4}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>已送达</view></block><block wx:else><block wx:if="{{psorder.leftminute>0}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image><text class="t1">{{psorder.leftminute+"分钟内"}}</text>送达</view></block><block wx:else><block wx:if="{{psorder.yujitime>0}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>已超时<text class="t1" style="margin-left:10rpx;">{{-psorder.leftminute+"分钟"}}</text></view></block><block wx:else><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>配送中</view></block></block></block></block></block></block><view class="flex1"></view><view class="f2"><text class="t1">{{orderinfo.freight_price}}</text>元</view></view><view class="content" style="border-bottom:0;"><block wx:if="{{type=='express_wx'}}"><block><block wx:if="{{psuser.latitude}}"><view class="f1"><view class="t1"><text class="x1">{{psorder.juli}}</text><text class="x2">{{psorder.juli_unit}}</text></view><view class="t2"><image class="img" src="{{pre_url+'/static/img/peisong/ps_juli.png'}}"></image></view><view class="t3"><text class="x1">{{psorder.juli2}}</text><text class="x2">{{psorder.juli2_unit}}</text></view></view></block></block></block><block wx:else><block wx:if="{{psorder.psid==-2}}"><block><view class="f1"><view class="t1"><text class="x1">{{psorder.juli}}</text><text class="x2">{{psorder.juli_unit}}</text></view><view class="t2"><image class="img" src="{{pre_url+'/static/img/peisong/ps_juli.png'}}"></image></view><view class="t3"><text class="x1">{{psorder.juli2}}</text><text class="x2">{{psorder.juli2_unit}}</text></view></view></block></block><block wx:else><block><view class="f1"><view class="t1"><text class="x1">{{psorder.juli}}</text><text class="x2">{{psorder.juli_unit}}</text></view><view class="t2"><image class="img" src="{{pre_url+'/static/img/peisong/ps_juli.png'}}"></image></view><view class="t3"><text class="x1">{{psorder.juli2}}</text><text class="x2">{{psorder.juli2_unit}}</text></view></view></block></block></block><view class="f2"><view class="t1">{{binfo.name}}</view><view class="t2">{{binfo.address}}</view><view class="t3">{{orderinfo.address}}</view></view><view data-event-opts="{{[['tap',[['daohang',['$event']]]]]}}" class="f3" catchtap="__e"><image class="img" src="{{pre_url+'/static/img/peisong/ps_daohang.png'}}"></image></view></view></view><view class="orderinfo"><view class="box-title">{{"商品清单("+orderinfo.procount+")"}}</view><block wx:for="{{prolist}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="item"><block wx:if="{{psorder.type!='paotui_order'}}"><block><text class="t1 flex1">{{item.name+" "+item.ggname}}</text><text class="t2 flex0">{{"￥"+item.sell_price+" ×"+item.num+''}}</text></block></block><block wx:else><block><text class="t1 flex1">{{item.name+''}}</text><text class="t2 flex0">{{'x'+item.num+''}}</text></block></block></view></block></view><block wx:if="{{psorder.status!=0}}"><view class="orderinfo"><view class="box-title">配送信息</view><block wx:if="{{psuser.realname}}"><view class="item"><text class="t1">配送员</text><text class="t2"><text style="font-weight:bold;">{{psuser.realname}}</text>{{"("+psuser.tel+")"}}</text></view></block><block wx:if="{{psorder.starttime}}"><view class="item"><text class="t1">接单时间</text><text class="t2">{{$root.m0}}</text></view></block><block wx:if="{{psorder.daodiantime}}"><view class="item"><text class="t1">到店时间</text><text class="t2">{{$root.m1}}</text></view></block><block wx:if="{{psorder.quhuotime}}"><view class="item"><text class="t1">取货时间</text><text class="t2">{{$root.m2}}</text></view></block><block wx:if="{{psorder.endtime}}"><view class="item"><text class="t1">送达时间</text><text class="t2">{{$root.m3}}</text></view></block></view></block><view class="orderinfo"><view class="box-title">订单信息</view><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{orderinfo.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{$root.m4}}</text></view><view class="item"><text class="t1">支付时间</text><text class="t2">{{$root.m5}}</text></view><view class="item"><text class="t1">支付方式</text><text class="t2">{{orderinfo.paytype}}</text></view><block wx:if="{{orderinfo.expect_take_time}}"><view class="item"><text class="t1">取件时间</text><text class="t2">{{orderinfo.expect_take_time}}</text></view></block><block wx:if="{{orderinfo.pic}}"><view class="item"><text class="t1">物品图片</text><view class="t2"><image style="width:400rpx;height:auto;" src="{{orderinfo.pic}}" mode="widthFix" data-url="{{orderinfo.pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{psorder.type!='paotui_order'}}"><view class="item"><text class="t1">商品金额</text><text class="t2 red">{{"¥"+orderinfo.product_price}}</text></view></block><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+orderinfo.totalprice}}</text></view><view class="item"><text class="t1">备注</text><text class="t2 red">{{orderinfo.message?orderinfo.message:'无'}}</text></view></view><view style="width:100%;height:120rpx;"></view><view class="bottom"><block wx:if="{{psorder.status!=0&&psuser.tel}}"><view class="f1" data-tel="{{psuser.tel}}" data-event-opts="{{[['tap',[['call',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/peisong/tel1.png'}}"></image>联系配送员</view></block><block wx:if="{{psorder.type!='paotui_order'}}"><block><block wx:if="{{psorder.status!=0}}"><view class="f2" data-tel="{{binfo.tel}}" data-event-opts="{{[['tap',[['call',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/peisong/tel2.png'}}"></image>联系商家</view></block></block></block><block wx:else><block><block wx:if="{{psorder.status!=0}}"><view class="f2" data-tel="{{shop_tel}}" data-event-opts="{{[['tap',[['call',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/peisong/tel2.png'}}"></image>联系商家</view></block></block></block><block wx:if="{{psorder.psid>0&&psorder.status==4}}"><view class="btn1" data-url="{{'commentps?id='+psorder.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">评价配送员</view></block></view></block></block><block wx:else><block wx:if="{{express_com=='货运托运'}}"><block><view class="orderinfo"><block wx:if="{{datalist.pic}}"><block><view class="item"><text class="t1">物流单照片</text></view><view class="item"><image class="t2" src="{{datalist.pic}}" data-url="{{datalist.pic}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block></block><block wx:if="{{datalist.fhname}}"><view class="item"><text class="t1">发货人信息</text><text class="t2">{{datalist.fhname}}</text></view></block><block wx:if="{{datalist.fhaddress}}"><view class="item"><text class="t1">发货地址</text><text class="t2">{{datalist.fhaddress}}</text></view></block><block wx:if="{{datalist.shname}}"><view class="item"><text class="t1">收货人信息</text><text class="t2">{{datalist.shname}}</text></view></block><block wx:if="{{datalist.shaddress}}"><view class="item"><text class="t1">收货地址</text><text class="t2">{{datalist.shaddress}}</text></view></block><block wx:if="{{datalist.remark}}"><view class="item"><text class="t1">备注</text><text class="t2">{{datalist.remark}}</text></view></block></view></block></block><block wx:else><block><view class="expressinfo"><view class="head"><view class="f1"><image src="{{pre_url+'/static/img/feiji.png'}}"></image></view><view class="f2"><view class="t1">快递公司：<text style="color:#333;" user-select="true" selectable="true">{{express_com}}</text></view><view class="t2">快递单号：<text style="color:#333;" user-select="true" selectable="true">{{express_no}}</text></view></view></view><view class="content"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['item '+(index==0?'on':'')]}}"><view class="f1"><image src="{{'/static/img/dot'+(index==0?'2':'1')+'.png'}}"></image></view><view class="f2"><text class="t2">{{item.time}}</text><text class="t1">{{item.context}}</text></view></view></block><block wx:if="{{nodata}}"><nodata vue-id="fdcf2210-1" text="暂未查找到物流信息" bind:__l="__l"></nodata></block></view></view></block></block></block></block></block><block wx:if="{{loading}}"><loading vue-id="fdcf2210-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="fdcf2210-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="fdcf2210-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>