require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pagesExt/order/detail"],{

/***/ 1079:
/*!*********************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/main.js?{"page":"pagesExt%2Forder%2Fdetail"} ***!
  \*********************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _detail = _interopRequireDefault(__webpack_require__(/*! ./pagesExt/order/detail.vue */ 1080));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_detail.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 1080:
/*!**************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/pagesExt/order/detail.vue ***!
  \**************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _detail_vue_vue_type_template_id_c6eb16b4___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./detail.vue?vue&type=template&id=c6eb16b4& */ 1081);
/* harmony import */ var _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./detail.vue?vue&type=script&lang=js& */ 1083);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _detail_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./detail.vue?vue&type=style&index=0&lang=css& */ 1085);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 36);

var renderjs





/* normalize component */

var component = Object(_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _detail_vue_vue_type_template_id_c6eb16b4___WEBPACK_IMPORTED_MODULE_0__["render"],
  _detail_vue_vue_type_template_id_c6eb16b4___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _detail_vue_vue_type_template_id_c6eb16b4___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pagesExt/order/detail.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 1081:
/*!*********************************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/pagesExt/order/detail.vue?vue&type=template&id=c6eb16b4& ***!
  \*********************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_c6eb16b4___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=c6eb16b4& */ 1082);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_c6eb16b4___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_c6eb16b4___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_c6eb16b4___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_c6eb16b4___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 1082:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!/Users/<USER>/Downloads/tcphp/uniapp/pagesExt/order/detail.vue?vue&type=template&id=c6eb16b4& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniPopup: function () {
      return Promise.all(/*! import() | components/uni-popup/uni-popup */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/uni-popup/uni-popup")]).then(__webpack_require__.bind(null, /*! @/components/uni-popup/uni-popup.vue */ 7096))
    },
    parse: function () {
      return Promise.all(/*! import() | components/parse/parse */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/parse/parse")]).then(__webpack_require__.bind(null, /*! @/components/parse/parse.vue */ 7152))
    },
    loading: function () {
      return __webpack_require__.e(/*! import() | components/loading/loading */ "components/loading/loading").then(__webpack_require__.bind(null, /*! @/components/loading/loading.vue */ 7131))
    },
    dpTabbar: function () {
      return __webpack_require__.e(/*! import() | components/dp-tabbar/dp-tabbar */ "components/dp-tabbar/dp-tabbar").then(__webpack_require__.bind(null, /*! @/components/dp-tabbar/dp-tabbar.vue */ 7110))
    },
    popmsg: function () {
      return __webpack_require__.e(/*! import() | components/popmsg/popmsg */ "components/popmsg/popmsg").then(__webpack_require__.bind(null, /*! @/components/popmsg/popmsg.vue */ 7124))
    },
    wxxieyi: function () {
      return __webpack_require__.e(/*! import() | components/wxxieyi/wxxieyi */ "components/wxxieyi/wxxieyi").then(__webpack_require__.bind(null, /*! @/components/wxxieyi/wxxieyi.vue */ 7138))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 =
    _vm.isload &&
    _vm.detail.status == 0 &&
    _vm.detail.paytypeid == 5 &&
    _vm.detail.transfer_check == 1
      ? _vm.t("转账汇款")
      : null
  var m1 =
    _vm.isload &&
    _vm.detail.status == 0 &&
    _vm.detail.paytypeid == 5 &&
    _vm.detail.transfer_check == 0
      ? _vm.t("转账汇款")
      : null
  var m2 =
    _vm.isload &&
    _vm.detail.status == 0 &&
    _vm.detail.paytypeid == 5 &&
    _vm.detail.transfer_check == -1
      ? _vm.t("转账汇款")
      : null
  var g0 =
    _vm.isload && _vm.detail.is_pingce != 1 && _vm.detail.mdid == -1
      ? _vm.storeshowall == false && _vm.storelist.length > 5
      : null
  var g1 =
    _vm.isload &&
    _vm.detail.is_pingce != 1 &&
    !(_vm.detail.mdid == -1) &&
    _vm.mendian_no_select == 1
      ? _vm.storeshowall == false && _vm.mendianArr.length > 5
      : null
  var m3 =
    _vm.isload &&
    _vm.detail.is_pingce != 1 &&
    !(_vm.detail.mdid == -1) &&
    !(_vm.mendian_no_select == 1) &&
    _vm.detail.freight_type == 1
      ? _vm.isNull(_vm.storeinfo)
      : null
  var l0 = _vm.isload
    ? _vm.__map(_vm.prolist, function (item, idx) {
        var $orig = _vm.__get_orig(item)
        var m4 = !(item.product_type && item.product_type == 2)
          ? !_vm.isNull(item.service_fee) && item.service_fee > 0
          : null
        var m5 =
          !(item.product_type && item.product_type == 2) && m4
            ? _vm.t("服务费")
            : null
        return {
          $orig: $orig,
          m4: m4,
          m5: m5,
        }
      })
    : null
  var m6 = _vm.isload
    ? !_vm.isNull(_vm.detail.service_fee) && _vm.detail.service_fee > 0
    : null
  var m7 = _vm.isload && m6 ? _vm.t("服务费") : null
  var m8 = _vm.isload && _vm.detail.leveldk_money > 0 ? _vm.t("会员") : null
  var m9 = _vm.isload && _vm.detail.weight_price > 0 ? _vm.t("包装费") : null
  var m10 = _vm.isload && _vm.detail.coupon_money > 0 ? _vm.t("优惠券") : null
  var m11 = _vm.isload && _vm.detail.scoredk_money > 0 ? _vm.t("积分") : null
  var m12 = _vm.isload && _vm.detail.dec_money > 0 ? _vm.t("余额") : null
  var m13 =
    _vm.isload && _vm.detail.silvermoneydec && _vm.detail.silvermoneydec > 0
      ? _vm.t("银值")
      : null
  var m14 =
    _vm.isload && _vm.detail.goldmoneydec && _vm.detail.goldmoneydec > 0
      ? _vm.t("金值")
      : null
  var m15 =
    _vm.isload && _vm.detail.shopscoredk_money > 0 ? _vm.t("产品积分") : null
  var m16 = _vm.isload
    ? !_vm.isNull(_vm.detail.service_fee_money) &&
      _vm.detail.service_fee_money > 0
    : null
  var m17 = _vm.isload && m16 ? _vm.t("服务费") : null
  var m18 =
    _vm.isload && _vm.detail.combine_money && _vm.detail.combine_money > 0
      ? _vm.t("余额")
      : null
  var m19 = _vm.isload && _vm.detail.is_yuanbao_pay == 1 ? _vm.t("元宝") : null
  var g2 = _vm.isload ? _vm.detail.formdata.length : null
  var l1 =
    _vm.isload && _vm.show_product_xieyi && _vm.product_xieyi
      ? _vm.__map(_vm.product_xieyi, function (xieyi_item, xieyi_index) {
          var $orig = _vm.__get_orig(xieyi_item)
          var m20 = _vm.t("color1")
          return {
            $orig: $orig,
            m20: m20,
          }
        })
      : null
  var m21 =
    _vm.isload &&
    _vm.fromfenxiao == 0 &&
    _vm.detail.status == 0 &&
    _vm.detail.paytypeid != 5
      ? _vm.t("color1")
      : null
  var m22 =
    _vm.isload &&
    _vm.fromfenxiao == 0 &&
    _vm.detail.status == 0 &&
    _vm.detail.paytypeid == 5 &&
    _vm.detail.transfer_check == 1
      ? _vm.t("color1")
      : null
  var m23 =
    _vm.isload &&
    _vm.fromfenxiao == 0 &&
    _vm.detail.status == 0 &&
    _vm.detail.paytypeid == 5 &&
    _vm.detail.transfer_check == 0
      ? _vm.t("color1")
      : null
  var m24 =
    _vm.isload &&
    _vm.fromfenxiao == 0 &&
    _vm.detail.status == 0 &&
    _vm.detail.paytypeid == 5 &&
    _vm.detail.transfer_check == -1
      ? _vm.t("color1")
      : null
  var g3 =
    _vm.isload && _vm.fromfenxiao == 0
      ? [1, 2, 3].includes(_vm.detail.status) && _vm.invoice
      : null
  var m25 =
    _vm.isload &&
    _vm.fromfenxiao == 0 &&
    _vm.detail.is_pingce == 1 &&
    (_vm.detail.status == 1 ||
      _vm.detail.status == 2 ||
      _vm.detail.status == 3) &&
    _vm.detail.pingce_status == 2
      ? _vm.t("color1")
      : null
  var m26 =
    _vm.isload &&
    _vm.fromfenxiao == 0 &&
    _vm.detail.is_pingce == 1 &&
    (_vm.detail.status == 1 ||
      _vm.detail.status == 2 ||
      _vm.detail.status == 3) &&
    !(_vm.detail.pingce_status == 2)
      ? _vm.t("color1")
      : null
  var m27 =
    _vm.isload &&
    _vm.fromfenxiao == 0 &&
    _vm.detail.status == 2 &&
    _vm.detail.can_collect &&
    _vm.detail.paytypeid != "4" &&
    (_vm.detail.balance_pay_status == 1 || _vm.detail.balance_price == 0)
      ? _vm.t("color1")
      : null
  var m28 =
    _vm.isload &&
    _vm.fromfenxiao == 0 &&
    _vm.detail.status == 2 &&
    _vm.detail.balance_pay_status == 0 &&
    _vm.detail.balance_price > 0
      ? _vm.t("color1")
      : null
  var m29 =
    _vm.isload &&
    _vm.fromfenxiao == 0 &&
    _vm.detail.bid > 0 &&
    _vm.detail.status == 3 &&
    _vm.iscommentdp == 0
      ? _vm.t("color1")
      : null
  var m30 = _vm.isload && _vm.detail.hexiao_code_member ? _vm.t("color1") : null
  var l2 =
    _vm.isload && _vm.selecthxnumDialogShow
      ? _vm.__map(_vm.hxnumlist, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m31 = _vm.hxnum == item ? _vm.t("color1") : null
          return {
            $orig: $orig,
            m31: m31,
          }
        })
      : null
  var m32 = _vm.isload && _vm.showproxieyi ? _vm.t("color1") : null
  var m33 = _vm.isload && _vm.showproxieyi ? _vm.t("color1rgb") : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        m2: m2,
        g0: g0,
        g1: g1,
        m3: m3,
        l0: l0,
        m6: m6,
        m7: m7,
        m8: m8,
        m9: m9,
        m10: m10,
        m11: m11,
        m12: m12,
        m13: m13,
        m14: m14,
        m15: m15,
        m16: m16,
        m17: m17,
        m18: m18,
        m19: m19,
        g2: g2,
        l1: l1,
        m21: m21,
        m22: m22,
        m23: m23,
        m24: m24,
        g3: g3,
        m25: m25,
        m26: m26,
        m27: m27,
        m28: m28,
        m29: m29,
        m30: m30,
        l2: l2,
        m32: m32,
        m33: m33,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 1083:
/*!***************************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/pagesExt/order/detail.vue?vue&type=script&lang=js& ***!
  \***************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js& */ 1084);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1084:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!/Users/<USER>/Downloads/tcphp/uniapp/pagesExt/order/detail.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, uni) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

var app = getApp();
var interval = null;
var _default = {
  data: function data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      pre_url: app.globalData.pre_url,
      prodata: '',
      djs: '',
      iscommentdp: "",
      detail: "",
      payorder: {},
      prolist: "",
      shopset: "",
      storeinfo: "",
      lefttime: "",
      codtxt: "",
      pay_transfer_info: {},
      invoice: 0,
      selectExpressShow: false,
      express_content: '',
      fromfenxiao: 0,
      hexiao_code_member: '',
      showprice_dollar: false,
      hexiao_qr: '',
      selecthxnumDialogShow: false,
      hxogid: '',
      hxnum: '',
      hxnumlist: [],
      storelist: [],
      storeshowall: false,
      mendian_no_select: 0,
      mendianArr: [],
      detail_content: '',
      email: '',
      show_product_xieyi: 0,
      //是否展示产品协议
      product_xieyi: [],
      //产品协议列表
      showproxieyi: 0,
      //是否展示产品协议弹窗
      proxieyi_content: '',
      //产品协议内容
      reportShow: false,
      //测评报告
      reportArr: {} //报告列表
    };
  },

  onLoad: function onLoad(opt) {
    this.opt = app.getopts(opt);
    if (this.opt && this.opt.fromfenxiao && this.opt.fromfenxiao == '1') {
      this.fromfenxiao = 1;
    }
    this.getdata();
  },
  onPullDownRefresh: function onPullDownRefresh() {
    this.getdata();
  },
  onUnload: function onUnload() {
    clearInterval(interval);
  },
  onShareAppMessage: function onShareAppMessage() {
    var that = this;
    if (that.detail.usegiveorder && that.detail.usegiveorder == 1) {
      return {
        title: that.detail.giveordertitle,
        path: '/pagesC/shop/takegiveorder?payorderid=' + that.detail.payorderid,
        imageUrl: that.detail.giveorderpic,
        desc: that.detail.giveordertitle
      };
    }
    return this._sharewx();
  },
  onShareTimeline: function onShareTimeline() {
    var that = this;
    if (that.detail.usegiveorder && that.detail.usegiveorder == 1) {
      return {
        title: that.detail.giveordertitle,
        path: '/pagesC/shop/takegiveorder?payorderid=' + that.detail.payorderid,
        imageUrl: that.detail.giveorderpic,
        desc: that.detail.giveordertitle
      };
    }
    var sharewxdata = this._sharewx();
    var query = sharewxdata.path.split('?')[1] + '&seetype=circle';
    return {
      title: sharewxdata.title,
      imageUrl: sharewxdata.imageUrl,
      query: query
    };
  },
  methods: {
    getdata: function getdata() {
      var that = this;
      that.loading = true;
      app.get('ApiOrder/detail', {
        id: that.opt.id,
        channel: that.opt.channel
      }, function (res) {
        that.loading = false;
        if (res.status == 1) {
          that.iscommentdp = res.iscommentdp, that.detail = res.detail;
          // 判断当前值存不存在					
          if (res.detail.pingce) {
            that.detail.pingce = JSON.parse(res.detail.pingce);
          }
          that.prolist = res.prolist;
          that.shopset = res.shopset;
          that.storeinfo = res.storeinfo;
          that.lefttime = res.lefttime;
          that.codtxt = res.codtxt;
          that.pay_transfer_info = res.pay_transfer_info;
          that.payorder = res.payorder;
          that.invoice = res.invoice;
          that.storelist = res.storelist || [];
          that.showprice_dollar = res.showprice_dollar;
          if (res.lefttime > 0) {
            interval = setInterval(function () {
              that.lefttime = that.lefttime - 1;
              that.getdjs();
            }, 1000);
          }
          that.mendian_no_select = res.mendian_no_select;
          that.mendianArr = res.mendianArr;
          that.show_product_xieyi = res.show_product_xieyi;
          that.product_xieyi = res.product_xieyi;
          that.loaded();

          //订单赠好友
          if (that.detail.usegiveorder && that.detail.usegiveorder == 1) {
            that._sharemp({
              title: that.detail.giveordertitle,
              desc: that.detail.giveordertitle,
              link: app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#/pagesC/shop/takegiveorder?payorderid=' + that.detail.payorderid,
              pic: that.detail.giveorderpic
            });
          }
          that.detail_content = res.detail.freight_content;
          if (that.detail.mdid == -1 && that.storelist) {
            app.getLocation(function (res) {
              var latitude = res.latitude;
              var longitude = res.longitude;
              that.latitude = latitude;
              that.longitude = longitude;
              var storelist = that.storelist;
              for (var x in storelist) {
                if (latitude && longitude && storelist[x].latitude && storelist[x].longitude) {
                  var juli = that.getDistance(latitude, longitude, storelist[x].latitude, storelist[x].longitude);
                  storelist[x].juli = juli;
                }
              }
              storelist.sort(function (a, b) {
                return a["juli"] - b["juli"];
              });
              for (var x in storelist) {
                if (storelist[x].juli) {
                  storelist[x].juli = '距离' + storelist[x].juli + '千米';
                }
              }
              that.storelist = storelist;
            });
          }
        } else {
          if (res.msg) {
            app.alert(res.msg, function () {
              if (res.url) app.goto(res.url);
            });
          } else if (res.url) {
            app.goto(res.url);
          } else {
            app.alert('您无查看权限');
          }
        }
      });
    },
    set_hexiao_code_member: function set_hexiao_code_member(e) {
      this.hexiao_code_member = e.detail.value;
    },
    hexiao: function hexiao() {
      var that = this;
      that.loading = true;
      app.post('ApiOrder/hexiao', {
        orderid: that.opt.id,
        hexiao_code_member: that.hexiao_code_member
      }, function (res) {
        that.loading = false;
        if (res.status != 1) {
          app.error(res.msg);
          return;
        }
        app.success(res.msg);
        that.closeHxqr();
        setTimeout(function () {
          that.getdata();
        }, 1000);
      });
    },
    getdjs: function getdjs() {
      var that = this;
      var totalsec = that.lefttime;
      if (totalsec <= 0) {
        that.djs = '00时00分00秒';
      } else {
        var houer = Math.floor(totalsec / 3600);
        var min = Math.floor((totalsec - houer * 3600) / 60);
        var sec = totalsec - houer * 3600 - min * 60;
        var djs = (houer < 10 ? '0' : '') + houer + '时' + (min < 10 ? '0' : '') + min + '分' + (sec < 10 ? '0' : '') + sec + '秒';
        that.djs = djs;
      }
    },
    todel: function todel(e) {
      var that = this;
      var orderid = e.currentTarget.dataset.id;
      app.confirm('确定要删除该订单吗?', function () {
        app.showLoading('删除中');
        app.post('ApiOrder/delOrder', {
          orderid: orderid
        }, function (data) {
          app.showLoading(false);
          app.success(data.msg);
          setTimeout(function () {
            app.goback(true);
          }, 1000);
        });
      });
    },
    toclose: function toclose(e) {
      var that = this;
      var orderid = e.currentTarget.dataset.id;
      app.confirm('确定要关闭该订单吗?', function () {
        app.showLoading('提交中');
        app.post('ApiOrder/closeOrder', {
          orderid: orderid
        }, function (data) {
          app.showLoading(false);
          app.success(data.msg);
          setTimeout(function () {
            that.getdata();
          }, 1000);
        });
      });
    },
    //转单上级审核
    transferOrder: function transferOrder(e) {
      var orderid = e.currentTarget.dataset.orderid;
      app.showLoading();
      app.post('ApiTransferOrderParentCheck/transferOrder', {
        id: orderid
      }, function (data) {
        app.showLoading(false);
        if (data.status == 0) {
          app.error(data.msg);
        } else {
          app.alert(data.msg);
          setTimeout(function () {
            this.getdata();
          }, 1000);
        }
      });
    },
    //查看评测状态和跳转链接
    toevaluate: function toevaluate(e) {
      app.showLoading();
      var that = this;
      var orderid = e.currentTarget.dataset.id;
      app.post('ApiOrder/pingceOrder', {
        id: orderid
      }, function (data) {
        app.showLoading(false);
        if (data.status == 1) app.goto(data.url);else app.error(data.msg);
      });
    },
    //查看评测报告
    viewReport: function viewReport(e) {
      this.reportShow = !this.reportShow;
      app.showLoading();
      var that = this;
      var orderid = e.currentTarget.dataset.id;
      app.post('ApiOrder/pingceOrder', {
        id: orderid
      }, function (data) {
        app.showLoading(false);
        if (data.status == 2) that.reportArr = data.report_arr;else app.error(data.msg);
      });
    },
    changeReportDialog: function changeReportDialog() {
      this.reportShow = !this.reportShow;
    },
    orderCollect: function orderCollect(e) {
      var that = this;
      var orderid = e.currentTarget.dataset.id;
      app.confirm('确定要收货吗?', function () {
        app.showLoading('收货中');
        if (app.globalData.platform == 'wx' && that.detail.wxpaylog && that.detail.wxpaylog.is_upload_shipping_info == 1) {
          app.post('ApiOrder/orderCollectBefore', {
            orderid: orderid
          }, function (data) {
            app.showLoading(false);
            if (data.status != 1) {
              app.error(data.msg);
              return;
            } else {
              if (wx.openBusinessView) {
                wx.openBusinessView({
                  businessType: 'weappOrderConfirm',
                  extraData: {
                    merchant_id: that.detail.wxpaylog.mch_id,
                    merchant_trade_no: that.detail.wxpaylog.ordernum,
                    transaction_id: that.detail.wxpaylog.transaction_id
                  },
                  success: function success(res) {
                    //dosomething
                    console.log('openBusinessView success');
                    console.log(res);
                    app.post('ApiOrder/orderCollect', {
                      orderid: orderid
                    }, function (data2) {
                      app.showLoading(false);
                      app.success(data2.msg);
                      setTimeout(function () {
                        that.getdata();
                      }, 1000);
                    });
                  },
                  fail: function fail(err) {
                    //dosomething
                    console.log('openBusinessView fail');
                    console.log(err);
                  },
                  complete: function complete() {
                    //dosomething
                  }
                });
              } else {
                //引导用户升级微信版本
                app.error('请升级微信版本');
                console.log('openBusinessView error');
              }
            }
          });
        } else {
          app.post('ApiOrder/orderCollect', {
            orderid: orderid
          }, function (data) {
            app.showLoading(false);
            app.success(data.msg);
            setTimeout(function () {
              that.getdata();
            }, 1000);
          });
        }
      });
    },
    showhxqr: function showhxqr(e) {
      this.hexiao_qr = e.currentTarget.dataset.hexiao_qr;
      this.$refs.dialogHxqr.open();
    },
    closeHxqr: function closeHxqr() {
      this.$refs.dialogHxqr.close();
    },
    showhxqr2: function showhxqr2(e) {
      var that = this;
      var leftnum = e.currentTarget.dataset.num - e.currentTarget.dataset.hxnum;
      this.hxogid = e.currentTarget.dataset.id;
      if (leftnum <= 0) {
        app.alert('没有剩余核销数量了');
        return;
      }
      var hxnumlist = [];
      for (var i = 0; i < leftnum; i++) {
        hxnumlist.push(i + 1 + '');
      }
      if (hxnumlist.length > 6) {
        that.hxnumlist = hxnumlist;
        that.selecthxnumDialogShow = true;
        that.hxnum = '';
      } else {
        uni.showActionSheet({
          itemList: hxnumlist,
          success: function success(res) {
            if (res.tapIndex >= 0) {
              that.hxnum = hxnumlist[res.tapIndex];
              that.gethxqr();
            }
          }
        });
      }
    },
    gethxqr: function gethxqr() {
      var that = this;
      var hxnum = this.hxnum;
      var hxogid = this.hxogid;
      if (!hxogid) {
        app.alert('请选择要核销的商品');
        return;
      }
      if (!hxnum) {
        app.alert('请选择核销数量');
        return;
      }
      app.showLoading();
      app.post('ApiOrder/getproducthxqr', {
        hxogid: hxogid,
        hxnum: hxnum
      }, function (data) {
        app.showLoading(false);
        if (data.status == 0) {
          app.alert(data.msg);
        } else {
          that.hexiao_qr = data.hexiao_qr;
          that.$refs.dialogHxqr.open();
        }
      });
    },
    hxnumRadioChange: function hxnumRadioChange(e) {
      var that = this;
      var index = e.currentTarget.dataset.index;
      this.hxnum = this.hxnumlist[index];
      setTimeout(function () {
        that.selecthxnumDialogShow = false;
        that.gethxqr();
      }, 200);
    },
    hideSelecthxnumDialog: function hideSelecthxnumDialog() {
      this.selecthxnumDialogShow = false;
    },
    openLocation: function openLocation(e) {
      var latitude = parseFloat(e.currentTarget.dataset.latitude);
      var longitude = parseFloat(e.currentTarget.dataset.longitude);
      var address = e.currentTarget.dataset.address;
      uni.openLocation({
        latitude: latitude,
        longitude: longitude,
        name: address,
        address: address,
        scale: 13
      });
    },
    openMendian: function openMendian(e) {
      var storeinfo = e.currentTarget.dataset.storeinfo;
      app.goto('/pages/shop/mendian?id=' + storeinfo.id);
    },
    logistics: function logistics(e) {
      var express_com = e.currentTarget.dataset.express_com;
      var express_no = e.currentTarget.dataset.express_no;
      var express_content = e.currentTarget.dataset.express_content;
      var express_type = e.currentTarget.dataset.express_type;
      var prolist = this.prolist;
      console.log(express_content);
      if (!express_content) {
        app.goto('/pagesExt/order/logistics?express_com=' + express_com + '&express_no=' + express_no + '&type=' + express_type);
      } else {
        express_content = JSON.parse(express_content);
        for (var i in express_content) {
          if (express_content[i].express_ogids) {
            var express_ogids = express_content[i].express_ogids.split(',');
            console.log(express_ogids);
            var express_oglist = [];
            for (var j in prolist) {
              if (app.inArray(prolist[j].id + '', express_ogids)) {
                express_oglist.push(prolist[j]);
              }
            }
            express_content[i].express_oglist = express_oglist;
          }
        }
        this.express_content = express_content;
        this.$refs.dialogSelectExpress.open();
      }
    },
    hideSelectExpressDialog: function hideSelectExpressDialog() {
      this.$refs.dialogSelectExpress.close();
    },
    doStoreShowAll: function doStoreShowAll() {
      this.storeshowall = true;
    },
    handleLinkClick: function handleLinkClick(url) {
      //是否包含http或https
      var regex = /^(?:http(s)?\:)?\/\//;
      if (!regex.test(url)) {
        url = "http://".concat(url);
      }
      app.goto(url);
    },
    showproductxieyi: function showproductxieyi(xieyi_index) {
      var that = this;
      var product_xieyi = that.product_xieyi;
      that.proxieyi_content = product_xieyi[xieyi_index] ? product_xieyi[xieyi_index].content : '';
      that.showproxieyi = 1;
    },
    hideproxieyi: function hideproxieyi() {
      this.showproxieyi = 0;
    },
    giveordersharemp: function giveordersharemp() {
      if (app.globalData.platform == 'mp') {
        var msg = '复制链接成功，或点击右上角发送给好友';
      } else {
        var msg = '复制成功,快去分享吧';
      }
      var that = this;
      var shareLink = app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#/pagesC/shop/takegiveorder?scene=pid_' + app.globalData.mid + '&payorderid=' + that.detail.payorderid + '&title=领礼物：' + that.detail.giveordertitle;
      uni.setClipboardData({
        data: shareLink,
        success: function success() {
          uni.showToast({
            title: msg,
            duration: 3000,
            icon: 'none'
          });
        },
        fail: function fail(err) {
          uni.showToast({
            title: '复制失败',
            duration: 2000,
            icon: 'none'
          });
        }
      });
    },
    giveordershareapp: function giveordershareapp() {
      var that = this;
      uni.showActionSheet({
        itemList: ['发送给微信好友', '分享到微信朋友圈'],
        success: function success(res) {
          if (res.tapIndex >= 0) {
            var scene = 'WXSceneSession';
            if (res.tapIndex == 1) {
              scene = 'WXSenceTimeline';
            }
            var sharedata = {};
            sharedata.provider = 'weixin';
            sharedata.type = 0;
            sharedata.scene = scene;
            sharedata.title = that.detail.giveordertitle;
            sharedata.summary = '';
            sharedata.href = app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#/pagesC/shop/takegiveorder?scene=pid_' + app.globalData.mid + '&payorderid=' + that.detail.payorderid + '&title=领礼物：' + that.detail.giveordertitle;
            sharedata.imageUrl = that.detail.giveorderpic;
            var sharelist = app.globalData.initdata.sharelist;
            if (sharelist) {
              for (var i = 0; i < sharelist.length; i++) {
                if (sharelist[i]['indexurl'] == app.globalData.initdata.indexurl) {
                  sharedata.title = sharelist[i].title;
                  sharedata.summary = sharelist[i].desc;
                  sharedata.imageUrl = sharelist[i].pic;
                  if (sharelist[i].url) {
                    var sharelink = sharelist[i].url;
                    if (sharelink.indexOf('/') === 0) {
                      sharelink = app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#' + sharelink;
                    }
                    if (app.globalData.mid > 0) {
                      sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid=' + app.globalData.mid;
                    }
                    sharedata.href = sharelink;
                  }
                }
              }
            }
            uni.share(sharedata);
          }
        }
      });
    }
  },
  computed: {
    contentWithLinks: function contentWithLinks() {
      // 使用正则表达式匹配链接
      var urlRegex = /https?:\/\/[^\s]+|www\.[^\s]+/gi;
      var match;
      var lastIndex = 0;
      var result = [];
      if (app.isNull(this.detail_content)) {
        return this.detail_content;
      }
      if (this.detail_content) {
        // 遍历所有匹配的链接
        while ((match = urlRegex.exec(this.detail_content)) !== null) {
          // 添加文本部分
          if (match.index > lastIndex) {
            result.push({
              text: this.detail_content.substring(lastIndex, match.index)
            });
          }
          // 添加链接部分
          result.push({
            text: match[0],
            url: match[0]
          });
          lastIndex = match.index + match[0].length;
        }

        // 添加剩余的文本部分
        if (lastIndex < this.detail_content.length) {
          result.push({
            text: this.detail_content.substring(lastIndex)
          });
        }
      }
      return result;
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 1085:
/*!***********************************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/pagesExt/order/detail.vue?vue&type=style&index=0&lang=css& ***!
  \***********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=css& */ 1086);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1086:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!/Users/<USER>/Downloads/tcphp/uniapp/pagesExt/order/detail.vue?vue&type=style&index=0&lang=css& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[1079,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pagesExt/order/detail.js.map