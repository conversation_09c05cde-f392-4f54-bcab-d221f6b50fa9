<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="413ec7bf-1" itemdata="{{['全部','待付款','待发货','待收货','已完成','退款/售后']}}" itemst="{{['all','0','1','2','3','5']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'detail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="head"><block wx:if="{{glass_order_custom==1}}"><block><view class="f1">{{"订单号："+item.$orig.ordernum+''}}<text style="margin-left:20rpx;color:#599dfd;" data-text="{{item.$orig.ordernum}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e">复制</text></view></block></block><block wx:else><block><block wx:if="{{item.$orig.bid!=0}}"><view class="f1" data-url="{{'/pagesExt/business/index?id='+item.$orig.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{pre_url+'/static/img/ico-shop.png'}}"></image>{{''+item.$orig.binfo.name}}</view></block><block wx:else><view class="f1"><image class="logo-row" src="{{item.$orig.binfo.logo}}"></image>{{''+item.$orig.binfo.name}}</view></block></block></block><view class="flex1"><block wx:if="{{item.$orig.yuding_type&&item.$orig.yuding_type=='1'}}"><text style="color:orangered;margin-left:10rpx;">[预定订单]</text></block></view><block wx:if="{{item.$orig.status==0}}"><text class="st0">待付款</text></block><block wx:if="{{item.$orig.status==1}}"><text class="st1">待发货</text></block><block wx:if="{{item.$orig.status==2}}"><text class="st2">待收货</text></block><block wx:if="{{item.$orig.status==3}}"><text class="st3">已完成</text></block><block wx:if="{{item.$orig.status==4}}"><text class="st4">已关闭</text></block><block wx:if="{{item.$orig.status==8}}"><text class="st8">待提货</text></block></view><block wx:for="{{item.l0}}" wx:for-item="item2" wx:for-index="idx" wx:key="idx"><block><view class="content" style="{{(idx+1==item.$orig.procount?'border-bottom:none':'')}}"><view data-url="{{'/pages/shop/product?id='+item2.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item2.$orig.pic}}" mode="aspectFill"></image></view><view class="detail"><text class="t1">{{item2.$orig.name}}</text><text class="t2">{{(item2.$orig.gg_group_title?item2.$orig.gg_group_title:'')+" "+item2.$orig.ggname}}</text><block wx:if="{{item2.$orig.protype&&item2.$orig.protype==3}}"><text class="t2">{{"手工费："+item2.$orig.hand_fee}}</text></block><block><block wx:if="{{item2.$orig.product_type&&item2.$orig.product_type==2}}"><view class="t3"><text class="x1 flex1">{{item2.$orig.real_sell_price+"元/斤"}}</text><text class="x2">{{"×"+item2.$orig.real_total_weight+"斤"}}</text></view></block><block wx:else><view class="t3"><text class="x1 flex1"><block wx:if="{{showprice_dollar&&item2.$orig.usd_sellprice}}"><text>{{"$"+item2.$orig.usd_sellprice+''}}</text></block>{{"￥"+item2.$orig.sell_price}}<block wx:if="{{item2.m0}}"><text>{{"+"+item2.$orig.service_fee+item2.m1}}</text></block></text><text class="x2">{{"×"+item2.$orig.num}}</text></view></block></block><block wx:if="{{mendian_no_select==1&&item2.$orig.is_hx}}"><block><view class="t3"><text class="x2">已核销</text></view></block></block><block wx:if="{{item2.$orig.has_glassrecord}}"><view class="t2 tgr">{{''+item2.$orig.glassrecord.name+" \n\t\t\t\t\t\t\t\t\t"+(item2.$orig.glassrecord.nickname?item2.$orig.glassrecord.nickname:'')+" \n\t\t\t\t\t\t\t\t\t"+(item2.$orig.glassrecord.check_time?item2.$orig.glassrecord.check_time:'')+"\n\t\t\t\t\t\t\t\t\t"+item2.$orig.glassrecord.typetxt+''}}</view></block><block wx:if="{{(item.$orig.status==1||item.$orig.status==2||item.$orig.status==8)&&(item.$orig.freight_type==1||item.$orig.freight_type==5)&&item2.$orig.is_quanyi!=1&&item2.$orig.hexiao_code}}"><block><view class="btn2" style="position:absolute;top:40rpx;right:0rpx;" data-id="{{item2.$orig.id}}" data-num="{{item2.$orig.num}}" data-hxnum="{{item2.$orig.hexiao_num}}" data-hexiao_code="{{item2.$orig.hexiao_code}}" data-event-opts="{{[['tap',[['showhxqr2',['$event']]]]]}}" catchtap="__e">核销码</view></block></block><block wx:if="{{(item.$orig.status==1||item.$orig.status==2||item.$orig.status==8)&&item2.$orig.is_quanyi==1&&item2.$orig.hexiao_code}}"><block><view class="btn2" style="position:absolute;top:40rpx;right:0rpx;" data-id="{{item2.$orig.id}}" data-num="{{item2.$orig.hexiao_num_total}}" data-hxnum="{{item2.$orig.hexiao_num_used}}" data-hexiao_code="{{item2.$orig.hexiao_code}}" data-hexiao_tip="{{item2.$orig.hexiao_tip}}" data-event-opts="{{[['tap',[['showhxqr2',['$event']]]]]}}" catchtap="__e">核销码</view></block></block></view></view></block></block><block wx:if="{{item.$orig.crk_givenum&&item.$orig.crk_givenum>0}}"><view style="color:#f60;line-height:70rpx;">{{"+随机赠送"+item.$orig.crk_givenum+"件"}}</view></block><block wx:if="{{item.$orig.isdygroupbuy&&item.$orig.isdygroupbuy==1}}"><view class="bottom"><text style="color:red;">抖音团购券</text></view></block><view class="bottom"><view>{{"共计"+item.$orig.procount+"件商品 实付:￥"+item.$orig.totalprice+''}}<block wx:if="{{item.$orig.balance_price>0&&item.$orig.balance_pay_status==0}}"><text style="display:block;float:right;">{{"尾款：￥"+item.$orig.balance_price}}</text></block></view><block wx:if="{{item.$orig.refund_status==1}}"><text style="color:red;padding-left:6rpx;">{{"退款中￥"+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==2}}"><text style="color:red;padding-left:6rpx;">{{"已退款￥"+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==3}}"><text style="color:red;padding-left:6rpx;">退款申请已驳回</text></block></view><block wx:if="{{glass_order_custom==1}}"><view class="bottom"><view class="binfo-name"><block wx:if="{{item.$orig.bid!=0}}"><text>{{item.$orig.binfo.name}}</text></block><block wx:else><text>{{item.$orig.binfo.name}}</text></block><block wx:if="{{item.$orig.express_com}}"><text>{{item.$orig.express_com}}</text></block><block wx:if="{{item.$orig.express_no}}"><text>{{item.$orig.express_no+''}}</text></block><block wx:if="{{item.$orig.express_no}}"><text style="color:#599dfd;" data-text="{{item.$orig.express_no}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e">复制</text></block></view><view class="member-content"><view>{{"收货人："+item.$orig.linkman+" "+item.$orig.tel}}</view><view>{{item.$orig.area+item.$orig.address}}</view><block wx:if="{{item.$orig.message}}"><view>{{"备注："+item.$orig.message}}</view></block></view></view></block><block wx:if="{{item.$orig.tips!=''}}"><view class="bottom"><text style="color:red;">{{item.$orig.tips}}</text></view></block><view class="op"><block wx:if="{{item.$orig.is_pingce==1&&(item.$orig.status==1||item.$orig.status==2||item.$orig.status==3)}}"><block><block wx:if="{{item.$orig.pingce_status==2}}"><view class="btn1" style="{{'background:'+(item.m2)+';'}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['viewReport',['$event']]]]]}}" catchtap="__e">查看报告</view></block><block wx:else><view class="btn1" style="{{'background:'+(item.m3)+';'}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['toevaluate',['$event']]]]]}}" catchtap="__e">继续测评</view></block></block></block><block wx:if="{{item.g0}}"><block><view class="btn2" data-url="{{'invoice?type=shop&orderid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">发票</view></block></block><block wx:if="{{item.$orig.is_fenqi&&item.$orig.is_fenqi==1}}"><view data-event-opts="{{[['tap',[['fenqiDetails',['$0'],[[['datalist','',index]]]]]]]}}" class="btn2" catchtap="__e">分期详情</view></block><view class="btn2" data-url="{{'detail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">详情</view><block wx:if="{{item.$orig.status!=4&&item.$orig.transfer_order_parent_check}}"><block><view class="btn2" style="max-width:220rpx;" data-orderid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['transferOrder',['$event']]]]]}}" catchtap="__e">转给上级审核</view></block></block><block wx:if="{{item.$orig.status==0}}"><block><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['toclose',['$event']]]]]}}" catchtap="__e">关闭订单</view><block wx:if="{{item.$orig.paytypeid!=5&&item.$orig.is_fenqi!=1}}"><view class="btn1" style="{{'background:'+(item.m4)+';'}}" data-url="{{'/pagesExt/pay/pay?id='+item.$orig.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去付款</view></block><block wx:if="{{item.$orig.paytypeid==5}}"><block><block wx:if="{{item.$orig.transfer_check==1}}"><view class="btn1" style="{{'background:'+(item.m5)+';'}}" data-url="{{'/pages/pay/transfer?id='+item.$orig.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">付款凭证</view></block><block wx:else><view class="btn1" style="{{'background:'+(item.m6)+';'}}"><block wx:if="{{item.$orig.transfer_check==0}}"><text>转账待审核</text></block><block wx:if="{{item.$orig.transfer_check==-1}}"><text>转账已驳回</text></block></view></block></block></block></block></block><block wx:if="{{item.$orig.status==1}}"><block><block wx:if="{{item.$orig.paytypeid!='4'&&item.$orig.is_fenqi!=1}}"><block><block wx:if="{{canrefund==1&&item.$orig.order_can_refund==1&&item.$orig.refundnum<item.$orig.procount}}"><view class="btn2" data-url="{{'refundSelect?orderid='+item.$orig.id+'&price='+item.$orig.totalprice}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">退款</view></block></block></block><block wx:else><block></block></block><block wx:if="{{item.$orig.freight_type==1&&item.$orig.freight_type1_shipping_status==1}}"><view class="btn2" data-index="{{index}}" data-event-opts="{{[['tap',[['logistics',['$event']]]]]}}" catchtap="__e">查看物流</view></block></block></block><block wx:if="{{item.$orig.status==2||item.$orig.status==8}}"><block><block wx:if="{{item.$orig.paytypeid!='4'}}"><block><block wx:if="{{canrefund==1&&item.$orig.order_can_refund==1&&item.$orig.refundnum<item.$orig.procount}}"><view class="btn2" data-url="{{'refundSelect?orderid='+item.$orig.id+'&price='+item.$orig.totalprice}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">退款</view></block></block></block><block wx:else><block></block></block><block wx:if="{{item.$orig.freight_type!=3&&item.$orig.freight_type!=4}}"><block><block wx:if="{{item.$orig.express_type=='express_wx'}}"><view class="btn2" data-index="{{index}}" data-event-opts="{{[['tap',[['logistics',['$event']]]]]}}" catchtap="__e">订单跟踪</view></block><block wx:else><view class="btn2" data-index="{{index}}" data-event-opts="{{[['tap',[['logistics',['$event']]]]]}}" catchtap="__e">查看物流</view></block></block></block><block wx:if="{{item.$orig.balance_pay_status==0&&item.$orig.balance_price>0}}"><view class="btn1" style="{{'background:'+(item.m7)+';'}}" data-url="{{'/pagesExt/pay/pay?id='+item.$orig.balance_pay_orderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">支付尾款</view></block><block wx:if="{{item.$orig.can_collect&&item.$orig.paytypeid!='4'&&(item.$orig.balance_pay_status==1||item.$orig.balance_price==0)}}"><view class="btn1" style="{{'background:'+(item.m8)+';'}}" data-id="{{item.$orig.id}}" data-index="{{index}}" data-event-opts="{{[['tap',[['orderCollect',['$event']]]]]}}" catchtap="__e">确认收货</view></block><block wx:if="{{!item.$orig.can_collect&&item.$orig.paytypeid!='4'&&(item.$orig.balance_pay_status==1||item.$orig.balance_price==0)}}"><view class="btn1" style="background:#bbb;">运输中</view></block></block></block><block wx:if="{{(item.$orig.status==1||item.$orig.status==2||item.$orig.status==8)&&(item.$orig.freight_type==1||item.$orig.freight_type==5)&&item.$orig.hexiao_qr}}"><block><view class="btn2" data-hexiao_qr="{{item.$orig.hexiao_qr}}" data-hexiao_num_remain="{{item.$orig.hexiao_num_remain}}" data-event-opts="{{[['tap',[['showhxqr',['$event']]]]]}}" catchtap="__e">核销码</view></block></block><block wx:if="{{item.$orig.refundCount&&item.$orig.is_fenqi!=1}}"><view class="btn2" data-url="{{'refundlist?orderid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">售后详情</view></block><block wx:if="{{item.$orig.ishand==1}}"><block><block wx:if="{{item.$orig.status==3}}"><block><block wx:if="{{item.$orig.canhand&&item.$orig.hand_num<item.$orig.procount}}"><view class="btn2" data-url="{{'/pagesA/handwork/hand?orderid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">回寄</view></block></block></block><block wx:if="{{item.$orig.handCount}}"><view class="btn2" data-url="{{'/pagesA/handwork/handlist?orderid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">查看回寄</view></block></block></block><block wx:if="{{item.$orig.status==3||item.$orig.status==4}}"><block><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" catchtap="__e">删除订单</view></block></block><block wx:if="{{item.$orig.status==3}}"><block><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['anotherOrder',['$0'],[[['datalist','',index]]]]]]]}}" catchtap="__e">再来一单</view></block></block><block wx:if="{{item.$orig.shop_order_exchange_product&&(item.$orig.status==3||item.$orig.status==2)}}"><block><view class="btn2" style="background-color:#1A1A1A;color:#fff;" data-url="{{'refundSelect?orderid='+item.$orig.id+'&type=exchange'}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">换货</view></block></block><block wx:if="{{item.$orig.isNeedCard==1}}"><view class="btn2" data-url="{{'/pagesB/order/uploadcard?orderid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">补充资料</view></block><block wx:if="{{item.$orig.bid>0&&item.$orig.status==3}}"><block><block wx:if="{{item.$orig.iscommentdp==0}}"><view class="btn1" style="{{'background:'+(item.m9)+';'}}" data-url="{{'/pagesExt/order/commentdp?orderid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">评价店铺</view></block></block></block></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="413ec7bf-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="413ec7bf-3" bind:__l="__l"></nodata></block><uni-popup class="vue-ref" vue-id="413ec7bf-4" id="dialogHxqr" type="dialog" data-ref="dialogHxqr" bind:__l="__l" vue-slots="{{['default']}}"><view class="hxqrbox"><image class="img" src="{{hexiao_qr}}" data-url="{{hexiao_qr}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">请出示核销码给核销员进行核销</view><block wx:if="{{hexiao_num_remain>0}}"><view class="txt">{{"剩余核销次数："+hexiao_num_remain}}</view></block><view data-event-opts="{{[['tap',[['closeHxqr',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="{{pre_url+'/static/img/close2.png'}}"></image></view></view></uni-popup><uni-popup class="vue-ref" vue-id="413ec7bf-5" id="dialogSelectExpress" type="dialog" data-ref="dialogSelectExpress" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{express_content}}"><view style="background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx;"><block wx:for="{{express_content}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="sendexpress" style="border-bottom:1px solid #f5f5f5;padding:20rpx 0;"><view class="sendexpress-item" style="display:flex;" data-url="{{'/pagesExt/order/logistics?express_com='+item.express_com+'&express_no='+item.express_no}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="flex1" style="color:#121212;">{{item.express_com+" - "+item.express_no}}</view><image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view><block wx:if="{{item.express_oglist}}"><view style="margin-top:20rpx;"><block wx:for="{{item.express_oglist}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><view class="oginfo-item" style="display:flex;align-items:center;margin-bottom:10rpx;"><image style="width:50rpx;height:50rpx;margin-right:10rpx;flex-shrink:0;" src="{{item2.pic}}"></image><view class="flex1" style="color:#555;">{{item2.name+"("+item2.ggname+")"}}</view></view></block></view></block></view></block></view></block></uni-popup><block wx:if="{{selecthxnumDialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hideSelecthxnumDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择核销数量</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hideSelecthxnumDialog',['$event']]]]]}}" catchtap="__e"></image><block wx:if="{{hexiao_tip}}"><view class="txt" style="color:red;">{{hexiao_tip}}</view></block></view><view class="popup__content"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="pstime-item" data-index="{{index}}" data-event-opts="{{[['tap',[['hxnumRadioChange',['$event']]]]]}}" bindtap="__e"><view class="flex1">{{item.$orig}}</view><view class="radio" style="{{(hxnum==item.$orig?'background:'+item.m10+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></view></view></view></block><uni-popup class="vue-ref" vue-id="413ec7bf-6" id="fenqidetails" type="dialog" data-ref="fenqidetails" bind:__l="__l" vue-slots="{{['default']}}"><view class="fenqi-popup-view"><view class="popup__title popup__title-text" style="padding:15rpx;">分期详情</view><view class="fenqi-details-list"><scroll-view style="width:100%;height:700rpx;" scroll-y="{{true}}"><block wx:for="{{fenqiDetailsLsit}}" wx:for-item="item" wx:for-index="index"><block><view class="options-fenqilist flex-col"><view class="fenqilist-view flex-bt"><view style="font-size:28rpx;color:#333;font-weight:bold;">{{item.fenqi_num+"期"}}</view><block wx:if="{{item.status==0}}"><view style="color:#bbb;font-size:28rpx;font-weight:bold;">未支付</view></block><block wx:if="{{item.status==1}}"><view style="color:#ff8758;font-size:28rpx;font-weight:bold;">已付款</view></block><block wx:if="{{item.status==2}}"><view style="color:#bbb;font-size:28rpx;font-weight:bold;">已过期</view></block></view><view class="fenqilist-view flex-bt" style="padding:8rpx 0rpx;"><view class="lable-text">支付金额</view><view class="content-text">{{"￥"+item.fenqi_money}}</view></view><view class="fenqilist-view flex-bt"><view class="lable-text">赠送数量</view><view class="content-text">{{item.fenqi_give_num+"张"}}</view></view><block wx:if="{{item.end_paytime}}"><view class="fenqilist-view flex-bt"><view class="lable-text">到期时间</view><view class="content-text">{{item.end_paytime}}</view></view></block></view></block></block></scroll-view></view><block wx:if="{{fenqibut_status}}"><view class="fenqi-but-view"><view data-event-opts="{{[['tap',[['fenquGopay',[1]]]]]}}" class="btn2" bindtap="__e">支付当期</view><view data-event-opts="{{[['tap',[['fenquGopay',[2]]]]]}}" class="btn2" bindtap="__e">全部支付</view></view></block></view></uni-popup><block wx:if="{{reportShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['changeReportDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">查看报告</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['changeReportDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:if="{{reportArr.bolePsyReport}}"><view class="clist-item" data-url="{{reportArr.bolePsyReport}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="flex1">32种人才心理特质报告</view><view class="radio"><image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></block><block wx:if="{{reportArr.bolePostfitReport}}"><view class="clist-item" data-url="{{reportArr.bolePostfitReport}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="flex1">42种职场岗位适配报</view><view><image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></block></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="413ec7bf-7" bind:__l="__l"></loading></block><dp-tabbar vue-id="413ec7bf-8" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="413ec7bf-9" data-ref="popmsg" bind:__l="__l"></popmsg></view>