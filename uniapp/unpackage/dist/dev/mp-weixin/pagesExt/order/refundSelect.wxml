<view class="container"><block wx:if="{{isload}}"><block><form report-submit="true" data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form-content"><view class="form-item"><text class="label">{{markname+"商品"}}</text></view><view class="product"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="content"><view data-url="{{'/pages/shop/product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.$orig.pic}}"></image></view><view class="detail"><text class="t1">{{item.$orig.name}}</text><text class="t2">{{item.$orig.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item.$orig.sell_price}}<block wx:if="{{item.m0}}"><text>{{"+"+item.$orig.service_fee+item.m1}}</text></block></text><text class="x2">{{"×"+item.$orig.num}}</text></view></view></view></block></view></view><view class="card-view"><view class="card-wrap"><view class="card-title">选择类型</view><block wx:if="{{opt.type!='exchange'}}"><view class="info-item mt" data-url="{{'refund?orderid='+detail.id+'&type=refund&ogid='+ogid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1 flex1 flex-col"><view>我要退款(无需退货)</view><view class="desc">没收到货，或与卖家协商一致不用退货只退款</view></view><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:if="{{opt.type!='exchange'}}"><view class="info-item" data-url="{{'refund?orderid='+detail.id+'&type=return&ogid='+ogid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1 flex1 flex-col"><view>我要退货退款</view><view class="desc">已收到货，需要退还收到的货物</view></view><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:if="{{detail.shop_order_exchange_product&&opt.type=='exchange'}}"><view class="info-item" data-url="{{'refund?orderid='+detail.id+'&type=exchange&ogid='+ogid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1 flex1 flex-col"><view>我要换货</view><view class="desc">对收到的商品不满意，可与商家协商换货</view></view><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block></view></view><view class="card-view"><view class="card-wrap"><view class="info-item mt" data-url="{{'refundlist?orderid='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1 flex1"><view>{{"查看本订单"+markname+"记录"}}</view></view><image class="t3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="5baece7d-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="5baece7d-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5baece7d-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>