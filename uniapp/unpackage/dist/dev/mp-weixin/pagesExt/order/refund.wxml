<view class="container"><block wx:if="{{isload}}"><block><form report-submit="true" data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form-content"><view class="form-item"><text class="label">{{markname+"商品"}}</text></view><view class="product"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="content"><block wx:if="{{opt.type=='exchange'}}"><view class="radio" style="{{(item.$orig.checked?'background:'+item.m0+';border:0':'')}}" data-index="{{index}}" data-proid="{{item.$orig.proid}}" data-ogid="{{item.$orig.id}}" data-sell_price="{{item.$orig.sell_price}}" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></block><view data-url="{{'/pages/shop/product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="productimg" src="{{item.$orig.pic}}"></image></view><view class="detail"><text class="t1">{{item.$orig.name}}</text><text class="t2">{{item.$orig.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item.$orig.sell_price}}</text><block wx:if="{{opt.type=='exchange'}}"><text class="x2">{{"×"+item.$orig.num}}</text></block><block wx:if="{{opt.type!='exchange'}}"><view class="num-wrap"><view class="addnum"><view class="minus" data-index="{{index}}" data-ogid="{{item.$orig.id}}" data-num="{{refundNum[index].num}}" data-event-opts="{{[['tap',[['gwcminus',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/cart-minus.png'}}"></image></view><input class="input" type="number" data-index="{{index}}" data-ogid="{{item.$orig.id}}" data-max="{{item.$orig.num-item.$orig.refund_num}}" data-num="{{refundNum[index].num}}" data-event-opts="{{[['blur',[['gwcinput',['$event']]]]]}}" value="{{refundNum[index].num}}" bindblur="__e"/><view class="plus" data-index="{{index}}" data-ogid="{{item.$orig.id}}" data-max="{{item.$orig.num-item.$orig.refund_num}}" data-num="{{refundNum[index].num}}" data-event-opts="{{[['tap',[['gwcplus',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/cart-plus.png'}}"></image></view></view><block wx:if="{{opt.type!='exchange'}}"><view class="text-desc">{{"申请数量：最多可申请"+item.$orig.canRefundNum+"件"}}</view></block></view></block></view></view></view></block></view></view><view class="form-content"><block wx:if="{{opt.type=='refund'}}"><view class="form-item" style="display:none;"><text class="label">货物状态</text><view class="input-item"><picker style="height:80rpx;line-height:80rpx;border-bottom:1px solid #EEEEEE;" value="{{cindex}}" range="{{cateArr}}" name="receive" data-event-opts="{{[['change',[['pickerChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{cindex==-1?'请选择':cateArr[cindex]}}</view></picker></view></view></block><block wx:if="{{opt.type=='exchange'&&return_address.return_address}}"><view class="form-item"><text class="label">寄回地址</text><view class="flex"><view class="input-item" data-content="{{return_address.return_name+' '+return_address.return_tel+' '+return_address.return_province+' '+return_address.return_city+' '+return_address.return_area+' '+return_address.return_address}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" bindtap="__e">{{return_address.return_name+" "+return_address.return_tel+''}}<view class="_br"></view>{{''+return_address.return_province+" "+return_address.return_city+" "+return_address.return_area+" "+return_address.return_address}}</view><view class="btn-class" style="margin-left:20rpx;width:80rpx;" data-content="{{return_address.return_name+' '+return_address.return_tel+' '+return_address.return_province+' '+return_address.return_city+' '+return_address.return_area+' '+return_address.return_address}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" bindtap="__e">复制</view></view></view></block><block wx:if="{{opt.type=='exchange'}}"><view class="form-item"><text class="label">填写快递单号</text><view class="input-item" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['fahuo',['$event']]]]]}}" bindtap="__e"><input style="margin-top:10rpx;padding:0 10rpx;pointer-events:none;" type="text" disabled="{{true}}" placeholder="请输入寄回快递单号" placeholder-style="color:#999;" value="{{expressxians}}"/></view></view></block><view class="form-item"><text class="label">{{markname+"原因"}}</text><view class="input-item"><textarea placeholder="请输入原因" placeholder-style="color:#999;" name="reason" data-event-opts="{{[['input',[['reasonInput',['$event']]]]]}}" bindinput="__e"></textarea></view></view><block wx:if="{{opt.type=='refund'||opt.type=='return'}}"><view class="form-item"><text class="label">退款金额(元)</text><view class="flex"><input name="money" type="digit" placeholder="请输入退款金额" placeholder-style="color:#999;" data-event-opts="{{[['input',[['moneyInput',['$event']]]]]}}" value="{{money}}" bindinput="__e"/></view></view></block><view class="form-item flex-col"><view class="label">上传图片(最多三张)</view><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;" id="content_picpreview"><block wx:for="{{content_pic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="content_pic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0<3}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="content_pic" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view></view></view><button data-event-opts="{{[['tap',[['formSubmit',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m1)+';'}}" bindtap="__e">确定</button><view style="padding-top:30rpx;"></view></form></block></block><block wx:if="{{buydialogShow}}"><buydialog vue-id="15f83a59-1" proid="{{proid}}" btntype="1" needaddcart="{{false}}" showbuynum="{{false}}" menuindex="{{menuindex}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]],['^addcart',[['afteraddcart']]]]}}" bind:buydialogChange="__e" bind:addcart="__e" bind:__l="__l"></buydialog></block><uni-popup class="vue-ref" vue-id="15f83a59-2" id="dialogExpress" type="dialog" data-ref="dialogExpress" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">填写快递单号</text></view><view class="uni-dialog-content"><view><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><text style="font-size:28rpx;color:#000;">快递公司：</text><picker style="font-size:28rpx;border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;" value="{{express_index}}" range="{{expressdata}}" data-event-opts="{{[['change',[['expresschange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{expressdata[express_index]}}</view></picker></view><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;">快递单号：</view><view class="danhao-input-view"><input style="border:none;outline:none;padding:0 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;" type="text" placeholder="请输入快递单号" data-event-opts="{{[['input',[['__set_model',['','express_no','$event',[]]],['setexpressno',['$event']]]]]}}" value="{{express_no}}" bindinput="__e"/><image src="{{pre_url+'/static/img/admin/saoyisao.png'}}" data-event-opts="{{[['tap',[['saoyisao',['$event']]]]]}}" bindtap="__e"></image></view></view></view></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['dialogExpressClose',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><view data-event-opts="{{[['tap',[['refundExpress',['$event']]]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">确定</text></view></view></view></uni-popup><block wx:if="{{loading}}"><loading vue-id="15f83a59-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="15f83a59-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="15f83a59-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>