<view class="container"><block wx:if="{{isload}}"><block><view class="ordertop" style="{{('background:url('+pre_url+'/static/img/ordertop_refund.jpg);background-size:100%')}}"><block wx:if="{{detail.refund_status==0}}"><view class="f1"><view class="t1">已取消</view></view></block><block wx:if="{{detail.refund_status==1}}"><view class="f1"><view class="t1">审核中</view></view></block><block wx:if="{{detail.refund_status==2}}"><view class="f1"><view class="t1">审核通过，已退款</view></view></block><block wx:if="{{detail.refund_status==3}}"><view class="f1"><view class="t1">驳回</view></view></block><block wx:if="{{detail.refund_status==4}}"><block><block wx:if="{{!detail.isexpress}}"><view class="f1"><view class="t1">审核通过，待退货</view><view class="t2">联系商家进行退货</view></view></block><block wx:else><view class="f1"><view class="t1">审核通过，已寄回</view></view></block></block></block><block wx:if="{{shop_order_exchange_product&&detail.refund_status==5}}"><view class="f1"><view class="t1">商家已收货</view></view></block><block wx:if="{{shop_order_exchange_product&&detail.refund_status==6}}"><view class="f1"><view class="t1">商家已驳回换货</view></view></block><block wx:if="{{shop_order_exchange_product&&detail.refund_status==7}}"><view class="f1"><view class="t1">商家已寄出</view></view></block><block wx:if="{{shop_order_exchange_product&&detail.refund_status==8}}"><view class="f1"><view class="t1">换货完成</view></view></block></view><block wx:if="{{detail.bid>0}}"><view class="btitle flex-y-center" data-url="{{'/pagesExt/business/index?id='+detail.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:36rpx;height:36rpx;" src="{{detail.binfo.logo}}"></image><view class="flex1" style="padding-left:16rpx;" decode="true" space="true">{{detail.binfo.name}}</view></view></block><view class="product"><block wx:for="{{prolist}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="content"><view data-url="{{'/pages/shop/product?id='+item.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.pic}}"></image></view><view class="detail"><text class="t1">{{item.name}}</text><text class="t2">{{item.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item.sell_price}}</text><text class="x2">{{"×"+item.refund_num}}</text></view></view></view></block></view><block wx:if="{{$root.g0}}"><view class="product"><view class="item"><text class="t1">换新商品</text></view><block wx:for="{{newprolist}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="content"><view data-url="{{'/pages/shop/product?id='+item.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.pic}}"></image></view><view class="detail"><text class="t1">{{item.name}}</text><text class="t2">{{item.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item.sell_price}}</text><text class="x2">{{"×"+item.num}}</text></view></view></view></block></view></block><view class="orderinfo"><view class="item"><text class="t1">售后单编号</text><text class="t2" user-select="true" selectable="true">{{detail.refund_ordernum}}</text></view><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true" data-url="{{'detail?id='+detail.orderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{detail.ordernum}}</text></view><view class="item"><text class="t1">申请时间</text><text class="t2">{{detail.createtime}}</text></view></view><view class="orderinfo"><view class="item"><text class="t1">类型</text><text class="t2 red">{{detail.refund_type_label}}</text></view><block wx:if="{{detail.refund_type=='refund'||detail.refund_type=='return'}}"><view class="item"><text class="t1">申请退款金额</text><text class="t2 red">{{"¥"+detail.refund_money}}</text></view></block><view class="item"><text class="t1">{{markname+"状态"}}</text><block wx:if="{{detail.refund_status==0}}"><text class="t2 grey">已取消</text></block><block wx:if="{{detail.refund_status==1}}"><text class="t2 red">审核中</text></block><block wx:if="{{detail.refund_status==2}}"><text class="t2 red">已退款</text></block><block wx:if="{{detail.refund_status==3}}"><text class="t2 red">已驳回</text></block><block wx:if="{{detail.refund_status==4}}"><block><block wx:if="{{!detail.isexpress}}"><text class="t2 red">审核通过，待退货</text></block><block wx:else><text class="t2 red">审核通过，已寄回</text></block></block></block><block wx:if="{{shop_order_exchange_product&&detail.refund_status==5}}"><text class="t2 red">商家已收货</text></block><block wx:if="{{shop_order_exchange_product&&detail.refund_status==6}}"><text class="t2 red">商家已驳回</text></block><block wx:if="{{shop_order_exchange_product&&detail.refund_status==7}}"><text class="t2 red">商家已寄出</text></block><block wx:if="{{shop_order_exchange_product&&detail.refund_status==8}}"><text class="t2" style="color:green;">换货完成</text></block></view><block wx:if="{{shop_order_exchange_product&&detail.refund_status==6&&detail.exchange_reject_reason}}"><view class="item"><text class="t1">换货驳回原因</text><text class="t2">{{detail.exchange_reject_reason}}</text></view></block><view class="item"><text class="t1">{{markname+"原因"}}</text><text class="t2">{{detail.refund_reason}}</text></view><block wx:if="{{detail.refund_checkremark}}"><view class="item"><text class="t1">审核备注</text><text class="t2">{{detail.refund_checkremark}}</text></view></block><block wx:if="{{detail.refund_pics}}"><view class="item"><text class="t1">图片</text><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;" id="content_picpreview"><block wx:for="{{detail.refund_pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></view></view></block><block wx:if="{{detail.refund_status==2||detail.refund_status==4}}"><block><block wx:if="{{detail.return_address}}"><view class="item"><text class="t1">寄回地址</text><text class="t2" data-content="{{detail.return_province+' '+detail.return_city+' '+detail.return_area+' '+detail.return_address}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" bindtap="__e">{{detail.return_province+" "+detail.return_city+" "+detail.return_area+" "+detail.return_address}}</text></view></block><block wx:if="{{detail.return_name}}"><view class="item"><text class="t1">寄回联系人</text><text class="t2" data-content="{{detail.return_name+' '+detail.return_tel}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" bindtap="__e">{{detail.return_name+" "+detail.return_tel}}</text></view></block><block wx:if="{{detail.express_com}}"><view class="item"><text class="t1">快递公司</text><text class="t2">{{detail.express_com}}</text></view></block><block wx:if="{{detail.express_no}}"><view class="item"><text class="t1">快递单号</text><text class="t2">{{detail.express_no}}</text></view></block></block></block></view><block wx:if="{{shop_order_exchange_product&&detail.refund_type=='exchange'&&(detail.refund_status==7||detail.refund_status==8)}}"><view class="orderinfo"><block wx:if="{{detail.exchange_express_com}}"><view class="item"><text class="t1">换货快递公司</text><text class="t2">{{detail.exchange_express_com}}</text></view></block><block wx:if="{{detail.exchange_express_no}}"><view class="item"><text class="t1">换货快递单号</text><text class="t2" style="margin-top:2rpx;">{{detail.exchange_express_no+''}}</text><view class="btn-class" style="margin-left:20rpx;" data-expresscom="{{detail.exchange_express_com}}" data-expressno="{{detail.exchange_express_no}}" data-expresscontent="{{detail.exchange_express_content}}" data-event-opts="{{[['tap',[['logistics',['$event']]]]]}}" catchtap="__e">查看物流</view></view></block></view></block><block wx:if="{{detail.freight_type==11}}"><view class="orderinfo"><view class="item"><text class="t1">发货地址</text><text class="t2">{{"¥"+detail.freight_content.send_address+" - "+detail.freight_content.send_tel}}</text></view><view class="item"><text class="t1">收货地址</text><text class="t2">{{"¥"+detail.freight_content.receive_address+" - "+detail.freight_content.receive_tel}}</text></view></view></block><view style="width:100%;height:calc(160rpx + env(safe-area-inset-bottom));"></view><view class="bottom"><block wx:if="{{shop_order_exchange_product&&detail.refund_status==7}}"><block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['toExchangeConfirm',['$event']]]]]}}" bindtap="__e">确认收货</view></block></block><block wx:if="{{detail.refund_status==1||detail.refund_status==4||detail.refund_status==6}}"><block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['toclose',['$event']]]]]}}" bindtap="__e">取消</view></block></block><block wx:if="{{(detail.refund_status==2||detail.refund_status==4)&&detail.return_address}}"><view class="btn2" style="padding:0 20rpx;" data-content="{{detail.return_name+' '+detail.return_tel+' '+detail.return_province+detail.return_city+detail.return_area+detail.return_address}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" bindtap="__e">复制地址</view></block><block wx:if="{{detail.refund_status==4&&!detail.isexpress}}"><view class="btn2" style="padding:0 20rpx;" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['fahuo',['$event']]]]]}}" bindtap="__e">填写快递单号</view></block></view><uni-popup class="vue-ref" vue-id="56b48f70-1" id="dialogExpress" type="dialog" data-ref="dialogExpress" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">填写快递单号</text></view><view class="uni-dialog-content"><view><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><text style="font-size:28rpx;color:#000;">快递公司：</text><picker style="font-size:28rpx;border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;" value="{{express_index}}" range="{{expressdata}}" data-event-opts="{{[['change',[['expresschange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{expressdata[express_index]}}</view></picker></view><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;">快递单号：</view><view class="danhao-input-view"><input style="border:none;outline:none;padding:10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;" type="text" placeholder="请输入快递单号" data-event-opts="{{[['input',[['__set_model',['','express_no','$event',[]]],['setexpressno',['$event']]]]]}}" value="{{express_no}}" bindinput="__e"/><image src="{{pre_url+'/static/img/admin/saoyisao.png'}}" data-event-opts="{{[['tap',[['saoyisao',['$event']]]]]}}" bindtap="__e"></image></view></view></view></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['dialogExpressClose',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><view data-event-opts="{{[['tap',[['refundExpress',['$event']]]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">确定</text></view></view></view></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="56b48f70-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="56b48f70-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="56b48f70-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>