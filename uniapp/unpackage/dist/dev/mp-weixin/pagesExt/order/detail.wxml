<view class="container"><block wx:if="{{isload}}"><block><view class="ordertop" style="{{('background:url('+(shopset.order_detail_toppic?shopset.order_detail_toppic:pre_url+'/static/img/ordertop.png')+');background-size:100%')}}"><block wx:if="{{detail.status==0}}"><view class="f1"><view class="t1">等待买家付款</view><block wx:if="{{djs}}"><view class="t2">{{"剩余时间："+djs}}</view></block><block wx:if="{{detail.paytypeid==5}}"><view class="t2"><block wx:if="{{detail.transfer_check==1}}"><text>{{$root.m0+"后请上传付款凭证"}}</text></block><block wx:if="{{detail.transfer_check==0}}"><text>{{$root.m1+"待审核"}}</text></block><block wx:if="{{detail.transfer_check==-1}}"><text>{{$root.m2+"已驳回"}}</text></block></view></block></view></block><block wx:if="{{detail.status==1}}"><view class="f1"><view class="t1">{{detail.paytypeid==4?'已选择'+detail.paytype:'已成功付款'}}</view><block wx:if="{{detail.freight_type!=1}}"><view class="t2">我们会尽快为您发货</view></block><block wx:if="{{detail.freight_type==1}}"><view class="t2">请尽快前往自提地点取货</view></block></view></block><block wx:if="{{detail.status==2}}"><view class="f1"><view class="t1">订单已发货</view><block wx:if="{{detail.freight_type!=3}}"><text class="t2" user-select="true" selectable="true">{{"发货信息："+detail.express_com+" "+detail.express_no}}</text></block></view></block><block wx:if="{{detail.status==3}}"><view class="f1"><view class="t1">订单已完成</view></view></block><block wx:if="{{detail.status==4}}"><view class="f1"><view class="t1">订单已取消</view></view></block><block wx:if="{{detail.status==8}}"><view class="f1"><view class="t1">订单已到达代收点</view></view></block></view><block wx:if="{{detail.is_pingce!=1}}"><view class="address flex-y-center"><block wx:if="{{mendian_no_select==0}}"><view class="img"><image src="{{pre_url+'/static/img/address3.png'}}"></image></view></block><block wx:if="{{detail.mdid==-1}}"><view class="info"><view class="t1"><text user-select="true" selectable="true">{{detail.linkman+" "+detail.tel+" "+email+''}}</text></view><view class="t1" style="margin-top:20rpx;">取货地点：</view><view><block wx:for="{{storelist}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><block><block wx:if="{{idx<5||storeshowall==true}}"><view class="radio-item" data-latitude="{{item.latitude}}" data-longitude="{{item.longitude}}" data-company="{{item.name}}" data-address="{{item.address}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e"><view class="f1"><view>{{item.name}}</view><block wx:if="{{item.address}}"><view style="text-align:left;font-size:24rpx;color:#aaaaae;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;">{{item.address}}</view></block></view><text style="color:#f50;">{{item.juli}}</text></view></block></block></block><block wx:if="{{$root.g0}}"><view data-event-opts="{{[['tap',[['doStoreShowAll',['$event']]]]]}}" class="storeviewmore" bindtap="__e">- 查看更多 -</view></block></view><block wx:if="{{detail.worknum}}"><view class="t2">{{"工号："+detail.worknum}}</view></block></view></block><block wx:else><block wx:if="{{mendian_no_select==1}}"><view class="info"><text class="t1" user-select="true" selectable="true">{{detail.linkman+" "+detail.tel+" "+email}}</text><view class="t1" style="margin-top:20rpx;">取货地点：</view><view><block wx:for="{{mendianArr}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><block><block wx:if="{{idx<5||storeshowall==true}}"><view class="radio-item" data-latitude="{{item.latitude}}" data-longitude="{{item.longitude}}" data-company="{{item.name}}" data-address="{{item.address}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e"><view class="img"><image src="{{pre_url+'/static/img/address3.png'}}"></image></view><view class="f1"><view>{{item.name}}</view><block wx:if="{{item.address}}"><view style="text-align:left;font-size:24rpx;color:#aaaaae;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;">{{item.address}}</view></block></view><text style="color:#f50;">{{item.juli}}</text></view></block></block></block><block wx:if="{{$root.g1}}"><view data-event-opts="{{[['tap',[['doStoreShowAll',['$event']]]]]}}" class="storeviewmore" bindtap="__e">- 查看更多 -</view></block></view></view></block><block wx:else><view class="info"><text class="t1" user-select="true" selectable="true">{{detail.linkman+" "+detail.tel+" "+email}}</text><block wx:if="{{detail.freight_type!=1&&detail.freight_type!=3}}"><text class="t2" user-select="true" selectable="true">{{"地址："+detail.area+detail.address}}</text></block><block wx:if="{{detail.product_thali}}"><text class="t2">{{"学生姓名："+detail.product_thali_student_name}}</text></block><block wx:if="{{detail.product_thali}}"><text class="t2">{{"学校信息："+detail.product_thali_school}}</text></block><block wx:if="{{detail.freight_type==1}}"><block><block wx:if="{{!$root.m3}}"><text class="t2" data-storeinfo="{{storeinfo}}" user-select="true" selectable="true" data-event-opts="{{[['tap',[['openMendian',['$event']]]]]}}" bindtap="__e">{{"取货地点："+storeinfo.name+" - "+storeinfo.address}}</text></block><block wx:else><text class="t2">取货地点数据不存在，请联系客服</text></block></block></block><block wx:if="{{detail.worknum}}"><view class="t2">{{"工号："+detail.worknum}}</view></block></view></block></block></view></block><block wx:if="{{detail.usegiveorder&&detail.usegiveorder==1}}"><view class="orderinfo"><view class="title">赠好友</view><view class="item"><view class="t1">领取状态</view><block wx:if="{{detail.giveordermid>0}}"><view class="t2" style="color:green;">已领取</view></block><block wx:else><view class="t2" style="color:red;display:flex;justify-content:flex-end;align-items:center;"><view>未领取</view><block wx:if="{{detail.status==1}}"><block><button class="btn2" open-type="share">分享好友</button></block></block></view></block></view><block wx:if="{{detail.giveordermid>0&&detail.givemember}}"><block><view class="item"><text class="t1">好友信息</text><text class="flex1"></text><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{detail.givemember.headimg}}"></image><text style="height:80rpx;line-height:80rpx;">{{detail.givemember.nickname}}</text></view></block></block></view></block><block wx:if="{{detail.is_pingce==1}}"><view class="orderinfo"><view class="title">测评信息</view><view class="item"><text class="t1">姓名</text><text class="t2">{{detail.linkman}}</text></view><view class="item"><text class="t1">性别</text><text class="t2">{{detail.pingce.gender}}</text></view><view class="item"><text class="t1">生日</text><text class="t2">{{detail.pingce.age}}</text></view><view class="item"><text class="t1">手机</text><text class="t2">{{detail.tel}}</text></view><view class="item"><text class="t1">邮箱</text><text class="t2">{{detail.pingce.email}}</text></view><view class="item"><text class="t1">院校</text><text class="t2">{{detail.pingce.school}}</text></view><block wx:if="{{detail.pingce.faculties}}"><view class="item"><text class="t1">院系</text><text class="t2">{{detail.pingce.faculties?detail.pingce.faculties:''}}</text></view></block><view class="item"><text class="t1">专业</text><text class="t2">{{detail.pingce.major}}</text></view><view class="item"><text class="t1">学历</text><text class="t2">{{detail.pingce.education}}</text></view><view class="item"><text class="t1">入学年份</text><text class="t2">{{detail.pingce.enrol+"年"}}</text></view><block wx:if="{{detail.pingce.class_name}}"><view class="item"><text class="t1">班级</text><text class="t2">{{detail.pingce.class_name?detail.pingce.class_name:''}}</text></view></block></view></block><block wx:if="{{detail.bid>0}}"><view class="btitle flex-y-center" data-url="{{'/pagesExt/business/index?id='+detail.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:36rpx;height:36rpx;" src="{{detail.binfo.logo}}"></image><view class="flex1" style="padding-left:16rpx;" decode="true" space="true">{{detail.binfo.name}}</view></view></block><view class="product"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="box"><view class="content"><view data-url="{{'/pages/shop/product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.$orig.pic}}"></image></view><view class="detail"><text class="t1">{{item.$orig.name}}</text><view class="t2 flex flex-y-center flex-bt"><text>{{(item.$orig.gg_group_title?item.$orig.gg_group_title:'')+" "+item.$orig.ggname}}</text><block wx:if="{{detail.status==3&&item.$orig.iscomment==0&&shopset.comment==1}}"><view class="btn3" data-url="{{'comment?ogid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去评价</view></block><block wx:if="{{detail.status==3&&item.$orig.iscomment==1}}"><view class="btn3" data-url="{{'comment?ogid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">查看评价</view></block><block wx:if="{{(detail.status==2||detail.status==1)&&detail.paytypeid!='4'&&shopset.canrefund==1&&detail.order_can_refund==1&&item.$orig.refund_num<item.$orig.num}}"><block><view class="btn3" data-url="{{'refundSelect?orderid='+detail.id+'&ogid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">退款</view></block></block></view><block wx:if="{{item.$orig.protype&&item.$orig.protype==3}}"><view class="t2"><text>{{"手工费："+item.$orig.hand_fee}}</text></view></block><block wx:if="{{item.$orig.product_type&&item.$orig.product_type==2}}"><view class="t3"><text class="x1 flex1">{{item.$orig.real_sell_price+"元/斤"}}</text><text class="x2">{{"×"+item.$orig.real_total_weight+"斤"}}</text></view></block><block wx:else><view class="t3"><text class="x1 flex1">{{"￥"+item.$orig.sell_price}}<block wx:if="{{item.m4}}"><text>{{"+"+item.$orig.service_fee+item.m5}}</text></block></text><text class="x2">{{"×"+item.$orig.num}}</text></view></block><block wx:if="{{(detail.status==1||detail.status==2||detail.status==8)&&(detail.freight_type==1||detail.freight_type==5)&&item.$orig.is_quanyi!=1&&item.$orig.hexiao_code}}"><block><view class="btn2" style="position:absolute;top:20rpx;right:0rpx;" data-id="{{item.$orig.id}}" data-num="{{item.$orig.num}}" data-hxnum="{{item.$orig.hexiao_num}}" data-hexiao_code="{{item.$orig.hexiao_code}}" data-event-opts="{{[['tap',[['showhxqr',['$event']]]]]}}" catchtap="__e">核销码</view></block></block><block wx:if="{{(detail.status==1||detail.status==2||detail.status==8)&&item.$orig.is_quanyi==1&&item.$orig.hexiao_code}}"><block><view class="btn3" style="position:absolute;top:46rpx;right:100rpx;" data-id="{{item.$orig.id}}" data-num="{{item.$orig.hexiao_num_total}}" data-hxnum="{{item.$orig.hexiao_num_used}}" data-hexiao_code="{{item.$orig.hexiao_code}}" data-event-opts="{{[['tap',[['showhxqr2',['$event']]]]]}}" catchtap="__e">核销码</view></block></block><block wx:if="{{mendian_no_select==1&&item.$orig.is_hx}}"><block><view class="t3"><text class="x2">已核销</text></view></block></block></view></view><block wx:if="{{item.$orig.glassrecord}}"><view class="glassitem"><view class="gcontent"><view class="glassheader">{{''+item.$orig.glassrecord.name+"\n\t\t\t\t\t\t\t"+(item.$orig.glassrecord.nickname?item.$orig.glassrecord.nickname:'')+"\n\t\t\t\t\t\t\t"+(item.$orig.glassrecord.check_time?item.$orig.glassrecord.check_time:'')+"\n\t\t\t\t\t\t\t"+item.$orig.glassrecord.typetxt+''}}<block wx:if="{{item.$orig.glassrecord.double_ipd==0}}"><text class="pdl10">{{item.$orig.glassrecord.ipd?'PD'+item.$orig.glassrecord.ipd:''}}</text></block><block wx:else><text class="pdl10">{{"PD R"+item.$orig.glassrecord.ipd_right+" L"+item.$orig.glassrecord.ipd_left}}</text></block></view><view class="glassrow bt"><view class="grow">{{'R '+item.$orig.glassrecord.degress_right+"/"+(item.$orig.glassrecord.ats_right?item.$orig.glassrecord.ats_right:'0.00')+"*"+(item.$orig.glassrecord.ats_zright?item.$orig.glassrecord.ats_zright:'0')+''}}<block wx:if="{{item.$orig.glassrecord.type==3}}"><text class="pdl10">{{"ADD+"+(item.$orig.glassrecord.add_right?item.$orig.glassrecord.add_right:0)}}</text></block></view><view class="grow">{{'L '+item.$orig.glassrecord.degress_left+"/"+(item.$orig.glassrecord.ats_left?item.$orig.glassrecord.ats_left:'0.00')+"*"+(item.$orig.glassrecord.ats_zleft?item.$orig.glassrecord.ats_zleft:'0')+''}}<block wx:if="{{item.$orig.glassrecord.type==3}}"><text class="pdl10">{{"ADD+"+(item.$orig.glassrecord.add_left?item.$orig.glassrecord.add_left:0)}}</text></block></view></view><block wx:if="{{item.$orig.glassrecord.remark}}"><view class="glassrow">{{item.$orig.glassrecord.remark}}</view></block></view></view></block></view></block><block wx:if="{{detail.crk_givenum&&detail.crk_givenum>0}}"><view style="color:#f60;line-height:70rpx;">{{"+随机赠送"+detail.crk_givenum+"件"}}</view></block></view><block wx:if="{{(detail.status==3||detail.status==2)&&(detail.freight_type==3||detail.freight_type==4)}}"><view class="orderinfo"><view class="item flex-col"><view class="flex-bt order-info-title"><text class="t1" style="color:#111;">发货信息</text><view class="btn-class" data-text="{{detail.freight_content}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" bindtap="__e">复制</view></view><block wx:for="{{contentWithLinks}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text class="t2" style="text-align:left;margin-top:10rpx;padding:0 10rpx;" user-select="true" selectable="true"><block wx:if="{{item.url}}"><text data-event-opts="{{[['tap',[['handleLinkClick',['$0'],[[['contentWithLinks','',index,'url']]]]]]]}}" bindtap="__e">{{item.text}}</text></block><block wx:else><text>{{item.text}}</text></block></text></block></view></view></block><view class="orderinfo"><view class="item flex-bt"><text class="t1">订单编号</text><view class="ordernum-info flex-bt"><text class="t2" user-select="true" selectable="true">{{detail.ordernum}}</text><view class="btn-class" style="margin-left:20rpx;" data-text="{{detail.ordernum}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" bindtap="__e">复制</view></view></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{detail.createtime}}</text></view><block wx:if="{{detail.status>0&&detail.paytypeid!='4'&&detail.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{detail.paytime}}</text></view></block><block wx:if="{{detail.paytypeid}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{detail.paytype}}</text></view></block><block wx:if="{{detail.yuding_type=='1'}}"><view class="item"><text class="t1">订单类型</text><text class="t2">预定订单</text></view></block><block wx:if="{{detail.paytypeid=='5'&&detail.transfer_check==1}}"><block><block wx:if="{{pay_transfer_info.pay_transfer_account_name}}"><view class="item"><text class="t1">户名</text><text class="t2">{{pay_transfer_info.pay_transfer_account_name}}</text></view></block><block wx:if="{{pay_transfer_info.pay_transfer_account}}"><view class="item"><text class="t1">账户</text><text class="t2">{{pay_transfer_info.pay_transfer_account}}</text></view></block><block wx:if="{{pay_transfer_info.pay_transfer_bank}}"><view class="item"><text class="t1">开户行</text><text class="t2">{{pay_transfer_info.pay_transfer_bank}}</text></view></block><block wx:if="{{pay_transfer_info.pay_transfer_qrcode}}"><view class="item"><text class="t1">图片</text><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;" id="content_picpreview"><block wx:for="{{pay_transfer_info.pay_transfer_qrcode_arr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></view></view></block><block wx:if="{{pay_transfer_info.pay_transfer_desc}}"><view class="item"><text class="text-min">{{pay_transfer_info.pay_transfer_desc}}</text></view></block><view class="item"><text class="t1">付款凭证审核</text><text class="t2">{{payorder.check_status_label}}</text></view><block wx:if="{{payorder.check_remark}}"><view class="item"><text class="t1">审核备注</text><text class="t2">{{payorder.check_remark}}</text></view></block></block></block><block wx:if="{{detail.status>1&&detail.send_time}}"><view class="item"><text class="t1">发货时间</text><text class="t2">{{detail.send_time}}</text></view></block><block wx:if="{{detail.status==3&&detail.collect_time}}"><view class="item"><text class="t1">收货时间</text><text class="t2">{{detail.collect_time}}</text></view></block><block wx:if="{{detail.status==3&&detail.handtime}}"><view class="item"><text class="t1">回寄时间</text><text class="t2 red">{{detail.handtime}}</text></view></block></view><view class="orderinfo"><view class="item"><text class="t1">商品金额</text><text class="t2 red">{{"¥"+detail.product_price+''}}<block wx:if="{{$root.m6}}"><text>{{"+"+detail.service_fee+$root.m7}}</text></block></text></view><block wx:if="{{detail.leveldk_money>0}}"><view class="item"><text class="t1">{{$root.m8+"折扣"}}</text><text class="t2 red">{{"-¥"+detail.leveldk_money}}</text></view></block><block wx:if="{{detail.manjian_money>0}}"><view class="item"><text class="t1">满减活动</text><text class="t2 red">{{"-¥"+detail.manjian_money}}</text></view></block><block wx:if="{{detail.invoice_money>0}}"><view class="item"><text class="t1">发票费用</text><text class="t2 red">{{"+¥"+detail.invoice_money}}</text></view></block><view class="item"><text class="t1">配送方式</text><text class="t2">{{detail.freight_text}}</text></view><block wx:if="{{detail.freight_time}}"><view class="item"><text class="t1">{{(detail.freight_type!=1?'配送':'提货')+"时间"}}</text><text class="t2">{{detail.freight_time}}</text></view></block><block wx:if="{{detail.freight_price>0}}"><view class="item"><block wx:if="{{detail.freight_type==0||detail.freight_type==2}}"><text class="t1">配送费</text></block><block wx:else><text class="t1">服务费</text></block><text class="t2 red">{{"+¥"+detail.freight_price}}</text></view></block><block wx:if="{{detail.weight_price>0}}"><view class="item"><text class="t1">{{$root.m9}}</text><text class="t2 red">{{"¥"+detail.weight_price}}</text></view></block><block wx:if="{{detail.coupon_money>0}}"><view class="item"><text class="t1">{{$root.m10+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.coupon_money}}</text></view></block><block wx:if="{{detail.discount_rand_money>0}}"><view class="item"><text class="t1">随机立减</text><text class="t2 red">{{"-¥"+detail.discount_rand_money}}</text></view></block><block wx:if="{{detail.discount_money_admin>0}}"><view class="item"><text class="t1">商家优惠</text><text class="t2 red">{{"-¥"+detail.discount_money_admin}}</text></view></block><block wx:if="{{detail.scoredk_money>0}}"><view class="item"><text class="t1">{{$root.m11+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.scoredk_money}}</text></view></block><block wx:if="{{detail.dec_money>0}}"><view class="item"><text class="t1">{{$root.m12+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.dec_money}}</text></view></block><block wx:if="{{detail.silvermoneydec&&detail.silvermoneydec>0}}"><view class="item"><text class="t1">{{$root.m13+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.silvermoneydec}}</text></view></block><block wx:if="{{detail.goldmoneydec&&detail.goldmoneydec>0}}"><view class="item"><text class="t1">{{$root.m14+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.goldmoneydec}}</text></view></block><block wx:if="{{detail.dedamount_dkmoney&&detail.dedamount_dkmoney>0}}"><view class="item"><text class="t1">抵扣金抵扣</text><text class="t2 red">{{"-¥"+detail.dedamount_dkmoney}}</text></view></block><block wx:if="{{detail.shopscoredk_money>0}}"><view class="item"><text class="t1">{{$root.m15+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.shopscoredk_money}}</text></view></block><view class="item"><text class="t1">实付款</text><text class="t2 red"><block wx:if="{{showprice_dollar&&detail.usd_totalprice>0}}"><text>{{"$"+detail.usd_totalprice}}</text></block>{{'¥'+detail.totalprice+''}}<block wx:if="{{$root.m16}}"><text>{{'+ '+detail.service_fee_money+$root.m17}}</text></block></text></view><block wx:if="{{detail.combine_money&&detail.combine_money>0}}"><view class="item"><text class="t1">{{$root.m18+"已付"}}</text><text class="t2 red">{{"-¥"+detail.combine_money}}</text></view></block><block wx:if="{{detail.paytypeid==2&&detail.combine_wxpay&&detail.combine_wxpay>0}}"><view class="item"><text class="t1">微信已付</text><text class="t2 red">{{"-¥"+detail.combine_wxpay}}</text></view></block><block wx:if="{{(detail.paytypeid==3||detail.paytypeid>=302&&detail.paytypeid<=330)&&detail.combine_alipay&&detail.combine_alipay>0}}"><view class="item"><text class="t1">支付宝已付</text><text class="t2 red">{{"-¥"+detail.combine_alipay}}</text></view></block><block wx:if="{{detail.is_yuanbao_pay==1}}"><view class="item"><text class="t1">{{$root.m19}}</text><text class="t2 red">{{detail.total_yuanbao}}</text></view></block><view class="item"><text class="t1">订单状态</text><block wx:if="{{detail.status==0}}"><text class="t2">未付款</text></block><block wx:if="{{detail.status==1}}"><text class="t2">{{detail.paytypeid==4?'待发货':'已支付'}}</text></block><block wx:if="{{detail.status==2&&detail.express_isbufen==0}}"><text class="t2">已发货</text></block><block wx:if="{{detail.status==2&&detail.express_isbufen==1}}"><text class="t2">部分发货</text></block><block wx:if="{{detail.status==3}}"><text class="t2">已收货</text></block><block wx:if="{{detail.status==4}}"><text class="t2">已关闭</text></block><block wx:if="{{detail.status==8}}"><text class="t2">待提货</text></block></view><block wx:if="{{detail.refundingMoneyTotal>0}}"><view class="item"><text class="t1">退款中</text><text class="t2 red" data-url="{{'refundlist?orderid='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{"¥"+detail.refundingMoneyTotal}}</text><text class="t3 iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></block><block wx:if="{{detail.refundedMoneyTotal>0}}"><view class="item"><text class="t1">已退款</text><text class="t2 red" data-url="{{'refundlist?orderid='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{"¥"+detail.refundedMoneyTotal}}</text><text class="t3 iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></block><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款状态</text><block wx:if="{{detail.refund_status==1}}"><text class="t2 red">审核中</text></block><block wx:if="{{detail.refund_status==2}}"><text class="t2 red">已退款</text></block><block wx:if="{{detail.refund_status==3}}"><text class="t2 red">已驳回</text></block></view></block><block wx:if="{{detail.balance_price>0}}"><view class="item"><text class="t1">尾款</text><text class="t2 red">{{"¥"+detail.balance_price}}</text></view></block><block wx:if="{{detail.balance_price>0}}"><view class="item"><text class="t1">尾款状态</text><block wx:if="{{detail.balance_pay_status==1}}"><text class="t2">已支付</text></block><block wx:if="{{detail.balance_pay_status==0}}"><text class="t2">未支付</text></block></view></block></view><block wx:if="{{detail.checkmemid}}"><view class="orderinfo"><view class="item"><text class="t1">所选会员</text><text class="flex1"></text><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{detail.checkmember.headimg}}"></image><text style="height:80rpx;line-height:80rpx;">{{detail.checkmember.nickname}}</text></view></view></block><block wx:if="{{$root.g2>0}}"><view class="orderinfo"><block wx:for="{{detail.formdata}}" wx:for-item="item" wx:for-index="__i0__"><view class="item"><text class="t1">{{item[0]}}</text><block wx:if="{{item[2]=='upload'}}"><view class="t2"><image style="width:400rpx;height:auto;" src="{{item[1]}}" mode="widthFix" data-url="{{item[1]}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:else><block wx:if="{{item[2]=='upload_pics'}}"><block wx:for="{{item[1]}}" wx:for-item="picurl" wx:for-index="__i1__"><view class="t2"><image style="width:400rpx;height:auto;" src="{{picurl}}" mode="widthFix" data-url="{{picurl}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block></block><block wx:else><text class="t2" user-select="true" selectable="true">{{item[1]}}</text></block></block></view></block></view></block><block wx:if="{{detail.freight_type==11}}"><view class="orderinfo"><view class="item"><text class="t1">发货地址</text><text class="t2">{{"¥"+detail.freight_content.send_address+" - "+detail.freight_content.send_tel}}</text></view><view class="item"><text class="t1">收货地址</text><text class="t2">{{"¥"+detail.freight_content.receive_address+" - "+detail.freight_content.receive_tel}}</text></view></view></block><block wx:if="{{detail.isdygroupbuy==1}}"><view class="orderinfo"><view class="item"><text class="t1">抖音团购券信息</text><text class="t2">{{detail.dyorderids}}</text></view></view></block><block wx:if="{{detail.order_refund_tips}}"><view class="orderinfo refundtips"><textarea disabled="true" auto-height="true" value="{{detail.order_refund_tips}}"></textarea></view></block><block wx:if="{{show_product_xieyi&&product_xieyi}}"><view class="orderinfo"><view class="item"><text class="t1">商品协议</text><view class="t2"><block wx:for="{{$root.l1}}" wx:for-item="xieyi_item" wx:for-index="xieyi_index" wx:key="xieyi_index"><text data-event-opts="{{[['tap',[['showproductxieyi',[xieyi_index]]]]]}}" style="{{('color:'+xieyi_item.m20)}}" bindtap="__e">{{"《"+xieyi_item.$orig.name+"》"}}</text></block></view></view></view></block><view style="width:100%;height:calc(160rpx + env(safe-area-inset-bottom));"></view><block wx:if="{{fromfenxiao==0}}"><view class="bottom notabbarbot"><block wx:if="{{detail.payaftertourl&&detail.payafterbtntext}}"><block><view style="position:relative;"><block wx:if="{{detail.payafter_username}}"><block><view class="btn2">{{detail.payafterbtntext}}</view></block></block><block wx:else><block><view class="btn2" data-url="{{detail.payaftertourl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{detail.payafterbtntext}}</view></block></block></view></block></block><block wx:if="{{detail.isworkorder==1}}"><block><view class="btn2" data-url="{{'/pagesB/workorder/index?type=1&id='+detail.id}}" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">发起工单</view></block></block><block wx:if="{{detail.status!=4&&detail.transfer_order_parent_check}}"><block><view class="btn2" style="max-width:220rpx;" data-orderid="{{detail.id}}" data-event-opts="{{[['tap',[['transferOrder',['$event']]]]]}}" catchtap="__e">转给上级审核</view></block></block><block wx:if="{{detail.status==0}}"><block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['toclose',['$event']]]]]}}" bindtap="__e">关闭订单</view><block wx:if="{{detail.paytypeid!=5}}"><view class="btn1" style="{{'background:'+($root.m21)+';'}}" data-url="{{'/pagesExt/pay/pay?id='+detail.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">去付款</view></block><block wx:if="{{detail.paytypeid==5}}"><block><block wx:if="{{detail.transfer_check==1}}"><view class="btn1" style="{{'background:'+($root.m22)+';'}}" data-url="{{'/pages/pay/transfer?id='+detail.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">上传付款凭证</view></block><block wx:if="{{detail.transfer_check==0}}"><view class="btn1" style="{{'background:'+($root.m23)+';'}}">转账待审核</view></block><block wx:if="{{detail.transfer_check==-1}}"><view class="btn1" style="{{'background:'+($root.m24)+';'}}">转账已驳回</view></block></block></block></block></block><block wx:if="{{detail.status==1}}"><block><block wx:if="{{detail.paytypeid!='4'}}"><block><block wx:if="{{shopset.canrefund==1&&detail.order_can_refund==1&&detail.refundnum<detail.procount}}"><view class="btn2" data-url="{{'refundSelect?orderid='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">退款</view></block></block></block><block wx:else><block></block></block></block></block><block wx:if="{{(detail.status==2||detail.status==3)&&detail.freight_type!=3&&detail.freight_type!=4}}"><block><block wx:if="{{detail.express_type=='express_wx'}}"><view class="btn2" data-express_type="{{detail.express_type}}" data-express_com="{{detail.express_com}}" data-express_no="{{detail.express_no}}" data-express_content="{{detail.express_content}}" data-event-opts="{{[['tap',[['logistics',['$event']]]]]}}" bindtap="__e">订单跟踪</view></block><block wx:else><view class="btn2" data-express_type="{{detail.express_type}}" data-express_com="{{detail.express_com}}" data-express_no="{{detail.express_no}}" data-express_content="{{detail.express_content}}" data-event-opts="{{[['tap',[['logistics',['$event']]]]]}}" bindtap="__e">查看物流</view></block></block></block><block wx:if="{{$root.g3}}"><block><view class="btn2" data-url="{{'invoice?type=shop&orderid='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">发票</view></block></block><block wx:if="{{detail.is_pingce==1&&(detail.status==1||detail.status==2||detail.status==3)}}"><block><block wx:if="{{detail.pingce_status==2}}"><view class="btn1" style="{{'background:'+($root.m25)+';'}}" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['viewReport',['$event']]]]]}}" bindtap="__e">查看报告</view></block><block wx:else><view class="btn1" style="{{'background:'+($root.m26)+';'}}" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['toevaluate',['$event']]]]]}}" bindtap="__e">继续测评</view></block></block></block><block wx:if="{{detail.status==2}}"><block><block wx:if="{{detail.paytypeid!='4'}}"><block><block wx:if="{{shopset.canrefund==1&&detail.order_can_refund==1&&detail.refundnum<detail.procount}}"><view class="btn2" data-url="{{'refundSelect?orderid='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">退款</view></block></block></block><block wx:if="{{detail.can_collect&&detail.paytypeid!='4'&&(detail.balance_pay_status==1||detail.balance_price==0)}}"><view class="btn1" style="{{'background:'+($root.m27)+';'}}" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['orderCollect',['$event']]]]]}}" bindtap="__e">确认收货</view></block><block wx:if="{{!detail.can_collect&&detail.paytypeid!='4'&&(detail.balance_pay_status==1||detail.balance_price==0)}}"><view class="btn1" style="background:#bbb;">运输中</view></block><block wx:if="{{detail.balance_pay_status==0&&detail.balance_price>0}}"><view class="btn1" style="{{'background:'+($root.m28)+';'}}" data-url="{{'/pagesExt/pay/pay?id='+detail.balance_pay_orderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">支付尾款</view></block></block></block><block wx:if="{{(detail.status==1||detail.status==2||detail.status==8)&&(detail.freight_type==1||detail.freight_type==5)&&detail.hexiao_qr}}"><block><view class="btn2" data-hexiao_qr="{{detail.hexiao_qr}}" data-event-opts="{{[['tap',[['showhxqr',['$event']]]]]}}" bindtap="__e">核销码</view></block></block><block wx:if="{{detail.refundCount}}"><view class="btn2" data-url="{{'refundlist?orderid='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">售后详情</view></block><block wx:if="{{detail.ishand==1}}"><block><block wx:if="{{detail.status==3}}"><block><block wx:if="{{detail.canhand&&detail.hand_num<detail.procount}}"><view class="btn2" data-url="{{'/pagesA/handwork/hand?orderid='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">回寄</view></block></block></block><block wx:if="{{detail.handCount}}"><view class="btn2" data-url="{{'/pagesA/handwork/handlist?orderid='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">查看回寄</view></block></block></block><block wx:if="{{detail.status==3||detail.status==4}}"><block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" bindtap="__e">删除订单</view></block></block><block wx:if="{{detail.shop_order_exchange_product&&(detail.status==3||detail.status==2)}}"><block><view class="btn2" style="background-color:#1A1A1A;color:#fff;" data-url="{{'refundSelect?orderid='+detail.id+'&type=exchange'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">换货</view></block></block><block wx:if="{{detail.bid>0&&detail.status==3}}"><block><block wx:if="{{iscommentdp==0}}"><view class="btn1" style="{{'background:'+($root.m29)+';'}}" data-url="{{'/pagesExt/order/commentdp?orderid='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">评价店铺</view></block><block wx:if="{{iscommentdp==1}}"><view class="btn2" data-url="{{'/pagesExt/order/commentdp?orderid='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看评价</view></block></block></block></view></block><uni-popup class="vue-ref" vue-id="1676bdc0-1" id="dialogHxqr" type="dialog" data-ref="dialogHxqr" bind:__l="__l" vue-slots="{{['default']}}"><view class="hxqrbox"><image class="img" src="{{hexiao_qr}}" data-url="{{hexiao_qr}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">请出示核销码给核销员进行核销</view><block wx:if="{{detail.hexiao_code_member}}"><view><input style="border:1px #eee solid;padding:10rpx;margin:20rpx 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;" type="number" placeholder="请输入核销密码" data-event-opts="{{[['input',[['set_hexiao_code_member',['$event']]]]]}}" bindinput="__e"/><button data-event-opts="{{[['tap',[['hexiao',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m30)+';'}}" bindtap="__e">确定</button></view></block><view data-event-opts="{{[['tap',[['closeHxqr',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="{{pre_url+'/static/img/close2.png'}}"></image></view></view></uni-popup><uni-popup class="vue-ref" vue-id="1676bdc0-2" id="dialogSelectExpress" type="dialog" data-ref="dialogSelectExpress" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{express_content}}"><view style="background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx;"><block wx:for="{{express_content}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="sendexpress" style="border-bottom:1px solid #f5f5f5;padding:20rpx 0;"><view class="sendexpress-item" style="display:flex;" data-url="{{'/pagesExt/order/logistics?express_com='+item.express_com+'&express_no='+item.express_no}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="flex1" style="color:#121212;">{{item.express_com+" - "+item.express_no}}</view><image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view><block wx:if="{{item.express_oglist}}"><view style="margin-top:20rpx;"><block wx:for="{{item.express_oglist}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><view class="oginfo-item" style="display:flex;align-items:center;margin-bottom:10rpx;"><image style="width:50rpx;height:50rpx;margin-right:10rpx;flex-shrink:0;" src="{{item2.pic}}"></image><view class="flex1" style="color:#555;">{{item2.name+"("+item2.ggname+")"}}</view></view></block></view></block></view></block></view></block></uni-popup><block wx:if="{{selecthxnumDialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hideSelecthxnumDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择核销数量</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hideSelecthxnumDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="pstime-item" data-index="{{index}}" data-event-opts="{{[['tap',[['hxnumRadioChange',['$event']]]]]}}" bindtap="__e"><view class="flex1">{{item.$orig}}</view><view class="radio" style="{{(hxnum==item.$orig?'background:'+item.m31+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></view></view></view></block><block wx:if="{{showproxieyi}}"><view class="xieyibox"><view class="xieyibox-content"><view style="overflow:scroll;height:100%;"><parse vue-id="1676bdc0-3" content="{{proxieyi_content}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view><view data-event-opts="{{[['tap',[['hideproxieyi',['$event']]]]]}}" style="{{'position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center;width:50%;height:60rpx;line-height:60rpx;color:#fff;border-radius:8rpx;'+('background:'+('linear-gradient(90deg,'+$root.m32+' 0%,rgba('+$root.m33+',0.8) 100%)')+';')}}" bindtap="__e">确定</view></view></view></block><block wx:if="{{reportShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['changeReportDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">查看报告</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['changeReportDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:if="{{reportArr.bolePsyReport}}"><view class="clist-item" data-url="{{reportArr.bolePsyReport}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="flex1">32种人才心理特质报告</view><view class="radio"><image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></block><block wx:if="{{reportArr.bolePostfitReport}}"><view class="clist-item" data-url="{{reportArr.bolePostfitReport}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="flex1">42种职场岗位适配报</view><view><image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></block></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="1676bdc0-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="1676bdc0-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="1676bdc0-6" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="1676bdc0-7" bind:__l="__l"></wxxieyi></view>