<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="content2"><view class="item2"><view class="f1">接收人ID</view></view><view class="item3"><view class="f2"><input class="input" type="number" name="mid" value="" placeholder="请输入接收人ID" placeholder-style="color:#999;font-size:36rpx"/></view></view><view class="item4" style="height:1rpx;"></view><view class="item2"><view class="f1">转赠数量</view></view><view class="item3"><view class="f2"><input class="input" type="number" name="integral" value="" placeholder="请输入转赠数量" placeholder-style="color:#999;font-size:36rpx" data-event-opts="{{[['input',[['moneyinput',['$event']]]]]}}" bindinput="__e"/></view></view><view class="item4" style="height:170rpx;line-height:50rpx;padding:10rpx 0;"><view style="margin-right:10rpx;">{{'您的当前'+$root.m0+"："+myyuanbao+''}}<block wx:if="{{yuanbao_money_ratio}}"><view>{{'还需要支付'+yuanbao_money_ratio+'%比例的现金'}}</view></block><view>转赠后不可退回</view></view></view></view><button class="btn" style="{{'background:'+($root.m1)+';'}}" form-type="submit">转账</button><view class="text-center" data-url="/pages/my/usercenter" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>{{"返回"+$root.m2+"中心"}}</text></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="e4b0fd80-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="e4b0fd80-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>