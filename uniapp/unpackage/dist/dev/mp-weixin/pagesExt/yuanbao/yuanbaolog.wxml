<view class="container"><block wx:if="{{isload}}"><block><view class="myyuanbao" style="{{'background:'+($root.m0)+';'}}"><view class="f1">{{"我的"+$root.m1}}</view><view class="f2">{{myyuanbao}}<block wx:if="{{yuanbaoTransfer}}"><view class="btn-mini" data-url="/pagesExt/yuanbao/yuanbaoTransfer" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">转账</view></block></view></view><view class="content" id="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item"><view class="f1"><text class="t1">{{item.remark}}</text><text class="t2">{{item.createtime}}</text></view><view class="f2"><block wx:if="{{item.yuanbao>0}}"><block><text class="t1">{{"+"+item.yuanbao}}</text></block></block><block wx:else><block><text class="t2">{{item.yuanbao}}</text></block></block></view></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="d8e15ce6-1" bind:__l="__l"></nodata></block></view><block wx:if="{{nomore}}"><nomore vue-id="d8e15ce6-2" bind:__l="__l"></nomore></block></block></block><block wx:if="{{loading}}"><loading vue-id="d8e15ce6-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="d8e15ce6-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="d8e15ce6-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>