
.top{width:100%;display:flex;flex-direction:column;align-items:center;padding-top:60rpx}
.top .f1{height:60rpx;line-height:60rpx;color:#939393;font-size:24rpx;}
.top .f2{color:#101010;font-weight:bold;font-size:72rpx;height:120rpx;line-height:120rpx}
.top .f2 .t1{font-size:44rpx}
.top .f2 .t3{font-size:50rpx}
.top .f3{color:#FC5729;font-size:26rpx;height:70rpx;line-height:70rpx;display: flex;align-items: center;}
.paytype{width:94%;margin:20rpx 3% 80rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;margin-top:20rpx;background:#fff}
.paytype .f1{height:100rpx;line-height:100rpx;padding:0 30rpx;color:#333333;font-weight:bold}
.paytype .f2{padding:0 30rpx}
.paytype .f2 .item{border-bottom:1px solid #f5f5f5;height:100rpx;display:flex;align-items:center}
.paytype .f2 .item:last-child{border-bottom:0}
.paytype .f2 .item .t1{flex:1;display:flex;align-items:center;color:#222222;font-size:30rpx;font-weight:bold}
.paytype .f2 .item .t1 .img{width:44rpx;height:44rpx;margin-right:40rpx}
.paytype .f2 .item .radio{flex-shrink:0;width: 36rpx;height: 36rpx;background: #FFFFFF;border: 3rpx solid #BFBFBF;border-radius: 50%;margin-right:10rpx}
.paytype .f2 .item .radio .radio-img{width:100%;height:100%}
.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:10rpx;margin-top:30rpx;color: #fff;font-size: 30rpx;font-weight:bold}
.daifu-btn{background: #fc5729;}
.op{width:94%;margin:20rpx 3%;display:flex;align-items:center;margin-top:40rpx}
.op .btn{flex:1;height:100rpx;line-height:100rpx;background:#07C160;width:90%;margin:0 10rpx;border-radius:10rpx;color: #fff;font-size:28rpx;font-weight:bold;display:flex;align-items:center;justify-content:center}
.op .btn .img{width:48rpx;height:48rpx;margin-right:20rpx}
/* 广告位 */
.ad-box{width: 94%; margin: 30rpx 3% 0 3%;background: #FFFFFF;border-radius: 10rpx;padding: 20rpx;}
.ad-item{width: 100%;display: flex;justify-content: center;margin-bottom: 20rpx;border-radius: 10rpx;}
.ad-item image{border-radius: 12rpx;width: 100%;}
.ad-item:last-child{margin-bottom: 0;}
.give-coupon .coupon-coupon .pt_right{padding: 20rpx;}
.give-coupon .coupon-coupon .pt_right .f1 .t2{height: unset;line-height: unset;}
.give-coupon .coupon-coupon .pt_right .f1 .t4{ max-width: 310rpx; overflow: hidden;  text-overflow: ellipsis; white-space: nowrap;}
.uni-popup-dialog {width: 300px;border-radius: 5px;background-color: #fff;}
.uni-dialog-title {display: flex;flex-direction: row;justify-content: center;padding-top: 15px;padding-bottom: 5px;}
.uni-dialog-title-text {font-size: 16px;font-weight: 500;}
.uni-dialog-content {display: flex;flex-direction: row;justify-content: center;align-items: center;padding: 5px 15px 15px 15px;}
.uni-dialog-content-text {font-size: 14px;color: #6e6e6e;}
.uni-dialog-button-group {display: flex;flex-direction: row;border-top-color: #f5f5f5;border-top-style: solid;border-top-width: 1px;}
.uni-dialog-button {display: flex;flex: 1;flex-direction: row;justify-content: center;align-items: center;height: 45px;}
.uni-border-left {border-left-color: #f0f0f0;border-left-style: solid;border-left-width: 1px;}
.uni-dialog-button-text {font-size: 14px;}
.uni-button-color {color: #007aff;}
.posterShare { position: fixed;display: flex; z-index: 99; width: 750rpx; height: 100%; left: 0;top:0;justify-content: center;background: rgba(0,0,0,0.8);
}
.posterButton{width: 240rpx;height: 75rpx;line-height: 74rpx;text-align: center;border-radius: 50rpx;border: 1px solid #fff;color: #fff;font-weight: bold;letter-spacing: 15rpx;}
.uni-popup-dialog .img{width:80%}

