<view class="container"><block wx:if="{{isload}}"><block><view class="topbg" style="{{'background:'+($root.m0)+';'}}"><view class="headimg"><image src="{{order_member.headimg}}"></image></view><view><text class="nickname">{{order_member.nickname+''}}</text>发起了代付请求~</view></view><view class="box box-price"><view class="top"><view class="f1">需付款</view><block wx:if="{{payorder.score==0}}"><view class="f2"><text class="t1">￥</text><text class="t2">{{payorder.money}}</text></view></block><block wx:else><block wx:if="{{payorder.money>0&&payorder.score>0}}"><view class="f2"><text class="t1">￥</text><text class="t2">{{payorder.money}}</text><text style="font-size:28rpx;">{{'+\n\t\t\t\t\t\t'+payorder.score+$root.m1}}</text></view></block><block wx:else><view class="f2"><text class="t3">{{payorder.score+$root.m2}}</text></view></block></block></view><view class="paytype"><block wx:if="{{payorder.money==0&&payorder.score>0}}"><block><view class="f2"><block wx:if="{{moneypay==1}}"><view class="item" data-typeid="1" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1 flex"><image class="img" src="{{pre_url+'/static/img/pay-money.png'}}"></image><view class="flex-col"><text>{{$root.m3+"支付"}}</text><view style="font-size:22rpx;font-weight:normal;">{{"剩余"+$root.m4}}<text style="color:#FC5729;">{{userinfo.score}}</text></view></view></view><view class="radio" style="{{(typeid=='1'?'background:'+$root.m5+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></view></block></block><block wx:else><block><view class="f2"><block wx:if="{{wxpay==1&&(wxpay_type==0||wxpay_type==1||wxpay_type==2||wxpay_type==3)}}"><view class="item" data-typeid="2" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-weixin.png'}}"></image>微信支付</view><view class="radio" style="{{(typeid=='2'?'background:'+$root.m6+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{wxpay==1&&wxpay_type==22}}"><view class="item" data-typeid="22" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-weixin.png'}}"></image>微信支付</view><view class="radio" style="{{(typeid=='22'?'background:'+$root.m7+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{alipay==2}}"><view class="item" data-typeid="23" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-alipay.png'}}"></image>支付宝支付</view><view class="radio" style="{{(typeid=='23'?'background:'+$root.m8+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{alipay==1}}"><view class="item" data-typeid="3" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-alipay.png'}}"></image>支付宝支付</view><view class="radio" style="{{(typeid=='3'?'background:'+$root.m9+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{more_alipay==1}}"><block><block wx:if="{{alipay2==1}}"><view class="item" data-typeid="31" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-alipay.png'}}"></image>支付宝支付2</view><view class="radio" style="{{(typeid=='31'?'background:'+$root.m10+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{alipay3==1}}"><view class="item" data-typeid="32" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-alipay.png'}}"></image>支付宝支付3</view><view class="radio" style="{{(typeid=='32'?'background:'+$root.m11+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></block></block><block wx:if="{{baidupay==1}}"><view class="item" data-typeid="11" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/pay-money.png'}}"></image>在线支付</view><view class="radio" style="{{(typeid=='11'?'background:'+$root.m12+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{toutiaopay==1}}"><view class="item" data-typeid="12" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/pay-money.png'}}"></image>在线支付</view><view class="radio" style="{{(typeid=='12'?'background:'+$root.m13+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{moneypay==1}}"><view class="item" data-typeid="1" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1 flex"><image class="img" src="{{pre_url+'/static/img/pay-money.png'}}"></image><view class="flex-col"><text>{{$root.m14+"支付"}}</text><view style="font-size:22rpx;font-weight:normal;">可用余额<text style="color:#FC5729;">{{"￥"+userinfo.money}}</text></view></view></view><view class="radio" style="{{(typeid=='1'?'background:'+$root.m15+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></view></block></block></view><view><block wx:if="{{typeid!='0'}}"><button data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m16)+';'}}" bindtap="__e">帮好友支付</button></block><uni-popup class="vue-ref" vue-id="dfeafed4-1" id="dialogInput" type="dialog" data-ref="dialogInput" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('dfeafed4-2')+','+('dfeafed4-1')}}" mode="password" title="支付密码" value="" placeholder="请输入支付密码" data-event-opts="{{[['^confirm',[['getpwd']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></view></view><block wx:if="{{$root.g0>0}}"><view class="box goods"><block wx:for="{{order_goods}}" wx:for-item="bitem" wx:for-index="bindex" wx:key="bindex"><view class="bitem"><view class="box-title">{{bitem.bname}}</view><block wx:for="{{bitem.goodslist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="goods-item"><view class="left"><image class="proimg" src="{{item.pic}}"></image></view><view class="right"><view><view class="title">{{item.name}}</view><block wx:if="{{item.ggname}}"><view class="hui">{{item.ggname}}</view></block><view class="hui">{{"×"+item.num}}</view></view><view><view class="title">{{"￥"+(item.real_totalprice?item.real_totalprice:item.totalprice)}}</view><block wx:if="{{item.real_totalprice&&item.real_totalprice!=item.totalprice}}"><view class="hui line">{{"￥"+item.totalprice}}</view></block></view></view></view></block></view></block></view></block><block wx:if="{{daifu_desc}}"><view class="daifu_desc"><view class="title">说明</view><textarea value="{{daifu_desc}}"></textarea></view></block><view style="height:40rpx;"></view><block wx:if="{{give_coupon_show}}"><block><view class="give-coupon flex-x-center flex-y-center"><view class="coupon-block"><image style="width:630rpx;height:330rpx;" src="{{pre_url+'/static/img/coupon-top.png'}}"></image><view class="coupon-del flex-x-center flex-y-center" data-url="{{give_coupon_close_url}}" data-event-opts="{{[['tap',[['give_coupon_close',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/coupon-del.png'}}"></image></view><view class="flex-x-center"><view class="coupon-info"><view class="flex-x-center coupon-get">{{"获得"+give_coupon_num+"张"+$root.m17}}</view><view style="background:#f5f5f5;padding:10rpx 0;"><block wx:for="{{give_coupon_list}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><block wx:if="{{index<3}}"><block><view class="coupon-coupon"><view class="{{[item.type==1?'pt_img1':'pt_img2']}}"></view><view class="{{['pt_left',item.type==1?'':'bg2']}}"><block wx:if="{{item.type==1}}"><view class="f1"><text class="t0">￥</text><text class="t1">{{item.money}}</text></view></block><block wx:if="{{item.type==2}}"><view class="f1">礼品券</view></block><block wx:if="{{item.type==3}}"><view class="f1"><text class="t1">{{item.limit_count}}</text><text class="t2">次</text></view></block><block wx:if="{{item.type==4}}"><view class="f1">抵运费</view></block><block wx:if="{{item.type==1||item.type==4}}"><view class="f2"><block wx:if="{{item.minprice>0}}"><text>{{"满"+item.minprice+"元可用"}}</text></block><block wx:else><text>无门槛</text></block></view></block></view><view class="pt_right"><view class="f1"><view class="t1">{{item.name}}</view><block wx:if="{{item.type==1}}"><view class="t2">代金券</view></block><block wx:if="{{item.type==2}}"><view class="t2">礼品券</view></block><block wx:if="{{item.type==3}}"><view class="t2">计次券</view></block><block wx:if="{{item.type==4}}"><view class="t2">运费抵扣券</view></block></view></view><block wx:if="{{item.givenum>1}}"><view class="coupon_num">{{"×"+item.givenum+''}}</view></block></view></block></block></block></block></view><view class="flex-x-center coupon-btn" data-url="/pagesExt/coupon/mycoupon" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">前往查看</view></view></view></view></view></block></block><uni-popup class="vue-ref" vue-id="dfeafed4-3" id="dialogOpenWeapp" type="dialog" maskClick="{{false}}" data-ref="dialogOpenWeapp" bind:__l="__l" vue-slots="{{['default']}}"><view style="background:#fff;padding:50rpx;position:relative;border-radius:20rpx;"><view style="height:80px;line-height:80px;width:200px;margin:0 auto;font-size:18px;text-align:center;font-weight:bold;color:#333;">恭喜您支付成功</view><view style="height:50px;line-height:50px;width:200px;margin:0 auto;border-radius:5px;color:#66f;font-size:14px;text-align:center;" data-url="{{detailurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看订单详情</view></view></uni-popup><uni-popup class="vue-ref" vue-id="dfeafed4-4" id="dialogPayconfirm" type="dialog" maskClick="{{false}}" data-ref="dialogPayconfirm" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('dfeafed4-5')+','+('dfeafed4-4')}}" type="info" title="支付确认" content="是否已完成支付" data-event-opts="{{[['^confirm',[['PayconfirmFun']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></block></block><block wx:if="{{invite_status&&invite_free}}"><view data-event-opts="{{[['tap',[['closeInvite',['$event']]]]]}}" style="width:100%;height:100%;background-color:#000;position:fixed;opacity:0.5;z-index:99;top:0;" bindtap="__e"></view></block><block wx:if="{{invite_status&&invite_free}}"><view style="width:700rpx;margin:0 auto;position:fixed;top:10%;left:25rpx;z-index:100;"><view data-event-opts="{{[['tap',[['gotoInvite',['$event']]]]]}}" style="background-color:#fff;border-radius:20rpx;overflow:hidden;width:100%;min-height:700rpx;" bindtap="__e"><image style="width:100%;height:auto;" src="{{invite_free.pic}}" mode="widthFix"></image></view><block wx:if="{{invite_status&&invite_free}}"><view data-event-opts="{{[['tap',[['closeInvite',['$event']]]]]}}" style="width:80rpx;height:80rpx;line-height:80rpx;text-align:center;font-size:30rpx;background-color:#fff;margin:0 auto;border-radius:50%;margin-top:20rpx;" bindtap="__e">X</view></block></view></block><block wx:if="{{loading}}"><loading vue-id="dfeafed4-6" bind:__l="__l"></loading></block><dp-tabbar vue-id="dfeafed4-7" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="dfeafed4-8" data-ref="popmsg" bind:__l="__l"></popmsg></view>