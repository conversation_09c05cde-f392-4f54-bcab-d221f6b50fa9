require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pagesExt/pay/daifu"],{

/***/ 1309:
/*!******************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/main.js?{"page":"pagesExt%2Fpay%2Fdaifu"} ***!
  \******************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _daifu = _interopRequireDefault(__webpack_require__(/*! ./pagesExt/pay/daifu.vue */ 1310));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_daifu.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 1310:
/*!***********************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/pagesExt/pay/daifu.vue ***!
  \***********************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _daifu_vue_vue_type_template_id_2defce70___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./daifu.vue?vue&type=template&id=2defce70& */ 1311);
/* harmony import */ var _daifu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./daifu.vue?vue&type=script&lang=js& */ 1313);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _daifu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _daifu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _daifu_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./daifu.vue?vue&type=style&index=0&lang=css& */ 1315);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 36);

var renderjs





/* normalize component */

var component = Object(_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _daifu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _daifu_vue_vue_type_template_id_2defce70___WEBPACK_IMPORTED_MODULE_0__["render"],
  _daifu_vue_vue_type_template_id_2defce70___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _daifu_vue_vue_type_template_id_2defce70___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pagesExt/pay/daifu.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 1311:
/*!******************************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/pagesExt/pay/daifu.vue?vue&type=template&id=2defce70& ***!
  \******************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_daifu_vue_vue_type_template_id_2defce70___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./daifu.vue?vue&type=template&id=2defce70& */ 1312);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_daifu_vue_vue_type_template_id_2defce70___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_daifu_vue_vue_type_template_id_2defce70___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_daifu_vue_vue_type_template_id_2defce70___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_daifu_vue_vue_type_template_id_2defce70___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 1312:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!/Users/<USER>/Downloads/tcphp/uniapp/pagesExt/pay/daifu.vue?vue&type=template&id=2defce70& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniPopup: function () {
      return Promise.all(/*! import() | components/uni-popup/uni-popup */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/uni-popup/uni-popup")]).then(__webpack_require__.bind(null, /*! @/components/uni-popup/uni-popup.vue */ 7096))
    },
    uniPopupDialog: function () {
      return __webpack_require__.e(/*! import() | components/uni-popup-dialog/uni-popup-dialog */ "components/uni-popup-dialog/uni-popup-dialog").then(__webpack_require__.bind(null, /*! @/components/uni-popup-dialog/uni-popup-dialog.vue */ 7240))
    },
    loading: function () {
      return __webpack_require__.e(/*! import() | components/loading/loading */ "components/loading/loading").then(__webpack_require__.bind(null, /*! @/components/loading/loading.vue */ 7131))
    },
    dpTabbar: function () {
      return __webpack_require__.e(/*! import() | components/dp-tabbar/dp-tabbar */ "components/dp-tabbar/dp-tabbar").then(__webpack_require__.bind(null, /*! @/components/dp-tabbar/dp-tabbar.vue */ 7110))
    },
    popmsg: function () {
      return __webpack_require__.e(/*! import() | components/popmsg/popmsg */ "components/popmsg/popmsg").then(__webpack_require__.bind(null, /*! @/components/popmsg/popmsg.vue */ 7124))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.isload ? _vm.t("color1") : null
  var m1 =
    _vm.isload &&
    !(_vm.payorder.score == 0) &&
    _vm.payorder.money > 0 &&
    _vm.payorder.score > 0
      ? _vm.t("积分")
      : null
  var m2 =
    _vm.isload &&
    !(_vm.payorder.score == 0) &&
    !(_vm.payorder.money > 0 && _vm.payorder.score > 0)
      ? _vm.t("积分")
      : null
  var m3 =
    _vm.isload &&
    _vm.payorder.money == 0 &&
    _vm.payorder.score > 0 &&
    _vm.moneypay == 1
      ? _vm.t("积分")
      : null
  var m4 =
    _vm.isload &&
    _vm.payorder.money == 0 &&
    _vm.payorder.score > 0 &&
    _vm.moneypay == 1
      ? _vm.t("积分")
      : null
  var m5 =
    _vm.isload &&
    _vm.payorder.money == 0 &&
    _vm.payorder.score > 0 &&
    _vm.moneypay == 1 &&
    _vm.typeid == "1"
      ? _vm.t("color1")
      : null
  var m6 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.wxpay == 1 &&
    (_vm.wxpay_type == 0 ||
      _vm.wxpay_type == 1 ||
      _vm.wxpay_type == 2 ||
      _vm.wxpay_type == 3) &&
    _vm.typeid == "2"
      ? _vm.t("color1")
      : null
  var m7 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.wxpay == 1 &&
    _vm.wxpay_type == 22 &&
    _vm.typeid == "22"
      ? _vm.t("color1")
      : null
  var m8 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.alipay == 2 &&
    _vm.typeid == "23"
      ? _vm.t("color1")
      : null
  var m9 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.alipay == 1 &&
    _vm.typeid == "3"
      ? _vm.t("color1")
      : null
  var m10 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.more_alipay == 1 &&
    _vm.alipay2 == 1 &&
    _vm.typeid == "31"
      ? _vm.t("color1")
      : null
  var m11 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.more_alipay == 1 &&
    _vm.alipay3 == 1 &&
    _vm.typeid == "32"
      ? _vm.t("color1")
      : null
  var m12 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.baidupay == 1 &&
    _vm.typeid == "11"
      ? _vm.t("color1")
      : null
  var m13 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.toutiaopay == 1 &&
    _vm.typeid == "12"
      ? _vm.t("color1")
      : null
  var m14 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.moneypay == 1
      ? _vm.t("余额")
      : null
  var m15 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.moneypay == 1 &&
    _vm.typeid == "1"
      ? _vm.t("color1")
      : null
  var m16 = _vm.isload && _vm.typeid != "0" ? _vm.t("color1") : null
  var g0 = _vm.isload ? _vm.order_goods.length : null
  var m17 = _vm.isload && _vm.give_coupon_show ? _vm.t("优惠券") : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        m2: m2,
        m3: m3,
        m4: m4,
        m5: m5,
        m6: m6,
        m7: m7,
        m8: m8,
        m9: m9,
        m10: m10,
        m11: m11,
        m12: m12,
        m13: m13,
        m14: m14,
        m15: m15,
        m16: m16,
        g0: g0,
        m17: m17,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 1313:
/*!************************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/pagesExt/pay/daifu.vue?vue&type=script&lang=js& ***!
  \************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_daifu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./daifu.vue?vue&type=script&lang=js& */ 1314);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_daifu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_daifu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_daifu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_daifu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_daifu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1314:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!/Users/<USER>/Downloads/tcphp/uniapp/pagesExt/pay/daifu.vue?vue&type=script&lang=js& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni, wx) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

var app = getApp();
var _default = {
  data: function data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      pre_url: app.globalData.pre_url,
      detailurl: '',
      tourl: '',
      typeid: '0',
      wxpay: 0,
      wxpay_type: 0,
      alipay: 0,
      baidupay: 0,
      toutiaopay: 0,
      moneypay: 0,
      cancod: 0,
      pay_month: 0,
      pay_transfer: 0,
      codtxt: '',
      pay_month_txt: '',
      give_coupon_list: [],
      give_coupon_num: 0,
      userinfo: [],
      paypwd: '',
      hiddenmodalput: true,
      payorder: {},
      tmplids: [],
      give_coupon_show: false,
      give_coupon_close_url: "",
      more_alipay: 0,
      alipay2: 0,
      alipay3: 0,
      open_pay: false,
      //打开支付选项
      pay_type: '',
      //支付类型（新增）

      order_member: [],
      order_goods: [],
      invite_free: '',
      invite_status: false,
      free_tmplids: '',
      sharepic: app.globalData.initdata.logo,
      daifu_desc: ''
    };
  },
  onShareAppMessage: function onShareAppMessage() {
    sharedata.summary = '您有一份好友代付待查收，请尽快处理！';
    return this._sharewx({
      title: '您的好友向您发出了代付请求',
      pic: this.sharepic
    });
  },
  onShareTimeline: function onShareTimeline() {
    var sharewxdata = this._sharewx({
      title: '您有一份好友代付待查收，请尽快处理！',
      pic: this.sharepic
    });
    var query = sharewxdata.path.split('?')[1] + '&seetype=circle';
    console.log(sharewxdata);
    console.log(query);
    return {
      title: sharewxdata.title,
      imageUrl: sharewxdata.imageUrl,
      query: query
    };
  },
  onShow: function onShow() {
    if (app.globalData.platform == 'wx' && app.globalData.hide_home_button == 1) {
      uni.hideHomeButton();
    }
  },
  onLoad: function onLoad(opt) {
    this.opt = app.getopts(opt);
    if (this.opt.tourl) this.tourl = decodeURIComponent(this.opt.tourl);
    this.getdata();
  },
  onPullDownRefresh: function onPullDownRefresh() {
    this.getdata();
  },
  methods: {
    getdata: function getdata() {
      var that = this;
      that.loading = true;
      var thisurl = '';
      if (app.globalData.platform == 'mp' || app.globalData.platform == 'h5') {
        thisurl = location.href;
      }
      app.post('ApiPay/daifu', {
        orderid: that.opt.id,
        thisurl: thisurl,
        tourl: that.tourl,
        scene: app.globalData.scene
      }, function (res) {
        that.loading = false;
        if (res.status == 0) {
          app.error(res.msg);
          return;
        }
        that.wxpay = res.wxpay;
        that.wxpay_type = res.wxpay_type;
        that.alipay = res.alipay;
        that.baidupay = res.baidupay;
        that.toutiaopay = res.toutiaopay;
        that.cancod = res.cancod;
        that.codtxt = res.codtxt;
        that.pay_money = res.pay_money;
        that.pay_money_txt = res.pay_money_txt;
        that.moneypay = res.moneypay;
        that.pay_transfer = res.pay_transfer;
        that.pay_transfer_info = res.pay_transfer_info;
        that.pay_month = res.pay_month;
        that.pay_month_txt = res.pay_month_txt;
        that.payorder = res.payorder;
        that.userinfo = res.userinfo;
        that.order_member = res.order_member;
        that.order_goods = res.order_goods;
        that.tmplids = res.tmplids;
        that.give_coupon_list = res.give_coupon_list;
        that.daifu_desc = res.daifu_desc;
        if (that.give_coupon_list) {
          for (var i in that.give_coupon_list) {
            that.give_coupon_num += that.give_coupon_list[i]['givenum'];
          }
        }
        that.detailurl = res.detailurl;
        that.tourl = res.tourl;
        that.more_alipay = res.more_alipay;
        that.alipay2 = res.alipay2;
        that.alipay3 = res.alipay3;
        if (that.wxpay) {
          if (that.wxpay_type == 22) {
            that.typeid = 22;
          } else {
            that.typeid = 2;
          }
        } else if (that.moneypay) {
          that.typeid = 1;
        } else if (that.alipay) {
          that.typeid = 3;
          if (that.alipay == 2) {
            that.typeid = 23;
          }
        } else if (that.more_alipay) {
          if (that.alipay2) {
            that.typeid = 31;
          }
          if (that.alipay3) {
            that.typeid = 32;
          }
        } else if (that.baidupay) {
          that.typeid = 11;
        } else if (that.toutiaopay) {
          that.typeid = 12;
        }
        if (that.payorder.money == 0 && that.payorder.score > 0) {
          that.typeid = 1;
        }
        if (res.invite_free) {
          that.invite_free = res.invite_free;
        }
        if (res.free_tmplids) {
          that.free_tmplids = res.free_tmplids;
        }
        uni.setNavigationBarTitle({
          title: res.daifu_txt
        });
        that.loaded();
      });
    },
    getpwd: function getpwd(done, val) {
      this.paypwd = val;
      this.topay({
        currentTarget: {
          dataset: {
            typeid: 1
          }
        }
      });
    },
    changeradio: function changeradio(e) {
      var that = this;
      var typeid = e.currentTarget.dataset.typeid;
      that.typeid = typeid;
      console.log(typeid);
    },
    topay: function topay(e) {
      var that = this;
      var typeid = that.typeid;
      var orderid = this.payorder.id;
      if (typeid == 1) {
        //余额支付
        if (that.userinfo.haspwd && that.paypwd == '') {
          that.$refs.dialogInput.open();
          return;
        }
        app.confirm('确定用' + that.t('余额') + '支付吗?', function () {
          app.showLoading('提交中');
          app.post('ApiPay/daifu', {
            op: 'submit',
            orderid: orderid,
            typeid: typeid,
            paypwd: that.paypwd,
            pay_type: that.pay_type
          }, function (res) {
            app.showLoading(false);
            if (res.status == 0) {
              app.error(res.msg);
              return;
            }
            if (res.status == 2) {
              app.success(res.msg);
              that.subscribeMessage(function () {
                if (that.invite_free) {
                  that.invite_status = true;
                } else {
                  setTimeout(function () {
                    if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                      that.give_coupon_show = true;
                      that.give_coupon_close_url = that.tourl;
                    } else {
                      that.gotourl(that.tourl, 'reLaunch');
                    }
                  }, 1000);
                }
              });
              return;
            }
          });
        });
      } else if (typeid == 2) {
        //微信支付
        console.log(app);
        app.showLoading('提交中');
        app.post('ApiPay/daifu', {
          op: 'submit',
          orderid: orderid,
          typeid: typeid
        }, function (res) {
          app.showLoading(false);
          if (res.status == 0) {
            app.error(res.msg);
            return;
          }
          if (res.status == 2) {
            //无需付款
            app.success(res.msg);
            that.subscribeMessage(function () {
              if (that.invite_free) {
                that.invite_status = true;
              } else {
                setTimeout(function () {
                  if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                    that.give_coupon_show = true;
                    that.give_coupon_close_url = that.tourl;
                  } else {
                    that.gotourl(that.tourl, 'reLaunch');
                  }
                }, 1000);
              }
            });
            return;
          }
          var opt = res.data;
          if (app.globalData.platform == 'wx') {
            if (that.payorder.type == 'shop' || that.wxpay_type == 2) {
              if (opt.orderInfo) {
                console.log('requestOrderPayment1');
                wx.requestOrderPayment({
                  'timeStamp': opt.timeStamp,
                  'nonceStr': opt.nonceStr,
                  'package': opt.package,
                  'signType': opt.signType ? opt.signType : 'MD5',
                  'paySign': opt.paySign,
                  'orderInfo': opt.orderInfo,
                  'success': function success(res2) {
                    app.success('付款完成');
                    that.subscribeMessage(function () {
                      if (that.invite_free) {
                        that.invite_status = true;
                      } else {
                        setTimeout(function () {
                          if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                            that.give_coupon_show = true;
                            that.give_coupon_close_url = that.tourl;
                          } else {
                            that.gotourl(that.tourl, 'reLaunch');
                          }
                        }, 1000);
                      }
                    });
                  },
                  'fail': function fail(res2) {
                    //app.alert(JSON.stringify(res2))
                  }
                });
              } else {
                console.log('requestOrderPayment2');
                wx.requestOrderPayment({
                  'timeStamp': opt.timeStamp,
                  'nonceStr': opt.nonceStr,
                  'package': opt.package,
                  'signType': opt.signType ? opt.signType : 'MD5',
                  'paySign': opt.paySign,
                  'success': function success(res2) {
                    app.success('付款完成');
                    that.subscribeMessage(function () {
                      if (that.invite_free) {
                        that.invite_status = true;
                      } else {
                        setTimeout(function () {
                          if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                            that.give_coupon_show = true;
                            that.give_coupon_close_url = that.tourl;
                          } else {
                            that.gotourl(that.tourl, 'reLaunch');
                          }
                        }, 1000);
                      }
                    });
                  },
                  'fail': function fail(res2) {
                    //app.alert(JSON.stringify(res2))
                  }
                });
              }
            } else {
              uni.requestPayment({
                'provider': 'wxpay',
                'timeStamp': opt.timeStamp,
                'nonceStr': opt.nonceStr,
                'package': opt.package,
                'signType': opt.signType ? opt.signType : 'MD5',
                'paySign': opt.paySign,
                'success': function success(res2) {
                  app.success('付款完成');
                  that.subscribeMessage(function () {
                    if (that.invite_free) {
                      that.invite_status = true;
                    } else {
                      setTimeout(function () {
                        if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                          that.give_coupon_show = true;
                          that.give_coupon_close_url = that.tourl;
                        } else {
                          that.gotourl(that.tourl, 'reLaunch');
                        }
                      }, 1000);
                    }
                  });
                },
                'fail': function fail(res2) {
                  //app.alert(JSON.stringify(res2))
                }
              });
            }
          } else if (app.globalData.platform == 'mp') {

            /*
            var jweixin = require('jweixin-module');
            jweixin.chooseWXPay({
            	timestamp: opt.timeStamp, // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
            	nonceStr: opt.nonceStr, // 支付签名随机串，不长于 32 位
            	package: opt.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\*\*\*）
            	signType: opt.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
            	paySign: opt.paySign, // 支付签名
            	success: function (res2) {
            		// 支付成功后的回调函数
            		app.success('付款完成');
            		that.subscribeMessage(function () {
            			setTimeout(function () {
            				if (that.give_coupon_list && that.give_coupon_list.length > 0) {
            					that.give_coupon_show = true;
            					that.give_coupon_close_url = that.tourl;
            				} else {
            					that.gotourl(that.tourl,'reLaunch');
            				}
            			}, 1000);
            		});
            	}
            });
            */
          } else if (app.globalData.platform == 'h5') {
            location.href = opt.wx_url + '&redirect_url=' + encodeURIComponent(location.href.split('#')[0] + '#' + that.tourl);
          } else if (app.globalData.platform == 'app') {
            console.log(opt);
            uni.requestPayment({
              'provider': 'wxpay',
              'orderInfo': opt,
              'success': function success(res2) {
                app.success('付款完成');
                that.subscribeMessage(function () {
                  if (that.invite_free) {
                    that.invite_status = true;
                  } else {
                    setTimeout(function () {
                      if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                        that.give_coupon_show = true;
                        that.give_coupon_close_url = that.tourl;
                      } else {
                        that.gotourl(that.tourl, 'reLaunch');
                      }
                    }, 1000);
                  }
                });
              },
              'fail': function fail(res2) {
                console.log(res2);
                //app.alert(JSON.stringify(res2))
              }
            });
          } else if (app.globalData.platform == 'qq') {
            qq.requestWxPayment({
              url: opt.wx_url,
              referer: opt.referer,
              success: function success(res) {
                that.subscribeMessage(function () {
                  if (that.invite_free) {
                    that.invite_status = true;
                  } else {
                    setTimeout(function () {
                      if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                        that.give_coupon_show = true;
                        that.give_coupon_close_url = that.tourl;
                      } else {
                        that.gotourl(that.tourl, 'reLaunch');
                      }
                    }, 1000);
                  }
                });
              },
              fail: function fail(res) {}
            });
          }
        });
      } else if (typeid == 3 || typeid == 31 || typeid == 32) {
        //支付宝支付
        app.showLoading('提交中');
        app.post('ApiPay/daifu', {
          op: 'submit',
          orderid: orderid,
          typeid: typeid
        }, function (res) {
          console.log(res);
          app.showLoading(false);
          if (res.status == 0) {
            app.error(res.msg);
            return;
          }
          if (res.status == 2) {
            //无需付款
            app.success(res.msg);
            that.subscribeMessage(function () {
              if (that.invite_free) {
                that.invite_status = true;
              } else {
                setTimeout(function () {
                  if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                    that.give_coupon_show = true;
                    that.give_coupon_close_url = that.tourl;
                  } else {
                    that.gotourl(that.tourl, 'reLaunch');
                  }
                }, 1000);
              }
            });
            return;
          }
          var opt = res.data;
          if (app.globalData.platform == 'alipay') {
            uni.requestPayment({
              'provider': 'alipay',
              'orderInfo': opt.trade_no,
              'success': function success(res2) {
                console.log(res2);
                if (res2.resultCode == '6001') {
                  return;
                }
                app.success('付款完成');
                that.subscribeMessage(function () {
                  if (that.invite_free) {
                    that.invite_status = true;
                  } else {
                    setTimeout(function () {
                      if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                        that.give_coupon_show = true;
                        that.give_coupon_close_url = that.tourl;
                      } else {
                        that.gotourl(that.tourl, 'reLaunch');
                      }
                    }, 1000);
                  }
                });
              },
              'fail': function fail(res2) {
                //app.alert(JSON.stringify(res2))
              }
            });
          } else if (app.globalData.platform == 'mp' || app.globalData.platform == 'h5') {
            document.body.innerHTML = res.data;
            document.forms['alipaysubmit'].submit();
          } else if (app.globalData.platform == 'app') {
            console.log('------------alipay----------');
            console.log(opt);
            console.log('------------alipay end----------');
            uni.requestPayment({
              'provider': 'alipay',
              'orderInfo': opt,
              'success': function success(res2) {
                console.log('------------success----------');
                console.log(res2);
                app.success('付款完成');
                that.subscribeMessage(function () {
                  if (that.invite_free) {
                    that.invite_status = true;
                  } else {
                    setTimeout(function () {
                      if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                        that.give_coupon_show = true;
                        that.give_coupon_close_url = that.tourl;
                      } else {
                        that.gotourl(that.tourl, 'reLaunch');
                      }
                    }, 1000);
                  }
                });
              },
              'fail': function fail(res2) {
                console.log(res2);
                //app.alert(JSON.stringify(res2))
              }
            });
          }
        });
      } else if (typeid == '11') {
        app.showLoading('提交中');
        app.post('ApiPay/daifu', {
          op: 'submit',
          orderid: orderid,
          typeid: typeid
        }, function (res) {
          app.showLoading(false);
          swan.requestPolymerPayment({
            'orderInfo': res.orderInfo,
            'success': function success(res2) {
              app.success('付款完成');
              that.subscribeMessage(function () {
                if (that.invite_free) {
                  that.invite_status = true;
                } else {
                  setTimeout(function () {
                    if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                      that.give_coupon_show = true;
                      that.give_coupon_close_url = that.tourl;
                    } else {
                      that.gotourl(that.tourl, 'reLaunch');
                    }
                  }, 1000);
                }
              });
            },
            'fail': function fail(res2) {
              if (res2.errCode != 2) {
                app.alert(JSON.stringify(res2));
              }
            }
          });
        });
      } else if (typeid == '12') {
        app.showLoading('提交中');
        app.post('ApiPay/daifu', {
          op: 'submit',
          orderid: orderid,
          typeid: typeid
        }, function (res) {
          app.showLoading(false);
          console.log(res.orderInfo);
          tt.pay({
            'service': 5,
            'orderInfo': res.orderInfo,
            'success': function success(res2) {
              if (res2.code === 0) {
                app.success('付款完成');
                that.subscribeMessage(function () {
                  if (that.invite_free) {
                    that.invite_status = true;
                  } else {
                    setTimeout(function () {
                      if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                        that.give_coupon_show = true;
                        that.give_coupon_close_url = that.tourl;
                      } else {
                        that.gotourl(that.tourl, 'reLaunch');
                      }
                    }, 1000);
                  }
                });
              }
            },
            'fail': function fail(res2) {
              app.alert(JSON.stringify(res2));
            }
          });
        });
      } else if (typeid == '22') {
        if (app.globalData.platform == 'wx') {
          wx.login({
            success: function success(res) {
              if (res.code) {
                app.showLoading('提交中');
                app.post('ApiPay/getYunMpauthParams', {
                  jscode: res.code
                }, function (res) {
                  app.showLoading(false);
                  app.post('https://showmoney.cn/scanpay/fixed/mpauth', res.params, function (res2) {
                    console.log(res2.sessionKey);
                    app.post('ApiPay/getYunUnifiedParams', {
                      orderid: orderid,
                      sessionKey: res2.sessionKey
                    }, function (res3) {
                      app.post('https://showmoney.cn/scanpay/unified', res3.params, function (res4) {
                        if (res4.respcd == '09') {
                          wx.requestPayment({
                            timeStamp: res4.timeStamp,
                            nonceStr: res4.nonceStr,
                            package: res4.package,
                            signType: res4.mpSignType,
                            paySign: res4.mpSign,
                            success: function success(result) {
                              app.success('付款完成');
                              that.subscribeMessage(function () {
                                if (that.invite_free) {
                                  that.invite_status = true;
                                } else {
                                  setTimeout(function () {
                                    if (that.give_coupon_list && that.give_coupon_list.length > 0) {
                                      that.give_coupon_show = true;
                                      that.give_coupon_close_url = that.tourl;
                                    } else {
                                      that.gotourl(that.tourl, 'reLaunch');
                                    }
                                  }, 1000);
                                }
                              });
                            },
                            fail: function fail(res5) {
                              //app.alert(JSON.stringify(res5))
                            }
                          });
                        } else {
                          app.alert(res4.errorDetail);
                        }
                      });
                    });
                  });
                });
              } else {
                console.log('登录失败！' + res.errMsg);
              }
            }
          });
        } else {
          var url = app.globalData.baseurl + 'ApiPay/daifu' + '&aid=' + app.globalData.aid + '&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id;
          url += '&op=submit&orderid=' + orderid + '&typeid=22';
          location.href = url;
        }
      } else if (typeid == '23') {
        //var url = app.globalData.baseurl + 'ApiPay/daifu'+'&aid=' + app.globalData.aid + '&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id;
        //url += '&op=submit&orderid='+orderid+'&typeid=23';
        //location.href = url;
        setTimeout(function () {
          that.$refs.dialogPayconfirm.open();
        }, 1000);
        app.goto('/pages/index/webView2?orderid=' + orderid + '&typeid=23' + '&aid=' + app.globalData.aid + '&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id);
        return;
        app.showLoading('提交中');
        app.post('ApiPay/daifu', {
          op: 'submit',
          orderid: orderid,
          typeid: 23
        }, function (res) {
          app.showLoading(false);
          console.log(res);
          app.goto('url::' + res.url);
        });
      } else if (typeid == '24') {
        //var url = app.globalData.baseurl + 'ApiPay/daifu'+'&aid=' + app.globalData.aid + '&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id;
        //url += '&op=submit&orderid='+orderid+'&typeid=23';
        //location.href = url;

        app.goto('/pages/index/webView2?orderid=' + orderid + '&typeid=24');
        return;
        app.showLoading('提交中');
        app.post('ApiPay/daifu', {
          op: 'submit',
          orderid: orderid,
          typeid: 24
        }, function (res) {
          app.showLoading(false);
          console.log(res);
          app.goto('url::' + res.url);
        });
      }
    },
    topay2: function topay2() {
      var that = this;
      var orderid = this.payorder.id;
      app.confirm('确定要' + that.codtxt + '吗?', function () {
        app.showLoading('提交中');
        app.post('ApiPay/daifu', {
          op: 'submit',
          orderid: orderid,
          typeid: 4
        }, function (res) {
          app.showLoading(false);
          if (res.status == 0) {
            app.error(res.msg);
            return;
          }
          if (res.status == 2) {
            //无需付款
            app.success(res.msg);
            that.subscribeMessage(function () {
              setTimeout(function () {
                that.gotourl(that.tourl, 'reLaunch');
              }, 1000);
            });
            return;
          }
        });
      });
    },
    topayMonth: function topayMonth() {
      var that = this;
      var orderid = this.payorder.id;
      app.confirm('确定要' + that.pay_month_txt + '支付吗?', function () {
        app.showLoading('提交中');
        app.post('ApiPay/daifu', {
          op: 'submit',
          orderid: orderid,
          typeid: 41
        }, function (res) {
          app.showLoading(false);
          if (res.status == 0) {
            app.error(res.msg);
            return;
          }
          if (res.status == 2) {
            //无需付款
            app.success(res.msg);
            that.subscribeMessage(function () {
              setTimeout(function () {
                that.gotourl(that.tourl, 'reLaunch');
              }, 1000);
            });
            return;
          }
        });
      });
    },
    topayTransfer: function topayTransfer(e) {
      var that = this;
      var orderid = this.payorder.id;
      app.confirm('确定要' + e.currentTarget.dataset.text + '吗?', function () {
        app.showLoading('提交中');
        app.post('ApiPay/daifu', {
          op: 'submit',
          orderid: orderid,
          typeid: 5
        }, function (res) {
          app.showLoading(false);
          if (res.status == 0) {
            app.error(res.msg);
            return;
          }
          if (res.status == 2) {
            //无需付款
            app.success(res.msg);
            that.subscribeMessage(function () {});
            setTimeout(function () {
              that.gotourl('transfer?id=' + orderid, 'reLaunch');
            }, 1000);
            return;
          }
        });
      });
    },
    give_coupon_close: function give_coupon_close(e) {
      var that = this;
      var tourl = e.currentTarget.dataset.url;
      this.give_coupon_show = false;
      that.gotourl(tourl, 'reLaunch');
    },
    gotourl: function gotourl(tourl, opentype) {
      var that = this;
      if (app.globalData.platform == 'mp' || app.globalData.platform == 'h5') {
        if (tourl.indexOf('miniProgram::') === 0) {
          //其他小程序
          tourl = tourl.slice(13);
          var tourlArr = tourl.split('|');
          that.showOpenWeapp();
          return;
        }
      }
      app.goto(tourl, opentype);
    },
    showOpenWeapp: function showOpenWeapp() {
      this.$refs.dialogOpenWeapp.open();
    },
    closeOpenWeapp: function closeOpenWeapp() {
      this.$refs.dialogOpenWeapp.close();
    },
    PayconfirmFun: function PayconfirmFun() {
      this.gotourl(this.tourl, 'reLaunch');
    },
    close_pay: function close_pay() {
      var that = this;
      that.open_pay = false;
    },
    closeInvite: function closeInvite() {
      var that = this;
      that.invite_status = false;
      setTimeout(function () {
        if (that.give_coupon_list && that.give_coupon_list.length > 0) {
          that.give_coupon_show = true;
          that.give_coupon_close_url = that.tourl;
        } else {
          that.gotourl(that.tourl, 'reLaunch');
        }
      }, 1000);
    },
    gotoInvite: function gotoInvite() {
      var that = this;
      var free_tmplids = that.free_tmplids;
      if (free_tmplids && free_tmplids.length > 0) {
        uni.requestSubscribeMessage({
          tmplIds: free_tmplids,
          success: function success(res) {
            console.log(res);
          },
          fail: function fail(res) {
            console.log(res);
          }
        });
      }
      app.goto('/pagesExt/invite_free/index', 'reLaunch');
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"]))

/***/ }),

/***/ 1315:
/*!********************************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/pagesExt/pay/daifu.vue?vue&type=style&index=0&lang=css& ***!
  \********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_daifu_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./daifu.vue?vue&type=style&index=0&lang=css& */ 1316);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_daifu_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_daifu_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_daifu_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_daifu_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_daifu_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1316:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!/Users/<USER>/Downloads/tcphp/uniapp/pagesExt/pay/daifu.vue?vue&type=style&index=0&lang=css& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[1309,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pagesExt/pay/daifu.js.map