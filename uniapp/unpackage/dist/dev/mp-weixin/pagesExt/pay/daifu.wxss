
.topbg {
	height: 360rpx;
	padding-top: 90rpx;
	text-align: center;
	color: #fff;
	display: flex;
	flex-direction: column;
}
.box-price{position: relative;top:-70rpx;padding-bottom: 60rpx;}
.topbg .headimg image {
	height: 120rpx;
	width: 120rpx;
	border-radius: 50%;
}
.topbg .nickname {
	font-size: 36rpx;
	font-weight: bold;
	padding-right: 6rpx;
}
.box {
	width: 94%;
	margin: 0 3%;
	background: #FFFFFF;
	margin-top: 20rpx;
	border-radius: 20rpx;
}
.box-title {
	height: 70rpx;
	line-height: 70rpx;
	color: #333333;
	font-weight: bold;
	font-size: 30rpx;
	/* border-bottom: 1px solid #F0F3F6; */
}
.goods {
	padding: 20rpx;
	margin-top: -40rpx;
	border-radius: 10rpx;
}
.bitem{padding-bottom: 30rpx;}
.goods-item {
	display: flex;
	justify-content: center;
	justify-content: space-between;
	padding: 8rpx 0;
}
.goods-item .proimg {
	width: 120rpx;
	height: 120rpx;
	border-radius: 10rpx;
}
.goods-item .right {
	align-self: flex-start;
	flex: 1;
	padding-left: 30rpx;
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	line-height: 40rpx;
}
.goods-item .right .f1 {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}
.goods-item .right .title{
	font-size: 28rpx;
}
.goods-item .right .hui{
	font-size: 24rpx;
	color: #b0b0b0;
}
.goods-item .right .line{text-decoration: line-through;}
.top {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-top: 20rpx
}
.top .f1 {
	height: 60rpx;
	line-height: 60rpx;
	font-size: 24rpx;
}
.top .f2 {
	color: #101010;
	font-weight: bold;
	font-size: 46rpx;
	height: 70rpx;
	line-height: 70rpx
}

/* .top .f2 .t1 {
	font-size: 44rpx
} */
.top .f2 .t3 {
	font-size: 50rpx
}
.top .f3 {
	color: #FC5729;
	font-size: 26rpx;
	height: 70rpx;
	line-height: 70rpx
}
.paytype {
	padding: 0 20rpx;
}
.paytype .f1 {
	height: 100rpx;
	line-height: 100rpx;
	padding: 0 30rpx;
	color: #333333;
	font-weight: bold
}
.paytype .f2 {
	padding: 0 30rpx
}
.paytype .f2 .item {
	border-bottom: 1px solid #f5f5f5;
	height: 100rpx;
	display: flex;
	align-items: center
}
.paytype .f2 .item:last-child {
	border-bottom: 0
}
.paytype .f2 .item .t1 {
	flex: 1;
	display: flex;
	align-items: center;
	color: #222222;
	font-size: 30rpx;
	font-weight: bold
}
.paytype .f2 .item .t1 .img {
	width: 44rpx;
	height: 44rpx;
	margin-right: 40rpx
}
.paytype .f2 .item .radio {
	flex-shrink: 0;
	width: 36rpx;
	height: 36rpx;
	background: #FFFFFF;
	border: 3rpx solid #BFBFBF;
	border-radius: 50%;
	margin-right: 10rpx
}
.paytype .f2 .item .radio .radio-img {
	width: 100%;
	height: 100%
}
.btn {
	height: 90rpx;
	line-height: 90rpx;
	width: 85%;
	margin: 0 auto;
	border-radius: 10rpx;
	margin-top: 30rpx;
	color: #fff;
	font-size: 30rpx;
	font-weight: bold
}
.daifu-btn {
	background: #fc5729;
}
.op {
	width: 94%;
	margin: 20rpx 3%;
	display: flex;
	align-items: center;
	margin-top: 40rpx
}
.op .btn {
	flex: 1;
	height: 100rpx;
	line-height: 100rpx;
	background: #07C160;
	width: 90%;
	margin: 0 10rpx;
	border-radius: 10rpx;
	color: #fff;
	font-size: 28rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center
}
.op .btn .img {
	width: 48rpx;
	height: 48rpx;
	margin-right: 20rpx
}
.daifu_desc{padding: 30rpx;}
.daifu_desc .title{font-size: 30rpx;color: #5E5E5E;font-weight: bold;padding: 10rpx 0;}
.daifu_desc textarea{width: 100%; line-height: 46rpx;font-size: 24rpx;color: #222222;}

