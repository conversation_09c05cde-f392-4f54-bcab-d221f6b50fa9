<view class="container"><block wx:if="{{isload}}"><block><view class="top"><view class="f1">需支付金额</view><block wx:if="{{payorder.score==0}}"><view class="f2"><text class="t1">￥</text><text class="t2">{{payorder.money}}</text><block wx:if="{{$root.m0}}"><text style="font-size:28rpx;">{{'+ '+payorder.service_fee_money+$root.m1}}</text></block></view></block><block wx:else><block wx:if="{{payorder.money>0&&payorder.score>0}}"><view class="f2"><text class="t1">￥</text><text class="t2">{{payorder.money}}</text><text style="font-size:28rpx;">{{'+ '+payorder.score+$root.m2}}</text></view></block><block wx:else><view class="f2"><text class="t3">{{payorder.score+$root.m3}}</text></view></block></block><block wx:if="{{payorder.discountText}}"><view style="color:#F00;">{{payorder.discountText}}</view></block><block wx:if="{{detailurl!=''}}"><view class="f3" data-url="{{detailurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">订单详情<text class="iconfont iconjiantou"></text></view></block></view><view class="paytype"><view class="f1">选择支付方式：</view><block wx:if="{{payorder.money==0&&payorder.score>0}}"><block><view class="f2"><view class="item" data-typeid="1" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1 flex"><image class="img" src="{{pre_url+'/static/img/pay-money.png'}}"></image><view class="flex-col"><text>{{$root.m4+"支付"}}</text><view style="font-size:22rpx;font-weight:normal;">{{"剩 余"+$root.m5}}<text style="color:#FC5729;">{{userinfo.score}}</text></view></view></view><view class="radio" style="{{(typeid=='1'?'background:'+$root.m6+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></view></block></block><block wx:else><block><view class="f2"><block wx:if="{{moneypay!=2}}"><block><block wx:if="{{wxpay==1&&(wxpay_type==0||wxpay_type==1||wxpay_type==2||wxpay_type==3||wxpay_type==4||wxpay_type==5||wxpay_type==6||wxpay_type==8)}}"><view class="item" data-typeid="2" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-weixin.png'}}"></image>微信支付</view><view class="radio" style="{{(combines.wxpay==0&&typeid=='2'||combines.wxpay=='2'?'background:'+$root.m7+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{wxpay==1&&wxpay_type==22}}"><view class="item" data-typeid="22" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-weixin.png'}}"></image>微信支付</view><view class="radio" style="{{(typeid=='22'?'background:'+$root.m8+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{alipay==2}}"><view class="item" data-typeid="23" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-alipay.png'}}"></image>支付宝支付</view><view class="radio" style="{{(typeid=='23'?'background:'+$root.m9+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{alipay==1}}"><view class="item" data-typeid="3" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-alipay.png'}}"></image>支付宝支付</view><view class="radio" style="{{(combines.alipay==0&&typeid=='3'||combines.alipay=='3'?'background:'+$root.m10+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{more_alipay==1}}"><block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index"><view class="item" data-typeid="{{item.$orig.typeid}}" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-alipay.png'}}"></image>{{item.$orig.name}}</view><view class="radio" style="{{(combines.alipay==0&&typeid==item.$orig.typeid||combines.alipay==item.$orig.typeid?'background:'+item.m11+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></block></block><block wx:if="{{paypal==1}}"><view class="item" data-typeid="51" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/paypal.png'}}"></image>PayPal支付</view><view class="radio" style="{{(typeid=='51'?'background:'+$root.m12+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{$root.m13}}"><view class="item" data-typeid="61" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-cash.png'}}"></image>银联支付</view><view class="radio" style="{{(typeid=='61'?'background:'+$root.m14+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{baidupay==1}}"><view class="item" data-typeid="11" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/pay-money.png'}}"></image>在线支付</view><view class="radio" style="{{(typeid=='11'?'background:'+$root.m15+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{toutiaopay==1}}"><view class="item" data-typeid="12" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/pay-money.png'}}"></image>在线支付</view><view class="radio" style="{{(typeid=='12'?'background:'+$root.m16+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{overdraft_moneypay==1}}"><view class="item" data-typeid="38" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1 flex"><image class="img" src="{{pre_url+'/static/img/overdraft_pay.png'}}"></image><view class="flex-col"><text>{{$root.m17}}</text><view style="font-size:22rpx;font-weight:normal;">可用额度<text style="color:#FC5729;">{{userinfo.overdraft_money}}</text></view></view></view><view class="radio" style="{{(typeid=='38'?'background:'+$root.m18+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{$root.m19}}"><view class="item" data-typeid="122" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-yunshanfu.png'}}"></image>云闪付支付</view><view class="radio" style="{{(typeid=='122'?'background:'+$root.m20+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></block></block><block wx:if="{{moneypay==1||moneypay==2}}"><view class="item" data-typeid="1" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1 flex"><image class="img" src="{{pre_url+'/static/img/pay-money.png'}}"></image><view class="flex-col"><text>{{$root.m21+"支付"}}</text><view style="font-size:22rpx;font-weight:normal;">可用余额<text style="color:#FC5729;">{{"￥"+userinfo.money}}</text></view></view></view><view class="radio" style="{{(combines.moneypay==0&&typeid=='1'||combines.moneypay=='1'?'background:'+$root.m22+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{xiaofeipay==1}}"><view class="item" data-typeid="71" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1 flex"><image class="img" src="{{pre_url+'/static/img/pay-money.png'}}"></image><view class="flex-col"><text>{{$root.m23+"支付"}}</text><view style="font-size:22rpx;font-weight:normal;">可用余额<text style="color:#FC5729;">{{"￥"+userinfo.xiaofei_money}}</text></view></view></view><view class="radio" style="{{(typeid=='71'?'background:'+$root.m24+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{yuanbaopay==1}}"><view class="item" style="height:130rpx;" data-typeid="yuanbao" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1 flex"><image class="img" src="{{pre_url+'/static/img/pay-money.png'}}"></image><view class="flex-col"><text>{{$root.m25+"支付"}}</text><text style="font-size:22rpx;font-weight:normal;">{{'可用'+$root.m26}}<text style="color:#FC5729;">{{userinfo.yuanbao}}</text></text><text style="font-size:22rpx;font-weight:normal;">需支付<text style="color:#FC5729;">{{yuanbao_msg}}</text></text></view></view><view class="radio" style="{{(typeid=='yuanbao'?'background:'+$root.m27+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></view></block></block></view><block wx:if="{{combines.moneypay==1&&(combines.wxpay!=0||combines.alipay!=0)}}"><block><block wx:if="{{typeid!='0'&&liji_pay_button}}"><button data-event-opts="{{[['tap',[['topay3',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m28)+';'}}" bindtap="__e">立即支付</button></block><block wx:if="{{$root.m29}}"><button class="btn" style="{{'background:'+($root.m30)+';'}}" data-wxpaytype="7" data-event-opts="{{[['tap',[['topay3',['$event']]]]]}}" bindtap="__e">微信收款码</button></block></block></block><block wx:else><block><block wx:if="{{typeid!='0'&&liji_pay_button}}"><button data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m31)+';'}}" bindtap="__e">立即支付</button></block><block wx:if="{{$root.m32}}"><button class="btn" style="{{'background:'+($root.m33)+';'}}" data-wxpaytype="7" data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" bindtap="__e">微信收款码</button></block></block></block><block wx:if="{{cancod==1}}"><button data-event-opts="{{[['tap',[['topay2',['$event']]]]]}}" class="btn" style="background:rgba(126,113,246,0.5);" bindtap="__e">{{codtxt}}<block wx:if="{{cod_frontmoney>0}}"><text style="font-size:24rpx;">{{"(需付定金￥"+cod_frontmoney+")"}}</text></block></button></block><block wx:if="{{pay_transfer==1}}"><button class="btn" style="{{'background:'+($root.m34)+';'}}" data-text="{{$root.m35}}" data-event-opts="{{[['tap',[['topayTransfer',['$event']]]]]}}" bindtap="__e">{{$root.m36}}</button></block><block wx:if="{{pay_month==1}}"><button data-event-opts="{{[['tap',[['topayMonth',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m37)+';'}}" bindtap="__e">{{pay_month_txt}}</button></block><block wx:if="{{daifu}}"><block><block wx:if="{{$root.m38}}"><button data-event-opts="{{[['tap',[['todaifu',['$event']]]]]}}" class="btn daifu-btn" bindtap="__e">{{''+daifu_txt+''}}</button></block><block wx:else><button class="btn daifu-btn" open-type="share">{{''+daifu_txt+''}}</button></block></block></block><uni-popup class="vue-ref" vue-id="a41d7bae-1" id="dialogInput" type="dialog" data-ref="dialogInput" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('a41d7bae-2')+','+('a41d7bae-1')}}" mode="password" title="支付密码" value="" placeholder="请输入支付密码" data-event-opts="{{[['^confirm',[['getpwd']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><block wx:if="{{$root.g0>0}}"><block><view class="ad-box"><block wx:for="{{adlist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><block wx:if="{{item.pic}}"><view class="ad-item" data-url="{{item.url}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.pic}}" mode="widthFix"></image></view></block></block></block></view><view style="height:30rpx;"></view></block></block><block wx:if="{{give_coupon_show}}"><block><view class="give-coupon flex-x-center flex-y-center"><view class="coupon-block"><image style="width:630rpx;height:330rpx;" src="{{pre_url+'/static/img/coupon-top.png'}}"></image><view class="coupon-del flex-x-center flex-y-center" data-url="{{give_coupon_close_url}}" data-event-opts="{{[['tap',[['give_coupon_close',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/coupon-del.png'}}"></image></view><view class="flex-x-center"><view class="coupon-info"><view class="flex-x-center coupon-get">{{"获得"+give_coupon_num+"张"+$root.m39}}</view><view style="background:#f5f5f5;padding:10rpx 0;"><block wx:for="{{give_coupon_list}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><block wx:if="{{index<3}}"><block><view class="coupon-coupon"><view class="{{[item.type==1?'pt_img1':'pt_img2']}}"></view><view class="{{['pt_left',item.type==1?'':'bg2']}}"><block wx:if="{{item.type==1||item.type==5}}"><view class="f1"><text class="t0">￥</text><text class="t1">{{item.money}}</text></view></block><block wx:if="{{item.type==3}}"><view class="f1"><text class="t1">{{item.limit_count}}</text><text class="t2">次</text></view></block><block wx:if="{{item.type==10}}"><view class="f1"><text class="t1">{{item.discount/10}}</text><text class="t2">折</text></view></block><block wx:if="{{item.type!=1&&item.type!=3&&item.type!=5&&item.type!=10}}"><block><view class="f1">{{item.type_txt}}</view></block></block><block wx:if="{{item.type==1||item.type==4||item.type==5||item.type==10}}"><view class="f2"><block wx:if="{{item.minprice>0}}"><text>{{"满"+item.minprice+"元可用"}}</text></block><block wx:else><text>无门槛</text></block></view></block></view><view class="pt_right"><view class="f1"><view class="t1">{{item.name}}</view><block wx:if="{{item.type_txt}}"><view class="t2">{{item.type_txt}}</view></block><block wx:if="{{item.bid>0}}"><view class="t4">{{"适用商家："+item.bname}}</view></block></view></view><block wx:if="{{item.givenum>1}}"><view class="coupon_num">{{"×"+item.givenum}}</view></block></view></block></block></block></block></view><view class="flex-x-center coupon-btn" data-url="/pagesExt/coupon/mycoupon" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">前往查看</view></view></view></view></view></block></block><uni-popup class="vue-ref" vue-id="a41d7bae-3" id="dialogOpenWeapp" type="dialog" maskClick="{{false}}" data-ref="dialogOpenWeapp" bind:__l="__l" vue-slots="{{['default']}}"><view style="background:#fff;padding:50rpx;position:relative;border-radius:20rpx;"><view style="height:80px;line-height:80px;width:200px;margin:0 auto;font-size:18px;text-align:center;font-weight:bold;color:#333;">恭喜您支付成功</view><view style="height:50px;line-height:50px;width:200px;margin:0 auto;border-radius:5px;color:#66f;font-size:14px;text-align:center;" data-url="{{detailurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看订单详情</view></view></uni-popup><uni-popup class="vue-ref" vue-id="a41d7bae-4" id="dialogPayconfirm" type="dialog" maskClick="{{false}}" data-ref="dialogPayconfirm" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('a41d7bae-5')+','+('a41d7bae-4')}}" type="info" title="支付确认" content="是否已完成支付" data-event-opts="{{[['^confirm',[['PayconfirmFun']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><block wx:if="{{yuanbaopay==1&&open_pay}}"><view style="width:100%;height:100%;position:fixed;z-index:10;background-color:#000;opacity:0.45;top:0;"></view></block><block wx:if="{{yuanbaopay==1&&open_pay}}"><view style="width:90%;position:fixed;z-index:11;left:5%;top:25%;background-color:#fff;"><view class="paytype"><view class="f2"><block wx:if="{{wxpay==1&&(wxpay_type==0||wxpay_type==1||wxpay_type==2||wxpay_type==3||wxpay_type==4)}}"><view class="item" data-typeid="2" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-weixin.png'}}"></image>微信支付</view><view class="radio" style="{{(typeid=='2'?'background:'+$root.m40+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{wxpay==1&&wxpay_type==22}}"><view class="item" data-typeid="22" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-weixin.png'}}"></image>微信支付</view><view class="radio" style="{{(typeid=='22'?'background:'+$root.m41+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{alipay==2}}"><view class="item" data-typeid="23" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-alipay.png'}}"></image>支付宝支付</view><view class="radio" style="{{(typeid=='23'?'background:'+$root.m42+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{alipay==1}}"><view class="item" data-typeid="3" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-alipay.png'}}"></image>支付宝支付</view><view class="radio" style="{{(typeid=='3'?'background:'+$root.m43+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{more_alipay==1}}"><block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index"><view class="item" data-typeid="{{item.$orig.typeid}}" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/withdraw-alipay.png'}}"></image>{{item.$orig.name}}</view><view class="radio" style="{{(typeid==item.$orig.typeid?'background:'+item.m44+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></block></block><block wx:if="{{baidupay==1}}"><view class="item" data-typeid="11" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/pay-money.png'}}"></image>在线支付</view><view class="radio" style="{{(typeid=='11'?'background:'+$root.m45+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{toutiaopay==1}}"><view class="item" data-typeid="12" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="{{pre_url+'/static/img/pay-money.png'}}"></image>在线支付</view><view class="radio" style="{{(typeid=='12'?'background:'+$root.m46+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{moneypay==1}}"><view class="item" data-typeid="1" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1 flex"><image class="img" src="{{pre_url+'/static/img/pay-money.png'}}"></image><view class="flex-col"><text>{{$root.m47+"支付"}}</text><view style="font-size:22rpx;font-weight:normal;">可用余额<text style="color:#FC5729;">{{"￥"+userinfo.money}}</text></view></view></view><view class="radio" style="{{(typeid=='1'?'background:'+$root.m48+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block><block wx:if="{{xiaofeipay==1}}"><view class="item" data-typeid="71" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1 flex"><image class="img" src="{{pre_url+'/static/img/pay-money.png'}}"></image><view class="flex-col"><text>{{$root.m49+"支付"}}</text><view style="font-size:22rpx;font-weight:normal;">可用余额<text style="color:#FC5729;">{{"￥"+userinfo.xiaofei_money}}</text></view></view></view><view class="radio" style="{{(typeid=='71'?'background:'+$root.m50+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></view></view><view style="overflow:hidden;width:100%;"><view style="width:300rpx;float:left;"><view><button data-event-opts="{{[['tap',[['close_pay',['$event']]]]]}}" class="btn" style="margin-bottom:20rpx;background-color:#999;" bindtap="__e">取消</button></view></view><view style="width:300rpx;float:right;"><view><block wx:if="{{typeid!='0'}}"><button data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" class="btn" style="{{'margin-bottom:20rpx;'+('background:'+($root.m51)+';')}}" bindtap="__e">确定</button></block></view></view></view></view></block><block wx:if="{{invite_status&&invite_free}}"><block><view data-event-opts="{{[['tap',[['closeInvite',['$event']]]]]}}" style="width:100%;height:100%;background-color:#000;position:fixed;opacity:0.5;z-index:99;top:0;" bindtap="__e"></view><view style="width:700rpx;margin:0 auto;position:fixed;top:10%;left:25rpx;z-index:100;"><view data-event-opts="{{[['tap',[['gotoInvite',['$event']]]]]}}" style="background-color:#fff;border-radius:20rpx;overflow:hidden;width:100%;min-height:700rpx;" bindtap="__e"><image style="width:100%;height:auto;" src="{{invite_free.pic}}" mode="widthFix"></image></view><block wx:if="{{invite_status&&invite_free}}"><view data-event-opts="{{[['tap',[['closeInvite',['$event']]]]]}}" style="width:80rpx;height:80rpx;line-height:80rpx;text-align:center;font-size:30rpx;background-color:#fff;margin:0 auto;border-radius:50%;margin-top:20rpx;" bindtap="__e">X</view></block></view></block></block><block wx:if="{{showposter}}"><view class="posterShare"><view style="width:708rpx;height:764rpx;margin-top:40rpx;"><image style="width:100%;height:100%;" src="{{pre_url+'/static/img/share_guide.png'}}"></image><view style="display:flex;justify-content:center;"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="posterButton" bindtap="__e">确认</view></view></view></view></block><uni-popup class="vue-ref" vue-id="a41d7bae-6" id="dialogInvitecashback" type="dialog" data-ref="dialogInvitecashback" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">邀请返现</text></view><view class="uni-dialog-content"><view style="line-height:50rpx;">{{''+payorder.ictips+''}}</view></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['dialogInvitecashbackClose',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><block><view class="uni-dialog-button uni-border-left" data-url="{{'/pages/shop/product?id='+payorder.proid+'&sharetypevisible=true'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">分享</text></view></block></view></view></uni-popup><uni-popup class="vue-ref" vue-id="a41d7bae-7" id="buildWxNativeH5" type="dialog" data-ref="buildWxNativeH5" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog" style="text-align:center;"><view class="uni-dialog-title"><text class="uni-dialog-title-text">扫码付款</text></view><image class="img" src="{{pay_wx_qrcode_url}}" data-url="{{pay_wx_qrcode_url}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="uni-dialog-content"><view style="line-height:50rpx;">请用微信扫描二维码完成付款</view></view><view class="uni-dialog-button-group"><block><view class="uni-dialog-button uni-border-left" data-url="{{tourl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">已完成支付</text></view></block><view data-event-opts="{{[['tap',[['buildWxNativeH5Close',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消支付</text></view></view></view></uni-popup><uni-popup class="vue-ref" vue-id="a41d7bae-8" id="dialogGiveorder" type="dialog" data-ref="dialogGiveorder" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">赠好友</text></view><view class="uni-dialog-content"><view style="line-height:50rpx;">{{''+giveordertitle+''}}</view></view><view style="width:560rpx;margin:auto;"><image class="img" style="width:100%;" src="{{giveorderpic}}" data-url="{{giveorderpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['dialogGiveorderClose',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><block><view class="uni-dialog-button uni-border-left"><button class="uni-dialog-button-text uni-button-color" open-type="share">分享好友</button></view></block></view></view></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="a41d7bae-9" bind:__l="__l"></loading></block><dp-tabbar vue-id="a41d7bae-10" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="a41d7bae-11" data-ref="popmsg" bind:__l="__l"></popmsg></view>