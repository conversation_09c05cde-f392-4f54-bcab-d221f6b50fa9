require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pagesExt/pay/pay"],{

/***/ 1301:
/*!****************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/main.js?{"page":"pagesExt%2Fpay%2Fpay"} ***!
  \****************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _pay = _interopRequireDefault(__webpack_require__(/*! ./pagesExt/pay/pay.vue */ 1302));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_pay.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 1302:
/*!*********************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/pagesExt/pay/pay.vue ***!
  \*********************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _pay_vue_vue_type_template_id_58310e7d___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./pay.vue?vue&type=template&id=58310e7d& */ 1303);
/* harmony import */ var _pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pay.vue?vue&type=script&lang=js& */ 1305);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _pay_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./pay.vue?vue&type=style&index=0&lang=css& */ 1307);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 36);

var renderjs





/* normalize component */

var component = Object(_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _pay_vue_vue_type_template_id_58310e7d___WEBPACK_IMPORTED_MODULE_0__["render"],
  _pay_vue_vue_type_template_id_58310e7d___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _pay_vue_vue_type_template_id_58310e7d___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pagesExt/pay/pay.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 1303:
/*!****************************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/pagesExt/pay/pay.vue?vue&type=template&id=58310e7d& ***!
  \****************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_pay_vue_vue_type_template_id_58310e7d___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pay.vue?vue&type=template&id=58310e7d& */ 1304);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_pay_vue_vue_type_template_id_58310e7d___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_pay_vue_vue_type_template_id_58310e7d___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_pay_vue_vue_type_template_id_58310e7d___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_pay_vue_vue_type_template_id_58310e7d___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 1304:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!/Users/<USER>/Downloads/tcphp/uniapp/pagesExt/pay/pay.vue?vue&type=template&id=58310e7d& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniPopup: function () {
      return Promise.all(/*! import() | components/uni-popup/uni-popup */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/uni-popup/uni-popup")]).then(__webpack_require__.bind(null, /*! @/components/uni-popup/uni-popup.vue */ 7096))
    },
    uniPopupDialog: function () {
      return __webpack_require__.e(/*! import() | components/uni-popup-dialog/uni-popup-dialog */ "components/uni-popup-dialog/uni-popup-dialog").then(__webpack_require__.bind(null, /*! @/components/uni-popup-dialog/uni-popup-dialog.vue */ 7240))
    },
    loading: function () {
      return __webpack_require__.e(/*! import() | components/loading/loading */ "components/loading/loading").then(__webpack_require__.bind(null, /*! @/components/loading/loading.vue */ 7131))
    },
    dpTabbar: function () {
      return __webpack_require__.e(/*! import() | components/dp-tabbar/dp-tabbar */ "components/dp-tabbar/dp-tabbar").then(__webpack_require__.bind(null, /*! @/components/dp-tabbar/dp-tabbar.vue */ 7110))
    },
    popmsg: function () {
      return __webpack_require__.e(/*! import() | components/popmsg/popmsg */ "components/popmsg/popmsg").then(__webpack_require__.bind(null, /*! @/components/popmsg/popmsg.vue */ 7124))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 =
    _vm.isload && _vm.payorder.score == 0
      ? !_vm.isNull(_vm.payorder.service_fee_money) &&
        _vm.payorder.service_fee_money > 0
      : null
  var m1 = _vm.isload && _vm.payorder.score == 0 && m0 ? _vm.t("服务费") : null
  var m2 =
    _vm.isload &&
    !(_vm.payorder.score == 0) &&
    _vm.payorder.money > 0 &&
    _vm.payorder.score > 0
      ? _vm.t("积分")
      : null
  var m3 =
    _vm.isload &&
    !(_vm.payorder.score == 0) &&
    !(_vm.payorder.money > 0 && _vm.payorder.score > 0)
      ? _vm.t("积分")
      : null
  var m4 =
    _vm.isload && _vm.payorder.money == 0 && _vm.payorder.score > 0
      ? _vm.t("积分")
      : null
  var m5 =
    _vm.isload && _vm.payorder.money == 0 && _vm.payorder.score > 0
      ? _vm.t("积分")
      : null
  var m6 =
    _vm.isload &&
    _vm.payorder.money == 0 &&
    _vm.payorder.score > 0 &&
    _vm.typeid == "1"
      ? _vm.t("color1")
      : null
  var m7 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.moneypay != 2 &&
    _vm.wxpay == 1 &&
    (_vm.wxpay_type == 0 ||
      _vm.wxpay_type == 1 ||
      _vm.wxpay_type == 2 ||
      _vm.wxpay_type == 3 ||
      _vm.wxpay_type == 4 ||
      _vm.wxpay_type == 5 ||
      _vm.wxpay_type == 6 ||
      _vm.wxpay_type == 8) &&
    ((_vm.combines.wxpay == 0 && _vm.typeid == "2") ||
      _vm.combines.wxpay == "2")
      ? _vm.t("color1")
      : null
  var m8 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.moneypay != 2 &&
    _vm.wxpay == 1 &&
    _vm.wxpay_type == 22 &&
    _vm.typeid == "22"
      ? _vm.t("color1")
      : null
  var m9 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.moneypay != 2 &&
    _vm.alipay == 2 &&
    _vm.typeid == "23"
      ? _vm.t("color1")
      : null
  var m10 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.moneypay != 2 &&
    _vm.alipay == 1 &&
    ((_vm.combines.alipay == 0 && _vm.typeid == "3") ||
      _vm.combines.alipay == "3")
      ? _vm.t("color1")
      : null
  var l0 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.moneypay != 2 &&
    _vm.more_alipay == 1
      ? _vm.__map(_vm.more_alipay_data, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m11 =
            (_vm.combines.alipay == 0 && _vm.typeid == item.typeid) ||
            _vm.combines.alipay == item.typeid
              ? _vm.t("color1")
              : null
          return {
            $orig: $orig,
            m11: m11,
          }
        })
      : null
  var m12 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.moneypay != 2 &&
    _vm.paypal == 1 &&
    _vm.typeid == "51"
      ? _vm.t("color1")
      : null
  var m13 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.moneypay != 2
      ? _vm.adapay_union == 1 && _vm.getplatform() == "h5"
      : null
  var m14 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.moneypay != 2 &&
    m13 &&
    _vm.typeid == "61"
      ? _vm.t("color1")
      : null
  var m15 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.moneypay != 2 &&
    _vm.baidupay == 1 &&
    _vm.typeid == "11"
      ? _vm.t("color1")
      : null
  var m16 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.moneypay != 2 &&
    _vm.toutiaopay == 1 &&
    _vm.typeid == "12"
      ? _vm.t("color1")
      : null
  var m17 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.moneypay != 2 &&
    _vm.overdraft_moneypay == 1
      ? _vm.t("信用额度")
      : null
  var m18 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.moneypay != 2 &&
    _vm.overdraft_moneypay == 1 &&
    _vm.typeid == "38"
      ? _vm.t("color1")
      : null
  var m19 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.moneypay != 2
      ? _vm.yunshanfuwxpay == 1 && _vm.getplatform() == "wx"
      : null
  var m20 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.moneypay != 2 &&
    m19 &&
    _vm.typeid == "122"
      ? _vm.t("color1")
      : null
  var m21 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    (_vm.moneypay == 1 || _vm.moneypay == 2)
      ? _vm.t("余额")
      : null
  var m22 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    (_vm.moneypay == 1 || _vm.moneypay == 2) &&
    ((_vm.combines.moneypay == 0 && _vm.typeid == "1") ||
      _vm.combines.moneypay == "1")
      ? _vm.t("color1")
      : null
  var m23 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.xiaofeipay == 1
      ? _vm.t("冻结佣金")
      : null
  var m24 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.xiaofeipay == 1 &&
    _vm.typeid == "71"
      ? _vm.t("color1")
      : null
  var m25 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.yuanbaopay == 1
      ? _vm.t("元宝")
      : null
  var m26 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.yuanbaopay == 1
      ? _vm.t("元宝")
      : null
  var m27 =
    _vm.isload &&
    !(_vm.payorder.money == 0 && _vm.payorder.score > 0) &&
    _vm.yuanbaopay == 1 &&
    _vm.typeid == "yuanbao"
      ? _vm.t("color1")
      : null
  var m28 =
    _vm.isload &&
    _vm.combines.moneypay == 1 &&
    (_vm.combines.wxpay != 0 || _vm.combines.alipay != 0) &&
    _vm.typeid != "0" &&
    _vm.liji_pay_button
      ? _vm.t("color1")
      : null
  var m29 =
    _vm.isload &&
    _vm.combines.moneypay == 1 &&
    (_vm.combines.wxpay != 0 || _vm.combines.alipay != 0)
      ? _vm.typeid == "2" && _vm.wxpay_native_h5 && _vm.getplatform() == "h5"
      : null
  var m30 =
    _vm.isload &&
    _vm.combines.moneypay == 1 &&
    (_vm.combines.wxpay != 0 || _vm.combines.alipay != 0) &&
    m29
      ? _vm.t("color1")
      : null
  var m31 =
    _vm.isload &&
    !(
      _vm.combines.moneypay == 1 &&
      (_vm.combines.wxpay != 0 || _vm.combines.alipay != 0)
    ) &&
    _vm.typeid != "0" &&
    _vm.liji_pay_button
      ? _vm.t("color1")
      : null
  var m32 =
    _vm.isload &&
    !(
      _vm.combines.moneypay == 1 &&
      (_vm.combines.wxpay != 0 || _vm.combines.alipay != 0)
    )
      ? _vm.typeid == "2" && _vm.wxpay_native_h5 && _vm.getplatform() == "h5"
      : null
  var m33 =
    _vm.isload &&
    !(
      _vm.combines.moneypay == 1 &&
      (_vm.combines.wxpay != 0 || _vm.combines.alipay != 0)
    ) &&
    m32
      ? _vm.t("color1")
      : null
  var m34 = _vm.isload && _vm.pay_transfer == 1 ? _vm.t("color2") : null
  var m35 = _vm.isload && _vm.pay_transfer == 1 ? _vm.t("转账汇款") : null
  var m36 = _vm.isload && _vm.pay_transfer == 1 ? _vm.t("转账汇款") : null
  var m37 = _vm.isload && _vm.pay_month == 1 ? _vm.t("color1") : null
  var m38 =
    _vm.isload && _vm.daifu
      ? _vm.getplatform() == "h5" ||
        _vm.getplatform() == "mp" ||
        _vm.getplatform() == "app"
      : null
  var g0 = _vm.isload ? _vm.adlist.length : null
  var m39 = _vm.isload && _vm.give_coupon_show ? _vm.t("优惠券") : null
  var m40 =
    _vm.isload &&
    _vm.yuanbaopay == 1 &&
    _vm.open_pay &&
    _vm.wxpay == 1 &&
    (_vm.wxpay_type == 0 ||
      _vm.wxpay_type == 1 ||
      _vm.wxpay_type == 2 ||
      _vm.wxpay_type == 3 ||
      _vm.wxpay_type == 4) &&
    _vm.typeid == "2"
      ? _vm.t("color1")
      : null
  var m41 =
    _vm.isload &&
    _vm.yuanbaopay == 1 &&
    _vm.open_pay &&
    _vm.wxpay == 1 &&
    _vm.wxpay_type == 22 &&
    _vm.typeid == "22"
      ? _vm.t("color1")
      : null
  var m42 =
    _vm.isload &&
    _vm.yuanbaopay == 1 &&
    _vm.open_pay &&
    _vm.alipay == 2 &&
    _vm.typeid == "23"
      ? _vm.t("color1")
      : null
  var m43 =
    _vm.isload &&
    _vm.yuanbaopay == 1 &&
    _vm.open_pay &&
    _vm.alipay == 1 &&
    _vm.typeid == "3"
      ? _vm.t("color1")
      : null
  var l1 =
    _vm.isload && _vm.yuanbaopay == 1 && _vm.open_pay && _vm.more_alipay == 1
      ? _vm.__map(_vm.more_alipay_data, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var m44 = _vm.typeid == item.typeid ? _vm.t("color1") : null
          return {
            $orig: $orig,
            m44: m44,
          }
        })
      : null
  var m45 =
    _vm.isload &&
    _vm.yuanbaopay == 1 &&
    _vm.open_pay &&
    _vm.baidupay == 1 &&
    _vm.typeid == "11"
      ? _vm.t("color1")
      : null
  var m46 =
    _vm.isload &&
    _vm.yuanbaopay == 1 &&
    _vm.open_pay &&
    _vm.toutiaopay == 1 &&
    _vm.typeid == "12"
      ? _vm.t("color1")
      : null
  var m47 =
    _vm.isload && _vm.yuanbaopay == 1 && _vm.open_pay && _vm.moneypay == 1
      ? _vm.t("余额")
      : null
  var m48 =
    _vm.isload &&
    _vm.yuanbaopay == 1 &&
    _vm.open_pay &&
    _vm.moneypay == 1 &&
    _vm.typeid == "1"
      ? _vm.t("color1")
      : null
  var m49 =
    _vm.isload && _vm.yuanbaopay == 1 && _vm.open_pay && _vm.xiaofeipay == 1
      ? _vm.t("冻结佣金")
      : null
  var m50 =
    _vm.isload &&
    _vm.yuanbaopay == 1 &&
    _vm.open_pay &&
    _vm.xiaofeipay == 1 &&
    _vm.typeid == "71"
      ? _vm.t("color1")
      : null
  var m51 =
    _vm.isload && _vm.yuanbaopay == 1 && _vm.open_pay && _vm.typeid != "0"
      ? _vm.t("color1")
      : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        m2: m2,
        m3: m3,
        m4: m4,
        m5: m5,
        m6: m6,
        m7: m7,
        m8: m8,
        m9: m9,
        m10: m10,
        l0: l0,
        m12: m12,
        m13: m13,
        m14: m14,
        m15: m15,
        m16: m16,
        m17: m17,
        m18: m18,
        m19: m19,
        m20: m20,
        m21: m21,
        m22: m22,
        m23: m23,
        m24: m24,
        m25: m25,
        m26: m26,
        m27: m27,
        m28: m28,
        m29: m29,
        m30: m30,
        m31: m31,
        m32: m32,
        m33: m33,
        m34: m34,
        m35: m35,
        m36: m36,
        m37: m37,
        m38: m38,
        g0: g0,
        m39: m39,
        m40: m40,
        m41: m41,
        m42: m42,
        m43: m43,
        l1: l1,
        m45: m45,
        m46: m46,
        m47: m47,
        m48: m48,
        m49: m49,
        m50: m50,
        m51: m51,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 1305:
/*!**********************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/pagesExt/pay/pay.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pay.vue?vue&type=script&lang=js& */ 1306);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1306:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!/Users/<USER>/Downloads/tcphp/uniapp/pagesExt/pay/pay.vue?vue&type=script&lang=js& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, uni) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

var app = getApp();
var _default = {
  data: function data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      pre_url: app.globalData.pre_url,
      detailurl: '',
      tourl: '',
      typeid: '0',
      wxpay: 0,
      wxpay_type: 0,
      alipay: 0,
      baidupay: 0,
      toutiaopay: 0,
      moneypay: 0,
      //余额支付开关, 受商品设置影响product_moneypay 0关闭 1开启 2仅限余额
      cancod: 0,
      cod_frontmoney: 0,
      cod_payorderid: 0,
      overdraft_moneypay: 0,
      daifu: 0,
      daifu_txt: '好友代付',
      pay_month: 0,
      pay_transfer: 0,
      codtxt: '',
      pay_month_txt: '',
      give_coupon_list: [],
      give_coupon_num: 0,
      userinfo: [],
      paypwd: '',
      hiddenmodalput: true,
      payorder: {},
      tmplids: [],
      give_coupon_show: false,
      give_coupon_close_url: "",
      more_alipay: 0,
      more_alipay_data: [],
      paypal: 0,
      dapay_union: 0,
      //元宝支付
      yuanbao_money: 0,
      //现金
      total_yuanbao: 0,
      //总元宝
      yuanbao_msg: '',
      //元宝文字描述
      yuanbaopay: 0,
      //是否开启元宝支付
      open_pay: false,
      //打开支付选项
      pay_type: '',
      //支付类型（新增）
      payResponseStatus: false,
      //支付回调是否执行，防止重复跳转

      invite_free: '',
      invite_status: false,
      free_tmplids: '',
      sharepic: app.globalData.initdata.logo,
      adlist: [],
      //广告位

      alih5pay: false,
      alih5: false,
      alipay_type: 0,
      //支付宝普通模式
      ali_appid: '',
      alipayopenid: '',
      xiaofeipay: 0,
      //冻结佣金钱包支付
      alipayPlugin: 0,
      //支付宝交易组件
      is_pingce: 0,
      is_maidan: 0,
      ispost: 0,
      //是否可以提交，防止过快点击次数 0可以，1不可以

      //是否开启余额和微信或支付组合支付
      iscombine: 0,
      combines: {
        'moneypay': 0,
        'wxpay': 0,
        'alipay': 0
      },
      share_payment: 0,
      //付款前分享，0关闭，1开启
      showposter: false,
      share_product: [],
      //分享商品
      moneypay_lvprice_status: false,
      //会员价仅限余额支付
      pay_wx_qrcode_url: '',
      //微信收款二维码
      wxpay_native_h5: false,
      //是否开启微信收款吗
      wx_liji_pay: true,
      //后台控制h5端微信立即支付按钮状态
      liji_pay_button: true,
      //是否开启即支付按钮
      yunshanfuwxpay: 0,
      //云闪付小程序支付
      usegiveorder: false,
      //是否使用赠送礼物功能
      giveordertitle: '',
      giveorderpic: ''
    };
  },
  onShareAppMessage: function onShareAppMessage() {
    var that = this;
    //付款前分享
    if (!app.isEmpty(that.share_product)) {
      that.sharecallback();
      return {
        title: that.share_product.sharetitle,
        path: '/pages/shop/product?id=' + that.share_product.id,
        imageUrl: that.share_product.sharepic,
        desc: that.share_product.sharedesc
      };
    }
    if (that.usegiveorder) {
      return {
        title: that.giveordertitle,
        path: '/pagesC/shop/takegiveorder?payorderid=' + that.payorder.id,
        imageUrl: that.giveorderpic,
        desc: that.giveordertitle
      };
    }
    var title = '您有一份好友代付待查收，请尽快处理！';
    var sharepic = that.sharepic;
    var sharelink = app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#/pagesExt/pay/daifu?scene=id_' + that.payorder.id;
    console.log({
      title: title,
      tolink: sharelink,
      pic: sharepic
    });
    var sharedata = this._sharewx({
      title: title,
      tolink: sharelink,
      pic: sharepic
    });
    return sharedata;
  },
  onShareTimeline: function onShareTimeline() {
    var that = this;
    //付款前分享
    if (!app.isEmpty(that.share_product)) {
      that.sharecallback();
      return {
        title: that.share_product.sharetitle,
        path: '/pages/shop/product?id=' + that.share_product.id,
        imageUrl: that.share_product.sharepic,
        desc: that.share_product.sharedesc
      };
    }
    if (that.usegiveorder) {
      return {
        title: that.giveordertitle,
        path: '/pagesC/shop/takegiveorder?payorderid=' + that.payorder.id,
        imageUrl: that.giveorderpic,
        desc: that.giveordertitle
      };
    }
    var title = '您有一份好友代付待查收，请尽快处理！';
    var sharepic = that.sharepic;
    var sharelink = app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#/pagesExt/pay/daifu?scene=id_' + that.payorder.id;
    var sharewxdata = this._sharewx({
      title: title,
      tolink: sharelink,
      pic: sharepic
    });
    var query = sharewxdata.path.split('?')[1] + '&seetype=circle';
    var link = app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#' + sharewxdata.path.split('?')[0] + '&seetype=circle';
    return {
      title: sharewxdata.title,
      imageUrl: sharewxdata.imageUrl,
      query: query,
      link: link
    };
  },
  onLoad: function onLoad(opt) {
    this.opt = app.getopts(opt);
    if (this.opt.tourl) this.tourl = decodeURIComponent(this.opt.tourl);
    //this.getdata();
    if (this.opt.is_maidan) this.is_maidan = this.opt.is_maidan;
  },
  onShow: function onShow() {
    var that = this;

    //是否可以用uni.getEnterOptionsSync函数，uni有版本限制vue3项目：uni-app 3.2.13+ 支持；vue2项目：uni-app 3.5.1+ 支持。
    var getEnterOptionsSync = true;
    if (getEnterOptionsSync) {
      var opt = wx.getEnterOptionsSync();
      if (opt && opt.referrerInfo && opt.referrerInfo.extraData) {
        var payStatus = opt.referrerInfo.extraData.payStatus;
        //  payStatus: "0"=支付失败；
        //  payStatus: "1"=支付成功；
        //  payStatus: "3"=支付取消；
        if (payStatus == '1') {
          setTimeout(function () {
            if (that.give_coupon_list && Object.keys(that.give_coupon_list).length > 0) {
              that.give_coupon_show = true;
              that.give_coupon_close_url = that.tourl;
            } else {
              that.gotourl(that.tourl, 'reLaunch');
            }
          }, 1000);
        } else if (payStatus == '0') {
          app.alert('支付失败，请重试');
        } else if (payStatus == '3') {
          //取消支付操作
          app.post('ApiPay/cancelpay', {
            orderid: that.opt.id,
            typeid: 0
          }, function (res) {});
        }
      }
    }
    this.getdata();
  },
  onPullDownRefresh: function onPullDownRefresh() {
    this.getdata();
  },
  methods: {
    getdata: function getdata() {
      var that = this;
      that.loading = true;
      var thisurl = '';
      if (app.globalData.platform == 'mp' || app.globalData.platform == 'h5') {
        thisurl = location.href;
      }
      app.post('ApiPay/pay', {
        orderid: that.opt.id,
        is_maidan: that.is_maidan,
        thisurl: thisurl,
        tourl: that.tourl,
        scene: app.globalData.scene
      }, function (res) {
        that.loading = false;
        if (res.status == 0) {
          app.error(res.msg);
          if (res.url && !that.payResponseStatus) {
            setTimeout(function () {
              app.goto(res.url);
            }, 1000);
          }
          return;
        }
        that.is_pingce = res.is_pingce;
        that.wxpay = res.wxpay;
        that.wxpay_type = res.wxpay_type;
        that.alipay = res.alipay;
        that.baidupay = res.baidupay;
        that.toutiaopay = res.toutiaopay;
        that.cancod = res.cancod;
        that.codtxt = res.codtxt;
        that.cod_frontmoney = res.cod_frontmoney || 0;
        that.cod_payorderid = res.cod_payorderid || 0;
        that.daifu = res.daifu;
        that.daifu_txt = res.daifu_txt;
        that.pay_money = res.pay_money;
        that.pay_money_txt = res.pay_money_txt;
        that.moneypay = res.moneypay;
        that.overdraft_moneypay = res.overdraft_moneypay;
        that.xiaofeipay = res.xiaofeipay;
        that.pay_transfer = res.pay_transfer;
        that.pay_transfer_info = res.pay_transfer_info;
        that.pay_month = res.pay_month;
        that.pay_month_txt = res.pay_month_txt;
        that.payorder = res.payorder;
        that.userinfo = res.userinfo;
        that.tmplids = res.tmplids;
        that.give_coupon_list = res.give_coupon_list;
        if (that.give_coupon_list) {
          that.give_coupon_num = 0;
          for (var i in that.give_coupon_list) {
            that.give_coupon_num += that.give_coupon_list[i]['givenum'];
          }
        }
        that.detailurl = res.detailurl;
        that.tourl = res.tourl;
        that.paypal = res.paypal || 0;
        that.adapay_union = res.adapay_union || 0;
        that.more_alipay = res.more_alipay;
        that.more_alipay_data = res.more_alipay_data || [];
        that.yuanbao_money = res.yuanbao_money;
        that.total_yuanbao = res.total_yuanbao;
        that.yuanbao_msg = res.yuanbao_msg;
        that.yuanbaopay = res.yuanbaopay;
        that.wxpay_native_h5 = res.wxpay_native_h5;
        that.wx_liji_pay = res.wx_liji_pay;
        if (that.wxpay) {
          if (that.wxpay_type == 22) {
            that.typeid = 22;
          } else {
            that.typeid = 2;
          }
        } else if (that.alipay) {
          that.typeid = 3;
          if (that.alipay == 2) {
            that.typeid = 23;
          }
        } else if (that.moneypay) {
          that.typeid = 1;
        } else if (that.more_alipay) {
          that.typeid = that.more_alipay_data[0].typeid;
        } else if (that.baidupay) {
          that.typeid = 11;
        } else if (that.toutiaopay) {
          that.typeid = 12;
        } else if (that.xiaofeipay) {
          that.typeid = 71;
        }
        if (that.payorder.money == 0 && that.payorder.score > 0) {
          that.typeid = 1;
        }
        if (res.invite_free) {
          that.invite_free = res.invite_free;
        }
        if (res.free_tmplids) {
          that.free_tmplids = res.free_tmplids;
        }
        if (res.share_payment) {
          that.share_payment = res.share_payment;
        }
        if (res.yunshanfuwxpay) {
          that.yunshanfuwxpay = res.yunshanfuwxpay;
        }
        //支付广告位
        if (res.adlist && res.adlist.length > 0) {
          that.adlist = res.adlist;
        }
        if (res.alih5pay) {
          if (that.alih5) {
            that.wxpay = false;
          }
          that.alih5pay = true;
          if (res.alipay_type) {
            that.alipay_type = res.alipay_type;
            if (res.alipay_type == 3 && that.alih5) {
              var oScript = document.createElement('script');
              oScript.type = 'text/javascript';
              oScript.src = 'https://gw.alipayobjects.com/as/g/h5-lib/alipayjsapi/3.1.1/alipayjsapi.min.js';
              document.body.appendChild(oScript);
            }
          }
          if (res.ali_appid) {
            that.ali_appid = res.ali_appid;
          }
          if (res.alipayopenid) {
            that.alipayopenid = res.alipayopenid;
          }
          if (res.alipayPlugin) {
            that.alipayPlugin = res.alipayPlugin;
          }
        }

        //余额和微信或支付宝组合支付
        if (res.iscombine && res.iscombine == 1) {
          that.iscombine = 1;
          that.changeCombines(that.typeid);
        }

        //会员价仅限余额支付
        if (res.moneypay_lvprice_status) {
          that.moneypay_lvprice_status = true;
          if (that.payorder.moneypaytypeid && that.payorder.moneypaytypeid > 0) {
            that.typeid = that.payorder.moneypaytypeid;
          } else {
            if (res.moneypay == 1 || res.moneypay == 2) {
              that.typeid = 1; //默认余额支付
            }
          }
        }

        //判断h5端微信立即支付按钮是否显示
        if (!that.wx_liji_pay && that.typeid == 2 && app.globalData.platform == 'h5') {
          //如果后台配置是关闭并且是微信支付并且是h5端，则隐藏按钮
          that.liji_pay_button = false;
        }
        that.loaded();

        //付款前分享
        if (!app.isEmpty(res.share_product)) {
          that.share_product = res.share_product;
          var platform = app.getplatform();
        }

        //订单赠好友
        if (res.usegiveorder) {
          that.usegiveorder = res.usegiveorder;
          that.giveordertitle = res.giveordertitle;
          that.giveorderpic = res.giveorderpic;
          that._sharemp({
            title: that.giveordertitle,
            desc: that.giveordertitle,
            link: app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#/pagesC/shop/takegiveorder?payorderid=' + that.payorder.id,
            pic: that.giveorderpic
          });
        }
        if (that.opt && that.opt.paypal == 'success') {
          that.typeid = 51;
          app.showLoading('支付中');
          app.post('ApiPay/paypalRedirect', {
            orderid: that.opt.id,
            paymentId: that.opt.paymentId,
            PayerID: that.opt.PayerID
          }, function (res) {
            app.showLoading(false);
            if (res.status == 1) {
              app.success(res.msg);
              that.subscribeMessage(function () {
                if (that.invite_free) {
                  that.invite_status = true;
                } else {
                  setTimeout(function () {
                    if (that.give_coupon_list && Object.keys(that.give_coupon_list).length > 0) {
                      that.give_coupon_show = true;
                      that.give_coupon_close_url = that.tourl;
                    } else {
                      //uni.navigateBack();
                      that.gotourl(that.tourl, 'reLaunch');
                    }
                  }, 1000);
                }
              });
            } else if (res.status == 0) {
              app.error(res.msg);
            }
          });
        }
      });
    },
    getpwd: function getpwd(done, val) {
      this.paypwd = val;
      this.topay({
        currentTarget: {
          dataset: {
            typeid: 1
          }
        }
      });
    },
    changeradio: function changeradio(e) {
      var that = this;
      var typeid = e.currentTarget.dataset.typeid;
      //会员价仅限余额支付
      if (that.moneypay_lvprice_status) {
        if (typeid == 1) {
          that.payorder['money'] = that.payorder['moneyprice'];
        } else {
          that.payorder['money'] = that.payorder['putongprice'];
        }
      }
      that.typeid = typeid;
      if (that.iscombine == 1) {
        that.changeCombines(typeid);
      }

      //判断h5端微信立即支付按钮是否显示
      if (!that.wx_liji_pay && that.typeid == 2 && app.globalData.platform == 'h5') {
        //如果后台配置是关闭并且是微信支付并且是h5端，则隐藏按钮
        that.liji_pay_button = false;
      } else {
        that.liji_pay_button = true;
      }
    },
    topay: function topay(e) {
      var that = this;

      //付款前分享
      if (that.share_payment == 1 && that.payorder.share_payment_status == 0) {
        that.showposter = true;
        return;
      }

      //是否可以提交
      if (that.ispost != 0) return;
      that.ispost = 1;
      that.payResponseStatus = false;
      var typeid = that.typeid;
      var orderid = this.payorder.id;
      if (e.currentTarget.dataset.isfrontpay == 1) {
        var orderid = this.cod_payorderid;
      }
      if (typeid == 1 || typeid == 71 || typeid == 38) {
        //余额支付
        if (that.userinfo.haspwd && that.paypwd == '') {
          that.$refs.dialogInput.open();
          that.ispost = 0;
          return;
        }
        var money_name = that.t('余额');
        if (typeid == 71) {
          money_name = that.t('冻结佣金');
        } else if (typeid == 38) {
          money_name = that.t('信用额度');
        }
        if (that.payorder.money == 0 && that.payorder.score > 0) {
          var money_name = that.t('积分');
        }
        app.confirm('确定用' + money_name + '支付吗?', function () {
          app.showLoading('提交中');
          app.post('ApiPay/pay', {
            op: 'submit',
            orderid: orderid,
            is_maidan: that.is_maidan,
            typeid: typeid,
            paypwd: that.paypwd,
            pay_type: that.pay_type,
            combines: that.combines
          }, function (res) {
            app.showLoading(false);
            that.ispost = 0;
            if (res.status == 0) {
              that.paypwd = '';
              app.error(res.msg);
              return;
            }
            if (res.status == 2) {
              app.success(res.msg);
              that.subscribeMessage(function () {
                if (that.invite_free) {
                  that.invite_status = true;
                } else if (that.payorder.ictips) {
                  that.$refs.dialogInvitecashback.open();
                } else if (that.usegiveorder) {
                  that.$refs.dialogGiveorder.open();
                } else {
                  setTimeout(function () {
                    if (that.give_coupon_list && Object.keys(that.give_coupon_list).length > 0) {
                      that.give_coupon_show = true;
                      that.give_coupon_close_url = that.tourl;
                    } else {
                      that.gotourl(that.tourl, 'reLaunch');
                    }
                  }, 1000);
                }
              });
              return;
            }
          });
        }, function () {
          //取消支付操作
          app.post('ApiPay/cancelpay', {
            orderid: orderid,
            typeid: typeid
          }, function (res) {});
          setTimeout(function () {
            that.ispost = 0;
          }, 1000);
        });
      } else if (typeid == 2) {
        //微信支付
        console.log('微信支付');
        var wxpaytype = e.currentTarget.dataset.wxpaytype || 0;
        app.showLoading('提交中');
        app.post('ApiPay/pay', {
          op: 'submit',
          orderid: orderid,
          is_maidan: that.is_maidan,
          typeid: typeid,
          combines: that.combines,
          wxpay_type: wxpaytype
        }, function (res) {
          app.showLoading(false);
          that.ispost = 0;
          if (res.status == 0) {
            app.error(res.msg);
            return;
          } else if (res.status == 2) {
            //无需付款
            app.success(res.msg);
            that.subscribeMessage(function () {
              if (that.invite_free) {
                that.invite_status = true;
              } else if (that.payorder.ictips) {
                that.$refs.dialogInvitecashback.open();
              } else if (that.usegiveorder) {
                that.$refs.dialogGiveorder.open();
              } else {
                setTimeout(function () {
                  if (that.give_coupon_list && Object.keys(that.give_coupon_list).length > 0) {
                    that.give_coupon_show = true;
                    that.give_coupon_close_url = that.tourl;
                  } else {
                    that.gotourl(that.tourl, 'reLaunch');
                  }
                }, 1000);
              }
            });
            return;
          } else if (res.status == 3) {
            //跳转到指定页 可返回
            //弹出提示
            app.alert(res.msg, function () {
              that.ispost = 0;
              if (res.url) {
                app.goto(res.url);
              }
            });
            return;
          }
          var opt = res.data;
          if (res.data.pay_wx_qrcode_url) {
            //微信收款码
            that.pay_wx_qrcode_url = res.data.pay_wx_qrcode_url;
            that.$refs.buildWxNativeH5.open();
            return;
          }
          if (app.globalData.platform == 'wx') {
            if (that.wxpay_type == 8) {
              //b2b支付

              console.log('wxpay b2b');
              wx.requestCommonPayment({
                signData: JSON.stringify(opt.signData),
                paySig: opt.paySig,
                signature: opt.signature,
                mode: 'retail_pay_goods',
                success: function success(res) {
                  app.success('付款完成');
                  console.log('requestCommonPayment success', res);
                  if (that.invite_free) {
                    that.invite_status = true;
                  } else if (that.payorder.ictips) {
                    that.$refs.dialogInvitecashback.open();
                  } else if (that.usegiveorder) {
                    that.$refs.dialogGiveorder.open();
                  } else {
                    setTimeout(function () {
                      if (that.give_coupon_list && Object.keys(that.give_coupon_list).length > 0) {
                        that.give_coupon_show = true;
                        that.give_coupon_close_url = that.tourl;
                      } else {
                        that.gotourl(that.tourl, 'reLaunch');
                      }
                    }, 1000);
                  }
                  return;
                },
                fail: function fail(_ref) {
                  var errMsg = _ref.errMsg,
                    errno = _ref.errno;
                  app.error(errMsg + ' ' + errno);
                  console.error(errMsg, errno);
                  return;
                }
              });
            } else if (that.payorder.type == 'shop' || that.wxpay_type == 2) {
              //自定义版交易组件的小程序 或 2二级商户模式
              if (opt.orderInfo) {
                console.log('requestOrderPayment1');
                wx.requestOrderPayment({
                  'timeStamp': opt.timeStamp,
                  'nonceStr': opt.nonceStr,
                  'package': opt.package,
                  'signType': opt.signType ? opt.signType : 'MD5',
                  'paySign': opt.paySign,
                  'orderInfo': opt.orderInfo,
                  'success': function success(res2) {
                    that.payResponseStatus = true;
                    app.success('付款完成');
                    that.subscribeMessage(function () {
                      if (that.invite_free) {
                        that.invite_status = true;
                      } else if (that.payorder.ictips) {
                        that.$refs.dialogInvitecashback.open();
                      } else if (that.usegiveorder) {
                        that.$refs.dialogGiveorder.open();
                      } else {
                        setTimeout(function () {
                          if (that.give_coupon_list && Object.keys(that.give_coupon_list).length > 0) {
                            that.give_coupon_show = true;
                            that.give_coupon_close_url = that.tourl;
                          } else {
                            that.gotourl(that.tourl, 'reLaunch');
                          }
                        }, 1000);
                      }
                    });
                  },
                  'fail': function fail(res2) {
                    //app.alert(JSON.stringify(res2))
                  },
                  'complete': function complete(res2) {
                    if (res2 && res2.errMsg && res2.errMsg == 'requestPayment:fail cancel') {
                      //取消支付操作
                      app.post('ApiPay/cancelpay', {
                        orderid: orderid,
                        typeid: typeid
                      }, function (res3) {});
                    }
                  }
                });
              } else if (opt.sxpay && opt.path) {
                //随行付
                uni.openEmbeddedMiniProgram({
                  appId: opt.appId,
                  path: opt.path,
                  extraData: {},
                  success: function success(res) {
                    console.log('随行付半屏小程序打开');
                  }
                });
              } else {
                console.log('requestOrderPayment2');
                wx.requestOrderPayment({
                  'timeStamp': opt.timeStamp,
                  'nonceStr': opt.nonceStr,
                  'package': opt.package,
                  'signType': opt.signType ? opt.signType : 'MD5',
                  'paySign': opt.paySign,
                  'success': function success(res2) {
                    that.payResponseStatus = true;
                    app.success('付款完成');
                    that.subscribeMessage(function () {
                      if (that.invite_free) {
                        console.log('invite_free');
                        that.invite_status = true;
                      } else if (that.payorder.ictips) {
                        console.log('ictips', that.payorder.ictips);
                        that.$refs.dialogInvitecashback.open();
                      } else if (that.usegiveorder) {
                        that.$refs.dialogGiveorder.open();
                      } else {
                        setTimeout(function () {
                          if (that.give_coupon_list && Object.keys(that.give_coupon_list).length > 0) {
                            that.give_coupon_show = true;
                            that.give_coupon_close_url = that.tourl;
                          } else {
                            console.log('tourl', that.tourl);
                            that.gotourl(that.tourl, 'reLaunch');
                          }
                        }, 1000);
                      }
                    });
                  },
                  'fail': function fail(res2) {
                    //app.alert(JSON.stringify(res2))
                  },
                  'complete': function complete(res2) {
                    if (res2 && res2.errMsg && res2.errMsg == 'requestPayment:fail cancel') {
                      //取消支付操作
                      app.post('ApiPay/cancelpay', {
                        orderid: orderid,
                        typeid: typeid
                      }, function (res3) {});
                    }
                  }
                });
              }
            } else {
              //普通的微信支付
              if (opt.sxpay && opt.path) {
                //随行付
                uni.openEmbeddedMiniProgram({
                  appId: opt.appId,
                  path: opt.path,
                  extraData: {},
                  success: function success(res) {
                    console.log('随行付半屏小程序打开');
                  }
                });
              } else {
                console.log('wxpay default 默认的微信支付');
                uni.requestPayment({
                  'provider': 'wxpay',
                  'timeStamp': opt.timeStamp,
                  'nonceStr': opt.nonceStr,
                  'package': opt.package,
                  'signType': opt.signType ? opt.signType : 'MD5',
                  'paySign': opt.paySign,
                  'success': function success(res2) {
                    that.payResponseStatus = true;
                    app.success('付款完成');
                    that.subscribeMessage(function () {
                      if (that.invite_free) {
                        console.log('invite_free');
                        that.invite_status = true;
                      } else if (that.payorder.ictips) {
                        console.log('ictips', that.payorder.ictips);
                        that.$refs.dialogInvitecashback.open();
                      } else if (that.usegiveorder) {
                        that.$refs.dialogGiveorder.open();
                      } else {
                        setTimeout(function () {
                          if (that.give_coupon_list && Object.keys(that.give_coupon_list).length > 0) {
                            console.log('give_coupon_list', that.give_coupon_list);
                            that.give_coupon_show = true;
                            that.give_coupon_close_url = that.tourl;
                          } else {
                            console.log('tourl', that.tourl);
                            that.gotourl(that.tourl, 'reLaunch');
                          }
                        }, 1000);
                      }
                    });
                  },
                  'fail': function fail(res2) {
                    console.log(res2);
                    //app.alert(JSON.stringify(res2))
                  },

                  'complete': function complete(res2) {
                    if (res2 && res2.errMsg && res2.errMsg == 'requestPayment:fail cancel') {
                      //取消支付操作
                      app.post('ApiPay/cancelpay', {
                        orderid: orderid,
                        typeid: typeid
                      }, function (res3) {});
                    }
                  }
                });
              }
            }
          } else if (app.globalData.platform == 'mp') {} else if (app.globalData.platform == 'h5') {} else if (app.globalData.platform == 'app') {
            console.log(opt);
          } else if (app.globalData.platform == 'qq') {}
        });
      } else if (typeid == 3 || typeid >= 302 && typeid <= 330) {
        //支付宝支付
        setTimeout(function () {
          that.ispost = 0;
        }, 1000);
        if (that.alih5pay && that.alih5 && that.alipay_type == 3) {
          console.log('alih5pay');
          if (!that.alipayopenid) {
            if (that.ali_appid) {
              var ali_appid = that.ali_appid;
              ap.getAuthCode({
                appId: ali_appid,
                scopes: ['auth_base']
              }, function (res) {
                //var res = JSON.stringify(res);
                if (!res.error && res.authCode) {
                  app.post('ApiIndex/setalipayopenid', {
                    code: res.authCode,
                    platform: "h5"
                  }, function (res) {
                    if (res.status == 1) {
                      that.alipayopenid = res.openid;
                      that.alih5_pay(orderid, typeid);
                    } else {
                      app.alert(res.msg);
                      return;
                    }
                  });
                } else {
                  if (res.errorMessage) {
                    app.alert(res.errorMessage);
                  } else if (res.errorDesc) {
                    app.alert(res.errorDesc);
                  } else {
                    app.alert('授权出错');
                  }
                  return;
                }
              });
            } else {
              app.alert('系统未配置支付宝参数');
              return;
            }
          } else {
            that.alih5_pay(orderid, typeid);
          }
        } else {
          console.log('alih5pay2');
          if (app.globalData.platform == 'mp') {
            var gourl = '/pages/index/webView2?orderid=' + orderid + '&typeid=' + typeid + '&aid=' + app.globalData.aid + '&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id;
            if (that.iscombine && that.combines.moneypay == 1) {
              gourl += '&moneypay=1';
            }
            app.goto(gourl);
            return;
          }
          //支付宝交易组件
          if (that.alipayPlugin && app.globalData.platform == 'alipay') {
            //判断是否需要使用交易组件创建订单
          } else {
            console.log('alipaySubmit before');
            that.alipaySubmit(orderid, typeid, 0);
          }
        }
      } else if (typeid == '11') {} else if (typeid == '12') {} else if (typeid == '22') {
        if (app.globalData.platform == 'wx') {
          wx.login({
            success: function success(res) {
              if (res.code) {
                app.showLoading('提交中');
                app.post('ApiPay/getYunMpauthParams', {
                  jscode: res.code
                }, function (res) {
                  app.showLoading(false);
                  that.ispost = 0;
                  app.post('https://showmoney.cn/scanpay/fixed/mpauth', res.params, function (res2) {
                    console.log(res2.sessionKey);
                    app.post('ApiPay/getYunUnifiedParams', {
                      orderid: orderid,
                      sessionKey: res2.sessionKey
                    }, function (res3) {
                      app.post('https://showmoney.cn/scanpay/unified', res3.params, function (res4) {
                        if (res4.respcd == '09') {
                          wx.requestPayment({
                            timeStamp: res4.timeStamp,
                            nonceStr: res4.nonceStr,
                            package: res4.package,
                            signType: res4.mpSignType,
                            paySign: res4.mpSign,
                            success: function success(result) {
                              app.success('付款完成');
                              that.subscribeMessage(function () {
                                if (that.invite_free) {
                                  that.invite_status = true;
                                } else if (that.payorder.ictips) {
                                  that.$refs.dialogInvitecashback.open();
                                } else if (that.usegiveorder) {
                                  that.$refs.dialogGiveorder.open();
                                } else {
                                  setTimeout(function () {
                                    if (that.give_coupon_list && Object.keys(that.give_coupon_list).length > 0) {
                                      that.give_coupon_show = true;
                                      that.give_coupon_close_url = that.tourl;
                                    } else {
                                      that.gotourl(that.tourl, 'reLaunch');
                                    }
                                  }, 1000);
                                }
                              });
                            },
                            fail: function fail(res5) {
                              //app.alert(JSON.stringify(res5))
                            },
                            complete: function complete(res5) {
                              if (res5 && res5.errMsg && res5.errMsg == 'requestPayment:fail cancel') {
                                //取消支付操作
                                app.post('ApiPay/cancelpay', {
                                  orderid: orderid,
                                  typeid: typeid
                                }, function (res6) {});
                              }
                            }
                          });
                        } else {
                          app.alert(res4.errorDetail);
                        }
                      });
                    });
                  });
                });
              } else {
                that.ispost = 0;
                console.log('登录失败！' + res.errMsg);
              }
            },
            fail: function fail(res) {
              that.ispost = 0;
            }
          });
        } else {}
      } else if (typeid == '23') {
        //var url = app.globalData.baseurl + 'ApiPay/pay'+'&aid=' + app.globalData.aid + '&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id;
        //url += '&op=submit&orderid='+orderid+'&typeid=23';
        //location.href = url;
        setTimeout(function () {
          that.$refs.dialogPayconfirm.open();
          that.ispost = 0;
        }, 1000);
        app.goto('/pages/index/webView2?orderid=' + orderid + '&typeid=23' + '&aid=' + app.globalData.aid + '&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id);
        return;
        // app.showLoading('提交中');
        // app.post('ApiPay/pay',{op:'submit',orderid: orderid,typeid: 23},function(res){
        // 	app.showLoading(false);
        //       that.ispost = 0;
        // 	console.log(res)
        // 	app.goto('url::'+res.url);
        // });
      } else if (typeid == '24') {
        //var url = app.globalData.baseurl + 'ApiPay/pay'+'&aid=' + app.globalData.aid + '&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id;
        //url += '&op=submit&orderid='+orderid+'&typeid=23';
        //location.href = url;
        setTimeout(function () {
          that.ispost = 0;
        }, 1000);
        app.goto('/pages/index/webView2?orderid=' + orderid + '&typeid=24');
        return;
        // app.showLoading('提交中');
        // app.post('ApiPay/pay',{op:'submit',orderid: orderid,typeid: 24},function(res){
        // 	app.showLoading(false);
        // that.ispost = 0;
        // 	console.log(res)
        // 	app.goto('url::'+res.url);
        // });
      } else if (typeid == 'yuanbao') {
        //元宝支付
        var total_yuanbao = that.total_yuanbao - 0;
        var u_yuanbao = that.userinfo.yuanbao - 0;
        if (total_yuanbao > u_yuanbao) {
          app.alert(that.t('元宝') + '不足');
          return;
        }
        that.open_pay = true;
        that.pay_type = 'yuanbao';
        that.ispost = 0;
      } else if (typeid == '51') {
        app.showLoading('提交中');
        app.post('ApiPay/pay', {
          op: 'submit',
          orderid: orderid,
          typeid: typeid
        }, function (res) {
          app.showLoading(false);
          that.ispost = 0;
          console.log(res);
          if (res.status == 1) {
            if (app.globalData.platform == 'app') {
              var wv = plus.webview.create("", "custom-webview", {
                top: uni.getSystemInfoSync().statusBarHeight + 44
              });
              wv.loadURL(res.data);
              var currentWebview = that.$scope.$getAppWebview();
              currentWebview.append(wv);
            } else {
              app.goto('url::' + res.data);
            }
          } else {
            app.alert(res.msg);
          }
        });
      } else if (typeid == '61') {
        app.showLoading('提交中');
        app.post('ApiPay/pay', {
          op: 'submit',
          orderid: orderid,
          typeid: typeid
        }, function (res) {
          app.showLoading(false);
          that.ispost = 0;
          console.log(res);
          if (res.status == 1) {
            if (app.globalData.platform == 'app') {} else {
              var div = document.createElement('div');
              div.innerHTML = res.data; //后台返回接收到的html数据
              document.body.appendChild(div);
              document.forms[0].submit();
            }
          } else {
            app.alert(res.msg);
          }
        });
      } else if (typeid == '122' && app.globalData.platform == 'wx') {
        app.post('ApiPay/pay', {
          op: 'submit',
          orderid: orderid,
          typeid: typeid
        }, function (res) {
          if (res.status === 0) {
            that.ispost = 0;
            return app.alert(res.msg);
          }
          //检查 appId 和 path 是否有效
          if (!res.data.cqpMpAppId || !res.data.cqpMpPath) {
            return app.alert('小程序信息不完整');
          }
          //跳转到云闪付小程序
          uni.navigateToMiniProgram({
            appId: res.data.cqpMpAppId,
            path: res.data.cqpMpPath,
            success: function success(e) {
              that.subscribeMessage(function () {
                setTimeout(function () {
                  if (that.give_coupon_list && Object.keys(that.give_coupon_list).length > 0) {
                    that.give_coupon_show = true;
                    that.give_coupon_close_url = that.tourl;
                  } else {
                    that.gotourl(that.tourl, 'reLaunch');
                  }
                }, 1000);
              });
              return;
            },
            fail: function fail() {
              that.ispost = 0;
            }
          });
        });
      }
    },
    alipaySubmit: function alipaySubmit(orderid, typeid, alipayPluginOrder, sourceId) {
      //如果开通了交易组件且有权限，则同步创建交易组件订单
      if (!alipayPluginOrder) {
        alipayPluginOrder = 0;
      }
      if (!sourceId) {
        sourceId = '';
      }
      var that = this;
      app.showLoading('提交中');
      app.post('ApiPay/pay', {
        op: 'submit',
        orderid: orderid,
        typeid: typeid,
        alipayPluginOrder: alipayPluginOrder,
        sourceId: sourceId,
        combines: that.combines
      }, function (res) {
        console.log(res);
        app.showLoading(false);
        if (res.status == 0) {
          app.error(res.msg);
          return;
        }
        if (res.status == 2) {
          //无需付款
          app.success(res.msg);
          that.subscribeMessage(function () {
            if (that.invite_free) {
              that.invite_status = true;
            } else {
              setTimeout(function () {
                if (that.give_coupon_list && Object.keys(that.give_coupon_list).length > 0) {
                  that.give_coupon_show = true;
                  that.give_coupon_close_url = that.tourl;
                } else {
                  that.gotourl(that.tourl, 'reLaunch');
                }
              }, 1000);
            }
          });
          return;
        }
        var opt = res.data;
        if (app.globalData.platform == 'alipay') {} else if (app.globalData.platform == 'mp' || app.globalData.platform == 'h5') {
          if (res.type && res.type == 'adapay') {
            window.location.href = res.data;
          } else if (res.type && res.type == 'huifu') {
            window.location.href = res.data.payurl;
          } else {
            document.body.innerHTML = res.data;
            document.forms['alipaysubmit'].submit();
          }
        } else if (app.globalData.platform == 'app') {}
      });
    },
    topay2: function topay2() {
      var that = this;
      var orderid = this.payorder.id;
      if (this.cod_frontmoney > 0) {
        this.topay({
          currentTarget: {
            dataset: {
              isfrontpay: 1
            }
          }
        });
        return;
      }
      app.confirm('确定要' + that.codtxt + '吗?', function () {
        app.showLoading('提交中');
        app.post('ApiPay/pay', {
          op: 'submit',
          orderid: orderid,
          typeid: 4
        }, function (res) {
          app.showLoading(false);
          if (res.status == 0) {
            app.error(res.msg);
            return;
          }
          if (res.status == 2) {
            //无需付款
            app.success(res.msg);
            that.subscribeMessage(function () {
              setTimeout(function () {
                that.gotourl(that.tourl, 'reLaunch');
              }, 1000);
            });
            return;
          }
        });
      }, function () {
        //取消支付操作
        app.post('ApiPay/cancelpay', {
          orderid: orderid,
          typeid: 4
        }, function (res) {});
      });
    },
    topayMonth: function topayMonth() {
      var that = this;
      var orderid = this.payorder.id;
      app.confirm('确定要' + that.pay_month_txt + '支付吗?', function () {
        app.showLoading('提交中');
        app.post('ApiPay/pay', {
          op: 'submit',
          orderid: orderid,
          typeid: 41
        }, function (res) {
          app.showLoading(false);
          if (res.status == 0) {
            app.error(res.msg);
            return;
          }
          if (res.status == 2) {
            //无需付款
            app.success(res.msg);
            that.subscribeMessage(function () {
              setTimeout(function () {
                that.gotourl(that.tourl, 'reLaunch');
              }, 1000);
            });
            return;
          }
        });
      }, function () {
        //取消支付操作
        app.post('ApiPay/cancelpay', {
          orderid: orderid,
          typeid: 41
        }, function (res) {});
      });
    },
    topayTransfer: function topayTransfer(e) {
      var that = this;
      var orderid = this.payorder.id;
      app.confirm('确定要' + e.currentTarget.dataset.text + '吗?', function () {
        app.showLoading('提交中');
        app.post('ApiPay/pay', {
          op: 'submit',
          orderid: orderid,
          typeid: 5
        }, function (res) {
          app.showLoading(false);
          if (res.status == 1) {
            //需审核付款
            app.success(res.msg);
            setTimeout(function () {
              app.goto(res.gotourl, 'reLaunch');
            }, 1000);
            return;
          } else if (res.status == 2) {
            //无需付款
            app.success(res.msg);
            setTimeout(function () {
              that.gotourl('transfer?id=' + orderid, 'reLaunch');
            }, 1000);
            return;
          } else {
            app.error(res.msg);
            return;
          }
        });
      }, function () {
        //取消支付操作
        app.post('ApiPay/cancelpay', {
          orderid: orderid,
          typeid: 5
        }, function (res) {});
      });
    },
    give_coupon_close: function give_coupon_close(e) {
      var that = this;
      var tourl = e.currentTarget.dataset.url;
      this.give_coupon_show = false;
      that.gotourl(tourl, 'reLaunch');
    },
    gotourl: function gotourl(tourl, opentype) {
      var that = this;
      if (app.globalData.platform == 'mp' || app.globalData.platform == 'h5') {
        if (tourl.indexOf('miniProgram::') === 0) {
          //其他小程序
          tourl = tourl.slice(13);
          var tourlArr = tourl.split('|');
          console.log(tourlArr);
          that.showOpenWeapp();
          return;
        }
        if (tourl.indexOf('/h5zb/client/main') === 0) {
          app.goback();
          return;
        }
      }
      if (that.is_pingce == 1) {
        that.paysuccessToUrl();
        return;
      }
      app.goto(tourl, opentype);
    },
    showOpenWeapp: function showOpenWeapp() {
      this.$refs.dialogOpenWeapp.open();
    },
    paysuccessToUrl: function paysuccessToUrl() {
      //  支付成功后查询订单详情
      app.get('ApiOrder/pingceOrder', {
        id: this.payorder.orderid
      }, function (res) {
        app.goto(res.url, 'reLaunch');
      });
    },
    closeOpenWeapp: function closeOpenWeapp() {
      this.$refs.dialogOpenWeapp.close();
    },
    PayconfirmFun: function PayconfirmFun() {
      this.gotourl(this.tourl, 'reLaunch');
    },
    close_pay: function close_pay() {
      var that = this;
      that.open_pay = false;
    },
    closeInvite: function closeInvite() {
      var that = this;
      that.invite_status = false;
      setTimeout(function () {
        if (that.give_coupon_list && Object.keys(that.give_coupon_list).length > 0) {
          that.give_coupon_show = true;
          that.give_coupon_close_url = that.tourl;
        } else {
          that.gotourl(that.tourl, 'reLaunch');
        }
      }, 1000);
    },
    gotoInvite: function gotoInvite() {
      var that = this;
      var free_tmplids = that.free_tmplids;
      if (free_tmplids && free_tmplids.length > 0) {
        uni.requestSubscribeMessage({
          tmplIds: free_tmplids,
          success: function success(res) {
            console.log(res);
          },
          fail: function fail(res) {
            console.log(res);
          }
        });
      }
      app.goto('/pagesExt/invite_free/index', 'reLaunch');
    },
    todaifu: function todaifu(e) {
      var that = this;
      var platform = app.getplatform();
      var id = that.payorder.id;
      if (platform == 'mp' || platform == 'h5') {
        var sharelink = app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#/pagesExt/pay/daifu?scene=id_' + that.payorder.id;
        this._sharemp({
          title: "您有一份好友代付待查收，请尽快处理~",
          link: sharelink,
          pic: that.sharepic
        });
        app.error('点击右上角发送给好友或分享到朋友圈');
      } else if (platform == 'app') {} else {
        app.error('该终端不支持此操作');
      }
    },
    alih5_pay: function alih5_pay(orderid, typeid) {},
    buildWxNativeH5Close: function buildWxNativeH5Close() {
      var that = this;
      that.$refs.buildWxNativeH5.close();
    },
    dialogInvitecashbackClose: function dialogInvitecashbackClose() {
      var that = this;
      that.$refs.dialogInvitecashback.close();
      setTimeout(function () {
        if (that.give_coupon_list && Object.keys(that.give_coupon_list).length > 0) {
          that.give_coupon_show = true;
          that.give_coupon_close_url = that.tourl;
        } else {
          that.gotourl(that.tourl, 'reLaunch');
        }
      }, 900);
    },
    changeCombines: function changeCombines(typeid) {
      var that = this;
      //查询是否开启余额组合支付
      if (that.iscombine == 1) {
        var combines = that.combines;
        if (typeid == 1) {
          if (combines.moneypay == 0) {
            combines.moneypay = typeid;
            that.typeid = combines.wxpay ? combines.wxpay : combines.alipay ? combines.alipay : 1;
          } else {
            combines.moneypay = 0;
            that.typeid = combines.wxpay ? combines.wxpay : combines.alipay ? combines.alipay : 0;
          }
        } else if (typeid == 2) {
          if (combines.wxpay == 0) {
            combines.wxpay = typeid;
            combines.alipay = 0;
          } else {
            combines.wxpay = 0;
            combines.alipay = 0;
            that.typeid = combines.moneypay ? combines.moneypay : 0;
          }
        } else if (typeid == 3 || typeid >= 302 && typeid <= 330) {
          if (combines.alipay == 0) {
            combines.wxpay = 0;
            combines.alipay = typeid;
          } else {
            combines.wxpay = 0;
            combines.alipay = 0;
            that.typeid = combines.moneypay ? combines.moneypay : 0;
          }
        } else {
          combines = {
            'moneypay': 0,
            'wxpay': 0,
            'alipay': 0
          };
        }
        that.combines = combines;
      }
    },
    topay3: function topay3(e) {
      var that = this;
      var msg = '确定选择组合支付吗？组合支付将直接扣除' + that.t('余额') + '部分，剩余部分由其他支付方式支付。';
      app.confirm(msg, function () {
        that.topay(e);
      });
    },
    posterDialogClose: function posterDialogClose() {
      this.showposter = false;
    },
    sharemp: function sharemp() {
      var that = this;
      var platform = app.getplatform();
      if (platform == 'mp') {
        if (!app.isEmpty(that.share_product)) {
          var share = that.share_product;
          this._sharemp({
            title: share.sharetitle,
            desc: share.sharedesc,
            link: app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#/pages/shop/product?id=' + share.id,
            pic: share.sharepic,
            callback: function callback() {
              that.sharecallback();
            }
          });
          app.error('点击右上角发送给好友或分享到朋友圈');
        }
      }
    },
    sharecallback: function sharecallback() {
      var that = this;
      app.post('ApiPay/sharePaymentStatus', {
        orderid: that.payorder.id
      }, function (res) {
        if (res.status == 1) {
          that.showposter = false;
          that.share_payment = 0;
        }
      });
    },
    giveordersharemp: function giveordersharemp() {
      if (app.globalData.platform == 'mp') {
        var msg = '复制链接成功，或点击右上角发送给好友';
      } else {
        var msg = '复制成功,快去分享吧';
      }
      var that = this;
      var shareLink = app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#/pagesC/shop/takegiveorder?scene=pid_' + app.globalData.mid + '&payorderid=' + that.payorder.id + '&title=领礼物：' + that.giveordertitle;
      uni.setClipboardData({
        data: shareLink,
        success: function success() {
          uni.showToast({
            title: msg,
            duration: 3000,
            icon: 'none'
          });
        },
        fail: function fail(err) {
          uni.showToast({
            title: '复制失败',
            duration: 2000,
            icon: 'none'
          });
        }
      });
    },
    giveordershareapp: function giveordershareapp() {
      var that = this;
      uni.showActionSheet({
        itemList: ['发送给微信好友', '分享到微信朋友圈'],
        success: function success(res) {
          if (res.tapIndex >= 0) {
            var scene = 'WXSceneSession';
            if (res.tapIndex == 1) {
              scene = 'WXSenceTimeline';
            }
            var sharedata = {};
            sharedata.provider = 'weixin';
            sharedata.type = 0;
            sharedata.scene = scene;
            sharedata.title = that.giveordertitle;
            sharedata.summary = '';
            sharedata.href = app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#/pagesC/shop/takegiveorder?scene=pid_' + app.globalData.mid + '&payorderid=' + that.payorder.id + '&title=领礼物：' + that.giveordertitle;
            sharedata.imageUrl = that.giveorderpic;
            var sharelist = app.globalData.initdata.sharelist;
            if (sharelist) {
              for (var i = 0; i < sharelist.length; i++) {
                if (sharelist[i]['indexurl'] == app.globalData.initdata.indexurl) {
                  sharedata.title = sharelist[i].title;
                  sharedata.summary = sharelist[i].desc;
                  sharedata.imageUrl = sharelist[i].pic;
                  if (sharelist[i].url) {
                    var sharelink = sharelist[i].url;
                    if (sharelink.indexOf('/') === 0) {
                      sharelink = app.globalData.pre_url + '/h5/' + app.globalData.aid + '.html#' + sharelink;
                    }
                    if (app.globalData.mid > 0) {
                      sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid=' + app.globalData.mid;
                    }
                    sharedata.href = sharelink;
                  }
                }
              }
            }
            uni.share(sharedata);
          }
        }
      });
    },
    dialogGiveorderClose: function dialogGiveorderClose() {
      var that = this;
      that.$refs.dialogGiveorder.close();
      setTimeout(function () {
        if (that.give_coupon_list && Object.keys(that.give_coupon_list).length > 0) {
          that.give_coupon_show = true;
          that.give_coupon_close_url = that.tourl;
        } else {
          that.gotourl(that.tourl, 'reLaunch');
        }
      }, 900);
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 1307:
/*!******************************************************************************************************!*\
  !*** /Users/<USER>/Downloads/tcphp/uniapp/pagesExt/pay/pay.vue?vue&type=style&index=0&lang=css& ***!
  \******************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_pay_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pay.vue?vue&type=style&index=0&lang=css& */ 1308);
/* harmony import */ var _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_pay_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_pay_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_pay_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_pay_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Applications_HBuilderX_app_Contents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_pay_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1308:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!/Users/<USER>/Downloads/tcphp/uniapp/pagesExt/pay/pay.vue?vue&type=style&index=0&lang=css& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[1301,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pagesExt/pay/pay.js.map