<view class="container"><block wx:if="{{isload}}"><block><form report-submit="true" data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="orderinfo"><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{detail.ordernum}}</text></view><view class="item"><text class="t1">订单金额</text><text class="t2 red">{{"¥"+detail.money}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{orderDetail.createtime}}</text></view><block wx:if="{{detail.paytypeid}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{detail.paytype}}</text></view></block><block wx:if="{{pay_transfer_info.pay_transfer_account_name}}"><view class="item"><text class="t1">户名</text><text class="t2">{{pay_transfer_info.pay_transfer_account_name}}</text></view></block><block wx:if="{{pay_transfer_info.pay_transfer_account}}"><view class="item"><text class="t1">账户</text><text class="t2">{{pay_transfer_info.pay_transfer_account}}</text></view></block><block wx:if="{{pay_transfer_info.pay_transfer_bank}}"><view class="item"><text class="t1">开户行</text><text class="t2">{{pay_transfer_info.pay_transfer_bank}}</text></view></block><block wx:if="{{pay_transfer_info.pay_transfer_qrcode}}"><view class="item"><text class="t1">图片</text><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;" id="content_picpreview"><block wx:for="{{pay_transfer_info.pay_transfer_qrcode_arr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></view></view></block><block wx:if="{{pay_transfer_info.pay_transfer_desc}}"><view class="item"><text class="text-min">{{pay_transfer_info.pay_transfer_desc}}</text></view></block><view class="item"><text class="t1">审核状态</text><text class="t2">{{detail.check_status_label}}</text></view><block wx:if="{{detail.check_remark}}"><view class="item"><text class="t1">审核备注</text><text class="t2">{{detail.check_remark}}</text></view></block></view><view class="form-content"><view class="form-item flex-col"><view class="label">上传付款凭证(最多三张)</view><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;" id="content_picpreview"><block wx:for="{{pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><block wx:if="{{detail.check_status!=1}}"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pics" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/ico-del.png'}}"></image></view></block><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pics" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view></view></view><block wx:if="{{detail.check_status!=1}}"><button class="btn" style="{{'background:'+($root.m0)+';'}}" form-type="submit">确定</button></block><block wx:if="{{detail.money_recharge_transfer}}"><view data-event-opts="{{[['tap',[['goback',['$event']]]]]}}" class="btn-a" bindtap="__e">返回</view></block><block wx:else><view class="btn-a" data-url="{{'pages/order/detail?id='+detail.orderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看订单详情</view></block><block wx:if="{{detail.transfer_order_parent_check}}"><view class="btn-a" style="padding:0 30rpx;" data-url="pages/my/usercenter" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">暂不上传凭证</view></block><view style="padding-top:30rpx;"></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="6726c1e0-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="6726c1e0-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="6726c1e0-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>