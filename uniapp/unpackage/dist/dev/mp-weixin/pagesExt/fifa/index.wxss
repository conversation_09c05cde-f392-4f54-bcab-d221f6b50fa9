
page{
	background: linear-gradient(90deg, #405ADD 0%, #695DDE 100%);
}


.banner.data-v-4920fa35{
	position: absolute;
	width: 100%;
	display: block;
}
.page.data-v-4920fa35{
	position: relative;
	padding: 35rpx 30rpx 30rpx 30rpx;
}
.title.data-v-4920fa35{
	position: relative;
	font-size: 36rpx;
	font-family: Source Han Sans CN;
	font-weight: bold;
	color: #FFFFFF;
	text-align: center;
}
.title image.data-v-4920fa35{
	position: absolute;
	height: 45rpx;
	width: 45rpx;
	left: 30rpx;
	top: 0;
	bottom: 0;
	margin: auto 0;
}
.my.data-v-4920fa35{
	position: relative;
	padding: 70rpx 0;
}
.my_title.data-v-4920fa35{
	font-size: 48rpx;
	font-family: Source Han Sans CN;
	font-weight: bold;
	color: #FFFFFF;
}
.my_text.data-v-4920fa35{
	font-size: 24rpx;
	font-family: Source <PERSON> Sans CN;
	font-weight: 400;
	color: #C9D4FF;
	margin-top: 20rpx;
}
.my_text text.data-v-4920fa35{
	color: #FFCA4C;
}
.my_data.data-v-4920fa35{
	margin-top: 50rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.my_grade.data-v-4920fa35{
	display: flex;
	width: 270rpx;
	justify-content: space-between;
}
.my_value.data-v-4920fa35{
	font-size: 36rpx;
	font-family: Source Han Sans CN;
	font-weight: bold;
	color: #FFFFFF;
	text-align: center;
}
.my_lable.data-v-4920fa35{
	font-size: 24rpx;
	font-family: Source Han Sans CN;
	font-weight: 400;
	color: #C9D4FF;
	margin-top: 20rpx;
	text-align: center;
}
.my_btn.data-v-4920fa35{
	width: 213rpx;
	height: 72rpx;
	background: #FFCA4C;
	border-radius: 36rpx;
	font-size: 24rpx;
	font-family: Source Han Sans CN;
	font-weight: 400;
	color: #533F10;
	display: flex;
	align-items: center;
	justify-content: center;
}
.my_btn image.data-v-4920fa35{
	height: 28rpx;
	width: 28rpx;
	margin-right: 5rpx;
}
.table.data-v-4920fa35{
	padding: 0 0 5rpx 0;
	white-space: nowrap;
}
.table_item.data-v-4920fa35{
	display: inline-block;
	padding: 10rpx 30rpx;
	border-radius: 20rpx;
	color: rgba(255, 255, 255, 0.3);
}
.table_time.data-v-4920fa35{
	font-size: 24rpx;
	font-family: Source Han Sans CN;
	font-weight: 400;
	text-align: center;
}
.table_week.data-v-4920fa35{
	font-size: 20rpx;
	font-family: Source Han Sans CN;
	font-weight: 400;
	text-align: center;
	margin-top: 5rpx;
}
.table_active.data-v-4920fa35{
	color: #fff;
	background: rgba(255, 255, 255, 0.12);
}
.module.data-v-4920fa35{
	position: relative;
	padding: 70rpx 30rpx 45rpx 30rpx;
	background: #f5f5ff;
	border-radius: 20rpx;
	margin-top: 30rpx;
}
.module_data.data-v-4920fa35{
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	width: 240rpx;
	height: 95rpx;
	color: #C1C0C0;
	margin: 0 auto;
}
.module_active.data-v-4920fa35{
	color: #415CC0;
}
.module_tag.data-v-4920fa35{
	width: 240rpx;
	height: 95rpx;
}
.module_title.data-v-4920fa35{
	position: absolute;
	width: 100%;
	text-align: center;
	top: 20rpx;
	font-size: 20rpx;
	line-height: 20rpx;
	font-family: Source Han Sans CN;
	font-weight: 400;
}
.module_time.data-v-4920fa35{
	position: absolute;
	width: 100%;
	text-align: center;
	top: 55rpx;
	font-size: 28rpx;
	line-height: 28rpx;
	font-family: DIN Pro;
	font-weight: bold;
}
.module_content.data-v-4920fa35{
	display: flex;
	align-items: flex-end;
}
.module_team.data-v-4920fa35{
	flex: 1;
}
.module_head.data-v-4920fa35{
	width: 80rpx;
	height:53rpx;
	display: block;
	margin: 0 auto;
}
.module_text.data-v-4920fa35{
	font-size: 24rpx;
	font-family: Source Han Sans CN;
	font-weight: 400;
	color: #121212;
	margin-top: 15rpx;
	text-align: center;
}
.module_grade.data-v-4920fa35{
	width: 60rpx;
	height: 72rpx;
	background: #3D5C9B;
	border-radius: 8rpx;
	font-size: 36rpx;
	font-weight: bold;
	color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
}
.module_r.data-v-4920fa35{
	background: #AB3F3F;
}
.module_state.data-v-4920fa35{
	padding: 0 35rpx;
}
.module_vs.data-v-4920fa35{
	font-size: 24rpx;
	line-height: 24rpx;
	font-family: DIN Pro;
	font-weight: bold;
	color: #415CC0;
	text-align: center;
}
.module_status.data-v-4920fa35{
	font-size: 20rpx;
	line-height: 20rpx;
	font-family: Source Han Sans CN;
	font-weight: 400;
	color: #C1C0C0;
	margin-top: 20rpx;
	text-align: center;
}
.module_n.data-v-4920fa35{
	color: #C7AF8C;
}



