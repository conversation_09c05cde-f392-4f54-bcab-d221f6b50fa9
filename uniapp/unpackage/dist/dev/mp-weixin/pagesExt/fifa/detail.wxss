
page {
	background: linear-gradient(90deg, #405ADD 0%, #695DDE 100%);
}


.banner.data-v-73515bfe {
	position: absolute;
	width: 100%;
	display: block;
}
.page.data-v-73515bfe {
	position: relative;
	padding: 35rpx 30rpx 30rpx 30rpx;
}
.title.data-v-73515bfe {
	position: relative;
	font-size: 36rpx;
	font-family: Source Han Sans CN;
	font-weight: bold;
	color: #FFFFFF;
	text-align: center;
}
.title image.data-v-73515bfe {
	position: absolute;
	height: 45rpx;
	width: 45rpx;
	left: 30rpx;
	top: 0;
	bottom: 0;
	margin: auto 0;
}
.module.data-v-73515bfe {
	position: relative;
	padding: 70rpx 30rpx 45rpx 30rpx;
	background: #f5f5ff;
	border-radius: 20rpx;
	margin-top: 65rpx;
}
.module_data.data-v-73515bfe {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	width: 240rpx;
	height: 95rpx;
	color: #415CC0;
	margin: 0 auto;
}
.module_tag.data-v-73515bfe {
	width: 240rpx;
	height: 95rpx;
}
.module_title.data-v-73515bfe {
	position: absolute;
	width: 100%;
	text-align: center;
	top: 20rpx;
	font-size: 20rpx;
	line-height: 20rpx;
	font-family: Source Han Sans CN;
	font-weight: 400;
}
.module_time.data-v-73515bfe {
	position: absolute;
	width: 100%;
	text-align: center;
	top: 55rpx;
	font-size: 28rpx;
	line-height: 28rpx;
	font-family: DIN Pro;
	font-weight: bold;
}
.module_content.data-v-73515bfe {
	display: flex;
	align-items: flex-end;
}
.module_team.data-v-73515bfe {
	flex: 1;
}
.module_head.data-v-73515bfe{
	width: 80rpx;
	height:53rpx;
	display: block;
	margin: 0 auto;
}
.module_text.data-v-73515bfe {
	font-size: 24rpx;
	font-family: Source Han Sans CN;
	font-weight: 400;
	color: #121212;
	margin-top: 15rpx;
	text-align: center;
}
.module_grade.data-v-73515bfe {
	width: 60rpx;
	height: 72rpx;
	background: #3D5C9B;
	border-radius: 8rpx;
	font-size: 36rpx;
	font-weight: bold;
	color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
}
.module_r.data-v-73515bfe {
	background: #AB3F3F;
}
.module_state.data-v-73515bfe {
	padding: 0 35rpx;
}
.module_vs.data-v-73515bfe {
	font-size: 24rpx;
	line-height: 24rpx;
	font-family: DIN Pro;
	font-weight: bold;
	color: #415CC0;
	text-align: center;
}
.module_status.data-v-73515bfe {
	font-size: 20rpx;
	line-height: 20rpx;
	font-family: Source Han Sans CN;
	font-weight: 400;
	color: #C7AF8C;
	margin-top: 20rpx;
	text-align: center;
}
.table.data-v-73515bfe {
	display: flex;
}
.table_item.data-v-73515bfe {
	position: relative;
	padding: 0 0 35rpx 0;
	font-size: 28rpx;
	font-family: Source Han Sans CN;
	font-weight: bold;
	text-align: center;
	flex: 1;
	color: #C1C0C0;
}
.table_active.data-v-73515bfe {
	color: #121212;
}
.table_tag.data-v-73515bfe {
	position: absolute;
	left: 0;
	right: 0;
	bottom: 20rpx;
	margin: 0 auto;
	width: 40rpx;
	height: 6rpx;
	background: #3C62ED;
}
.guess.data-v-73515bfe {
	position: relative;
	padding: 40rpx 40rpx 110rpx 40rpx;
	margin-top: 20rpx;
	background: #FFFFFF;
	border-radius: 20rpx;
}
.state.data-v-73515bfe {
	padding: 30rpx 0 0 0;
}
.state_item.data-v-73515bfe {
	position: relative;
	height: 100rpx;
	background: #FFFFFF;
	border: 1rpx solid #F0F0F1;
	border-radius: 20rpx;
	padding: 0 50rpx;
	font-size: 28rpx;
	font-family: Source Han Sans CN;
	font-weight: 500;
	display: flex;
	align-items: center;
	color: #121212;
	margin-top: 20rpx;
}
.state_active.data-v-73515bfe {
	border: 1rpx solid #2D6FE8;
	color: #3C62ED;
	background: rgba(45, 111, 232, 0.2);
}
.state_icon.data-v-73515bfe {
	height: 36rpx;
	width: 36rpx;
	position: absolute;
	top: 0;
	bottom: 0;
	right: 40rpx;
	margin: auto 0;
}
.grade.data-v-73515bfe {
	position: relative;
}
.grade_title.data-v-73515bfe {
	font-size: 28rpx;
	text-align: center;
	font-family: Source Han Sans CN;
	font-weight: 500;
	color: #121212;
	margin-top: 50rpx;
}
.grade_title text.data-v-73515bfe:first-child {
	color: #3C62ED;
	margin: 0 10rpx;
}
.grade_title text.data-v-73515bfe:last-child {
	color: #ED3C83;
	margin: 0 10rpx;
}
.grade_module.data-v-73515bfe {
	display: grid;
	grid-template-columns: repeat(5, 1fr);
	grid-column-gap: 20rpx;
	grid-row-gap: 20rpx;
	margin-top: 30rpx;
}
.grade_item.data-v-73515bfe {
	height: 56rpx;
	background: #FFFFFF;
	border: 1rpx solid #F0F0F1;
	border-radius: 12rpx;
	font-size: 26rpx;
	text-align: center;
	font-family: Source Han Sans CN;
	font-weight: 500;
	color: #121212;
	display: flex;
	align-items: center;
	justify-content: center;
}
.grade_active.data-v-73515bfe {
	background: #3C62ED;
	color: #fff;
}
.btn.data-v-73515bfe {
	width: 650rpx;
	height: 108rpx;
	background: #FFCA4C;
	border-radius: 54rpx;
	font-size: 32rpx;
	font-family: Source Han Sans CN;
	font-weight: 500;
	color: #533F10;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 45rpx auto 0 auto;
}
.qd_guize.data-v-73515bfe{
	padding: 20rpx 40rpx 110rpx 40rpx;
	margin-top: 40rpx;
	background: #FFFFFF;
	border-radius: 20rpx;}
.qd_guize .gztitle.data-v-73515bfe{width:100%;text-align:center;font-size:32rpx;color:#656565;font-weight:bold;height:100rpx;line-height:100rpx}
.guize_txt.data-v-73515bfe{box-sizing: border-box;line-height:42rpx;}

