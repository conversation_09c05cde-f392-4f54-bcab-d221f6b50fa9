<block wx:if="{{isload}}"><view class="container"><view class="main flex-y-center"><block wx:if="{{showtype=='png'}}"><image class="img" style="width:100%;" src="{{fujianpic}}" role="img" mode="widthFix" data-url="{{fujianpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></block><block wx:else><block wx:if="{{showtype=='mp3'}}"><view class="play video"><view class="play-left"><image hidden="{{!(playshow)}}" src="{{pre_url+'/static/img/video_icon.png'}}" data-url="{{fujianpic}}" data-event-opts="{{[['tap',[['play',['$event']]]]]}}" bindtap="__e"></image><image hidden="{{!(!playshow)}}" src="{{pre_url+'/static/img/play.png'}}" data-event-opts="{{[['tap',[['pauseaudio',['$event']]]]]}}" bindtap="__e"></image><text>{{nowtime}}</text></view><view class="play-right"><slider class="slider" style="margin-top:30rpx;" block-size="16" min="{{0}}" max="{{time}}" value="{{currentTime}}" activeColor="#595959" data-event-opts="{{[['change',[['sliderChange',['$event']]]],['changing',[['sliderChanging',['$event']]]]]}}" bindchange="__e" bindchanging="__e"></slider></view><view class="play-end"><text>{{duration}}</text></view></view></block><block wx:else><video class="video" style="z-index:100;" id="video" autoplay="{{true}}" src="{{fujianpic}}"></video></block></block><block wx:if="{{down_auth=='1'}}"><view class="save"><button data-event-opts="{{[['tap',[['savevideo',['$event']]]]]}}" class="btn_save" bindtap="__e"><block wx:if="{{showtype=='mp3'}}"><text>保存到手机</text></block><block wx:else><text>保存至手机相册</text></block></button></view></block></view><wxxieyi vue-id="2cc4a15c-1" bind:__l="__l"></wxxieyi></view></block>