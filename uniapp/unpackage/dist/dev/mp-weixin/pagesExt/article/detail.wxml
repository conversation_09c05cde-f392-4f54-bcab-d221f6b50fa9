<view><block wx:if="{{!is_gather}}"><block><block wx:if="{{isload}}"><block><view class="container"><view class="header"><block wx:if="{{detail.showname==1}}"><text class="title">{{detail.name}}</text></block><block wx:if="{{detail.showsendtime==1||detail.showauthor==1||detail.showreadcount==1}}"><view class="artinfo"><block wx:if="{{detail.showsendtime==1}}"><text class="t1">{{detail.createtime}}</text></block><block wx:if="{{detail.showauthor==1}}"><text class="t2">{{detail.author}}</text></block><block wx:if="{{detail.showreadcount==1}}"><text class="t3">{{"阅读："+detail.readcount}}</text></block></view></block><view style="padding:8rpx 0;"><dp vue-id="ebd0dc70-1" pagecontent="{{pagecontent}}" richtype="{{richtype}}" richurl="{{richurl}}" bind:__l="__l"></dp></view><block wx:if="{{reward}}"><block><view data-event-opts="{{[['tap',[['changeReward',['$event']]]]]}}" style="width:360rpx;background-color:#FA5151;border-radius:12rpx;color:#fff;margin:40rpx auto;line-height:80rpx;" bindtap="__e"><view style="display:flex;width:160rpx;margin:0 auto;"><image style="width:32rpx;height:32rpx;margin-top:20rpx;margin-right:10rpx;" src="{{pre_url+'/static/img/dianzan.png'}}"></image>打赏作者</view></view><view style="width:360rpx;margin:0 auto;overflow:hidden;text-align:center;line-height:80rpx;"><view style="width:80rpx;float:left;border-top:2rpx solid #E5E5E5;margin-top:40rpx;"></view><view style="width:200rpx;font-size:24rpx;float:left;color:#B2B2B2;"><block wx:if="{{detail.reward_num>1000}}"><text style="color:#536084;">1000+</text></block><block wx:else><text style="color:#536084;">{{detail.reward_num}}</text></block><text>人打赏</text></view><view style="width:80rpx;float:right;border-top:2rpx solid #E5E5E5;margin-top:40rpx;"></view></view><block wx:if="{{detail.reward_log}}"><view style="width:640rpx;margin:0rpx auto;overflow:hidden;margin-bottom:40rpx;"><block wx:for="{{detail.reward_log}}" wx:for-item="item" wx:for-index="index"><block><image style="width:70rpx;height:70rpx;margin-top:20rpx;margin-right:10rpx;" src="{{item}}"></image></block></block></view></block></block></block></view><block wx:if="{{$root.g0}}"><view class="zybox"><view class="zy_title">资源明细<text class="zy_tip">获取后可预览</text></view><block wx:for="{{detail.fujian_list}}" wx:for-item="item" wx:for-index="index"><view class="zy_list" data-type="{{item.type}}" data-url="{{item.url}}" data-event-opts="{{[['tap',[['openFile',['$event']]]]]}}" bindtap="__e"><view class="zy_left flex-y-center"><block wx:if="{{item.type=='pdf'}}"><image class="image zy_image" src="{{pre_url+'/static/img/article/pdf.png'}}"></image></block><block wx:else><block wx:if="{{item.type=='xlsx'||item.type=='xls'}}"><image class="image zy_image" src="{{pre_url+'/static/img/article/excel.png'}}"></image></block><block wx:else><block wx:if="{{item.type=='doc'||item.type=='docx'}}"><image class="image zy_image" src="{{pre_url+'/static/img/article/word.png'}}"></image></block><block wx:else><block wx:if="{{item.type=='ppt'||item.type=='pptx'}}"><image class="image zy_image" src="{{pre_url+'/static/img/article/ppt.png'}}"></image></block><block wx:else><block wx:if="{{item.type=='mp4'}}"><image class="image zy_image" src="{{pre_url+'/static/img/article/mp4.png'}}"></image></block><block wx:else><block wx:if="{{item.type=='mp3'}}"><image class="image zy_image" src="{{pre_url+'/static/img/article/mp3.png'}}"></image></block><block wx:else><block wx:if="{{item.type=='zip'||item.type=='rar'||item.type=='7z'}}"><image class="image zy_image" src="{{pre_url+'/static/img/article/zip.png'}}"></image></block><block wx:else><image class="image zy_image" src="{{pre_url+'/static/img/article/png.png'}}"></image></block></block></block></block></block></block></block></view><view class="zy_content flex-y-center">{{''+item.name+''}}</view><view class="flex-y-center f1" style="justify-content:center;padding:0 10rpx;"><block wx:if="{{detail.is_look_resource=='0'||detail.is_have=='0'}}"><image class="image suo_image" src="{{pre_url+'/static/img/article/suo.png'}}"></image></block><block wx:else><button class="btn_yl"><block wx:if="{{item.type=='zip'||item.type=='rar'||item.type=='7z'}}"><text>下载</text></block><block wx:else><text>预览</text></block></button></block></view></view></block></view></block><block wx:if="{{detail.canpl==1}}"><block><view class="plbox"><view class="plbox_title"><text class="t1">评论</text><text>{{"("+plcount+")"}}</text></view><view class="plbox_content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="idx" wx:key="id"><block><view class="item1 flex"><view class="f1 flex0"><image src="{{item.$orig.headimg}}"></image></view><view class="f2 flex-col"><text class="t1">{{item.$orig.nickname}}</text><view class="t2 plcontent"><parse vue-id="{{'ebd0dc70-2-'+idx}}" content="{{item.$orig.content}}" bind:__l="__l"></parse></view><block wx:if="{{item.g1>0}}"><block><view class="relist"><block wx:for="{{item.$orig.replylist}}" wx:for-item="hfitem" wx:for-index="index" wx:key="index"><block><view class="item2"><view>{{hfitem.nickname+"："}}</view><view class="f2 plcontent"><parse vue-id="{{'ebd0dc70-3-'+idx+'-'+index}}" content="{{hfitem.content}}" bind:__l="__l"></parse></view></view></block></block></view></block></block><view class="t3 flex"><text>{{item.$orig.createtime}}</text><view class="flex1"><block wx:if="{{detail.canplrp==1}}"><text class="phuifu" style="cursor:pointer;" data-url="{{'pinglun?type=1&id='+detail.id+'&hfid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">回复</text></block></view><view class="flex-y-center pzan" data-id="{{item.$orig.id}}" data-index="{{idx}}" data-event-opts="{{[['tap',[['pzan',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/zan-'+(item.$orig.iszan==1?'2':'1')+'.png'}}"></image>{{item.$orig.zan}}</view></view></view></view></block></block></view><block wx:if="{{loading}}"><loading vue-id="ebd0dc70-4" bind:__l="__l"></loading></block></view><view style="height:160rpx;"></view><view class="{{['pinglun',menuindex>-1?'tabbarbot-scoped':'notabbarbot']}}"><view class="pinput" data-url="{{'pinglun?type=0&id='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">发表评论</view><view class="zan flex-y-center" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['zan',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/zan-'+(iszan?'2':'1')+'.png'}}"></image><text style="padding-left:2px;">{{detail.zan}}</text></view><block wx:if="{{detail.btntxt&&detail.btnurl}}"><block><view class="buybtn" style="cursor:pointer;" onclick="{{'location.href='+detail.btnurl}}">{{detail.btntxt}}</view></block></block><block wx:if="{{$root.g2}}"><view><block wx:if="{{detail.is_have=='0'}}"><view data-event-opts="{{[['tap',[['getResource',['$event']]]]]}}" class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m0)+';'}}" bindtap="__e">获取资源</view></block><block wx:else><view data-event-opts="{{[['tap',[['toRecord',['$event']]]]]}}" class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m1)+';'}}" bindtap="__e">已获取·去下载</view></block></view></block></view></block></block></view></block></block><block wx:if="{{reward&&openreward}}"><block><view data-event-opts="{{[['tap',[['changeReward',['$event']]]]]}}" style="background-color:#000;opacity:0.4;width:100%;height:100%;position:fixed;top:0;z-index:996;" bindtap="__e"></view><view style="position:fixed;width:690rpx;left:30rpx;top:30%;background-color:#fff;z-index:997;border-radius:8rpx;"><view style="overflow:hidden;padding:30rpx 30rpx 0;"><text>打赏作者</text><image style="float:right;width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['changeReward',['$event']]]]]}}" bindtap="__e"></image></view><block wx:if="{{reward_num_type==1}}"><view style="padding:0 30rpx 30rpx;"><view style="width:300rpx;text-align:center;margin:0 auto;overflow:hidden;line-height:70rpx;margin-top:20rpx;"><view class="reward_typel" style="{{(reward_type==1?'background-color: #FA5151;color: #fff;':'background-color: #f3f3f3;color: #000;')}}" data-type="1" data-event-opts="{{[['tap',[['changeRewardtype',['$event']]]]]}}" bindtap="__e">金额</view><view class="reward_typer" style="{{(reward_type==2?'background-color: #FA5151;color: #fff;':'background-color: #f3f3f3;color: #000;')}}" data-type="2" data-event-opts="{{[['tap',[['changeRewardtype',['$event']]]]]}}" bindtap="__e">{{''+$root.m2+''}}</view></view><block wx:if="{{reward_data}}"><view style="width:580rpx;overflow:hidden;text-align:center;margin:0 auto;line-height:80rpx;color:#FA5151;font-size:30rpx;"><block wx:if="{{reward_type==1&&reward_data.money_data}}"><block><block wx:for="{{reward_data.money_data}}" wx:for-item="item" wx:for-index="index"><view class="{{[reward_num==item?'reward_content reward_num':'reward_content']}}" style="{{((index+1)%3==0?'':'margin-right: 20rpx;')}}"><view data-num="{{item}}" data-event-opts="{{[['tap',[['selRewardnum',['$event']]]]]}}" bindtap="__e">{{'￥'+item+''}}</view></view></block></block></block><block wx:if="{{reward_type==2&&reward_data.score_data}}"><block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index"><view class="{{[reward_num==item.$orig?'reward_content reward_num':'reward_content']}}" style="{{((index+1)%3==0?'':'margin-right: 20rpx;')}}"><view data-num="{{item.$orig}}" data-event-opts="{{[['tap',[['selRewardnum',['$event']]]]]}}" bindtap="__e">{{''+item.$orig+item.m3+''}}</view></view></block></block></block></view></block><block wx:if="{{reward_type==1}}"><view class="reward_num_type" data-type="1" data-event-opts="{{[['tap',[['changeRewardNumType',['$event']]]]]}}" bindtap="__e">其他金额</view></block><block wx:if="{{reward_type==2}}"><view class="reward_num_type" data-type="1" data-event-opts="{{[['tap',[['changeRewardNumType',['$event']]]]]}}" bindtap="__e">{{"其他"+$root.m4}}</view></block></view></block><block wx:if="{{reward_num_type==2}}"><view><view><view style="width:250rpx;margin:40rpx auto;font-size:40rpx;font-weight:bold;line-height:80rpx;display:flex;text-align:center;"><block wx:if="{{reward_type==1}}"><text>￥</text></block><input style="height:80rpx;line-height:80rpx;display:inline-block;width:170rpx;border-bottom:2rpx solid #eee;" placeholder="0(整数)" placeholder-style="line-height: 80rpx;" data-event-opts="{{[['input',[['__set_model',['','reward_num','$event',[]]],['inputRewardnum',['$event']]]]]}}" value="{{reward_num}}" bindinput="__e"/><block wx:if="{{reward_type==2}}"><text style="font-size:36rpx;">{{$root.m5}}</text></block></view><block wx:if="{{reward_type==1}}"><view class="reward_num_type" data-type="2" data-event-opts="{{[['tap',[['changeRewardNumType',['$event']]]]]}}" bindtap="__e">固定金额</view></block><block wx:if="{{reward_type==2}}"><view class="reward_num_type" data-type="2" data-event-opts="{{[['tap',[['changeRewardNumType',['$event']]]]]}}" bindtap="__e">{{"固定"+$root.m6}}</view></block></view><view data-event-opts="{{[['tap',[['postReward',['$event']]]]]}}" style="width:100%;line-height:80rpx;text-align:center;border-top:2rpx solid #eee;font-size:30rpx;color:#FA5151;" bindtap="__e">确定</view></view></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="ebd0dc70-5" bind:__l="__l"></loading></block></block></block><block wx:else><block><web-view src="{{pre_url+'/h5/'+aid+'.html#/pagesExt/article/detail?id='+opt.id+'&pid='+mid}}"></web-view></block></block><block wx:if="{{countdownShow&&countdownTime}}"><view class="countdown-content"><view class="count-main"><view class="bg-view"></view><progress active="{{true}}" duration="{{countdownTime}}" percent="100" stroke-width="6" activeColor="#feea17" backgroundColor="#792908" data-event-opts="{{[['activeend',[['endCountdown',['$event']]]]]}}" bindactiveend="__e"></progress><view class="text-view"><text>{{"浏览"+readTime+"秒"}}</text><text>获得奖励</text></view></view></view></block><dp-tabbar vue-id="ebd0dc70-6" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="ebd0dc70-7" data-ref="popmsg" bind:__l="__l"></popmsg></view>