<view class="container"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="product-item2" data-url="{{'index?id='+item.$orig.mpid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="data" style="{{'background:'+(item.$orig.info.bgpic?'url('+item.$orig.info.bgpic+')':'#fff')+';'+('background-size:'+('100%')+';')}}"><view class="data_info" style="{{(field_list2?'':'border:0;margin:0')}}"><image class="data_head _img" src="{{item.$orig.info.headimg}}" alt></image><view><view class="data_name">{{item.$orig.info.realname}}</view><block wx:if="{{item.$orig.info.touxian1}}"><view class="data_text">{{item.$orig.info.touxian1}}</view></block><block wx:if="{{item.$orig.info.touxian2}}"><view class="data_text">{{item.$orig.info.touxian2}}</view></block><block wx:if="{{item.$orig.info.touxian3}}"><view class="data_text">{{item.$orig.info.touxian3}}</view></block></view></view><block wx:for="{{item.l0}}" wx:for-item="item2" wx:for-index="index2"><view class="data_list"><block wx:if="{{index2=='tel'}}"><image src="{{item2.m0}}" alt class="_img"></image></block><block wx:else><block wx:if="{{index2=='weixin'}}"><image src="{{pre_url+'/static/img/weixin.png'}}" alt class="_img"></image></block><block wx:else><block wx:if="{{index2=='address'}}"><image src="{{item2.m1}}" alt class="_img"></image></block><block wx:else><image src="{{item2.$orig.icon}}" alt class="_img"></image></block></block></block>{{''+item.$orig.info[index2]+''}}</view></block></view></view><view class="foot"><text class="flex1">{{"收藏时间："+item.m2}}</text><text class="btn" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['favoritedel',['$event']]]]]}}" bindtap="__e">取消收藏</text></view></view></block><block wx:if="{{nomore}}"><nomore vue-id="daedb84c-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="daedb84c-2" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="daedb84c-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="daedb84c-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="daedb84c-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>