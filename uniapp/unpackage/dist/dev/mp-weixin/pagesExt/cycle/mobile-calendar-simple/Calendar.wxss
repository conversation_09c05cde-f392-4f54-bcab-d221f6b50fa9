.calendar-tz.data-v-83fa5b2c {
  width: 100%;
  height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
}
.calendar-tz.fixed.data-v-83fa5b2c {
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 900;
}
.calendar-tz .week-number.data-v-83fa5b2c {
  background: #fff;
  padding: 0 1%;
  box-shadow: 0 2px 15px rgba(100, 100, 100, 0.1);
}
.calendar-tz .week-number ._span.data-v-83fa5b2c {
  display: inline-block;
  text-align: center;
  padding: 12px 0;
  font-size: 14px;
  width: 14.28%;
}
.calendar-tz .week-number ._span.data-v-83fa5b2c:first-child,
.calendar-tz .week-number ._span.data-v-83fa5b2c:last-child {
  color: #1C75FF;
}
.calendar-tz .tips.data-v-83fa5b2c {
  padding: 6px 10px;
  background: #fff7dc;
  font-size: 12px;
  color: #9e8052;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.calendar-tz .content.data-v-83fa5b2c {
  flex: 1;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  padding-bottom: 100rpx;
}
.calendar-tz .content .con.data-v-83fa5b2c {
  color: #333;
  padding-top: 10px;
  position: relative;
}
.calendar-tz .content .con ._h3.data-v-83fa5b2c {
  width: 100%;
  font-weight: normal;
  text-align: center;
  font-size: 16px;
  padding: 10px 0;
}
.calendar-tz .content .con .month-bg.data-v-83fa5b2c {
  position: absolute;
  text-align: center;
  opacity: 0.4;
  left: 0;
  right: 0;
  bottom: 0;
  top: 20%;
  font-size: 220px;
  font-weight: bold;
  color: #f8f8f8;
}
.calendar-tz .content .con .each-month.data-v-83fa5b2c {
  display: block;
  width: 98%;
  font-size: 0;
  margin: 0 auto;
  padding-left: 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}
.calendar-tz .content .con .each-month .each-day.data-v-83fa5b2c {
  position: relative;
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  width: 14.28%;
  font-size: 16px;
  height: 50px;
  margin: 2px auto;
}
.calendar-tz .content .con .each-month .each-day ._div.data-v-83fa5b2c {
  display: inline-block;
  font-size: 14px;
  width: 98%;
  height: 100%;
  justify-content: space-around;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
}
.calendar-tz .content .con .each-month .each-day.between.data-v-83fa5b2c {
  background: rgba(75, 217, 173, 0.1);
}
.calendar-tz .content .con .each-month .each-day .day.data-v-83fa5b2c {
  font-size: 16px;
}
.calendar-tz .content .con .each-month .each-day .day-tip.data-v-83fa5b2c,
.calendar-tz .content .con .each-month .each-day .recent.data-v-83fa5b2c {
  font-size: 10px;
  height: 14px;
}
.calendar-tz .content .con .each-month .each-day .day-tip ._i.data-v-83fa5b2c,
.calendar-tz .content .con .each-month .each-day .recent ._i.data-v-83fa5b2c {
  font-size: 10px;
}
.calendar-tz .content .con .each-month .each-day .recent.data-v-83fa5b2c {
  color: #ccc;
}
.calendar-tz .content .con .each-month .each-day .disabled.data-v-83fa5b2c {
  color: #ccc !important;
}
.calendar-tz .content .con .each-month .each-day .disabled .day-tip.data-v-83fa5b2c {
  color: #ccc !important;
}
.calendar-tz .content .con .each-month .each-day .today.data-v-83fa5b2c {
  background: rgba(100, 100, 100, 0.1);
}
.calendar-tz .content .con .each-month .each-day .trip-time.data-v-83fa5b2c {
  background: #1C75FF;
  color: #fff !important;
}
.calendar-tz .content .con .each-month .each-day .trip-time .recent.data-v-83fa5b2c,
.calendar-tz .content .con .each-month .each-day .trip-time .day-tip.data-v-83fa5b2c {
  color: #fff !important;
}
.calendar-tz .content .con .each-month .each-day .trip-time-order.data-v-83fa5b2c {
  background: #f44336;
  color: #fff !important;
}
.calendar-tz .content .con .each-month .each-day .trip-time-order .recent.data-v-83fa5b2c,
.calendar-tz .content .con .each-month .each-day .trip-time-order .day-tip.data-v-83fa5b2c {
  color: #fff !important;
}
.calendar-tz .content .con .each-month .each-day .weekend.data-v-83fa5b2c {
  color: #1C75FF;
}
/***右侧进入动画***/
.slide-enter-active.data-v-83fa5b2c,
.slide-leave-active.data-v-83fa5b2c {
  transition: all 0.2s ease;
}
.slide-enter.data-v-83fa5b2c,
.slide-leave-to.data-v-83fa5b2c {
  -webkit-transform: translateX(100%);
  transform: translateX(100%);
}

