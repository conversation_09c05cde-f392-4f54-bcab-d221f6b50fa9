
page {
	background: #F6F6F6;
}


.page.data-v-2cabe197 {
	padding: 30rpx;
}
.module.data-v-2cabe197 {
	background: #FFFFFF;
	padding: 0 30rpx 50rpx 30rpx;
	border-radius: 10rpx;
	margin: 0 auto;
	width: 690rpx;
	box-sizing: border-box;
}
.module_title.data-v-2cabe197 {
	padding: 30rpx;
	border-bottom: 1px solid #f6f6f6;
	font-size: 30rpx;
	color: #333;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.module_num.data-v-2cabe197 {
	font-size: 28rpx;
	margin-left: 30rpx;
}
.module_state.data-v-2cabe197 {
	font-size: 24rpx;
	font-family: PingFang SC;
	font-weight: bold;
	color: #6FD16B;
}
.module_list.data-v-2cabe197 {
	margin-top: 30rpx;
	font-size: 26rpx;
	display: flex;
	align-items: center;
}
.module_lable.data-v-2cabe197 {
	width: 120rpx;
	text-align: right;
	color: #999;
}
.module_text.data-v-2cabe197 {
	margin-left: 30rpx;
	color: #333;
	flex: 1;
}
.module_update.data-v-2cabe197{
	color: #FC4343;
}
.module_update ._img.data-v-2cabe197{
	height: 24rpx;
	width: 24rpx;
	margin-right: 10rpx;
}
.opt.data-v-2cabe197 {
	padding: 20rpx 30rpx;
}
.wl_btn.data-v-2cabe197 {
	width: 240rpx;
	height: 70rpx;
	background: #FD4A46;
	border-radius: 10rpx;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-family: PingFang SC;
	font-weight: bold;
	color: #FFFFFF;
	box-sizing: border-box;
}
.opt_btn.data-v-2cabe197 {
	height: 88rpx;
	background: #FD4A46;
	border-radius: 10rpx;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-family: PingFang SC;
	font-weight: bold;
	color: #FFFFFF;
	box-sizing: border-box;
	margin-top: 30rpx;
}
.alert.data-v-2cabe197{
	position: fixed;
	height: 100%;
	width: 100%;
	top: 0;
	left: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.6);
}
.alert_none.data-v-2cabe197{
	position: absolute;
	height: 100%;
	width: 100%;
}
.alert_module.data-v-2cabe197{
	position: relative;
	width: 560rpx;
	padding: 30rpx;
	box-sizing: border-box;
	background: #FFFFFF;
	border-radius: 24rpx;
}
.alert_title.data-v-2cabe197{
	font-size: 32rpx;
	font-family: PingFang SC;
	font-weight: bold;
	color: #323232;
	text-align: center;
	padding: 0 0 20rpx 0;
}
.alert_close.data-v-2cabe197{
	position: absolute;
	right: 35rpx;
	top: 35rpx;
	height: 28rpx;
	width: 28rpx;
}
.alert_item.data-v-2cabe197{
	height: 120rpx;
	background: #F9F6F6;
	border-radius: 8rpx;
	margin-top: 20rpx;
	padding: 0 30rpx;
	display: flex;
	align-items: center;
}
.alert_tag.data-v-2cabe197{
	width: 8rpx;
	height: 8rpx;
	background: #323232;
	border-radius: 50%;
}
.alert_data.data-v-2cabe197{
	flex: 1;
	margin-left: 25rpx;
}
.alert_name.data-v-2cabe197{
	font-size: 26rpx;
	font-family: PingFang SC;
	font-weight: bold;
	color: #323232;
}
.alert_text.data-v-2cabe197{
	font-size: 22rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: #999999;
	margin-top: 15rpx;
}
.alert_icon.data-v-2cabe197{
	height: 36rpx;
	width: 36rpx;
}
.alert_active.data-v-2cabe197{
	background: #ffe8e8;
}
.alert_active .alert_tag.data-v-2cabe197{
	background: #FC4343;
}
.alert_active .alert_name.data-v-2cabe197{
	color: #FC4343;
}
.alert_active .alert_text.data-v-2cabe197{
	color: #FC4343;
	opacity: 0.7;
}
.alert_opt.data-v-2cabe197{
	margin-top: 60rpx;
	display: flex;
}
.alert_btn.data-v-2cabe197{
	flex: 1;
	height: 88rpx;
	background: #ffe8e8;
	font-weight: bold;
	color: #FC4343;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 26rpx;
	border-radius: 8rpx;
}
.alert_btn.data-v-2cabe197:last-child{
	flex: 2;
	margin-left: 20rpx;
	height: 88rpx;
	background: #FC4343;
	font-weight: bold;
	color: #fff;
	font-size: 26rpx;
	border-radius: 8rpx;
}
.btn2.data-v-2cabe197{ border-radius: 10rpx;margin-left:20rpx;margin-top: 10rpx;width:160rpx;height:60rpx;line-height:60rpx;color:#fff;background:#FC4343;border-radius:3px;text-align:center}
.hxqrbox.data-v-2cabe197{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}
.hxqrbox .img.data-v-2cabe197{width:400rpx;height:400rpx}
.hxqrbox .txt.data-v-2cabe197{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}
.hxqrbox .close.data-v-2cabe197{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx}


