<view class="data-v-6f5c83ee"><block wx:if="{{isload}}"><view class="page data-v-6f5c83ee"><view class="head data-v-6f5c83ee"><image class="head_img _img data-v-6f5c83ee" src="{{nowguige.pic||product.pic}}" data-url="{{nowguige.pic||product.pic}}" alt data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="data-v-6f5c83ee"><view class="head_title data-v-6f5c83ee">{{product.name}}</view><view class="head_text data-v-6f5c83ee">{{nowguige.name+" | "+product.ps_cycle_title}}</view><view class="head_price data-v-6f5c83ee"><text class="head_icon data-v-6f5c83ee">￥</text><block wx:if="{{nowguige.sell_price>0}}"><text class="data-v-6f5c83ee">{{nowguige.sell_price}}</text></block><block wx:else><text class="data-v-6f5c83ee">{{product.sell_price}}</text></block></view></view></view><view class="body data-v-6f5c83ee"><block wx:for="{{guigedata}}" wx:for-item="item" wx:for-index="index"><view class="body_item data-v-6f5c83ee"><view class="body_title flex flex-bt data-v-6f5c83ee">{{item.title}}<text class="body_text data-v-6f5c83ee">请选择周期购计划</text></view><view class="body_content data-v-6f5c83ee"><block wx:for="{{item.items}}" wx:for-item="item2" wx:for-index="index2" wx:key="*this"><view class="{{['body_tag','data-v-6f5c83ee',ggselected[item.k]==item2.k?'body_active':'']}}" data-itemk="{{item.k}}" data-idx="{{item2.k}}" data-event-opts="{{[['tap',[['ggchange',['$event']]]]]}}" bindtap="__e">{{item2.title}}</view></block></view></view></block><view class="body_item data-v-6f5c83ee"><view class="body_title flex flex-bt data-v-6f5c83ee">配送时间<text class="body_text data-v-6f5c83ee">{{product.ps_cycle_title}}</text></view><block wx:if="{{product.ps_cycle==1}}"><view class="body_content data-v-6f5c83ee"><block wx:for="{{rateList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['rateClick',[index]]]]]}}" class="{{['body_tag','data-v-6f5c83ee',rateIndex===index?'body_active':'']}}" bindtap="__e">{{item.label}}</view></block></view></block></view><view class="body_item data-v-6f5c83ee"><view class="body_title flex flex-bt data-v-6f5c83ee"><label class="_span data-v-6f5c83ee">开始时间</label><view data-event-opts="{{[['tap',[['toCheckDate',['$event']]]]]}}" class="body_data data-v-6f5c83ee" bindtap="__e">{{week+" "+(startDate?startDate:'请选择开始时间')+''}}<image class="body_detail _img data-v-6f5c83ee" src="{{pre_url+'/static/img/week/week_detail.png'}}"></image></view></view></view><view class="body_item data-v-6f5c83ee"><view class="body_title flex flex-bt data-v-6f5c83ee"><text class="data-v-6f5c83ee">配送期数</text><view class="flex-y-center data-v-6f5c83ee"><block wx:if="{{min_qsnum!=1}}"><text class="body_notice data-v-6f5c83ee">{{min_qsnum+"期起订"}}</text></block><view class="body_data data-v-6f5c83ee"><block wx:if="{{qsnumState}}"><image class="{{['body_opt','_img','data-v-6f5c83ee',qsnum<=min_qsnum?'body_disabled':'']}}" src="{{pre_url+'/static/img/week/week_cut.png'}}" data-event-opts="{{[['tap',[['qsminus',['$event']]]]]}}" bindtap="__e"></image></block><block wx:if="{{!qsnumState}}"><image class="body_opt body_disabled _img data-v-6f5c83ee" src="{{pre_url+'/static/img/week/week_cut.png'}}"></image></block><input class="body_num data-v-6f5c83ee" type="number" data-event-opts="{{[['blur',[['getQsTotal',['$event']]]]]}}" value="{{qsnum}}" bindblur="__e"/><image class="body_opt _img data-v-6f5c83ee" src="{{pre_url+'/static/img/week/week_add.png'}}" data-event-opts="{{[['tap',[['qsplus',['$event']]]]]}}" bindtap="__e"></image></view></view></view></view><view class="body_item data-v-6f5c83ee"><view class="body_title flex flex-bt data-v-6f5c83ee"><text class="data-v-6f5c83ee">每期数量</text><view class="flex-y-center data-v-6f5c83ee"><block wx:if="{{min_num!=1}}"><text class="body_notice data-v-6f5c83ee">{{min_num+"件起订"}}</text></block><view class="body_data data-v-6f5c83ee"><block wx:if="{{numState}}"><image class="{{['body_opt','_img','data-v-6f5c83ee',num<=min_num?'body_disabled':'']}}" src="{{pre_url+'/static/img/week/week_cut.png'}}" data-event-opts="{{[['tap',[['gwcminus',['$event']]]]]}}" bindtap="__e"></image></block><block wx:if="{{!numState}}"><image class="body_opt body_disabled _img data-v-6f5c83ee" src="{{pre_url+'/static/img/week/week_cut.png'}}"></image></block><input class="body_num data-v-6f5c83ee" type="number" data-event-opts="{{[['blur',[['getTotal',['$event']]]]]}}" value="{{num}}" bindblur="__e"/><image class="body_opt _img data-v-6f5c83ee" src="{{pre_url+'/static/img/week/week_add.png'}}" data-event-opts="{{[['tap',[['gwcplus',['$event']]]]]}}" bindtap="__e"></image></view></view></view></view></view></view></block><block wx:if="{{product.status==1}}"><view class="{{['bottombar','flex-row','flex-xy-center','data-v-6f5c83ee',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view class="operate_data data-v-6f5c83ee"><view class="operate_text data-v-6f5c83ee">配送<text class="operate_color data-v-6f5c83ee">{{!qsnum?'':qsnum}}</text>期，共<text class="operate_color data-v-6f5c83ee">{{!num||!qsnum?'':num*qsnum}}</text>件商品</view><view class="operate_price data-v-6f5c83ee"><text class="operate_lable data-v-6f5c83ee">总价：</text><text class="operate_tag data-v-6f5c83ee">￥</text><text class="operate_num data-v-6f5c83ee">{{!num||!qsnum?'':totalprice}}</text></view></view><view data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" class="tobuy flex1 data-v-6f5c83ee" style="{{'background:'+($root.m0)+';'}}" bindtap="__e"><text class="data-v-6f5c83ee">去结算</text></view></view></block><block wx:if="{{loading}}"><loading vue-id="41e6841d-1" class="data-v-6f5c83ee" bind:__l="__l"></loading></block></view>