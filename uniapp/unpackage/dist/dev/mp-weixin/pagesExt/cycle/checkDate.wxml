<block wx:if="{{pageState}}"><view class="data-v-b600f6ae"><calendar vue-id="0a1cdd06-1" is-show="{{true}}" between-start="{{disabledStartDate}}" ys-num="{{opt.ys}}" choose-type="{{opt.type}}" start-date="{{startDate}}" end-date="{{endDate}}" tip-data="{{t_data}}" mode="1" data-event-opts="{{[['^callback',[['getDate']]]]}}" bind:callback="__e" class="data-v-b600f6ae" bind:__l="__l"></calendar><block wx:if="{{noticeState}}"><view class="date-notice data-v-b600f6ae">点击日期修改开始时间</view></block><view data-event-opts="{{[['tap',[['toDetail',['$event']]]]]}}" class="date-footer data-v-b600f6ae" bindtap="__e">确定</view></view></block>