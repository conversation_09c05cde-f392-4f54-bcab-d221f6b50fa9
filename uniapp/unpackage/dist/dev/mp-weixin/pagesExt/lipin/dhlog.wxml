<block wx:if="{{isload==1}}"><view class="container"><view class="content" id="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item"><view class="f1"><text class="t1" style="margin-bottom:10rpx;">{{item.remark}}</text><block wx:if="{{item.cardno}}"><text class="t1">{{"卡号："+item.cardno}}</text></block><block wx:else><text class="t1">{{"兑换码："+item.code}}</text></block><text class="t2">{{"兑换时间："+item.createtime}}</text></view></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="5c720d81-1" bind:__l="__l"></nodata></block></view><block wx:if="{{nomore}}"><nomore vue-id="5c720d81-2" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="5c720d81-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="5c720d81-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5c720d81-5" data-ref="popmsg" bind:__l="__l"></popmsg></view></block>