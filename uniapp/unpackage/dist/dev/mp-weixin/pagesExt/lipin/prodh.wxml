<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['topay',['$event']]]]]}}" bindsubmit="__e"><block wx:if="{{needaddress==0}}"><view class="address-add"><view class="linkitem"><text class="f1">联 系 人：</text><input class="input" type="text" placeholder="请输入您的姓名" placeholder-style="color:#626262;font-size:28rpx" data-event-opts="{{[['input',[['inputLinkman',['$event']]]]]}}" value="{{linkman}}" bindinput="__e"/></view><view class="linkitem"><text class="f1">联系电话：</text><input class="input" type="text" placeholder="请输入您的手机号" placeholder-style="color:#626262;font-size:28rpx" data-event-opts="{{[['input',[['inputTel',['$event']]]]]}}" value="{{tel}}" bindinput="__e"/></view></view></block><block wx:else><view class="address-add flex-y-center" data-url="{{'/pagesB/address/'+(address.id?'address':'addressadd')+'?fromPage=buy&type='+(havetongcheng==1?'1':'0')}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image class="img" src="{{pre_url+'/static/img/address.png'}}"></image></view><block wx:if="{{address.id}}"><view class="f2 flex1"><view style="font-weight:bold;color:#111111;font-size:30rpx;">{{address.name+" "+address.tel+''}}<block wx:if="{{address.company}}"><text>{{address.company}}</text></block></view><view style="font-size:24rpx;">{{address.area+" "+address.address}}</view></view></block><block wx:else><view class="f2 flex1">请选择收货地址</view></block><image class="f3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:for="{{$root.l3}}" wx:for-item="buydata" wx:for-index="index" wx:key="index"><view class="buydata"><view class="btitle"><image class="img" src="{{pre_url+'/static/img/ico-shop.png'}}"></image>{{buydata.$orig.business.name}}</view><view class="bcontent"><view class="product"><block wx:for="{{buydata.l0}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><view class="item flex-y-center"><view class="img" data-url="{{'/pages/shop/product?id='+item.$orig.product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.$orig.product.pic}}"></image></view><view data-event-opts="{{[['tap',[['chooseClick',['$0',index2],[[['allbuydata','',index],['prodata','',index2]]]]]]]}}" class="flex1 flex-y-center" bindtap="__e"><view class="info flex1"><view class="f1">{{item.$orig.product.name}}</view><view class="f2">{{"规格："+item.$orig.guige.name}}</view><view class="f3"><text style="font-weight:bold;">{{"￥"+item.$orig.guige.sell_price}}</text><block wx:if="{{item.$orig.guige.score_price}}"><text style="font-weight:bold;">{{"+"+item.$orig.guige.score_price+item.m0}}</text></block><text style="padding-left:20rpx;">{{'× '+item.$orig.num}}</text></view></view><block wx:if="{{needChoose}}"><view class="radio" style="{{(chooseIndex===index2?'background:'+item.m1+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></block></view></view></block></view><view class="freight"><view class="f1">配送方式</view><view class="freight-ul"><view class="flex" style="width:100%;overflow-y:hidden;overflow-x:scroll;"><block wx:for="{{buydata.l1}}" wx:for-item="item" wx:for-index="idx2" wx:key="idx2"><block><view class="freight-li" style="{{(buydata.$orig.freightkey==idx2?'color:'+item.m2+';background:rgba('+item.m3+',0.2)':'')}}" data-bid="{{buydata.$orig.bid}}" data-index="{{idx2}}" data-event-opts="{{[['tap',[['changeFreight',['$event']]]]]}}" bindtap="__e">{{item.$orig.name}}</view></block></block></view></view><block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].minpriceset==1&&buydata.$orig.freightList[buydata.$orig.freightkey].minprice>0&&buydata.$orig.freightList[buydata.$orig.freightkey].minprice>buydata.$orig.product_price}}"><view class="freighttips">{{"满"+buydata.$orig.freightList[buydata.$orig.freightkey].minprice+"元起送，还差"+buydata.g0+"元"}}</view></block><block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].isoutjuli==1}}"><view class="freighttips">超出配送范围</view></block></view><block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].pstimeset==1}}"><view class="price"><view class="f1">{{(buydata.$orig.freightList[buydata.$orig.freightkey].pstype==1?'取货':'配送')+"时间"}}</view><view class="f2" data-bid="{{buydata.$orig.bid}}" data-event-opts="{{[['tap',[['choosePstime',['$event']]]]]}}" bindtap="__e">{{buydata.$orig.pstimetext==''?'请选择时间':buydata.$orig.pstimetext}}<text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></view></block><block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].pstype==1&&buydata.$orig.freightList[buydata.$orig.freightkey].isbusiness!=1}}"><view class="storeitem"><view class="panel"><view class="f1">取货地点</view><block wx:if="{{buydata.g1>0}}"><view class="f2" data-bid="{{buydata.$orig.bid}}" data-freightkey="{{buydata.$orig.freightkey}}" data-storekey="{{buydata.$orig.freightList[buydata.$orig.freightkey].storekey}}" data-event-opts="{{[['tap',[['openMendian',['$event']]]]]}}" bindtap="__e"><text class="iconfont icondingwei"></text>{{''+buydata.$orig.freightList[buydata.$orig.freightkey].storedata[buydata.$orig.freightList[buydata.$orig.freightkey].storekey].name+''}}</view></block><block wx:else><view class="f2">暂无</view></block></view><block wx:for="{{buydata.l2}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><block><block wx:if="{{idx<5||storeshowall==true}}"><view class="radio-item" data-bid="{{buydata.$orig.bid}}" data-index="{{idx}}" data-event-opts="{{[['tap',[['choosestore',['$event']]]]]}}" catchtap="__e"><view class="f1"><view>{{item.$orig.name}}</view><block wx:if="{{item.$orig.address}}"><view class="flex-y-center" style="text-align:left;font-size:24rpx;color:#aaaaae;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;">{{item.$orig.address}}</view></block></view><text style="color:#f50;">{{item.$orig.juli}}</text><view class="radio" style="{{(buydata.$orig.freightList[buydata.$orig.freightkey].storekey==idx?'background:'+item.m4+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></block></block><block wx:if="{{buydata.g2}}"><view data-event-opts="{{[['tap',[['doStoreShowAll',['$event']]]]]}}" class="storeviewmore" bindtap="__e">- 查看更多 -</view></block></view></block><block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].pstype==1&&buydata.$orig.freightList[buydata.$orig.freightkey].isbusiness==1}}"><view class="storeitem"><view class="panel"><view class="f1">取货地点</view></view><block wx:for="{{buydata.$orig.freightList[buydata.$orig.freightkey].storedata}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><block><block wx:if="{{idx<5||storeshowall==true}}"><view class="radio-item" data-freightkey="{{buydata.$orig.freightkey}}" data-storekey="{{idx}}" data-bid="{{buydata.$orig.bid}}" data-index="{{idx}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e"><view class="f1"><view>{{item.name}}</view><block wx:if="{{item.address}}"><view class="flex-y-center" style="text-align:left;font-size:24rpx;color:#aaaaae;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;">{{item.address}}</view></block></view><text style="color:#f50;">{{item.juli}}</text></view></block></block></block><block wx:if="{{buydata.g3}}"><view data-event-opts="{{[['tap',[['doStoreShowAll',['$event']]]]]}}" class="storeviewmore" bindtap="__e">- 查看更多 -</view></block></view></block><view class="price"><view class="f1">商品金额</view><view class="f2"><text>{{"¥"+(needChoose?chooseIndex===''?buydata.$orig.prodata[0].price:buydata.$orig.prodata[chooseIndex].price:buydata.$orig.product_price)}}</text><block wx:if="{{buydata.$orig.score_price&&buydata.$orig.score_price*1>0}}"><text>{{"+"+(needChoose?chooseIndex===''?buydata.$orig.prodata[0].score:buydata.$orig.prodata[chooseIndex].score:buydata.$orig.score_price)+buydata.m5}}</text></block></view></view><block wx:if="{{hdinfo.freight_status==1}}"><view class="price"><view class="f1">{{''+(buydata.$orig.freightList[buydata.$orig.freightkey].freight_price_txt||'运费')+''}}<block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].pstype!=1&&buydata.$orig.freightList[buydata.$orig.freightkey].freeset==1}}"><text style="color:#aaa;font-size:24rpx;">{{"（满"+buydata.$orig.freightList[buydata.$orig.freightkey].free_price+"元包邮）"}}</text></block></view><text class="f2">{{"+¥"+buydata.$orig.freightList[buydata.$orig.freightkey].freight_price}}</text></view></block><view style="display:none;">{{test}}</view><block wx:for="{{buydata.$orig.freightList[buydata.$orig.freightkey].formdata}}" wx:for-item="item" wx:for-index="idx" wx:key="id"><view class="form-item"><view class="label">{{item.val1}}<block wx:if="{{item.val3==1}}"><text style="color:red;">*</text></block></view><block wx:if="{{item.key=='input'}}"><block><input class="input" type="text" name="{{'form'+buydata.$orig.bid+'_'+idx}}" placeholder="{{item.val2}}" placeholder-style="font-size:28rpx"/></block></block><block wx:if="{{item.key=='textarea'}}"><block><textarea class="textarea" name="{{'form'+buydata.$orig.bid+'_'+idx}}" placeholder="{{item.val2}}" placeholder-style="font-size:28rpx"></textarea></block></block><block wx:if="{{item.key=='radio'}}"><block><radio-group class="radio-group" name="{{'form'+buydata.$orig.bid+'_'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><radio class="radio" value="{{item1}}"></radio>{{item1+''}}</label></block></radio-group></block></block><block wx:if="{{item.key=='checkbox'}}"><block><checkbox-group class="checkbox-group" name="{{'form'+buydata.$orig.bid+'_'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><checkbox class="checkbox" value="{{item1}}"></checkbox>{{item1+''}}</label></block></checkbox-group></block></block><block wx:if="{{item.key=='selector'}}"><block><picker class="picker" mode="selector" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="{{item.val2[buydata.$orig.editorFormdata[idx]]}}" range="{{item.val2}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{buydata.$orig.editorFormdata[idx]||buydata.$orig.editorFormdata[idx]===0}}"><view>{{''+item.val2[buydata.$orig.editorFormdata[idx]]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='time'}}"><block><picker class="picker" mode="time" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="{{buydata.$orig.editorFormdata[idx]}}" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{buydata.$orig.editorFormdata[idx]}}"><view>{{buydata.$orig.editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='date'}}"><block><picker class="picker" mode="date" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="{{buydata.$orig.editorFormdata[idx]}}" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{buydata.$orig.editorFormdata[idx]}}"><view>{{buydata.$orig.editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='upload'}}"><block><input style="display:none;" type="text" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="{{buydata.$orig.editorFormdata[idx]}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{buydata.$orig.editorFormdata[idx]}}"><view class="form-imgbox"><view class="layui-imgbox-close" style="z-index:2;" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="form-imgbox-img"><image class="image" src="{{buydata.$orig.editorFormdata[idx]}}" data-url="{{buydata.$orig.editorFormdata[idx]}}" mode="aspectFit" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:else><view class="form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></block></view></block></block></view></block></view></view></block><block wx:if="{{$root.g4}}"><view class="address-add"><view style="width:100%;padding:20rpx 0rpx;display:flex;align-items:center;color:#111111;font-weight:bold;font-size:30rpx;">费用</view><block wx:for="{{hdinfo.fee_items}}" wx:for-item="hitem" wx:for-index="hindex" wx:key="hindex"><view class="price"><text class="f1">{{hitem.name}}</text><text class="f2">{{"￥"+hitem.money}}</text></view></block></view></block><view style="width:100%;height:182rpx;"></view><view class="footer flex"><view class="text1 flex1">总计：<text style="font-weight:bold;font-size:36rpx;">{{"￥"+alltotalprice}}</text></view><button class="op" style="{{'background:'+('linear-gradient(-90deg,'+$root.m6+' 0%,rgba('+$root.m7+',0.8) 100%)')+';'}}" form-type="submit">确定兑换</button></view></form><block wx:if="{{pstimeDialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hidePstimeDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">{{"请选择"+(allbuydata[nowbid].freightList[allbuydata[nowbid].freightkey].pstype==1?'取货':'配送')+"时间"}}</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hidePstimeDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l4}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="pstime-item" data-index="{{index}}" data-event-opts="{{[['tap',[['pstimeRadioChange',['$event']]]]]}}" bindtap="__e"><view class="flex1">{{item.$orig.title}}</view><view class="radio" style="{{(allbuydata[nowbid].freight_time==item.$orig.value?'background:'+item.m8+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="e3f8f720-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="e3f8f720-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="e3f8f720-3" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="e3f8f720-4" bind:__l="__l"></wxxieyi></view>