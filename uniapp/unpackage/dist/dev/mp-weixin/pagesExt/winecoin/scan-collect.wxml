<view class="container"><block wx:if="{{isload}}"><block><view class="merchant-info"><view class="merchant-header"><image class="merchant-avatar" src="{{userinfo.headimg||'/static/img/default_avatar.png'}}"></image><view class="merchant-details"><view class="merchant-name">{{userinfo.nickname||'商家'}}</view><view class="merchant-balance">{{"余额："+(userinfo.wine_coin||'0.00')+" 酒币"}}</view></view></view></view><view class="scan-section"><view class="scan-title">扫描用户付款码收款</view><view data-event-opts="{{[['tap',[['startScan',['$event']]]]]}}" class="scan-container" bindtap="__e"><view class="scan-frame"><view class="scan-icon">📷</view><view class="scan-text">点击扫描付款码</view></view></view><view class="scan-tips">请确保光线充足，将付款码放在取景框内</view></view><view class="manual-section"><view class="manual-title">或手动输入付款码</view><view class="input-group"><input class="payment-code-input" placeholder="请输入付款码" maxlength="50" data-event-opts="{{[['input',[['__set_model',['','manualCode','$event',[]]]]]]}}" value="{{manualCode}}" bindinput="__e"/><button class="confirm-btn" disabled="{{!$root.g0}}" data-event-opts="{{[['tap',[['confirmManualCode',['$event']]]]]}}" bindtap="__e">确认</button></view></view><view class="quick-amounts"><view class="amounts-title">快捷金额</view><view class="amounts-grid"><block wx:for="{{quickAmounts}}" wx:for-item="amount" wx:for-index="__i0__" wx:key="*this"><view data-event-opts="{{[['tap',[['setQuickAmount',['$0'],[[['quickAmounts','',__i0__]]]]]]]}}" class="amount-item" bindtap="__e">{{''+amount+'酒币'}}</view></block></view></view><view class="collect-records"><view class="record-item" data-url="/pagesExt/winecoin/log" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="record-icon">📋</view><view class="record-text">收款记录</view><view class="record-arrow">></view></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="f275ae0a-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="f275ae0a-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="f275ae0a-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>