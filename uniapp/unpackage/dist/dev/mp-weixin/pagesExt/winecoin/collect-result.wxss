
.container {
	background-color: #F8FAFC;
	min-height: 100vh;
	padding: 60rpx 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

/* 结果状态 */
.result-section {
	text-align: center;
	margin-bottom: 60rpx;
}
.result-icon {
	font-size: 120rpx;
	margin-bottom: 30rpx;
	display: block;
}
.result-icon.success {
	color: #059669;
}
.result-icon.error {
	color: #DC2626;
}
.result-title {
	font-size: 48rpx;
	font-weight: 600;
	margin-bottom: 20rpx;
}
.result-title {
	color: #059669;
}
.result-section .error .result-title {
	color: #DC2626;
}
.result-message {
	font-size: 28rpx;
	color: #64748B;
}

/* 交易详情 */
.transaction-details {
	width: 100%;
	background: #fff;
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
	margin-bottom: 40rpx;
}
.amount-display {
	text-align: center;
	padding-bottom: 40rpx;
	border-bottom: 1rpx solid #F1F5F9;
	margin-bottom: 40rpx;
}
.amount-label {
	font-size: 28rpx;
	color: #64748B;
	margin-bottom: 16rpx;
}
.amount-value {
	font-size: 64rpx;
	font-weight: 700;
	color: #059669;
	margin-bottom: 12rpx;
}
.amount-rmb {
	font-size: 24rpx;
	color: #94A3B8;
}
.detail-items {
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}
.detail-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.detail-label {
	font-size: 28rpx;
	color: #64748B;
}
.detail-value {
	font-size: 28rpx;
	color: #1E293B;
	font-weight: 500;
	max-width: 400rpx;
	text-align: right;
	word-break: break-all;
}

/* 操作按钮 */
.action-buttons {
	width: 100%;
	display: flex;
	gap: 20rpx;
	margin-bottom: 40rpx;
}
.secondary-btn,
.primary-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 16rpx;
	font-size: 32rpx;
	font-weight: 600;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
}
.secondary-btn {
	background: #F8FAFC;
	color: #64748B;
	border: 2rpx solid #E2E8F0;
}
.primary-btn {
	background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
	color: #fff;
}

/* 分享按钮 */
.share-section {
	width: 100%;
}
.share-btn {
	width: 100%;
	height: 88rpx;
	background: #fff;
	border: 2rpx solid #E2E8F0;
	border-radius: 16rpx;
	font-size: 28rpx;
	color: #64748B;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 500;
}
.share-btn:active {
	background: #F8FAFC;
}

