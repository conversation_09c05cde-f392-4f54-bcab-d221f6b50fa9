<view class="container"><block wx:if="{{isload}}"><block><view class="wine-balance" style="{{'background:'+(balanceBg)+';'}}"><view class="balance-info"><view class="balance-label">我的酒币</view><view class="balance-amount">{{userinfo.wine_coin||'0.00'}}</view><view class="balance-tips" data-url="/pagesExt/winecoin/log" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>充值记录</text><text class="iconfont iconjiantou" style="font-size:20rpx;"></text></view></view></view><view class="recharge-content"><block wx:if="{{rechargeSet.can_input==1}}"><view class="amount-section"><view class="section-title">充值金额（元）</view><view class="amount-input-box"><text class="currency-symbol">¥</text><input style="font-size:60rpx;" type="digit" name="amount" placeholder="请输入充值金额" placeholder-style="color:#999;font-size:40rpx" data-event-opts="{{[['input',[['onAmountInput',['$event']]]]]}}" value="{{rechargeAmount}}" bindinput="__e"/></view><block wx:if="{{rechargeSet.min_amount>0}}"><view class="amount-tips">{{'最低充值'+rechargeSet.min_amount+'元'}}</view></block></view></block><block wx:if="{{$root.g0}}"><view class="preset-amounts"><block wx:if="{{rechargeSet.can_input==0}}"><view class="section-title">选择充值金额</view></block><view class="amount-grid"><block wx:for="{{rechargeSet.give_sets}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.money>0}}"><view class="{{['amount-item',selectedAmount==item.money?'selected':'']}}" data-amount="{{item.money}}" data-give="{{item.give}}" data-event-opts="{{[['tap',[['selectAmount',['$event']]]]]}}" bindtap="__e"><text class="amount-money">{{(rechargeSet.can_input==1?'满':'充')+item.money+"元"}}</text><block wx:if="{{item.give>0}}"><text class="amount-give">{{"赠"+item.give+"酒币"}}</text></block><block wx:if="{{item.give_score>0}}"><text class="amount-give">{{"+"+item.give_score+"积分"}}</text></block></view></block></block></view></view></block><block wx:if="{{rechargeSet.recharge_desc}}"><view class="recharge-desc"><view class="desc-title">充值说明</view><text class="desc-content">{{rechargeSet.recharge_desc}}</text></view></block></view><view class="recharge-actions"><view data-event-opts="{{[['tap',[['goRecharge',['$event']]]]]}}" class="recharge-btn" style="{{'background:'+(btnBg)+';'}}" bindtap="__e">立即充值</view></view></block></block><block wx:if="{{loading}}"><loading vue-id="43a5e864-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="43a5e864-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="43a5e864-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>