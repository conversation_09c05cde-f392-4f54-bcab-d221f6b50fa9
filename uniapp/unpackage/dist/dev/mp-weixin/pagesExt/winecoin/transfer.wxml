<view class="container"><block wx:if="{{isload}}"><block><view class="wine-balance" style="{{'background:'+(balanceBg)+';'}}"><view class="balance-info"><view class="balance-label">可转账酒币</view><view class="balance-amount">{{userinfo.wine_coin||'0.00'}}</view><view class="balance-tips">{{"今日已转账: "+(transferSet.today_transferred||'0.00')}}</view></view></view><view class="transfer-form"><view class="form-section"><view class="section-title">收款人</view><block wx:if="{{!selectedUser}}"><view data-event-opts="{{[['tap',[['showUserSearch',['$event']]]]]}}" class="recipient-selector" bindtap="__e"><text class="placeholder">点击选择收款人</text><text class="iconfont iconjiantou"></text></view></block><block wx:if="{{selectedUser}}"><view class="selected-user"><image class="user-avatar" src="{{selectedUser.headimg||pre_url+'/static/img/default-avatar.png'}}"></image><view class="user-info"><text class="user-name">{{selectedUser.nickname}}</text><text class="user-id">{{"ID: "+selectedUser.id}}</text></view><view data-event-opts="{{[['tap',[['showUserSearch',['$event']]]]]}}" class="change-user" bindtap="__e"><text class="iconfont iconbianji"></text></view></view></block></view><view class="form-section"><view class="section-title">转账金额</view><view class="amount-input-box"><input type="digit" name="amount" placeholder="请输入转账金额" placeholder-style="color:#999;" data-event-opts="{{[['input',[['onAmountInput',['$event']]]]]}}" value="{{transferAmount}}" bindinput="__e"/><text class="unit">酒币</text></view><view class="amount-tips"><text>{{"最小转账: "+transferSet.min_amount+"酒币"}}</text><text>{{"最大转账: "+transferSet.max_amount+"酒币"}}</text></view></view><block wx:if="{{transferSet.fee_enable&&showFeeInfo}}"><view class="form-section"><view class="fee-info-box"><view class="fee-row"><text class="fee-label">{{"手续费 ("+transferSet.fee_rate_percent+"%)"}}</text><text class="fee-amount">{{feeAmount+" 酒币"}}</text></view><view class="fee-row total-row"><text class="fee-label">实际扣除</text><text class="fee-amount total">{{totalAmount+" 酒币"}}</text></view></view></view></block><view class="form-section"><view class="section-title">转账备注（可选）</view><view class="remark-input-box"><textarea name="remark" placeholder="请输入转账备注" placeholder-style="color:#999;" maxlength="100" data-event-opts="{{[['input',[['onRemarkInput',['$event']]]]]}}" value="{{transferRemark}}" bindinput="__e"></textarea></view></view><block wx:if="{{transferSet.transfer_desc}}"><view class="transfer-desc"><view class="desc-title">转账说明</view><text class="desc-content">{{transferSet.transfer_desc}}</text></view></block></view><view class="transfer-actions"><view data-event-opts="{{[['tap',[['goTransfer',['$event']]]]]}}" class="transfer-btn" style="{{'background:'+(btnBg)+';'}}" bindtap="__e">确认转账</view></view><block wx:if="{{showSearchModal}}"><view data-event-opts="{{[['tap',[['hideUserSearch',['$event']]]]]}}" class="user-search-modal" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content" catchtap="__e"><view class="modal-header"><text class="modal-title">选择收款人</text><text data-event-opts="{{[['tap',[['hideUserSearch',['$event']]]]]}}" class="modal-close" bindtap="__e">×</text></view><view class="search-box"><view class="input-wrapper"><input type="number" placeholder="请输入手机号" placeholder-style="color:#999999;" maxlength="11" data-event-opts="{{[['input',[['onSearchInput',['$event']]]],['confirm',[['searchUsers',['$event']]]]]}}" value="{{searchKeyword}}" bindinput="__e" bindconfirm="__e"/></view><view data-event-opts="{{[['tap',[['searchUsers',['$event']]]]]}}" class="{{['search-btn',(searchLoading)?'disabled':'']}}" bindtap="__e"><block wx:if="{{!searchLoading}}"><text>搜索</text></block><block wx:else><text>搜索中...</text></block></view></view><view class="search-tips"><text>💡 只能通过手机号搜索用户，请确保对方已授权手机号</text></view><block wx:if="{{$root.g0>0}}"><view class="search-results"><block wx:for="{{$root.l0}}" wx:for-item="user" wx:for-index="index" wx:key="index"><view class="search-result-item" data-user="{{user.g1}}" data-event-opts="{{[['tap',[['selectUser',['$event']]]]]}}" bindtap="__e"><image class="result-avatar" src="{{user.$orig.headimg||pre_url+'/static/img/default-avatar.png'}}"></image><view class="result-info"><text class="result-name">{{user.$orig.nickname}}</text><text class="result-phone">{{user.m0}}</text></view><view class="select-icon">✓</view></view></block></view></block><block wx:if="{{$root.g2}}"><view class="no-results"><text class="no-results-icon">😔</text><text class="no-results-text">未找到该手机号对应的用户</text><text class="no-results-tips">请确认手机号正确且对方已授权</text></view></block><block wx:if="{{$root.g3}}"><view class="search-empty"><text>未找到相关用户</text></view></block><block wx:if="{{searchLoading}}"><view class="search-loading"><text>搜索中...</text></view></block></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="2c6805ec-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="2c6805ec-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="2c6805ec-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>