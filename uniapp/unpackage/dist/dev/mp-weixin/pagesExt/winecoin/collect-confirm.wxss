
.container {
	background-color: #F8FAFC;
	min-height: 100vh;
	padding-bottom: 200rpx;
}

/* 付款用户信息 */
.payer-info {
	background: #fff;
	margin: 20rpx;
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
}
.payer-header {
	display: flex;
	align-items: center;
}
.payer-avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	margin-right: 24rpx;
}
.payer-details {
	flex: 1;
}
.payer-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #1E293B;
	margin-bottom: 8rpx;
}
.payer-balance {
	font-size: 24rpx;
	color: #64748B;
}
.payment-status {
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
	font-weight: 500;
}
.payment-status.valid {
	background: #DCFCE7;
	color: #059669;
}
.payment-status.expired {
	background: #FEE2E2;
	color: #DC2626;
}
.payment-status.used {
	background: #F3F4F6;
	color: #6B7280;
}

/* 收款金额 */
.amount-section {
	background: #fff;
	margin: 20rpx;
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
}
.amount-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #1E293B;
	margin-bottom: 30rpx;
}
.amount-input-container {
	display: flex;
	align-items: center;
	border-bottom: 4rpx solid #FFD700;
	padding-bottom: 20rpx;
	margin-bottom: 20rpx;
}
.currency-symbol {
	font-size: 48rpx;
	color: #FFD700;
	font-weight: 600;
	margin-right: 16rpx;
}
.amount-input {
	flex: 1;
	font-size: 64rpx;
	font-weight: 600;
	color: #1E293B;
	text-align: center;
}
.currency-unit {
	font-size: 28rpx;
	color: #64748B;
	margin-left: 16rpx;
}
.amount-tips {
	font-size: 24rpx;
	color: #94A3B8;
	text-align: center;
}

/* 快捷金额 */
.quick-amounts {
	margin: 20rpx;
}
.amounts-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 16rpx;
}
.amount-item {
	height: 80rpx;
	border: 2rpx solid #E2E8F0;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	color: #64748B;
	background: #fff;
	transition: all 0.3s ease;
}
.amount-item.active,
.amount-item:active {
	border-color: #FFD700;
	background: #FFFBF0;
	color: #F59E0B;
}

/* 收款备注 */
.remark-section {
	background: #fff;
	margin: 20rpx;
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
}
.remark-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #1E293B;
	margin-bottom: 24rpx;
}
.remark-input {
	width: 100%;
	height: 120rpx;
	border: 2rpx solid #E2E8F0;
	border-radius: 16rpx;
	padding: 20rpx;
	font-size: 28rpx;
	color: #1E293B;
	background: #F8FAFC;
	resize: none;
}
.remark-input:focus {
	border-color: #FFD700;
	background: #fff;
}

/* 操作按钮 */
.action-buttons {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding: 30rpx;
	border-top: 1rpx solid #F1F5F9;
	display: flex;
	gap: 20rpx;
}
.cancel-btn,
.confirm-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 16rpx;
	font-size: 32rpx;
	font-weight: 600;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
}
.cancel-btn {
	background: #F8FAFC;
	color: #64748B;
	border: 2rpx solid #E2E8F0;
}
.confirm-btn {
	background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
	color: #fff;
}
.confirm-btn.disabled {
	background: #E2E8F0;
	color: #94A3B8;
}

/* 付款码信息 */
.code-info {
	background: #fff;
	margin: 20rpx;
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
}
.info-item {
	display: flex;
	margin-bottom: 16rpx;
}
.info-item:last-child {
	margin-bottom: 0;
}
.info-label {
	font-size: 28rpx;
	color: #64748B;
	width: 140rpx;
}
.info-value {
	flex: 1;
	font-size: 28rpx;
	color: #1E293B;
	word-break: break-all;
}

