<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{payerInfo}}"><view class="payer-info"><view class="payer-header"><image class="payer-avatar" src="{{payerInfo.headimg||'/static/img/default_avatar.png'}}"></image><view class="payer-details"><view class="payer-name">{{payerInfo.nickname||'用户'}}</view><view class="payer-balance">{{"余额："+(payerInfo.wine_coin||'0.00')+" 酒币"}}</view></view><view class="{{['payment-status',paymentCodeStatus]}}">{{''+paymentCodeStatusText+''}}</view></view></view></block><view class="amount-section"><view class="amount-title">收款金额</view><view class="amount-input-container"><text class="currency-symbol">￥</text><input class="amount-input" type="digit" placeholder="0.00" maxlength="10" data-event-opts="{{[['input',[['__set_model',['','collectAmount','$event',[]]],['onAmountInput',['$event']]]]]}}" value="{{collectAmount}}" bindinput="__e"/><text class="currency-unit">酒币</text></view><block wx:if="{{collectAmount}}"><view class="amount-tips">{{'约等于 '+$root.g0+' 元'}}</view></block></view><view class="quick-amounts"><view class="amounts-grid"><block wx:for="{{quickAmounts}}" wx:for-item="amount" wx:for-index="__i0__" wx:key="*this"><view data-event-opts="{{[['tap',[['setAmount',['$0'],[[['quickAmounts','',__i0__]]]]]]]}}" class="{{['amount-item',(collectAmount==amount)?'active':'']}}" bindtap="__e">{{''+amount+''}}</view></block></view></view><view class="remark-section"><view class="remark-title">收款备注（可选）</view><textarea class="remark-input" placeholder="请输入收款备注" maxlength="100" data-event-opts="{{[['input',[['__set_model',['','collectRemark','$event',[]]]]]]}}" value="{{collectRemark}}" bindinput="__e"></textarea></view><view class="action-buttons"><button data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="cancel-btn" bindtap="__e">取消</button><button class="{{['confirm-btn',(!canConfirm)?'disabled':'']}}" disabled="{{!canConfirm}}" data-event-opts="{{[['tap',[['confirmCollect',['$event']]]]]}}" bindtap="__e">确认收款</button></view><block wx:if="{{paymentCodeInfo}}"><view class="code-info"><view class="info-item"><text class="info-label">付款码：</text><text class="info-value">{{paymentCodeInfo.payment_code}}</text></view><view class="info-item"><text class="info-label">有效期：</text><text class="info-value">{{$root.m0}}</text></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="24d4d7ea-1" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="24d4d7ea-2" data-ref="popmsg" bind:__l="__l"></popmsg></view>