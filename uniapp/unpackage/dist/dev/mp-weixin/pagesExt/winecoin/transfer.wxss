
.container {
	background-color: #F8FAFC;
	min-height: 100vh;
}

/* 酒币余额显示 */
.wine-balance {
	padding: 40rpx 30rpx;
	margin: 20rpx;
	border-radius: 24rpx;
	color: #fff;
}
.balance-info {
	text-align: center;
}
.balance-label {
	font-size: 28rpx;
	opacity: 0.9;
	margin-bottom: 16rpx;
}
.balance-amount {
	font-size: 72rpx;
	font-weight: 700;
	margin-bottom: 16rpx;
}
.balance-tips {
	font-size: 24rpx;
	opacity: 0.8;
}

/* 转账表单 */
.transfer-form {
	background: #fff;
	margin: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
}
.form-section {
	padding: 32rpx 24rpx;
	border-bottom: 1rpx solid #F1F5F9;
}
.form-section:last-child {
	border-bottom: none;
}
.section-title {
	color: #1E293B;
	font-size: 28rpx;
	font-weight: 600;
	margin-bottom: 20rpx;
}

/* 收款人选择 */
.recipient-selector {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx;
	background: #F8FAFC;
	border-radius: 12rpx;
	border: 2rpx dashed #CBD5E1;
}
.placeholder {
	color: #64748B;
	font-size: 28rpx;
}
.selected-user {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background: #F8FAFC;
	border-radius: 12rpx;
	border: 2rpx solid #10B981;
}
.user-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 20rpx;
}
.user-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}
.user-name {
	color: #1E293B;
	font-size: 28rpx;
	font-weight: 600;
	margin-bottom: 4rpx;
}
.user-id {
	color: #64748B;
	font-size: 24rpx;
}
.change-user {
	padding: 16rpx;
	color: #64748B;
	font-size: 32rpx;
}

/* 金额输入 */
.amount-input-box {
	display: flex;
	align-items: center;
	padding: 24rpx;
	background: #F8FAFC;
	border-radius: 12rpx;
	margin-bottom: 16rpx;
}
.amount-input-box input {
	flex: 1;
	font-size: 48rpx;
	font-weight: 600;
	color: #1E293B;
}
.unit {
	color: #64748B;
	font-size: 28rpx;
	margin-left: 16rpx;
}
.amount-tips {
	display: flex;
	justify-content: space-between;
	color: #64748B;
	font-size: 24rpx;
}

/* 备注输入 */
.remark-input-box {
	background: #F8FAFC;
	border-radius: 12rpx;
	padding: 24rpx;
}
.remark-input-box textarea {
	width: 100%;
	min-height: 120rpx;
	font-size: 28rpx;
	color: #1E293B;
	line-height: 1.5;
}

/* 转账说明 */
.transfer-desc {
	padding: 24rpx;
	border-top: 1rpx solid #F1F5F9;
}
.desc-title {
	color: #1E293B;
	font-size: 28rpx;
	font-weight: 600;
	margin-bottom: 12rpx;
}
.desc-content {
	color: #64748B;
	font-size: 24rpx;
	line-height: 1.6;
}

/* 转账按钮 */
.transfer-actions {
	padding: 40rpx 20rpx;
}
.transfer-btn {
	width: 100%;
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 32rpx;
	font-weight: 600;
	box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3);
}

/* 用户搜索弹窗 */
.user-search-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}
.modal-content {
	width: 90%;
	max-height: 80%;
	background: #fff;
	border-radius: 16rpx;
	overflow: hidden;
}
.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx 24rpx;
	border-bottom: 1rpx solid #F1F5F9;
}
.modal-title {
	color: #1E293B;
	font-size: 32rpx;
	font-weight: 600;
}
.modal-close {
	color: #64748B;
	font-size: 48rpx;
	padding: 8rpx;
}

/* 搜索框 */
.search-box {
	display: flex;
	align-items: center;
	padding: 24rpx;
	border-bottom: 1rpx solid #F1F5F9;
}
.input-wrapper {
	flex: 1;
	height: 80rpx;
	background: #F8FAFC;
	border-radius: 12rpx;
	border: 2rpx solid #E2E8F0;
	margin-right: 16rpx;
	display: flex;
	align-items: center;
	padding: 0 24rpx;
	box-sizing: border-box;
}
.input-wrapper input {
	width: 100%;
	height: 100%;
	background: transparent;
	border: none;
	outline: none;
	font-size: 32rpx;
	color: #1E293B;
}
.input-wrapper:focus-within {
	border-color: #3B82F6;
	background: #fff;
}
.search-btn {
	padding: 24rpx 32rpx;
	background: #3B82F6;
	border-radius: 12rpx;
	color: #fff;
	font-size: 28rpx;
	font-weight: 600;
	min-width: 120rpx;
	text-align: center;
}
.search-btn.disabled {
	background: #94A3B8;
	color: #fff;
}
.search-tips {
	padding: 20rpx 24rpx;
	background: #FEF3C7;
	border-left: 4rpx solid #F59E0B;
	margin: 0 24rpx;
	border-radius: 8rpx;
}
.search-tips text {
	color: #92400E;
	font-size: 24rpx;
	line-height: 1.5;
}

/* 搜索结果 */
.search-results {
	max-height: 400rpx;
	overflow-y: scroll;
}
.search-result-item {
	display: flex;
	align-items: center;
	padding: 24rpx;
	border-bottom: 1rpx solid #F1F5F9;
	transition: background-color 0.2s;
}
.search-result-item:hover {
	background: #F8FAFC;
}
.search-result-item:last-child {
	border-bottom: none;
}
.result-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 20rpx;
}
.result-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}
.result-name {
	color: #1E293B;
	font-size: 32rpx;
	font-weight: 600;
	margin-bottom: 8rpx;
}
.result-phone {
	color: #64748B;
	font-size: 24rpx;
}
.select-icon {
	color: #10B981;
	font-size: 32rpx;
	font-weight: bold;
}

/* 手续费信息 */
.fee-info-box {
	background: #FEF3C7;
	border: 1rpx solid #F59E0B;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-top: 16rpx;
}
.fee-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}
.fee-row:last-child {
	margin-bottom: 0;
}
.fee-row.total-row {
	border-top: 1rpx solid #F59E0B;
	padding-top: 12rpx;
	margin-top: 12rpx;
	margin-bottom: 0;
}
.fee-label {
	color: #92400E;
	font-size: 28rpx;
}
.fee-amount {
	color: #92400E;
	font-size: 28rpx;
	font-weight: 600;
}
.fee-amount.total {
	color: #DC2626;
	font-size: 32rpx;
	font-weight: bold;
}

/* 无结果状态 */
.no-results {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx 24rpx;
	text-align: center;
}
.no-results-icon {
	font-size: 80rpx;
	margin-bottom: 20rpx;
}
.no-results-text {
	color: #64748B;
	font-size: 28rpx;
	margin-bottom: 12rpx;
}
.no-results-tips {
	color: #94A3B8;
	font-size: 24rpx;
	font-weight: 600;
	margin-bottom: 4rpx;
}
.result-id {
	color: #64748B;
	font-size: 24rpx;
	margin-bottom: 4rpx;
}
.result-tel {
	color: #94A3B8;
	font-size: 22rpx;
}

/* 搜索状态 */
.search-empty,
.search-loading {
	text-align: center;
	padding: 80rpx 40rpx;
	color: #64748B;
	font-size: 28rpx;
}

