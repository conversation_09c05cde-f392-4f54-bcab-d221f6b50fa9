
.container {
	background-color: #F8FAFC;
	min-height: 100vh;
}

/* 酒币钱包卡片 */
.wine-wallet-card {
	margin: 20rpx;
	border-radius: 24rpx;
	overflow: hidden;
	box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.3);
}
.wallet-bg {
	padding: 40rpx 30rpx;
	position: relative;
}
.wallet-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
}
.wallet-title {
	display: flex;
	align-items: center;
	color: #fff;
	font-size: 32rpx;
	font-weight: 600;
}
.wallet-icon {
	width: 48rpx;
	height: 48rpx;
	margin-right: 16rpx;
}
.wallet-help {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	color: #fff;
	font-size: 32rpx;
}
.balance-section {
	text-align: center;
	margin-bottom: 40rpx;
}
.balance-label {
	color: rgba(255, 255, 255, 0.8);
	font-size: 28rpx;
	margin-bottom: 16rpx;
}
.balance-row {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 20rpx;
}
.balance-amount {
	color: #fff;
	font-size: 72rpx;
	font-weight: 700;
	line-height: 1;
	margin-bottom: 8rpx;
}
.balance-toggle {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	cursor: pointer;
	transition: all 0.3s ease;
}
.balance-toggle:hover {
	background: rgba(255, 255, 255, 0.3);
	-webkit-transform: scale(1.1);
	        transform: scale(1.1);
}
.eye-icon {
	font-size: 32rpx;
	color: #fff;
}
.quick-actions {
	display: flex;
	justify-content: space-around;
}
.action-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	color: #fff;
	font-size: 24rpx;
	padding: 16rpx 12rpx;
	border-radius: 16rpx;
	min-width: 120rpx;
	transition: all 0.3s ease;
}
.action-symbol {
	width: 88rpx;
	height: 88rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 12rpx;
	background: rgba(255, 255, 255, 0.2);
	-webkit-backdrop-filter: blur(10rpx);
	        backdrop-filter: blur(10rpx);
	font-size: 48rpx;
	font-weight: bold;
	color: #fff;
}

/* 不同功能的特色背景 */
.recharge-item:active {
	background: rgba(16, 185, 129, 0.2);
}
.withdraw-item:active {
	background: rgba(239, 68, 68, 0.2);
}
.transfer-item:active {
	background: rgba(59, 130, 246, 0.2);
}
.record-item:active {
	background: rgba(156, 163, 175, 0.2);
}
.menu-item {
	display: flex;
	align-items: center;
	padding: 32rpx 24rpx;
	border-bottom: 1rpx solid #F1F5F9;
}
.menu-item:last-child {
	border-bottom: none;
}
.menu-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 16rpx;
	background: #F3F4F6;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}
.menu-symbol {
	font-size: 32rpx;
}
.menu-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}
.menu-title {
	color: #1E293B;
	font-size: 32rpx;
	font-weight: 600;
	margin-bottom: 8rpx;
}
.menu-desc {
	color: #64748B;
	font-size: 24rpx;
}
.menu-arrow {
	color: #CBD5E1;
	font-size: 24rpx;
}

/* 付款码样式 */
.payment-code-item:active {
	background: rgba(168, 85, 247, 0.2);
}
.scan-collect-item:active {
	background: rgba(59, 130, 246, 0.2);
}

/* 付款码弹窗 */
.payment-code-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}
.payment-code-content {
	width: 640rpx;
	background: #fff;
	border-radius: 24rpx;
	padding: 40rpx;
	position: relative;
	max-height: 90vh;
	overflow-y: auto;
}
.payment-code-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #F1F5F9;
}
.payment-code-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #1E293B;
}
.payment-code-close {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #64748B;
	font-size: 32rpx;
	border-radius: 50%;
	background: #F8FAFC;
	cursor: pointer;
}
.payment-code-user {
	display: flex;
	align-items: center;
	margin-bottom: 40rpx;
	padding: 24rpx;
	background: #F8FAFC;
	border-radius: 16rpx;
}
.payment-code-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 24rpx;
}
.payment-code-userinfo {
	flex: 1;
}
.payment-code-nickname {
	font-size: 32rpx;
	font-weight: 600;
	color: #1E293B;
	margin-bottom: 8rpx;
}
.payment-code-balance {
	font-size: 24rpx;
	color: #64748B;
}
.payment-code-qr-section {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 40rpx;
	min-height: 240rpx;
}
.payment-code-qr-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 30rpx;
	background: #fff;
	border: 2rpx solid #E2E8F0;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
.payment-code-qr {
	background: #fff;
}
.payment-code-loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 200rpx;
}
.loading-text {
	color: #64748B;
	font-size: 28rpx;
	margin-top: 20rpx;
}
.payment-code-info {
	text-align: center;
	margin-bottom: 40rpx;
}
.payment-code-tips {
	font-size: 28rpx;
	color: #64748B;
	margin-bottom: 24rpx;
}
.payment-code-expire {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
	font-size: 32rpx;
	font-weight: 600;
	color: #059669;
}
.payment-code-expire.expired {
	color: #DC2626;
}
.expire-text {
	font-size: 24rpx;
	font-weight: normal;
	color: #64748B;
}
.payment-code-actions {
	display: flex;
	gap: 20rpx;
}
.refresh-btn {
	flex: 1;
	height: 88rpx;
	background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
	color: #fff;
	border: none;
	border-radius: 16rpx;
	font-size: 32rpx;
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: center;
}
.refresh-btn:disabled {
	background: #E2E8F0;
	color: #94A3B8;
}
.refresh-btn:active:not(:disabled) {
	background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
}



