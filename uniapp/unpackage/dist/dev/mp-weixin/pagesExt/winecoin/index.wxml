<view class="container"><block wx:if="{{isload}}"><block><view class="wine-wallet-card"><view class="wallet-bg" style="{{'background:'+(walletBg)+';'}}"><view class="wallet-header"><view class="wallet-title"><image class="wallet-icon" src="{{pre_url+'/static/img/winecoin-icon.png'}}"></image><text>酒币钱包</text></view><view class="wallet-help" data-url="/pagesExt/winecoin/help" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="iconfont iconbangzhu"></text></view></view><view class="balance-section"><view class="balance-label">我的酒币</view><view class="balance-row"><view class="balance-amount">{{showBalance?userinfo.wine_coin||'0.00':'****'}}</view><view data-event-opts="{{[['tap',[['toggleBalance',['$event']]]]]}}" class="balance-toggle" bindtap="__e"><text class="eye-icon">{{showBalance?'👁️':'🙈'}}</text></view></view></view><view class="quick-actions"><view class="action-item recharge-item" data-url="/pagesExt/winecoin/recharge" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="action-symbol">+</view><text>充值</text></view><view class="action-item withdraw-item" data-url="/pagesExt/winecoin/withdraw" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="action-symbol">-</view><text>提现</text></view><view class="action-item transfer-item" data-url="/pagesExt/winecoin/transfer" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="action-symbol">⇄</view><text>转账</text></view><view data-event-opts="{{[['tap',[['showPaymentCode',['$event']]]]]}}" class="action-item payment-code-item" bindtap="__e"><view class="action-symbol">⊞</view><text>付款码</text></view><view class="action-item scan-collect-item" data-url="/pagesExt/winecoin/scan-collect" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="action-symbol">📷</view><text>收款</text></view><view class="action-item record-item" data-url="/pagesExt/winecoin/log" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="action-symbol">≡</view><text>明细</text></view></view></view></view><block wx:if="{{showPaymentCodeModal}}"><view data-event-opts="{{[['tap',[['hidePaymentCode',['$event']]]]]}}" class="payment-code-modal" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="payment-code-content" catchtap="__e"><view class="payment-code-header"><view class="payment-code-title">酒币付款码</view><view data-event-opts="{{[['tap',[['hidePaymentCode',['$event']]]]]}}" class="payment-code-close" bindtap="__e">✕</view></view><view class="payment-code-user"><image class="payment-code-avatar" src="{{userinfo.headimg||'/static/img/default_avatar.png'}}"></image><view class="payment-code-userinfo"><view class="payment-code-nickname">{{userinfo.nickname||'用户'}}</view><view class="payment-code-balance">{{"余额："+(userinfo.wine_coin||'0.00')+" 酒币"}}</view></view></view><view class="payment-code-qr-section"><block wx:if="{{paymentCodeData}}"><view class="payment-code-qr-container"><canvas class="payment-code-qr" style="{{'width:'+(qrSize+'px')+';'+('height:'+(qrSize+'px')+';')}}" canvas-id="paymentQRCode"></canvas></view></block><block wx:else><view class="payment-code-loading"><view class="loading-text">正在生成付款码...</view></view></block></view><view class="payment-code-info"><view class="payment-code-tips">请将此付款码展示给商家扫描</view><block wx:if="{{paymentCodeExpire>0}}"><view class="payment-code-expire"><text>{{$root.g0+":"+$root.g1}}</text><text class="expire-text">后过期</text></view></block><block wx:else><view class="payment-code-expire expired"><text>付款码已过期</text></view></block></view><view class="payment-code-actions"><button class="refresh-btn" disabled="{{refreshing}}" data-event-opts="{{[['tap',[['refreshPaymentCode',['$event']]]]]}}" bindtap="__e">{{''+(refreshing?'生成中...':'刷新付款码')+''}}</button></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="246c6c95-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="246c6c95-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="246c6c95-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>