
.container {
	background-color: #F8FAFC;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 主卡片 */
.main-card {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	margin: 20rpx;
	border-radius: 24rpx;
	padding: 40rpx;
	color: #fff;
}
.transaction-header {
	display: flex;
	align-items: center;
}
.transaction-icon {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 32rpx;
	background: rgba(255, 255, 255, 0.2);
	-webkit-backdrop-filter: blur(10rpx);
	        backdrop-filter: blur(10rpx);
}
.transaction-icon.positive {
	background: rgba(16, 185, 129, 0.3);
}
.transaction-icon.negative {
	background: rgba(239, 68, 68, 0.3);
}
.transaction-icon.neutral {
	background: rgba(156, 163, 175, 0.3);
}
.transaction-symbol {
	font-size: 56rpx;
	font-weight: bold;
}
.transaction-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}
.transaction-title {
	font-size: 36rpx;
	font-weight: 700;
	margin-bottom: 8rpx;
}
.transaction-time {
	font-size: 28rpx;
	opacity: 0.8;
}
.transaction-amount {
	text-align: right;
}
.amount-value {
	display: block;
	font-size: 48rpx;
	font-weight: 700;
	line-height: 1;
}
.amount-unit {
	font-size: 24rpx;
	opacity: 0.8;
	margin-top: 8rpx;
}

/* 详情卡片 */
.detail-card {
	background: #fff;
	margin: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.card-title {
	background: #F8FAFC;
	padding: 24rpx 32rpx;
	font-size: 32rpx;
	font-weight: 600;
	color: #334155;
	border-bottom: 1rpx solid #E2E8F0;
}
.detail-list {
	padding: 0 32rpx;
}
.detail-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 32rpx 0;
	border-bottom: 1rpx solid #F1F5F9;
}
.detail-item:last-child {
	border-bottom: none;
}
.label {
	font-size: 28rpx;
	color: #64748B;
}
.value {
	font-size: 28rpx;
	color: #1E293B;
	text-align: right;
	flex: 1;
	margin-left: 32rpx;
}
.value.positive {
	color: #10B981;
	font-weight: 600;
}
.value.negative {
	color: #EF4444;
	font-weight: 600;
}
.value.status {
	background: #E0F2FE;
	color: #0369A1;
	padding: 8rpx 16rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
}

/* 用户信息 */
.user-info {
	display: flex;
	align-items: center;
	padding: 32rpx;
}
.user-avatar {
	width: 96rpx;
	height: 96rpx;
	border-radius: 50%;
	margin-right: 24rpx;
}
.user-details {
	display: flex;
	flex-direction: column;
}
.user-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #1E293B;
	margin-bottom: 8rpx;
}
.user-id {
	font-size: 24rpx;
	color: #64748B;
}

/* 关联内容 */
.relation-content {
	padding-bottom: 32rpx;
}
.action-btn {
	margin: 32rpx;
	margin-top: 0;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	text-align: center;
	padding: 24rpx;
	border-radius: 12rpx;
	font-size: 28rpx;
	font-weight: 600;
}

/* 帮助卡片 */
.help-card {
	background: #FEF3C7;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
	border: 1rpx solid #FDE68A;
}
.help-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #92400E;
	margin-bottom: 16rpx;
}
.help-content {
	display: flex;
	flex-direction: column;
}
.help-content text {
	font-size: 24rpx;
	color: #A16207;
	line-height: 1.6;
	margin-bottom: 8rpx;
}
.help-content text:last-child {
	margin-bottom: 0;
}

/* 订单号复制样式 */
.order-no-value {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	gap: 16rpx;
}
.order-no-text {
	word-break: break-all;
	flex: 1;
	min-width: 0;
}
.copy-btn {
	display: flex;
	align-items: center;
	padding: 6rpx 12rpx;
	background: #F3F4F6;
	border-radius: 6rpx;
	transition: background-color 0.2s;
	flex: 0 0 auto;
}
.copy-btn:active {
	background: #E5E7EB;
}
.copy-icon {
	font-size: 20rpx;
	margin-right: 6rpx;
}
.copy-text {
	font-size: 20rpx;
	color: #6B7280;
}
.fee-amount {
	color: #F59E0B !important;
	font-weight: 600;
}

