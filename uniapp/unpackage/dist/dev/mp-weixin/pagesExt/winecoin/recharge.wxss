
.container {
	background-color: #F8FAFC;
	min-height: 100vh;
}

/* 酒币余额显示 */
.wine-balance {
	padding: 40rpx 30rpx;
	margin: 20rpx;
	border-radius: 24rpx;
	color: #fff;
}
.balance-info {
	text-align: center;
}
.balance-label {
	font-size: 28rpx;
	opacity: 0.9;
	margin-bottom: 16rpx;
}
.balance-amount {
	font-size: 72rpx;
	font-weight: 700;
	margin-bottom: 20rpx;
}
.balance-tips {
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	opacity: 0.8;
}
.balance-tips text:first-child {
	margin-right: 8rpx;
}

/* 充值内容 */
.recharge-content {
	background: #fff;
	margin: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
}
.section-title {
	color: #1E293B;
	font-size: 32rpx;
	font-weight: 600;
	padding: 32rpx 24rpx 16rpx;
}

/* 金额输入 */
.amount-section {
	padding: 0 24rpx;
}
.amount-input-box {
	display: flex;
	align-items: center;
	padding: 32rpx 24rpx;
	background: #F8FAFC;
	border-radius: 12rpx;
	margin-bottom: 16rpx;
}
.currency-symbol {
	color: #64748B;
	font-size: 48rpx;
	margin-right: 16rpx;
}
.amount-input-box input {
	flex: 1;
	color: #1E293B;
	font-weight: 600;
}
.amount-tips {
	color: #64748B;
	font-size: 24rpx;
	padding-bottom: 24rpx;
}

/* 预设金额 */
.preset-amounts {
	padding: 0 24rpx 24rpx;
}
.amount-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}
.amount-item {
	flex: 1;
	min-width: calc(50% - 8rpx);
	padding: 32rpx 16rpx;
	border: 2rpx solid #E2E8F0;
	border-radius: 12rpx;
	text-align: center;
	background: #fff;
	transition: all 0.3s ease;
}
.amount-item.selected {
	border-color: #FFD700;
	background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.1) 100%);
}
.amount-money {
	display: block;
	color: #1E293B;
	font-size: 28rpx;
	font-weight: 600;
	margin-bottom: 8rpx;
}
.amount-give {
	display: block;
	color: #EF4444;
	font-size: 24rpx;
	line-height: 1.4;
}

/* 充值说明 */
.recharge-desc {
	padding: 24rpx;
	border-top: 1rpx solid #F1F5F9;
}
.desc-title {
	color: #1E293B;
	font-size: 28rpx;
	font-weight: 600;
	margin-bottom: 12rpx;
}
.desc-content {
	color: #64748B;
	font-size: 24rpx;
	line-height: 1.6;
}

/* 充值按钮 */
.recharge-actions {
	padding: 40rpx 20rpx;
}
.recharge-btn {
	width: 100%;
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 32rpx;
	font-weight: 600;
	box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
}

