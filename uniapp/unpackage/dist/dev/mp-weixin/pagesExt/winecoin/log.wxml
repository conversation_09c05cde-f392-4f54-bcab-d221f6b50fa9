<view class="container"><block wx:if="{{isload}}"><block><scroll-view class="filter-tabs-container" scroll-x="true" show-scrollbar="false"><view class="filter-tabs"><view class="{{['filter-tab',selectedType==''?'active':'']}}" data-type="all" data-event-opts="{{[['tap',[['selectType',['$event']]]]]}}" bindtap="__e">全部</view><view class="{{['filter-tab',selectedType=='recharge'?'active':'']}}" data-type="recharge" data-event-opts="{{[['tap',[['selectType',['$event']]]]]}}" bindtap="__e">充值</view><view class="{{['filter-tab',selectedType=='withdraw'?'active':'']}}" data-type="withdraw" data-event-opts="{{[['tap',[['selectType',['$event']]]]]}}" bindtap="__e">提现</view><view class="{{['filter-tab',selectedType=='transfer_in'?'active':'']}}" data-type="transfer_in" data-event-opts="{{[['tap',[['selectType',['$event']]]]]}}" bindtap="__e">转入</view><view class="{{['filter-tab',selectedType=='transfer_out'?'active':'']}}" data-type="transfer_out" data-event-opts="{{[['tap',[['selectType',['$event']]]]]}}" bindtap="__e">转出</view><view class="{{['filter-tab',selectedType=='payment'?'active':'']}}" data-type="payment" data-event-opts="{{[['tap',[['selectType',['$event']]]]]}}" bindtap="__e">支付</view><view class="{{['filter-tab',selectedType=='refund'?'active':'']}}" data-type="refund" data-event-opts="{{[['tap',[['selectType',['$event']]]]]}}" bindtap="__e">退款</view><view class="{{['filter-tab',selectedType=='bonus'?'active':'']}}" data-type="bonus" data-event-opts="{{[['tap',[['selectType',['$event']]]]]}}" bindtap="__e">奖励</view><view class="{{['filter-tab',selectedType=='fee'?'active':'']}}" data-type="fee" data-event-opts="{{[['tap',[['selectType',['$event']]]]]}}" bindtap="__e">手续费</view><view class="{{['filter-tab',selectedType=='activity'?'active':'']}}" data-type="activity" data-event-opts="{{[['tap',[['selectType',['$event']]]]]}}" bindtap="__e">活动</view></view></scroll-view><block wx:if="{{$root.g0>0}}"><view class="log-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="log-item" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['viewDetail',['$event']]]]]}}" bindtap="__e"><view class="log-icon"><text class="log-symbol">{{item.m0}}</text></view><view class="log-info"><view class="log-title"><text class="title-text">{{item.$orig.type_desc}}</text><block wx:if="{{item.$orig.other_user}}"><text class="other-user">{{item.m1}}</text></block></view><view class="log-time">{{item.$orig.createtime}}</view><block wx:if="{{item.$orig.remark}}"><view class="log-remark">{{item.$orig.remark}}</view></block></view><view class="log-amount"><text class="{{['amount-value',item.$orig.amount>=0?'positive':'negative']}}">{{''+item.m2+''}}</text><text class="balance-after">{{"余额: "+item.$orig.balance_after}}</text></view></view></block></view></block><block wx:if="{{$root.g1}}"><view class="empty-state"><image class="empty-icon" src="{{pre_url+'/static/img/empty-transaction.png'}}"></image><text class="empty-text">暂无交易记录</text><text class="empty-desc">快去充值酒币吧~</text></view></block><block wx:if="{{$root.g2>0}}"><view class="load-more"><block wx:if="{{!loadingMore&&hasMore}}"><text data-event-opts="{{[['tap',[['loadMore',['$event']]]]]}}" bindtap="__e">点击加载更多</text></block><block wx:if="{{loadingMore}}"><text>加载中...</text></block><block wx:if="{{$root.g3}}"><text>没有更多数据了</text></block></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="356583a3-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="356583a3-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="356583a3-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>