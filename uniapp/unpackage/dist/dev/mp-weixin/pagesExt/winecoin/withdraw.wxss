
.container {
	background: #f5f5f5;
	min-height: 100vh;
	padding: 20rpx;
}
.header {
	margin-bottom: 30rpx;
}
.balance-card {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20rpx;
	padding: 40rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	color: white;
}
.balance-info {
	flex: 1;
}
.balance-label {
	font-size: 28rpx;
	opacity: 0.9;
	display: block;
	margin-bottom: 10rpx;
}
.balance-amount {
	font-size: 48rpx;
	font-weight: bold;
	display: block;
}
.balance-icon {
	font-size: 60rpx;
	opacity: 0.8;
}
.form-section {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}
.form-item {
	margin-bottom: 40rpx;
}
.form-item:last-child {
	margin-bottom: 0;
}
.form-label {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}
.amount-input-wrapper {
	position: relative;
	display: flex;
	align-items: center;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 0 20rpx;
	background: #fafafa;
}
.amount-input {
	flex: 1;
	height: 80rpx;
	font-size: 32rpx;
	color: #333;
}
.currency {
	font-size: 28rpx;
	color: #999;
	margin-left: 10rpx;
}
.amount-tips {
	display: flex;
	justify-content: space-between;
	margin-top: 15rpx;
	font-size: 24rpx;
	color: #999;
}
.withdraw-methods {
	display: flex;
	gap: 20rpx;
}
.method-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 30rpx 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	background: #fafafa;
	transition: all 0.3s;
}
.method-item.active {
	border-color: #667eea;
	background: #f0f4ff;
}
.method-icon {
	font-size: 40rpx;
	margin-bottom: 10rpx;
}
.method-name {
	font-size: 28rpx;
	color: #333;
}
.form-input {
	height: 80rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 0 20rpx;
	font-size: 32rpx;
	background: #fafafa;
}
.fee-section {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}
.fee-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}
.fee-item:last-child {
	margin-bottom: 0;
}
.fee-item.total {
	border-top: 1rpx solid #e0e0e0;
	padding-top: 20rpx;
	font-weight: 600;
}
.fee-label {
	font-size: 30rpx;
	color: #666;
}
.fee-value {
	font-size: 30rpx;
	color: #333;
}
.tips-section {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}
.tips-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}
.tips-content {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}
.tips-content text {
	display: block;
	margin-bottom: 10rpx;
}
.submit-section {
	padding: 30rpx 0;
}
.submit-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: center;
}
.submit-btn.disabled {
	background: #ccc;
	color: #999;
}
.loading-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}
.loading-content {
	background: white;
	padding: 40rpx 60rpx;
	border-radius: 20rpx;
	font-size: 30rpx;
	color: #333;
}

