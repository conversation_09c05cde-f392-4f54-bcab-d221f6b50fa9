
.container {
	background-color: #F8FAFC;
	min-height: 100vh;
}

/* 商家信息 */
.merchant-info {
	background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
	padding: 40rpx 30rpx;
	margin-bottom: 20rpx;
}
.merchant-header {
	display: flex;
	align-items: center;
}
.merchant-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	margin-right: 30rpx;
	border: 4rpx solid rgba(255, 255, 255, 0.3);
}
.merchant-details {
	flex: 1;
}
.merchant-name {
	color: #fff;
	font-size: 36rpx;
	font-weight: 600;
	margin-bottom: 12rpx;
}
.merchant-balance {
	color: rgba(255, 255, 255, 0.9);
	font-size: 28rpx;
}

/* 扫码区域 */
.scan-section {
	background: #fff;
	margin: 20rpx;
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
	text-align: center;
}
.scan-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #1E293B;
	margin-bottom: 40rpx;
}
.scan-container {
	margin-bottom: 30rpx;
}
.scan-frame {
	width: 400rpx;
	height: 400rpx;
	border: 4rpx dashed #FFD700;
	border-radius: 24rpx;
	margin: 0 auto;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: #FFFBF0;
	position: relative;
	transition: all 0.3s ease;
}
.scan-frame:active {
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
	background: #FFF8E1;
}
.scan-icon {
	font-size: 100rpx;
	margin-bottom: 20rpx;
	opacity: 0.7;
}
.scan-text {
	font-size: 28rpx;
	color: #64748B;
}
.scan-tips {
	font-size: 24rpx;
	color: #94A3B8;
}

/* 手动输入区域 */
.manual-section {
	background: #fff;
	margin: 20rpx;
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
}
.manual-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #1E293B;
	margin-bottom: 30rpx;
}
.input-group {
	display: flex;
	gap: 20rpx;
	align-items: center;
}
.payment-code-input {
	flex: 1;
	height: 88rpx;
	border: 2rpx solid #E2E8F0;
	border-radius: 16rpx;
	padding: 0 24rpx;
	font-size: 28rpx;
	background: #F8FAFC;
}
.payment-code-input:focus {
	border-color: #FFD700;
	background: #fff;
}
.confirm-btn {
	width: 160rpx;
	height: 88rpx;
	background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
	color: #fff;
	border: none;
	border-radius: 16rpx;
	font-size: 28rpx;
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: center;
}
.confirm-btn:disabled {
	background: #E2E8F0;
	color: #94A3B8;
}

/* 快捷金额 */
.quick-amounts {
	background: #fff;
	margin: 20rpx;
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
}
.amounts-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #1E293B;
	margin-bottom: 30rpx;
}
.amounts-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20rpx;
}
.amount-item {
	height: 80rpx;
	border: 2rpx solid #E2E8F0;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	color: #64748B;
	background: #F8FAFC;
	transition: all 0.3s ease;
}
.amount-item:active {
	border-color: #FFD700;
	background: #FFFBF0;
	color: #F59E0B;
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
}

/* 收款记录 */
.collect-records {
	background: #fff;
	margin: 20rpx;
	border-radius: 24rpx;
	padding: 0;
	overflow: hidden;
}
.record-item {
	display: flex;
	align-items: center;
	padding: 40rpx 30rpx;
	border-bottom: 1rpx solid #F1F5F9;
}
.record-item:last-child {
	border-bottom: none;
}
.record-icon {
	font-size: 44rpx;
	margin-right: 24rpx;
	opacity: 0.7;
}
.record-text {
	flex: 1;
	font-size: 32rpx;
	color: #1E293B;
	font-weight: 500;
}
.record-arrow {
	font-size: 24rpx;
	color: #CBD5E1;
}
.record-item:active {
	background: #F8FAFC;
}

