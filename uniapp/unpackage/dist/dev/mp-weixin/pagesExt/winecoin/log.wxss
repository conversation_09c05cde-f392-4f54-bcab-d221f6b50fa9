
.container {
	background-color: #F8FAFC;
	min-height: 100vh;
}

/* 筛选选项 */
.filter-tabs-container {
	margin: 20rpx;
	border-radius: 16rpx;
	background: #fff;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.filter-tabs {
	display: flex;
	padding: 20rpx;
	white-space: nowrap;
	min-width: -webkit-fit-content;
	min-width: fit-content;
}
.filter-tab {
	flex: none;
	min-width: 120rpx;
	text-align: center;
	padding: 16rpx 24rpx;
	margin-right: 16rpx;
	border-radius: 12rpx;
	font-size: 26rpx;
	color: #64748B;
	transition: all 0.3s ease;
	white-space: nowrap;
}
.filter-tab:last-child {
	margin-right: 0;
}
.filter-tab.active {
	background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
	color: #fff;
	font-weight: 600;
}

/* 流水列表 */
.log-list {
	background: #fff;
	margin: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
}
.log-item {
	display: flex;
	align-items: center;
	padding: 32rpx 24rpx;
	border-bottom: 1rpx solid #F1F5F9;
	transition: all 0.3s ease;
	position: relative;
}
.log-item:last-child {
	border-bottom: none;
}
.log-item:active {
	background: #F8FAFC;
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
}
.log-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	position: relative;
}
.log-icon::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	border-radius: 50%;
	background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.1) 100%);
	opacity: 0;
	transition: opacity 0.3s ease;
}
.log-item:active .log-icon::before {
	opacity: 1;
}
.log-symbol {
	font-size: 32rpx;
	color: #64748B;
	font-weight: bold;
	z-index: 1;
}
.log-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}
.log-title {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}
.title-text {
	color: #1E293B;
	font-size: 28rpx;
	font-weight: 600;
	margin-right: 16rpx;
}
.other-user {
	color: #64748B;
	font-size: 24rpx;
	background: #F1F5F9;
	padding: 4rpx 12rpx;
	border-radius: 8rpx;
}
.log-time {
	color: #64748B;
	font-size: 24rpx;
	margin-bottom: 4rpx;
}
.log-remark {
	color: #94A3B8;
	font-size: 22rpx;
	line-height: 1.4;
}
.log-amount {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}
.amount-value {
	font-size: 32rpx;
	font-weight: 700;
	margin-bottom: 8rpx;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}
.amount-value.positive {
	color: #10B981;
	background: linear-gradient(135deg, #10B981, #059669);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}
.amount-value.negative {
	color: #EF4444;
	background: linear-gradient(135deg, #EF4444, #DC2626);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}
.balance-after {
	color: #64748B;
	font-size: 22rpx;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 120rpx 40rpx;
	text-align: center;
}
.empty-icon {
	width: 160rpx;
	height: 160rpx;
	margin-bottom: 32rpx;
	opacity: 0.6;
}
.empty-text {
	color: #64748B;
	font-size: 32rpx;
	font-weight: 600;
	margin-bottom: 16rpx;
}
.empty-desc {
	color: #94A3B8;
	font-size: 24rpx;
}

/* 加载更多 */
.load-more {
	text-align: center;
	padding: 40rpx;
	color: #64748B;
	font-size: 26rpx;
}

