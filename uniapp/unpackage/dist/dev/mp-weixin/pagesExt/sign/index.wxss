
page{background:#f4f4f4}
.qd_head{width: 100%;/* height:940rpx; */position: relative;}
.qdbg{width: 100%;height:940rpx;}
.myscore{position:absolute;top:60rpx;width:100%;display:flex;color:#fff;flex-direction:column;align-items:center;z-index:2}
.myscore .f1{font-size:56rpx;font-weight:bold}
.myscore .f2{font-size:32rpx}
.signlog{position:absolute;top:50rpx;right:20rpx;color:#fff;font-size:28rpx;z-index:2}
.pmtext{position:absolute;top:100rpx;right:20rpx;color:#fff;font-size:28rpx;z-index:2}
.canyutext{position:absolute;top:150rpx;right:20rpx;color:#fff;font-size:28rpx;z-index:2}
.bonus_text{position:absolute;top:500rpx;width:100%;display:flex;color:#fff;flex-direction:column;align-items:center;z-index:2;font-size: 40rpx;}
.bonus_text .title1{ font-size: 30rpx;}
.bonus_text .t2{font-size:56rpx;font-weight:bold}
.qd_head .signbtn{position:absolute;top:740rpx;width:100%;display:flex;flex-direction:column;align-items:center;z-index:2}
.qd_head .signbtn .btn{width:440rpx;height:80rpx;border-radius:40rpx;font-size:32rpx;font-weight:bold;color:#fff}
.qd_head .signbtn .btn2{width:440rpx;height:80rpx;background:#FCB0B0;border-radius:40rpx;font-size:32rpx;font-weight:bold;color:#fff}
.qd_head .signbtn .signtip{color:#999999;margin-top:16rpx}
.btn-a { text-align: center;margin-top: 18rpx;}
.qd_head2 { padding-bottom: 20rpx;background-position: center;background-repeat: no-repeat;background-size:cover;}
.qd_head2 .calendar { width: 96%;margin: 0 2%;}
.qd_head2 .signbtn {/* position:initial;*/top: 136rpx;}
.qd_head2 .signbtn .signtip {color: #fff;}
.qd_guize{width:100%;margin:0;padding-bottom:20rpx}
.qd_guize .gztitle{width:100%;text-align:center;font-size:32rpx;color:#656565;font-weight:bold;height:100rpx;line-height:100rpx}
.guize_txt{box-sizing: border-box;padding:0 30rpx;line-height:42rpx;}
.paiming{ width:94%;margin:0 3%;background:#fff;border-radius:10px;padding:20rpx 20rpx;}
.paiming .item{ line-height: 80rpx;border-bottom: 1px dashed #eee;}
.paiming .item:last-child{border:0}
.paiming .item .f1{flex:1;display:flex;flex-direction:column}
.paiming .item .f1 .t1{color:#000000;font-size:30rpx;word-break:break-all;overflow:hidden;text-overflow:ellipsis;}
.paiming .item .f1 .t2{color:#666666}
.paiming .item .f1 .t3{color:#666666}
.paiming .item .f2{ flex:1;text-align:right;font-size:30rpx;}
.paiming .item .f2 .t1{color:#03bc01}
.paiming .item .f2 .t2{color:#000000}
.modal{ position: fixed; width: 100%; height: 100%; top:0; background: rgba(0, 0, 0, 0.5); z-index: 200;}
.modal .signbox{ position: absolute; background-color: #fff; top:20%; left:10% ;width:80%}
.modal .title{ height: 80rpx; line-height: 80rpx; text-align: center; font-weight: bold; border-bottom: 1rpx solid #f5f5f5; font-size: 32rpx;
}
.modal .f1{ height: 100rpx; line-height:100rpx; margin-left: 40rpx; font-size: 28rpx; margin-bottom: 40rpx;}
.modal .f1 .t1{ color:#F21A2E
}
.modal .f1 .t2{ color: #F21A2E; font-size: 36rpx;}
.modal .btn{ display: flex; margin: 0rpx 20rpx 30rpx;}
.modal .btn .btn-cancel{  background-color: #F2F2F2; width: 40%; border-radius: 10rpx;height:60rpx; font-size: 26rpx;}
.modal .btn .confirm{ width: 40%; border-radius: 10rpx; color: #fff;height: 60rpx; font-size: 26rpx;}
.itembox{ display: flex; justify-content: center; height: 100rpx; line-height: 100rpx;
}
.itembox .item .t3{ color: #F21A2E;
}
.itembox .item .t2{ margin-right: 20rpx; font-size: 36rpx; color: #F21A2E;font-weight: bold;
}
.form-uploadbtn{position:relative;height:180rpx;width:180rpx;margin-right: 16rpx;margin-bottom:10rpx;}


