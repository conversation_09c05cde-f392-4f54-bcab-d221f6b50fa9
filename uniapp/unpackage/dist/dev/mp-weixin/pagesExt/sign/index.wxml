<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{style==1}}"><view class="qd_head"><block wx:if="{{signset.bgpic}}"><block><image class="qdbg" src="{{signset.bgpic}}"></image></block></block><block wx:else><block><image class="qdbg" src="{{pre_url+'/static/img/sign-bg.png'}}"></image></block></block><view class="myscore"><view class="f1">{{userinfo.score}}</view><view class="f2">{{$root.m0}}</view></view><view class="signlog" data-url="signrecord" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">签到记录</view><block wx:if="{{signset.ispay==1}}"><block><view class="pmtext" data-url="/pagesB/sign/pmlist" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">奖励排名</view><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="canyutext" catchtap="__e">{{"参与人数："+cynum}}</view><block wx:if="{{signset.isshowbonus==1}}"><view class="bonus_text"><view class="title"><text style="color:#fff;">￥</text><text class="t2">{{bonusprice}}</text></view><view class="title1">奖金池金额</view></view></block></block></block><block wx:if="{{!hassign}}"><view class="signbtn"><button data-event-opts="{{[['tap',[['signinNew',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m1)+';'}}" bindtap="__e">立即签到</button><picker style="padding-top:5px;" mode="date" end="{{endDate}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{is_forget==1}}"><button class="btn" style="{{'background:'+($root.m2)+';'}}" mode="date">补签</button></block></picker></view></block><block wx:else><view class="signbtn" style="{{(!is_forget?'top: 740rpx':'top: 700rpx')}}"><button class="btn2">今日已签到</button><picker style="padding-top:5px;" mode="date" end="{{endDate}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{is_forget==1}}"><button class="btn" style="{{'background:'+($root.m3)+';'}}" mode="date">补签</button></block></picker><view class="signtip">{{"已连续签到"+userinfo.signtimeslx+"天"}}</view></view></block></view></block><block wx:if="{{style==2}}"><view class="qd_head qd_head2" style="{{('background-image:url('+signset.bgpic+');')}}"><view class="signlog" data-url="signrecord" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">签到记录</view><block wx:if="{{!hassign}}"><view class="signbtn"><button data-event-opts="{{[['tap',[['signinNew',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m4)+';'}}" bindtap="__e">立即签到</button><picker style="padding-top:5px;" mode="date" end="{{endDate}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{is_forget==1}}"><button class="btn" style="{{'background:'+($root.m5)+';'}}" mode="date">补签</button></block></picker><view class="signtip">{{"当前共"+userinfo.score+$root.m6}}</view></view></block><block wx:else><view class="signbtn"><button class="btn2">今日已签到</button><picker style="padding-top:5px;" mode="date" end="{{endDate}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{is_forget==1}}"><button class="btn" style="{{'background:'+($root.m7)+';'}}" mode="date">补签</button></block></picker><view class="signtip">{{"已连续签到"+userinfo.signtimeslx+"天，共"+userinfo.score+$root.m8}}</view></view></block><view class="calendar" style="{{(!is_forget?'padding-top: 300rpx':'padding-top: 360rpx')}}"><uni-calendar vue-id="84a8c1dc-1" insert="{{true}}" lunar="{{false}}" start-date="{{start_date}}" end-date="{{end_date}}" selected="{{selectedDate}}" showMonth="{{false}}" backColor="{{$root.m9}}" fontColor="#fff" bind:__l="__l"></uni-calendar></view></view></block><block wx:if="{{$root.g0}}"><view class="qd_guize"><view class="gztitle">— 签到排名 —</view><view class="paiming"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item flex"><view class="f1"><text class="t1">{{item.nickname}}</text></view><view class="f2"><text class="t2">连续签到</text><text class="t1">{{item.signtimeslx}}</text><text class="t2">天</text></view></view></block></view><block wx:if="{{$root.g1}}"><view data-event-opts="{{[['tap',[['getPaiming',['$event']]]]]}}" class="btn-a" style="{{'color:'+($root.m10)+';'}}" bindtap="__e">查看更多</view></block><block wx:if="{{nomore}}"><nomore vue-id="84a8c1dc-2" bind:__l="__l"></nomore></block></view></block><view class="qd_guize"><view class="gztitle">— 签到规则 —</view><view class="guize_txt"><parse vue-id="84a8c1dc-3" content="{{signset.guize}}" bind:__l="__l"></parse></view></view><block wx:if="{{showpay}}"><view class="modal"><view class="signbox"><view class="title">提示信息</view><view class="f1">需支付金额：<text class="t1">￥</text><text class="t2">{{signset.payprice}}</text></view><view class="btn"><button data-event-opts="{{[['tap',[['cancel',['$event']]]]]}}" class="btn-cancel" bindtap="__e">取消</button><button data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" class="confirm" style="{{('background:linear-gradient(90deg,'+$root.m11+' 0%,rgba('+$root.m12+',0.8) 100%)')}}" catchtap="__e">立即支付</button></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="84a8c1dc-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="84a8c1dc-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="84a8c1dc-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>