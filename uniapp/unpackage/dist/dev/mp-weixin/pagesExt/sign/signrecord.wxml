<view class="container"><view class="content"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.remark}}</text><block wx:if="{{item.status_text}}"><text class="t2">{{item.status_text}}</text></block><text class="t2">{{item.signdate}}</text></view><view class="f2"><text class="t1">{{"+"+item.score}}</text></view></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="703c786a-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="703c786a-2" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="703c786a-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="703c786a-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="703c786a-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>