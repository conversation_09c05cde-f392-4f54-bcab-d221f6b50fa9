<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="214bdabf-1" itemdata="{{['全部','通过','待审核','驳回']}}" itemst="{{['all','1','0','-1']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="content" id="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item" data-url="{{'record?id='+item.id+'&pid='+item.pid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><text class="t1">{{item.name}}</text><text class="t2">{{"提交时间："+item.createtime}}</text></view><view class="f2"><block wx:if="{{item.status==0}}"><text class="t1" style="color:#333;">待审核</text></block><block wx:if="{{item.status==1}}"><text class="t1" style="color:green;">已通过</text></block><block wx:if="{{item.status==2}}"><text class="t1" style="color:red;">已驳回</text></block></view></view></block></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="214bdabf-2" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="214bdabf-3" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="214bdabf-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="214bdabf-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="214bdabf-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>