
radio{-webkit-transform: scale(0.6);transform: scale(0.6);}
checkbox{-webkit-transform: scale(0.6);transform: scale(0.6);}
.apply_box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}
.apply_title { background: #fff}
.apply_title .qr_goback{ width:18rpx;height:32rpx; margin-left:24rpx;     margin-top: 34rpx;}
.apply_title .qr_title{ font-size: 36rpx; color: #242424;   font-weight:bold;margin: 0 auto; line-height: 100rpx;}
.apply_item{ line-height: 100rpx; display: flex;justify-content: center;border-bottom:1px solid #eee
}
.apply_item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center
}
.apply_item .upload_pic image{ width: 32rpx;height: 32rpx;
}
.set-btn{width: 90%;margin:0 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}
.uploadbtn{position:relative;height:200rpx;width:200rpx}
.topcontent{padding: 20rpx 0;margin-bottom:20rpx; background: #fff;isplay:flex;border-radius:16rpx;position:relative;z-index:2;}
.topcontent .logo{width:160rpx;height:160rpx;border:2px solid rgba(255,255,255,0.5);border-radius:50%;}
.topcontent .logo .img{width:100%;height:100%;border-radius:50%;}
.topcontent .title {color:#222222;font-size:36rpx;font-weight:bold;margin-top:12rpx}
.topcontent .desc {display:flex;align-items:center;margin-left: 10rpx;flex: 1;}
.topcontent .desc .f1{ margin:20rpx 0; font-size: 24rpx;color:#FC5648;display:flex;align-items:center}
.topcontent .desc .f1 .img{ width:24rpx;height:24rpx;margin-right:10rpx;}
.topcontent .desc .f2{ margin:10rpx 0;font-size: 24rpx;color:#666;}

