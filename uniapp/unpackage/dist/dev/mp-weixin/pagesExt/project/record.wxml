<view><block wx:if="{{isload}}"><block><block wx:if="{{info.id&&info.status==-1}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;">{{"审核不通过："+info.reason+"，请修改后再提交"}}</view></block><block wx:if="{{info.id&&info.status==0}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;">您已提交申请，请等待审核</view></block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><view>邀请二维码<text style="color:red;">*</text></view></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{pic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0==0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pic" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="pic" maxlength="-1" value="{{$root.g1}}"/><view class="apply_item"><view>联系手机号</view><view class="flex-y-center"><input type="text" name="tel" placeholder="请填写手机号码" value="{{info.tel}}"/></view></view></view><view style="padding:30rpx 0;"><block wx:if="{{!info.id||info.status==-1}}"><button class="set-btn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">提交申请</button></block></view><block wx:if="{{info.status==1}}"><view style="text-align:center;padding:0 30rpx;">你的项目邀请二维码已审核固定<view class="_br"></view>如要修改请联系群管理</view></block></form></block></block><block wx:if="{{loading}}"><loading vue-id="6aa41f6b-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="6aa41f6b-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="6aa41f6b-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>