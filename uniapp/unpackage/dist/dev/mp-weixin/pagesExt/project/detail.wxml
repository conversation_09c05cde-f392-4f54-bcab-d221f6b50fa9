<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="apply_box"><view class="topcontent flex"><view class="logo"><image class="img" src="{{info.logo}}"></image></view><view class="desc"><view class="f2">{{info.content}}</view></view></view><view class="apply_item" style="border-bottom:0;"><text>邀请二维码</text></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;justify-content:center;"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.g0>0}}"><view class="layui-imgbox"><view class="layui-imgbox-img"><image src="{{item.$orig}}" data-url="{{item.$orig}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></block><block wx:if="{{$root.g1==0}}"><view>{{info.tips}}</view></block></view><input type="text" hidden="true" name="pic" maxlength="-1" value="{{$root.g2}}"/></view><view style="padding:30rpx 0;"><button class="set-btn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" data-url="{{'record?pid='+info.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">上传我的邀请码</button></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="bc28192a-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="bc28192a-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="bc28192a-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>