
radio{-webkit-transform: scale(0.6);transform: scale(0.6);}
checkbox{-webkit-transform: scale(0.6);transform: scale(0.6);}
.apply_box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}
.apply_title { background: #fff}
.apply_title .qr_goback{ width:18rpx;height:32rpx; margin-left:24rpx;     margin-top: 34rpx;}
.apply_title .qr_title{ font-size: 36rpx; color: #242424;   font-weight:bold;margin: 0 auto; line-height: 100rpx;}
.apply_item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee
}
.apply_box .apply_item:last-child{ border:none}
.apply_item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}
.apply_item input::-webkit-input-placeholder{ color:#999999}
.apply_item input::placeholder{ color:#999999}
.apply_item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}
.apply_item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center
}
.apply_item .upload_pic image{ width: 32rpx;height: 32rpx;
}
.set-btn{width: 90%;margin:0 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}
.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.layui-imgbox-img>image{max-width:100%;}
.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.uploadbtn{position:relative;height:200rpx;width:200rpx}
/* 自定义字段显示 */
/* .dp-form-item{width: 100%;display:flex;align-items: center;border-bottom:1px solid #F0F3F6;padding: 10rpx 0;} */
/* .dp-form-item:last-child{border:0} */
.dp-form-item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee
}
.dp-form-item:last-child{ border:none}
.dp-form-item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}
.dp-form-item input::-webkit-input-placeholder{ color:#999999}
.dp-form-item input::placeholder{ color:#999999}
/* .dp-form-item .label{line-height: 50rpx;width:156rpx;margin-right: 10px;flex-shrink:0;text-align: right;color: #666666;font-size: 28rpx;} */
/* .dp-form-item .input{height: 88rpx;line-height: 88rpx;overflow: hidden;flex:1;border-radius:2px;} */
.dp-form-item .textarea{height:180rpx;line-height:40rpx;overflow: hidden;flex:1;border:none;border-radius:2px;padding:8rpx}
.dp-form-item .radio{height: 88rpx;line-height: 88rpx;display:flex;align-items:center}
.dp-form-item .radio2{display:flex;align-items:center;}
.dp-form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}
.dp-form-item .checkbox{height: 88rpx;line-height: 88rpx;display:flex;align-items:center}
.dp-form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}
.dp-form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}
.dp-form-item .layui-form-switch{}
.dp-form-item .picker{height: 88rpx;line-height:88rpx;flex:1;}
.dp-form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.dp-form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-20rpx;top:-25rpx;color:#999;font-size:32rpx;}
.dp-form-imgbox-close .image{width:100%;height:100%}
.dp-form-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px; margin:2px; border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.dp-form-imgbox-img>.image{max-width:100%;}
.dp-form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.dp-form-uploadbtn{position:relative;height:200rpx;width:200rpx}
/* 套餐选择样式 */
.package-list{
	padding: 20rpx 0;
}
.package-item{
	border: 2rpx solid #eee;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	transition: all 0.3s;
	overflow: hidden;
	word-break: break-all;
	word-wrap: break-word;
}
.package-item.active{
	border-color: #07c160;
	background: #f0faf5;
}
.package-header{
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10rpx;
}
.package-name{
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.package-price{
	font-size: 28rpx;
}
.free-tag{
	color: #07c160;
	font-weight: bold;
}
.price-text{
	color: #ff6600;
	font-weight: bold;
}
.package-info{
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}
.duration{
	font-size: 26rpx;
	color: #666;
	margin-right: 20rpx;
}
.tag{
	font-size: 22rpx;
	padding: 4rpx 12rpx;
	border-radius: 20rpx;
	margin-right: 10rpx;
}
.tag.recommend{
	background: #fff3e0;
	color: #ff6600;
}
.tag.popular{
	background: #e8f5e9;
	color: #4caf50;
}
.package-features{
	margin-top: 15rpx;
	padding-top: 15rpx;
	border-top: 1rpx solid #f0f0f0;
}
.feature-item{
	font-size: 26rpx;
	color: #666;
	margin-bottom: 8rpx;
	display: flex;
	align-items: flex-start;
	word-break: break-all;
	word-wrap: break-word;
}
.feature-icon{
	color: #07c160;
	margin-right: 10rpx;
	font-weight: bold;
	flex-shrink: 0;
}
.package-desc{
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
	line-height: 1.5;
}

