<view><block wx:if="{{isload}}"><block><block wx:if="{{info.id&&info.status==2}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;">{{"审核不通过："+info.reason+"，请修改后再提交"}}</view></block><block wx:if="{{info.id&&info.status==0}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;">您已提交申请，请等待审核</view></block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="apply_box"><view class="apply_item"><view>联系人姓名<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="linkman" placeholder="请填写姓名" value="{{info.linkman}}"/></view></view><view class="apply_item"><view>联系人电话<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="linktel" placeholder="请填写手机号码" value="{{info.linktel}}"/></view></view></view><view class="apply_box"><view class="apply_item"><view>商家名称<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="name" placeholder="请输入商家名称" value="{{info.name}}"/></view></view><view class="apply_item"><view>商家描述<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="desc" placeholder="请输入商家描述" value="{{info.desc}}"/></view></view><view class="apply_item"><view>主营类目<text style="color:red;">*</text></view><view><picker value="{{cindex}}" range="{{cateArr}}" data-event-opts="{{[['change',[['cateChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{cateArr[cindex]}}</view></picker></view></view><view class="apply_item"><view>店铺坐标<text style="color:red;">*</text></view><view data-event-opts="{{[['tap',[['locationSelect',['$event']]]]]}}" class="flex-y-center" bindtap="__e"><input type="text" readonly="{{true}}" placeholder="请选择坐标" name="zuobiao" value="{{latitude?latitude+','+longitude:''}}"/></view></view><view class="apply_item"><view>店铺地址<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="address" placeholder="请输入商家详细地址" value="{{address}}"/></view></view><input style="position:fixed;left:-200%;" type="text" hidden="true" name="latitude" value="{{latitude}}"/><input style="position:fixed;left:-200%;" type="text" hidden="true" name="longitude" value="{{longitude}}"/><view class="apply_item"><view>客服电话<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="tel" placeholder="请填写客服电话" value="{{info.tel}}"/></view></view><block wx:if="{{queue_free_set.show_rate_back==1}}"><view class="apply_item"><view>{{$root.m0+$root.m1+"(%)"}}</view><view class="flex-y-center"><input type="text" name="rate_back" placeholder="请填写比例" value="{{queue_free_set.rate_back}}"/></view></view></block><block wx:if="{{active_coin==1}}"><view><view class="apply_item"><view>让利比例(%)</view><view class="flex-y-center"><input type="text" name="activecoin_ratio" placeholder="请填写比例" value="{{info.activecoin_ratio}}"/></view></view><view class="apply_item"><view>让利到消费者比例(%)</view><view class="flex-y-center"><input type="text" name="business_activecoin_ratio" placeholder="请填写比例" value="{{info.business_activecoin_ratio}}"/></view></view><view class="apply_item"><view>让利到商家比例(%)</view><view class="flex-y-center"><input type="text" name="member_activecoin_ratio" placeholder="请填写比例" value="{{info.member_activecoin_ratio}}"/></view></view></view></block><view class="apply_item" style="line-height:50rpx;"><textarea name="content" placeholder="请输入商家简介" value="{{info.content}}"></textarea></view></view><block wx:if="{{$root.g0}}"><view class="apply_box"><view class="apply_item" style="border-bottom:0;padding-bottom:10rpx;"><view>选择套餐<text style="color:red;">*</text></view></view><view class="package-list"><block wx:for="{{$root.l0}}" wx:for-item="pkg" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['selectPackage',['$0'],[[['packages','id',pkg.$orig.id]]]]]]]}}" class="{{['package-item',(selectedPackageId==pkg.$orig.id)?'active':'']}}" bindtap="__e"><view class="package-header"><view class="package-name">{{pkg.$orig.name}}</view><view class="package-price"><block wx:if="{{pkg.$orig.price==0}}"><text class="free-tag">免费</text></block><block wx:else><text class="price-text">{{"￥"+pkg.$orig.price}}</text></block></view></view><view class="package-info"><view class="duration">{{"有效期："+(pkg.$orig.duration==0?'永久':pkg.$orig.duration+'天')}}</view><block wx:if="{{pkg.$orig.is_recommend==1}}"><view class="tag recommend">推荐</view></block><block wx:if="{{pkg.$orig.is_popular==1}}"><view class="tag popular">热门</view></block></view><block wx:if="{{pkg.g1}}"><view class="package-features"><block wx:for="{{pkg.$orig.features_array}}" wx:for-item="feature" wx:for-index="fidx" wx:key="fidx"><view class="feature-item"><text class="feature-icon">✓</text>{{''+feature+''}}</view></block></view></block><block wx:if="{{pkg.$orig.desc}}"><view class="package-desc">{{pkg.$orig.desc}}</view></block></view></block></view></view></block><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><view>商家logo<text style="color:red;">*</text></view></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{pic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g2==0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pic" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="pic" maxlength="-1" value="{{$root.g3}}"/></view><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><view>{{"商家照片("+min_bpic_num+"-"+max_bpic_num+"张)"}}<text style="color:red;">*</text></view></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pics" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g4<max_bpic_num}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pics" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="pics" maxlength="-1" value="{{$root.g5}}"/></view><block wx:if="{{!bset.nearby}}"><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><view>证明材料<text style="color:red;"></text></view></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{zhengming}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="zhengming" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="zhengming" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></view><input type="text" hidden="true" name="zhengming" maxlength="-1" value="{{$root.g6}}"/></view></block><block wx:if="{{show_custom_field}}"><block><block wx:if="{{show_custom_field}}"><view class="apply_box custom_field"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="dp-form-item"><view>{{item.$orig.val1}}<block wx:if="{{item.$orig.val3==1}}"><text style="color:red;">*</text></block></view><view class="flex-y-center"><block wx:if="{{item.$orig.key=='input'||item.$orig.key=='realname'||item.$orig.key=='usercard'}}"><block><block wx:if="{{item.$orig.val5}}"><text style="margin-right:10rpx;">{{item.$orig.val5}}</text></block><input class="input" type="{{item.$orig.input_type}}" name="{{'form'+idx}}" placeholder="{{item.$orig.val2}}" placeholder-style="font-size:30rpx;color:#B2B5BE" data-formidx="{{'form'+idx}}" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{editorFormdata[idx]}}" bindinput="__e"/></block></block><block wx:if="{{item.$orig.key=='textarea'}}"><block><textarea class="textarea" name="{{'form'+idx}}" placeholder="{{item.$orig.val2}}" placeholder-style="font-size:30rpx;color:#B2B5BE" data-formidx="{{'form'+idx}}" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{editorFormdata[idx]}}" bindinput="__e"></textarea></block></block><block wx:if="{{item.$orig.key=='radio'||item.$orig.key=='sex'}}"><block><radio-group class="flex" style="flex-wrap:wrap;" name="{{'form'+idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['setfield',['$event']]]]]}}" bindchange="__e"><block wx:for="{{item.$orig.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><radio class="radio" style="transform:scale(0.8);" value="{{item1}}" checked="{{editorFormdata[idx]&&editorFormdata[idx]==item1?true:false}}"></radio>{{item1+''}}</label></block></radio-group></block></block><block wx:if="{{item.$orig.key=='checkbox'}}"><block><checkbox-group class="flex" style="flex-wrap:wrap;" name="{{'form'+idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['setfield',['$event']]]]]}}" bindchange="__e"><block wx:for="{{item.l1}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><checkbox class="checkbox" style="transform:scale(0.8);" value="{{item1.$orig}}" checked="{{item1.m2?true:false}}"></checkbox>{{item1.$orig+''}}</label></block></checkbox-group></block></block><block wx:if="{{item.$orig.key=='selector'}}"><block><picker class="picker" mode="selector" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}" range="{{item.$orig.val2}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]||editorFormdata[idx]===0}}"><view>{{''+item.$orig.val2[editorFormdata[idx]]}}</view></block><block wx:else><view style="color:#b2b5be;">请选择</view></block></picker></block></block><block wx:if="{{item.$orig.key=='time'}}"><block><picker class="picker" mode="time" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}" start="{{item.$orig.val2[0]}}" end="{{item.$orig.val2[1]}}" range="{{item.$orig.val2}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view>{{editorFormdata[idx]}}</view></block><block wx:else><view style="color:#b2b5be;">请选择</view></block></picker></block></block><block wx:if="{{item.$orig.key=='date'||item.$orig.key=='birthday'}}"><block><picker class="picker" mode="date" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}" start="{{item.$orig.val2[0]}}" end="{{item.$orig.val2[1]}}" range="{{item.$orig.val2}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view>{{editorFormdata[idx]}}</view></block><block wx:else><view style="color:#b2b5be;">请选择</view></block></picker></block></block><block wx:if="{{item.$orig.key=='region'}}"><block><uni-data-picker vue-id="{{'1ff03bda-1-'+idx}}" localdata="{{items}}" popup-title="请选择省市区" placeholder="{{editorFormdata[idx]||'请选择省市区'}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['^change',[['onchange']]]]}}" bind:change="__e" bind:__l="__l"></uni-data-picker><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{regiondata?regiondata:editorFormdata[idx]}}"/></block></block><block wx:if="{{item.$orig.key=='upload'}}"><block><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}"/><view class="flex" style="flex-wrap:wrap;"><block wx:if="{{editorFormdata[idx]}}"><view class="dp-form-imgbox"><view class="dp-form-imgbox-close" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['removeimgzdy',['$event']]]]]}}" bindtap="__e"><image class="image" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="dp-form-imgbox-img"><image class="image" src="{{editorFormdata[idx]}}" data-url="{{editorFormdata[idx]}}" mode="widthFix" data-idx="{{idx}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:else><view class="dp-form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></block></view></block></block><block wx:if="{{item.$orig.key=='upload_file'}}"><block><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}"/><view class="flex" style="flex-wrap:wrap;"><block wx:if="{{editorFormdata[idx]}}"><view class="dp-form-imgbox"><view class="dp-form-imgbox-close" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['removeimgzdy',['$event']]]]]}}" bindtap="__e"><image class="image" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="dp-form-imgbox-img">已上传</view></view></block><block wx:else><view class="dp-form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['editorChooseFile',['$event']]]]]}}" bindtap="__e"></view></block></view></block></block></view></view></block></view></block><view style="display:none;">{{test}}</view></block></block><view class="apply_box"><view><view class="apply_item" style="border:none;"><view>登录账号<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="un" placeholder="请设置登录账号" autocomplete="off" value="{{info.un}}"/></view></view><view style="border-bottom:1px solid #eee;color:#999;margin-top:-20rpx;padding-bottom:10rpx;font-size:24rpx;"><block wx:if="{{bset.nearby}}"><text>如需开通商城功能请联系后台</text></block></view></view><view class="apply_item"><view>登录密码<text style="color:red;">*</text></view><view class="flex-y-center"><input type="password" name="pwd" placeholder="请设置登录密码" autocomplete="off" value="{{info.pwd}}"/></view></view><view class="apply_item"><view>确认密码<text style="color:red;">*</text></view><view class="flex-y-center"><input type="password" name="repwd" placeholder="请再次设置登录密码" value="{{info.repwd}}"/></view></view></view><block wx:if="{{bset.xieyi_show==1}}"><block><block wx:if="{{!info.id||info.status==2}}"><view class="flex-y-center" style="margin-left:20rpx;color:#999;"><checkbox-group data-event-opts="{{[['change',[['isagreeChange',['$event']]]]]}}" bindchange="__e"><label class="flex-y-center"><checkbox value="1" checked="{{isagree}}"></checkbox>阅读并同意</label></checkbox-group><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" style="color:#666;" bindtap="__e">《商户入驻协议》</text></view></block></block></block><view style="padding:30rpx 0;"><block wx:if="{{!info.id||info.status==2}}"><button class="set-btn" style="{{('background:'+(submitDisabled?'#ccc':'linear-gradient(90deg,'+$root.m3+' 0%,rgba('+$root.m4+',0.8) 100%)'))}}" form-type="submit" disabled="{{submitDisabled}}"><block wx:if="{{submitDisabled}}"><text style="margin-right:20rpx;">请先支付套餐费用</text></block><block wx:else><block wx:if="{{selectedPackage&&selectedPackage.price>0}}"><text style="margin-right:20rpx;">{{"套餐费用"+selectedPackage.price+"元"}}</text></block><block wx:else><block wx:if="{{bset.deposit&&bset.deposit>0}}"><text style="margin-right:20rpx;">{{$root.m5+bset.deposit}}</text></block></block></block>提交申请</button></block></view></form><view style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7);" id="xieyi" hidden="{{!showxieyi}}"><view style="width:90%;margin:0 auto;height:85%;margin-top:10%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px;"><view style="overflow:scroll;height:100%;"><parse vue-id="1ff03bda-2" content="{{bset.xieyi}}" bind:__l="__l"></parse></view><view data-event-opts="{{[['tap',[['hidexieyi',['$event']]]]]}}" style="position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center;" bindtap="__e">已阅读并同意</view></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="1ff03bda-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="1ff03bda-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="1ff03bda-5" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="1ff03bda-6" bind:__l="__l"></wxxieyi></view>