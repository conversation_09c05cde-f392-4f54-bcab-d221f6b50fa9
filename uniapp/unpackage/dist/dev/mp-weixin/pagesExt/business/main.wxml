<view class="container" style="{{'background-color:'+(pageinfo.bgcolor)+';'}}"><dp vue-id="4053c1a2-1" pagecontent="{{pagecontent}}" menuindex="{{menuindex}}" bind:__l="__l"></dp><dp-guanggao vue-id="4053c1a2-2" guanggaopic="{{guanggaopic}}" guanggaourl="{{guanggaourl}}" guanggaotype="{{guanggaotype}}" bind:__l="__l"></dp-guanggao><block wx:if="{{oglist}}"><view style="position:fixed;top:15vh;left:20rpx;z-index:991;background:rgba(0,0,0,0.6);border-radius:20rpx;color:#fff;padding:0 10rpx;"><swiper style="position:relative;height:54rpx;width:450rpx;" autoplay="true" interval="{{5000}}" vertical="true"><block wx:for="{{oglist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="flex-y-center" data-url="{{'/pages/shop/product?id='+item.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px;" src="{{item.headimg}}"></image><view style="width:400rpx;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:22rpx;" class="_div">{{item.nickname+" "+item.showtime+"购买了 "+item.name}}</view></swiper-item></block></swiper></view></block><dp-tabbar vue-id="4053c1a2-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="4053c1a2-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>