
page{width: #eef1f6;}
.container {
		display: flex;
		flex-direction: column;
}
.content{ width: 100%;}
.calloutContent{background-color: #fff; padding: 20rpx;border-radius: 10rpx;
}
.customCallout {
			box-sizing: border-box;
			background-color: #fff;
			border: 1px solid #ccc;
			border-radius: 30px;
			width: 150px;
			height: 40px;
			display: inline-flex;
			padding: 5px 20px;
			justify-content: center;
			align-items: center;
}
.calloutSub{ margin-top: 20rpx;}
.content1{ position: fixed; z-index: 1000;bottom: 0;width: 96%;margin: 0 2%;height: auto;background: #FFFFFF;border-radius: 16rpx 16rpx 0 0 ;max-height: 60%;overflow-y: scroll;font-family: PingFang SC;}
.mendian-box{padding: 0 20rpx 20rpx 20rpx;min-height: 700rpx;}
.mendian-info{display: flex;align-items: center;width: 100%;border-bottom: 1rpx solid #F6F6F6;margin-bottom: 20rpx;padding-bottom: 16rpx;}
.mendian-info .b1{background-color: #fbfbfb;}
.mendian-info .b1 image{height: 100rpx;width:100rpx;border-radius: 6rpx;border: 1px solid #e8e8e8;}
.mendian-info .b2{flex:1;line-height: 38rpx;margin-left: 20rpx;overflow: hidden;}
.mendian-info .b2 .t1{padding-bottom: 10rpx;}
.mendian-info .b2 .t2{font-size: 24rpx;color: #999;}
.mendian-info .b3{display: flex;justify-content: flex-end;flex-shrink: 0;padding-left: 20rpx;}
.mendian-info .b3 image{width: 40rpx;height: 40rpx;}
.mendian-info .tag{padding:0 10rpx;margin-right: 10rpx;display: inline-block;font-size: 22rpx;border-radius: 8rpx;flex-shrink: 0;}
.mendian-info .mendian-address{text-overflow: ellipsis;flex:1;width: 300rpx;white-space: nowrap;}
.mendian-info .line{border-right: 1rpx solid #999;width: 10rpx;flex-shrink: 0;height: 16rpx;padding-left:10rpx;margin-right: 12rpx;}
.mendian-info .mendian-distance{color: #3b3b3b;font-weight: 600;flex-shrink: 0;}
.toggle{display: flex;align-items: center;justify-content: center;height: 50rpx;}
.toggle-icon{width: 34rpx;height: 28rpx;}
.toggle-icon.up{-webkit-transform:scaleY(-1);transform:scaleY(-1)}

