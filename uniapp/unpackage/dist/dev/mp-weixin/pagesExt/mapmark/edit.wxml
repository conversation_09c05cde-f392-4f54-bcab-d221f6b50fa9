<view style="{{('background:'+bgcolor)}}"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="form-box" style="{{('background:'+bgcolor)}}"><view class="form-item"><view class="f1"><text style="color:red;">*</text>标注名称</view><view class="f2"><input type="text" name="name" placeholder="请填写标注名称" placeholder-style="color:#888"/></view></view><view class="form-item"><view class="f1"><text style="color:red;">*</text>经营类型</view><view class="f2"><input type="text" name="shop_type" placeholder="请填写经营类型" placeholder-style="color:#888"/></view></view><view class="form-item"><view class="f1"><text style="color:red;">*</text>营业电话</view><view class="f2"><input type="text" name="shop_tel" placeholder="请填写营业电话" placeholder-style="color:#888"/></view></view><view class="form-item"><view class="f1"><text style="color:red;">*</text>营业时间</view><view class="f2"><input type="text" name="shop_time" placeholder="请填写营业时间" placeholder-style="color:#888"/></view></view><view class="form-item"><view class="f1"><text style="color:red;">*</text>详细经营地址</view><view class="f2"><input type="text" name="address" placeholder="请填写详细经营地址" placeholder-style="color:#888"/></view></view><view class="form-item"><view class="f1"><text style="color:red;">*</text>联系电话</view><view class="f2"><input type="text" name="mobile" placeholder="请填写联系电话" placeholder-style="color:#888"/></view></view></view><view class="form-box" style="{{('background:'+bgcolor)}}"><view class="form-item flex-col"><view class="f1"><text style="color:red;">*</text>上传营业执照</view><view class="f2" style="flex-wrap:wrap;"><block wx:for="{{license_img}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="license_img" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0<5}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="license_img" data-pernum="9" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="license_img" maxlength="-1" value="{{$root.g1}}"/></view><view class="form-item flex-col"><view class="f1"><text style="color:red;">*</text>门面照片</view><view class="f2" style="flex-wrap:wrap;"><block wx:for="{{shop_img}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="shop_img" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g2<5}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="shop_img" data-pernum="9" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="shop_img" maxlength="-1" value="{{$root.g3}}"/></view></view><view class="form-box"><view class="form-item flex-col"><view class="f1">标注平台</view><view class="f2" style="line-height:30px;"><checkbox-group class="radio-group" name="cids" data-event-opts="{{[['change',[['bindGettjChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><label><checkbox value="{{''+item.$orig.id}}" checked="{{item.m0?true:false}}"></checkbox>{{''+item.$orig.name}}</label></block></checkbox-group></view></view></view><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m1+' 0%,rgba('+$root.m2+',0.8) 100%)')}}" form-type="submit">提交<block wx:if="{{total_pay>0}}"><text>{{"（需支付："+total_pay+"元)"}}</text></block></button><view style="height:50rpx;"></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="2a63c83c-1" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="2a63c83c-2" data-ref="popmsg" bind:__l="__l"></popmsg></view>