<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="form-box"><view class="form-item"><view class="f1">标注名称</view><view class="f2">{{info.name}}</view></view><view class="form-item"><view class="f1">经营类型</view><view class="f2">{{info.shop_type}}</view></view><view class="form-item"><view class="f1">营业电话</view><view class="f2">{{info.shop_tel}}</view></view><view class="form-item"><view class="f1">营业时间</view><view class="f2">{{info.shop_time}}</view></view><view class="form-item"><view class="f1">详细经营地址</view><view class="f2">{{info.address}}</view></view><view class="form-item"><view class="f1">联系电话</view><view class="f2">{{info.mobile}}</view></view></view><view class="form-box"><view class="form-item flex-col"><view class="f1">上传营业执照</view><view class="f2" style="flex-wrap:wrap;"><block wx:for="{{license_img}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></view><input type="text" hidden="true" name="license_img" maxlength="-1" value="{{$root.g0}}"/></view><view class="form-item flex-col"><view class="f1">门面照片</view><view class="f2" style="flex-wrap:wrap;"><block wx:for="{{shop_img}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></view><input type="text" hidden="true" name="shop_img" maxlength="-1" value="{{$root.g1}}"/></view></view><view class="form-box"><view class="form-item flex-col"><view class="f1">标注平台</view><view class="f2" style="line-height:30px;"><block wx:for="{{map_cats}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><block><view class="btn2">{{item}}</view></block></block></view></view></view><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}">{{'已支付'+info.pay_money+''}}</button><view style="height:50rpx;"></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="dadba30a-1" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="dadba30a-2" data-ref="popmsg" bind:__l="__l"></popmsg></view>