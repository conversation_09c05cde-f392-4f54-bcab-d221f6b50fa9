
.flex{display: flex;align-items: center;}
.flex-s{display: flex;justify-content: flex-start;align-items: center;}
.flex-e{display: flex;justify-content: flex-end;align-items: center;}
.flex-sb{display: flex;justify-content: space-between;align-items: center;}
.flex-c{display: flex;justify-content: center;align-items: center;}
.hui{color: #CCCCCC;}
.top{height: 400rpx;}
.content{position: relative;top: -400rpx;padding: 20rpx;}
.box{width: 100%;background: #FFFFFF; color: #222222;padding: 20rpx;border-radius: 20rpx;margin-bottom: 20rpx;}
.tips-top{width: 100%;text-align: center;font-size: 36rpx;color: #FFFFFF;padding: 30rpx 0;}
.tips-info{font-size: 16px; font-weight: bold;line-height: 45px;
}
.tips-info .tips{font-size: 30rpx;color: #cdcdcd;font-weight: normal;}
.tips-desc{color: #9d9d9d;font-size: 28rpx;}
.tips-ref{margin-right: 20rpx;}
.tips-desc image{width: 30rpx;height: 30rpx;padding-top: 8rpx;}
.tips-dushu{font-size: 24rpx;color:#9d9d9d;padding-top: 6rpx;}
.red{color: #ff0000;}
.bottom{margin: 40rpx 0;}
.dangan .form-label{font-size: 32rpx;font-weight: bold;}
.form-item{display: flex;justify-content: flex-start;align-items: center;/* border-bottom: 1rpx solid #f6f6f6; */ padding: 30rpx 20rpx;}
	/* .box .form-item:last-child{border-bottom: none;} */
.form-value textarea{padding: 10rpx 0;}
.form-label{flex-shrink: 0;width: 200rpx;flex-wrap: wrap;padding-right: 30rpx;font-size: 28rpx;}
.form-value{flex: 1;}
.form-tips{color: #CCCCCC;font-size: 28rpx;padding: 20rpx 0;}
.form-value .down{width: 28rpx;height: 28rpx;vertical-align:middle;flex-shrink: 0;}
.form-value.radio label{margin-right: 10rpx;}
.form-value.upload{display: flex;align-items: center;flex-wrap: wrap;}
.textarea-item .textarea{border: 1rpx solid #e5e5e5;max-width: 480rpx;border-radius: 4rpx;padding: 16rpx;height: 160rpx;font-size: 28rpx;}
.name-item input{text-align: right}
 .input-value-border{border: none;}
 .input-value{line-height: normal;padding: 0;}
	/* 行排列 */
.form-item-row{border-bottom: 1rpx solid #f6f6f6; padding: 20rpx 0;}
.form-item-row .form-label,.form-item-row .form-value{width: 100%;}
.form-item-row .form-value textarea{width: 100%;height: 200rpx;}
.form-option{display: flex;justify-content: center;padding: 30rpx;}
.form-option .btn{text-align: center;width: 100%;border-radius: 80rpx;line-height: 84rpx;}
.dushu{display: flex;justify-content: space-between;}
.unit-item{display: flex;justify-content: space-between;margin:0 6rpx;border-bottom: 1rpx solid #ededed;flex:1;font-size: 28rpx;}
.unit-item .unit{color: #8d8d8d;line-height: 44rpx;padding-left: 10rpx;}
.bb{border-bottom: 1rpx solid #e5e5e5;}
.mgL10{margin-left: 30rpx;}
.tips-jz{width: 40rpx;height: 40rpx;}
.jz-item .unit-item{width: 45%;}
.table-header{font-weight: bold;}
.table-header .form-value{display: flex;justify-content: flex-end;align-items: center;text-align: right;}
.table-header .table-item{width: 50%;flex-shrink: 0;}
.table-body{color: #cdcdcd; font-size: 24rpx;}
.table-body .form-item{padding: 20rpx 20rpx;color: #8d8d8d;}
.table-body .form-value{text-align: right;font-size: 24rpx;}
.table-body .form-item .uni-input-input{font-size: 24rpx;}
	/* 初始状态为隐藏，高度为0 */
.box-content {
    overflow: hidden; /* 防止内容溢出 */
    height: 0; /* 初始高度为0 */
    transition: height 0.3s ease; /* 添加高度变化的过渡效果 */
}
  

