
.content{background: #FFFFFF;padding: 30rpx;border-radius: 10rpx;color: #222222;width: 94%;margin: 20rpx 3% 0 3%;}
.row{padding: 20rpx 10rpx;display: flex;justify-content: flex-start;align-items: center;}
.mt{margin-top: 110rpx;}
.row .value{flex: 1;flex-wrap: wrap;}
.row .value-multi{flex:1;display: flex;justify-content: flex-start;align-items: center;}
.row-title{font-size: 30rpx;border-bottom: 1px solid #e2e2e2;padding-bottom: 30rpx;display: flex;justify-content: space-between;align-items: center;}
.row-title .r{font-size: 28rpx;color: #bbb;}
.value-multi .value-item{width: 45%;}
.bottom{border-top: 1rpx solid #e2e2e2;display: flex;justify-content: flex-end;padding-top: 30rpx;}
.bottom .btn{width: 150rpx; border-radius: 8rpx;color: #222222;border: 1px solid #e2e2e2;}
.bottom .btn1{background: #009688;color: #FFFFFF;}
.goadd{width: 90%;margin: 0 5%;}
.goadd .btn{border-radius: 60rpx;line-height: 90rpx;}
.remark{color: #bbb;}
.base{display: flex;justify-content: space-between;align-items: center;line-height: 46rpx;color: #bbb;font-size: 24rpx;}
.base .name{font-size: 32rpx;font-weight: bold;color: #222222;}
.lable{flex-shrink: 0;width: 160rpx;color:#bbb;}
.table-item{width: 33%;flex-shrink: 0;}
.table-body .row{color: #cdcdcd; font-size: 24rpx;display: flex;justify-content: space-around;text-align: right;}
.table-body .row.table-header{font-weight: bold;color: #222222;}

