<view class="container"><block wx:if="{{isload}}"><block><view class="top" style="{{'background:'+('linear-gradient('+$root.m0+' 0%,rgba('+$root.m1+',0) 100%)')+';'}}"></view><view class="content"><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="tips-top">创建视力档案，加速加工眼镜</view><view class="box"><view class="form-item dangan"><view class="form-label">档案名称</view><view class="form-value name-item bb"><input type="text" name="name" placeholder="请输入档案名称" placeholder-style="font-size:28rpx;color:#cccccc" value="{{detail.name}}"/></view></view></view><view class="box"><view class="tips-info">视力信息</view><view class="tips-desc flex"><view class="tips-ref">可参考验光单</view><view data-url="set?field=desc" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="red">填写说明</text><image src="{{pre_url+'/static/img/arrowright2.png'}}"></image></view></view><view class="form-item"><view class="form-label">视力</view><view class="form-value radio"><radio-group name="type" data-event-opts="{{[['change',[['typechange',['$event']]]]]}}" bindchange="__e"><label><radio style="transform:scale(0.7);" value="1" color="{{$root.m2}}" checked="{{type==1?true:false}}"></radio>近视</label><label><radio style="transform:scale(0.7);" value="2" color="{{$root.m3}}" checked="{{type==2?true:false}}"></radio>远视</label><label><radio style="transform:scale(0.7);" value="3" color="{{$root.m4}}" checked="{{type==3?true:false}}"></radio>远近两用</label></radio-group></view></view><view class="form-item bb"><view class="form-label">散光</view><view class="form-value radio"><radio-group name="is_ats" data-event-opts="{{[['change',[['atsChange',['$event']]]]]}}" bindchange="__e"><label><radio style="transform:scale(0.7);" value="0" color="{{$root.m5}}" checked="{{isAts==0?true:false}}"></radio>无</label><label><radio style="transform:scale(0.7);" value="1" color="{{$root.m6}}" checked="{{isAts==1?true:false}}"></radio>有</label></radio-group></view></view><view class="form-item table-header"><view class="form-label">验光数据</view><view class="form-value"><view class="table-item">右眼</view><view class="table-item">左眼</view></view></view><view class="table-body"><view class="form-item"><view class="form-label">球镜(Sph)</view><view class="form-value"><view class="dushu"><view class="unit-item"><picker style="width:100%;" name="degress_right" value="{{degress_right_index}}" mode="selector" range="{{degresslist}}" data-field="degress_right" data-event-opts="{{[['change',[['selectChange',['$event']]]]]}}" bindchange="__e"><view class="{{['flex-e',degress_right_index>-1?'':'hui']}}"><text>{{degress_right_index>-1?degresslist[degress_right_index]:'右眼'}}</text><image class="down" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></picker></view><view class="unit-item mgL10"><picker style="width:100%;" name="degress_left" value="{{degress_left_index}}" mode="selector" range="{{degresslist}}" data-field="degress_left" data-event-opts="{{[['change',[['selectChange',['$event']]]]]}}" bindchange="__e"><view class="{{['flex-e',degress_left_index>-1?'':'hui']}}"><text>{{degress_left_index>-1?degresslist[degress_left_index]:'左眼'}}</text><image class="down" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></picker></view></view></view></view><block wx:if="{{isAts==1}}"><block><view class="form-item"><view class="form-label">柱镜(Cyl)</view><view class="form-value"><view class="dushu"><view class="unit-item"><picker style="width:100%;" name="ats_right" value="{{ats_right_index}}" mode="selector" range="{{atslist}}" data-field="ats_right" data-event-opts="{{[['change',[['selectChange',['$event']]]]]}}" bindchange="__e"><view class="{{['flex-e',ats_right_index>-1?'':'hui']}}"><text>{{ats_right_index>-1?atslist[ats_right_index]:'右眼'}}</text><image class="down" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></picker></view><view class="unit-item mgL10"><picker style="width:100%;" name="ats_left" value="{{ats_left_index}}" mode="selector" range="{{atslist}}" data-field="ats_left" data-event-opts="{{[['change',[['selectChange',['$event']]]]]}}" bindchange="__e"><view class="{{['flex-e',ats_left_index>-1?'':'hui']}}"><text>{{ats_left_index>-1?atslist[ats_left_index]:'左眼'}}</text><image class="down" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></picker></view></view></view></view><view class="form-item"><view class="form-label">轴位(Axis)</view><view class="form-value"><view class="dushu"><view class="unit-item"><picker style="width:100%;" name="ats_zright" value="{{ats_zright_index}}" mode="selector" range="{{atszlist}}" data-field="ats_zright" data-event-opts="{{[['change',[['selectChange',['$event']]]]]}}" bindchange="__e"><view class="{{['flex-e',ats_zright_index>-1?'':'hui']}}"><text>{{ats_zright_index>-1?atszlist[ats_zright_index]:'右眼'}}</text><image class="down" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></picker></view><view class="unit-item mgL10"><picker style="width:100%;" name="ats_zleft" value="{{ats_zleft_index}}" mode="selector" range="{{atszlist}}" data-field="ats_zleft" data-event-opts="{{[['change',[['selectChange',['$event']]]]]}}" bindchange="__e"><view class="{{['flex-e',ats_zleft_index>-1?'':'hui']}}"><text>{{ats_zleft_index>-1?atszlist[ats_zleft_index]:'左眼'}}</text><image class="down" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></picker></view></view></view></view></block></block><block wx:if="{{type==3}}"><view class="form-item"><view class="form-label">下加光(ADD)</view><view class="form-value"><view class="dushu"><view class="unit-item"><picker style="width:100%;" name="add_right" value="{{add_right_index}}" mode="selector" range="{{addlist}}" data-field="add_right" data-event-opts="{{[['change',[['selectChange',['$event']]]]]}}" bindchange="__e"><view class="{{['flex-e',add_right_index>-1?'':'hui']}}"><text>{{add_right_index>-1?addlist[add_right_index]:'右眼'}}</text><image class="down" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></picker></view><view class="unit-item mgL10"><picker style="width:100%;" name="add_left" value="{{add_left_index}}" mode="selector" range="{{addlist}}" data-field="add_left" data-event-opts="{{[['change',[['selectChange',['$event']]]]]}}" bindchange="__e"><view class="{{['flex-e',add_left_index>-1?'':'hui']}}"><text>{{add_left_index>-1?addlist[add_left_index]:'左眼'}}</text><image class="down" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></picker></view></view></view></view></block><view class="form-item"><view class="form-label"><view>瞳距(PD)</view><view style="font-size:24rpx;">双瞳距<checkbox style="transform:scale(0.6);" checked="{{doublepd}}" data-event-opts="{{[['tap',[['pdchange',['$event']]]]]}}" bindtap="__e"></checkbox></view></view><view class="form-value"><block wx:if="{{doublepd}}"><view class="flex-s"><view class="unit-item"><picker style="width:100%;" name="ipd_right" value="{{pd_right_index}}" mode="selector" range="{{dpdlist}}" data-field="pd_right" data-event-opts="{{[['change',[['selectChange',['$event']]]]]}}" bindchange="__e"><view class="{{['flex-e',pd_right_index>-1?'':'hui']}}"><text>{{pd_right_index>-1?dpdlist[pd_right_index]:'右眼'}}</text><image class="down" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></picker></view><view class="unit-item"><picker style="width:100%;" name="ipd_left" value="{{pd_left_index}}" mode="selector" range="{{dpdlist}}" data-field="pd_left" data-event-opts="{{[['change',[['selectChange',['$event']]]]]}}" bindchange="__e"><view class="{{['flex-e',pd_left_index>-1?'':'hui']}}"><text>{{pd_left_index>-1?dpdlist[pd_left_index]:'左眼'}}</text><image class="down" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></picker></view></view></block><block wx:if="{{!doublepd}}"><view class="unit-item"><picker style="width:100%;" name="ipd" value="{{pd_index}}" mode="selector" range="{{pdlist}}" data-field="pd" data-event-opts="{{[['change',[['selectChange',['$event']]]]]}}" bindchange="__e"><view class="{{['flex-e',pd_index>-1?'':'hui']}}"><text>{{pd_index>-1?pdlist[pd_index]:'瞳距'}}</text><image class="down" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></picker></view></block></view></view><view class="form-item jz-item"><view class="form-label flex-y-center"><text>矫正视力</text><image class="tips-jz" src="{{pre_url+'/static/img/tanhao.png'}}" data-event-opts="{{[['tap',[['showJzTips',['$event']]]]]}}" bindtap="__e"></image></view><view class="form-value dushu"><view class="unit-item"><picker style="width:100%;" name="correction_right" value="{{correction_right_index}}" mode="selector" range="{{visionlist}}" data-field="correction_right" data-event-opts="{{[['change',[['selectChange',['$event']]]]]}}" bindchange="__e"><view class="{{['flex-e',correction_right_index>-1?'':'hui']}}"><text>{{correction_right_index>-1?visionlist[correction_right_index]:'右眼'}}</text><image class="down" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></picker></view><view class="unit-item"><picker style="width:100%;" name="correction_left" value="{{correction_left_index}}" mode="selector" range="{{visionlist}}" data-field="correction_left" data-event-opts="{{[['change',[['selectChange',['$event']]]]]}}" bindchange="__e"><view class="{{['flex-e',correction_left_index>-1?'':'hui']}}"><text>{{correction_left_index>-1?visionlist[correction_left_index]:'左眼'}}</text><image class="down" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></picker></view></view></view></view></view><view class="box"><view class="form-item textarea-item"><view class="form-label" style="width:120rpx;">备注</view><view class="form-value"><textarea class="textarea" name="remark" placeholder="请输入备注信息" placeholder-style="font-size:28rpx;color:#cccccc" value="{{detail.remark}}"></textarea></view></view></view><view class="box"><view data-event-opts="{{[['tap',[['toggleBoxContent',['$event']]]]]}}" class="tips-info flex-bt show-box" bindtap="__e"><view>个人信息<text class="tips">（建议填写）</text></view><view><text class="{{[iconClass]}}" style="color:#999;font-weight:normal;"></text></view></view><view class="box-content" style="{{'height:'+(boxContentHeight)+';'}}"><view class="form-item"><view class="form-label">姓名</view><view class="form-value unit-item"><input type="text" name="nickname" placeholder="请输入姓名" placeholder-style="font-size:28rpx;color:#cccccc" value="{{detail.nickname}}"/></view></view><view class="form-item"><view class="form-label">年龄</view><view class="form-value unit-item"><input type="number" name="age" placeholder="请输入年龄" placeholder-style="font-size:28rpx;color:#cccccc" value="{{detail.age}}"/><text class="unit">岁</text></view></view><view class="form-item"><view class="form-label">性别</view><view class="form-value radio"><radio-group name="sex"><label><radio style="transform:scale(0.8);" value="1" color="{{$root.m7}}" checked="{{detail.sex==1?true:false}}"></radio>男</label><label><radio style="transform:scale(0.8);" value="2" color="{{$root.m8}}" checked="{{detail.sex==2?true:false}}"></radio>女</label></radio-group></view></view><view class="form-item"><view class="form-label">手机号码</view><view class="form-value unit-item"><input type="text" name="tel" placeholder="请输入手机号码" placeholder-style="font-size:28rpx;color:#cccccc" value="{{detail.tel}}"/></view></view><view class="form-item"><view class="form-label">验光时间</view><view class="form-value unit-item"><picker style="width:100%;" name="check_time" value="{{check_time}}" mode="date" data-event-opts="{{[['change',[['bindDateChange',['$event']]]]]}}" bindchange="__e"><view class="{{['flex-sb',check_time?'':'hui']}}"><text>{{check_time?check_time:'请选择验光时间'}}</text><image class="down" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></picker></view></view><view class="form-item"><view class="form-label">验光单位</view><view class="form-value unit-item"><input type="text" name="optometry_company" placeholder="请输入验光单位" placeholder-style="font-size:28rpx;color:#cccccc" value="{{detail.optometry_company}}"/></view></view><view class="form-item"><view class="form-label">验光师</view><view class="form-value unit-item"><input type="text" name="optometry_name" placeholder="请输入验光师" placeholder-style="font-size:28rpx;color:#cccccc" value="{{detail.optometry_name}}"/></view></view></view></view><view class="bottom"><view class="flex-x-center"><radio-group data-event-opts="{{[['tap',[['ruleChange',['$event']]]]]}}" bindtap="__e"><label><radio style="transform:scale(0.8);" color="{{$root.m9}}" checked="{{isrule}}"></radio></label></radio-group><view>我已阅读并同意<text class="red" data-url="set?field=xieyi" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">《用户信息协议授权协议》</text>中的全部内容</view></view><view class="form-option"><button class="btn" style="{{'background:'+('linear-gradient(-90deg,'+$root.m10+' 0%,rgba('+$root.m11+',0.8) 100%)')+';'+('color:'+('#ffffff')+';')}}" form-type="submit" data-type="1">保存</button></view></view></form></view></block></block><block wx:if="{{loading}}"><loading vue-id="480f5e7c-1" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="480f5e7c-2" data-ref="popmsg" bind:__l="__l"></popmsg></view>