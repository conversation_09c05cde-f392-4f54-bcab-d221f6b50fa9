<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{$root.g0>0}}"><block><dd-tab vue-id="49c71d2b-1" itemdata="{{namelist}}" itemst="{{indexlist}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view class="box mt"><block wx:if="{{detail.nickname||detail.check_time}}"><view class="content"><view class="base"><view class="fl-l"><view class="name">{{detail.nickname}}</view><view><block wx:if="{{detail.sex}}"><text>{{(detail.sex==1?'男':'女')+''}}</text></block>{{''+(detail.age?detail.age+'岁':'')}}</view></view><block wx:if="{{detail.check_time}}"><view class="fl-r" style="text-align:right;"><view class="t">验光时间</view><view class="t">{{detail.check_time}}</view></view></block></view><view class="remark">{{'备注：'+detail.remark+''}}</view></view></block><view class="content"><view class="row-title"><view class="l">{{detail.typetxt}}</view><view class="r">瞳距<block wx:if="{{detail.double_ipd==0}}"><text>{{detail.ipd}}</text></block><block wx:else><text>{{"R"+detail.ipd_right+" L"+detail.ipd_left}}</text></block></view></view><view class="table-body"><view class="row table-header"><view class="table-item lable" style="color:#222222;">验光数据</view><view class="table-item">右眼</view><view class="table-item">左眼</view></view><view class="row"><view class="table-item lable">球镜(Sph)</view><view class="table-item">{{detail.degress_right}}</view><view class="table-item">{{detail.degress_left}}</view></view><view class="row"><view class="table-item lable">柱镜(Cyl)</view><view class="table-item">{{detail.ats_right}}</view><view class="table-item">{{detail.ats_left}}</view></view><view class="row"><view class="table-item lable">轴位(Axis)</view><view class="table-item">{{detail.ats_zright}}</view><view class="table-item">{{detail.ats_zleft}}</view></view><block wx:if="{{detail.type==3}}"><view class="row"><view class="table-item lable">下加光(ADD)</view><view class="table-item">{{detail.add_right}}</view><view class="table-item">{{detail.add_left}}</view></view></block><view class="row"><view class="table-item lable">矫正视力</view><view class="table-item">{{detail.correction_right}}</view><view class="table-item">{{detail.correction_left}}</view></view></view><view class="bottom"><button class="btn" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['del',['$event']]]]]}}" bindtap="__e">删除</button><button class="btn" data-url="{{'add?id='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">编辑</button><button class="btn" data-url="add" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">添加</button><block wx:if="{{confirm==1}}"><button class="btn btn1" style="{{'background:'+($root.m0)+';'+('color:'+('#ffffff')+';')}}" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['selectrecord',['$event']]]]]}}" bindtap="__e">选择</button></block></view></view></view></block></block><block wx:else><block><nodata vue-id="49c71d2b-2" text="还没有你的视力档案哟~" bind:__l="__l"></nodata><view class="goadd"><button class="btn" style="{{'background:'+('linear-gradient(-90deg,'+$root.m1+' 0%,rgba('+$root.m2+',0.8) 100%)')+';'+('color:'+('#ffffff')+';')}}" data-url="add" form-type="submit" data-type="1" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">完善视力档案</button></view></block></block></block></block><block wx:if="{{loading}}"><loading vue-id="49c71d2b-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="49c71d2b-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="49c71d2b-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>