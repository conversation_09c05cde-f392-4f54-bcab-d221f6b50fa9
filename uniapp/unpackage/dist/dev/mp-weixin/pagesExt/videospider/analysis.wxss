
.banner {
	position: relative;
	width: 100%;
	display: block;
}
.page {
	padding: 30rpx;
}
.title {
	font-size: 32rpx;
	font-weight: bold;
}
.textarea {
	position: relative;
}
.textarea_module {
	position: relative;
	padding: 30rpx;
	margin: 20rpx 0 0 0;
	display: block;
	border-radius: 20rpx;
	background: #f0f0f0;
	height: 260rpx;
	font-size: 28rpx;
	width: 100%;
	box-sizing: border-box;
}
.btn {
	display: block;
	height: 90rpx;
	border-radius: 100rpx;
	background: #FF9900;
	color: #fff;
	text-align: center;
	line-height: 90rpx;
	font-size: 28rpx;
	margin-top: 30rpx;
}
.btn_tip{
	font-size: 22rpx;
}
.sustain {
	position: relative;
	margin-top: 50rpx;
}
.sustain_title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}
.sustain_module {
	position: relative;
	display: grid;
	margin: 15rpx 0 0 0;
	grid-template-columns: repeat(5, 1fr);
	grid-column-gap: 10rpx;
	grid-row-gap: 10rpx;
}
.sustain_image {
	width: 90rpx;
	height: 90rpx;
	display: block;
	margin: 0 auto;
}
.sustain_text {
	font-size: 24rpx;
	color: #333;
	text-align: center;
	margin: 10rpx 0 0 0;
}
.freight {width: 100%;padding: 20rpx 0;background: #fff;display: flex;flex-direction: column;}
.freight .f1 {color: #333;margin-bottom: 10rpx}
.freight .f2 {color: #111111;text-align: right;flex: 1}
.freight .f3 {width: 24rpx;height: 28rpx;}
.freighttips {color: red;font-size: 24rpx;}
.freight-ul {width: 100%;}
.freight-li {background: #F5F6F8;border-radius: 24rpx;color: #6C737F;font-size: 24rpx;line-height: 48rpx;padding: 0 28rpx;margin: 12rpx 10rpx 12rpx 0;display: inline-block;white-space: break-spaces;max-width: 610rpx;vertical-align: middle;}

