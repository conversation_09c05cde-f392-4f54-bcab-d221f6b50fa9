<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="form-box"><view class="form-item"><view class="f1">视频标题</view><view class="f2">{{info.title}}</view></view><view class="form-item"><view class="f1">视频封面</view><view class="f2"><view class="layui-imgbox"><view class="layui-imgbox-img"><image src="{{info.cover}}" data-url="{{info.cover}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></view><view class="form-item"><view class="f1">视频链接</view><view class="f2"><view data-event-opts="{{[['tap',[['copyUrl',['$0'],['info.url']]]]]}}" bindtap="__e">复制视频链接</view></view></view><view class="form-item"><video id="myVideo" src="{{info.url}}" controls="{{true}}" data-event-opts="{{[['error',[['videoErrorCallback',['$event']]]]]}}" binderror="__e"></video></view><view class="form-item"><view class="f1"><view data-event-opts="{{[['tap',[['downVideo',['$0'],['info.url']]]]]}}" bindtap="__e">点击下载视频</view></view></view></view><view style="height:50rpx;"></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="7d6c721c-1" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="7d6c721c-2" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="7d6c721c-3" bind:__l="__l"></wxxieyi></view>