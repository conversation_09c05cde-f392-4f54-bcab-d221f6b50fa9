<view style="{{('background:'+bgcolor)}}"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="page"><view class="title">请输入关键词开始你的创作</view><view class="textarea"><textarea class="textarea_module" name="ai_text" placeholder="多个关键字以逗号拼接,最多输入100字" data-event-opts="{{[['input',[['__set_model',['','ai_text','$event',[]]]]]]}}" value="{{ai_text}}" bindinput="__e"></textarea><view class="textarea_text">0/100</view></view><view class="try flex-y-center"><view class="try_title">试一试：</view><scroll-view class="try_module" scroll-x="{{true}}"><block wx:for="{{keywords}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><block><view class="{{['try_item',(keyword_class==item.id)?'try_active':'']}}" data-id="{{item.id}}" data-keywords="{{item.keyword}}" data-event-opts="{{[['tap',[['writeText',['$event']]]]]}}" bindtap="__e">{{item.name}}</view></block></block></scroll-view></view><view class="title">渲染引擎</view><scroll-view class="render" scroll-x="{{true}}"><view class="render_item render_active">百度文心</view></scroll-view><view class="title">选择风格</view><scroll-view class="style" scroll-x="{{true}}"><block wx:for="{{cats}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><block><view class="style_item" data-style="{{item.name}}" data-id="{{item.id}}" data-event-opts="{{[['tap',[['choseStyle',['$0'],[[['cats','',idx]]]]]]]}}" bindtap="__e"><image src="{{item.pic}}" mode></image><view class="{{['style_title',(style_class==item.id)?'style_active':'']}}">{{item.name}}</view></view></block></block></scroll-view><view class="title">图片比例</view><view class="size flex"><view data-event-opts="{{[['tap',[['choseBili',[1]]]]]}}" class="size_module" bindtap="__e"><view class="{{['size_item','flex-xy-center',(img_bili==1)?'style_active':'']}}"><view class="size_tag1"></view></view><view class="size_title">1:1</view></view><view data-event-opts="{{[['tap',[['choseBili',[2]]]]]}}" class="size_module" bindtap="__e"><view class="{{['size_item','flex-xy-center',(img_bili==2)?'style_active':'']}}"><view class="size_tag2"></view></view><view class="size_title">2:3</view></view><view data-event-opts="{{[['tap',[['choseBili',[3]]]]]}}" class="size_module" bindtap="__e"><view class="{{['size_item','flex-xy-center',(img_bili==3)?'style_active':'']}}"><view class="size_tag3"></view></view><view class="size_title">3:2</view></view></view><block wx:if="{{!end_time}}"><view class="freight" style="{{('background:'+bgcolor)}}"><view class="f1">支付方式</view><view class="freight-ul"><view style="width:100%;overflow-y:hidden;overflow-x:scroll;white-space:nowrap;"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="idx2" wx:key="idx2"><block><view class="freight-li" style="{{(pay_way==item.$orig.type?'color:'+item.m0+';background:rgba('+item.m1+',0.2)':'')}}" data-type="{{item.$orig.type}}" data-totalpay="{{item.$orig.num}}" data-index="{{idx2}}" data-event-opts="{{[['tap',[['changePayway',['$event']]]]]}}" bindtap="__e">{{item.$orig.name+''}}</view></block></block></view></view><block wx:for="{{pay_ways}}" wx:for-item="item" wx:for-index="idx2" wx:key="idx2"><block><block wx:if="{{pay_way==item.type}}"><view class="freighttips">{{item.desc}}</view></block></block></block></view></block><block wx:if="{{end_time}}"><view class="freight" style="{{('background:'+bgcolor)}}"><view class="freighttips">{{"试用有效期截止："+end_time}}</view></view></block><button class="btn" form-type="submit">开始绘画<block wx:if="{{total_pay>0&&pay_way!='free'}}"><text class="btn_tip">{{"(消费"+total_pay+pay_type+")"}}</text></block></button></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="29361fb2-1" bind:__l="__l"></loading></block></view>