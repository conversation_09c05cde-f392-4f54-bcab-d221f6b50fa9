
page {
	background: #fff;
}
.page {
	padding: 30rpx;
}
.title {
	font-size: 32rpx;
	font-weight: bold;
}
.textarea {
	position: relative;
}
.textarea_module {
	position: relative;
	padding: 30rpx;
	margin: 20rpx 0 0 0;
	display: block;
	border-radius: 20rpx;
	background: #f0f0f0;
	height: 260rpx;
	font-size: 28rpx;
	width: 100%;
	box-sizing: border-box;
}
.textarea_text {
	position: absolute;
	width: 100%;
	bottom: 30rpx;
	text-align: right;
	font-size: 26rpx;
	color: #999;
	padding: 0 30rpx;
	box-sizing: border-box;
}
.try {
	padding: 30rpx 0;
	overflow: hidden;
}
.try_title {
	font-size: 26rpx;
	color: #333;
	width: 120rpx;
	flex-shrink: 0;
}
.try_module {
	white-space: nowrap;
	width: calc(100% - 120rpx);
}
.try_item {
	padding: 10rpx 15rpx;
	font-size: 24rpx;
	color: #333;
	border: 1px solid #f0f0f0;
	border-radius: 4rpx;
	margin: 0 15rpx 0 0;
	display: inline-block;
}
.try_item:last-child {
	margin: 0 0 0 0;
}
.try_active{
	color: #FF9900;
	border: 1px solid #FF9900;
}
.render {
	white-space: nowrap;
	padding: 30rpx 0;
}
.render_item {
	padding: 10rpx 15rpx;
	font-size: 24rpx;
	color: #333;
	border: 1px solid #f0f0f0;
	border-radius: 4rpx;
	margin: 0 15rpx 0 0;
	display: inline-block;
}
.render_item:last-child {
	margin: 0 0 0 0;
}
.render_active{
	color: #FF9900;
	border: 1px solid #FF9900;
}
.style {
	white-space: nowrap;
	padding: 30rpx 0;
}
.style_item {
	position: relative;
	width: 170rpx;
	height: 170rpx;
	margin: 0 15rpx 0 0;
	display: inline-block;
}
.style_item image{
	width: 100%;
	height: 100%;
}
.style_title{
	position: absolute;
	bottom: 0;
	width: 100%;
	box-sizing: border-box;
	height: 35rpx;
	line-height: 35rpx;
	text-align: center;
	padding: 0 10rpx;
	font-size: 22rpx;
	color: #fff;
	background: rgba(0, 0, 0, 0.5);
}
.style_item:last-child {
	margin: 0 0 0 0;
}
.style_active{
	background: #FF9900;
}
.size{
	position: relative;
	padding: 30rpx 0;
}
.size_module{
	margin: 0 30rpx 0 0;
}
.size_item{
	width: 150rpx;
	height: 150rpx;
	border: 2px solid #ddd;
	border-radius: 8rpx;
}
.size_tag1{
	width: 120rpx;
	height: 120rpx;
	background: #ddd;
	border-radius: 8rpx;
}
.size_tag2{
	width: 84rpx;
	height: 120rpx;
	background: #ddd;
	border-radius: 8rpx;
}
.size_tag3{
	height: 84rpx;
	width: 120rpx;
	background: #ddd;
	border-radius: 8rpx;
}
.size_title{
	font-size: 26rpx;
	color: #333;
	margin: 20rpx 0 0 0;
	text-align: center;
}
.size_active{
	border-color: #FF9900;
}
.size_active view{
	background: #FF9900;
}
.btn{
	display: block;
	height: 90rpx;
	border-radius: 100rpx;
	background: #FF9900;
	color: #fff;
	text-align: center;
	line-height: 90rpx;
	font-size: 28rpx;
}
.btn_tip{
	font-size: 22rpx;
}
.freight {width: 100%;padding: 20rpx 0;background: #fff;display: flex;flex-direction: column;}
.freight .f1 {color: #333;margin-bottom: 10rpx}
.freight .f2 {color: #111111;text-align: right;flex: 1}
.freight .f3 {width: 24rpx;height: 28rpx;}
.freighttips {color: red;font-size: 24rpx;}
.freight-ul {width: 100%;}
.freight-li {background: #F5F6F8;border-radius: 24rpx;color: #6C737F;font-size: 24rpx;line-height: 48rpx;padding: 0 28rpx;margin: 12rpx 10rpx 12rpx 0;display: inline-block;white-space: break-spaces;max-width: 610rpx;vertical-align: middle;}

