<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="form-box"><view class="form-item"><view class="f1">图片预览</view><view class="f2"><view class="layui-imgbox"><view class="layui-imgbox-img"><image src="{{info.pic}}" data-url="{{info.pic}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></view><view class="form-item"><view class="f1">图片链接</view><view class="f2"><u-button vue-id="67201506-1" type="primary" data-event-opts="{{[['^click',[['copyUrl',['$0'],['info.pic']]]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">点击复制图片链接</u-button></view></view></view><view style="height:50rpx;"></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="67201506-2" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="67201506-3" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="67201506-4" bind:__l="__l"></wxxieyi></view>