
.container {
    width: 100%;
    display: flex;
    flex-direction: column
}
.content {
    width: 94%;
    margin: 0 3% 20rpx 3%;
}
.content .item {
    width: 100%;
    background: #fff;
    margin: 20rpx 0;
    padding: 20rpx 20rpx;
    border-radius: 8px;
    display: flex;
    align-items: center
}
.content .item:last-child {
    border: 0
}
.content .item .f1 {
    width: 500rpx;
    display: flex;
    flex-direction: column
}
.content .item .f1 .t1 {
    color: #000000;
    font-size: 30rpx;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
}
.content .item .f1 .t2 {
    color: #666666
}
.content .item .f1 .t3 {
    color: #666666
}
.content .item .f2 {
    flex: 1;
    width: 200rpx;
    font-size: 36rpx;
    text-align: right
}
.content .item .f2 .t1 {
    color: #03bc01
}
.content .item .f2 .t2 {
    color: #000000
}
.content .item .f3 {
    flex: 1;
    width: 200rpx;
    font-size: 32rpx;
    text-align: right
}
.content .item .f3 .t1 {
    color: #03bc01
}
.content .item .f3 .t2 {
    color: #000000
}
.data-empty {
    background: #fff
}
.mymoney {
    width: 94%;
    margin: 20rpx 3%;
    border-radius: 10rpx 56rpx 10rpx 10rpx;
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 70rpx 0
}
.mymoney .f1 {
    margin: 0 0 0 60rpx;
    color: rgba(255, 255, 255, 0.8);
    font-size: 24rpx;
}
.mymoney .f2 {
    margin: 20rpx 0 0 60rpx;
    color: #fff;
    font-size: 64rpx;
    font-weight: bold
}
.mymoney .f3 {
    height: 56rpx;
    padding: 0 10rpx 0 20rpx;
    border-radius: 28rpx 0px 0px 28rpx;
    background: rgba(255, 255, 255, 0.2);
    font-size: 20rpx;
    font-weight: bold;
    color: #fff;
    display: flex;
    align-items: center;
    position: absolute;
    top: 94rpx;
    right: 0
}

