<view class="container"><block wx:if="{{isload}}"><block><view class="mymoney" style="{{'background:'+($root.m0)+';'}}"><view class="f1">{{"我的"+type_name}}</view><view class="f2"><text style="font-size:26rpx;">￥</text>{{money}}</view></view><view class="content"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.remark}}</text><text class="t2">{{item.createtime}}</text><text class="t3">{{"变更后余额: "+item.after}}</text></view><view class="f2"><block wx:if="{{item.money>0}}"><text class="t1">{{"+"+item.money}}</text></block><block wx:else><text class="t2">{{item.money}}</text></block></view></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="f66deaa4-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="f66deaa4-2" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="f66deaa4-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="f66deaa4-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="f66deaa4-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>