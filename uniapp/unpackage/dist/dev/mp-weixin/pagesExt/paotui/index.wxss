
page{
    width: 100%;
    height: 100%;
}
.map {
    width: 750rpx;
    margin: 0 auto;
    height: 500rpx;
}
.btn{
    width: 250rpx;display: inline-block;padding: 20rpx;line-height: 70rpx;
}
.btn1{
    border-radius: 70rpx;
}
.first_title{
    width: 100%;
    overflow: hidden;
    font-size: 34rpx;
    line-height: 40rpx;
}
.black{width: 16rpx;height: 16rpx;background-color: #000;border-radius: 50%;float: left;margin-top: 14rpx;}
.red{width: 16rpx;height: 16rpx;background-color:#FF3A51;border-radius: 50%;float: left;margin-top: 14rpx;}
.red_view{overflow: hidden;margin-top: 20rpx;background-color:#F6F6F6;border-radius: 12rpx;padding: 20rpx;}
.goorder{width: 200rpx;float: right;text-align: center;line-height: 80rpx;border-radius: 8rpx;margin: 20rpx auto;color: #fff;}
.goorder2{width: 700rpx;margin:0 auto;text-align: center;line-height: 100rpx;border-radius: 8rpx;margin: 20rpx auto;color: #fff;}
.lookMore{width: 40rpx;height: 40rpx;float: left;border: 2rpx solid #eee;border-radius: 50%;padding: 10rpx;margin-top: 22rpx;margin-left:10rpx}
.picker-view {
    width: 750rpx;
    height: 400rpx;
    margin-top: 20rpx;
    margin: 0 auto;
}
.item {
    height: 50px;
    line-height: 50px;
    align-items: center;
    justify-content: center;
    text-align: center;
}
.back_index{position: fixed;bottom: 200rpx;right: 0rpx;width: 100rpx;line-height: 100rpx;border-radius: 50%;text-align: center;color:#fff}
.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.layui-imgbox-img>image{max-width:100%;}
.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.uploadbtn{position:relative;height:200rpx;width:200rpx}

