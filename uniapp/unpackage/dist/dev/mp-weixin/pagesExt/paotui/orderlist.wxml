<view style="width:100%;height:100%;"><block wx:if="{{isload}}"><block><dd-tab vue-id="11f3b6aa-1" itemdata="{{['全部','待接单','处理中','已完成','退款']}}" itemst="{{['all','1','24','5','-2']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetabs']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view style="overflow:hidden;margin-bottom:40rpx;"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view style="width:700rpx;margin:0 auto;border-radius:12rpx;background-color:#fff;padding-bottom:20rpx;margin-bottom:20rpx;"><view style="width:660rpx;margin:0 auto;"><view style="overflow:hidden;border-bottom:2rpx #f4f4f4 solid;line-height:80rpx;"><view style="float:left;"><block wx:if="{{item.$orig.btntype==1}}"><text>帮我送</text></block><block wx:if="{{item.$orig.btntype==2}}"><text>帮我取</text></block></view><view style="float:right;"><block wx:if="{{item.$orig.status==0}}"><text class="st0">待付款</text></block><block wx:if="{{item.$orig.status==1}}"><text class="st1">待接单</text></block><block wx:if="{{item.$orig.status==2}}"><text class="st2">已接单</text></block><block wx:if="{{item.$orig.status==3}}"><text class="st2">已到店</text></block><block wx:if="{{item.$orig.status==4}}"><text class="st2">配送中</text></block><block wx:if="{{item.$orig.status==5}}"><text class="st3">已完成</text></block><block wx:if="{{item.$orig.status==-1}}"><text class="st3">已取消</text></block><block wx:if="{{item.$orig.status==-2}}"><text class="st3" style="color:red;">退款失败</text></block></view></view><view style="overflow:hidden;padding:20rpx;" data-url="{{'detail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="black"></view><view style="float:left;margin-left:20rpx;width:580rpx;"><view class="first_title">{{''+item.$orig.take_area+''}}</view><view style="font-size:30rpx;color:#666666;line-height:50rpx;">{{''+item.$orig.take_address+''}}</view><view style="font-size:24rpx;color:#666666;line-height:50rpx;">{{''+item.$orig.take_name+" "+item.$orig.take_tel+''}}</view></view></view><view class="red_view"><view class="red"></view><view style="float:left;margin-left:20rpx;width:580rpx;"><view class="first_title">{{''+item.$orig.send_area+''}}</view><view style="font-size:30rpx;color:#666666;line-height:50rpx;">{{''+item.$orig.send_address+''}}</view><view style="font-size:24rpx;color:#666666;line-height:50rpx;">{{''+item.$orig.send_name+" "+item.$orig.send_tel+''}}</view></view></view><view style="overflow:hidden;border-top:2rpx #f4f4f4 solid;border-bottom:2rpx #f4f4f4 solid;margin:20rpx 0;line-height:80rpx;"><view style="float:left;"><view>实付：<text style="color:red;">{{"￥"+item.$orig.totalprice}}</text></view><block wx:if="{{item.$orig.cancel_fee>0}}"><view style="margin-left:20rpx;"><view>违约金：<text style="color:red;">{{"￥"+item.$orig.cancel_fee}}</text></view></view></block><block wx:if="{{item.$orig.refund_status==2&&item.$orig.refund_money>0}}"><view style="margin-left:20rpx;"><view>已退款：<text style="color:red;">{{"￥"+item.$orig.refund_money}}</text></view></view></block><block wx:if="{{item.$orig.status!=-1&&item.$orig.refund_status==1&&item.$orig.refund_money>0}}"><view style="margin-left:20rpx;"><text style="color:red;">等待退款</text></view></block><block wx:if="{{item.$orig.status!=-1&&item.$orig.refund_status==-1&&item.$orig.refund_money>0}}"><view style="margin-left:20rpx;"><view style="color:red;">退款驳回</view></view></block><block wx:if="{{item.$orig.status!=-1&&item.$orig.refund_status==-2&&item.$orig.refund_money>0}}"><view style="margin-left:20rpx;"><view style="color:red;">退款驳回</view></view></block></view></view><view style="overflow:hidden;display:flex;"><view class="btn2" data-url="{{'orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">详情</view><block wx:if="{{item.$orig.status>=0&&item.$orig.status<=cancel_status}}"><block><view class="btn2" data-id="{{item.$orig.id}}" data-type="1" data-event-opts="{{[['tap',[['tocancel',['$event']]]]]}}" catchtap="__e">取消订单</view></block></block><block wx:if="{{item.$orig.status==-2}}"><block><view class="btn2" data-id="{{item.$orig.id}}" data-type="2" data-event-opts="{{[['tap',[['tocancel',['$event']]]]]}}" catchtap="__e">退款</view></block></block><block wx:if="{{item.$orig.status==5&&end_refund_status}}"><block><block wx:if="{{item.$orig.refund_status==1}}"><block><block wx:if="{{item.$orig.refund_money<=0}}"><view class="btn2" data-id="{{item.$orig.id}}" data-type="20" data-event-opts="{{[['tap',[['tocancel',['$event']]]]]}}" catchtap="__e">申请退款</view></block></block></block><block wx:else><block><block wx:if="{{item.$orig.refund_status!=2}}"><view class="btn2" data-id="{{item.$orig.id}}" data-type="20" data-event-opts="{{[['tap',[['tocancel',['$event']]]]]}}" catchtap="__e">申请退款</view></block></block></block></block></block><block wx:if="{{item.$orig.status==0}}"><block><block wx:if="{{item.$orig.paytypeid!=5}}"><view class="btn1" style="{{'background:'+(item.m0)+';'}}" data-url="{{'/pagesExt/pay/pay?id='+item.$orig.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去付款</view></block></block></block><block wx:if="{{item.$orig.status>=2&&item.$orig.status<=5}}"><block><view class="btn2" data-id="{{item.$orig.id}}" data-express_no="{{item.$orig.express_no}}" data-express_type="{{item.$orig.express_type}}" data-event-opts="{{[['tap',[['logistics',['$event']]]]]}}" catchtap="__e">订单跟踪</view></block></block><block wx:if="{{item.$orig.status==-1||item.$orig.status==5}}"><block><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" catchtap="__e">删除订单</view></block></block></view></view></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="11f3b6aa-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="11f3b6aa-3" bind:__l="__l"></nodata></block></block></block><view class="back_index" style="{{('background:'+$root.m1)}}" data-url="/pages/index/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">首页</view></view>