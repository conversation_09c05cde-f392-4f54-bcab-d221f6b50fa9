
.text-min { font-size: 24rpx; color: #999;}
.ordertop{width:100%;height:220rpx;}
.ordertop .f1{color:#fff}
.ordertop .f1 .t1{font-size:32rpx;height:60rpx;line-height:60rpx}
.ordertop .f1 .t2{font-size:24rpx}
.orderinfo{width:700rpx;margin:0 auto;border-radius:8rpx;margin-top:16rpx;padding: 20rpx;background: #FFF;}
.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}
.orderinfo .item:last-child{ border-bottom: 0;}
.orderinfo .item .t1{width:200rpx;flex-shrink:0}
.orderinfo .item .t2{flex:1;text-align:right}
.orderinfo .item .t3{ margin-top: 3rpx;}
.orderinfo .item .red{color:red}
.first_title{
    width: 100%;
    overflow: hidden;
    font-size: 30rpx;
    line-height: 40rpx;
}
.black{width: 16rpx;height: 16rpx;background-color: #000;border-radius: 50%;float: left;margin-top: 14rpx;}
.red{width: 16rpx;height: 16rpx;background-color:#FF3A51;border-radius: 50%;float: left;margin-top: 14rpx;}
.red_view{overflow: hidden;background-color:#F6F6F6;border-radius: 12rpx;padding: 20rpx;}
.bottom{ width: 100%; height:calc(92rpx + env(safe-area-inset-bottom));background: #fff; position: fixed; bottom: 0px;left: 0px;display:flex;justify-content:flex-end;align-items:center;padding: 0 20rpx;}
.btn { border-radius: 10rpx;color: #fff;}
.btn1{height:60rpx;line-height:60rpx;color:#fff;border-radius:3px;text-align:center;flex-shrink: 0;margin: 0 0 0 15rpx;padding: 0 15rpx;}
.btn2{height:60rpx;line-height:60rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;padding: 0 15rpx;flex-shrink: 0;margin: 0 0 0 15rpx;}
.btn3{font-size:24rpx;height:50rpx;line-height:50rpx;color:#333;background:#fff;border:1px solid #cdcdcd;border-radius:3px;text-align:center;padding: 0 15rpx;flex-shrink: 0;margin: 0 0 0 15rpx;}


