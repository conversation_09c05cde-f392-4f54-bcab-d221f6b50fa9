<view class="container"><block wx:if="{{isload}}"><block><view class="ordertop" style="{{('background:url('+(pre_url+'/static/img/ordertop.png')+');background-size:100%;overflow:hidden')}}"><view style="width:660rpx;margin:0 auto;margin-top:70rpx;"><block wx:if="{{detail.status==0}}"><view class="f1"><view class="t1">待付款</view></view></block><block wx:if="{{detail.status==1}}"><view class="f1"><view class="t1">待接单</view></view></block><block wx:if="{{detail.status==2}}"><view class="f1"><view class="t1">已接单</view></view></block><block wx:if="{{detail.status==3}}"><view class="f1"><view class="t1">已到店</view></view></block><block wx:if="{{detail.status==4}}"><view class="f1"><view class="t1">配送中</view></view></block><block wx:if="{{detail.status==5}}"><view class="f1"><view class="t1">已完成</view></view></block><block wx:if="{{detail.status==-1}}"><view class="f1"><view class="t1">已取消</view></view></block><block wx:if="{{detail.status==-2}}"><view class="f1"><view class="t1">退款失败</view></view></block></view></view><view style="width:100%;background-color:#fff;padding:20rpx 0;"><block wx:if="{{detail.btntype==1}}"><view style="width:700rpx;margin:0 auto;background-color:#fff;"><view style="overflow:hidden;border-bottom:2rpx #f4f4f4 solid;line-height:80rpx;"><view style="float:left;">帮我送</view></view><view style="overflow:hidden;padding:20rpx;" data-url="{{'detail?id='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="black"></view><view style="float:left;margin-left:20rpx;width:580rpx;"><view class="first_title">{{''+detail.take_area+''}}</view><view style="font-size:30rpx;color:#666666;line-height:50rpx;">{{''+detail.take_address+''}}</view><view style="font-size:24rpx;color:#666666;line-height:50rpx;">{{''+detail.take_name+" "+detail.take_tel+''}}</view></view></view><view class="red_view"><view class="red"></view><view style="float:left;margin-left:20rpx;width:580rpx;"><view class="first_title">{{''+detail.send_area+''}}</view><view style="font-size:30rpx;color:#666666;line-height:50rpx;">{{''+detail.send_address+''}}</view><view style="font-size:24rpx;color:#666666;line-height:50rpx;">{{''+detail.send_name+" "+detail.send_tel+''}}</view></view></view></view></block><block wx:if="{{detail.btntype==2}}"><view style="width:700rpx;margin:0 auto;background-color:#fff;"><view style="overflow:hidden;border-bottom:2rpx #f4f4f4 solid;line-height:80rpx;"><view style="float:left;">帮我取</view></view><view class="red_view" style="margin-top:0;" data-url="{{'detail?id='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="red"></view><view style="float:left;margin-left:20rpx;width:580rpx;"><view class="first_title">{{''+detail.take_area+''}}</view><view style="font-size:30rpx;color:#666666;line-height:50rpx;">{{''+detail.take_address+''}}</view><view style="font-size:24rpx;color:#666666;line-height:50rpx;">{{''+detail.take_name+" "+detail.take_tel+''}}</view></view></view><view style="overflow:hidden;padding:20rpx;margin-top:20rpx;"><view class="black"></view><view style="float:left;margin-left:20rpx;width:580rpx;"><view class="first_title">{{''+detail.send_area+''}}</view><view style="font-size:30rpx;color:#666666;line-height:50rpx;">{{''+detail.send_address+''}}</view><view style="font-size:24rpx;color:#666666;line-height:50rpx;">{{''+detail.send_name+" "+detail.send_tel+''}}</view></view></view></view></block></view><view class="orderinfo"><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{detail.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{detail.createtime}}</text></view><block wx:if="{{detail.status>0&&detail.paytypeid!='4'&&detail.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{detail.paytime}}</text></view></block><block wx:if="{{detail.paytypeid}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{detail.paytype}}</text></view></block><block wx:if="{{detail.status>=2&&detail.starttime}}"><view class="item"><text class="t1">接单时间</text><text class="t2">{{detail.starttime}}</text></view></block><block wx:if="{{detail.status>=3&&detail.daodiantime}}"><view class="item"><text class="t1">到店时间</text><text class="t2">{{detail.daodiantime}}</text></view></block><block wx:if="{{detail.status>=4&&detail.quhuotime}}"><view class="item"><text class="t1">取货时间</text><text class="t2">{{detail.quhuotime}}</text></view></block><block wx:if="{{detail.status==5&&detail.endtime}}"><view class="item"><text class="t1">完成时间</text><text class="t2">{{detail.endtime}}</text></view></block></view><view class="orderinfo"><view class="item"><text class="t1">物品</text><text class="t2">{{detail.name}}</text></view><block wx:if="{{detail.pic}}"><view class="item"><text class="t1">物品图片</text><view class="t2"><image style="width:400rpx;height:auto;" src="{{detail.pic}}" mode="widthFix" data-url="{{detail.pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><view class="item"><text class="t1">重量</text><text class="t2">{{detail.weight+"公斤"}}</text></view><view class="item"><text class="t1">取件时间</text><text class="t2">{{detail.take_time}}</text></view><view class="item"><text class="t1">备注</text><text class="t2">{{detail.remark}}</text></view><block wx:if="{{detail.distance_fee>0}}"><view class="item"><text class="t1">距离费用</text><text class="t2" style="color:red;">{{"+¥"+detail.distance_fee}}</text></view></block><block wx:if="{{detail.weight_fee>0}}"><view class="item"><text class="t1">重量费用</text><text class="t2" style="color:red;">{{"+¥"+detail.weight_fee}}</text></view></block><block wx:if="{{detail.tip_fee>0}}"><view class="item"><text class="t1">小费</text><text class="t2" style="color:red;">{{"+¥"+detail.tip_fee}}</text></view></block><block wx:if="{{detail.time_fee>0}}"><view class="item"><text class="t1">特殊时间段附加</text><text class="t2" style="color:red;">{{"+¥"+detail.time_fee}}</text></view></block><block wx:if="{{detail.dt_fee>0}}"><view class="item"><text class="t1">动态溢价</text><text class="t2" style="color:red;">{{"+¥"+detail.dt_fee}}</text></view></block><view class="item"><text class="t1">实付款</text><text class="t2" style="color:red;">{{'¥'+detail.totalprice}}</text></view><block wx:if="{{detail.cancel_fee>0}}"><view class="item"><text class="t1">违约金</text><text class="t2" style="color:red;">{{"¥"+detail.cancel_fee}}</text></view></block><block wx:if="{{detail.refund_status==2&&detail.refund_money>0}}"><view class="item"><text class="t1">已退款</text><text class="t2" style="color:red;">{{"¥"+detail.refund_money}}</text></view></block><block wx:if="{{detail.status!=-1&&detail.refund_status==1&&detail.refund_money>0}}"><view class="item"><text class="t1">退款状态</text><text class="t2" style="color:red;">等待退款</text></view></block><block wx:if="{{detail.status!=-1&&detail.refund_status==-1}}"><view class="item"><text class="t1">退款状态</text><text class="t2" style="color:red;">退款驳回</text></view></block><block wx:if="{{detail.status!=-1&&detail.refund_status==-2&&detail.refund_money>0}}"><view class="item"><text class="t1">退款状态</text><text class="t2" style="color:red;">退款失败</text></view></block></view><view style="width:100%;height:calc(160rpx + env(safe-area-inset-bottom));"></view><block wx:if="{{detail.status!=-1}}"><view class="bottom notabbarbot"><block wx:if="{{detail.status>=0&&detail.status<=cancel_status}}"><block><view class="btn2" data-id="{{detail.id}}" data-type="1" data-event-opts="{{[['tap',[['tocancel',['$event']]]]]}}" catchtap="__e">取消订单</view></block></block><block wx:if="{{detail.status==-2}}"><block><view class="btn2" data-id="{{detail.id}}" data-type="2" data-event-opts="{{[['tap',[['tocancel',['$event']]]]]}}" catchtap="__e">退款</view></block></block><block wx:if="{{detail.status==5&&end_refund_status}}"><block><block wx:if="{{detail.refund_status==1}}"><block><block wx:if="{{detail.refund_money<=0}}"><view class="btn2" data-id="{{detail.id}}" data-type="20" data-event-opts="{{[['tap',[['tocancel',['$event']]]]]}}" catchtap="__e">申请退款</view></block></block></block><block wx:else><block><block wx:if="{{detail.refund_status!=2}}"><view class="btn2" data-id="{{detail.id}}" data-type="20" data-event-opts="{{[['tap',[['tocancel',['$event']]]]]}}" catchtap="__e">申请退款</view></block></block></block></block></block><block wx:if="{{detail.status==0}}"><block><block wx:if="{{detail.paytypeid!=5}}"><view class="btn1" style="{{'background:'+($root.m0)+';'}}" data-url="{{'/pagesExt/pay/pay?id='+detail.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去付款</view></block></block></block><block wx:if="{{detail.status>=2&&detail.status<=4}}"><block><view class="btn2" data-id="{{detail.id}}" data-express_no="{{detail.express_no}}" data-express_type="{{detail.express_type}}" data-event-opts="{{[['tap',[['logistics',['$event']]]]]}}" catchtap="__e">订单跟踪</view></block></block><block wx:if="{{detail.status==-1||detail.status==5}}"><block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" catchtap="__e">删除订单</view></block></block></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="62e28bd0-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="62e28bd0-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="62e28bd0-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>