
.container {
    display: flex;
    flex-direction: column
}
.addfromwx {
    width: 94%;
    margin: 20rpx 3% 0 3%;
    border-radius: 5px;
    padding: 20rpx 3%;
    background: #FFF;
    display: flex;
    align-items: center;
    color: #666;
    font-size: 28rpx;
}
.addfromwx .img {
    width: 40rpx;
    height: 40rpx;
    margin-right: 20rpx;
}
.form {
    width: 94%;
    margin: 20rpx 3%;
    border-radius: 5px;
    padding: 0 3%;
    background: #FFF;
    overflow: hidden;
}
.form-item {
    display: flex;
    align-items: center;
    width: 100%;
    border-bottom: 1px #ededed solid;
    height: 98rpx;
}
.form-item:last-child {
    border: 0
}
.form-item .label {
    color: #8B8B8B;
    font-weight: bold;
    height: 60rpx;
    line-height: 60rpx;
    text-align: left;
    width: 160rpx;
    padding-right: 20rpx
}
.form-item .input {
    flex: 1;
    height: 60rpx;
    line-height: 60rpx;
    text-align: right
}
.savebtn {
    width: 100%;
    height: 96rpx;
    line-height: 96rpx;
    text-align: center;
    border-radius: 12rpx;
    color: #fff;
    font-weight: bold;
    margin: 30rpx 0;
    border: none;
}
.topsearch{width:94%;margin:16rpx 3%;}
.topsearch .f1{height:60rpx;border-radius:30rpx;border:0;background-color:#fff;flex:1}
.topsearch .f1 .img{width:24rpx;height:24rpx;margin-left:10px}
.topsearch .f1 input{height:100%;flex:1;padding:0 20rpx;font-size:28rpx;color:#333;}
.content{width:94%;margin:10rpx 3%;background:#fff;border-radius:5px;padding:20rpx 40rpx;}
.content .f1{height:96rpx;line-height:96rpx;display:flex;align-items:center}
.content .f1 .t1{color:#2B2B2B;font-weight:bold;font-size:30rpx}
.content .f1 .t2{color:#999999;font-size:28rpx;margin-left:10rpx}
.content .f1 .t3{width:28rpx;height:28rpx}
.content .f2{color:#2b2b2b;font-size:26rpx;line-height:42rpx;padding-bottom:20rpx;}
.content .f3{height:96rpx;display:flex;align-items:center}
.content .radio{flex-shrink:0;width: 36rpx;height: 36rpx;background: #FFFFFF;border: 3rpx solid #BFBFBF;border-radius: 50%;}
.content .radio .radio-img{width:100%;height:100%}
.content .mrtxt{color:#2B2B2B;font-size:26rpx;margin-left:10rpx}
.content .del{font-size:24rpx}
.container .btn-add{width:90%;max-width:700px;margin:0 auto;height:96rpx;line-height:96rpx;text-align:center;color:#fff;font-size:30rpx;font-weight:bold;border-radius:40rpx;position: fixed;left:0px;right:0;bottom:0;margin-bottom:20rpx;}
.container .btn-add2{width:43%;max-width:700px;margin:0 auto;height:96rpx;line-height:96rpx;text-align:center;color:#fff;font-size:30rpx;font-weight:bold;border-radius:40rpx;position: fixed;left:5%;bottom:0;margin-bottom:20rpx;}
.container .btn-add3{width:43%;max-width:700px;margin:0 auto;height:96rpx;line-height:96rpx;text-align:center;color:#fff;font-size:30rpx;font-weight:bold;border-radius:40rpx;position: fixed;right:5%;bottom:0;margin-bottom:20rpx;}


