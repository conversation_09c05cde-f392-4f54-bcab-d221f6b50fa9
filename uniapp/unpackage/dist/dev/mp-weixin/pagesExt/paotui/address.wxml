<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="form"><view class="form-item"><text class="label">姓 名</text><input class="input" type="text" placeholder="请输入姓名" placeholder-style="font-size:28rpx;color:#BBBBBB" name="name" value="{{name}}"/></view><block wx:if="{{showCompany}}"><view class="form-item"><text class="label">公 司</text><input class="input" type="text" placeholder="请输入公司或单位名称" placeholder-style="font-size:28rpx;color:#BBBBBB" name="company" value="{{company}}"/></view></block><view class="form-item"><text class="label">手机号</text><input class="input" type="number" placeholder="请输入手机号" placeholder-style="font-size:28rpx;color:#BBBBBB" name="tel" value="{{tel}}"/></view><view class="form-item"><text class="label flex0">选择位置</text><text data-event-opts="{{[['tap',[['selectzuobiao',['$event']]]]]}}" class="flex1" style="{{'text-align:right;'+(area?'':'color:#BBBBBB')}}" bindtap="__e">{{area?area:'请选择您的位置'}}</text></view><view class="form-item"><text class="label">{{$root.m0}}</text><input class="input" type="text" placeholder="{{'请输入'+$root.m1}}" placeholder-style="font-size:28rpx;color:#BBBBBB" name="address" value="{{address}}"/></view><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m2+' 0%,rgba('+$root.m3+',0.8) 100%)')}}" form-type="submit">保存并使用</button></view></form><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="content" data-id="{{item.id}}" data-name="{{item.name}}" data-tel="{{item.tel}}" data-area="{{item.area}}" data-address="{{item.address}}" data-longitude="{{item.longitude}}" data-latitude="{{item.latitude}}" data-event-opts="{{[['tap',[['setdefault',['$event']]]]]}}" catchtap="__e"><view class="f1"><text class="t1">{{item.name}}</text><text class="t2">{{item.tel}}</text><block wx:if="{{item.company}}"><text class="t2">{{item.company}}</text></block><text class="flex1"></text><image class="t3" src="{{pre_url+'/static/img/edit.png'}}" data-url="{{'/pagesB/address/addressadd?id='+item.id+'&type=1'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"></image></view><view class="f2">{{item.area+" "+item.address}}</view></view></block><block wx:if="{{nodata}}"><nodata vue-id="23dc24e3-1" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="23dc24e3-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="23dc24e3-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="23dc24e3-4" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="23dc24e3-5" bind:__l="__l"></wxxieyi></view>