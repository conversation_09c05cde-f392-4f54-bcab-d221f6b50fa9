<view class="container"><block wx:if="{{isload}}"><block><view class="search-container" data-url="search" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="search-box"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><view class="search-text">搜索感兴趣的商品</view></view></view><view class="content-container"><view class="nav_left"><view class="{{['nav_left_items '+(curIndex==-1?'active':'')]}}" data-index="-1" data-id="0" data-event-opts="{{[['tap',[['switchRightTab',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+($root.m0)+';'}}"></view>全部</view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['nav_left_items '+(curIndex==index?'active':'')]}}" data-index="{{index}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['switchRightTab',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+(item.m1)+';'}}"></view>{{item.$orig.name}}</view></block></block></view><view class="nav_right"><view class="nav_right-content"><block wx:if="{{$root.g0}}"><view class="classify-ul"><view class="flex" style="width:100%;overflow-y:hidden;overflow-x:scroll;"><view class="classify-li" style="{{(curIndex2==-1?'color:'+$root.m2+';background:rgba('+$root.m3+',0.2)':'')}}" data-id="{{clist[curIndex].id}}" data-index="-1" data-event-opts="{{[['tap',[['changeCTab',['$event']]]]]}}" bindtap="__e">全部</view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="idx2" wx:key="idx2"><block><view class="classify-li" style="{{(curIndex2==idx2?'color:'+item.m4+';background:rgba('+item.m5+',0.2)':'')}}" data-id="{{item.$orig.id}}" data-index="{{idx2}}" data-event-opts="{{[['tap',[['changeCTab',['$event']]]]]}}" bindtap="__e">{{item.$orig.name}}</view></block></block></view></view></block><scroll-view class="classify-box" scroll-y="true" data-event-opts="{{[['scrolltolower',[['scrolltolower',['$event']]]]]}}" bindscrolltolower="__e"><view class="product-itemlist"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" data-type="{{item.$orig.type?item.$orig.type:0}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['toDetail',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image></view><view class="product-info"><view class="p1"><text>{{item.$orig.name}}</text></view><view class="p2"><block wx:if="{{item.$orig.show_free&&item.$orig.sell_price<=0}}"><view class="t1" style="{{'color:'+(item.m6)+';'}}">免费</view></block><block wx:else><view class="t1" style="{{'color:'+(item.m7)+';'}}">{{item.$orig.sell_price}}<text style="font-size:24rpx;padding-left:2px;">{{"元/"+item.$orig.danwei}}</text></view></block><block wx:if="{{item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view><view class="p3"><block wx:if="{{item.$orig.sales>0}}"><view class="p3-1"><text style="overflow:hidden;">{{"已售"+item.$orig.sales+"件"}}</text></view></block></view></view></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="7ea80122-1" text="没有更多商品了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="7ea80122-2" text="暂无相关商品" bind:__l="__l"></nodata></block><view style="width:100%;height:100rpx;"></view></scroll-view></view></view></view><block wx:if="{{buydialogShow}}"><buydialog vue-id="7ea80122-3" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]]]}}" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block></block></block><block wx:if="{{loading}}"><loading vue-id="7ea80122-4" loadstyle="left:62.5%" bind:__l="__l"></loading></block><dp-tabbar vue-id="7ea80122-5" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="7ea80122-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>