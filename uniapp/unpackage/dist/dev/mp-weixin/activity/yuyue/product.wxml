<view><block wx:if="{{isload}}"><block><block wx:if="{{sysset.showgzts}}"><block><view style="width:100%;height:88rpx;"></view><view class="follow_topbar"><view class="headimg"><image src="{{sysset.logo}}"></image></view><view class="info"><view class="i">欢迎进入<text style="{{'color:'+($root.m0)+';'}}">{{sysset.name}}</text></view><view class="i">关注公众号享更多专属服务</view></view><view data-event-opts="{{[['tap',[['showsubqrcode',['$event']]]]]}}" class="sub" style="{{'background-color:'+($root.m1)+';'}}" bindtap="__e">立即关注</view></view><uni-popup class="vue-ref" vue-id="a29a246a-1" id="qrcodeDialog" type="dialog" data-ref="qrcodeDialog" bind:__l="__l" vue-slots="{{['default']}}"><view class="qrcodebox"><image class="img" src="{{sysset.qrcode}}" data-url="{{sysset.qrcode}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">长按识别二维码关注</view><view data-event-opts="{{[['tap',[['closesubqrcode',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="{{pre_url+'/static/img/close2.png'}}"></image></view></view></uni-popup></block></block><dp-guanggao vue-id="a29a246a-2" guanggaopic="{{guanggaopic}}" guanggaourl="{{guanggaourl}}" bind:__l="__l"></dp-guanggao><block wx:if="{{$root.g0>0}}"><view style="position:fixed;top:15vh;left:20rpx;z-index:9;background:rgba(0,0,0,0.6);border-radius:20rpx;color:#fff;padding:0 10rpx;"><swiper style="position:relative;height:54rpx;width:350rpx;" autoplay="{{true}}" interval="{{5000}}" vertical="{{true}}"><block wx:for="{{bboglist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="flex-y-center" data-url="{{'/yuyue/yuyue/product?id='+item.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px;" src="{{item.headimg}}"></image><view style="width:300rpx;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:22rpx;">{{item.nickname+" "+item.showtime+"购买了该商品"}}</view></swiper-item></block></swiper></view></block><block wx:if="{{isplay==0}}"><view class="swiper-container"><swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" interval="{{500000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{product.pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item class="swiper-item"><view class="swiper-item-view"><image class="img" src="{{item}}" mode="widthFix"></image></view></swiper-item></block></block></swiper><block wx:if="{{$root.g1>1}}"><view class="imageCount">{{current+1+"/"+$root.g2}}</view></block><block wx:if="{{product.video}}"><view data-event-opts="{{[['tap',[['payvideo',['$event']]]]]}}" class="provideo" bindtap="__e"><image src="{{pre_url+'/static/img/video.png'}}"></image><view class="txt">{{product.video_duration}}</view></view></block></view></block><view class="header"><view class="price_share"><view class="title">{{product.name}}</view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/share.png'}}"></image><text class="txt">分享</text></view></view><view class="pricebox flex"><view class="price"><block wx:if="{{set.show_free&&product.min_price<=0&&product.max_price<=0}}"><view class="f1" style="{{'color:'+($root.m2)+';'}}">免费</view></block><block wx:else><view class="f1" style="{{'color:'+($root.m3)+';'}}">{{''+product.min_price}}<block wx:if="{{product.max_price!=product.min_price}}"><text>{{"-"+product.max_price}}</text></block><text style="font-size:24rpx;font-weight:normal;padding-left:6rpx;">{{"元/"+product.danwei}}</text></view></block><block wx:if="{{product.market_price*1>product.sell_price*1}}"><view class="f2">{{"￥"+product.market_price}}<block wx:if="{{product.max_price!=product.min_price}}"><text>起</text></block></view></block></view><view class="sales_stock"><view class="f1">{{"已售："+product.sales+''}}</view></view></view><block wx:if="{{product.sellpoint}}"><view class="sellpoint">{{product.sellpoint}}</view></block><block wx:if="{{product.balance_price>0}}"><view style="margin:20rpx 0;color:red;font-size:22rpx;">{{"定金金额："+product.advance_price+"元，尾款金额："+product.balance_price+"元"}}</view></block><block wx:if="{{$root.g3>0}}"><view class="cuxiaodiv"><block wx:if="{{$root.g4>0}}"><view class="fuwupoint"><view data-event-opts="{{[['tap',[['showfuwudetail',['$event']]]]]}}" class="f1" bindtap="__e"><block wx:for="{{fuwulist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="t">{{item.name}}</view></block></view><view data-event-opts="{{[['tap',[['showfuwudetail',['$event']]]]]}}" class="f2" bindtap="__e"><image src="{{pre_url+'/static/img/arrow-point.png'}}" mode="widthFix"></image></view></view></block></view></block></view><block wx:if="{{$root.g5>0}}"><view class="cuxiaopoint"><view class="f0">优惠</view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f1" bindtap="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="t" style="{{'background:'+('rgba('+item.m4+',0.1)')+';'+('color:'+(item.m5)+';')}}"><text class="t0" style="padding:0 6px;">券</text><text class="t1">{{item.$orig.name}}</text></view></block></view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f2" bindtap="__e"><image src="{{pre_url+'/static/img/arrow-point.png'}}" mode="widthFix"></image></view></view></block><view class="choosebox"><block wx:if="{{showselectpeople&&product.fwpeople==1}}"><view data-event-opts="{{[['tap',[['gotopeople',['$event']]]]]}}" class="choosedate" style="border-bottom:2rpx solid #eee;" bindtap="__e"><view class="f0">服务人员</view><view class="f1 flex1">{{worker?worker.realname:'请选择人员'}}</view><image class="f2" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><view class="choose" data-btntype="2" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" bindtap="__e"><view class="f0">选择服务</view><view class="flex1 flex-y-center"><text class="xuanzefuwu-text">{{ggname}}</text><block wx:if="{{num}}"><text>{{"× "+num}}</text></block></view><image class="f2" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view><block wx:if="{{!isfuwu}}"><block><block wx:if="{{!selmoretime}}"><block><view data-event-opts="{{[['tap',[['chooseTime',['$event']]]]]}}" class="choosedate" bindtap="__e"><view class="f0">服务时间</view><view class="f1 flex1">{{yydate}}</view><image class="f2" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block></block><block wx:else><block><view data-event-opts="{{[['tap',[['chooseTime2',['$event']]]]]}}" class="choosedate" bindtap="__e"><view class="f0">服务时间</view><view class="f1 flex1">{{yydate}}</view><image class="f2" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view><view class="choosedate"><view class="f0">已选数量</view><view class="f1 flex1">{{product.timejg+"分钟 X "+sortsnum}}</view></view></block></block></block></block></view><block wx:if="{{commentcount>0}}"><view class="commentbox"><view class="title"><view class="f1">{{"评价("+commentcount+")"}}</view><view class="f2" data-url="{{'commentlist?proid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">好评率<text style="{{'color:'+($root.m6)+';'}}">{{product.comment_haopercent+"%"}}</text><image style="width:32rpx;height:32rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view><view class="comment"><block wx:if="{{$root.g6>0}}"><view class="item"><view class="f1"><image class="t1" src="{{commentlist[0].headimg}}"></image><view class="t2">{{commentlist[0].nickname}}</view><view class="flex1"></view><view class="t3"><block wx:for="{{[0,1,2,3,4]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><image class="img" src="{{pre_url+'/static/img/star'+(commentlist[0].score>item2?'2native':'')+'.png'}}"></image></block></view></view><view class="f2"><text class="t1">{{commentlist[0].content}}</text><view class="t2"><block wx:if="{{commentlist[0].content_pic!=''}}"><block><block wx:for="{{commentlist[0].content_pic}}" wx:for-item="itemp" wx:for-index="index" wx:key="index"><block><view data-url="{{itemp}}" data-urls="{{commentlist[0].content_pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"><image src="{{itemp}}" mode="widthFix"></image></view></block></block></block></block></view></view><view class="f3" data-url="{{'commentlist?proid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看全部评价</view></view></block><block wx:else><view class="nocomment">暂无评价~</view></block></view></view></block><block wx:if="{{shopset.showjd==1}}"><view class="shop"><image class="p1" src="{{business.logo}}"></image><view class="p2 flex1"><view class="t1">{{business.name}}</view><view class="t2">{{business.desc}}</view></view><button class="p4" style="{{'background:'+('linear-gradient(90deg,'+$root.m7+' 0%,rgba('+$root.m8+',0.8) 100%)')+';'}}" data-url="{{product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid}}" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">进入店铺</button></view></block><view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">服务详情</view><view class="t2"></view><view class="t1"></view></view><view class="detail"><dp vue-id="a29a246a-3" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></view><view style="width:100%;height:140rpx;"></view><block wx:if="{{product.status==1}}"><view class="{{['bottombar','flex-row',menuindex>-1?'tabbarbot':'']}}"><view class="f1"><view class="item" data-url="{{'prolist?bid='+product.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/shou.png'}}"></image><view class="t1">首页</view></view><block wx:if="{{kfurl!='contact::'}}"><view class="item" data-url="{{kfurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/kefu.png'}}"></image><view class="t1">客服</view></view></block><block wx:else><button class="item" open-type="contact" show-message-card="true"><image class="img" src="{{pre_url+'/static/img/kefu.png'}}"></image><view class="t1">客服</view></button></block><view data-event-opts="{{[['tap',[['addfavorite',['$event']]]]]}}" class="item" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/shoucang.png'}}"></image><view class="t1">{{isfavorite?'已收藏':'收藏'}}</view></view></view><view class="op"><block wx:if="{{isfuwu}}"><view class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m9)+';'}}" data-btntype="2" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" bindtap="__e">立即预约</view></block><block wx:else><view data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m10)+';'}}" bindtap="__e">立即预约</view></block></view></view></block><block wx:if="{{buydialogShow}}"><yybuydialog vue-id="a29a246a-4" proid="{{product.id}}" btntype="{{btntype}}" menuindex="{{menuindex}}" isfuwu="{{isfuwu}}" data-event-opts="{{[['^currgg',[['currgg']]],['^buydialogChange',[['buydialogChange']]],['^addcart',[['addcart']]],['^tobuy',[['tobuy']]]]}}" bind:currgg="__e" bind:buydialogChange="__e" bind:addcart="__e" bind:tobuy="__e" bind:__l="__l"></yybuydialog></block><scrolltop vue-id="a29a246a-5" isshow="{{scrolltopshow}}" bind:__l="__l"></scrolltop><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m11=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/sharefriends.png'}}"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m12=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/sharefriends.png'}}"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m13!='h5'}}"><button class="f1" open-type="share"><image class="img" src="{{pre_url+'/static/img/sharefriends.png'}}"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/sharepic.png'}}"></image><text class="t1">生成分享图片</text></view></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/close.png'}}"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block><block wx:if="{{timeDialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hidetimeDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择时间</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hidetimeDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="order-tab"><view class="order-tab2"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['item '+(curTopIndex==index?'on':'')]}}" data-index="{{index}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e"><view class="datetext">{{item.$orig.weeks}}</view><view class="datetext2">{{item.$orig.date}}</view><view class="after" style="{{'background:'+(item.m14)+';'}}"></view></view></block></block></view></view><view class="flex daydate"><block wx:for="{{timelist}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><block><view class="{{['date '+(timeindex==index2&&item.status==1?'on':'')+(item.status==0?'hui':'')]}}" data-index2="{{index2}}" data-status="{{item.status}}" data-time="{{item.timeint}}" data-event-opts="{{[['tap',[['switchDateTab',['$event']]]]]}}" bindtap="__e">{{''+item.time}}</view></block></block></view><view class="op"><button data-event-opts="{{[['tap',[['selectDate',['$event']]]]]}}" class="tobuy on" style="{{'background-color:'+($root.m15)+';'}}" bindtap="__e">确 定</button></view></view></view></block><block wx:if="{{timeDialogShow2}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hidetimeDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:1000rpx;"><view class="popup__title2"><image class="popup_close2" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/biao.png'}}" data-event-opts="{{[['tap',[['hidetimeDialog',['$event']]]]]}}" catchtap="__e"></image><text class="popup__title-text">选择时间</text></view><view class="order-tab"><view class="order-tab3"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['item '+(curTopIndex==index?'on':'')]}}" data-index="{{index}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" catchtap="__e"><view class="datetext">{{item.$orig.weeks}}</view><view class="datetext2">{{item.$orig.date}}</view><view class="after" style="{{'background:'+(item.m16)+';'}}"></view></view></block></block></view></view><view class="daydate2"><view class="datebox"><block wx:for="{{timelist}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><block><view class="{{['date '+(timeindex==index2&&item.status==1?'on':'')+(item.status==0?'hui':'')]}}" data-index2="{{index2}}" data-status="{{item.status}}" data-time="{{item.timeint}}" data-event-opts="{{[['tap',[['switchDateTab',['$event']]]]]}}" catchtap="__e"><text class="t1">{{item.time}}</text><block wx:if="{{product.fwpeople==0}}"><text class="t2">{{"剩余 "+item.stock}}</text></block></view></block></block></view></view><view class="op"><button data-event-opts="{{[['tap',[['selectDate',['$event']]]]]}}" class="tobuy on" style="{{'background-color:'+($root.m17)+';'}}" bindtap="__e">确 定</button></view></view></view></block><block wx:if="{{timeDialogShow3}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hidetimeDialog2',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择时间</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hidetimeDialog2',['$event']]]]]}}" catchtap="__e"></image></view><view class="order-tab"><view class="order-tab2"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['item '+(curTopIndex==index?'on':'')]}}" data-index="{{index}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['switchTopTab2',['$event']]]]]}}" bindtap="__e"><view class="datetext">{{item.$orig.weeks}}</view><view class="datetext2">{{item.$orig.date}}</view><view class="after" style="{{'background:'+(item.m18)+';'}}"></view></view></block></block></view></view><block wx:for="{{datetimes}}" wx:for-item="item2" wx:for-index="index2" wx:key="*this"><block><block wx:if="{{curTopIndex==index2}}"><view class="flex daydate" style="justify-content:space-around;font-size:26rpx;"><block wx:for="{{item2.times}}" wx:for-item="item3" wx:for-index="index3" wx:key="index3"><block><view class="{{['date '+(item3.issel&&item3.status==1?'on':'')+(item3.status==0?'hui':'')]}}" style="min-width:19%;width:auto;" data-sort="{{item3.sort}}" data-index="{{index2}}" data-index2="{{index3}}" data-status="{{item3.status}}" data-year="{{item2.year}}" data-date="{{item2.date}}" data-time="{{item3.time}}" data-time2="{{item3.time2}}" data-timeint="{{item3.timeint}}" data-event-opts="{{[['tap',[['switchDateTab2',['$event']]]]]}}" bindtap="__e">{{''+item3.timerange}}</view></block></block></view></block></block></block><view class="selsortsnum"><view>{{"已选数量(最少选择"+product.datetype1_modelselnum+"个连续时间段)"}}</view><view>{{product.timejg+"分钟 X "+sortsnum}}</view></view><view class="op"><button data-event-opts="{{[['tap',[['hidetimeDialog2',['$event']]]]]}}" class="tobuy on" style="{{'background-color:'+($root.m19)+';'}}" bindtap="__e">确定</button></view></view></view></block><block wx:if="{{showfuwudialog}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hidefuwudetail',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">服务</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hidefuwudetail',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{fuwulist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="service-item"><view class="fuwudialog-content"><view class="f1">{{item.name}}</view><text class="f2">{{item.desc}}</text></view></view></block></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="a29a246a-6" bind:__l="__l"></loading></block><dp-tabbar vue-id="a29a246a-7" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="a29a246a-8" data-ref="popmsg" bind:__l="__l"></popmsg></view>