<view class="container"><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入姓名/手机号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><view class="order-tab"><view class="order-tab2"><view class="{{['item '+(curTopIndex==-1?'on':'')]}}" data-index="{{-1}}" data-id="{{0}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">全部<view class="after" style="{{'background:'+($root.m0)+';'}}"></view></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['item '+(curTopIndex==index?'on':'')]}}" data-index="{{index}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">{{item.$orig.name}}<view class="after" style="{{'background:'+(item.m1)+';'}}"></view></view></block></block></view></view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="content flex" data-id="{{item.$orig.id}}"><view class="f1" data-url="{{'peodetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="headimg"><image src="{{item.$orig.headimg}}"></image></view><view class="text1"><text class="t1">{{item.$orig.realname+''}}</text><block wx:if="{{item.$orig.typename}}"><text class="t2">{{item.$orig.typename+''}}</text></block><view class="text2">{{item.$orig.jineng}}</view><view class="text3 flex"><view class="t4">服务<text>{{''+item.$orig.totalnum}}</text>次</view><view class="t5">评分<text>{{item.$orig.comment_score}}</text></view></view></view></view><view><view class="yuyue" style="{{'background:'+(item.m2)+';'}}" data-url="{{'/yuyue/yuyue/peodetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">预约</view></view></view></block><block wx:if="{{nodata}}"><nodata vue-id="310eeba2-1" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="310eeba2-2" bind:__l="__l"></nomore></block></block></block><block wx:if="{{loading}}"><loading vue-id="310eeba2-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="310eeba2-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="310eeba2-5" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="310eeba2-6" bind:__l="__l"></wxxieyi></view>