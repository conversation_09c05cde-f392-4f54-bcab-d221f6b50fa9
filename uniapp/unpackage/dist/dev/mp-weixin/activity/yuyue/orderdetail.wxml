<view class="container"><block wx:if="{{isload}}"><block><view class="ordertop flex" style="{{('background:url('+pre_url+'/static/img/orderbg.png);background-size:100%')}}"><block wx:if="{{detail.status==0}}"><view class="f1"><view class="t1">等待买家付款</view><block wx:if="{{djs}}"><view class="t2">{{"剩余时间："+djs}}</view></block></view></block><block wx:if="{{detail.status==1&&detail.refund_status==0}}"><view class="f1"><block wx:if="{{!detail.worker_id}}"><block><view class="t2">我们会尽快为您派单</view></block></block><block wx:else><block><view class="t2">订单已接单</view></block></block></view></block><block wx:if="{{detail.status==2}}"><view class="f1"><view class="t1">订单服务中</view></view></block><block wx:if="{{detail.status==3}}"><view class="f1"><view class="t1">订单已完成</view></view></block><block wx:if="{{detail.status==4}}"><view class="f1"><view class="t1">订单已取消</view></view></block><view class="orderx"><image src="{{pre_url+'/static/img/orderx.png'}}"></image></view></view><view class="orderinfo orderinfotop"><view class="title">订单信息</view><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{detail.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{detail.createtime}}</text></view><view class="item"><text class="t1">预约时间</text><text class="t2">{{detail.yy_time}}</text></view></view><block wx:if="{{$root.g0>0}}"><view class="orderinfo"><block wx:for="{{detail.formdata}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="item"><text class="t1">{{item[0]}}</text><block wx:if="{{item[2]=='upload'}}"><view class="t2"><image style="width:400rpx;height:auto;" src="{{item[1]}}" mode="widthFix" data-url="{{item[1]}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:else><block wx:if="{{item[2]=='upload_video'}}"><view class="t2"><video style="width:100%;" src="{{item[1]}}"></video></view></block><block wx:else><block wx:if="{{item[2]=='upload_pics'}}"><view class="t2"><block wx:for="{{item[1]}}" wx:for-item="vv" wx:for-index="__i1__" wx:key="*this"><block><image style="width:200rpx;height:auto;margin-right:10rpx;" src="{{vv}}" mode="widthFix" data-url="{{vv}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></block></block></view></block><block wx:else><text class="t2" user-select="true" selectable="true">{{item[1]}}</text></block></block></block></view></block></view></block><view class="product"><view class="title">服务信息</view><view class="content"><view data-url="{{'product?id='+prolist.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{prolist.propic}}"></image></view><view class="detail"><text class="t1">{{prolist.proname}}</text><view class="t2 flex flex-y-center flex-bt"><text>{{prolist.ggname}}</text><block wx:if="{{detail.status==3&&prolist.iscomment==0}}"><view class="btn3" data-url="{{'comment?oid='+prolist.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去评价</view></block><block wx:if="{{detail.status==3&&prolist.iscomment==1}}"><view class="btn3" data-url="{{'comment?oid='+prolist.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">查看评价</view></block></view><view class="t3"><text class="x1 flex1">{{"￥"+prolist.product_price}}</text><text class="x2">{{"×"+prolist.num}}</text></view></view></view></view><block wx:if="{{(detail.status==3||detail.status==2)&&(detail.freight_type==3||detail.freight_type==4)}}"><view class="orderinfo"><view class="item flex-col"><text class="t1" style="color:#111;">发货信息</text><text class="t2" style="text-align:left;margin-top:10rpx;padding:0 10rpx;" user-select="true" selectable="true">{{detail.freight_content}}</text></view></view></block><view class="orderinfo"><view class="item"><text class="t1">商品金额</text><text class="t2 red">{{"¥"+detail.product_price}}</text></view><view class="item"><text class="t1">订单金额</text><text class="t2 red">{{"¥"+detail.totalprice}}</text></view><block wx:if="{{detail.leveldk_money>0}}"><view class="item"><text class="t1">{{$root.m0+"折扣"}}</text><text class="t2 red">{{"-¥"+detail.leveldk_money}}</text></view></block><block wx:if="{{detail.manjian_money>0}}"><view class="item"><text class="t1">满减活动</text><text class="t2 red">{{"-¥"+detail.manjian_money}}</text></view></block><block wx:if="{{detail.freight_type==1&&detail.freightprice>0}}"><view class="item"><text class="t1">服务费</text><text class="t2 red">{{"+¥"+detail.freight_price}}</text></view></block><block wx:if="{{detail.freight_time}}"><view class="item"><text class="t1">{{(detail.freight_type!=1?'配送':'提货')+"时间"}}</text><text class="t2">{{detail.freight_time}}</text></view></block><block wx:if="{{detail.coupon_money>0}}"><view class="item"><text class="t1">{{$root.m1+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.coupon_money}}</text></view></block><block wx:if="{{detail.scoredk_money>0}}"><view class="item"><text class="t1">{{$root.m2+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.scoredk_money}}</text></view></block><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+detail.paymoney}}</text></view><block wx:if="{{detail.balance_price>0}}"><view class="item"><text class="t1">尾款</text><text class="t2 red">{{"¥"+detail.balance_price}}</text></view></block><block wx:if="{{detail.balance_price>0}}"><view class="item"><text class="t1">尾款状态</text><block wx:if="{{detail.balance_pay_status==1}}"><text class="t2">已支付</text></block><block wx:if="{{detail.balance_pay_status==0}}"><text class="t2">未支付</text></block></view></block><view class="item"><text class="t1">订单状态</text><block wx:if="{{detail.status==0}}"><text class="t2">未付款</text></block><block wx:if="{{detail.status==1}}"><text class="t2">已付款</text></block><block wx:if="{{detail.status==2}}"><text class="t2">服务中</text></block><block wx:if="{{detail.status==3}}"><text class="t2">已完成</text></block><block wx:if="{{detail.status==4}}"><text class="t2">已关闭</text></block><block wx:if="{{detail.refundCount}}"><text style="margin-left:8rpx;">{{"有退款("+detail.refundCount+")"}}</text></block></view><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款状态</text><block wx:if="{{detail.refund_status==1}}"><text class="t2 red">{{"审核中,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==2}}"><text class="t2 red">{{"已退款,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==3}}"><text class="t2 red">{{"已驳回,¥"+detail.refund_money}}</text></block></view></block><block wx:if="{{detail.status>0&&detail.paytypeid!='4'&&detail.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{detail.paytime}}</text></view></block><block wx:if="{{detail.paytypeid}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{detail.paytype}}</text></view></block><block wx:if="{{detail.status>1&&detail.send_time}}"><view class="item"><text class="t1">派单时间</text><text class="t2">{{detail.send_time}}</text></view></block><block wx:if="{{detail.status>1&&detail.addmoney>0}}"><view class="item"><text class="t1">补差价</text><text class="t2 red">{{"￥"+detail.addmoney}}</text></view></block><block wx:if="{{detail.status==3&&detail.collect_time}}"><view class="item"><text class="t1">完成时间</text><text class="t2">{{detail.collect_time}}</text></view></block><view class="item"><text class="t1">服务方式</text><text class="t2"><block wx:if="{{detail.fwtype==2}}"><block>{{''+text['上门服务']+''}}</block></block><block wx:if="{{detail.fwtype==3}}"><block>到商家服务</block></block><block wx:if="{{detail.fwtype==1}}"><block>{{''+text['到店服务']+''}}</block></block></text></view><block wx:if="{{detail.remark&&detail.show_remark}}"><view class="item"><text class="t1">备注</text><text class="t2" data-remark="{{detail.remark}}" data-event-opts="{{[['longpress',[['copyRemark',['$event']]]],['tap',[['gotourl',['$0'],['detail.remark_tourl']]]]]}}" bindlongpress="__e" bindtap="__e">{{detail.remark}}</text></view></block><block wx:if="{{detail.fwbid&&detail.fwbinfo}}"><block><view class="item"><text class="t1">商家名称</text><text class="t2">{{detail.fwbinfo.name}}</text></view><block wx:if="{{detail.fwbinfo.address}}"><view class="item"><view class="t1">商家地址</view><block wx:if="{{!detail.fwbinfo.latitude||!detail.fwbinfo.longitude}}"><view class="t2">{{''+detail.fwbinfo.address+''}}</view></block><block wx:else><view class="t2" data-latitude="{{detail.fwbinfo.latitude}}" data-longitude="{{detail.fwbinfo.longitude}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e">{{''+detail.fwbinfo.address+''}}</view></block></view></block></block></block></view><view class="orderinfo"><view class="title"><block wx:if="{{!detail.carid}}"><block>顾客信息</block></block><block wx:else><block><block wx:if="{{detail.fwtype==2}}"><block>{{''+text['上门服务']+''}}</block></block><block wx:if="{{detail.fwtype==3}}"><block>到商家服务</block></block><block wx:if="{{detail.fwtype==1}}"><block>{{''+text['到店服务']+''}}</block></block></block></block></view><view class="item"><text class="t1">姓名</text><text class="t2">{{detail.linkman}}</text></view><view class="item"><text class="t1">手机号</text><text class="t2">{{detail.tel}}</text></view><block wx:if="{{!detail.carid}}"><block><block wx:if="{{detail.fwtype==2}}"><view class="item"><text class="t1">上门地址</text><text class="t2">{{detail.area+" "+detail.address}}</text></view></block></block></block><block wx:else><block><block wx:if="{{detail.fwtype==2}}"><block><view class="item"><text class="t1">车辆位置</text><text class="t2">{{detail.area}}</text></view><view class="item"><text class="t1">停靠位置</text><text class="t2">{{detail.address}}</text></view></block></block><view class="item"><text class="t1">车辆信息</text><text class="t2">{{detail.car_number+"."+detail.car_color+"."+(detail.car_type==1?'SUV':detail.car_type==2?'MPV':'轿车')}}</text></view></block></block></view><view class="orderinfo"><view class="title">店铺信息</view><view class="item"><text class="t1">店铺名称</text><text class="t2">{{detail.binfo.name}}</text></view><view class="item"><text class="t1">店铺地址</text><text class="t2">{{detail.binfo.fulladdress}}</text></view></view><view style="width:100%;height:calc(160rpx + env(safe-area-inset-bottom));"></view><view class="bottom notabbarbot"><block wx:if="{{detail.isworkorder==1}}"><block><view class="btn2" data-url="{{'/pagesB/workorder/index?type=2&id='+detail.id}}" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">发起工单</view></block></block><block wx:if="{{detail.status==0}}"><block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['toclose',['$event']]]]]}}" bindtap="__e">关闭订单</view><block wx:if="{{detail.paytypeid!=5}}"><view class="btn1" style="{{'background:'+($root.m3)+';'}}" data-url="{{'/pagesExt/pay/pay?id='+detail.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">去付款</view></block><block wx:if="{{detail.paytypeid==5}}"><view class="btn1" style="{{'background:'+($root.m4)+';'}}" data-url="{{'/pages/pay/transfer?id='+detail.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">上传付款凭证</view></block></block></block><block wx:if="{{detail.status==1}}"><block><block wx:if="{{detail.paytypeid!='4'&&detail.refund_status==0||detail.refund_status==3}}"><block><view class="btn2" data-url="{{'refund?orderid='+detail.id+'&price='+detail.paymoney}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">退款</view></block></block></block></block><block wx:if="{{(detail.status==2||detail.status==3||detail.worker_id>0)&&detail.status!=4}}"><block><view class="btn2" data-express_com="{{detail.express_com}}" data-express_no="{{detail.worker_orderid}}" data-event-opts="{{[['tap',[['logistics',['$event']]]]]}}" bindtap="__e">查看进度</view></block></block><block wx:if="{{detail.status==2}}"><block><block wx:if="{{detail.balance_pay_status==0&&detail.balance_price>0}}"><view class="btn1" style="{{'background:'+($root.m5)+';'}}" data-url="{{'/pagesExt/pay/pay?id='+detail.balance_pay_orderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">支付尾款</view></block></block></block><block wx:if="{{detail.status==3||detail.status==4}}"><block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" bindtap="__e">删除订单</view></block></block><block wx:if="{{detail.bid>0&&detail.status==3}}"><block><block wx:if="{{iscommentdp==1}}"><view class="btn2" data-url="{{'commentdp?orderid='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看评价</view></block></block></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="2185159b-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="2185159b-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="2185159b-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>