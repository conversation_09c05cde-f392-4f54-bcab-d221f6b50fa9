<view class="container"><block wx:if="{{isload}}"><block><view><view class="search-container"><view class="search-box"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input class="search-text" placeholder="搜索商家" placeholder-style="color:#aaa;font-size:24rpx" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" bindconfirm="__e"/></view></view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><view class="order-box" data-url="{{'jdorderdetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="head"><block wx:if="{{item.fwtype!=2}}"><view class="fwtype1">到店</view></block><block wx:else><view class="fwtype2">上门</view></block><block wx:if="{{item.fwtype==1||item.fwtype==3}}"><view><block wx:if="{{item.status==3}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>已完成</view></block><block wx:if="{{item.status==1}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image><text class="t1">等待客户上门</text></view></block><block wx:if="{{item.status==2}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image><text class="t1" style="margin-left:10rpx;">服务中</text></view></block><block wx:if="{{item.status==10}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image><text class="t1" style="margin-left:10rpx;">已取消</text></view></block></view></block><block wx:else><block wx:if="{{item.fwtype==2}}"><view><block wx:if="{{item.status==3}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>已完成</view></block><block wx:else><block wx:if="{{item.status==1}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>期望上门时间<text class="t1">{{item.orderinfo.yydate}}</text></view></block><block wx:else><block wx:if="{{item.status==2}}"><block><block wx:if="{{showaddmoney}}"><view class="f1"><block wx:if="{{!item.sign_status}}"><block><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>已到达，等待服务</block></block><block wx:else><block><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>已到达，正在服务</block></block></view></block><block wx:else><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>已到达，服务中</view></block></block></block></block></block><block wx:if="{{item.status==1}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>已取消</view></block></view></block></block><view class="flex1"></view><view class="f2"><block wx:if="{{item.showprice}}"><view class="t1"><text>{{"￥"+item.order_totalprice}}</text><block wx:if="{{item.ticheng>0}}"><text class="t11">{{"(￥"+item.ticheng+")"}}</text></block></view></block><block wx:else><block><text class="t1">{{item.ticheng}}</text>元</block></block></view></view><view class="content"><block wx:if="{{!item.orderinfo.protype}}"><block><block wx:if="{{item.fwtype==2}}"><view class="f1"><view class="t1"><text class="x1">{{item.juli}}</text><text class="x2">{{item.juli_unit}}</text></view><view class="t2"><image class="img" src="{{pre_url+'/static/img/peisong/ps_juli.png'}}"></image></view><view class="t3"><text class="x1">{{item.juli2}}</text><text class="x2">{{item.juli2_unit}}</text></view></view></block><view class="f2"><view class="t1">{{item.binfo.name}}</view><view class="t2">{{item.binfo.address}}</view><view class="t3">{{item.orderinfo.address}}</view><view class="t2">{{item.orderinfo.area}}</view></view><view class="f3" data-index="{{index}}" data-protype="0" data-fwtype="{{item.fwtype}}" data-event-opts="{{[['tap',[['daohang',['$event']]]]]}}" catchtap="__e"><image class="img" src="{{pre_url+'/static/img/peisong/ps_daohang.png'}}"></image></view></block></block><block wx:else><block><view class="f1" style="margin-top:38rpx;"><view class="t3"><text class="x1">{{item.juli2}}</text><text class="x2">{{item.juli2_unit}}</text></view></view><view class="f2"><view class="t3">{{item.orderinfo.address}}</view><view class="t2">{{item.orderinfo.area}}</view></view><view class="f3" data-index="{{index}}" data-protype="1" data-event-opts="{{[['tap',[['daohang',['$event']]]]]}}" catchtap="__e"><image class="img" src="{{pre_url+'/static/img/peisong/ps_daohang.png'}}"></image></view></block></block></view><block wx:if="{{item.fwtype==1||item.fwtype==3}}"><view class="op"><block wx:if="{{item.order_status==0}}"><view class="t3">用户待支付</view></block><block wx:if="{{item.order_status!=0}}"><block><block wx:if="{{item.status==1}}"><view class="t1">已接单，待顾客上门</view></block><block wx:if="{{item.status==2}}"><view class="t1">顾客已到达</view></block><block wx:if="{{item.status==3}}"><view class="t1">已完成</view></block></block></block><view class="flex1"></view><block wx:if="{{item.order_status==0}}"><view class="btn3" data-id="{{item.id}}" data-price="{{item.order_totalprice}}" data-event-opts="{{[['tap',[['changeprice',['$event']]]]]}}" catchtap="__e">改 价</view></block><block wx:if="{{item.order_status!=0}}"><block><block wx:if="{{item.status==1}}"><view class="btn1" data-id="{{item.id}}" data-st="2" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" catchtap="__e">顾客已到达</view></block><block wx:if="{{item.status==2}}"><view class="btn1" data-id="{{item.id}}" data-st="3" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" catchtap="__e">我已完成</view></block></block></block></view></block><block wx:else><block wx:if="{{item.fwtype==2}}"><view class="op"><block wx:if="{{item.order_status==0}}"><view class="t3">用户待支付</view></block><block wx:if="{{item.order_status!=0}}"><block><block wx:if="{{item.status==1}}"><view class="t1">已接单，等待上门</view></block><block wx:if="{{item.status==2}}"><view class="t1">{{"已到达，共用时"+item.useminute+"分钟"}}</view></block><block wx:if="{{item.status==3}}"><view class="t1">已完成</view></block></block></block><view class="flex1"></view><block wx:if="{{item.order_status==0}}"><view class="btn3" data-id="{{item.id}}" data-price="{{item.order_totalprice}}" data-event-opts="{{[['tap',[['changeprice',['$event']]]]]}}" catchtap="__e">改 价</view></block><block wx:if="{{item.order_status!=0}}"><block><block wx:if="{{showaddmoney}}"><block><block wx:if="{{item.sign_status==1&&item.status==2&&item.addprice<=0}}"><view class="btn1 btn2" data-id="{{item.id}}" data-event-opts="{{[['tap',[['addmoney',['$event']]]]]}}" catchtap="__e">补差价</view></block><block wx:if="{{item.status==1}}"><view class="btn1" data-id="{{item.id}}" data-st="2" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" catchtap="__e">出发</view></block><block wx:if="{{item.addprice>0}}"><view class="btn1 btn2" data-id="{{item.id}}" data-key="{{index}}" data-event-opts="{{[['tap',[['showpaycode',['$event']]]]]}}" catchtap="__e">查看补余款</view></block><block wx:if="{{!item.sign_status&&item.status==2}}"><view class="btn1" data-id="{{item.id}}" data-st="5" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" catchtap="__e">开始服务</view></block><block wx:if="{{item.sign_status==1&&item.status==2}}"><view class="btn1" data-id="{{item.id}}" data-st="3" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" catchtap="__e">服务完成</view></block></block></block><block wx:else><block><block wx:if="{{item.status==1}}"><block><block wx:if="{{yuyuecar&&item.protype&&cancancel}}"><view class="btn1" style="margin-right:20rpx;background:#fff;color:#222;border:2rpx solid #222;" data-id="{{item.id}}" data-st="10" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" catchtap="__e">取消</view></block><block wx:if="{{yuyuecar&&item.protype&&needstartpic}}"><block><view class="btn1" data-url="{{'/pagesA/yuyuecar/uppic?id='+item.id+'&st=2'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">我已到达</view></block></block><block wx:else><block><block wx:if="{{item.status==1}}"><view class="btn1" data-id="{{item.id}}" data-st="2" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" catchtap="__e">我已到达</view></block></block></block></block></block><block wx:if="{{item.status==2}}"><block><block wx:if="{{yuyuecar&&item.protype&&needendpic}}"><block><view class="btn1" data-url="{{'/pagesA/yuyuecar/uppic?id='+item.id+'&st=3'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">我已完成</view></block></block><block wx:else><block><view class="btn1" data-id="{{item.id}}" data-st="3" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" catchtap="__e">我已完成</view></block></block></block></block></block></block></block></block></view></block></block></view></block></block><block wx:if="{{nomore}}"><nomore vue-id="25634cb0-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="25634cb0-2" bind:__l="__l"></nodata></block></view><block wx:if="{{showtabbar}}"><view class="tabbar"><view class="tabbar-bot"></view><view class="tabbar-bar" style="background-color:#ffffff;"><view class="tabbar-item" data-url="dating" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/home.png'}}"></image></view><view class="tabbar-text">大厅</view></view><view class="tabbar-item" data-url="jdorderlist" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/order'+(st!=3?'2':'')+'.png'}}"></image></view><view class="{{['tabbar-text',st!=3?'active':'']}}">订单</view></view><view class="tabbar-item" data-url="jdorderlist?st=3" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/orderwc'+(st==3?'2':'')+'.png'}}"></image></view><view class="{{['tabbar-text',st==3?'active':'']}}">已完成</view></view><block wx:if="{{showform}}"><view class="tabbar-item" data-url="formlog" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/dangan.png'}}"></image></view><view class="tabbar-text">档案</view></view></block><view class="tabbar-item" data-url="my" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/my.png'}}"></image></view><view class="tabbar-text">我的</view></view></view></view></block><block wx:if="{{showmodal}}"><view class="modal"><view class="addmoney"><view class="title">{{(addprice>0?'修改':'创建')+"补余款"}}</view><view class="item"><label class="label">金额：</label><input type="text" name="blance_price" placeholder="请输入补余款金额" placeholder-style="font-size:24rpx" data-event-opts="{{[['input',[['bindMoney',['$event']]]]]}}" value="{{addprice}}" bindinput="__e"/>元</view><view class="btn"><button data-event-opts="{{[['tap',[['cancel',['$event']]]]]}}" class="btn-cancel" bindtap="__e">取消</button><button data-event-opts="{{[['tap',[['addconfirm',['$event']]]]]}}" class="confirm" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" catchtap="__e">确定</button></view></view></view></block><block wx:if="{{showpaycodes}}"><view class="modal"><view class="addmoney"><view class="title">查看补余款</view><view class="item"><label>金额：</label><text class="price">{{addprice}}</text>元</view><view class="item" style="padding-top:0;"><label>支付状态：</label><block wx:if="{{addmoneystatus==1}}"><text class="t2">已支付</text></block><block wx:if="{{addmoneystatus==0}}"><text class="t2" style="color:red;">待支付</text></block></view><view class="qrcode"><image src="{{paycode}}"></image></view><view class="btn"><button data-event-opts="{{[['tap',[['cancel',['$event']]]]]}}" class="btn-cancel" bindtap="__e">关闭</button><block wx:if="{{addmoneystatus==0}}"><button class="btn-update" style="{{('background:linear-gradient(90deg,'+$root.m2+' 0%,rgba('+$root.m3+',0.8) 100%)')}}" data-key="{{index}}" data-id="{{id}}" data-event-opts="{{[['tap',[['update',['$event']]]]]}}" bindtap="__e">修改差价</button></block></view></view></view></block><block wx:if="{{ischangeprice}}"><view class="modal"><view class="addmoney changeprice"><view class="title">订单改价</view><view class="item flex-y-center"><label class="label">订单金额：</label><input type="text" name="blance_price" placeholder="订单金额" placeholder-style="font-size:24rpx" data-event-opts="{{[['input',[['__set_model',['','totalprice','$event',[]]]]]]}}" value="{{totalprice}}" bindinput="__e"/>元</view><view class="flex-xy-center"><button data-event-opts="{{[['tap',[['changepriceCancel',['$event']]]]]}}" class="cbtn" bindtap="__e">取消</button><button data-event-opts="{{[['tap',[['changepriceSub',['$event']]]]]}}" class="cbtn" catchtap="__e">确定</button></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="25634cb0-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="25634cb0-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="25634cb0-5" data-ref="popmsg" bind:__l="__l"></popmsg><view style="display:none;">{{timestamp}}</view><wxxieyi vue-id="25634cb0-6" bind:__l="__l"></wxxieyi></view>