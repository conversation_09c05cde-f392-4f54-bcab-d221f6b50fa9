<view><block wx:if="{{isload}}"><block><block wx:if="{{sysset.showgzts}}"><block><view style="width:100%;height:88rpx;"></view><view class="follow_topbar"><view class="headimg"><image src="{{sysset.logo}}"></image></view><view class="info"><view class="i">欢迎进入<text style="{{'color:'+($root.m0)+';'}}">{{sysset.name}}</text></view><view class="i">关注公众号享更多专属服务</view></view><view data-event-opts="{{[['tap',[['showsubqrcode',['$event']]]]]}}" class="sub" style="{{'background-color:'+($root.m1)+';'}}" bindtap="__e">立即关注</view></view><uni-popup class="vue-ref" vue-id="2a792fbf-1" id="qrcodeDialog" type="dialog" data-ref="qrcodeDialog" bind:__l="__l" vue-slots="{{['default']}}"><view class="qrcodebox"><image class="img" src="{{sysset.qrcode}}" data-url="{{sysset.qrcode}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">长按识别二维码关注</view><view data-event-opts="{{[['tap',[['closesubqrcode',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="{{pre_url+'/static/img/close2.png'}}"></image></view></view></uni-popup></block></block><block wx:if="{{isplay==0}}"><view class="swiper-container"><swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" interval="{{500000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><swiper-item class="swiper-item"><view class="swiper-item-view"><image class="img" src="{{sysset.pic}}" mode="widthFix"></image></view></swiper-item></swiper></view></block><view class="header"><view class="price_share"><view class="title">{{product.name}}</view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/share.png'}}"></image><text class="txt">分享</text></view></view><view class="pricebox flex"><view class="price"><view class="f1" style="{{'color:'+($root.m2)+';'}}"><text>{{product.price+product.unit}}</text></view></view></view></view><view class="choosebox"><view data-event-opts="{{[['tap',[['chooseTime',['$event']]]]]}}" class="choosedate" bindtap="__e"><view class="f0">服务时间</view><view class="f1 flex1">{{days+" "+selectDates}}</view><image class="f2" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view><view data-event-opts="{{[['tap',[['chooseTime',['$event']]]]]}}" class="choosedate" bindtap="__e"><view class="f0">服务时长</view><view class="f1 flex1"><slider class="slider" block-size="10" min="{{1}}" max="{{10}}" value="{{currentnum}}" activeColor="#595959" data-event-opts="{{[['change',[['sliderChange',['$event']]]],['changing',[['sliderChanging',['$event']]]]]}}" bindchange="__e" bindchanging="__e"></slider><text>{{currentnum+" "+product.unit}}</text></view><image class="f2" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view><view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">服务详情</view><view class="t2"></view><view class="t1"></view></view><view class="detail"><image src="{{sysset.detailpic}}" mode="widthFix"></image></view><view style="width:100%;height:140rpx;"></view><view class="{{['bottombar','flex-row',menuindex>-1?'tabbarbot':'']}}"><view class="f1"><view class="item" data-url="{{'prolist?bid='+product.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/shou.png'}}"></image><view class="t1">首页</view></view><block wx:if="{{kfurl!='contact::'}}"><view class="item" data-url="{{kfurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/kefu.png'}}"></image><view class="t1">客服</view></view></block><block wx:else><button class="item" open-type="contact" show-message-card="true"><image class="img" src="{{pre_url+'/static/img/kefu.png'}}"></image><view class="t1">客服</view></button></block></view><view class="op"><view data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m3)+';'}}" bindtap="__e">立即预约</view></view></view><block wx:if="{{buydialogShow}}"><yybuydialog vue-id="2a792fbf-2" proid="{{product.id}}" btntype="{{btntype}}" menuindex="{{menuindex}}" data-event-opts="{{[['^currgg',[['currgg']]],['^buydialogChange',[['buydialogChange']]],['^addcart',[['addcart']]]]}}" bind:currgg="__e" bind:buydialogChange="__e" bind:addcart="__e" bind:__l="__l"></yybuydialog></block><scrolltop vue-id="2a792fbf-3" isshow="{{scrolltopshow}}" bind:__l="__l"></scrolltop><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m4=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/sharefriends.png'}}"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m5=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/sharefriends.png'}}"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m6!='h5'}}"><button class="f1" open-type="share"><image class="img" src="{{pre_url+'/static/img/sharefriends.png'}}"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/sharepic.png'}}"></image><text class="t1">生成分享图片</text></view></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/close.png'}}"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block><block wx:if="{{timeDialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hidetimeDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择时间</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hidetimeDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="order-tab"><view class="order-tab2"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['item '+(curTopIndex==index?'on':'')]}}" data-index="{{index}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e"><view class="datetext">{{item.$orig.weeks}}</view><view class="datetext2">{{item.$orig.date}}</view><view class="after" style="{{'background:'+(item.m7)+';'}}"></view></view></block></block></view></view><view class="flex daydate"><block wx:for="{{timelist}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><block><view class="{{['date '+(timeindex==index2&&item.status==1?'on':'')+(item.status==0?'hui':'')]}}" data-index2="{{index2}}" data-status="{{item.status}}" data-time="{{item.timeint}}" data-event-opts="{{[['tap',[['switchDateTab',['$event']]]]]}}" bindtap="__e">{{''+item.time}}</view></block></block></view><view class="op"><button data-event-opts="{{[['tap',[['selectDate',['$event']]]]]}}" class="tobuy on" style="{{'background-color:'+($root.m8)+';'}}" bindtap="__e">确 定</button></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="2a792fbf-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="2a792fbf-5" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="2a792fbf-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>