<view><block wx:if="{{isload}}"><block><block wx:if="{{info.id&&info.shstatus==2}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;">{{"审核不通过："+info.reason+"，请修改后再提交"}}</view></block><block wx:if="{{info.id&&info.shstatus==0&&info.apply_paymoney>0&&order.status==0}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;">您已提交资料，待支付</view></block><block wx:else><block wx:if="{{info.id&&info.shstatus==0&&info.apply_paymoney>0&&order.status==1}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;">您已提交申请，请等待审核</view></block><block wx:else><block wx:if="{{info.id&&info.shstatus==0&&info.apply_paymoney==0}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;">您已提交申请，请等待审核</view></block></block></block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="apply_box"><view class="apply_item"><view>姓名<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="realname" placeholder="请填写姓名" value="{{info.realname}}"/></view></view><view class="apply_item"><view>电话<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="tel" placeholder="请填写手机号码" value="{{info.tel}}"/></view></view></view><view class="apply_box"><block wx:if="{{!isapply2}}"><block><view class="apply_item"><view>加入商家<text style="color:red;">*</text></view><view><picker value="{{bindex}}" range="{{blist}}" range-key="name" data-event-opts="{{[['change',[['busChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{bname}}</view></picker></view></view><view class="apply_item"><view class="f1">服务类目<text style="color:red;">*</text></view><view data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" style="display:flex;align-items:center;" bindtap="__e"><block wx:if="{{$root.g0>0}}"><text>{{fwcnames}}</text></block><block wx:else><text style="color:#888;">请选择</text></block><image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></block></block><view class="apply_item"><view>所属类型<text style="color:red;">*</text></view><view><picker value="{{cindex}}" range="{{cateArr}}" data-event-opts="{{[['change',[['cateChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{cateArr[cindex]}}</view></picker></view></view><view class="apply_item"><text class="label">年龄</text><view class="flex-y-center"><input type="number" name="age" placeholder="请填写年龄" value="{{info.age}}"/></view></view><view class="apply_item"><text class="label">性别</text><view class="flex-y-center"><radio-group class="radio-group" name="sex"><label class="radio"><radio value="1" checked="{{info.sex==1?true:false}}"></radio>男</label><label class="radio"><radio value="2" checked="{{info.sex==2?true:false}}"></radio>女</label></radio-group></view></view><block wx:if="{{!hide_city}}"><block><view class="apply_item"><view>服务城市<text style="color:red;">*</text></view><view class="flex-y-center"><uni-data-picker vue-id="362c79ac-1" localdata="{{items}}" border="{{false}}" placeholder="{{regiondata||'请选择省市区'}}" data-event-opts="{{[['^change',[['regionchange']]]]}}" bind:change="__e" bind:__l="__l"></uni-data-picker></view></view><block wx:if="{{!isapply2}}"><block><view class="apply_item"><view>定位坐标<text style="color:red;">*</text></view><view data-event-opts="{{[['tap',[['locationSelect',['$event']]]]]}}" class="flex-y-center" bindtap="__e"><input type="text" disabled="{{true}}" placeholder="请选择坐标" value="{{latitude?latitude+','+longitude:''}}"/></view></view><view class="apply_item"><view>服务公里数<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="fuwu_juli" placeholder="请输入服务公里数" value="{{info.fuwu_juli}}"/>KM</view></view></block></block></block></block><view class="apply_item"><view>个人简介</view><view class="flex-y-center"><input type="text" name="desc" placeholder="个人简介" value="{{info.desc}}"/></view></view><input style="position:fixed;left:-200%;" type="text" hidden="true" name="latitude" value="{{latitude}}"/><input style="position:fixed;left:-200%;" type="text" hidden="true" name="longitude" value="{{longitude}}"/></view><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><view>工作照片<text style="color:red;">*</text></view></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{headimg}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="headimg" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g1==0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="headimg" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="headimg" maxlength="-1" value="{{$root.g2}}"/></view><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><view>身份证正反面<text style="color:red;">*</text></view></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{codepic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="codepic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g3<2}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="codepic" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="codepic" maxlength="-1" value="{{$root.g4}}"/></view><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><text>其他证件<text style="color:red;"></text>(上传资格证书和健康证）</text></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{otherpic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="otherpic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="otherpic" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></view><input type="text" hidden="true" name="otherpic" maxlength="-1" value="{{$root.g5}}"/></view><view class="apply_box"><view class="apply_item"><view>设置登录账号<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="un" placeholder="请填写登录账号" autocomplete="off" value="{{info.un}}"/></view></view><view class="apply_item"><view>设置登录密码<text style="color:red;">*</text></view><view class="flex-y-center"><input type="password" name="pwd" placeholder="请填写登录密码" autocomplete="off" value="{{info.pwd}}"/></view></view><view class="apply_item"><view>确认密码<text style="color:red;">*</text></view><view class="flex-y-center"><input type="password" name="repwd" placeholder="请再次填写密码" value="{{info.repwd}}"/></view></view><block wx:if="{{set.apply_paymoney>0}}"><view class="apply_item">{{(isapply2?'保证金费用':'缴纳金额')+"："}}<view style="color:red;">￥<text style="font-weight:bold;font-size:36rpx;color:red;">{{set.apply_paymoney}}</text></view></view></block></view><block wx:if="{{set.xieyi_show==1}}"><block><block wx:if="{{!info.id||info.shstatus==2}}"><view class="flex-y-center" style="margin-left:20rpx;color:#999;"><checkbox-group data-event-opts="{{[['change',[['isagreeChange',['$event']]]]]}}" bindchange="__e"><label class="flex-y-center"><checkbox value="1" checked="{{isagree}}"></checkbox>阅读并同意</label></checkbox-group><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" style="{{('color:'+$root.m0)}}" bindtap="__e">《入驻协议》</text></view></block></block></block><view style="padding:30rpx 0;"><block wx:if="{{!info.id||info.shstatus==2}}"><button class="set-btn" style="{{('background:linear-gradient(90deg,'+$root.m1+' 0%,rgba('+$root.m2+',0.8) 100%)')}}" form-type="submit">提交申请</button></block></view><view style="padding:30rpx 0;"><block wx:if="{{info.id&&info.status==0&&info.apply_paymoney>0&&order.status==0&&info.shstatus==0}}"><button class="set-btn" style="{{('background:linear-gradient(90deg,'+$root.m3+' 0%,rgba('+$root.m4+',0.8) 100%)')}}" data-url="{{'/pagesExt/pay/pay?id='+order.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">立即支付</button></block></view></form><view style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7);" id="xieyi" hidden="{{!showxieyi}}"><view style="width:90%;margin:0 auto;height:85%;margin-top:10%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px;"><view style="overflow:scroll;height:100%;"><parse vue-id="362c79ac-2" content="{{set.xieyi}}" bind:__l="__l"></parse></view><view data-event-opts="{{[['tap',[['hidexieyi',['$event']]]]]}}" style="position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center;" bindtap="__e">已阅读并同意</view></view></view></block></block><block wx:if="{{fwlistshow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择服务类目</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><view class="clist-item" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['fwcidsChange',['$event']]]]]}}" bindtap="__e"><view class="flex1">{{item.$orig.name}}</view><view class="radio" style="{{(item.m5?'background:'+item.m6+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view><block wx:for="{{item.l0}}" wx:for-item="item2" wx:for-index="index2" wx:key="id"><block><view class="clist-item" style="padding-left:80rpx;" data-id="{{item2.$orig.id}}" data-event-opts="{{[['tap',[['fwcidsChange',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.g6-1==index2}}"><view class="flex1">{{"└ "+item2.$orig.name}}</view></block><block wx:else><view class="flex1">{{"├ "+item2.$orig.name}}</view></block><view class="radio" style="{{(item2.m7?'background:'+item2.m8+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></block></block></block></view></view></view></block><block wx:if="{{loading}}"><loading vue-id="362c79ac-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="362c79ac-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="362c79ac-5" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="362c79ac-6" bind:__l="__l"></wxxieyi></view>