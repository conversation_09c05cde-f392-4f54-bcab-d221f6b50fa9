<view class="container"><block wx:if="{{isload}}"><block><view class="topbox"><view class="order-tab"><view class="order-tab2"><view class="{{['item '+(curIndex==-1?'on':'')]}}" data-index="-1" data-id="0" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">全部<view class="after" style="{{'background:'+($root.m0)+';'}}"></view></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['item '+(curIndex==index?'on':'')]}}" data-index="{{index}}" data-id="{{item.$orig.appid}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">{{item.$orig.name}}<view class="after" style="{{'background:'+(item.m1)+';'}}"></view></view></block></block></view></view><block wx:if="{{curIndex!='-1'}}"><view class="classify-ul"><view class="libox flex" style="width:100%;"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="idx2" wx:key="idx2"><block><view class="classify-li" style="{{(curIndex2==idx2?'color:'+item.m2+';background:rgba('+item.m3+',0.2)':'')}}" data-id="{{item.$orig.appid}}" data-index="{{idx2}}" data-event-opts="{{[['tap',[['changeCTab',['$event']]]]]}}" bindtap="__e">{{item.$orig.name}}</view></block></block></view></view></block></view><view class="content flex" style="{{(curIndex==-1?'top: 100rpx;':'top:'+toppx)}}"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="f1" data-url="{{'peodetail2?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="headimg"><image src="{{item.$orig.avatar}}"></image></view><view class="text1 flex"><text class="t1">{{item.$orig.name+''}}</text><text class="t3">{{item.$orig.distanceDesc+''}}</text></view><view class="text3 flex"><block wx:if="{{curIndex==-1}}"><view class="t3">{{"接单量:"+item.$orig.acceptOrderTotal+''}}<text class="statusdesc">{{item.$orig.statusDesc}}</text></view></block><block wx:else><text class="t3">{{item.$orig.priceDesc+''}}</text></block><view class="yuyue" style="{{'background:'+(item.m4)+';'}}" data-url="{{'/yuyue/yuyue/peodetail2?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">预约</view></view></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="004d168c-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="004d168c-2" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="004d168c-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="004d168c-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="004d168c-5" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="004d168c-6" bind:__l="__l"></wxxieyi></view>