<view class="container"><block wx:if="{{isload}}"><block><view class="content"><view class="top flex"><view class="headimg"><image src="{{data.headimg}}"></image></view><view class="f1"><view class="t1">{{data.realname}}</view><view class="t2">{{data.jineng}}</view><view class="t3"><text class="t11"><text class="bold">{{data.totalnum}}</text>次服务</text><text class="t11"><text class="bold">{{data.comment_score}}</text>评价</text></view></view></view><block wx:if="{{data.desc}}"><view class="desc">{{''+data.desc+''}}</view></block><view class="list"><view class="tab"><view class="{{['item '+(curTopIndex==0?'on':'')]}}" data-index="{{0}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">{{"服务 "+data.count}}<view class="after" style="{{'background:'+($root.m0)+';'}}"></view></view><view class="{{['item '+(curTopIndex==1?'on':'')]}}" data-index="{{1}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">{{"评价 "+data.comment_score}}<view class="after" style="{{'background:'+($root.m1)+';'}}"></view></view><block wx:if="{{data.showdesc}}"><view class="{{['item '+(curTopIndex==2?'on':'')]}}" data-index="{{2}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">个人资料<view class="after" style="{{'background:'+($root.m2)+';'}}"></view></view></block></view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{curTopIndex==0}}"><view class="content2 flex" data-id="{{item.id}}"><view class="f1" data-url="{{'product?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="headimg"><image src="{{item.pic}}"></image></view><view class="text1" style="margin-bottom:16rpx;"><text class="t1">{{item.name+''}}</text><view class="text2">{{"已售 "+item.sales}}</view><view class="text3"><text class="t4">￥<text class="price">{{''+item.sell_price}}</text></text></view></view></view><view><view class="yuyue" data-url="{{'product?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">预约</view></view></view></block></block><block wx:if="{{curTopIndex==1}}"><view class="comment"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><image class="t1" src="{{item.headimg}}"></image><view class="t2">{{item.nickname}}</view><view class="flex1"></view><view class="t3"><block wx:for="{{[0,1,2,3,4]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><image class="img" src="{{pre_url+'/static/img/star'+(item.score>item2?'2native':'')+'.png'}}"></image></block></view></view><view style="color:#777;font-size:22rpx;">{{item.createtime}}</view><view class="f2"><text class="t1">{{item.content}}</text><view class="t2"><block wx:if="{{item.content_pic!=''}}"><block><block wx:for="{{item.content_pic}}" wx:for-item="itemp" wx:for-index="index" wx:key="index"><block><view data-url="{{itemp}}" data-urls="{{item.content_pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"><image src="{{itemp}}" mode="widthFix"></image></view></block></block></block></block></view></view><block wx:if="{{item.reply_content}}"><view class="f3"><view class="arrow"></view><view class="t1">{{"商家回复："+item.reply_content}}</view></view></block></view></block></view></block><block wx:if="{{curTopIndex==2}}"><view><view class="plugdesc"><view class="item"><label class="t1">服务类目：</label>{{data.typename}}</view><view class="item"><label class="t1">服务城市：</label>{{data.citys}}</view><view class="item"><label class="t1">服务公里数：</label>{{data.fuwu_juli+"km"}}</view><view class="item"><label class="t1">性别：</label>{{data.sex}}</view><view class="item"><label class="t1">年龄：</label>{{data.age}}</view><view class="item"><label class="t1">联系信息：</label>{{data.tel}}</view></view></view></block></view></view><block wx:if="{{nomore}}"><nomore vue-id="24d6159d-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="24d6159d-2" bind:__l="__l"></nodata></block><view style="height:140rpx;"></view></block></block><block wx:if="{{loading}}"><loading vue-id="24d6159d-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="24d6159d-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="24d6159d-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>