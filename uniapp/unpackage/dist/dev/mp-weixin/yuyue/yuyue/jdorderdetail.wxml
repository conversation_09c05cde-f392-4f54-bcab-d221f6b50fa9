<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{orderinfo.protype==1}}"><map class="map" longitude="{{psorder.longitude2}}" latitude="{{psorder.latitude2}}" scale="14" markers="{{[{id:0,latitude:psorder.latitude2,longitude:psorder.longitude2,iconPath:pre_url+'/static/img/peisong/marker_kehu.png',width:'44',height:'54'},{id:2,latitude:worker.latitude,longitude:worker.longitude,iconPath:pre_url+'/static/img/peisong/marker_qishou.png',width:'44',height:'54'}]}}"></map></block><block wx:else><block wx:if="{{psorder.status!=4}}"><map class="map" longitude="{{binfo.longitude}}" latitude="{{binfo.latitude}}" scale="14" markers="{{[{id:0,latitude:binfo.latitude,longitude:binfo.longitude,iconPath:pre_url+'/static/img/peisong/marker_business.png',width:'44',height:'54'},{id:1,latitude:orderinfo.latitude,longitude:orderinfo.longitude,iconPath:pre_url+'/static/img/peisong/marker_kehu.png',width:'44',height:'54'},{id:2,latitude:worker.latitude,longitude:worker.longitude,iconPath:pre_url+'/static/img/peisong/marker_qishou.png',width:'44',height:'54'}]}}"></map></block><block wx:else><map class="map" longitude="{{binfo.longitude}}" latitude="{{binfo.latitude}}" scale="14" markers="{{[{id:0,latitude:binfo.latitude,longitude:binfo.longitude,iconPath:pre_url+'/static/img/peisong/marker_business.png',width:'44',height:'54'},{id:0,latitude:orderinfo.latitude,longitude:orderinfo.longitude,iconPath:pre_url+'/static/img/peisong/marker_kehu.png',width:'44',height:'54'}]}}"></map></block></block><view class="order-box"><view class="head"><block wx:if="{{psorder.fwtype!=2}}"><view class="fwtype1">到店</view></block><block wx:else><view class="fwtype2">上门</view></block><block wx:if="{{psorder.fwtype==1||psorder.fwtype==3}}"><view><block wx:if="{{psorder.status==3}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>已完成</view></block><block wx:if="{{psorder.status==1}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>{{''+orderinfo.yydate}}<text class="t1">预计上门时间</text></view></block><block wx:if="{{psorder.status==2}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image><text class="t1" style="margin-left:10rpx;">服务中</text></view></block></view></block><block wx:else><block wx:if="{{psorder.fwtype==2}}"><view><block wx:if="{{psorder.status==3}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>已完成</view></block><block wx:else><block wx:if="{{psorder.status==1}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>期望上门时间<text class="t1">{{orderinfo.yydate}}</text></view></block><block wx:else><block wx:if="{{psorder.status==2}}"><view class="f1"><image class="img" src="{{pre_url+'/static/img/peisong/ps_time.png'}}"></image>已到达，服务中</view></block></block></block></view></block></block><view class="flex1"></view><view class="f2"><block wx:if="{{psorder.showprice}}"><view class="t1"><text>{{"￥"+psorder.order_totalprice}}</text><block wx:if="{{psorder.ticheng>0}}"><text class="t11">{{"(￥"+psorder.ticheng+")"}}</text></block></view></block><block wx:else><block><text class="t1">{{psorder.ticheng}}</text>元</block></block></view></view><view class="content" style="border-bottom:0;"><block wx:if="{{!orderinfo.protype}}"><block><block wx:if="{{psorder.fwtype==2}}"><view class="f1"><view class="t1"><text class="x1">{{psorder.juli}}</text><text class="x2">{{psorder.juli_unit}}</text></view><view class="t2"><image class="img" src="{{pre_url+'/static/img/peisong/ps_juli.png'}}"></image></view><view class="t3"><text class="x1">{{psorder.juli2}}</text><text class="x2">{{psorder.juli2_unit}}</text></view></view></block><view class="f2"><view class="t1">{{binfo.name}}</view><view class="t2">{{binfo.address}}</view><view class="t3">{{orderinfo.address}}</view><view class="t2">{{orderinfo.area}}</view></view><view class="f3" data-protype="0" data-fwtype="{{psorder.fwtype}}" data-event-opts="{{[['tap',[['daohang',['$event']]]]]}}" catchtap="__e"><image class="img" src="{{pre_url+'/static/img/peisong/ps_daohang.png'}}"></image></view></block></block><block wx:else><block><view class="f1" style="margin-top:38rpx;"><view class="t3"><text class="x1">{{psorder.juli2}}</text><text class="x2">{{psorder.juli2_unit}}</text></view></view><view class="f2"><view class="t3">{{orderinfo.address}}</view><view class="t2">{{orderinfo.area}}</view></view><view class="f3" data-protype="1" data-event-opts="{{[['tap',[['daohang',['$event']]]]]}}" catchtap="__e"><image class="img" src="{{pre_url+'/static/img/peisong/ps_daohang.png'}}"></image></view></block></block></view></view><view class="orderinfo"><view class="box-title">{{"商品清单("+orderinfo.procount+")"}}</view><block wx:for="{{prolist}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="item"><text class="t1 flex1">{{item.name+" "+item.ggname}}</text><text class="t2 flex0">{{"￥"+item.sell_price+" ×"+item.num+''}}</text></view></block></view><block wx:if="{{psorder.status!=0}}"><view class="orderinfo"><view class="box-title">服务信息</view><view class="item"><text class="t1">用户姓名</text><text class="t2">{{orderinfo.linkman}}</text></view><view class="item"><text class="t1">用户电话</text><text class="t2">{{orderinfo.tel}}</text></view><view class="item"><text class="t1">预约时间</text><text class="t2">{{orderinfo.yydate}}</text></view><view class="item"><text class="t1">接单时间</text><text class="t2">{{$root.m0}}</text></view><block wx:if="{{psorder.daodiantime}}"><view class="item"><text class="t1">{{yuyue_sign?'出发时间':'到店时间'}}</text><text class="t2">{{$root.m1}}</text></view></block><block wx:if="{{psorder.sign_time}}"><view class="item"><text class="t1">开始时间</text><text class="t2">{{$root.m2}}</text></view></block><block wx:if="{{psorder.endtime}}"><view class="item"><text class="t1">完成时间</text><text class="t2">{{$root.m3}}</text></view></block></view></block><view class="orderinfo"><view class="box-title">订单信息</view><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{orderinfo.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{$root.m4}}</text></view><block wx:if="{{orderinfo.status>0&&orderinfo.status<4}}"><block><view class="item"><text class="t1">支付时间</text><text class="t2">{{$root.m5}}</text></view><view class="item"><text class="t1">支付方式</text><text class="t2">{{orderinfo.paytype}}</text></view></block></block><view class="item"><text class="t1">商品金额</text><text class="t2 red">{{"¥"+orderinfo.product_price}}</text></view><view class="item"><text class="t1">订单金额</text><text class="t2 red">{{"¥"+orderinfo.totalprice}}</text></view><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+orderinfo.paymoney}}</text></view><block wx:if="{{orderinfo.message}}"><view class="item"><text class="t1">备注</text><text class="t2 red">{{orderinfo.message?orderinfo.message:'无'}}</text></view></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><block wx:if="{{item.g0>0}}"><view class="item"><text class="t1">{{item.$orig[0]}}</text><block wx:if="{{item.$orig[2]=='upload'}}"><view class="t2"><image style="width:400rpx;height:auto;" src="{{item.$orig[1]}}" mode="widthFix" data-url="{{item.$orig[1]}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:else><block wx:if="{{item.$orig[2]=='upload_video'}}"><view class="t2"><video style="width:100%;" src="{{item.$orig[1]}}"></video></view></block><block wx:else><block wx:if="{{item.$orig[2]=='upload_pics'}}"><view class="t2"><block wx:for="{{item.$orig[1]}}" wx:for-item="vv" wx:for-index="__i1__" wx:key="*this"><block><image style="width:200rpx;height:auto;margin-right:10rpx;" src="{{vv}}" mode="widthFix" data-url="{{vv}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></block></block></view></block><block wx:else><text class="t2" user-select="true" selectable="true">{{item.$orig[1]}}</text></block></block></block></view></block></block><view class="item"><text class="t1">服务方式</text><text class="t2"><block wx:if="{{orderinfo.fwtype==2}}"><block>{{''+text['上门服务']+''}}</block></block><block wx:if="{{orderinfo.fwtype==3}}"><block>到商家服务</block></block><block wx:if="{{orderinfo.fwtype==1}}"><block>{{''+text['到店服务']+''}}</block></block></text></view><block wx:if="{{orderinfo.fwbid&&orderinfo.fwbinfo}}"><block><view class="item"><text class="t1">商家名称</text><text class="t2">{{orderinfo.fwbinfo.name}}</text></view><block wx:if="{{orderinfo.fwbinfo.address}}"><view class="item"><view class="t1">商家地址</view><block wx:if="{{!orderinfo.fwbinfo.latitude||!orderinfo.fwbinfo.longitude}}"><view class="t2">{{''+orderinfo.fwbinfo.address+''}}</view></block><block wx:else><view class="t2" data-latitude="{{orderinfo.fwbinfo.latitude}}" data-longitude="{{orderinfo.fwbinfo.longitude}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e">{{''+orderinfo.fwbinfo.address+''}}</view></block></view></block></block></block></view><view style="width:100%;height:120rpx;"></view><block wx:if="{{psorder.status!=4}}"><view class="bottom"><block wx:if="{{psorder.status!=0}}"><view class="f1" data-tel="{{orderinfo.tel}}" data-event-opts="{{[['tap',[['call',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/peisong/tel1.png'}}"></image>联系顾客</view></block><block wx:if="{{psorder.status!=0}}"><view class="f2" data-tel="{{binfo.tel}}" data-event-opts="{{[['tap',[['call',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/peisong/tel2.png'}}"></image>联系商家</view></block><block wx:if="{{psorder.status==0&&psorder.isqd==1}}"><view class="btn1" data-id="{{psorder.id}}" data-event-opts="{{[['tap',[['qiangdan',['$event']]]]]}}" bindtap="__e">立即抢单</view></block><block wx:if="{{psorder.status==0&&!psorder.isqd}}"><view class="btn1" style="background:#BCBFC7;">{{psorder.djs+"后可抢单"}}</view></block><block wx:if="{{psorder.fwtype==1||psorder.fwtype==3}}"><block><block wx:if="{{psorder.status==1}}"><view class="btn1" data-id="{{psorder.id}}" data-st="2" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" bindtap="__e">顾客已到店</view></block><block wx:if="{{psorder.status==2}}"><view class="btn1" data-id="{{psorder.id}}" data-st="3" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" bindtap="__e">我已完成</view></block></block></block><block wx:if="{{psorder.fwtype==2}}"><block><block wx:if="{{psorder.status==1}}"><block><block wx:if="{{yuyuecar&&psorder.protype&&needstartpic}}"><block><view class="btn1" data-url="{{'/pagesA/yuyuecar/uppic?id='+psorder.id+'&st=2'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">我已到达</view></block></block><block wx:else><block><view class="btn1" data-id="{{psorder.id}}" data-st="2" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" bindtap="__e">我已到达</view></block></block></block></block><block wx:if="{{psorder.status==2}}"><block><block wx:if="{{yuyuecar&&psorder.protype&&needendpic}}"><block><view class="btn1" data-url="{{'/pagesA/yuyuecar/uppic?id='+psorder.id+'&st=3'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">我已完成</view></block></block><block wx:else><block><view class="btn1" data-id="{{psorder.id}}" data-st="3" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" bindtap="__e">我已完成</view></block></block></block></block><block wx:if="{{psorder.status==3}}"><view class="btn1" style="background:#f1f1f1;color:#333;">已完成</view></block></block></block></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="efdb79ba-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="efdb79ba-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="efdb79ba-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>