<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="79b00988-1" itemdata="{{['全部','待付款','派单中','待确认','已完成','已取消']}}" itemst="{{['all','0','1','2','3','4']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="head"><block wx:if="{{item.$orig.bid!=0}}"><view class="f1" data-url="{{'/pagesExt/business/index?id='+item.$orig.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{pre_url+'/static/img/ico-shop.png'}}"></image>{{''+item.$orig.binfo.name}}</view></block><block wx:else><view>{{"订单号："+item.$orig.ordernum}}</view></block><view class="flex1"></view><block wx:if="{{item.$orig.status==0}}"><text class="st0">待付款</text></block><block wx:if="{{item.$orig.status==1&&item.$orig.refund_status==0&&item.$orig.worker_orderid}}"><block><block wx:if="{{item.$orig.worker.status==0}}"><text class="st1">待接单</text></block><block wx:if="{{item.$orig.worker.status==1}}"><text class="st1">已接单</text></block><block wx:if="{{item.$orig.worker.status==2}}"><text class="st2">服务中</text></block></block></block><block wx:else><block wx:if="{{item.$orig.status==1&&item.$orig.refund_status==0}}"><block><text class="st1">派单中</text></block></block></block><block wx:if="{{item.$orig.status==1&&item.$orig.refund_status==1}}"><text class="st1">退款审核中</text></block><block wx:if="{{item.$orig.status==2}}"><text class="st2">服务中</text></block><block wx:if="{{item.$orig.status==3&&item.$orig.isconmement==0}}"><text class="st3">待评价</text></block><block wx:if="{{item.$orig.status==3}}"><text class="st4">已完成</text></block><block wx:if="{{item.$orig.status==4}}"><text class="st4">订单已关闭</text></block></view><view class="content" style="border-bottom:none;"><block wx:if="{{item.$orig.paidan_type==3}}"><view><image src="{{item.$orig.propic}}"></image></view></block><block wx:else><view data-url="{{'product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item.$orig.propic}}"></image></view></block><view class="detail"><text class="t1">{{item.$orig.proname}}</text><text class="t1">{{"预约日期："+item.$orig.yy_time}}</text><block wx:if="{{item.$orig.balance_price>0}}"><view class="t3"><text class="x1 flex1">{{"实付金额：￥"+item.$orig.totalprice}}</text><block wx:if="{{item.$orig.balance_price>0}}"><text class="x1 flex1">{{"尾款：￥"+item.$orig.balance_price}}</text></block></view></block><block wx:else><view class="t3"><text class="x1 flex1">{{"实付金额：￥"+item.$orig.totalprice}}</text><block wx:if="{{isshowpandan}}"><text class="x1 flex1">{{"含跑腿费：￥"+item.$orig.paidan_money}}</text></block></view></block></view></view><block wx:if="{{item.$orig.send_time>0}}"><view class="bottom"><text>{{"派单时间："+item.$orig.senddate}}</text><block wx:if="{{item.$orig.refund_status==1}}"><text style="color:red;">{{'退款中￥'+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==2}}"><text style="color:red;">{{'已退款￥'+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==3}}"><text style="color:red;">退款申请已驳回</text></block></view></block><view class="op"><view class="btn2" data-url="{{'orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">详情</view><block wx:if="{{item.$orig.status==0}}"><block><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['toclose',['$event']]]]]}}" catchtap="__e">关闭订单</view><view class="btn1" style="{{'background:'+(item.m0)+';'}}" data-url="{{'/pagesExt/pay/pay?id='+item.$orig.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去付款</view></block></block><block wx:if="{{item.$orig.status==1}}"><block><block wx:if="{{item.$orig.cancel}}"><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['toclose',['$event']]]]]}}" catchtap="__e">取消订单</view></block><block wx:if="{{item.$orig.refund_status==0||item.$orig.refund_status==3}}"><view class="btn2" data-url="{{'refund?orderid='+item.$orig.id+'&price='+item.$orig.totalprice}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">申请退款</view></block></block></block><block wx:if="{{item.$orig.status==2}}"><block><block wx:if="{{item.$orig.refund_status==0||item.$orig.refund_status==3}}"><view class="btn2" data-url="{{'refund?orderid='+item.$orig.id+'&price='+item.$orig.totalprice}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">申请退款</view></block><block wx:if="{{item.$orig.freight_type!=3}}"><view class="btn2" data-url="{{'logistics?express_no='+item.$orig.worker_orderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">查看进度</view></block><block wx:if="{{item.$orig.balance_pay_status==0&&item.$orig.balance_price>0&&item.$orig.addmoney==0}}"><view class="btn1" style="{{'background:'+(item.m1)+';'}}" data-url="{{'/pagesExt/pay/pay?id='+item.$orig.balance_pay_orderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">支付尾款</view></block><block wx:else><block wx:if="{{item.$orig.balance_pay_status==0&&item.$orig.balance_price>0&&item.$orig.addmoney>0&&item.$orig.addmoneyStatus==1}}"><view class="btn1" style="{{'background:'+(item.m2)+';'}}" data-url="{{'/pagesExt/pay/pay?id='+item.$orig.balance_pay_orderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">支付尾款</view></block></block><block wx:if="{{item.$orig.addmoneyStatus==0&&item.$orig.addmoney>0}}"><view class="btn1" style="{{'background:'+(item.m3)+';'}}" data-url="{{'/pagesExt/pay/pay?id='+item.$orig.addmoneyPayorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">补差价</view></block></block></block><block wx:if="{{item.$orig.status==3||item.$orig.status==4}}"><block><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" catchtap="__e">删除订单</view></block></block></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="79b00988-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="79b00988-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="79b00988-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="79b00988-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="79b00988-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>