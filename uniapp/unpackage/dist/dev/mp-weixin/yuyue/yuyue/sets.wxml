<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="apply_box"><view class="apply_item"><view>姓名<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="realname" placeholder="请填写姓名" value="{{info.realname}}"/></view></view><view class="apply_item"><view>电话<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="tel" placeholder="请填写手机号码" value="{{info.tel}}"/></view></view></view><view class="apply_box"><view class="apply_item"><view>加入商家<text style="color:red;">*</text></view><view><picker value="{{bindex}}" range="{{blist}}" range-key="name" data-event-opts="{{[['change',[['busChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{bname}}</view></picker></view></view><view class="apply_item"><view class="f1">服务类目<text style="color:red;">*</text></view><view data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" style="display:flex;align-items:center;" bindtap="__e"><block wx:if="{{$root.g0>0}}"><text>{{fwcnames}}</text></block><block wx:else><text style="color:#888;">请选择</text></block><image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view><view class="apply_item"><view>所属类型<text style="color:red;">*</text></view><view><picker value="{{cindex}}" range="{{cateArr}}" data-event-opts="{{[['change',[['cateChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{cateArr[cindex]}}</view></picker></view></view><view class="apply_item"><text class="label">年龄</text><view class="flex-y-center"><input type="number" name="age" placeholder="请填写年龄" value="{{info.age}}"/></view></view><view class="apply_item"><text class="label">性别</text><view class="flex-y-center"><radio-group class="radio-group" name="sex"><label class="radio"><radio value="1" checked="{{info.sex==1?true:false}}"></radio>男</label><label class="radio"><radio value="2" checked="{{info.sex==2?true:false}}"></radio>女</label></radio-group></view></view><view class="apply_item"><view>服务城市<text style="color:red;">*</text></view><view class="flex-y-center"><uni-data-picker vue-id="03ffb843-1" localdata="{{items}}" border="{{false}}" placeholder="{{regiondata||'请选择省市区'}}" data-event-opts="{{[['^change',[['regionchange']]]]}}" bind:change="__e" bind:__l="__l"></uni-data-picker></view></view><view class="apply_item"><view>定位坐标<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" disabled="{{true}}" placeholder="请选择坐标" data-event-opts="{{[['tap',[['locationSelect',['$event']]]]]}}" value="{{latitude?latitude+','+longitude:''}}" bindtap="__e"/></view></view><view class="apply_item"><view>服务公里数<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="fuwu_juli" placeholder="请输入服务公里数" value="{{info.fuwu_juli}}"/>KM</view></view><view class="apply_item"><view>个人简介</view><view class="flex-y-center"><input type="text" name="desc" placeholder="个人简介" value="{{info.desc}}"/></view></view><input type="text" hidden="true" name="latitude" value="{{latitude}}"/><input type="text" hidden="true" name="longitude" value="{{longitude}}"/></view><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><view>工作照片<text style="color:red;">*</text></view></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{headimg}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="headimg" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g1==0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="headimg" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="headimg" maxlength="-1" value="{{$root.g2}}"/></view><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><view>身份证正反面<text style="color:red;">*</text></view></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{codepic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="codepic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g3<2}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="codepic" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="codepic" maxlength="-1" value="{{$root.g4}}"/></view><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><text>其他证件<text style="color:red;"></text>(上传资格证书和健康证，没有可以不填）</text></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{otherpic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="otherpic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="otherpic" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></view><input type="text" hidden="true" name="otherpic" maxlength="-1" value="{{$root.g5}}"/></view><view class="apply_box"><view class="apply_item"><view>登录账号<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="un" placeholder="请填写登录账号" autocomplete="off" value="{{info.un}}"/></view></view></view><view style="padding:30rpx 0;"><block wx:if="{{info.id}}"><button class="set-btn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">修改资料</button></block></view></form></block></block><block wx:if="{{fwlistshow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择服务类目</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><view class="clist-item" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['fwcidsChange',['$event']]]]]}}" bindtap="__e"><view class="flex1">{{item.$orig.name}}</view><view class="radio" style="{{(item.m2?'background:'+item.m3+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view><block wx:for="{{item.l0}}" wx:for-item="item2" wx:for-index="index2" wx:key="id"><block><view class="clist-item" style="padding-left:80rpx;" data-id="{{item2.$orig.id}}" data-event-opts="{{[['tap',[['fwcidsChange',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.g6-1==index2}}"><view class="flex1">{{"└ "+item2.$orig.name}}</view></block><block wx:else><view class="flex1">{{"├ "+item2.$orig.name}}</view></block><view class="radio" style="{{(item2.m4?'background:'+item2.m5+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></block></block></block></view></view></view></block><block wx:if="{{loading}}"><loading vue-id="03ffb843-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="03ffb843-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="03ffb843-4" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="03ffb843-5" bind:__l="__l"></wxxieyi></view>