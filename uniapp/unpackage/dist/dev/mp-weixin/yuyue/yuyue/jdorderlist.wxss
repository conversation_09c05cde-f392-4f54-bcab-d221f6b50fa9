.tabbar{height: auto; position: relative;}
.tabbar-icon {width: 50rpx;height: 50rpx;}
.tabbar-bar {display: flex;flex-direction: row;width: 100%;height:100rpx;position: fixed;bottom: 0;padding:10rpx 0 0 0;background: #fff;font-size: 24rpx;color: #999;border-top: 1px solid #e5e5e5;z-index: 8;box-sizing:content-box}
.tabbar-item {flex: 1;text-align: center;overflow: hidden;}
.tabbar-image-box {height: 54rpx;margin-bottom: 4rpx;}
.tabbar-text {line-height: 30rpx;font-size: 24rpx;color:#222222}
.tabbar-text.active{color:#06A051}
.tabbar-bot{height:110rpx;width:100%;box-sizing:content-box}
@supports(bottom: env(safe-area-inset-bottom)){
.tabbar-bot{padding-bottom:env(safe-area-inset-bottom);}
.tabbar-bar{padding-bottom:env(safe-area-inset-bottom);}
}
.container{ width:100%;display:flex;flex-direction:column}
.search-container {width: 100%;height:100rpx;padding: 20rpx 23rpx 20rpx 23rpx;background-color: #fff;position: relative;overflow: hidden;border-bottom:1px solid #f5f5f5}
.search-box {display:flex;align-items:center;height:60rpx;border-radius:30rpx;border:0;background-color:#f7f7f7;flex:1}
.search-box .img{width:24rpx;height:24rpx;margin-right:10rpx;margin-left:30rpx}
.search-box .search-text {font-size:24rpx;color:#222;width: 100%;}
.order-box{ width: 94%;margin:20rpx 3%;padding:6rpx 3%; background: #fff;border-radius:8px}
.order-box .head{ display:flex;width:100%; border-bottom: 1px #f5f5f5 solid; height:88rpx; line-height:88rpx; overflow: hidden; color: #999;}
.order-box .head .f1{display:flex;align-items:center;color:#222222;font-size: 26rpx;}
.order-box .head .f1 .img{width:24rpx;height:24rpx;margin-right:4px}
.order-box .head .f1 .t1{color:#06A051;margin-right:10rpx}
.order-box .head .f2{color:#FF6F30}
.order-box .head .f2 .t1{font-size:36rpx;margin-right:4rpx}
.order-box .head .f2 .t11{font-size:30rpx;color: #999;}
.order-box .content{display:flex;justify-content:space-between;width: 100%; padding:16rpx 0px;border-bottom: 1px solid #f5f5f5;position:relative}
.order-box .content .f1{width:100rpx;display:flex;flex-direction:column;align-items:center}
.order-box .content .f1 .t1{display:flex;flex-direction:column;align-items:center}
.order-box .content .f1 .t1 .x1{color:#FF6F30;font-size:28rpx;font-weight:bold}
.order-box .content .f1 .t1 .x2{color:#999999;font-size:24rpx;margin-bottom:8rpx}
.order-box .content .f1 .t2 .img{width:12rpx;height:36rpx}
.order-box .content .f1 .t3{display:flex;flex-direction:column;align-items:center}
.order-box .content .f1 .t3 .x1{color:#FF6F30;font-size:28rpx;font-weight:bold}
.order-box .content .f1 .t3 .x2{color:#999999;font-size:24rpx}
.order-box .content .f2{flex:1;padding:0 20rpx}
.order-box .content .f2 .t1{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-bottom:6rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.order-box .content .f2 .t2{font-size:24rpx;color:#222222;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.order-box .content .f2 .t3{font-size:36rpx;color:#222222;font-weight:bold;line-height:50rpx;margin-top:30rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.order-box .content .f3 .img{width:72rpx;height:168rpx}
.order-box .op{display:flex;justify-content:flex-end;align-items:center;width:100%; padding:20rpx 0px; border-top: 1px #f4f4f4 solid; color: #555;}
.order-box .op .t1{color:#06A051;font-weight:bold}
.order-box .op .t3{color:#ff711d;}
.order-box .op .btn1{background:linear-gradient(-90deg, #06A051 0%, #03B269 100%);height:70rpx;line-height:70rpx;color:#fff;border-radius:10rpx;text-align:center;font-size:28rpx; padding: 0 20rpx; font-size: 24rpx;}
.order-box .op .btn2{ margin-right: 20rpx; font-size: 24rpx; background:rgb(5,162,83,0.1) ; color: #06A152; border: 1rpx solid #1AB271;
}
.order-box .op .btn3{font-size: 24rpx; background: #ff8f4d; color:#ffffff; height:60rpx;border-radius: 10rpx;min-width: 110rpx;display: flex;align-items: center;justify-content: center;}
.modal{ position: fixed; width: 100%; height: 100%; bottom: 0; background: rgb(0,0,0,0.4); z-index: 100; display: flex; justify-content: center;}
.modal .addmoney{ width: 100%; background: #fff; width: 80%; position: absolute; top: 30%; border-radius: 10rpx;
}
.modal .title{ height: 80rpx; line-height: 80rpx; text-align: center; font-weight: bold; border-bottom: 1rpx solid #f5f5f5; font-size: 32rpx;
}
.modal .item{ display: flex; padding: 30rpx;}
.modal .item input{ width: 200rpx;}
.modal .item label{ width:200rpx; text-align: right; font-weight: bold;}
.modal .item .t2{ color: #008000; font-weight: bold;}
.modal .btn{ display: flex; margin: 30rpx 20rpx;
}
.modal .btn .btn-cancel{  background-color: #F2F2F2; width: 150rpx; border-radius: 10rpx;}
.modal .btn .confirm{ width: 150rpx; border-radius: 10rpx; color: #fff;}
.modal .btn .btn-update{ width: 150rpx; border-radius: 10rpx; color: #fff;
}
.modal .addmoney .price{ color: red; font-size: 32rpx; font-weight: bold;}
.modal .qrcode{ display: flex; align-items: center;}
.modal .qrcode image{width: 300rpx; height: 300rpx; margin: auto;}
.changeprice{height: 400rpx;}
.changeprice .item{height: 160rpx;}
.changeprice .item input{border-bottom: 1px solid #e1e1e1;padding:14rpx;line-height: 70rpx;height: 70rpx;text-align: center;}
.changeprice .title{font-weight: normal;font-size: 28rpx;}
.changeprice .cbtn{border: 1px solid #e1e1e1;width: 35%;text-align: center;border-radius: 8rpx;margin:20rpx 10rpx;}
.changeprice .cbtn:last-child{background: #ff8f4d; color:#ffffff;}
.fwtype1{background: linear-gradient(-90deg, #06A051 0%, #03B269 100%);color: #fff;border-radius: 8rpx;
	text-align: center;margin: 24rpx 10rpx;padding: 5rpx 8rpx;line-height: 33rpx;font-size: 22rpx;}
.fwtype2{background: #FF6F30;color: #fff;border-radius: 8rpx;text-align: center;margin: 24rpx 8rpx;padding: 5rpx 10rpx;line-height: 33rpx;font-size: 22rpx;}

