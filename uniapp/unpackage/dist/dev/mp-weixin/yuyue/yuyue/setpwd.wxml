<view class="addressadd"><block wx:if="{{isload}}"><block><form id="setform" data-event-opts="{{[['submit',[['confirm',['$event']]]]]}}" bindsubmit="__e"><view class="form"><view class="form-item"><label class="label">登录账户</label><view class="f2 flex flex1">{{user.un}}</view></view><view class="form-item"><label class="label">原 密 码</label><view class="f2 flex flex1"><input type="text" password="true" name="oldpwd" value="" placeholder="请输入原密码" placeholder-style="font-size:28rpx" autocomplete="off"/></view></view><view class="form-item"><label class="label">新 密 码</label><view class="f2 flex flex1"><input type="text" password="true" name="pwd" value="" placeholder="请输入新密码" placeholder-style="font-size:28rpx" autocomplete="off"/></view></view><view class="form-item"><label class="label">确认密码</label><view class="f2 flex flex1"><input type="text" password="true" name="repwd" value="" placeholder="请再次输入密码" placeholder-style="font-size:28rpx" autocomplete="off"/></view></view></view><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">确定修改</button></form></block></block></view>