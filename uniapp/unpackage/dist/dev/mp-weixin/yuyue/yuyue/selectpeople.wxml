<view class="container"><block wx:if="{{isload}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="content flex" data-id="{{item.id}}" data-realname="{{item.realname}}" data-tel="{{item.tel}}" data-yystatus="{{item.yystatus}}" data-event-opts="{{[['tap',[['setdefault',['$event']]]]]}}" catchtap="__e"><view class="f1"><view class="headimg"><block wx:if="{{item.headimg}}"><image src="{{item.headimg}}"></image></block><block wx:else><image src="{{pre_url+'/static/img/touxiang.png'}}"></image></block></view><view class="text1"><text class="t1">{{item.realname+''}}</text><block wx:if="{{item.typename}}"><text class="t2">{{item.typename}}</text></block><view class="text2">{{item.jineng}}</view></view></view><view class="{{['yuyue '+(item.yystatus==-1?'hui':'')]}}">预约</view></view></block><block wx:if="{{nodata}}"><nodata vue-id="cc4b876e-1" bind:__l="__l"></nodata></block><view style="height:140rpx;"></view></block></block><block wx:if="{{loading}}"><loading vue-id="cc4b876e-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="cc4b876e-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="cc4b876e-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>