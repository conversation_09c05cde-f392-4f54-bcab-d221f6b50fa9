<view class="container"><block wx:if="{{isload}}"><block><view class="search-container" style="{{(history_show?'height:100%;':'')}}"><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="搜索感兴趣的商品" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['searchbtn',['$event']]]]]}}" class="search-btn" bindtap="__e"><block wx:if="{{!history_show&&productlisttype=='itemlist'}}"><image style="height:36rpx;width:36rpx;" src="{{pre_url+'/static/img/show-cascades.png'}}"></image></block><block wx:if="{{!history_show&&productlisttype=='item2'}}"><image style="height:36rpx;width:36rpx;" src="{{pre_url+'/static/img/show-list.png'}}"></image></block><block wx:if="{{history_show}}"><text>搜索</text></block></view></view><view hidden="{{!(history_show)}}" class="search-history"><view><text class="search-history-title">最近搜索</text><view data-event-opts="{{[['tap',[['deleteSearchHistory',['$event']]]]]}}" class="delete-search-history" bindtap="__e"><image style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/del.png'}}"></image></view></view><view class="search-history-list"><block wx:for="{{history_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="search-history-item" data-value="{{item}}" data-event-opts="{{[['tap',[['historyClick',['$event']]]]]}}" bindtap="__e">{{item+''}}</view></block><block wx:if="{{$root.g0}}"><view class="flex-y-center"><image style="width:36rpx;height:36rpx;margin-right:10rpx;" src="{{pre_url+'/static/img/tanhao.png'}}"></image>暂无记录</view></block></view></view></view><view class="product-container"><block wx:if="{{$root.g1}}"><block><block wx:if="{{productlisttype=='item2'}}"><dp-yuyue-item vue-id="7000d7cc-1" data="{{datalist}}" bind:__l="__l"></dp-yuyue-item></block><block wx:if="{{productlisttype=='itemlist'}}"><dp-yuyue-itemlist vue-id="7000d7cc-2" data="{{datalist}}" bind:__l="__l"></dp-yuyue-itemlist></block></block></block><block wx:if="{{nomore}}"><nomore vue-id="7000d7cc-3" text="没有更多商品了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="7000d7cc-4" text="没有查找到相关商品" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="7000d7cc-5" bind:__l="__l"></loading></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="7000d7cc-6" bind:__l="__l"></loading></block><dp-tabbar vue-id="7000d7cc-7" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="7000d7cc-8" data-ref="popmsg" bind:__l="__l"></popmsg></view>