<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="title">服务人员登录</view><view class="loginform"><view class="form-item"><image class="img" src="{{pre_url+'/static/img/reg-tel.png'}}"></image><input class="input" type="text" placeholder="请输入登录账号" placeholder-style="font-size:30rpx;color:#B2B5BE" name="username" value=""/></view><view class="form-item"><image class="img" src="{{pre_url+'/static/img/reg-pwd.png'}}"></image><input class="input" type="text" placeholder="请输入密码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="password" value="" password="{{true}}"/></view><view class="form-item"><image class="img" src="{{pre_url+'/static/img/reg-code.png'}}"></image><input class="input" type="text" placeholder="请输入验证码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="captcha" value=""/><image style="width:240rpx;height:80rpx;" src="{{pre_url+'/?s=/ApiIndex/captcha&aid='+aid+'&session_id='+session_id+'&t='+randt}}" data-event-opts="{{[['tap',[['regetcaptcha',['$event']]]]]}}" bindtap="__e"></image></view><button class="form-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" form-type="submit">登录</button></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="ab35c042-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="ab35c042-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="ab35c042-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>