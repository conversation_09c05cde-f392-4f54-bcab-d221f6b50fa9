<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['topay',['$event']]]]]}}" bindsubmit="__e"><block wx:if="{{!protype}}"><block><block wx:if="{{sindex==1||sindex==3}}"><view class="address-add"><view class="linkitem"><text class="f1">联 系 人：</text><input class="input" type="text" placeholder="请输入您的姓名" placeholder-style="color:#626262;font-size:28rpx;" data-event-opts="{{[['input',[['inputLinkman',['$event']]]]]}}" value="{{linkman}}" bindinput="__e"/></view><view class="linkitem"><text class="f1">联系电话：</text><input class="input" type="text" placeholder="请输入您的手机号" placeholder-style="color:#626262;font-size:28rpx;" data-event-opts="{{[['input',[['inputTel',['$event']]]]]}}" value="{{tel}}" bindinput="__e"/></view></view></block><block wx:else><view class="address-add flex-y-center" data-url="{{'/pagesB/address/'+(address.id?'address':'addressadd')+'?fromPage=buy&type=1'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image class="img" src="{{pre_url+'/static/img/address.png'}}"></image></view><block wx:if="{{address.id}}"><view class="f2 flex1"><view style="font-weight:bold;color:#111111;font-size:30rpx;">{{address.name+" "+address.tel+''}}<block wx:if="{{address.company}}"><text>{{address.company}}</text></block></view><view style="font-size:24rpx;">{{address.area+" "+address.address}}</view></view></block><block wx:else><view class="f2 flex1">请选择您的地点</view></block><image class="f3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block></block></block><block wx:if="{{sindex==3}}"><view class="address-add flex-y-center" data-url="{{'/pagesB/yuyue/selectbusiness?prodata='+prodata}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image class="img" src="{{pre_url+'/static/img/address.png'}}"></image></view><block wx:if="{{fwbusiness.id}}"><view class="f2 flex1"><view style="font-weight:bold;color:#111111;font-size:30rpx;">{{fwbusiness.name}}</view></view></block><block wx:else><view class="f2 flex1">请选择要去的商家</view></block><image class="f3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:for="{{$root.l3}}" wx:for-item="buydata" wx:for-index="index" wx:key="index"><view class="buydata"><block wx:if="{{sindex==1}}"><view class="business-info"><view class="linkitem"><text class="f1">店铺名称：</text><text class="f2">{{buydata.$orig.business.name}}</text></view><view class="linkitem"><text class="f1">店铺地址：</text><text class="f2"><block wx:if="{{buydata.$orig.business.province!='北京市'||buydata.$orig.business.province!='上海市'||buydata.$orig.business.province!='重庆市'||buydata.$orig.business.province!='天津市'}}"><text>{{buydata.$orig.business.province}}</text></block>{{''+buydata.$orig.business.city+buydata.$orig.business.district+buydata.$orig.business.address+''}}</text></view></view></block><view class="bcontent"><block wx:if="{{protype==0}}"><view class="btitle">服务信息</view></block><block wx:if="{{protype==0}}"><view class="product"><block wx:for="{{buydata.$orig.prodata}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><view class="item flex"><view class="img" data-url="{{'product?id='+item.product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.guige.pic}}"><image src="{{item.guige.pic}}"></image></block><block wx:else><image src="{{item.product.pic}}"></image></block></view><view class="info flex1"><view class="f1">{{item.product.name}}</view><view class="f2">{{item.guige.name}}</view><view class="f3"><text style="font-weight:bold;">{{"￥"+item.guige.sell_price}}</text><text style="padding-left:20rpx;">{{'× '+item.num}}</text></view></view></view></block></view></block><view class="freight"><view class="f1">服务方式</view><view class="freight-ul"><block wx:for="{{buydata.l0}}" wx:for-item="item" wx:for-index="idx2" wx:key="idx2"><block><view class="freight-li" style="{{(item.$orig.key==sindex?'color:'+item.m0+';background:rgba('+item.m1+',0.2)':'')}}" data-index="{{item.$orig.key}}" data-event-opts="{{[['tap',[['selectFwtype',['$event']]]]]}}" bindtap="__e">{{item.$orig.name+''}}</view></block></block></view></view><block wx:if="{{protype==1}}"><view class="price"><view class="f1">选择服务规格</view><view class="f2" data-btntype="2" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" bindtap="__e">{{ggname?ggname:'请选择服务规格'}}</view></view></block><block wx:if="{{protype==1}}"><view class="price"><view class="f1">购买数量</view><view class="f2">{{num+danwei}}</view></view></block><view class="price"><view class="f1">预约时间</view><block wx:if="{{isdate}}"><view data-event-opts="{{[['tap',[['chooseTime',['$event']]]]]}}" class="f2" bindtap="__e"><block wx:if="{{yydate}}"><text>{{yydate}}</text></block><block wx:else><text style="color:#999;">请选择预约时间</text></block></view></block><block wx:else><view class="f2">{{''+yydate+" \n              "+(yydates_num>0?yydates_num+'个时间段':'')+''}}</view></block></view><block wx:if="{{buydata.$orig.fwpeople==1||buydata.$orig.fwpeople==3}}"><view class="price"><view class="f1">服务人员</view><block wx:if="{{!workerid||workerid==0}}"><view class="f2" data-url="{{'selectpeople?prodata='+prodata+'&yydate='+yydate+'&sindex='+sindex+'&linkman='+linkman+'&tel='+tel}}" data-event-opts="{{[['tap',[['gotopeople',['$event']]]]]}}" bindtap="__e">{{''+(!buydata.m2?buydata.$orig.fw.realname:'请选择人员')}}<text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></block><block wx:else><view class="f2">{{''+(!buydata.m3?buydata.$orig.fw.realname:'请选择人员')+''}}</view></block></view></block></view><block wx:if="{{protype&&protype==1}}"><view class="bcontent2"><block wx:if="{{sindex==2}}"><view><view class="price"><text class="f1">车辆位置</text><view data-event-opts="{{[['tap',[['locationSelect',['$event']]]]]}}" class="f2" style="overflow:hidden;line-height:60rpx;" bindtap="__e"><text>{{carlocat_name}}</text><image style="width:30rpx;height:30rpx;float:right;margin-top:16rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view><view class="price"><text class="f1">停靠位置</text><view class="f2" style="overflow:hidden;line-height:60rpx;padding-left:20rpx;"><input class="input" type="text" name="carlocat_stop" placeholder="请输入车辆停靠位置" placeholder-style="font-size:28rpx" data-event-opts="{{[['input',[['inputCarlocatStop',['$event']]]]]}}" value="{{carlocat_stop}}" bindinput="__e"/></view></view></view></block><view class="price"><text class="f1">车辆信息</text><view class="f2" style="overflow:hidden;line-height:60rpx;" data-url="/pagesA/yuyuecar/car" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>{{carinfor?carinfor.infor:''}}</text><image style="width:30rpx;height:30rpx;float:right;margin-top:16rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></view></block><view class="bcontent2"><block wx:if="{{buydata.$orig.leveldk_money>0}}"><view class="price"><text class="f1">{{buydata.m4+"折扣("+userinfo.discount+"折)"}}</text><text class="f2">{{"-¥"+buydata.$orig.leveldk_money}}</text></view></block><block wx:if="{{yyset.iscoupon==1}}"><view class="price"><view class="f1">{{buydata.m5}}</view><block wx:if="{{buydata.$orig.couponCount>0}}"><view class="f2" data-bid="{{buydata.$orig.bid}}" data-event-opts="{{[['tap',[['showCouponList',['$event']]]]]}}" bindtap="__e"><text style="{{'color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx;'+('background:'+(buydata.m6)+';')}}">{{buydata.$orig.couponrid!=0?buydata.$orig.couponList[buydata.$orig.couponkey].couponname:buydata.$orig.couponCount+'张可用'}}</text><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></block><block wx:else><text class="f2" style="color:#999;">{{"无可用"+buydata.m7}}</text></block></view></block><view class="price"><text class="f1">服务价格</text><text class="f2">{{"¥"+buydata.$orig.product_price}}</text></view><block wx:if="{{buydata.$orig.prodata[0].product.balance>0}}"><view class="price"><text class="f1">应付定金</text><text class="f2">{{"¥"+buydata.$orig.sell_price}}</text></view></block><block wx:if="{{buydata.$orig.coupontype==3}}"><view class="price"><text class="f1">计次卡</text><text class="f2" style="color:red;">{{"-"+buydata.$orig.product_price}}</text></view></block><view style="display:none;">{{test}}</view><block wx:for="{{buydata.l2}}" wx:for-item="item" wx:for-index="idx" wx:key="id"><view class="form-item"><view class="label">{{item.$orig.val1}}<block wx:if="{{item.$orig.val3==1}}"><text style="color:red;">*</text></block></view><block wx:if="{{item.$orig.key=='input'}}"><block><input class="input" type="text" name="{{'form'+buydata.$orig.bid+'_'+idx}}" placeholder="{{item.$orig.val2}}" data-idx="{{idx}}" data-bid="{{buydata.$orig.bid}}" placeholder-style="font-size:28rpx" data-event-opts="{{[['input',[['inputChange',['$event']]]]]}}" value="{{buydata.$orig.editorFormdata[idx]}}" bindinput="__e"/></block></block><block wx:if="{{item.$orig.key=='textarea'}}"><block><textarea class="textarea" name="{{'form'+buydata.$orig.bid+'_'+idx}}" placeholder="{{item.$orig.val2}}" placeholder-style="font-size:28rpx" data-idx="{{idx}}" data-bid="{{buydata.$orig.bid}}" data-event-opts="{{[['input',[['inputChange',['$event']]]]]}}" value="{{buydata.$orig.editorFormdata[idx]}}" bindinput="__e"></textarea></block></block><block wx:if="{{item.$orig.key=='radio'}}"><block><radio-group class="radio-group" name="{{'form'+buydata.$orig.bid+'_'+idx}}" data-idx="{{idx}}" data-bid="{{buydata.$orig.bid}}" data-event-opts="{{[['change',[['checkboxChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{item.$orig.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><radio class="radio" value="{{item1}}" checked="{{buydata.$orig.editorFormdata&&buydata.$orig.editorFormdata[idx]==item1?true:false}}"></radio>{{item1+''}}</label></block></radio-group></block></block><block wx:if="{{item.$orig.key=='checkbox'}}"><block><checkbox-group class="checkbox-group" name="{{'form'+buydata.$orig.bid+'_'+idx}}" data-idx="{{idx}}" data-bid="{{buydata.$orig.bid}}" data-event-opts="{{[['change',[['checkboxChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{item.l1}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><checkbox class="checkbox" value="{{item1.$orig}}" checked="{{item1.m8?true:false}}"></checkbox>{{item1.$orig+''}}</label></block></checkbox-group></block></block><block wx:if="{{item.$orig.key=='selector'}}"><block><picker class="picker" mode="selector" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="{{buydata.$orig.editorFormdata[idx]}}" range="{{item.$orig.val2}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{buydata.$orig.editorFormdata[idx]||buydata.$orig.editorFormdata[idx]===0}}"><view>{{''+item.$orig.val2[buydata.$orig.editorFormdata[idx]]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.$orig.key=='time'}}"><block><picker class="picker" mode="time" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="{{buydata.$orig.editorFormdata[idx]}}" start="{{item.$orig.val2[0]}}" end="{{item.$orig.val2[1]}}" range="{{item.$orig.val2}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{buydata.$orig.editorFormdata[idx]}}"><view>{{buydata.$orig.editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.$orig.key=='date'}}"><block><picker class="picker" mode="date" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="{{buydata.$orig.editorFormdata[idx]}}" start="{{item.$orig.val2[0]}}" end="{{item.$orig.val2[1]}}" range="{{item.$orig.val2}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{buydata.$orig.editorFormdata[idx]}}"><view>{{buydata.$orig.editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.$orig.key=='upload'}}"><block><input style="display:none;" type="text" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="{{buydata.$orig.editorFormdata[idx]}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{buydata.$orig.editorFormdata[idx]}}"><view class="form-imgbox"><view class="layui-imgbox-close" style="z-index:2;" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="form-imgbox-img"><image class="image" src="{{buydata.$orig.editorFormdata[idx]}}" data-url="{{buydata.$orig.editorFormdata[idx]}}" mode="aspectFit" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:else><view class="form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></block></view></block></block><block wx:if="{{item.$orig.key=='upload_pics'}}"><block><input style="display:none;" type="text" name="{{'form'+buydata.$orig.bid+'_'+idx}}" maxlength="-1" value="{{buydata.$orig.editorFormdata&&buydata.$orig.editorFormdata[idx]?item.g0:''}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:for="{{buydata.$orig.editorFormdata[idx]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><view class="form-imgbox"><view class="layui-imgbox-close" style="z-index:2;" data-index="{{index2}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-type="pics" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image class="image" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="form-imgbox-img"><image class="image" src="{{item2}}" data-url="{{item2}}" mode="aspectFit" data-idx="{{idx}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><view class="form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-type="pics" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></view></block></block><block wx:if="{{item.$orig.key=='upload_video'}}"><block><input style="display:none;" type="text" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="{{buydata.$orig.editorFormdata[idx]}}"/><view class="flex-y-center" style="flex-wrap:wrap;padding-top:20rpx;justify-content:flex-end;"><block wx:if="{{item.m9}}"><view style="color:#999;margin-right:20rpx;">{{item.$orig.val2}}</view></block><block wx:if="{{buydata.$orig.editorFormdata[idx]}}"><view class="form-imgbox"><view class="layui-imgbox-close" style="z-index:2;" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image class="image" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view style="overflow:hidden;white-space:pre-wrap;word-wrap:break-word;color:#4786BC;width:430rpx;"><video style="width:100%;" src="{{buydata.$orig.editorFormdata[idx]}}"></video></view></view></block><block wx:else><view class="form-uploadbtn" style="{{'margin-right:20rpx;'+('background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx')+';')+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['upVideo',['$event']]]]]}}" bindtap="__e"></view></block></view></block></block></view></block><block wx:if="{{buydata.m10}}"><view class="draft-box" style="padding:20rpx 0;"><view class="btn" data-bid="{{buydata.$orig.bid}}" data-event-opts="{{[['tap',[['saveDraft',['$event']]]]]}}" bindtap="__e">保存草稿</view></view></block></view></view></block><block wx:if="{{userinfo.canscoredk&&userinfo.score2money>0&&(userinfo.scoremaxtype==0||userinfo.scoremaxtype==1&&userinfo.scoredkmaxmoney>0)}}"><view class="scoredk"><checkbox-group data-event-opts="{{[['change',[['scoredk',['$event']]]]]}}" class="flex" style="width:100%;" bindchange="__e"><view class="f1"><view>{{userinfo.score*1+" "+$root.m11+'可抵扣'}}<text style="color:#e94745;">{{userinfo.scoredk_money*1}}</text>元</view><block wx:if="{{userinfo.scoremaxtype==0&&userinfo.scoredkmaxpercent>0&&userinfo.scoredkmaxpercent<100}}"><view style="font-size:22rpx;color:#999;">{{'最多可抵扣订单金额的'+userinfo.scoredkmaxpercent+"%"}}</view></block><block wx:else><block wx:if="{{userinfo.scoremaxtype==1}}"><view style="font-size:22rpx;color:#999;">{{'最多可抵扣'+userinfo.scoredkmaxmoney+"元"}}</view></block></block></view><view class="f2">{{"使用"+$root.m12+'抵扣'}}<checkbox style="margin-left:6px;transform:scale(.8);" value="1"></checkbox></view></checkbox-group></view></block><view style="width:100%;height:182rpx;"></view><view class="footer flex"><view class="text1 flex1">总计：<text style="font-weight:bold;font-size:36rpx;">{{"￥"+alltotalprice}}</text></view><block wx:if="{{issubmit}}"><button class="op" style="background:#999;">确认提交</button></block><block wx:else><button class="op" style="{{'background:'+('linear-gradient(-90deg,'+$root.m13+' 0%,rgba('+$root.m14+',0.8) 100%)')+';'}}" form-type="submit">确认提交</button></block></view></form><block wx:if="{{couponvisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">{{"请选择"+$root.m15}}</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><couponlist vue-id="231b4162-1" couponlist="{{allbuydata[bid].couponList}}" choosecoupon="{{true}}" selectedrid="{{allbuydata[bid].couponrid}}" bid="{{bid}}" data-event-opts="{{[['^chooseCoupon',[['chooseCoupon']]]]}}" bind:chooseCoupon="__e" bind:__l="__l"></couponlist></view></view></view></block></block></block><block wx:if="{{timeDialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hidetimeDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择时间</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hidetimeDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="order-tab"><view class="order-tab2"><block wx:for="{{$root.l4}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['item '+(curTopIndex==index?'on':'')]}}" data-index="{{index}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e"><view class="datetext">{{item.$orig.weeks}}</view><view class="datetext2">{{item.$orig.date}}</view><view class="after" style="{{'background:'+(item.m16)+';'}}"></view></view></block></block></view></view><view class="flex daydate"><block wx:for="{{timelist}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><block><view class="{{['date '+(timeindex==index2&&item.status==1?'on':'')+(item.status==0?'hui':'')]}}" data-index2="{{index2}}" data-status="{{item.status}}" data-time="{{item.timeint}}" data-event-opts="{{[['tap',[['switchDateTab',['$event']]]]]}}" bindtap="__e">{{''+item.time}}</view></block></block></view><view class="op"><button data-event-opts="{{[['tap',[['selectDate',['$event']]]]]}}" class="tobuy on" style="{{'background-color:'+($root.m17)+';'}}" bindtap="__e">确 定</button></view></view></view></block><block wx:if="{{buydialogShow}}"><yybuydialog vue-id="231b4162-2" proid="{{proid}}" btntype="{{btntype}}" menuindex="{{menuindex}}" isfuwu="{{isfuwu}}" data-event-opts="{{[['^currgg',[['currgg']]],['^buydialogChange',[['buydialogChange']]],['^addcart',[['addcart']]],['^tobuy',[['tobuy']]]]}}" bind:currgg="__e" bind:buydialogChange="__e" bind:addcart="__e" bind:tobuy="__e" bind:__l="__l"></yybuydialog></block><block wx:if="{{loading}}"><loading vue-id="231b4162-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="231b4162-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="231b4162-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>